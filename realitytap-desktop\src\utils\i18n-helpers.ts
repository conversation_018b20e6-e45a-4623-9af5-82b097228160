/**
 * 国际化辅助工具函数
 * 用于处理动态文本替换和快捷键相关的国际化
 */

import { getFrequencyAdjustmentKeyDisplayName } from '@/components/editor/waveform/config/waveform-constants';

/**
 * 替换文本中的占位符
 * @param text 包含占位符的文本，如 "按住{key}键"
 * @param replacements 替换映射，如 { key: "Ctrl" }
 * @returns 替换后的文本
 */
export function replaceTextPlaceholders(text: string, replacements: Record<string, string>): string {
  let result = text;
  for (const [key, value] of Object.entries(replacements)) {
    result = result.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
  }
  return result;
}

/**
 * 获取带有正确快捷键的频率调节提示文本
 * @param baseText 基础文本，包含 {key} 占位符
 * @returns 替换快捷键后的文本
 */
export function getFrequencyAdjustmentHintText(baseText: string): string {
  const keyDisplayName = getFrequencyAdjustmentKeyDisplayName();
  return replaceTextPlaceholders(baseText, { key: keyDisplayName });
}

/**
 * 创建一个 Vue 组合式函数，用于在组件中使用动态快捷键提示
 */
export function useKeyboardShortcutText() {
  /**
   * 获取频率调节提示文本
   * @param t i18n 翻译函数
   * @returns 动态的频率调节提示文本
   */
  const getFrequencyHint = (t: (key: string) => string): string => {
    const baseText = t('editor.eventProperties.frequencyAdjustmentHint');
    return getFrequencyAdjustmentHintText(baseText);
  };

  return {
    getFrequencyHint,
  };
}
