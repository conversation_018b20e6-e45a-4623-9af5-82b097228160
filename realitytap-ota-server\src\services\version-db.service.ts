import { config } from '@/config/server.config';
import {
  ChannelsConfig,
  PlatformRelease,
  ServerVersionInfo,
  UpdateCheckRequest,
  UpdateCheckResponse,
  VersionsConfig,
} from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import { VersionUtil } from '@/utils/version.util';
import { ChannelDAO } from '@/dao/channel.dao';
import { VersionDAO } from '@/dao/version.dao';
import { PlatformReleaseDAO } from '@/dao/platform-release.dao';

/**
 * 基于数据库的版本管理服务
 */
export class VersionDatabaseService {
  private channelDAO: ChannelDAO;
  private versionDAO: VersionDAO;
  private platformReleaseDAO: PlatformReleaseDAO;
  
  // 缓存
  private versionsCache: VersionsConfig | null = null;
  private channelsCache: ChannelsConfig | null = null;
  private lastCacheUpdate: number = 0;
  private readonly cacheTimeout = 60000; // 1分钟缓存

  constructor() {
    this.channelDAO = new ChannelDAO();
    this.versionDAO = new VersionDAO();
    this.platformReleaseDAO = new PlatformReleaseDAO();
  }

  /**
   * 检查版本更新
   */
  async checkForUpdates(request: UpdateCheckRequest): Promise<UpdateCheckResponse> {
    try {
      logger.info('Checking for updates (DB)', { request });

      // 验证请求参数
      this.validateUpdateRequest(request);

      // 获取渠道配置
      const channelConfig = await this.getChannelConfig(request.channel);
      if (!channelConfig || !channelConfig.enabled) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      // 获取最新的平台发布
      const latestRelease = await this.platformReleaseDAO.getLatestPlatformRelease(
        request.channel,
        request.platform,
        request.architecture
      );

      if (!latestRelease) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      // 获取版本信息
      const versionEntity = await this.versionDAO.getVersionById(latestRelease.version_id);
      if (!versionEntity) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      const latestVersion = versionEntity.version;

      // 检查是否有更新
      const hasUpdate = VersionUtil.hasUpdate(request.currentVersion, latestVersion);

      if (!hasUpdate) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      // 检查灰度发布
      const shouldReceiveUpdate = this.shouldReceiveUpdate(channelConfig, request);
      if (!shouldReceiveUpdate) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      // 构建下载 URL
      const encodedFilename = encodeURIComponent(latestRelease.filename);
      const downloadPath = `/api/v1/download/${encodedFilename}`;
      const downloadUrl = config.server.baseUrl ? `${config.server.baseUrl}${downloadPath}` : downloadPath;

      // 检查强制更新逻辑
      const isForced = await this.checkForceUpdate(request.channel, request.currentVersion, latestVersion);

      const response: UpdateCheckResponse = {
        hasUpdate: true,
        latestVersion,
        downloadUrl,
        releaseNotes: latestRelease.release_notes,
        fileSize: latestRelease.file_size,
        checksum: latestRelease.checksum,
        isForced,
        signature: latestRelease.signature,
        releaseDate: latestRelease.release_date,
      };

      logger.info('Update check completed (DB)', { request, response });
      return response;
    } catch (error) {
      logger.error('Failed to check for updates (DB)', { request, error });
      throw new Error(`Failed to check for updates: ${error}`);
    }
  }

  /**
   * 获取渠道配置
   */
  private async getChannelConfig(channelName: string) {
    const channel = await this.channelDAO.getChannelByName(channelName);
    if (!channel) return null;

    return {
      enabled: channel.enabled,
      description: channel.description,
      autoUpdate: channel.auto_update,
      rolloutPercentage: channel.rollout_percentage,
      priority: channel.priority
    };
  }

  /**
   * 获取版本配置（兼容旧格式）
   */
  async getVersionsConfig(): Promise<VersionsConfig> {
    const now = Date.now();
    if (this.versionsCache && now - this.lastCacheUpdate < this.cacheTimeout) {
      return this.versionsCache;
    }

    try {
      const channels = await this.channelDAO.getAllChannels();
      const versionsConfig: VersionsConfig = {
        channels: {},
        minimumVersions: {},
        deprecatedVersions: []
      };

      for (const channel of channels) {
        logger.info('Processing channel', { channelName: channel.name });
        const latestVersion = await this.versionDAO.getLatestVersionByChannel(channel.name);
        logger.info('Latest version for channel', { channelName: channel.name, latestVersion });
        if (!latestVersion) continue;

        const platformReleases = await this.platformReleaseDAO.getPlatformReleasesByVersionId(latestVersion.id);
        logger.info('Platform releases for version', { versionId: latestVersion.id, platformReleases });
        
        const platforms: Record<string, Record<string, PlatformRelease>> = {};
        
        for (const release of platformReleases) {
          if (!platforms[release.platform]) {
            platforms[release.platform] = {};
          }

          const platformData = platforms[release.platform];
          if (platformData) {
            platformData[release.architecture] = {
              filename: release.filename,
              size: release.file_size,
              checksum: release.checksum,
              releaseDate: release.release_date,
              releaseNotes: release.release_notes || '',
              signature: release.signature
            };
          }
        }

        versionsConfig.channels[channel.name] = {
          version: latestVersion.version,
          platforms
        };
      }

      this.versionsCache = versionsConfig;
      this.lastCacheUpdate = now;
      return versionsConfig;
    } catch (error) {
      throw new Error(`Failed to load versions config from database: ${error}`);
    }
  }

  /**
   * 获取渠道配置（兼容旧格式）
   */
  async getChannelsConfig(): Promise<ChannelsConfig> {
    const now = Date.now();
    if (this.channelsCache && now - this.lastCacheUpdate < this.cacheTimeout) {
      return this.channelsCache;
    }

    try {
      this.channelsCache = await this.channelDAO.getChannelsConfig();
      return this.channelsCache;
    } catch (error) {
      throw new Error(`Failed to load channels config from database: ${error}`);
    }
  }

  /**
   * 更新版本配置
   */
  async updateVersionsConfig(config: VersionsConfig): Promise<void> {
    try {
      // 解析配置并更新数据库
      for (const [channelName, channelInfo] of Object.entries(config.channels)) {
        if (!channelInfo || typeof channelInfo !== 'object' || !('platforms' in channelInfo)) {
          continue;
        }

        // 确保渠道存在
        let channel = await this.channelDAO.getChannelByName(channelName);
        if (!channel) {
          // 创建新渠道
          const channelId = await this.channelDAO.createChannel({
            name: channelName,
            enabled: true,
            rollout_percentage: 100,
            description: `Auto-created channel: ${channelName}`
          });
          // 重新获取创建的渠道
          channel = await this.channelDAO.getChannelByName(channelName);
        }

        if (!channel) {
          logger.error('Failed to create or get channel', { channelName });
          continue;
        }

        // 处理该渠道的版本信息
        const channelVersion = (channelInfo as any).version;
        if (channelVersion) {
          // 检查版本是否已存在
          let version = await this.versionDAO.getVersionByChannelAndVersion(channel.id, channelVersion);
          if (!version) {
            // 创建新版本
            const versionId = await this.versionDAO.createVersion({
              channel_id: channel.id,
              version: channelVersion
            });
            version = await this.versionDAO.getVersionById(versionId);
          }

          if (!version) {
            logger.error('Failed to create or get version', { channelName, version: channelVersion });
            continue;
          }

          // 处理平台发布信息
          const platforms = (channelInfo as any).platforms;
          if (platforms && typeof platforms === 'object') {
            for (const [platformName, platformInfo] of Object.entries(platforms)) {
              if (!platformInfo || typeof platformInfo !== 'object') {
                continue;
              }

              for (const [archName, archInfo] of Object.entries(platformInfo)) {
                if (!archInfo || typeof archInfo !== 'object') {
                  continue;
                }

                const archData = archInfo as any;
                if (!archData.filename) {
                  continue;
                }

                // 检查平台发布是否已存在
                let platformRelease = await this.platformReleaseDAO.getPlatformRelease(
                  version.id,
                  platformName,
                  archName
                );

                const releaseData = {
                  version_id: version.id,
                  platform: platformName,
                  architecture: archName,
                  filename: archData.filename,
                  file_size: archData.size || 0,
                  checksum: archData.checksum || '',
                  signature: archData.signature || null,
                  release_date: archData.releaseDate || new Date().toISOString(),
                  release_notes: archData.releaseNotes || ''
                };

                logger.info('Saving platform release data', {
                  channelName,
                  version: channelInfo.version,
                  platform: platformName,
                  architecture: archName,
                  filename: archData.filename,
                  hasSignature: !!archData.signature,
                  signatureLength: archData.signature ? archData.signature.length : 0,
                  signaturePreview: archData.signature ? archData.signature.substring(0, 50) + '...' : null,
                  isUpdate: !!platformRelease,
                  releaseData,
                });

                if (platformRelease) {
                  // 更新现有的平台发布
                  await this.platformReleaseDAO.updatePlatformRelease(platformRelease.id, releaseData);
                  logger.info('Updated existing platform release', {
                    releaseId: platformRelease.id,
                    filename: archData.filename,
                    hasSignature: !!archData.signature,
                  });
                } else {
                  // 创建新的平台发布
                  const newReleaseId = await this.platformReleaseDAO.createPlatformRelease(releaseData);
                  logger.info('Created new platform release', {
                    releaseId: newReleaseId,
                    filename: archData.filename,
                    hasSignature: !!archData.signature,
                  });
                }
              }
            }
          }
        }
      }

      this.clearCache();
      logger.info('Versions config updated in database');
    } catch (error) {
      logger.error('Failed to update versions config in database', { error });
      throw new Error(`Failed to update versions config in database: ${error}`);
    }
  }

  /**
   * 更新渠道配置
   */
  async updateChannelsConfig(config: ChannelsConfig): Promise<void> {
    try {
      await this.channelDAO.updateChannelsConfig(config);
      this.clearCache();
      logger.info('Channels config updated in database');
    } catch (error) {
      throw new Error(`Failed to update channels config in database: ${error}`);
    }
  }

  /**
   * 验证更新请求
   */
  private validateUpdateRequest(request: UpdateCheckRequest): void {
    if (!VersionUtil.isValid(request.currentVersion)) {
      throw new Error('Invalid current version format');
    }

    const supportedPlatforms = ['windows', 'macos', 'linux'];
    if (!supportedPlatforms.includes(request.platform)) {
      throw new Error(`Unsupported platform: ${request.platform}`);
    }

    const supportedArchitectures = ['x86_64', 'aarch64', 'x86'];
    if (!supportedArchitectures.includes(request.architecture)) {
      throw new Error(`Unsupported architecture: ${request.architecture}`);
    }

    const supportedChannels = ['stable', 'beta', 'alpha'];
    if (!supportedChannels.includes(request.channel)) {
      throw new Error(`Unsupported channel: ${request.channel}`);
    }
  }

  /**
   * 判断是否应该接收更新（灰度发布）
   */
  private shouldReceiveUpdate(channelConfig: any, request: UpdateCheckRequest): boolean {
    const rolloutPercentage = channelConfig.rolloutPercentage || 100;

    if (rolloutPercentage >= 100) {
      return true;
    }

    // 基于用户标识生成一个稳定的随机数
    const userIdentifier = `${request.platform}-${request.architecture}`;
    const hash = require('crypto').createHash('md5').update(userIdentifier).digest('hex');
    const hashNumber = parseInt(hash.substring(0, 8), 16);
    const percentage = (hashNumber % 100) + 1;

    return percentage <= rolloutPercentage;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.versionsCache = null;
    this.channelsCache = null;
    this.lastCacheUpdate = 0;
    logger.info('Version database service cache cleared');
  }

  /**
   * 获取所有可用版本
   */
  async getAvailableVersions(): Promise<VersionsConfig> {
    return this.getVersionsConfig();
  }

  /**
   * 获取渠道信息
   */
  async getChannelInfo(): Promise<ChannelsConfig> {
    return this.getChannelsConfig();
  }

  /**
   * 检查强制更新逻辑
   * 按优先级顺序：
   * 1. 最新版本检查：如果最新版本本身被标记为强制更新，则返回 true
   * 2. 中间版本检查：如果从当前版本到最新版本之间的任何版本被标记为强制更新，则返回 true
   * 3. 非强制更新：如果最新版本和所有中间版本都没有被标记为强制更新，则返回 false
   */
  private async checkForceUpdate(channelName: string, currentVersion: string, latestVersion: string): Promise<boolean> {
    try {
      logger.info('Checking force update logic', { channelName, currentVersion, latestVersion });

      // 1. 检查最新版本是否为强制更新
      const latestVersionEntity = await this.versionDAO.getVersionByChannelNameAndVersion(channelName, latestVersion);
      if (latestVersionEntity?.force_update) {
        logger.info('Latest version is marked as force update', { latestVersion });
        return true;
      }

      // 2. 检查中间版本是否有强制更新
      const hasForceUpdateInRange = await this.versionDAO.hasForceUpdateInRange(channelName, currentVersion, latestVersion);
      if (hasForceUpdateInRange) {
        logger.info('Found force update in version range', { currentVersion, latestVersion });
        return true;
      }

      // 3. 没有强制更新
      logger.info('No force update required', { currentVersion, latestVersion });
      return false;
    } catch (error) {
      logger.error('Error checking force update', { error, channelName, currentVersion, latestVersion });
      // 出错时默认不强制更新
      return false;
    }
  }

  /**
   * 创建新版本发布
   */
  async createVersionRelease(
    channelName: string,
    version: string,
    platformReleases: Array<{
      platform: string;
      architecture: string;
      filename: string;
      fileSize: number;
      checksum: string;
      signature?: string;
      releaseNotes?: string;
    }>,
    forceUpdate: boolean = false
  ): Promise<void> {
    try {
      // 获取渠道
      const channel = await this.channelDAO.getChannelByName(channelName);
      if (!channel) {
        throw new Error(`Channel not found: ${channelName}`);
      }

      // 创建版本
      const versionId = await this.versionDAO.createVersion({
        channel_id: channel.id,
        version,
        force_update: forceUpdate
      });

      // 创建平台发布
      for (const release of platformReleases) {
        await this.platformReleaseDAO.createPlatformRelease({
          version_id: versionId,
          platform: release.platform,
          architecture: release.architecture,
          filename: release.filename,
          file_size: release.fileSize,
          checksum: release.checksum,
          signature: release.signature,
          release_date: new Date().toISOString(),
          release_notes: release.releaseNotes
        });
      }

      this.clearCache();
      logger.info('Version release created', { channelName, version, platformCount: platformReleases.length });
    } catch (error) {
      throw new Error(`Failed to create version release: ${error}`);
    }
  }
}

// 导出单例实例
export const versionDatabaseService = new VersionDatabaseService();
