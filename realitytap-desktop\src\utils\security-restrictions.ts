/**
 * 生产环境安全限制模块
 * 在生产环境中禁用危险的键盘快捷键和浏览器功能
 */

import { logger, LogModule } from '@/utils/logger/logger';

/**
 * 危险按键配置
 * 注意：这个配置对象目前未使用，保留用于未来可能的配置化需求
 */
// @ts-ignore - 保留用于未来配置化需求
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const _DANGEROUS_KEYS = {
  // 刷新页面
  F5: 'F5',
  CTRL_R: 'KeyR',

  // 开发者工具
  F12: 'F12',
  CTRL_SHIFT_I: 'KeyI',
  CTRL_SHIFT_J: 'KeyJ',
  CTRL_SHIFT_C: 'KeyC',
  CTRL_U: 'KeyU',

  // 其他危险快捷键
  CTRL_SHIFT_R: 'KeyR', // 强制刷新
  CTRL_F5: 'F5', // 强制刷新
  ALT_F4: 'F4', // 关闭窗口（虽然 Tauri 可能会处理）
} as const;

/**
 * 安全限制管理器
 */
export class SecurityRestrictions {
  private isEnabled = false;
  private keydownHandler: ((event: KeyboardEvent) => void) | null = null;
  private contextmenuHandler: ((event: MouseEvent) => void) | null = null;
  private selectstartHandler: ((event: Event) => void) | null = null;
  private dragstartHandler: ((event: DragEvent) => void) | null = null;

  /**
   * 检查是否应该启用安全限制
   */
  private shouldEnableRestrictions(): boolean {
    // 只在生产环境启用
    return !import.meta.env.DEV;
  }

  /**
   * 检查按键是否为危险按键
   */
  private isDangerousKey(event: KeyboardEvent): boolean {
    const { key, code, ctrlKey, shiftKey, altKey } = event;

    // F5 键
    if (key === 'F5' || code === 'F5') {
      return true;
    }

    // F12 键
    if (key === 'F12' || code === 'F12') {
      return true;
    }

    // Ctrl+R (刷新)
    if (ctrlKey && !shiftKey && !altKey && (key === 'r' || key === 'R' || code === 'KeyR')) {
      return true;
    }

    // Ctrl+Shift+R (强制刷新)
    if (ctrlKey && shiftKey && !altKey && (key === 'r' || key === 'R' || code === 'KeyR')) {
      return true;
    }

    // Ctrl+Shift+I (开发者工具)
    if (ctrlKey && shiftKey && !altKey && (key === 'i' || key === 'I' || code === 'KeyI')) {
      return true;
    }

    // Ctrl+Shift+J (控制台)
    if (ctrlKey && shiftKey && !altKey && (key === 'j' || key === 'J' || code === 'KeyJ')) {
      return true;
    }

    // Ctrl+Shift+C (元素检查器)
    if (ctrlKey && shiftKey && !altKey && (key === 'c' || key === 'C' || code === 'KeyC')) {
      return true;
    }

    // Ctrl+U (查看源代码)
    if (ctrlKey && !shiftKey && !altKey && (key === 'u' || key === 'U' || code === 'KeyU')) {
      return true;
    }

    // Alt+F4 (关闭窗口)
    if (altKey && !ctrlKey && !shiftKey && (key === 'F4' || code === 'F4')) {
      return true;
    }

    return false;
  }

  /**
   * 键盘事件处理器
   */
  private handleKeyDown = (event: KeyboardEvent): void => {
    if (!this.isEnabled) return;

    if (this.isDangerousKey(event)) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();

      // 在开发环境下输出警告（虽然生产环境不会执行到这里）
      if (import.meta.env.DEV) {
        logger.warn(LogModule.GENERAL, '安全限制：已阻止危险按键', {
          key: event.key,
          code: event.code,
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        });
      }
    }
  };

  /**
   * 右键菜单事件处理器
   * 只阻止浏览器默认右键菜单，不阻止应用内自定义右键菜单
   */
  private handleContextMenu = (event: MouseEvent): void => {
    if (!this.isEnabled) return;

    // 检查是否是应用内的自定义右键菜单
    const target = event.target as HTMLElement;

    // 如果目标元素或其父元素有特定的类名或属性，说明是应用内的自定义右键菜单
    // 这些元素应该被允许显示右键菜单
    const allowedSelectors = [
      '.n-tree-node',           // Naive UI 树节点
      '.waveform-canvas',       // 波形画布
      '.haptic-file-explorer',  // 触觉文件浏览器
      '[data-allow-contextmenu="true"]', // 明确标记允许右键菜单的元素
    ];

    // 检查目标元素或其父元素是否匹配允许的选择器
    let currentElement: HTMLElement | null = target;
    while (currentElement) {
      for (const selector of allowedSelectors) {
        if (currentElement.matches && currentElement.matches(selector)) {
          // 允许应用内的自定义右键菜单
          return;
        }
      }
      currentElement = currentElement.parentElement;
    }

    // 只阻止不在允许列表中的右键菜单（主要是浏览器默认右键菜单）
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
  };

  /**
   * 文本选择事件处理器
   */
  private handleSelectStart = (event: Event): void => {
    if (!this.isEnabled) return;

    event.preventDefault();
    event.stopPropagation();
  };

  /**
   * 拖拽事件处理器
   */
  private handleDragStart = (event: DragEvent): void => {
    if (!this.isEnabled) return;

    event.preventDefault();
    event.stopPropagation();
  };

  /**
   * 启用安全限制
   */
  public enable(): void {
    if (!this.shouldEnableRestrictions()) {
      logger.info(LogModule.GENERAL, '开发环境：安全限制已禁用');
      return;
    }

    if (this.isEnabled) {
      logger.warn(LogModule.GENERAL, '安全限制已经启用');
      return;
    }

    this.isEnabled = true;

    // 添加键盘事件监听器
    this.keydownHandler = this.handleKeyDown;
    document.addEventListener('keydown', this.keydownHandler, { 
      capture: true, 
      passive: false 
    });

    // 添加右键菜单事件监听器
    this.contextmenuHandler = this.handleContextMenu;
    document.addEventListener('contextmenu', this.contextmenuHandler, { 
      capture: true, 
      passive: false 
    });

    // 添加文本选择事件监听器
    this.selectstartHandler = this.handleSelectStart;
    document.addEventListener('selectstart', this.selectstartHandler, { 
      capture: true, 
      passive: false 
    });

    // 添加拖拽事件监听器
    this.dragstartHandler = this.handleDragStart;
    document.addEventListener('dragstart', this.dragstartHandler, { 
      capture: true, 
      passive: false 
    });

    logger.info(LogModule.GENERAL, '生产环境：安全限制已启用');
  }

  /**
   * 禁用安全限制
   */
  public disable(): void {
    if (!this.isEnabled) {
      return;
    }

    this.isEnabled = false;

    // 移除事件监听器
    if (this.keydownHandler) {
      document.removeEventListener('keydown', this.keydownHandler, { capture: true });
      this.keydownHandler = null;
    }

    if (this.contextmenuHandler) {
      document.removeEventListener('contextmenu', this.contextmenuHandler, { capture: true });
      this.contextmenuHandler = null;
    }

    if (this.selectstartHandler) {
      document.removeEventListener('selectstart', this.selectstartHandler, { capture: true });
      this.selectstartHandler = null;
    }

    if (this.dragstartHandler) {
      document.removeEventListener('dragstart', this.dragstartHandler, { capture: true });
      this.dragstartHandler = null;
    }

    logger.info(LogModule.GENERAL, '安全限制已禁用');
  }

  /**
   * 获取当前状态
   */
  public getStatus(): { enabled: boolean; environment: string } {
    return {
      enabled: this.isEnabled,
      environment: import.meta.env.DEV ? 'development' : 'production'
    };
  }
}

// 创建全局实例
export const securityRestrictions = new SecurityRestrictions();

/**
 * 初始化安全限制
 * 应该在应用启动时调用
 */
export function initializeSecurityRestrictions(): void {
  securityRestrictions.enable();
}

/**
 * 检查当前环境是否启用了安全限制
 */
export function isSecurityRestrictionsEnabled(): boolean {
  return securityRestrictions.getStatus().enabled;
}
