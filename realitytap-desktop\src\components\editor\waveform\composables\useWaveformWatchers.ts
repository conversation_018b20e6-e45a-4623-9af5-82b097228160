import { watch, type Ref } from "vue";
import { checkForRealEventDataChanges } from "../utils/event-change-detector";
import { DEBUG_EXPERIMENTS } from "../config/waveform-constants";
import { waveformLogger, audioLogger } from "@/utils/logger/logger";

// 使用 ReturnType 来获取正确的 Store 类型
type FileWaveformEditorStore = ReturnType<typeof import("@/stores/haptics-editor-store").useFileWaveformEditorStore>;
type ProjectStore = ReturnType<typeof import("@/stores/haptics-project-store").useProjectStore>;

/**
 * 波形图监听器管理 Composable
 * 统一管理所有 watch 监听器，减少主组件的复杂度
 */

export interface WatchersConfig {
  // Store 实例
  waveformStore: FileWaveformEditorStore;
  projectStore: ProjectStore;

  // Props 数据（events已移除，直接使用store中的数据）
  audioAmplitudeData?: any | null;
  showAudioWaveform?: boolean | Ref<boolean>;
  isUsingCachedData?: boolean | Ref<boolean>;
  amplitudeDisplayMode?: string;
  totalEffectDuration: number;
  baselineDuration: number;
  availableParentWidth: number;
  audioDuration?: number | null;

  // 状态引用
  hasAudioData: Ref<boolean>;

  // 回调函数
  updateAndDrawCanvas: () => void;
  setAudioData: (data: any) => void;
  clearAudioData: () => void;
  getAmplitudeConfig: () => any;

  // 异步管理器
  asyncManager: {
    createTimeout: (fn: () => void, delay: number, label: string) => void;
  };
}

export interface WatcherCleanup {
  stopAllWatchers: () => void;
  getWatcherCount: () => number;
}

export function useWaveformWatchers(config: WatchersConfig): WatcherCleanup {
  const watchStoppers: (() => void)[] = [];

  // 注释：events监听器已移除，现在直接使用store中的数据
  // 不再需要从props同步events到store，因为数据已经在store中

  // 监听 store 中的事件变化，标记文件为未保存状态
  const stopStoreEventsWatcher = watch(
    () => config.waveformStore.events,
    (newEvents, oldEvents) => {
      if (!oldEvents) return;

      let hasRealDataChange: boolean;
      if (DEBUG_EXPERIMENTS.DISABLE_EVENT_CHANGE_DETECTION) {
        waveformLogger.debug("🔍 实验7: 禁用事件变化检测");
        hasRealDataChange = true;
      } else {
        hasRealDataChange = checkForRealEventDataChanges(newEvents, oldEvents);
      }

      if (hasRealDataChange) {
        const currentFileUuid = config.projectStore.selectedFileUuid;
        if (currentFileUuid) {
          config.projectStore.markFileAsUnsaved(currentFileUuid);
          // 🔧 修复：同步更新项目缓存中的事件数据
          config.projectStore.updateFileCacheEvents(currentFileUuid, newEvents);
          waveformLogger.debug(`文件 ${currentFileUuid} 标记为未保存状态并更新缓存 - 检测到真实数据变化`, {
            eventCount: newEvents?.length || 0
          });
        }
      } else {
        waveformLogger.debug(`跳过标记未保存状态 - 仅为选中状态变化或无实质变化`);
      }
    },
    { deep: true }
  );
  watchStoppers.push(stopStoreEventsWatcher);

  // 监听 Store 中的音频振幅数据变化（优先）和 props 中的音频数据（向后兼容）
  const stopAudioDataWatcher = watch(
    [
      () => config.waveformStore.audioAmplitudeData,
      () => config.audioAmplitudeData
    ],
    ([storeAudioData, propsAudioData], [oldStoreData, oldPropsData]) => {
      // 优先使用 Store 中的音频数据
      const newData = storeAudioData || propsAudioData;
      const oldData = oldStoreData || oldPropsData;

      if (newData === oldData) return;

      if (newData) {
        audioLogger.debug("接收到音频振幅数据", {
          samplesLength: newData?.samples?.length,
          source: storeAudioData ? "Store" : "Props"
        });
        if (typeof newData === "object" && newData.samples && Array.isArray(newData.samples) && newData.samples.length > 0) {
          config.setAudioData(newData);
          config.asyncManager.createTimeout(() => config.updateAndDrawCanvas(), 0, "音频数据设置后重绘");
        } else {
          audioLogger.warn("接收到无效的音频振幅数据，清理音频数据");
          config.clearAudioData();
        }
      } else {
        audioLogger.debug("清理音频振幅数据");
        config.clearAudioData();
        config.asyncManager.createTimeout(() => config.updateAndDrawCanvas(), 0, "音频数据清理后重绘");
      }
    },
    { immediate: true, deep: true }
  );
  watchStoppers.push(stopAudioDataWatcher);

  // 强制检查一次 Store 中的音频数据（用于初始化时的数据同步）
  const initialStoreData = config.waveformStore.audioAmplitudeData;
  if (initialStoreData && initialStoreData.samples && initialStoreData.samples.length > 0) {
    audioLogger.debug("初始化时发现 Store 中的音频数据，强制同步", {
      samplesCount: initialStoreData.samples.length,
      duration: initialStoreData.duration_ms
    });
    config.setAudioData(initialStoreData);
    config.asyncManager.createTimeout(() => config.updateAndDrawCanvas(), 100, "初始化音频数据同步重绘");
  }

  // 监听音频波形显示状态变化
  const stopShowAudioWatcher = watch(
    () => typeof config.showAudioWaveform === 'object' && config.showAudioWaveform !== null
      ? config.showAudioWaveform.value
      : config.showAudioWaveform,
    (newValue) => {
      audioLogger.debug("音频波形显示状态变化", { showAudio: newValue });
      config.asyncManager.createTimeout(() => config.updateAndDrawCanvas(), 0, "音频波形显示状态变化重绘");
    }
  );
  watchStoppers.push(stopShowAudioWatcher);

  // 监听缓存数据状态变化
  const stopCachedDataWatcher = watch(
    () => config.isUsingCachedData,
    (isUsingCached) => {
      if (isUsingCached && config.hasAudioData.value) {
        audioLogger.debug("切换到缓存数据，强制重绘音频波形");
        config.asyncManager.createTimeout(
          () => {
            if (typeof config.setAudioData === "function") {
              // 优先使用 Store 中的音频数据
              const storeAudioData = config.waveformStore.audioAmplitudeData;
              const audioData = storeAudioData || config.audioAmplitudeData;
              if (audioData) {
                config.setAudioData(audioData);
              }
            }
            config.updateAndDrawCanvas();
          },
          0,
          "缓存数据切换重绘"
        );
      }
    },
    { immediate: true }
  );
  watchStoppers.push(stopCachedDataWatcher);

  // 监听振幅显示模式变化
  const stopAmplitudeModeWatcher = watch(
    () => config.amplitudeDisplayMode,
    (newMode) => {
      if (config.hasAudioData.value) {
        audioLogger.debug("振幅显示模式变化:", newMode);
        config.asyncManager.createTimeout(
          () => {
            if (typeof config.setAudioData === "function") {
              // 优先使用 Store 中的音频数据
              const storeAudioData = config.waveformStore.audioAmplitudeData;
              const audioData = storeAudioData || config.audioAmplitudeData;
              if (audioData) {
                config.setAudioData(audioData);
              }
            }
            config.updateAndDrawCanvas();
          },
          0,
          "振幅显示模式变化重绘"
        );
      }
    }
  );
  watchStoppers.push(stopAmplitudeModeWatcher);

  // 监听画布更新相关属性变化
  const stopCanvasUpdateWatcher = watch(
    [
      () => config.totalEffectDuration,
      () => config.baselineDuration,
      () => config.availableParentWidth,
      () => config.waveformStore.events, // 直接监听store中的events
    ],
    (newValues, oldValues) => {
      // 添加详细的变化日志，帮助调试
      if (oldValues) {
        const [newDuration, newBaseline, newWidth, newEvents] = newValues;
        const [oldDuration, oldBaseline, oldWidth, oldEvents] = oldValues;

        if (newDuration !== oldDuration) {
          waveformLogger.debug(`总时长变化: ${oldDuration}ms -> ${newDuration}ms`);
        }
        if (newBaseline !== oldBaseline) {
          waveformLogger.debug(`基线时长变化: ${oldBaseline}ms -> ${newBaseline}ms`);
        }
        if (newWidth !== oldWidth) {
          waveformLogger.debug(`可用宽度变化: ${oldWidth}px -> ${newWidth}px`);
        }
        if (newEvents !== oldEvents) {
          waveformLogger.debug(`事件数据变化: ${oldEvents?.length || 0} -> ${newEvents?.length || 0}`);
        }
      }

      // 强制触发重绘，确保时长变化立即生效
      config.asyncManager.createTimeout(() => config.updateAndDrawCanvas(), 0, "画布属性变化重绘");
    },
    { deep: true, immediate: false }
  );
  watchStoppers.push(stopCanvasUpdateWatcher);

  // 【修复】专门监听 Store 中的总时长变化，确保时长调整后立即重绘
  const stopStoreTotalDurationWatcher = watch(
    () => config.waveformStore.currentTotalDuration,
    (newDuration, oldDuration) => {
      if (newDuration !== oldDuration) {
        waveformLogger.debug(`Store总时长监听器触发: ${oldDuration}ms -> ${newDuration}ms`);

        // 立即触发重绘，不使用异步延迟
        config.updateAndDrawCanvas();

        // 同时使用异步管理器确保重绘完成
        config.asyncManager.createTimeout(
          () => {
            waveformLogger.debug(`Store总时长变化后强制重绘完成: ${newDuration}ms`);
            config.updateAndDrawCanvas();
          },
          10,
          "Store总时长变化强制重绘"
        );
      }
    },
    { immediate: false }
  );
  watchStoppers.push(stopStoreTotalDurationWatcher);

  // 监听音频时长变化
  const stopAudioDurationWatcher = watch(
    () => config.audioDuration,
    (newAudioDuration, oldAudioDuration) => {
      if (newAudioDuration !== oldAudioDuration) {
        audioLogger.debug(`音频时长变化: ${oldAudioDuration}ms -> ${newAudioDuration}ms`);
        config.asyncManager.createTimeout(() => config.updateAndDrawCanvas(), 0, "音频时长变化重绘");
      }
    },
    { immediate: false }
  );
  watchStoppers.push(stopAudioDurationWatcher);

  // 监听属性调整状态变化
  const stopAdjustingPropertiesWatcher = watch(
    () => config.waveformStore.isAdjustingProperties,
    (isAdjusting) => {
      if (isAdjusting) {
        // 这里可以添加属性调整时的特殊处理逻辑
        waveformLogger.debug("开始属性调整模式");
      }
    }
  );
  watchStoppers.push(stopAdjustingPropertiesWatcher);

  // 清理函数
  const stopAllWatchers = () => {
    waveformLogger.debug(`停止 ${watchStoppers.length} 个监听器`);
    watchStoppers.forEach((stop, index) => {
      try {
        stop();
        waveformLogger.debug(`已停止监听器 ${index + 1}`);
      } catch (error) {
        waveformLogger.warn(`停止监听器 ${index + 1} 时出错:`, error);
      }
    });
    watchStoppers.length = 0;
  };

  const getWatcherCount = () => watchStoppers.length;

  return {
    stopAllWatchers,
    getWatcherCount,
  };
}
