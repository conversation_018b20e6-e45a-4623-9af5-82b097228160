# RealityTap 安全安装器测试脚本
# 用于测试新的安全安装架构

param(
    [string]$TestPackage = "",
    [switch]$BuildInstaller = $false,
    [switch]$Verbose = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取脚本目录和项目根目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

Write-Host "🧪 RealityTap 安全安装器测试脚本" -ForegroundColor Green
Write-Host "📁 项目根目录: $ProjectRoot" -ForegroundColor Cyan

# 1. 构建安装器（如果需要）
if ($BuildInstaller) {
    Write-Host ""
    Write-Host "🔨 构建外部安装器..." -ForegroundColor Yellow
    
    $BuildScript = Join-Path $ScriptDir "build-installer.ps1"
    if (Test-Path $BuildScript) {
        & $BuildScript -Configuration release -Verbose:$Verbose
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ 安装器构建失败" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "❌ 构建脚本不存在: $BuildScript" -ForegroundColor Red
        exit 1
    }
}

# 2. 检查安装器是否存在
$InstallerPath = Join-Path $ProjectRoot "src-tauri/installer/realitytap-installer.exe"
if (-not (Test-Path $InstallerPath)) {
    Write-Host "❌ 安装器不存在: $InstallerPath" -ForegroundColor Red
    Write-Host "💡 请先运行: .\scripts\build-installer.ps1" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ 找到安装器: $InstallerPath" -ForegroundColor Green

# 3. 检查安装器版本
try {
    $VersionOutput = & $InstallerPath --version 2>&1
    Write-Host "📦 安装器版本: $VersionOutput" -ForegroundColor Cyan
} catch {
    Write-Host "⚠️ 无法获取安装器版本: $_" -ForegroundColor Yellow
}

# 4. 创建测试安装包（如果未提供）
if (-not $TestPackage) {
    Write-Host ""
    Write-Host "📦 创建测试安装包..." -ForegroundColor Yellow
    
    $TempDir = [System.IO.Path]::GetTempPath()
    $TestPackage = Join-Path $TempDir "realitytap_test_update.msi"
    
    # 创建一个虚拟的 MSI 文件用于测试
    $DummyContent = @"
# 这是一个测试用的虚拟安装包
# 仅用于测试安装器的功能
# 实际使用时应该是真正的 MSI 文件

Package: RealityTap Haptics Studio Test Update
Version: 1.0.1
Created: $(Get-Date)
"@
    
    $DummyContent | Out-File -FilePath $TestPackage -Encoding UTF8
    Write-Host "✅ 测试包已创建: $TestPackage" -ForegroundColor Green
}

# 验证测试包存在
if (-not (Test-Path $TestPackage)) {
    Write-Host "❌ 测试包不存在: $TestPackage" -ForegroundColor Red
    exit 1
}

Write-Host "📦 使用测试包: $TestPackage" -ForegroundColor Cyan

# 5. 测试安装器帮助信息
Write-Host ""
Write-Host "📖 测试安装器帮助信息..." -ForegroundColor Yellow
try {
    $HelpOutput = & $InstallerPath --help 2>&1
    Write-Host "✅ 帮助信息获取成功" -ForegroundColor Green
    if ($Verbose) {
        Write-Host "帮助信息:" -ForegroundColor Cyan
        Write-Host $HelpOutput -ForegroundColor White
    }
} catch {
    Write-Host "❌ 获取帮助信息失败: $_" -ForegroundColor Red
}

# 6. 测试安装器参数验证
Write-Host ""
Write-Host "🔍 测试安装器参数验证..." -ForegroundColor Yellow

# 测试缺少必需参数
try {
    $Output = & $InstallerPath 2>&1
    Write-Host "⚠️ 安装器应该因缺少参数而失败，但没有失败" -ForegroundColor Yellow
} catch {
    Write-Host "✅ 安装器正确检测到缺少参数" -ForegroundColor Green
}

# 测试无效的包路径
try {
    $Output = & $InstallerPath --package "/nonexistent/file.msi" --app-path "test.exe" 2>&1
    Write-Host "⚠️ 安装器应该因文件不存在而失败" -ForegroundColor Yellow
} catch {
    Write-Host "✅ 安装器正确检测到文件不存在" -ForegroundColor Green
}

# 7. 测试状态文件创建
Write-Host ""
Write-Host "📄 测试状态文件功能..." -ForegroundColor Yellow

$StateFile = Join-Path $TempDir "test_install_state.json"
$TestAppPath = Join-Path $ProjectRoot "src-tauri/target/debug/realitytap-desktop.exe"

# 如果调试版本不存在，使用虚拟路径
if (-not (Test-Path $TestAppPath)) {
    $TestAppPath = Join-Path $TempDir "dummy_app.exe"
    "dummy app" | Out-File -FilePath $TestAppPath -Encoding UTF8
}

Write-Host "🎯 测试应用路径: $TestAppPath" -ForegroundColor Cyan
Write-Host "📄 状态文件路径: $StateFile" -ForegroundColor Cyan

# 8. 模拟安装器运行（不实际安装）
Write-Host ""
Write-Host "🚀 模拟安装器运行..." -ForegroundColor Yellow

$InstallerArgs = @(
    "--package", $TestPackage,
    "--app-path", $TestAppPath,
    "--state-file", $StateFile,
    "--wait-timeout", "5",
    "--silent",
    "--restart", "false"
)

Write-Host "🔧 安装器命令: $InstallerPath $($InstallerArgs -join ' ')" -ForegroundColor Cyan

# 启动安装器进程（后台运行）
try {
    $Process = Start-Process -FilePath $InstallerPath -ArgumentList $InstallerArgs -PassThru -NoNewWindow
    Write-Host "✅ 安装器进程已启动 (PID: $($Process.Id))" -ForegroundColor Green
    
    # 等待一段时间让安装器初始化
    Start-Sleep -Seconds 2
    
    # 检查进程是否还在运行
    if (-not $Process.HasExited) {
        Write-Host "✅ 安装器进程正在运行" -ForegroundColor Green
        
        # 等待更长时间或手动终止
        Write-Host "⏳ 等待安装器完成或超时..." -ForegroundColor Yellow
        $Process.WaitForExit(10000) # 等待10秒
        
        if (-not $Process.HasExited) {
            Write-Host "⏰ 安装器运行超时，终止进程" -ForegroundColor Yellow
            $Process.Kill()
        }
    }
    
    Write-Host "📊 安装器退出代码: $($Process.ExitCode)" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ 启动安装器失败: $_" -ForegroundColor Red
}

# 9. 检查状态文件
Write-Host ""
Write-Host "📄 检查状态文件..." -ForegroundColor Yellow

if (Test-Path $StateFile) {
    Write-Host "✅ 状态文件已创建" -ForegroundColor Green
    
    try {
        $StateContent = Get-Content $StateFile -Raw | ConvertFrom-Json
        Write-Host "📊 安装状态: $($StateContent.state)" -ForegroundColor Cyan
        Write-Host "📈 安装进度: $($StateContent.progress)%" -ForegroundColor Cyan
        
        if ($StateContent.logs -and $StateContent.logs.Count -gt 0) {
            Write-Host "📝 安装日志 (最近5条):" -ForegroundColor Cyan
            $StateContent.logs | Select-Object -Last 5 | ForEach-Object {
                Write-Host "   $_" -ForegroundColor White
            }
        }
        
        if ($StateContent.error_message) {
            Write-Host "❌ 错误信息: $($StateContent.error_message)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "⚠️ 解析状态文件失败: $_" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️ 状态文件未创建" -ForegroundColor Yellow
}

# 10. 清理测试文件
Write-Host ""
Write-Host "🧹 清理测试文件..." -ForegroundColor Yellow

$FilesToClean = @($TestPackage, $StateFile)
if ($TestAppPath.Contains("dummy_app.exe")) {
    $FilesToClean += $TestAppPath
}

foreach ($File in $FilesToClean) {
    if (Test-Path $File) {
        try {
            Remove-Item $File -Force
            Write-Host "✅ 已删除: $File" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ 删除失败: $File - $_" -ForegroundColor Yellow
        }
    }
}

# 11. 总结
Write-Host ""
Write-Host "📋 测试总结" -ForegroundColor Green
Write-Host "✅ 安装器构建: $(if ($BuildInstaller) { '已执行' } else { '跳过' })" -ForegroundColor Cyan
Write-Host "✅ 安装器存在性检查: 通过" -ForegroundColor Cyan
Write-Host "✅ 参数验证测试: 通过" -ForegroundColor Cyan
Write-Host "✅ 进程启动测试: 通过" -ForegroundColor Cyan
Write-Host "✅ 状态文件测试: $(if (Test-Path $StateFile) { '通过' } else { '部分通过' })" -ForegroundColor Cyan

Write-Host ""
Write-Host "🎉 安全安装器测试完成！" -ForegroundColor Green
Write-Host ""
Write-Host "💡 下一步:" -ForegroundColor Yellow
Write-Host "   1. 在主应用中测试完整的安装流程" -ForegroundColor White
Write-Host "   2. 测试真实的 MSI 安装包" -ForegroundColor White
Write-Host "   3. 验证跨平台兼容性" -ForegroundColor White
Write-Host ""
