import { DatabaseConnection } from '@/database/connection';
import { logger } from '@/utils/logger.util';

/**
 * 基础 DAO 类，提供通用的数据库操作方法
 */
export abstract class BaseDAO {
  protected db: DatabaseConnection;
  protected tableName: string;

  constructor(tableName: string) {
    this.db = DatabaseConnection.getInstance();
    this.tableName = tableName;
  }

  /**
   * 根据 ID 查找记录
   */
  protected async findById<T>(id: number | string): Promise<T | undefined> {
    const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
    return await this.db.get<T>(sql, [id]);
  }

  /**
   * 查找所有记录
   */
  protected async findAll<T>(orderBy?: string): Promise<T[]> {
    let sql = `SELECT * FROM ${this.tableName}`;
    if (orderBy) {
      sql += ` ORDER BY ${orderBy}`;
    }
    return await this.db.all<T>(sql);
  }

  /**
   * 根据条件查找记录
   */
  protected async findWhere<T>(
    conditions: Record<string, any>,
    orderBy?: string,
    limit?: number
  ): Promise<T[]> {
    const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
    const values = Object.values(conditions);
    
    let sql = `SELECT * FROM ${this.tableName} WHERE ${whereClause}`;
    if (orderBy) {
      sql += ` ORDER BY ${orderBy}`;
    }
    if (limit) {
      sql += ` LIMIT ${limit}`;
    }
    
    return await this.db.all<T>(sql, values);
  }

  /**
   * 根据条件查找单条记录
   */
  protected async findOneWhere<T>(conditions: Record<string, any>): Promise<T | undefined> {
    const results = await this.findWhere<T>(conditions, undefined, 1);
    return results[0];
  }

  /**
   * 插入记录
   */
  protected async insert(data: Record<string, any>): Promise<number> {
    const columns = Object.keys(data);
    const placeholders = columns.map(() => '?').join(', ');
    const values = Object.values(data);
    
    const sql = `INSERT INTO ${this.tableName} (${columns.join(', ')}) VALUES (${placeholders})`;
    const result = await this.db.run(sql, values);
    
    return result.lastID!;
  }

  /**
   * 更新记录
   */
  protected async update(id: number | string, data: Record<string, any>): Promise<boolean> {
    const columns = Object.keys(data);
    const setClause = columns.map(col => `${col} = ?`).join(', ');
    const values = [...Object.values(data), id];
    
    const sql = `UPDATE ${this.tableName} SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    const result = await this.db.run(sql, values);
    
    return result.changes! > 0;
  }

  /**
   * 删除记录
   */
  protected async delete(id: number | string): Promise<boolean> {
    const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
    const result = await this.db.run(sql, [id]);
    
    return result.changes! > 0;
  }

  /**
   * 根据条件删除记录
   */
  protected async deleteWhere(conditions: Record<string, any>): Promise<number> {
    const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
    const values = Object.values(conditions);
    
    const sql = `DELETE FROM ${this.tableName} WHERE ${whereClause}`;
    const result = await this.db.run(sql, values);
    
    return result.changes!;
  }

  /**
   * 计数记录
   */
  protected async count(conditions?: Record<string, any>): Promise<number> {
    let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    let values: any[] = [];
    
    if (conditions) {
      const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
      values = Object.values(conditions);
      sql += ` WHERE ${whereClause}`;
    }
    
    const result = await this.db.get<{ count: number }>(sql, values);
    return result?.count || 0;
  }

  /**
   * 检查记录是否存在
   */
  protected async exists(conditions: Record<string, any>): Promise<boolean> {
    const count = await this.count(conditions);
    return count > 0;
  }

  /**
   * 批量插入
   */
  protected async batchInsert(records: Record<string, any>[]): Promise<void> {
    if (records.length === 0) return;

    const firstRecord = records[0];
    if (!firstRecord) {
      throw new Error('First record is undefined');
    }

    const columns = Object.keys(firstRecord);
    const placeholders = columns.map(() => '?').join(', ');
    const sql = `INSERT INTO ${this.tableName} (${columns.join(', ')}) VALUES (${placeholders})`;

    await this.db.transaction(async () => {
      for (const record of records) {
        const values = columns.map(col => record[col]);
        await this.db.run(sql, values);
      }
    });
  }

  /**
   * 执行原始 SQL 查询
   */
  protected async query<T>(sql: string, params: any[] = []): Promise<T[]> {
    return await this.db.all<T>(sql, params);
  }

  /**
   * 执行原始 SQL 语句
   */
  protected async execute(sql: string, params: any[] = []): Promise<number> {
    const result = await this.db.run(sql, params);
    return result.changes || 0;
  }

  /**
   * 分页查询
   */
  protected async paginate<T>(
    conditions?: Record<string, any>,
    orderBy?: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<{ data: T[]; total: number; page: number; pageSize: number; totalPages: number }> {
    const offset = (page - 1) * pageSize;
    
    // 构建基础查询
    let baseWhere = '';
    let values: any[] = [];
    
    if (conditions) {
      baseWhere = ' WHERE ' + Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
      values = Object.values(conditions);
    }

    // 获取总数
    const countSql = `SELECT COUNT(*) as count FROM ${this.tableName}${baseWhere}`;
    const countResult = await this.db.get<{ count: number }>(countSql, values);
    const total = countResult?.count || 0;

    // 获取分页数据
    let dataSql = `SELECT * FROM ${this.tableName}${baseWhere}`;
    if (orderBy) {
      dataSql += ` ORDER BY ${orderBy}`;
    }
    dataSql += ` LIMIT ${pageSize} OFFSET ${offset}`;
    
    const data = await this.db.all<T>(dataSql, values);

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  /**
   * 开始事务
   */
  protected async transaction<T>(callback: () => Promise<T>): Promise<T> {
    return await this.db.transaction(async () => {
      return await callback();
    });
  }

  /**
   * 记录操作日志（仅在开发环境记录）
   */
  protected logOperation(operation: string, data?: any): void {
    // 只在开发环境记录DAO操作日志，减少生产环境的冗余日志
    // 排除 system_logs 表的操作，避免循环日志
    if (process.env.NODE_ENV === 'development' && this.tableName !== 'system_logs') {
      logger.debug(`DAO Operation: ${operation}`, {
        table: this.tableName,
        operation,
        data: data ? JSON.stringify(data) : undefined
      });
    }
  }
}
