/**
 * 波形架构管理器
 * 整合所有优化功能，提供统一的架构管理接口
 */

import { ref, computed, onBeforeUnmount } from "vue";
import type { RenderableEvent } from "@/types/haptic-editor";
import { useWaveformDataAccess } from "./useWaveformDataAccess";
// useWaveformPerformanceOptimizer 已移除
import { useWaveformConsistency, type ConsistencyConfig } from "./useWaveformConsistency";
import { useWaveformErrorBoundary, type ErrorBoundaryConfig } from "./useWaveformErrorBoundary";
import { waveformLogger } from "@/utils/logger/logger";

/**
 * 架构管理器配置
 */
export interface ArchitectureManagerConfig {
  // 基础配置
  fileUuid: string;
  enableLogging?: boolean;
  enableDebugMode?: boolean;
  
  // 性能优化配置已移除
  
  // 一致性检查配置
  consistency?: ConsistencyConfig;
  
  // 错误边界配置
  errorBoundary?: ErrorBoundaryConfig;
  
  // 健康检查配置
  healthCheck?: {
    enabled?: boolean;
    interval?: number;
    thresholds?: {
      maxRenderTime?: number;
      maxErrorRate?: number;
      maxMemoryUsage?: number;
    };
  };
}

/**
 * 系统健康状态
 */
export interface SystemHealth {
  overall: 'excellent' | 'good' | 'warning' | 'critical';
  performance: {
    status: 'good' | 'warning' | 'critical';
    averageRenderTime: number;
    optimizationRatio: number;
  };
  consistency: {
    status: 'good' | 'warning' | 'critical';
    issuesCount: number;
    successRate: string;
  };
  errors: {
    status: 'good' | 'warning' | 'critical';
    totalErrors: number;
    recoveryRate: string;
    criticalErrors: number;
  };
  memory: {
    status: 'good' | 'warning' | 'critical';
    usage: number;
    cacheSize: number;
  };
}

/**
 * 架构统计信息
 */
export interface ArchitectureStats {
  systemHealth: SystemHealth;
  performance: any;
  consistency: any;
  errors: any;
  uptime: number;
  lastHealthCheck: number;
}

/**
 * 波形架构管理器Hook
 */
export function useWaveformArchitectureManager(
  waveformStore: any,
  getEvents: () => RenderableEvent[],
  config: ArchitectureManagerConfig
) {
  const {
    fileUuid,
    enableLogging = false,
    enableDebugMode = false,
    // performanceOptimizer 已移除
    consistency: consistencyConfig = {},
    errorBoundary: errorConfig = {},
    healthCheck = {},
  } = config;
  
  const {
    enabled: healthCheckEnabled = true,
    interval: healthCheckInterval = 10000, // 10秒
    thresholds = {},
  } = healthCheck;
  
  const {
    maxRenderTime = 16,
    maxErrorRate = 0.1, // 10%
    maxMemoryUsage = 100, // MB
  } = thresholds;
  
  // 系统状态
  const isInitialized = ref(false);
  const startTime = ref(Date.now());
  const lastHealthCheck = ref(0);
  const systemHealth = ref<SystemHealth | null>(null);
  
  // 健康检查定时器
  let healthCheckTimer: number | null = null;
  
  // 初始化数据访问
  const dataAccess = useWaveformDataAccess(waveformStore);
  
  // 性能优化器已移除
  
  // 初始化一致性检查
  const consistencyChecker = useWaveformConsistency(
    waveformStore,
    {
      ...consistencyConfig,
      enableLogging: enableLogging || enableDebugMode,
    }
  );
  
  // 初始化错误边界
  const errorBoundary = useWaveformErrorBoundary(
    waveformStore,
    {
      ...errorConfig,
      enableLogging: enableLogging || enableDebugMode,
      onError: (error) => {
        if (enableLogging) {
          waveformLogger.error(`Error in ${fileUuid}:`, error);
        }

        // 触发健康检查
        performHealthCheck();

        // 调用用户定义的错误处理
        if (errorConfig.onError) {
          errorConfig.onError(error);
        }
      },
      onRecovery: (error) => {
        if (enableLogging) {
          waveformLogger.info(`Recovery in ${fileUuid}:`, error);
        }

        // 触发健康检查
        performHealthCheck();

        // 调用用户定义的恢复处理
        if (errorConfig.onRecovery) {
          errorConfig.onRecovery(error);
        }
      },
    }
  );
  
  /**
   * 计算内存使用情况
   */
  const calculateMemoryUsage = (): number => {
    try {
      // 简化的内存使用计算
      const events = getEvents();
      const eventMemory = events.length * 0.5; // 假设每个事件约0.5KB
      
      // 性能优化器已移除，使用简化的内存计算
      const cacheMemory = 0; // 缓存内存计算已移除
      
      return eventMemory + cacheMemory;
    } catch (error) {
      waveformLogger.warn('Failed to calculate memory usage:', error);
      return 0;
    }
  };
  
  /**
   * 评估性能状态
   */
  const evaluatePerformanceHealth = () => {
    // 性能优化器已移除，返回简化的性能数据
    const avgRenderTime = 0;
    const optimizationRatio = 0;
    
    let status: 'good' | 'warning' | 'critical' = 'good';
    
    if (avgRenderTime > maxRenderTime * 2) {
      status = 'critical';
    } else if (avgRenderTime > maxRenderTime) {
      status = 'warning';
    }
    
    return {
      status,
      averageRenderTime: avgRenderTime,
      optimizationRatio,
    };
  };
  
  /**
   * 评估一致性状态
   */
  const evaluateConsistencyHealth = () => {
    const stats = consistencyChecker.getConsistencyStats();
    const lastResult = consistencyChecker.lastCheckResult.value;
    
    let status: 'good' | 'warning' | 'critical' = 'good';
    const issuesCount = lastResult?.issues.length || 0;
    
    if (issuesCount > 5) {
      status = 'critical';
    } else if (issuesCount > 2) {
      status = 'warning';
    }
    
    return {
      status,
      issuesCount,
      successRate: stats.successRate,
    };
  };
  
  /**
   * 评估错误状态
   */
  const evaluateErrorHealth = () => {
    const stats = errorBoundary.getErrorStats();
    const errorRate = stats.totalErrors > 0 
      ? stats.totalErrors / (stats.totalErrors + 100) // 假设基准操作数
      : 0;
    
    let status: 'good' | 'warning' | 'critical' = 'good';
    
    if (stats.criticalErrors > 0 || errorRate > maxErrorRate * 2) {
      status = 'critical';
    } else if (errorRate > maxErrorRate) {
      status = 'warning';
    }
    
    return {
      status,
      totalErrors: stats.totalErrors,
      recoveryRate: stats.recoveryRate,
      criticalErrors: stats.criticalErrors,
    };
  };
  
  /**
   * 评估内存状态
   */
  const evaluateMemoryHealth = () => {
    const usage = calculateMemoryUsage();
    // 性能优化器已移除，使用简化的缓存大小计算
    const cacheSize = 0;
    
    let status: 'good' | 'warning' | 'critical' = 'good';
    
    if (usage > maxMemoryUsage * 2) {
      status = 'critical';
    } else if (usage > maxMemoryUsage) {
      status = 'warning';
    }
    
    return {
      status,
      usage,
      cacheSize,
    };
  };
  
  /**
   * 执行健康检查
   */
  const performHealthCheck = (): SystemHealth => {
    const performance = evaluatePerformanceHealth();
    const consistency = evaluateConsistencyHealth();
    const errors = evaluateErrorHealth();
    const memory = evaluateMemoryHealth();
    
    // 计算整体健康状态
    const statuses = [performance.status, consistency.status, errors.status, memory.status];
    let overall: SystemHealth['overall'] = 'excellent';
    
    if (statuses.includes('critical')) {
      overall = 'critical';
    } else if (statuses.includes('warning')) {
      overall = 'warning';
    } else if (statuses.every(s => s === 'good')) {
      overall = 'good';
    }
    
    const health: SystemHealth = {
      overall,
      performance,
      consistency,
      errors,
      memory,
    };
    
    systemHealth.value = health;
    lastHealthCheck.value = Date.now();
    
    if (enableLogging && overall !== 'excellent' && overall !== 'good') {
      waveformLogger.warn(`System health: ${overall}`, health);
    }
    
    return health;
  };
  
  /**
   * 获取架构统计信息
   */
  const getArchitectureStats = (): ArchitectureStats => {
    return {
      systemHealth: systemHealth.value || performHealthCheck(),
      performance: {}, // 性能统计已移除
      consistency: consistencyChecker.getConsistencyStats(),
      errors: errorBoundary.getErrorStats(),
      uptime: Date.now() - startTime.value,
      lastHealthCheck: lastHealthCheck.value,
    };
  };
  
  /**
   * 优化渲染（统一入口）
   */
  const optimizeRendering = () => {
    try {
      // 执行一致性检查
      const events = getEvents();
      const consistencyResult = consistencyChecker.performConsistencyCheck(events);
      
      if (!consistencyResult.isConsistent && enableLogging) {
        waveformLogger.warn(`Consistency issues found:`, consistencyResult.issues);
      }
      
      // 性能优化器已移除，返回简化的优化结果
      return {
        eventsToRender: getEvents(),
        optimizationApplied: false,
        renderingStrategy: 'full' as const,
      };
    } catch (error) {
      // 错误处理
      errorBoundary.handleError(error as Error, 'rendering', {
        events: getEvents(),
        performConsistencyCheck: consistencyChecker.performConsistencyCheck,
        resetDrawState: () => {}, // 需要从外部传入
        clearAllCache: () => {}, // 需要从外部传入
        smartDrawWaveform: () => {}, // 需要从外部传入
      });
      
      // 返回安全的默认值
      return {
        eventsToRender: [],
        shouldRender: false,
        renderingMode: 'cached' as const,
        performanceInfo: {
          totalEvents: 0,
          renderedEvents: 0,
          optimizationRatio: 0,
          processingTime: 0,
          changeDetected: false,
        },
      };
    }
  };
  
  /**
   * 重置所有系统
   */
  const resetAllSystems = () => {
    // performanceOptimizer.resetOptimizer() 已移除
    consistencyChecker.resetConsistencyStats();
    errorBoundary.clearErrorHistory();
    systemHealth.value = null;
    lastHealthCheck.value = 0;
    
    if (enableLogging) {
      waveformLogger.debug(`All systems reset for ${fileUuid}`);
    }
  };
  
  /**
   * 初始化系统
   */
  const initializeSystem = () => {
    if (isInitialized.value) return;
    
    // 执行初始健康检查
    performHealthCheck();
    
    // 启动定期健康检查
    if (healthCheckEnabled) {
      healthCheckTimer = setInterval(() => {
        performHealthCheck();
      }, healthCheckInterval);
    }
    
    isInitialized.value = true;
    
    if (enableLogging) {
      waveformLogger.debug(`System initialized for ${fileUuid}`);
    }
  };
  
  /**
   * 清理系统
   */
  const cleanupSystem = () => {
    if (healthCheckTimer) {
      clearInterval(healthCheckTimer);
      healthCheckTimer = null;
    }
    
    isInitialized.value = false;
    
    if (enableLogging) {
      waveformLogger.debug(`System cleaned up for ${fileUuid}`);
    }
  };
  
  // 自动初始化
  initializeSystem();
  
  // 清理
  onBeforeUnmount(() => {
    cleanupSystem();
  });
  
  return {
    // 主要功能
    optimizeRendering,
    performHealthCheck,
    
    // 数据访问
    dataAccess,
    
    // 子系统访问（performanceOptimizer已移除）
    consistencyChecker,
    errorBoundary,
    
    // 状态访问
    systemHealth: computed(() => systemHealth.value),
    isInitialized: computed(() => isInitialized.value),
    
    // 统计信息
    getArchitectureStats,
    architectureStats: computed(() => getArchitectureStats()),
    
    // 管理功能
    resetAllSystems,
    initializeSystem,
    cleanupSystem,
    
    // 配置信息
    config: computed(() => ({
      fileUuid,
      enableLogging,
      enableDebugMode,
      healthCheckEnabled,
      uptime: Date.now() - startTime.value,
    })),
  };
}
