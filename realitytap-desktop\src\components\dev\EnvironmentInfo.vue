<!--
  环境信息组件
  显示当前运行环境的详细信息
-->
<template>
  <div class="env-info">
    <n-card title="环境变量">
      <pre>{{ envInfo }}</pre>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard } from "naive-ui";

const envInfo = JSON.stringify(
  {
    NODE_ENV: import.meta.env.NODE_ENV,
    DEV: import.meta.env.DEV,
    PROD: import.meta.env.PROD,
    VITE_LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL,
    VITE_ENABLE_PERFORMANCE_LOG: import.meta.env.VITE_ENABLE_PERFORMANCE_LOG,
    VITE_ENABLE_DRAG_LOG: import.meta.env.VITE_ENABLE_DRAG_LOG,
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine,
    hardwareConcurrency: navigator.hardwareConcurrency,
    maxTouchPoints: navigator.maxTouchPoints,
  },
  null,
  2
);
</script>

<style scoped>
.env-info pre {
  background: var(--code-color);
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
