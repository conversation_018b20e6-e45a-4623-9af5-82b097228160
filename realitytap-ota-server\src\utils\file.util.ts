import { config } from '@/config/server.config';
import { logger } from '@/utils/logger.util';
import crypto from 'crypto';
import fs from 'fs-extra';
import path from 'path';

/**
 * 文件操作工具类
 */
export class FileUtil {
  /**
   * 检查文件是否存在
   * @param filePath 文件路径
   * @returns 是否存在
   */
  static async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取文件大小
   * @param filePath 文件路径
   * @returns 文件大小（字节）
   */
  static async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      throw new Error(`Failed to get file size: ${error}`);
    }
  }

  /**
   * 获取文件信息
   * @param filePath 文件路径
   * @returns 文件信息
   */
  static async getFileInfo(filePath: string): Promise<{
    size: number;
    mtime: Date;
    ctime: Date;
    isFile: boolean;
    isDirectory: boolean;
  }> {
    try {
      const stats = await fs.stat(filePath);
      return {
        size: stats.size,
        mtime: stats.mtime,
        ctime: stats.ctime,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
      };
    } catch (error) {
      throw new Error(`Failed to get file info: ${error}`);
    }
  }

  /**
   * 确保目录存在
   * @param dirPath 目录路径
   */
  static async ensureDir(dirPath: string): Promise<void> {
    try {
      await fs.ensureDir(dirPath);
    } catch (error) {
      throw new Error(`Failed to ensure directory: ${error}`);
    }
  }

  /**
   * 安全地读取 JSON 文件
   * @param filePath 文件路径
   * @returns JSON 对象
   */
  static async readJSON<T = any>(filePath: string): Promise<T> {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      return JSON.parse(content) as T;
    } catch (error) {
      throw new Error(`Failed to read JSON file: ${error}`);
    }
  }

  /**
   * 安全地写入 JSON 文件
   * @param filePath 文件路径
   * @param data 数据
   */
  static async writeJSON(filePath: string, data: any): Promise<void> {
    try {
      await this.ensureDir(path.dirname(filePath));
      await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
    } catch (error) {
      throw new Error(`Failed to write JSON file: ${error}`);
    }
  }

  /**
   * 原子性地写入 JSON 文件（先写入临时文件，再重命名）
   * @param filePath 文件路径
   * @param data 数据
   */
  static async writeJSONAtomic(filePath: string, data: any): Promise<void> {
    const tempPath = `${filePath}.tmp`;
    const maxRetries = 3;
    const baseDelay = 100; // 基础延迟 100ms

    try {
      await this.ensureDir(path.dirname(filePath));
      await fs.writeFile(tempPath, JSON.stringify(data, null, 2), 'utf8');

      // 尝试重命名，在 Windows 环境下添加重试机制
      let lastError: Error | null = null;
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          await fs.rename(tempPath, filePath);
          return; // 成功，直接返回
        } catch (error) {
          lastError = error as Error;

          // 检查是否是权限错误（EPERM）或文件被占用错误（EBUSY）
          const isRetryableError =
            lastError.message.includes('EPERM') ||
            lastError.message.includes('EBUSY') ||
            lastError.message.includes('operation not permitted') ||
            lastError.message.includes('resource busy');

          if (!isRetryableError || attempt === maxRetries) {
            throw lastError;
          }

          // 在 Windows 环境下，尝试使用复制+删除的方式作为备选方案
          if (process.platform === 'win32' && attempt === maxRetries - 1) {
            try {
              await fs.copy(tempPath, filePath);
              await fs.remove(tempPath);
              return; // 成功，直接返回
            } catch (copyError) {
              // 如果复制也失败，继续抛出原始错误
            }
          }

          // 等待一段时间后重试，延迟时间递增
          const delay = baseDelay * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      throw lastError;
    } catch (error) {
      // 清理临时文件
      try {
        await fs.remove(tempPath);
      } catch {
        // 忽略清理错误
      }
      throw new Error(`Failed to write JSON file atomically: ${error}`);
    }
  }

  /**
   * 复制文件
   * @param src 源文件路径
   * @param dest 目标文件路径
   */
  static async copyFile(src: string, dest: string): Promise<void> {
    try {
      await this.ensureDir(path.dirname(dest));
      await fs.copy(src, dest);
    } catch (error) {
      throw new Error(`Failed to copy file: ${error}`);
    }
  }

  /**
   * 移动文件
   * @param src 源文件路径
   * @param dest 目标文件路径
   */
  static async moveFile(src: string, dest: string): Promise<void> {
    try {
      await this.ensureDir(path.dirname(dest));
      await fs.move(src, dest);
    } catch (error) {
      throw new Error(`Failed to move file: ${error}`);
    }
  }

  /**
   * 删除文件或目录
   * @param filePath 文件路径
   */
  static async remove(filePath: string): Promise<void> {
    try {
      await fs.remove(filePath);
    } catch (error) {
      throw new Error(`Failed to remove file: ${error}`);
    }
  }

  /**
   * 读取文件内容
   * @param filePath 文件路径
   * @param encoding 编码格式
   * @returns 文件内容
   */
  static async readFile(filePath: string, encoding: BufferEncoding = 'utf8'): Promise<string> {
    try {
      return await fs.readFile(filePath, encoding);
    } catch (error) {
      throw new Error(`Failed to read file: ${error}`);
    }
  }

  /**
   * 追加内容到文件
   * @param filePath 文件路径
   * @param content 要追加的内容
   * @param encoding 编码格式
   */
  static async appendFile(filePath: string, content: string, encoding: BufferEncoding = 'utf8'): Promise<void> {
    try {
      await fs.appendFile(filePath, content, encoding);
    } catch (error) {
      throw new Error(`Failed to append to file: ${error}`);
    }
  }

  /**
   * 列出目录中的文件
   * @param dirPath 目录路径
   * @param recursive 是否递归
   * @returns 文件列表
   */
  static async listFiles(dirPath: string, recursive: boolean = false): Promise<string[]> {
    try {
      if (!(await this.exists(dirPath))) {
        return [];
      }

      const files: string[] = [];
      const items = await fs.readdir(dirPath);

      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stats = await fs.stat(itemPath);

        if (stats.isFile()) {
          files.push(itemPath);
        } else if (stats.isDirectory() && recursive) {
          const subFiles = await this.listFiles(itemPath, true);
          files.push(...subFiles);
        }
      }

      return files;
    } catch (error) {
      throw new Error(`Failed to list files: ${error}`);
    }
  }

  /**
   * 获取文件扩展名
   * @param filePath 文件路径
   * @returns 扩展名
   */
  static getExtension(filePath: string): string {
    return path.extname(filePath).toLowerCase();
  }

  /**
   * 检查文件类型是否允许
   * @param filePath 文件路径
   * @returns 是否允许
   */
  static isAllowedFileType(filePath: string): boolean {
    const extension = this.getExtension(filePath);
    return config.download.allowedFileTypes.includes(extension);
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化的大小
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 解析文件大小字符串
   * @param sizeStr 大小字符串（如 "100MB"）
   * @returns 字节数
   */
  static parseFileSize(sizeStr: string): number {
    const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)$/i);
    if (!match) {
      throw new Error('Invalid file size format');
    }

    const [, sizeValue, unit] = match;
    const size = parseFloat(sizeValue || '0');
    const multipliers: Record<string, number> = {
      B: 1,
      KB: 1024,
      MB: 1024 * 1024,
      GB: 1024 * 1024 * 1024,
      TB: 1024 * 1024 * 1024 * 1024,
    };

    const multiplier = multipliers[(unit || 'B').toUpperCase()];
    if (!multiplier) {
      throw new Error('Invalid file size unit');
    }

    return Math.floor(size * multiplier);
  }

  /**
   * 清理旧文件
   * @param dirPath 目录路径
   * @param maxAge 最大年龄（毫秒）
   */
  static async cleanupOldFiles(dirPath: string, maxAge: number): Promise<number> {
    try {
      if (!(await this.exists(dirPath))) {
        return 0;
      }

      const files = await this.listFiles(dirPath, true);
      const now = Date.now();
      let deletedCount = 0;

      for (const filePath of files) {
        try {
          const stats = await fs.stat(filePath);
          const age = now - stats.mtime.getTime();

          if (age > maxAge) {
            await fs.remove(filePath);
            deletedCount++;
            logger.info(`Deleted old file: ${filePath}`);
          }
        } catch (error) {
          logger.warn(`Failed to process file during cleanup: ${filePath}`, { error });
        }
      }

      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup old files', { dirPath, error });
      return 0;
    }
  }

  /**
   * 计算文件校验和
   * @param filePath 文件路径
   * @param algorithm 哈希算法（默认 sha256）
   * @returns 文件校验和
   */
  static async calculateChecksum(filePath: string, algorithm: string = 'sha256'): Promise<string> {
    try {
      const hash = crypto.createHash(algorithm);
      const stream = fs.createReadStream(filePath);

      return new Promise((resolve, reject) => {
        stream.on('data', data => {
          hash.update(data);
        });

        stream.on('end', () => {
          resolve(hash.digest('hex'));
        });

        stream.on('error', error => {
          reject(error);
        });
      });
    } catch (error) {
      throw new Error(`Failed to calculate checksum: ${error}`);
    }
  }

  /**
   * 验证文件校验和
   * @param filePath 文件路径
   * @param expectedChecksum 期望的校验和
   * @param algorithm 哈希算法（默认 sha256）
   * @returns 是否匹配
   */
  static async verifyChecksum(
    filePath: string,
    expectedChecksum: string,
    algorithm: string = 'sha256',
  ): Promise<boolean> {
    try {
      const actualChecksum = await this.calculateChecksum(filePath, algorithm);
      return actualChecksum.toLowerCase() === expectedChecksum.toLowerCase();
    } catch (error) {
      logger.error('Failed to verify checksum', { filePath, error });
      return false;
    }
  }
}
