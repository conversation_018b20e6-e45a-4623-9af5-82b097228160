import type { RenderableContinuousEvent, RenderableEvent, RenderableTransientEvent } from "@/types/haptic-editor";
import type { Ref } from "vue";
import type { DragTarget } from "./useDragState";
import { MIN_CONTINUOUS_DURATION } from "../config/waveform-constants";
import { MIN_CURVE_POINT_INTERVAL_MS } from "../config/waveform-constants";

/**
 * 高效的单个事件深度克隆（避免JSON序列化）
 */
function deepCloneEvent(event: RenderableEvent): RenderableEvent {
  const clonedEvent: any = {
    id: event.id,
    startTime: event.startTime,
    type: event.type
  };

  if (event.type === 'transient') {
    const transientEvent = event as any;
    clonedEvent.intensity = transientEvent.intensity;
    clonedEvent.frequency = transientEvent.frequency;
    clonedEvent.peakTime = transientEvent.peakTime;
    clonedEvent.stopTime = transientEvent.stopTime;

    // 克隆其他可能的属性
    if (transientEvent.waveform) clonedEvent.waveform = transientEvent.waveform;
    if (transientEvent.sharpness !== undefined) clonedEvent.sharpness = transientEvent.sharpness;
    if (transientEvent.width !== undefined) clonedEvent.width = transientEvent.width;

  } else if (event.type === 'continuous') {
    const continuousEvent = event as any;
    clonedEvent.eventIntensity = continuousEvent.eventIntensity;
    clonedEvent.eventFrequency = continuousEvent.eventFrequency;
    clonedEvent.duration = continuousEvent.duration;
    clonedEvent.stopTime = continuousEvent.stopTime;

    // 深度克隆curves数组
    if (continuousEvent.curves && Array.isArray(continuousEvent.curves)) {
      clonedEvent.curves = continuousEvent.curves.map((curve: any) => ({
        timeOffset: curve.timeOffset,
        drawIntensity: curve.drawIntensity,
        rawIntensity: curve.rawIntensity,
        relativeCurveFrequency: curve.relativeCurveFrequency,
        curveFrequency: curve.curveFrequency,
        // 克隆其他可能的属性
        ...(curve.intensity !== undefined && { intensity: curve.intensity }),
        ...(curve.frequency !== undefined && { frequency: curve.frequency })
      }));
    }

    // 克隆其他可能的属性
    if (continuousEvent.waveform) clonedEvent.waveform = continuousEvent.waveform;
    if (continuousEvent.originalEventDuration !== undefined) clonedEvent.originalEventDuration = continuousEvent.originalEventDuration;
  }

  return clonedEvent as RenderableEvent;
}

/**
 * 验证和修复Curve点时间分布，确保相邻点之间至少有最小间隔
 */
export function validateAndFixCurveTimeDistribution(curves: any[], newDuration: number): any[] {
  if (!curves || curves.length <= 2) return curves;

  const fixedCurves = [...curves];
  const lastIndex = fixedCurves.length - 1;

  // 确保第一个和最后一个点的时间偏移正确
  fixedCurves[0].timeOffset = 0;
  fixedCurves[lastIndex].timeOffset = newDuration;

  // 修复中间点的时间分布
  for (let i = 1; i < lastIndex; i++) {
    const prevTimeOffset = fixedCurves[i - 1].timeOffset;
    const nextTimeOffset = fixedCurves[i + 1] ? fixedCurves[i + 1].timeOffset : newDuration;

    // 确保当前点与前一个点至少有最小间隔
    let minTimeOffset = prevTimeOffset + MIN_CURVE_POINT_INTERVAL_MS;

    // 确保当前点与后一个点至少有最小间隔
    let maxTimeOffset = nextTimeOffset - MIN_CURVE_POINT_INTERVAL_MS;

    // 如果没有足够的空间，按比例分配
    if (minTimeOffset >= maxTimeOffset) {
      const availableSpace = nextTimeOffset - prevTimeOffset;
      const segmentSize = Math.max(MIN_CURVE_POINT_INTERVAL_MS, Math.floor(availableSpace / 2));
      minTimeOffset = prevTimeOffset + segmentSize;
      maxTimeOffset = nextTimeOffset - segmentSize;
    }

    // 限制当前点的时间偏移在有效范围内
    fixedCurves[i].timeOffset = Math.max(minTimeOffset, Math.min(maxTimeOffset, fixedCurves[i].timeOffset));
  }

  return fixedCurves;
}

/**
 * 拖拽预览事件管理 composable
 * 负责创建拖拽过程中的预览事件
 */
export function useDragPreview(
  draggedEvent: Ref<RenderableEvent | null>,
  draggingTarget: Ref<DragTarget | null>,
  draggedCurveIndex: Ref<number>,
  currentDraggedTimeOffset: Ref<number>,
  currentDraggedIntensity: Ref<number>,
  currentDraggedRelativeFrequency: Ref<number>,
  isFrequencyAdjustmentKeyPressed: Ref<boolean>
) {
  // 创建拖拽预览事件
  const createPreviewEvent = (): RenderableEvent | undefined => {
    if (!draggedEvent.value) return undefined;

    // 创建预览事件的深拷贝，应用拖拽偏移
    const previewEvent = deepCloneEvent(draggedEvent.value);

    if (draggingTarget.value === "event") {
      // 整个事件拖拽
      const timeOffset = currentDraggedTimeOffset.value;
      previewEvent.startTime = Math.max(0, draggedEvent.value.startTime + timeOffset);
      previewEvent.stopTime = Math.max(previewEvent.startTime + 1, draggedEvent.value.stopTime + timeOffset);

      if (previewEvent.type === "transient") {
        (previewEvent as RenderableTransientEvent).peakTime = Math.max(
          previewEvent.startTime,
          Math.min(previewEvent.stopTime, (draggedEvent.value as RenderableTransientEvent).peakTime + timeOffset)
        );
      }
    } else if (draggingTarget.value === "transientPeak") {
      // 瞬态事件峰值点拖拽 - 只改变强度
      if (previewEvent.type === "transient") {
        (previewEvent as RenderableTransientEvent).intensity = Math.max(0, Math.min(100, currentDraggedIntensity.value));
      }
    } else if (draggingTarget.value === "continuousCurvePoint") {
      // 连续事件曲线点拖拽
      if (previewEvent.type === "continuous") {
        const continuousPreview = previewEvent as RenderableContinuousEvent;
        const curveIndex = draggedCurveIndex.value;

        // 特殊处理：第一个Curve点的水平拖拽（调整事件startTime和duration）
        if (curveIndex === 0 && !isFrequencyAdjustmentKeyPressed.value) {
          handleFirstCurvePointDrag(continuousPreview);
        } else if (curveIndex === continuousPreview.curves.length - 1 && !isFrequencyAdjustmentKeyPressed.value) {
          handleLastCurvePointDrag(continuousPreview);
        } else if (curveIndex >= 0 && curveIndex < continuousPreview.curves.length) {
          handleRegularCurvePointDrag(continuousPreview, curveIndex);
        }
      }
    }

    return previewEvent;
  };

  // 处理第一个Curve点的拖拽
  const handleFirstCurvePointDrag = (continuousPreview: RenderableContinuousEvent) => {
    const timeOffset = currentDraggedTimeOffset.value;
    const newStartTime = Math.max(0, draggedEvent.value!.startTime + timeOffset);
    const originalEndTime = draggedEvent.value!.stopTime;
    const newDuration = Math.max(MIN_CONTINUOUS_DURATION, originalEndTime - newStartTime);

    // 更新事件的时间范围
    continuousPreview.startTime = newStartTime;
    continuousPreview.stopTime = originalEndTime;
    continuousPreview.duration = newDuration;

    // 重新计算所有Curve点的时间偏移
    if (continuousPreview.curves && continuousPreview.curves.length > 0) {
      recalculateCurveTimeOffsets(continuousPreview, newDuration);
    }
  };

  // 处理最后一个Curve点的拖拽
  const handleLastCurvePointDrag = (continuousPreview: RenderableContinuousEvent) => {
    const durationChange = currentDraggedTimeOffset.value;
    const originalStartTime = draggedEvent.value!.startTime;
    const newDuration = Math.max(MIN_CONTINUOUS_DURATION, (draggedEvent.value as RenderableContinuousEvent).duration + durationChange);
    const newEndTime = originalStartTime + newDuration;

    // 更新事件的时间范围
    continuousPreview.startTime = originalStartTime;
    continuousPreview.stopTime = newEndTime;
    continuousPreview.duration = newDuration;

    // 重新计算所有Curve点的时间偏移
    if (continuousPreview.curves && continuousPreview.curves.length > 0) {
      recalculateCurveTimeOffsets(continuousPreview, newDuration);
    }
  };

  // 处理常规Curve点的拖拽
  const handleRegularCurvePointDrag = (continuousPreview: RenderableContinuousEvent, curveIndex: number) => {
    const originalCurve = draggedEvent.value!.type === "continuous" ? (draggedEvent.value as RenderableContinuousEvent).curves[curveIndex] : null;

    if (originalCurve) {
      const updatedCurve = { ...originalCurve };

      if (isFrequencyAdjustmentKeyPressed.value) {
        // 频率调节键+拖拽：调整频率
        updatedCurve.relativeCurveFrequency = currentDraggedRelativeFrequency.value;
        updatedCurve.curveFrequency = currentDraggedRelativeFrequency.value + continuousPreview.eventFrequency;
      } else {
        // 普通拖拽：调整位置和强度
        if (curveIndex === continuousPreview.curves.length - 1) {
          updatedCurve.drawIntensity = 0;
          updatedCurve.timeOffset = continuousPreview.duration;
        } else {
          updatedCurve.drawIntensity = Math.max(0, Math.min(100, currentDraggedIntensity.value));
          updatedCurve.timeOffset = Math.max(0, currentDraggedTimeOffset.value);
        }
      }

      continuousPreview.curves[curveIndex] = updatedCurve;
    }
  };

  // 重新计算Curve点时间偏移
  const recalculateCurveTimeOffsets = (continuousPreview: RenderableContinuousEvent, newDuration: number) => {
    const originalDuration = (draggedEvent.value as RenderableContinuousEvent).duration;
    const lastCurveIndex = continuousPreview.curves.length - 1;

    // 确保第一个点在时间偏移0
    continuousPreview.curves[0].timeOffset = 0;
    // 设置最后一个点的时间偏移为新的duration
    continuousPreview.curves[lastCurveIndex].timeOffset = newDuration;

    // 按比例缩放中间点，确保最小时间间隔
    if (lastCurveIndex > 1) {
      if (originalDuration > 0) {
        // 按比例重新分布中间点
        for (let i = 1; i < lastCurveIndex; i++) {
          const originalCurve = (draggedEvent.value as RenderableContinuousEvent).curves[i];
          continuousPreview.curves[i].timeOffset = (originalCurve.timeOffset / originalDuration) * newDuration;
        }
      } else {
        // 如果原始duration为0，使用等分分布
        const numberOfIntermediatePoints = lastCurveIndex - 1;
        const segmentDuration = Math.max(MIN_CURVE_POINT_INTERVAL_MS, Math.floor(newDuration / (numberOfIntermediatePoints + 1)));

        for (let i = 1; i < lastCurveIndex; i++) {
          continuousPreview.curves[i].timeOffset = segmentDuration * i;
        }
      }

      // 使用验证函数确保所有相邻点之间至少有最小间隔
      continuousPreview.curves = validateAndFixCurveTimeDistribution(continuousPreview.curves, newDuration);
    }
  };

  return {
    createPreviewEvent,
    validateAndFixCurveTimeDistribution,
  };
}
