use serde::Serialize;
use thiserror::Error;

#[derive(<PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, Serialize)]
#[allow(dead_code)]
pub enum Error {
    #[error("文件 I/O 错误: {0}")]
    Io(String), // Wrap IO errors as strings for serialization

    #[error("ZIP 文件处理错误: {0}")]
    Zip(String), // Wrap Zip errors

    #[error("JSON 解析/序列化错误: {0}")]
    <PERSON><PERSON>(String), // Wrap Serde JSON errors

    #[error("UUID 错误: {0}")]
    Uuid(String), // Wrap UUID errors

    #[error("找不到文件或条目: {0}")]
    NotFound(String),

    #[error("验证错误: {0}")]
    ValidationError(String),

    // 网络相关错误
    #[error("网络错误: {0}")]
    Network(String),

    // 项目重命名相关的错误
    #[error("名称无效: {0}")]
    NameInvalid(String),

    #[error("同名项目已存在: {0}")]
    NameAlreadyExists(String),

    #[error("项目路径未找到: {0}")]
    PathNotFound(String),

    #[error("重命名 I/O 错误: {0}")]
    RenameIoError(String),

    #[error("项目元数据读取错误: {0}")]
    MetadataReadError(String),

    #[error("项目元数据写入错误: {0}")]
    MetadataWriteError(String),

    // 设备管理相关错误
    #[error("设备错误: {0}")]
    DeviceError(String),

    #[error("设备连接错误: {0}")]
    DeviceConnectionError(String),

    #[error("设备传输错误: {0}")]
    DeviceTransmissionError(String),

    #[error("无效操作: {0}")]
    InvalidOperation(String),

    #[error("功能未实现: {0}")]
    NotImplemented(String),

    #[error("权限被拒绝: {0}")]
    PermissionDenied(String),

    #[error("操作超时: {0}")]
    Timeout(String),
}

// Implement From traits to convert underlying errors into our custom Error type
impl From<std::io::Error> for Error {
    fn from(err: std::io::Error) -> Self {
        Error::Io(err.to_string())
    }
}

impl From<zip::result::ZipError> for Error {
    fn from(err: zip::result::ZipError) -> Self {
        Error::Zip(err.to_string())
    }
}

impl From<serde_json::Error> for Error {
    fn from(err: serde_json::Error) -> Self {
        Error::Json(err.to_string())
    }
}

impl From<uuid::Error> for Error {
    fn from(err: uuid::Error) -> Self {
        Error::Uuid(err.to_string())
    }
}

// Define a type alias for Result using our custom error type
pub type Result<T> = std::result::Result<T, Error>;
