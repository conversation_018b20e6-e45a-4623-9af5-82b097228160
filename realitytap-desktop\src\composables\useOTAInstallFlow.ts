/**
 * OTA 安装流程组合函数
 * 提供完整的 OTA 更新安装流程管理
 */

import { computed, reactive, readonly } from 'vue';
import { useI18n } from '@/composables/useI18n';
import { otaService, type OTAUpdateInfo, type SafeInstallOptions, type ProcessInfo } from '@/services/ota.service';
import { safeInstallerService } from '@/services/installer/installer.service';
import { logger, LogModule } from '@/utils/logger/logger';

// 安装流程步骤枚举
export enum InstallFlowStep {
  IDLE = 'idle',
  CHECKING = 'checking',
  DOWNLOADING = 'downloading',
  VERIFYING = 'verifying',
  PREPARING = 'preparing',
  CLOSING_PROCESSES = 'closing_processes',
  INSTALLING = 'installing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// 安装流程状态接口
export interface InstallFlowState {
  currentStep: InstallFlowStep;
  progress: number;
  message: string;
  error: string | null;
  canCancel: boolean;
  updateInfo: OTAUpdateInfo | null;
  downloadedFilePath: string | null;
  detectedProcesses: ProcessInfo[];
}

// 安装流程选项接口
export interface OTAInstallFlowOptions {
  skipProcessCheck?: boolean;
  forceInstall?: boolean;
  autoRestart?: boolean;
  backupBeforeInstall?: boolean;
  validateSignature?: boolean;
}

/**
 * OTA 安装流程组合函数
 */
export function useOTAInstallFlow() {
  const { t } = useI18n();

  // 响应式状态
  const state = reactive<InstallFlowState>({
    currentStep: InstallFlowStep.IDLE,
    progress: 0,
    message: '',
    error: null,
    canCancel: true,
    updateInfo: null,
    downloadedFilePath: null,
    detectedProcesses: [],
  });

  // 计算属性
  const isActive = computed(() => state.currentStep !== InstallFlowStep.IDLE);
  const isCompleted = computed(() => state.currentStep === InstallFlowStep.COMPLETED);
  const isFailed = computed(() => state.currentStep === InstallFlowStep.FAILED);
  const canProceed = computed(() => !state.error && state.canCancel);

  /**
   * 开始安装流程
   */
  const startInstallFlow = async (options: OTAInstallFlowOptions = {}): Promise<boolean> => {
    try {
      logger.info(LogModule.GENERAL, '开始 OTA 安装流程', options);
      
      // 重置状态
      resetState();
      
      // 步骤1: 检查更新
      if (!(await checkForUpdates())) {
        return false;
      }
      
      // 步骤2: 下载更新
      if (!(await downloadUpdate())) {
        return false;
      }
      
      // 步骤3: 验证文件
      if (!(await verifyUpdate(options.validateSignature))) {
        return false;
      }
      
      // 步骤4: 准备安装
      if (!(await prepareInstallation(options))) {
        return false;
      }
      
      // 步骤5: 关闭相关进程
      if (!options.skipProcessCheck && !(await closeRelatedProcesses())) {
        return false;
      }
      
      // 步骤6: 执行安装
      if (!(await performInstallation(options))) {
        return false;
      }
      
      // 步骤7: 完成
      completeInstallation();
      
      return true;
    } catch (error) {
      logger.error(LogModule.GENERAL, '安装流程失败', error);
      setError(error instanceof Error ? error.message : '安装流程失败');
      return false;
    }
  };

  /**
   * 检查更新
   */
  const checkForUpdates = async (): Promise<boolean> => {
    updateStep(InstallFlowStep.CHECKING, 10, t('ota.checkingUpdates'));
    
    const result = await otaService.checkForUpdates();
    
    if (!result.hasUpdate) {
      setError(t('ota.noUpdatesAvailable'));
      return false;
    }
    
    if (result.errorMessage) {
      setError(result.errorMessage);
      return false;
    }
    
    state.updateInfo = result.updateInfo || null;
    return true;
  };

  /**
   * 下载更新
   */
  const downloadUpdate = async (): Promise<boolean> => {
    if (!state.updateInfo) {
      setError(t('ota.noUpdateInfo'));
      return false;
    }
    
    updateStep(InstallFlowStep.DOWNLOADING, 20, t('ota.downloadingUpdate'));
    
    const result = await otaService.downloadUpdate(
      state.updateInfo,
      (progress) => {
        const downloadProgress = 20 + (progress.percentage * 0.4); // 20-60%
        updateStep(InstallFlowStep.DOWNLOADING, downloadProgress, 
          t('ota.downloadingProgress', { 
            percentage: Math.round(progress.percentage),
            downloaded: formatBytes(progress.downloaded),
            total: formatBytes(progress.total)
          })
        );
      }
    );
    
    if (!result.success) {
      setError(result.errorMessage || t('ota.downloadFailed'));
      return false;
    }
    
    state.downloadedFilePath = result.filePath || null;
    return true;
  };

  /**
   * 验证更新文件
   */
  const verifyUpdate = async (validateSignature = true): Promise<boolean> => {
    if (!state.downloadedFilePath || !state.updateInfo) {
      setError(t('ota.noDownloadedFile'));
      return false;
    }
    
    updateStep(InstallFlowStep.VERIFYING, 65, t('ota.verifyingUpdate'));
    
    const result = await otaService.verifyUpdateFile(
      state.downloadedFilePath,
      state.updateInfo.checksum,
      validateSignature ? state.updateInfo.signature : undefined
    );
    
    if (!result.isValid) {
      setError(result.errorMessage || t('ota.verificationFailed'));
      return false;
    }
    
    return true;
  };

  /**
   * 准备安装
   */
  const prepareInstallation = async (options: OTAInstallFlowOptions): Promise<boolean> => {
    updateStep(InstallFlowStep.PREPARING, 70, t('ota.preparingInstallation'));
    
    // 检测相关进程
    if (!options.skipProcessCheck) {
      const processes = await otaService.getRunningProcesses();
      state.detectedProcesses = processes.filter(p => p.isCritical);
    }
    
    return true;
  };

  /**
   * 关闭相关进程
   */
  const closeRelatedProcesses = async (): Promise<boolean> => {
    if (state.detectedProcesses.length === 0) {
      return true;
    }
    
    updateStep(InstallFlowStep.CLOSING_PROCESSES, 75, t('ota.closingProcesses'));
    
    const results = await otaService.gracefulCloseProcesses(state.detectedProcesses);
    
    // 检查是否有进程关闭失败
    const failedProcesses = results.filter(r => !r.success);
    if (failedProcesses.length > 0) {
      const failedNames = failedProcesses.map(r => 
        state.detectedProcesses.find(p => p.pid === r.pid)?.name || `PID:${r.pid}`
      ).join(', ');
      
      setError(t('ota.processCloseFailed', { processes: failedNames }));
      return false;
    }
    
    return true;
  };

  /**
   * 执行安装
   */
  const performInstallation = async (options: OTAInstallFlowOptions): Promise<boolean> => {
    if (!state.downloadedFilePath) {
      setError(t('ota.noDownloadedFile'));
      return false;
    }
    
    updateStep(InstallFlowStep.INSTALLING, 80, t('ota.installingUpdate'));
    
    const installOptions: SafeInstallOptions = {
      skipBackup: !options.backupBeforeInstall,
      forceInstall: options.forceInstall,
      cleanupOnFailure: true,
      validateSignature: options.validateSignature,
    };
    
    const result = await safeInstallerService.startSafeInstall(
      state.downloadedFilePath,
      installOptions
    );
    
    if (!result.success) {
      setError(result.errorMessage || t('ota.installationFailed'));
      return false;
    }
    
    return true;
  };

  /**
   * 完成安装
   */
  const completeInstallation = (): void => {
    updateStep(InstallFlowStep.COMPLETED, 100, t('ota.installationCompleted'));
    state.canCancel = false;
  };

  /**
   * 取消安装流程
   */
  const cancelInstallFlow = async (): Promise<boolean> => {
    if (!state.canCancel) {
      return false;
    }
    
    try {
      // 如果正在安装，尝试取消安装
      if (state.currentStep === InstallFlowStep.INSTALLING) {
        await safeInstallerService.cancelInstall();
      }
      
      // 清理临时文件
      await otaService.cleanupTempFiles();
      
      resetState();
      return true;
    } catch (error) {
      logger.error(LogModule.GENERAL, '取消安装流程失败', error);
      return false;
    }
  };

  /**
   * 重置状态
   */
  const resetState = (): void => {
    state.currentStep = InstallFlowStep.IDLE;
    state.progress = 0;
    state.message = '';
    state.error = null;
    state.canCancel = true;
    state.updateInfo = null;
    state.downloadedFilePath = null;
    state.detectedProcesses = [];
  };

  /**
   * 更新步骤
   */
  const updateStep = (step: InstallFlowStep, progress: number, message: string): void => {
    state.currentStep = step;
    state.progress = Math.min(100, Math.max(0, progress));
    state.message = message;
  };

  /**
   * 设置错误
   */
  const setError = (error: string): void => {
    state.error = error;
    state.currentStep = InstallFlowStep.FAILED;
    state.canCancel = true;
  };

  /**
   * 格式化字节数
   */
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return {
    // 状态
    state: readonly(state),
    
    // 计算属性
    isActive,
    isCompleted,
    isFailed,
    canProceed,
    
    // 方法
    startInstallFlow,
    cancelInstallFlow,
    resetState,
  };
}
