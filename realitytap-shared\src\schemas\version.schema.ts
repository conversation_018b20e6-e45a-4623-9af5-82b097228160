import { z } from 'zod';
import { VERSION_REGEX } from '../constants/versions';

/**
 * 应用版本信息 Schema
 */
export const AppVersionInfoSchema = z.object({
  appName: z.string().min(1, 'App name is required'),
  appVersion: z.string().regex(VERSION_REGEX, 'Invalid app version format'),
  backendVersion: z.string().regex(VERSION_REGEX, 'Invalid backend version format'),
  buildDate: z.string().datetime('Invalid build date format'),
  buildMode: z.enum(['debug', 'release'], {
    errorMap: () => ({ message: 'Build mode must be debug or release' }),
  }),
  targetArch: z.enum(['x86_64', 'aarch64', 'x86'], {
    errorMap: () => ({ message: 'Invalid target architecture' }),
  }),
  targetOs: z.enum(['windows', 'macos', 'linux'], {
    errorMap: () => ({ message: 'Invalid target OS' }),
  }),
});

/**
 * OTA 版本信息 Schema
 */
export const OTAVersionInfoSchema = AppVersionInfoSchema.extend({
  releaseChannel: z.enum(['stable', 'beta', 'alpha'], {
    errorMap: () => ({ message: 'Invalid release channel' }),
  }),
  minimumVersion: z.string().regex(VERSION_REGEX, 'Invalid minimum version format'),
  deprecatedVersions: z.array(z.string().regex(VERSION_REGEX)),
});

/**
 * 更新检查请求 Schema
 */
export const UpdateCheckRequestSchema = z.object({
  currentVersion: z.string().regex(VERSION_REGEX, 'Invalid current version format'),
  platform: z.enum(['windows', 'macos', 'linux'], {
    errorMap: () => ({ message: 'Invalid platform' }),
  }),
  architecture: z.enum(['x86_64', 'aarch64', 'x86'], {
    errorMap: () => ({ message: 'Invalid architecture' }),
  }),
  channel: z.enum(['stable', 'beta', 'alpha']).default('stable'),
  locale: z.string().optional(),
});

/**
 * 更新检查响应 Schema
 */
export const UpdateCheckResponseSchema = z.object({
  hasUpdate: z.boolean(),
  latestVersion: z.string().regex(VERSION_REGEX).optional(),
  downloadUrl: z.string().url().optional(),
  releaseNotes: z.string().optional(),
  fileSize: z.number().positive().optional(),
  checksum: z.string().regex(/^(sha256|md5):[a-fA-F0-9]+$/).optional(),
  isForced: z.boolean().optional(),
  minimumVersion: z.string().regex(VERSION_REGEX).optional(),
});

/**
 * 版本比较 Schema
 */
export const VersionComparisonSchema = z.object({
  current: z.string().regex(VERSION_REGEX),
  latest: z.string().regex(VERSION_REGEX),
  hasUpdate: z.boolean(),
  updateType: z.enum(['major', 'minor', 'patch', 'none']),
});