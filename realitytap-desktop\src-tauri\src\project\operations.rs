// Project operations (file and group movements)
use crate::{
    error::{<PERSON><PERSON><PERSON>, Result},
    models,
    project::io::{read_project_directory, write_project_directory, HAPTICS_DIR_NAME},
};
use chrono::Utc;
use std::fs;
use std::path::PathBuf;
use uuid::Uuid;

/// Move a file to a different group or root directory
pub fn move_file_to_group(
    project_dir_path: &str,
    file_uuid_str: &str,
    new_group_uuid_str: Option<&str>,
) -> Result<models::Project> {
    let proj_dir_path_buf = PathBuf::from(project_dir_path);
    let mut project = read_project_directory(&proj_dir_path_buf)?;

    // Parse and validate file UUID
    let file_uuid_to_move = Uuid::parse_str(file_uuid_str)
        .map_err(|_| Error::ValidationError("Invalid file UUID format".to_string()))?;

    // Find the file to move
    let file_index = project
        .files
        .iter()
        .position(|f| f.file_uuid == file_uuid_to_move)
        .ok_or_else(|| Error::NotFound(format!("File with UUID {} not found", file_uuid_str)))?;

    // Get current file info
    let current_file = &project.files[file_index];
    let file_name = PathBuf::from(&current_file.path)
        .file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("")
        .to_string();

    if file_name.is_empty() {
        return Err(Error::ValidationError("Cannot extract file name from path".to_string()));
    }

    // Parse and validate target group UUID
    let target_group_uuid: Option<Uuid> = match new_group_uuid_str {
        Some(uuid_str) if !uuid_str.trim().is_empty() => {
            let parsed_uuid = Uuid::parse_str(uuid_str)
                .map_err(|_| Error::ValidationError("Invalid target group UUID format".to_string()))?;

            // Verify the target group exists
            if !project.groups.iter().any(|g| g.group_uuid == parsed_uuid) {
                return Err(Error::NotFound(format!(
                    "Target group with UUID {} not found",
                    uuid_str
                )));
            }
            Some(parsed_uuid)
        }
        _ => None, // Move to root directory
    };

    // Build the new file path
    let new_file_path = match target_group_uuid {
        Some(group_uuid) => {
            // Moving to a specific group
            let target_group = project
                .groups
                .iter()
                .find(|g| g.group_uuid == group_uuid)
                .unwrap(); // Safe because we verified existence above

            // Build path: haptics/{group_path}/{filename}
            PathBuf::from(HAPTICS_DIR_NAME)
                .join(&target_group.path)
                .join(&file_name)
                .to_string_lossy()
                .to_string()
        }
        None => {
            // Moving to root directory
            PathBuf::from(HAPTICS_DIR_NAME)
                .join(&file_name)
                .to_string_lossy()
                .to_string()
        }
    };

    // Check if the file is already in the target location
    if current_file.path == new_file_path {
        // File is already in the target location, no move needed
        return Ok(project);
    }

    // Check for naming conflicts at the target location
    if project.files.iter().any(|f| f.path == new_file_path && f.file_uuid != file_uuid_to_move) {
        return Err(Error::ValidationError(format!(
            "A file with the same name already exists at the target location: {}",
            new_file_path
        )));
    }

    // Build physical file paths
    let old_physical_path = proj_dir_path_buf.join(&current_file.path);
    let new_physical_path = proj_dir_path_buf.join(&new_file_path);

    // Verify source file exists
    if !old_physical_path.exists() {
        return Err(Error::NotFound(format!(
            "Source file does not exist: {:?}",
            old_physical_path
        )));
    }

    // Create target directory if it doesn't exist
    if let Some(parent_dir) = new_physical_path.parent() {
        fs::create_dir_all(parent_dir)
            .map_err(|e| Error::Io(format!("Failed to create target directory {:?}: {}", parent_dir, e)))?;
    }

    // Move the physical file
    fs::rename(&old_physical_path, &new_physical_path)
        .map_err(|e| Error::Io(format!(
            "Failed to move file from {:?} to {:?}: {}",
            old_physical_path, new_physical_path, e
        )))?;

    // Update project metadata
    project.files[file_index].path = new_file_path;
    project.files[file_index].group = target_group_uuid;
    project.files[file_index].last_modified_time = Utc::now();
    project.last_modified_time = Utc::now();

    // Save the updated project
    write_project_directory(&project, &proj_dir_path_buf)?;

    Ok(project)
}

/// Move a group to a different parent group
pub fn move_group_to_parent(
    project_dir_path: &str,
    group_uuid_str: &str,
    new_parent_group_uuid_str: Option<&str>,
) -> Result<models::Project> {
    let proj_dir_path_buf = PathBuf::from(project_dir_path);
    let mut project = read_project_directory(&proj_dir_path_buf)?;

    let group_uuid_to_move = Uuid::parse_str(group_uuid_str)
        .map_err(|_| Error::ValidationError("Invalid group UUID format".to_string()))?;

    // Find the group to move
    let group_index = project
        .groups
        .iter()
        .position(|g| g.group_uuid == group_uuid_to_move)
        .ok_or_else(|| Error::NotFound(format!("Group with UUID {} not found", group_uuid_str)))?;

    let old_group_path = project.groups[group_index].path.clone();
    let group_name = project.groups[group_index].name.clone();

    // Parse new parent group UUID
    let new_parent_uuid: Option<Uuid> = match new_parent_group_uuid_str {
        Some(uuid_str) => {
            if uuid_str.trim().is_empty() {
                None // Move to root
            } else {
                let parsed_uuid = Uuid::parse_str(uuid_str)
                    .map_err(|_| Error::ValidationError("Invalid parent group UUID format".to_string()))?;
                
                // Verify the parent group exists and is not the same as the group being moved
                if parsed_uuid == group_uuid_to_move {
                    return Err(Error::ValidationError("Cannot move group to itself".to_string()));
                }
                
                if !project.groups.iter().any(|g| g.group_uuid == parsed_uuid) {
                    return Err(Error::NotFound(format!(
                        "Target parent group with UUID {} not found",
                        uuid_str
                    )));
                }
                Some(parsed_uuid)
            }
        }
        None => None, // Move to root
    };

    // Determine new group path
    let new_group_path = if let Some(parent_uuid) = new_parent_uuid {
        // Find the parent group path
        let parent_group = project
            .groups
            .iter()
            .find(|g| g.group_uuid == parent_uuid)
            .ok_or_else(|| Error::NotFound("Target parent group not found".to_string()))?;
        
        format!("{}/{}", parent_group.path, group_name)
    } else {
        // Move to root
        group_name.clone()
    };

    // Check for path conflicts
    if project.groups.iter().any(|g| g.path == new_group_path && g.group_uuid != group_uuid_to_move) {
        return Err(Error::ValidationError(format!(
            "A group with the same path already exists: {}",
            new_group_path
        )));
    }

    // Move the physical directory
    let old_physical_path = proj_dir_path_buf.join(HAPTICS_DIR_NAME).join(&old_group_path);
    let new_physical_path = proj_dir_path_buf.join(HAPTICS_DIR_NAME).join(&new_group_path);

    // Ensure target parent directory exists
    if let Some(parent_dir) = new_physical_path.parent() {
        fs::create_dir_all(parent_dir)
            .map_err(|e| Error::Io(format!("Failed to create target parent directory: {}", e)))?;
    }

    // Move the directory
    if old_physical_path.exists() {
        fs::rename(&old_physical_path, &new_physical_path)
            .map_err(|e| Error::Io(format!("Failed to move group directory: {}", e)))?;
    }

    // Update group metadata
    let old_path_prefix = format!("{}/", old_group_path);
    let new_path_prefix = format!("{}/", new_group_path);

    // Update the moved group
    project.groups[group_index].path = new_group_path.clone();
    project.groups[group_index].parent_group_uuid = new_parent_uuid;

    // Update all descendant groups
    for group in project.groups.iter_mut() {
        if group.group_uuid != group_uuid_to_move && group.path.starts_with(&old_path_prefix) {
            group.path = group.path.replacen(&old_path_prefix, &new_path_prefix, 1);
        }
    }

    // Update all files in the moved group and its descendants
    for file in project.files.iter_mut() {
        if file.path.starts_with(&format!("{}/{}", HAPTICS_DIR_NAME, old_group_path)) {
            let relative_path = file.path.strip_prefix(&format!("{}/", HAPTICS_DIR_NAME)).unwrap_or("");
            if relative_path.starts_with(&old_group_path) {
                let new_relative_path = relative_path.replacen(&old_group_path, &new_group_path, 1);
                file.path = format!("{}/{}", HAPTICS_DIR_NAME, new_relative_path);
            }
        }
    }

    project.last_modified_time = Utc::now();

    // Save the project
    write_project_directory(&project, &proj_dir_path_buf)?;

    Ok(project)
}
