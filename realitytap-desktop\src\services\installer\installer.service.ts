/**
 * 安全安装器服务
 * 提供安全的软件安装功能
 */

import { invoke } from '@tauri-apps/api/core';
import { logger, LogModule } from '@/utils/logger/logger';

export interface InstallState {
  isInstalling: boolean;
  progress: number;
  currentOperation: string;
  logs: string[];
  error: string | null;
  canCancel: boolean;
}

export interface SafeInstallOptions {
  skipBackup?: boolean;
  forceInstall?: boolean;
  cleanupOnFailure?: boolean;
  timeout?: number;
}

export interface SafeInstallResult {
  success: boolean;
  errorMessage?: string;
  installPath?: string;
  backupPath?: string;
  logs?: string[];
}

/**
 * 安全安装器服务类
 */
export class SafeInstallerService {
  private currentState: InstallState = {
    isInstalling: false,
    progress: 0,
    currentOperation: '',
    logs: [],
    error: null,
    canCancel: true,
  };

  /**
   * 开始安全安装
   */
  async startSafeInstall(
    packagePath: string,
    options: SafeInstallOptions = {}
  ): Promise<SafeInstallResult> {
    try {
      logger.info(LogModule.DEVICE, '🚀 SafeInstallerService.startSafeInstall 开始', { packagePath, options });

      // 重置状态
      this.resetState();
      this.currentState.isInstalling = true;
      this.currentState.currentOperation = '准备安装...';

      // 调用 Tauri 后端进行安装
      const result = await invoke<SafeInstallResult>('safe_install_package', {
        packagePath,
        options,
      });

      this.currentState.isInstalling = false;
      this.currentState.progress = result.success ? 100 : 0;
      this.currentState.currentOperation = result.success ? '安装完成' : '安装失败';

      if (!result.success) {
        this.currentState.error = result.errorMessage || '未知错误';
      }

      return result;
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 安装失败', error);
      this.currentState.isInstalling = false;
      this.currentState.error = error instanceof Error ? error.message : '安装过程中发生错误';

      return {
        success: false,
        errorMessage: this.currentState.error,
      };
    }
  }

  /**
   * 取消安装
   */
  async cancelInstall(): Promise<boolean> {
    try {
      if (!this.currentState.isInstalling || !this.currentState.canCancel) {
        return false;
      }
      
      const result = await invoke<boolean>('cancel_install');
      
      if (result) {
        this.currentState.isInstalling = false;
        this.currentState.currentOperation = '安装已取消';
        this.currentState.error = '用户取消安装';
      }
      
      return result;
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 取消安装失败', error);
      return false;
    }
  }

  /**
   * 获取安装状态
   */
  async getInstallState(): Promise<InstallState> {
    try {
      // 如果正在安装，从后端获取最新状态
      if (this.currentState.isInstalling) {
        const backendState = await invoke<Partial<InstallState>>('get_install_state');
        
        if (backendState) {
          this.currentState = {
            ...this.currentState,
            ...backendState,
          };
        }
      }
      
      return { ...this.currentState };
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 获取安装状态失败', error);
      return { ...this.currentState };
    }
  }

  /**
   * 验证安装包
   */
  async validatePackage(packagePath: string): Promise<{
    isValid: boolean;
    errorMessage?: string;
    packageInfo?: {
      name: string;
      version: string;
      size: number;
      checksum: string;
    };
  }> {
    try {
      const result = await invoke<{
        isValid: boolean;
        errorMessage?: string;
        packageInfo?: {
          name: string;
          version: string;
          size: number;
          checksum: string;
        };
      }>('validate_package', { packagePath });
      
      return result;
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 验证安装包失败', error);
      return {
        isValid: false,
        errorMessage: error instanceof Error ? error.message : '验证失败',
      };
    }
  }

  /**
   * 重置状态
   */
  private resetState(): void {
    this.currentState = {
      isInstalling: false,
      progress: 0,
      currentOperation: '',
      logs: [],
      error: null,
      canCancel: true,
    };
  }

  /**
   * 添加日志
   */
  // private addLog(message: string): void {
  //   this.currentState.logs.push(`[${new Date().toISOString()}] ${message}`);
  //
  //   // 限制日志数量
  //   if (this.currentState.logs.length > 100) {
  //     this.currentState.logs = this.currentState.logs.slice(-50);
  //   }
  // }
}

// 导出单例实例
export const safeInstallerService = new SafeInstallerService();
