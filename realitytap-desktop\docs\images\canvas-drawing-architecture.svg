<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="7" 
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e1bee7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景分组 -->
  <rect x="20" y="20" width="860" height="120" rx="10" fill="url(#grad1)" stroke="#1976d2" stroke-width="1" opacity="0.3"/>
  <text x="30" y="40" font-family="Arial" font-size="14" font-weight="bold" fill="#1976d2">数据层 Data Layer</text>
  
  <rect x="20" y="160" width="860" height="180" rx="10" fill="url(#grad2)" stroke="#7b1fa2" stroke-width="1" opacity="0.3"/>
  <text x="30" y="180" font-family="Arial" font-size="14" font-weight="bold" fill="#7b1fa2">Canvas绘制层 Canvas Drawing Layer</text>
  
  <rect x="20" y="360" width="860" height="180" rx="10" fill="url(#grad3)" stroke="#388e3c" stroke-width="1" opacity="0.3"/>
  <text x="30" y="380" font-family="Arial" font-size="14" font-weight="bold" fill="#388e3c">优化层 Optimization Layer</text>
  
  <!-- 数据层组件 -->
  <rect x="50" y="60" width="100" height="50" rx="5" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2"/>
  <text x="100" y="80" text-anchor="middle" font-family="Arial" font-size="10">.he文件</text>
  <text x="100" y="95" text-anchor="middle" font-family="Arial" font-size="9">JSON数据</text>
  
  <rect x="200" y="60" width="100" height="50" rx="5" fill="#f8bbd9" stroke="#c2185b" stroke-width="2"/>
  <text x="250" y="80" text-anchor="middle" font-family="Arial" font-size="9">RealityTap</text>
  <text x="250" y="95" text-anchor="middle" font-family="Arial" font-size="9">Effect</text>
  
  <rect x="350" y="60" width="100" height="50" rx="5" fill="#c8e6c9" stroke="#388e3c" stroke-width="2"/>
  <text x="400" y="80" text-anchor="middle" font-family="Arial" font-size="9">Renderable</text>
  <text x="400" y="95" text-anchor="middle" font-family="Arial" font-size="9">Event[]</text>
  
  <!-- Canvas绘制层组件 -->
  <rect x="50" y="200" width="120" height="60" rx="5" fill="#fff9c4" stroke="#f57f17" stroke-width="2"/>
  <text x="110" y="220" text-anchor="middle" font-family="Arial" font-size="9">Interactive</text>
  <text x="110" y="235" text-anchor="middle" font-family="Arial" font-size="9">Waveform</text>
  <text x="110" y="250" text-anchor="middle" font-family="Arial" font-size="9">Canvas.vue</text>
  
  <rect x="200" y="200" width="100" height="60" rx="5" fill="#d1c4e9" stroke="#512da8" stroke-width="2"/>
  <text x="250" y="220" text-anchor="middle" font-family="Arial" font-size="9">Drawing</text>
  <text x="250" y="235" text-anchor="middle" font-family="Arial" font-size="9">Orchestrator</text>
  <text x="250" y="250" text-anchor="middle" font-family="Arial" font-size="9">绘制编排</text>
  
  <rect x="330" y="200" width="100" height="60" rx="5" fill="#ffccbc" stroke="#ff5722" stroke-width="2"/>
  <text x="380" y="220" text-anchor="middle" font-family="Arial" font-size="9">Coordinate</text>
  <text x="380" y="235" text-anchor="middle" font-family="Arial" font-size="9">Transform</text>
  <text x="380" y="250" text-anchor="middle" font-family="Arial" font-size="9">坐标转换</text>
  
  <rect x="460" y="200" width="100" height="60" rx="5" fill="#b39ddb" stroke="#673ab7" stroke-width="2"/>
  <text x="510" y="220" text-anchor="middle" font-family="Arial" font-size="9">Event</text>
  <text x="510" y="235" text-anchor="middle" font-family="Arial" font-size="9">Drawing</text>
  <text x="510" y="250" text-anchor="middle" font-family="Arial" font-size="9">事件绘制</text>
  
  <rect x="590" y="200" width="100" height="60" rx="5" fill="#a5d6a7" stroke="#4caf50" stroke-width="2"/>
  <text x="640" y="220" text-anchor="middle" font-family="Arial" font-size="9">Audio</text>
  <text x="640" y="235" text-anchor="middle" font-family="Arial" font-size="9">Waveform</text>
  <text x="640" y="250" text-anchor="middle" font-family="Arial" font-size="9">音频波形</text>
  
  <rect x="720" y="200" width="100" height="60" rx="5" fill="#ffab91" stroke="#ff5722" stroke-width="2"/>
  <text x="770" y="220" text-anchor="middle" font-family="Arial" font-size="9">Grid</text>
  <text x="770" y="235" text-anchor="middle" font-family="Arial" font-size="9">Drawing</text>
  <text x="770" y="250" text-anchor="middle" font-family="Arial" font-size="9">网格绘制</text>
  
  <!-- 绘制流程 -->
  <rect x="50" y="280" width="80" height="40" rx="3" fill="#e1f5fe" stroke="#0277bd" stroke-width="1"/>
  <text x="90" y="295" text-anchor="middle" font-family="Arial" font-size="8">1.清除</text>
  <text x="90" y="308" text-anchor="middle" font-family="Arial" font-size="8">画布</text>
  
  <rect x="150" y="280" width="80" height="40" rx="3" fill="#e1f5fe" stroke="#0277bd" stroke-width="1"/>
  <text x="190" y="295" text-anchor="middle" font-family="Arial" font-size="8">2.绘制</text>
  <text x="190" y="308" text-anchor="middle" font-family="Arial" font-size="8">网格</text>
  
  <rect x="250" y="280" width="80" height="40" rx="3" fill="#e1f5fe" stroke="#0277bd" stroke-width="1"/>
  <text x="290" y="295" text-anchor="middle" font-family="Arial" font-size="8">3.音频</text>
  <text x="290" y="308" text-anchor="middle" font-family="Arial" font-size="8">波形</text>
  
  <rect x="350" y="280" width="80" height="40" rx="3" fill="#e1f5fe" stroke="#0277bd" stroke-width="1"/>
  <text x="390" y="295" text-anchor="middle" font-family="Arial" font-size="8">4.触觉</text>
  <text x="390" y="308" text-anchor="middle" font-family="Arial" font-size="8">事件</text>
  
  <rect x="450" y="280" width="80" height="40" rx="3" fill="#e1f5fe" stroke="#0277bd" stroke-width="1"/>
  <text x="490" y="295" text-anchor="middle" font-family="Arial" font-size="8">5.辅助</text>
  <text x="490" y="308" text-anchor="middle" font-family="Arial" font-size="8">线条</text>
  
  <!-- 优化层组件 -->
  <rect x="50" y="400" width="100" height="50" rx="5" fill="#dcedc8" stroke="#689f38" stroke-width="2"/>
  <text x="100" y="420" text-anchor="middle" font-family="Arial" font-size="9">坐标缓存</text>
  <text x="100" y="435" text-anchor="middle" font-family="Arial" font-size="8">Coordinate Cache</text>
  
  <rect x="180" y="400" width="100" height="50" rx="5" fill="#dcedc8" stroke="#689f38" stroke-width="2"/>
  <text x="230" y="420" text-anchor="middle" font-family="Arial" font-size="9">虚拟滚动</text>
  <text x="230" y="435" text-anchor="middle" font-family="Arial" font-size="8">Virtual Scroll</text>
  
  <rect x="310" y="400" width="100" height="50" rx="5" fill="#dcedc8" stroke="#689f38" stroke-width="2"/>
  <text x="360" y="420" text-anchor="middle" font-family="Arial" font-size="9">Path2D缓存</text>
  <text x="360" y="435" text-anchor="middle" font-family="Arial" font-size="8">Path Cache</text>
  
  <rect x="440" y="400" width="100" height="50" rx="5" fill="#dcedc8" stroke="#689f38" stroke-width="2"/>
  <text x="490" y="420" text-anchor="middle" font-family="Arial" font-size="9">异步管理</text>
  <text x="490" y="435" text-anchor="middle" font-family="Arial" font-size="8">Async Manager</text>
  
  <rect x="570" y="400" width="100" height="50" rx="5" fill="#dcedc8" stroke="#689f38" stroke-width="2"/>
  <text x="620" y="420" text-anchor="middle" font-family="Arial" font-size="9">性能监控</text>
  <text x="620" y="435" text-anchor="middle" font-family="Arial" font-size="8">Performance</text>
  
  <rect x="700" y="400" width="100" height="50" rx="5" fill="#dcedc8" stroke="#689f38" stroke-width="2"/>
  <text x="750" y="420" text-anchor="middle" font-family="Arial" font-size="9">缩放管理</text>
  <text x="750" y="435" text-anchor="middle" font-family="Arial" font-size="8">Zoom Manager</text>
  
  <!-- 核心算法框 -->
  <rect x="550" y="480" width="300" height="180" rx="8" fill="#fff8e1" stroke="#ff8f00" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="560" y="500" font-family="Arial" font-size="12" font-weight="bold" fill="#ff8f00">核心算法 Core Algorithms</text>
  
  <text x="560" y="520" font-family="Arial" font-size="10" fill="#333">• mapTimeToXLocal(time) → x坐标</text>
  <text x="560" y="535" font-family="Arial" font-size="10" fill="#333">• mapIntensityToYLocal(intensity) → y坐标</text>
  <text x="560" y="550" font-family="Arial" font-size="10" fill="#333">• createAudioWaveformPath() → Path2D</text>
  <text x="560" y="565" font-family="Arial" font-size="10" fill="#333">• drawTransientEvent() → 瞬时事件</text>
  <text x="560" y="580" font-family="Arial" font-size="10" fill="#333">• drawContinuousEvent() → 连续事件</text>
  <text x="560" y="595" font-family="Arial" font-size="10" fill="#333">• calculateVirtualScrollOffset() → 滚动</text>
  <text x="560" y="610" font-family="Arial" font-size="10" fill="#333">• isPointInRadius() → 碰撞检测</text>
  <text x="560" y="625" font-family="Arial" font-size="10" fill="#333">• mapFrequencyToColor() → 颜色映射</text>
  <text x="560" y="640" font-family="Arial" font-size="10" fill="#333">• throttle() → 性能节流</text>
  
  <!-- 连接线 -->
  <line x1="150" y1="85" x2="190" y2="85" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="300" y1="85" x2="340" y2="85" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="400" y1="110" x2="400" y2="190" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
  
  <line x1="170" y1="230" x2="190" y2="230" stroke="#666" stroke-width="1" marker-end="url(#arrow)"/>
  <line x1="300" y1="230" x2="320" y2="230" stroke="#666" stroke-width="1" marker-end="url(#arrow)"/>
  <line x1="430" y1="230" x2="450" y2="230" stroke="#666" stroke-width="1" marker-end="url(#arrow)"/>
  <line x1="560" y1="230" x2="580" y2="230" stroke="#666" stroke-width="1" marker-end="url(#arrow)"/>
  <line x1="690" y1="230" x2="710" y2="230" stroke="#666" stroke-width="1" marker-end="url(#arrow)"/>
  
  <line x1="250" y1="260" x2="250" y2="270" stroke="#666" stroke-width="1" marker-end="url(#arrow)"/>
  
  <line x1="130" y1="300" x2="140" y2="300" stroke="#0277bd" stroke-width="1" marker-end="url(#arrow)"/>
  <line x1="230" y1="300" x2="240" y2="300" stroke="#0277bd" stroke-width="1" marker-end="url(#arrow)"/>
  <line x1="330" y1="300" x2="340" y2="300" stroke="#0277bd" stroke-width="1" marker-end="url(#arrow)"/>
  <line x1="430" y1="300" x2="440" y2="300" stroke="#0277bd" stroke-width="1" marker-end="url(#arrow)"/>
  
  <!-- 优化层到绘制层的连接 -->
  <line x1="100" y1="400" x2="380" y2="270" stroke="#689f38" stroke-width="1" stroke-dasharray="3,3" marker-end="url(#arrow)"/>
  <line x1="230" y1="400" x2="510" y2="270" stroke="#689f38" stroke-width="1" stroke-dasharray="3,3" marker-end="url(#arrow)"/>
  <line x1="360" y1="400" x2="640" y2="270" stroke="#689f38" stroke-width="1" stroke-dasharray="3,3" marker-end="url(#arrow)"/>
</svg>
