#!/bin/sh
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "${DEBUG:-false}" = "true" ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# Validate required environment variables
validate_env() {
    log_info "Validating environment variables..."
    
    # Check admin credentials
    if [ -z "${ADMIN_USERNAME:-}" ]; then
        log_error "ADMIN_USERNAME environment variable is required"
        exit 1
    fi
    
    if [ -z "${ADMIN_PASSWORD:-}" ]; then
        log_error "ADMIN_PASSWORD environment variable is required"
        exit 1
    fi
    
    # Check JWT secret
    if [ -z "${JWT_SECRET:-}" ]; then
        log_error "JWT_SECRET environment variable is required"
        exit 1
    fi
    
    # Validate JWT secret length (minimum 32 characters)
    if [ ${#JWT_SECRET} -lt 32 ]; then
        log_error "JWT_SECRET must be at least 32 characters long"
        exit 1
    fi
    
    log_info "Environment variables validation passed"
}

# Initialize directory structure
init_directories() {
    log_info "Initializing directory structure..."

    # Check current user
    CURRENT_USER=$(id -u)
    CURRENT_USER_NAME=$(id -un)

    log_info "Running as user: ${CURRENT_USER_NAME} (UID: ${CURRENT_USER})"

    # Ensure all required directories exist
    mkdir -p \
        "${STORAGE_PATH}/database" \
        "${STORAGE_PATH}/releases/stable" \
        "${STORAGE_PATH}/releases/beta" \
        "${STORAGE_PATH}/releases/alpha" \
        "${STORAGE_PATH}/metadata" \
        "${STORAGE_PATH}/logs" \
        "${STORAGE_PATH}/temp" \
        "${STORAGE_PATH}/backup/database"

    # Set permissions based on current user
    if [ "${CURRENT_USER}" = "0" ]; then
        log_info "Running as root, setting full permissions..."
        chmod -R 755 "${STORAGE_PATH}" 2>/dev/null || {
            log_warn "Could not set permissions for storage directory"
        }
    else
        log_info "Running as non-root user, checking write permissions..."
        if [ ! -w "${STORAGE_PATH}" ]; then
            log_error "Storage directory is not writable for current user"
            log_error "Please run with root privileges or fix host directory permissions"
            exit 1
        fi
    fi

    log_info "Directory structure initialized"
}

# Initialize database
init_database() {
    if [ "${DB_ENABLED:-true}" = "true" ]; then
        log_info "Initializing SQLite database..."

        # Ensure database directory exists
        DB_DIR="$(dirname "${DB_PATH}")"
        mkdir -p "${DB_DIR}"

        # Set permissions based on current user
        CURRENT_USER=$(id -u)
        if [ "${CURRENT_USER}" = "0" ]; then
            log_info "Running as root, setting database directory permissions..."
            chmod -R 755 "${DB_DIR}" 2>/dev/null || {
                log_warn "Could not set permissions for database directory"
            }
        else
            log_info "Running as non-root user, checking database directory permissions..."
        fi

        # Test database creation
        log_info "Testing database file creation..."
        TEST_DB_FILE="${DB_PATH}.test"

        if touch "${TEST_DB_FILE}" 2>/dev/null; then
            rm -f "${TEST_DB_FILE}"
            log_info "Database directory permissions verified"
        else
            log_error "Cannot create files in database directory: ${DB_DIR}"
            if [ "${CURRENT_USER}" = "0" ]; then
                log_error "This is unexpected when running as root. Please check:"
                log_error "  1. Disk space availability"
                log_error "  2. Docker volume mount configuration"
                log_error "  3. SELinux/AppArmor policies"
            else
                log_error "Please check:"
                log_error "  1. Host directory permissions"
                log_error "  2. Run with root privileges using docker-compose.root.yml"
                log_error "  3. Or fix permissions: sudo chown -R 1001:1001 ./data"
            fi
            exit 1
        fi

        # Database will be initialized by the application
        log_info "Database initialization will be handled by the application"
    else
        log_info "Database is disabled, using JSON file storage"
    fi
}

# Check SSL configuration
check_ssl_config() {
    if [ "${ENABLE_HTTPS:-false}" = "true" ]; then
        log_info "HTTPS mode enabled, checking SSL configuration..."
        
        SSL_CERT_PATH="${SSL_CERT_PATH:-/app/ssl/cert.pem}"
        SSL_KEY_PATH="${SSL_KEY_PATH:-/app/ssl/key.pem}"
        
        if [ ! -f "${SSL_CERT_PATH}" ]; then
            log_error "SSL certificate not found at ${SSL_CERT_PATH}"
            log_error "Please mount SSL certificate to ${SSL_CERT_PATH}"
            exit 1
        fi
        
        if [ ! -f "${SSL_KEY_PATH}" ]; then
            log_error "SSL private key not found at ${SSL_KEY_PATH}"
            log_error "Please mount SSL private key to ${SSL_KEY_PATH}"
            exit 1
        fi
        
        log_info "SSL configuration validated"
        export SSL_CERT_PATH
        export SSL_KEY_PATH
    else
        log_info "HTTP mode enabled"
    fi
}

# Set up configuration
setup_config() {
    log_info "Setting up application configuration..."
    
    # Set default values for optional environment variables
    export NODE_ENV="${NODE_ENV:-production}"
    export PORT="${PORT:-3000}"
    export HOST="${HOST:-0.0.0.0}"
    export DB_ENABLED="${DB_ENABLED:-true}"
    export LOG_LEVEL="${LOG_LEVEL:-info}"
    export SESSION_TIMEOUT="${SESSION_TIMEOUT:-3600000}"
    
    # Set BASE_URL based on HTTPS setting if not provided
    if [ -z "${BASE_URL:-}" ]; then
        if [ "${ENABLE_HTTPS:-false}" = "true" ]; then
            export BASE_URL="https://localhost:${PORT}"
        else
            export BASE_URL="http://localhost:${PORT}"
        fi
        log_info "BASE_URL set to: ${BASE_URL}"
    fi
    
    log_info "Configuration setup completed"
}

# Display startup information
display_startup_info() {
    CURRENT_USER=$(id -u)
    CURRENT_USER_NAME=$(id -un)

    log_info "=== RealityTap OTA Server Starting ==="
    log_info "Version: ${APP_VERSION:-latest}"
    log_info "Environment: ${NODE_ENV}"
    log_info "Running User: ${CURRENT_USER_NAME} (UID: ${CURRENT_USER})"
    log_info "Port: ${PORT}"
    log_info "Host: ${HOST}"
    log_info "Base URL: ${BASE_URL}"
    log_info "HTTPS Enabled: ${ENABLE_HTTPS:-false}"
    log_info "Database Enabled: ${DB_ENABLED}"
    log_info "Storage Path: ${STORAGE_PATH}"
    log_info "Admin Username: ${ADMIN_USERNAME}"
    log_info "Log Level: ${LOG_LEVEL}"
    log_info "======================================"
}

# Graceful shutdown handler
shutdown_handler() {
    log_info "Received shutdown signal, gracefully shutting down..."
    if [ -n "${APP_PID:-}" ]; then
        kill -TERM "${APP_PID}" 2>/dev/null || true
        wait "${APP_PID}" 2>/dev/null || true
    fi
    log_info "Shutdown completed"
    exit 0
}

# Set up signal handlers
trap shutdown_handler TERM INT

# Main execution
main() {
    log_info "Starting RealityTap OTA Server container..."
    
    # Run initialization steps
    validate_env
    setup_config
    init_directories
    init_database
    check_ssl_config
    display_startup_info
    
    # Start the application
    log_info "Starting Node.js application..."
    node dist/app.js &
    APP_PID=$!
    
    # Wait for the application to finish
    wait "${APP_PID}"
}

# Execute main function
main "$@"
