import { DownloadRecord, DownloadStatsData, DownloadStatsQuery } from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import crypto from 'crypto';

/**
 * 统计工具类
 * 提供下载统计相关的工具函数
 */
export class StatsUtil {
  /**
   * 生成下载记录ID
   * @returns 唯一ID
   */
  static generateRecordId(): string {
    return crypto.randomUUID();
  }

  /**
   * 脱敏IP地址
   * @param ip IP地址
   * @returns 脱敏后的IP地址
   */
  static anonymizeIP(ip: string): string {
    try {
      // IPv4 脱敏：保留前三段，最后一段替换为 xxx
      if (ip.includes('.') && !ip.includes(':')) {
        const parts = ip.split('.');
        if (parts.length === 4) {
          return `${parts[0]}.${parts[1]}.${parts[2]}.xxx`;
        }
      }

      // IPv6 脱敏：保留前四段，后面替换为 xxxx
      if (ip.includes(':')) {
        const parts = ip.split(':');
        if (parts.length >= 4) {
          return `${parts.slice(0, 4).join(':')}:xxxx:xxxx:xxxx:xxxx`;
        }
      }

      return 'xxx.xxx.xxx.xxx';
    } catch (error) {
      logger.warn('Failed to anonymize IP', { ip, error });
      return 'xxx.xxx.xxx.xxx';
    }
  }

  /**
   * 获取日期字符串（用于文件名）
   * @param date 日期对象
   * @returns YYYY-MM-DD 格式的日期字符串
   */
  static getDateString(date: Date = new Date()): string {
    const dateStr = date.toISOString().split('T')[0];
    return dateStr || '';
  }

  /**
   * 解析用户代理字符串，提取有用信息
   * @param userAgent 用户代理字符串
   * @returns 解析后的信息
   */
  static parseUserAgent(userAgent: string): {
    browser?: string;
    os?: string;
    version?: string;
  } {
    try {
      const result: { browser?: string; os?: string; version?: string } = {};

      // 检测操作系统
      if (userAgent.includes('Windows')) {
        result.os = 'Windows';
      } else if (userAgent.includes('Mac OS X') || userAgent.includes('macOS')) {
        result.os = 'macOS';
      } else if (userAgent.includes('Linux')) {
        result.os = 'Linux';
      }

      // 检测浏览器/客户端
      if (userAgent.includes('RealityTap')) {
        result.browser = 'RealityTap';
        // 尝试提取版本号
        const versionMatch = userAgent.match(/RealityTap\/(\d+\.\d+\.\d+)/);
        if (versionMatch) {
          result.version = versionMatch[1];
        }
      } else if (userAgent.includes('Chrome')) {
        result.browser = 'Chrome';
      } else if (userAgent.includes('Firefox')) {
        result.browser = 'Firefox';
      } else if (userAgent.includes('Safari')) {
        result.browser = 'Safari';
      }

      return result;
    } catch (error) {
      logger.warn('Failed to parse user agent', { userAgent, error });
      return {};
    }
  }

  /**
   * 验证统计查询参数
   * @param query 查询参数
   * @returns 验证结果
   */
  static validateStatsQuery(query: DownloadStatsQuery): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // 验证日期格式
    if (query.startDate && !this.isValidDateString(query.startDate)) {
      errors.push('Invalid startDate format. Expected YYYY-MM-DD');
    }

    if (query.endDate && !this.isValidDateString(query.endDate)) {
      errors.push('Invalid endDate format. Expected YYYY-MM-DD');
    }

    // 验证日期范围
    if (query.startDate && query.endDate) {
      const startDate = new Date(query.startDate);
      const endDate = new Date(query.endDate);
      if (startDate > endDate) {
        errors.push('startDate must be before endDate');
      }
    }

    // 验证分页参数
    if (query.limit !== undefined && (query.limit < 1 || query.limit > 1000)) {
      errors.push('limit must be between 1 and 1000');
    }

    if (query.offset !== undefined && query.offset < 0) {
      errors.push('offset must be non-negative');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证日期字符串格式
   * @param dateString 日期字符串
   * @returns 是否有效
   */
  private static isValidDateString(dateString: string): boolean {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if (!regex.test(dateString)) {
      return false;
    }

    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * 聚合统计数据
   * @param records 下载记录数组
   * @returns 聚合后的统计数据
   */
  static aggregateStats(records: DownloadRecord[]): DownloadStatsData {
    const stats: DownloadStatsData = {
      totalDownloads: records.length,
      totalBytes: 0,
      byVersion: {},
      byPlatform: {},
      byChannel: {},
      byDate: {},
      recentDownloads: [],
    };

    for (const record of records) {
      // 累计字节数
      stats.totalBytes += record.fileSize;

      // 按版本统计
      stats.byVersion[record.version] = (stats.byVersion[record.version] || 0) + 1;

      // 按平台统计
      stats.byPlatform[record.platform] = (stats.byPlatform[record.platform] || 0) + 1;

      // 按渠道统计
      stats.byChannel[record.channel] = (stats.byChannel[record.channel] || 0) + 1;

      // 按日期统计
      const dateKey = record.downloadTime.split('T')[0];
      if (dateKey) {
        stats.byDate[dateKey] = (stats.byDate[dateKey] || 0) + 1;
      }
    }

    // 最近下载记录（最多100条）
    stats.recentDownloads = records
      .sort((a, b) => new Date(b.downloadTime).getTime() - new Date(a.downloadTime).getTime())
      .slice(0, 100);

    return stats;
  }

  /**
   * 过滤下载记录
   * @param records 下载记录数组
   * @param query 查询条件
   * @returns 过滤后的记录
   */
  static filterRecords(records: DownloadRecord[], query: DownloadStatsQuery): DownloadRecord[] {
    let filtered = records;

    // 按日期范围过滤
    if (query.startDate) {
      const startDate = new Date(query.startDate);
      filtered = filtered.filter(record => new Date(record.downloadTime) >= startDate);
    }

    if (query.endDate) {
      const endDate = new Date(query.endDate + 'T23:59:59.999Z');
      filtered = filtered.filter(record => new Date(record.downloadTime) <= endDate);
    }

    // 按版本过滤
    if (query.version) {
      filtered = filtered.filter(record => record.version === query.version);
    }

    // 按平台过滤
    if (query.platform) {
      filtered = filtered.filter(record => record.platform === query.platform);
    }

    // 按渠道过滤
    if (query.channel) {
      filtered = filtered.filter(record => record.channel === query.channel);
    }

    // 分页
    const offset = query.offset || 0;
    const limit = query.limit || 100;

    return filtered.slice(offset, offset + limit);
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化后的大小字符串
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 从文件名解析版本信息
   * @param filename 文件名
   * @returns 解析后的版本信息
   */
  static parseFileMetadata(filename: string): {
    version: string;
    platform: string;
    architecture: string;
    channel: string;
  } {
    try {
      // 解析版本号 (例如: 1.0.0)
      const versionMatch = filename.match(/(\d+\.\d+\.\d+)/);
      const version = versionMatch?.[1] || '1.0.0';

      // 解析平台 (windows, macos, linux)
      const platformMatch = filename.match(/(windows|macos|linux)/i);
      const platform = platformMatch?.[1]?.toLowerCase() || 'windows';

      // 解析架构 (x86_64, aarch64, x86)
      const archMatch = filename.match(/(x86_64|aarch64|x86)/i);
      const architecture = archMatch?.[1]?.toLowerCase() || 'x86_64';

      // 解析渠道 (stable, beta, alpha)
      let channel = 'stable';
      if (filename.includes('-beta')) {
        channel = 'beta';
      } else if (filename.includes('-alpha')) {
        channel = 'alpha';
      }

      return {
        version,
        platform,
        architecture,
        channel,
      };
    } catch (error) {
      logger.warn('Failed to parse file metadata', { filename, error });
      return {
        version: '1.0.0',
        platform: 'windows',
        architecture: 'x86_64',
        channel: 'stable',
      };
    }
  }
}
