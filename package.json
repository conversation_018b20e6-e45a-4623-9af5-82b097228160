{"name": "realitytap-ecosystem", "version": "1.0.0", "description": "RealityTap ecosystem monorepo", "private": true, "scripts": {"build:shared": "pnpm --filter @realitytap/shared build", "build:desktop": "pnpm --filter realitytap-desktop build", "build:server": "pnpm --filter realitytap-ota-server build", "build:admin": "pnpm --filter realitytap-ota-admin build", "build:all": "pnpm -r build", "dev:shared": "pnpm --filter @realitytap/shared dev", "dev:desktop": "pnpm --filter realitytap-desktop dev", "dev:server": "pnpm --filter realitytap-ota-server dev", "dev:admin": "pnpm --filter realitytap-ota-admin dev", "tauri:dev": "pnpm --filter realitytap-desktop tauri:dev", "tauri:build": "pnpm --filter realitytap-desktop tauri:build", "install:all": "pnpm install", "clean": "pnpm -r clean", "lint": "pnpm -r lint", "test": "pnpm -r test", "type-check": "pnpm -r type-check"}, "devDependencies": {"typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}