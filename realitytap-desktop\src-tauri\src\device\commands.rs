// Tauri commands for device management

use crate::device::manager::<PERSON>ceManager;
use crate::error::Result;
use crate::models::device::*;
use std::sync::Arc;
use tauri::State;
use tokio::sync::RwLock;

// Global device manager state
pub type DeviceManagerState = Arc<RwLock<DeviceManager>>;

// Initialize device manager state
pub fn init_device_manager() -> DeviceManagerState {
    Arc::new(RwLock::new(DeviceManager::new()))
}

// === Device Scanning Commands ===

#[tauri::command]
pub async fn scan_devices(
    options: Option<DeviceScanOptions>,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<Vec<Device>> {
    log::info!("scan_devices called with options: {:?}", options);
    
    let manager = device_manager.read().await;
    let result = manager.scan_devices(options).await;
    
    match &result {
        Ok(devices) => log::info!("<PERSON>an completed, found {} devices", devices.len()),
        Err(e) => log::error!("<PERSON><PERSON> failed: {}", e),
    }
    
    result
}

#[tauri::command]
pub async fn get_scan_status(
    device_manager: State<'_, DeviceManagerState>,
) -> Result<bool> {
    let manager = device_manager.read().await;
    Ok(manager.is_scanning().await)
}

#[tauri::command]
pub async fn get_last_scan_time(
    device_manager: State<'_, DeviceManagerState>,
) -> Result<Option<String>> {
    let manager = device_manager.read().await;
    Ok(manager.get_last_scan_time().await)
}

// === Device Connection Commands ===

#[tauri::command]
pub async fn connect_device(
    device_id: String,
    config: Option<DeviceConnectionConfig>,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceOperationResult> {
    log::info!("connect_device called for device: {}", device_id);
    
    let manager = device_manager.read().await;
    
    match manager.connect_device(&device_id, config).await {
        Ok(()) => {
            log::info!("Device {} connected successfully", device_id);
            Ok(DeviceOperationResult::success(
                device_id,
                Some("设备连接成功".to_string()),
            ))
        }
        Err(e) => {
            log::error!("Failed to connect device {}: {}", device_id, e);
            Ok(DeviceOperationResult::error(device_id, e.to_string()))
        }
    }
}

#[tauri::command]
pub async fn disconnect_device(
    device_id: String,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceOperationResult> {
    log::info!("disconnect_device called for device: {}", device_id);
    
    let manager = device_manager.read().await;
    
    match manager.disconnect_device(&device_id).await {
        Ok(()) => {
            log::info!("Device {} disconnected successfully", device_id);
            Ok(DeviceOperationResult::success(
                device_id,
                Some("设备断开成功".to_string()),
            ))
        }
        Err(e) => {
            log::error!("Failed to disconnect device {}: {}", device_id, e);
            Ok(DeviceOperationResult::error(device_id, e.to_string()))
        }
    }
}

// === Device Management Commands ===

#[tauri::command]
pub async fn get_all_devices(
    device_manager: State<'_, DeviceManagerState>,
) -> Result<Vec<Device>> {
    log::info!("get_all_devices called");
    
    let manager = device_manager.read().await;
    Ok(manager.get_all_devices().await)
}

#[tauri::command]
pub async fn get_device(
    device_id: String,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<Option<Device>> {
    log::info!("get_device called for device: {}", device_id);
    
    let manager = device_manager.read().await;
    Ok(manager.get_device(&device_id).await)
}

#[tauri::command]
pub async fn add_device(
    device: Device,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceOperationResult> {
    log::info!("add_device called for device: {}", device.device_id);
    
    let manager = device_manager.read().await;
    
    match manager.add_device(device.clone()).await {
        Ok(()) => {
            log::info!("Device {} added successfully", device.device_id);
            Ok(DeviceOperationResult::success(
                device.device_id,
                Some("设备添加成功".to_string()),
            ))
        }
        Err(e) => {
            log::error!("Failed to add device {}: {}", device.device_id, e);
            Ok(DeviceOperationResult::error(device.device_id, e.to_string()))
        }
    }
}

#[tauri::command]
pub async fn remove_device(
    device_id: String,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceOperationResult> {
    log::info!("remove_device called for device: {}", device_id);
    
    let manager = device_manager.read().await;
    
    match manager.remove_device(&device_id).await {
        Ok(removed) => {
            if removed {
                log::info!("Device {} removed successfully", device_id);
                Ok(DeviceOperationResult::success(
                    device_id,
                    Some("设备删除成功".to_string()),
                ))
            } else {
                log::warn!("Device {} not found for removal", device_id);
                Ok(DeviceOperationResult::error(
                    device_id,
                    "设备不存在".to_string(),
                ))
            }
        }
        Err(e) => {
            log::error!("Failed to remove device {}: {}", device_id, e);
            Ok(DeviceOperationResult::error(device_id, e.to_string()))
        }
    }
}

#[tauri::command]
pub async fn update_device(
    device_id: String,
    device: Device,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceOperationResult> {
    log::info!("update_device called for device: {}", device_id);
    
    let manager = device_manager.read().await;
    
    match manager.update_device(&device_id, device).await {
        Ok(updated) => {
            if updated {
                log::info!("Device {} updated successfully", device_id);
                Ok(DeviceOperationResult::success(
                    device_id,
                    Some("设备更新成功".to_string()),
                ))
            } else {
                log::warn!("Device {} not found for update", device_id);
                Ok(DeviceOperationResult::error(
                    device_id,
                    "设备不存在".to_string(),
                ))
            }
        }
        Err(e) => {
            log::error!("Failed to update device {}: {}", device_id, e);
            Ok(DeviceOperationResult::error(device_id, e.to_string()))
        }
    }
}

// === Default Device Commands ===

#[tauri::command]
pub async fn set_default_device(
    device_id: Option<String>,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceOperationResult> {
    log::info!("set_default_device called with device_id: {:?}", device_id);
    
    let manager = device_manager.read().await;
    
    match manager.set_default_device(device_id.clone()).await {
        Ok(()) => {
            let message = if device_id.is_some() {
                "默认设备设置成功".to_string()
            } else {
                "已清除默认设备".to_string()
            };
            log::info!("{}", message);
            Ok(DeviceOperationResult::success(
                device_id.unwrap_or_default(),
                Some(message),
            ))
        }
        Err(e) => {
            log::error!("Failed to set default device: {}", e);
            Ok(DeviceOperationResult::error(
                device_id.unwrap_or_default(),
                e.to_string(),
            ))
        }
    }
}

#[tauri::command]
pub async fn get_default_device(
    device_manager: State<'_, DeviceManagerState>,
) -> Result<Option<Device>> {
    log::info!("get_default_device called");
    
    let manager = device_manager.read().await;
    Ok(manager.get_default_device().await)
}

// === Device Statistics Commands ===

#[tauri::command]
pub async fn get_device_statistics(
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceStatistics> {
    log::info!("get_device_statistics called");
    
    let manager = device_manager.read().await;
    Ok(manager.get_statistics().await)
}

// === Device Status Commands ===

#[tauri::command]
pub async fn get_device_status(
    device_id: String,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<Option<DeviceStatus>> {
    log::info!("get_device_status called for device: {}", device_id);
    
    let manager = device_manager.read().await;
    if let Some(device) = manager.get_device(&device_id).await {
        Ok(Some(device.status))
    } else {
        Ok(None)
    }
}

// === Data Transmission Commands ===

#[tauri::command]
pub async fn send_data_to_device(
    device_id: String,
    data: Vec<u8>,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceOperationResult> {
    log::info!("send_data_to_device called for device: {} with {} bytes", device_id, data.len());
    
    let manager = device_manager.read().await;
    
    match manager.send_data(&device_id, &data).await {
        Ok(()) => {
            log::info!("Data sent successfully to device {}", device_id);
            Ok(DeviceOperationResult::success(
                device_id,
                Some(format!("数据发送成功 ({} 字节)", data.len())),
            ))
        }
        Err(e) => {
            log::error!("Failed to send data to device {}: {}", device_id, e);
            Ok(DeviceOperationResult::error(device_id, e.to_string()))
        }
    }
}

// === Configuration Commands ===

#[tauri::command]
pub async fn get_device_manager_config(
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceManagerConfig> {
    log::info!("get_device_manager_config called");
    
    let manager = device_manager.read().await;
    Ok(manager.get_config().await)
}

#[tauri::command]
pub async fn update_device_manager_config(
    config: DeviceManagerConfig,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceOperationResult> {
    log::info!("update_device_manager_config called");
    
    let manager = device_manager.read().await;
    
    match manager.update_config(config).await {
        Ok(()) => {
            log::info!("Device manager config updated successfully");
            Ok(DeviceOperationResult::success(
                String::new(),
                Some("配置更新成功".to_string()),
            ))
        }
        Err(e) => {
            log::error!("Failed to update device manager config: {}", e);
            Ok(DeviceOperationResult::error(String::new(), e.to_string()))
        }
    }
}

// === Data Loading/Saving Commands ===

#[tauri::command]
pub async fn load_device_manager_data(
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceManagerData> {
    log::info!("load_device_manager_data called");
    
    let manager = device_manager.read().await;
    let devices = manager.get_all_devices().await;
    let default_device = manager.get_default_device().await;
    let config = manager.get_config().await;
    
    Ok(DeviceManagerData {
        devices,
        default_device_id: default_device.map(|d| d.device_id),
        config,
        last_updated: chrono::Utc::now().to_rfc3339(),
    })
}

#[tauri::command]
pub async fn save_device_manager_data(
    data: DeviceManagerData,
    device_manager: State<'_, DeviceManagerState>,
) -> Result<DeviceOperationResult> {
    log::info!("save_device_manager_data called with {} devices", data.devices.len());
    
    let manager = device_manager.read().await;
    
    // Update config
    if let Err(e) = manager.update_config(data.config).await {
        log::error!("Failed to update config: {}", e);
        return Ok(DeviceOperationResult::error(
            String::new(),
            format!("配置保存失败: {}", e),
        ));
    }
    
    // Set default device
    if let Err(e) = manager.set_default_device(data.default_device_id).await {
        log::error!("Failed to set default device: {}", e);
        return Ok(DeviceOperationResult::error(
            String::new(),
            format!("默认设备设置失败: {}", e),
        ));
    }
    
    // Add devices
    for device in data.devices {
        if let Err(e) = manager.add_device(device.clone()).await {
            log::error!("Failed to add device {}: {}", device.device_id, e);
        }
    }
    
    log::info!("Device manager data saved successfully");
    Ok(DeviceOperationResult::success(
        String::new(),
        Some("设备管理器数据保存成功".to_string()),
    ))
}
