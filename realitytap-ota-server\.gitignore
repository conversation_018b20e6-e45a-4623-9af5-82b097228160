# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
storage/logs/*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Storage files (keep structure but ignore content)
storage/metadata/*
storage/releases/*
!storage/releases/.gitkeep
storage/logs/*
!storage/logs/.gitkeep

# Temporary files
tmp/
temp/
*.tmp

# Docker
.dockerignore

# PM2
ecosystem.config.js

# Test files
test-results/
playwright-report/
test-results.xml

# Backup
storage/backup/

# Compiled scripts (TypeScript to JavaScript)
scripts/*.js
scripts/*.js.map
scripts/*.d.ts
scripts/*.d.ts.map

# Temporary migration files
scripts/temp-*.ts
scripts/check-*.ts
scripts/simple-*.ts
scripts/migrate-remaining-*.ts
scripts/add-missing-*.ts

# Test request files
test-request.json
*.test.json

# sqlite database file
*.db