package android.os.vibrator.realitytap.he;

import android.annotation.NonNull;
import android.os.Parcel;
import android.os.Parcelable;

/** @hide */
public class Event implements Parcelable {
    /** Event长振类型 */
    public static final String TYPE_CONTINUOUS = "continuous";
    /** Event短振类型 */
    public static final String TYPE_TRANSIENT = "transient";

    /** Event类型 */
    public static final int CONTINUOUS = 0x1000;
    /** Event类型 */
    public static final int TRANSIENT = 0x1001;

    /** Event类型 */
    private String type;

    /** Event持续时间 */
    private int duration;

    /** Event相对时间 */
    private int relativeTime;

    /** 马达ID 默认为0 */
    private int index = 0;

    /** 包络点列表，仅Continuous事件有效 */
    private Parameters parameters;

    public Event() {}

    protected Event(@NonNull Parcel in) {
        int typeInt = in.readInt();
        type = typeInt == TRANSIENT ? TYPE_TRANSIENT : TYPE_CONTINUOUS;
        duration = in.readInt();
        relativeTime = in.readInt();
        index = in.readInt();
        parameters = in.readParcelable(Parameters.class.getClassLoader());
    }

    public static final @NonNull Creator<Event> CREATOR = new Creator<>() {
        @Override
        public Event createFromParcel(@NonNull Parcel in) {
            return new Event(in);
        }

        @Override
        public Event[] newArray(int size) {
            return new Event[size];
        }
    };

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getDuration() {
        // transition event fixed length
        return TYPE_TRANSIENT.equals(type) ? 30 : duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getRelativeTime() {
        return relativeTime;
    }

    public void setRelativeTime(int relativeTime) {
        this.relativeTime = relativeTime;
    }

    public Parameters getParameters() {
        return parameters;
    }

    public void setParameters(Parameters parameters) {
        this.parameters = parameters;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        int typeInt = TYPE_TRANSIENT.equals(type) ? TRANSIENT : CONTINUOUS;
        dest.writeInt(typeInt);
        dest.writeInt(duration);
        dest.writeInt(relativeTime);
        dest.writeInt(index);
        dest.writeParcelable(parameters, flags);
    }

    @NonNull
    public Event copy() {
        Event copy = new Event();
        copy.setType(type);
        copy.setDuration(duration);
        copy.setRelativeTime(relativeTime);
        copy.setIndex(index);
        copy.setParameters(parameters != null ? parameters.copy() : null);
        return copy;
    }

    @NonNull
    @Override
    public String toString() {
        return "Event{" +
                "type='" + type + '\'' +
                ", duration=" + duration +
                ", relativeTime=" + relativeTime +
                ", index=" + index +
                ", parameters=" + parameters +
                '}';
    }
}
