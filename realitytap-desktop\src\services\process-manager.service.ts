/**
 * 进程管理服务
 * 处理RealityTap应用进程的检测、关闭和管理
 */

import { invoke } from '@tauri-apps/api/core';
import { logger, LogModule } from '@/utils/logger/logger';

// === 类型定义 ===

export interface ProcessInfo {
  pid: number;
  name: string;
  description: string;
  processType: string;
  priority: number;
  isCritical: boolean;
  executablePath?: string;
  memoryUsage: number;
  cpuUsage: number;
}

export interface ProcessCloseResult {
  pid: number;
  name: string;
  success: boolean;
  errorMessage?: string;
  closeMethod: string;
  closeDurationMs: number;
}

export interface ProcessCloseOptions {
  gracefulTimeout: number; // 优雅关闭超时时间（秒）
  forceClose: boolean;     // 是否强制关闭
  waitForExit: boolean;    // 是否等待进程退出
}

export interface UpdaterLaunchOptions {
  packagePath: string;
  appPath?: string;
  silent: boolean;
  waitTimeout: number;
  restart: boolean;
  backup: boolean;
}

// === 进程管理服务类 ===

export class ProcessManagerService {
  private static instance: ProcessManagerService;

  private constructor() {}

  public static getInstance(): ProcessManagerService {
    if (!ProcessManagerService.instance) {
      ProcessManagerService.instance = new ProcessManagerService();
    }
    return ProcessManagerService.instance;
  }

  /**
   * 获取所有RealityTap相关进程
   */
  async listRealityTapProcesses(): Promise<ProcessInfo[]> {
    try {
      logger.info(LogModule.DEVICE, '🔍 开始检测RealityTap相关进程');
      const processes = await invoke<ProcessInfo[]>('list_realitytap_processes');
      logger.info(LogModule.DEVICE, `✅ 检测完成，共发现 ${processes.length} 个RealityTap进程`);
      return processes;
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 检测RealityTap进程失败', error);
      throw new Error(`Failed to list RealityTap processes: ${error}`);
    }
  }

  /**
   * 优雅关闭进程
   */
  async closeProcessGracefully(
    pid: number,
    timeoutSeconds?: number
  ): Promise<ProcessCloseResult> {
    try {
      logger.info(LogModule.DEVICE, `🔄 开始优雅关闭进程 PID: ${pid}`);
      const result = await invoke<ProcessCloseResult>('close_process_gracefully', {
        pid,
        timeoutSeconds,
      });

      if (result.success) {
        logger.info(LogModule.DEVICE, `✅ 进程 ${result.name} 优雅关闭成功，耗时: ${result.closeDurationMs}ms`);
      } else {
        logger.warn(LogModule.DEVICE, `⚠️ 进程 ${result.name} 优雅关闭失败: ${result.errorMessage}`);
      }

      return result;
    } catch (error) {
      logger.error(LogModule.DEVICE, `❌ 优雅关闭进程 ${pid} 失败`, error);
      throw new Error(`Failed to close process gracefully: ${error}`);
    }
  }

  /**
   * 强制关闭进程
   */
  async forceCloseProcess(pid: number): Promise<ProcessCloseResult> {
    try {
      logger.info(LogModule.DEVICE, `💥 开始强制关闭进程 PID: ${pid}`);
      const result = await invoke<ProcessCloseResult>('force_close_process', { pid });

      if (result.success) {
        logger.info(LogModule.DEVICE, `✅ 进程 ${result.name} 强制关闭成功，耗时: ${result.closeDurationMs}ms`);
      } else {
        logger.error(LogModule.DEVICE, `❌ 进程 ${result.name} 强制关闭失败: ${result.errorMessage}`);
      }

      return result;
    } catch (error) {
      logger.error(LogModule.DEVICE, `❌ 强制关闭进程 ${pid} 失败`, error);
      throw new Error(`Failed to force close process: ${error}`);
    }
  }

  /**
   * 等待所有进程退出
   */
  async waitForProcessesExit(
    pids: number[],
    timeoutSeconds?: number
  ): Promise<boolean> {
    try {
      logger.info(LogModule.DEVICE, `⏳ 等待 ${pids.length} 个进程退出`);
      const allExited = await invoke<boolean>('wait_for_processes_exit', {
        pids,
        timeoutSeconds,
      });

      if (allExited) {
        logger.info(LogModule.DEVICE, '✅ 所有进程已退出');
      } else {
        logger.warn(LogModule.DEVICE, '⚠️ 仍有进程未退出，已超时');
      }

      return allExited;
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 等待进程退出失败', error);
      throw new Error(`Failed to wait for processes exit: ${error}`);
    }
  }

  /**
   * 批量关闭进程
   */
  async closeProcessesBatch(
    pids: number[],
    options: ProcessCloseOptions
  ): Promise<ProcessCloseResult[]> {
    try {
      logger.info(LogModule.DEVICE, `🔄 开始批量关闭 ${pids.length} 个进程`);
      const results = await invoke<ProcessCloseResult[]>('close_processes_batch', {
        pids,
        options,
      });

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      logger.info(LogModule.DEVICE, `✅ 批量关闭完成，成功: ${successCount}, 失败: ${failureCount}`);

      return results;
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 批量关闭进程失败', error);
      throw new Error(`Failed to close processes batch: ${error}`);
    }
  }

  /**
   * 复制updater到临时目录
   */
  async copyUpdaterToTemp(): Promise<string> {
    try {
      logger.info(LogModule.DEVICE, '📁 开始复制updater到临时目录');
      const tempPath = await invoke<string>('copy_updater_to_temp');
      logger.info(LogModule.DEVICE, `✅ Updater复制成功: ${tempPath}`);
      return tempPath;
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 复制updater失败', error);
      throw new Error(`Failed to copy updater to temp: ${error}`);
    }
  }

  /**
   * 启动updater程序
   */
  async launchUpdater(
    updaterPath: string,
    options: UpdaterLaunchOptions
  ): Promise<string> {
    try {
      logger.info(LogModule.DEVICE, `🚀 启动updater程序: ${updaterPath}`);
      logger.info(LogModule.DEVICE, '⚙️ 启动选项', options);

      const pid = await invoke<string>('launch_updater', {
        updaterPath,
        options,
      });

      logger.info(LogModule.DEVICE, `✅ Updater启动成功，PID: ${pid}`);
      return pid;
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 启动updater失败', error);
      throw new Error(`Failed to launch updater: ${error}`);
    }
  }

  /**
   * 获取当前应用的PID
   */
  async getCurrentAppPid(): Promise<number> {
    try {
      const pid = await invoke<number>('get_current_app_pid');
      logger.info(LogModule.DEVICE, `📋 当前应用PID: ${pid}`);
      return pid;
    } catch (error) {
      logger.error(LogModule.DEVICE, '❌ 获取当前应用PID失败', error);
      throw new Error(`Failed to get current app PID: ${error}`);
    }
  }

  /**
   * 检查进程是否存在
   */
  async isProcessRunning(pid: number): Promise<boolean> {
    try {
      const exists = await invoke<boolean>('is_process_running', { pid });
      return exists;
    } catch (error) {
      logger.error(LogModule.DEVICE, `❌ 检查进程 ${pid} 是否存在失败`, error);
      throw new Error(`Failed to check if process is running: ${error}`);
    }
  }

  /**
   * 获取进程详细信息
   */
  async getProcessInfo(pid: number): Promise<ProcessInfo | null> {
    try {
      const info = await invoke<ProcessInfo | null>('get_process_info', { pid });
      return info;
    } catch (error) {
      logger.error(LogModule.DEVICE, `❌ 获取进程 ${pid} 信息失败`, error);
      throw new Error(`Failed to get process info: ${error}`);
    }
  }

  /**
   * 创建默认的进程关闭选项
   */
  createDefaultCloseOptions(): ProcessCloseOptions {
    return {
      gracefulTimeout: 10,
      forceClose: false,
      waitForExit: true,
    };
  }

  /**
   * 创建默认的updater启动选项
   */
  createDefaultUpdaterOptions(packagePath: string): UpdaterLaunchOptions {
    return {
      packagePath,
      silent: true,
      waitTimeout: 120,
      restart: true,
      backup: true,
    };
  }
}

// === 导出单例实例 ===
export const processManagerService = ProcessManagerService.getInstance();
