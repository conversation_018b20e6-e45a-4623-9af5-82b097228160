<template>
  <div class="event-adjust-panel">
    <n-card size="small" class="panel-card" :bordered="false">
      <template #header>
        <div class="panel-header">{{ t("editor.eventProperties.title") }}</div>
      </template>

      <n-scrollbar style="max-height: calc(100vh - 300px)">
        <div v-if="selectedEvent" class="adjustments-container">
          <div v-if="selectedEvent.Type === 'transient'" class="adjust-section">
            <h3 class="section-title">{{ t("editor.eventProperties.transientEvent") }}</h3>
            <SliderControl
              :label="t('editor.eventProperties.startTime')"
              :value="selectedEvent.RelativeTime"
              :min="startTimeMinValue"
              :max="startTimeMaxValue"
              :step="1"
              unit="ms"
              @update:value="updateEventProperty('RelativeTime', $event)"
              @slider-start="onSliderStart('RelativeTime', $event, '调整开始时间')"
              @slider-end="onSliderEnd('RelativeTime', $event)"
            />
            <SliderControl
              :label="t('editor.eventProperties.intensity')"
              :value="selectedEvent.Parameters.Intensity"
              :min="0"
              :max="100"
              :step="1"
              @update:value="updateEventProperty('Parameters.Intensity', $event)"
              @slider-start="onSliderStart('Parameters.Intensity', $event, '调整强度')"
              @slider-end="onSliderEnd('Parameters.Intensity', $event)"
            />
            <SliderControl
              :label="t('editor.eventProperties.frequency')"
              :value="selectedEvent.Parameters.Frequency"
              :min="transientFrequencyMinValue"
              :max="transientFrequencyMaxValue"
              :step="1"
              @update:value="updateEventProperty('Parameters.Frequency', $event)"
              @slider-start="onSliderStart('Parameters.Frequency', $event, '调整频率')"
              @slider-end="onSliderEnd('Parameters.Frequency', $event)"
            />
          </div>

          <div v-if="selectedEvent.Type === 'continuous'" class="adjust-section">
            <h3 class="section-title">{{ t("editor.eventProperties.continuousEvent") }}</h3>
            <SliderControl
              :label="t('editor.eventProperties.startTime')"
              :value="selectedEvent.RelativeTime"
              :min="startTimeMinValue"
              :max="startTimeMaxValue"
              :step="1"
              unit="ms"
              @update:value="updateEventProperty('RelativeTime', $event)"
              @slider-start="onSliderStart('RelativeTime', $event, '调整开始时间')"
              @slider-end="onSliderEnd('RelativeTime', $event)"
            />
            <SliderControl
              :label="t('editor.eventProperties.globalIntensity')"
              :value="selectedEvent.Parameters.Intensity"
              :min="0"
              :max="100"
              :step="1"
              @update:value="updateEventProperty('Parameters.Intensity', $event)"
              @slider-start="onSliderStart('Parameters.Intensity', $event, '调整全局强度')"
              @slider-end="onSliderEnd('Parameters.Intensity', $event)"
            />
            <SliderControl
              :label="t('editor.eventProperties.globalFrequency')"
              :value="selectedEvent.Parameters.Frequency"
              :min="0"
              :max="100"
              :step="1"
              @update:value="updateEventProperty('Parameters.Frequency', $event)"
              @slider-start="onSliderStart('Parameters.Frequency', $event, '调整全局频率')"
              @slider-end="onSliderEnd('Parameters.Frequency', $event)"
            />
            <SliderControl
              :label="t('editor.eventProperties.duration')"
              :value="selectedEvent.Duration ?? 1"
              :min="25"
              :max="durationMaxValue"
              :step="1"
              unit="ms"
              @update:value="updateEventProperty('Duration', $event)"
              @slider-start="onSliderStart('Duration', $event, '调整持续时间')"
              @slider-end="onSliderEnd('Duration', $event)"
            />

            <!-- 选中曲线点时显示的调整面板 -->
            <div v-if="selectedCurvePoint" class="adjust-subsection">
              <h4 class="subsection-title">{{ t("editor.eventProperties.selectedCurvePoint") }}</h4>
              <div class="curve-point-info">
                <span class="curve-point-index">{{ t("editor.eventProperties.pointNumber") }}{{ selectedCurvePointIndex + 1 }}</span>
                <span class="curve-point-time">{{ t("editor.eventProperties.time") }}: {{ Math.round(selectedCurvePoint.timeOffset) }}ms</span>
              </div>
              <!-- 曲线点幅度调整滑块 -->
              <SliderControl
                v-if="!isFirstOrLastPoint"
                :label="t('editor.eventProperties.pointRelativeIntensity')"
                :value="selectedCurvePoint.rawIntensity"
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                @update:value="updateCurvePointRawIntensity($event)"
              />
              <div v-if="!isFirstOrLastPoint && selectedEvent && selectedEvent.Type === 'continuous'" class="computed-intensity-display">
                {{ t("editor.eventProperties.computedAbsoluteIntensity") }}:
                {{ (calculateRawIntensity * selectedEvent.Parameters.Intensity).toFixed(2) }}
              </div>
              <div v-if="isFirstOrLastPoint" class="point-hint intensity-zero-hint">{{ t("editor.eventProperties.firstLastPointZeroIntensity") }}</div>
              <SliderControl
                :label="t('editor.eventProperties.pointRelativeFrequency')"
                :value="selectedCurvePoint.relativeCurveFrequency"
                :min="-100"
                :max="100"
                :step="1"
                @update:value="updateCurvePointRelativeFrequency($event)"
              />
              <div v-if="selectedEvent && selectedEvent.Type === 'continuous'" class="computed-frequency-display">
                {{ t("editor.eventProperties.computedAbsoluteFrequency") }}:
                {{ Math.round(selectedCurvePoint.relativeCurveFrequency + selectedEvent.Parameters.Frequency) }}
              </div>
              <div class="point-hint">{{ getFrequencyHintFixed() }}</div>
            </div>
          </div>
        </div>

        <div v-else class="no-event-selected">{{ t("editor.eventProperties.selectEventToAdjust") }}</div>
      </n-scrollbar>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import SliderControl from "./ParameterSliderControl.vue";
import { useFileStateSync } from "@/composables/useFileStateSync";
import { getTransientDuration } from "@/types/haptic-file";
import { MIN_EVENT_INTERVAL_MS, getFrequencyAdjustmentKeyDisplayName } from "@/components/editor/waveform/config/waveform-constants";
import { useI18n } from "@/composables/useI18n";
import { globalDataFlowManager } from "@/utils/performance/BidirectionalDataFlowManager";

export interface EventParameters {
  Intensity: number;
  Frequency: number;
}

export interface SelectedEvent {
  Type: "transient" | "continuous";
  RelativeTime: number;
  Parameters: EventParameters;
  Duration?: number;
}

// 定义props
const props = defineProps<{
  fileUuid: string | null; // 文件UUID，用于文件级别状态隔离，可以为null
}>();

// 国际化
const { t } = useI18n();

// 导入所有语言文件来支持多语言
import zhCN from "@/locales/zh-CN";
import enUS from "@/locales/en-US";
import jaJP from "@/locales/ja-JP";
import koKR from "@/locales/ko-KR";
import { useLanguageStore } from "@/stores/language-store";

// 修复的频率提示函数，支持多语言并绕过 Vue I18n 问题
const getFrequencyHintFixed = () => {
  const languageStore = useLanguageStore();
  const currentLocale = languageStore.currentLocale;

  // 根据当前语言获取正确的文本
  let baseText: string;
  switch (currentLocale) {
    case "zh-CN":
      baseText = zhCN.editor.eventProperties.frequencyAdjustmentHint;
      break;
    case "en-US":
      baseText = enUS.editor.eventProperties.frequencyAdjustmentHint;
      break;
    case "ja-JP":
      baseText = jaJP.editor.eventProperties.frequencyAdjustmentHint;
      break;
    case "ko-KR":
      baseText = koKR.editor.eventProperties.frequencyAdjustmentHint;
      break;
    default:
      baseText = zhCN.editor.eventProperties.frequencyAdjustmentHint;
  }

  const keyDisplayName = getFrequencyAdjustmentKeyDisplayName();
  return baseText.replace("{key}", keyDisplayName);
};

// 使用文件级状态同步
const fileStateSync = useFileStateSync({
  fileUuid: props.fileUuid,
  enableLogging: true,
});

// 从状态同步中获取 store 和操作接口
const { store: waveformStore, operations } = fileStateSync;

// 简化的历史记录管理
import { logger, LogModule } from "@/utils/logger/logger";

// 直接更新 store，防抖已在 SliderControl 中处理
const updateSelectedEvent = (eventDetail: SelectedEvent) => {
  // 通过数据流管理器协调更新，避免与Canvas拖拽冲突
  globalDataFlowManager
    .submitUpdate(
      "panel",
      "property",
      { eventDetail, eventId: selectedRenderableEvent.value?.id },
      "high" // 用户在属性面板的操作是高优先级
    )
    .then(() => {
      operations.value.updateEvent(eventDetail);
    })
    .catch((error) => {
      logger.error(LogModule.GENERAL, "属性更新失败", error);
    });
};

const updateCurvePoint = (payload: any) => {
  operations.value.updateEvent(payload); // updateEvent handles curve point specific payloads
};

// 使用计算属性获取选中的事件
const selectedEvent = computed(() => waveformStore.value.selectedEvent);

// 获取原始的可渲染事件
const selectedRenderableEvent = computed(() => waveformStore.value.selectedRenderableEvent);

// 获取所有事件
const allEvents = computed(() => waveformStore.value.events);

// 获取选中事件的索引
const selectedEventIndex = computed(() => {
  if (!selectedRenderableEvent.value) return -1;
  return allEvents.value.findIndex((e: any) => e.id === selectedRenderableEvent.value?.id);
});

// 获取选中的曲线点和索引
const selectedCurvePoint = computed(() => waveformStore.value.selectedCurvePoint);
const selectedCurvePointIndex = computed(() => waveformStore.value.$state.selectedCurvePointIndex);

// 判断当前选中的点是否为首尾点
const isFirstOrLastPoint = computed(() => {
  if (selectedCurvePointIndex.value === -1 || !selectedRenderableEvent.value || selectedRenderableEvent.value.type !== "continuous") return false;
  const continuousEvent = selectedRenderableEvent.value;
  return selectedCurvePointIndex.value === 0 || selectedCurvePointIndex.value === continuousEvent.curves.length - 1;
});

// 计算选中点的原始强度比例
const calculateRawIntensity = computed(() => {
  if (!selectedCurvePoint.value || !selectedRenderableEvent.value || selectedRenderableEvent.value.type !== "continuous") return 0;
  if (isFirstOrLastPoint.value) return 0; // 首尾点强度总是0

  const continuousEvent = selectedRenderableEvent.value;
  // 防止除零错误
  if (continuousEvent.eventIntensity <= 0) return 0;

  return selectedCurvePoint.value.rawIntensity;
});

// 计算"Start Time"滑块的最小值
const startTimeMinValue = computed(() => {
  if (selectedEventIndex.value <= 0) return 0; // 如果是第一个事件或没找到，最小值为0

  const prevEvent = allEvents.value[selectedEventIndex.value - 1];
  if (!prevEvent) return 0;

  // 如果前一个事件是连续事件，使用其结束时间
  if (prevEvent.type === "continuous") {
    return prevEvent.stopTime;
  }
  // 如果前一个事件是瞬时事件，使用其结束时间
  else if (prevEvent.type === "transient") {
    return prevEvent.stopTime;
  }

  return 0;
});

// 计算当前事件的持续时间
const currentEventDuration = computed(() => {
  if (!selectedRenderableEvent.value) return 0;

  return selectedRenderableEvent.value.stopTime - selectedRenderableEvent.value.startTime;
});

// 计算"Start Time"滑块的最大值
const startTimeMaxValue = computed(() => {
  const eventsCount = allEvents.value.length;
  const duration = currentEventDuration.value;

  // 如果没有后续事件，最大值为总时长减去当前事件时长
  if (selectedEventIndex.value === -1 || selectedEventIndex.value >= eventsCount - 1) {
    return Math.max(0, waveformStore.value.totalDuration - duration); // 使用store中的总时长
  }

  // 如果有后续事件，最大值为后续事件的开始时间减去当前事件时长
  const nextEvent = allEvents.value[selectedEventIndex.value + 1];
  if (!nextEvent) return Math.max(0, waveformStore.value.totalDuration - duration);

  // 确保开始时间加上持续时间不超过下一个事件的开始时间
  const maxStart = nextEvent.startTime - duration;
  return Math.max(0, maxStart); // 避免负值
});

// 计算连续事件Duration的最大值
const durationMaxValue = computed(() => {
  if (!selectedEvent.value || selectedEvent.value.Type !== "continuous") return waveformStore.value.totalDuration;

  const eventsCount = allEvents.value.length;
  const currentStartTime = selectedEvent.value.RelativeTime;

  // 默认最大值：总时长 - 当前事件开始时间
  let maxDuration = waveformStore.value.totalDuration - currentStartTime;

  // 如果有后续事件，限制最大值为后续事件开始时间 - 当前事件开始时间
  if (selectedEventIndex.value !== -1 && selectedEventIndex.value < eventsCount - 1) {
    const nextEvent = allEvents.value[selectedEventIndex.value + 1];
    if (nextEvent) {
      maxDuration = Math.min(maxDuration, nextEvent.startTime - currentStartTime);
    }
  }

  return Math.max(25, maxDuration); // 确保最小值为25
});

// 计算 transient 事件频率的最小值
const transientFrequencyMinValue = computed(() => {
  if (!selectedEvent.value || selectedEvent.value.Type !== "transient") return 0;

  const currentStartTime = selectedEvent.value.RelativeTime;
  const eventsCount = allEvents.value.length;

  // 确定下一个事件的开始时间（如果有的话）
  let nextEventStartTime: number | undefined;
  if (selectedEventIndex.value !== -1 && selectedEventIndex.value < eventsCount - 1) {
    const nextEvent = allEvents.value[selectedEventIndex.value + 1];
    if (nextEvent) {
      nextEventStartTime = nextEvent.startTime;
    }
  }

  // 计算考虑所有约束的最大允许持续时间
  const maxAllowedDuration = calculateMaxAllowedDuration(currentStartTime, nextEventStartTime);

  return calculateMinFrequencyForDuration(maxAllowedDuration);
});

// 辅助函数：根据最大允许持续时间计算最小频率
const calculateMinFrequencyForDuration = (maxAllowedDuration: number): number => {
  // 如果可用时间太小，无法放置任何 transient 事件
  if (maxAllowedDuration < 8) return 100; // 8ms是频率100时的最小持续时间

  // 如果可用时间足够大，可以使用最小频率
  if (maxAllowedDuration >= 25) return 0; // 25ms是频率0时的最大持续时间

  // 使用二分查找找到满足持续时间约束的最小频率
  let minFreq = 0;
  let maxFreq = 100;

  while (minFreq <= maxFreq) {
    const midFreq = Math.floor((minFreq + maxFreq) / 2);
    const duration = getTransientDuration(midFreq);

    if (duration <= maxAllowedDuration) {
      // 当前频率可行，尝试更小的频率（更长的持续时间）
      maxFreq = midFreq - 1;
    } else {
      // 当前频率不可行，需要更大的频率（更短的持续时间）
      minFreq = midFreq + 1;
    }
  }

  return Math.max(0, Math.min(100, minFreq));
};

// 辅助函数：计算考虑所有约束的最大允许持续时间
const calculateMaxAllowedDuration = (currentStartTime: number, nextEventStartTime?: number): number => {
  let maxDuration = Infinity;

  // 约束1：总时长限制
  const totalDuration = waveformStore.value.totalDuration;
  maxDuration = Math.min(maxDuration, totalDuration - currentStartTime);

  // 约束2：下一个事件的位置限制
  if (nextEventStartTime !== undefined) {
    maxDuration = Math.min(maxDuration, nextEventStartTime - currentStartTime - MIN_EVENT_INTERVAL_MS);
  }

  return Math.max(0, maxDuration);
};

// 计算 transient 事件频率的最大值
const transientFrequencyMaxValue = computed(() => {
  if (!selectedEvent.value || selectedEvent.value.Type !== "transient") return 100;

  // 默认最大频率为100，但需要考虑各种约束
  return 100;
});

// 简化的滑块开始事件处理（历史记录由Store自动管理）
const onSliderStart = (propertyPath: string, value: number, description: string) => {
  logger.info(LogModule.GENERAL, "[HapticEventPropertiesPanel] 🎛️ 滑块开始事件", {
    propertyPath,
    value,
    description,
    selectedEventType: selectedEvent.value?.Type,
    timestamp: new Date().toLocaleTimeString(),
  });
};

// 滑块结束事件处理 - 历史记录由统一机制自动处理
const onSliderEnd = (propertyPath: string, value: number) => {
  logger.info(LogModule.GENERAL, "[HapticEventPropertiesPanel] 🏁 滑块结束事件", {
    propertyPath,
    value,
    selectedEventType: selectedEvent.value?.Type,
    timestamp: new Date().toLocaleTimeString(),
  });
  // 注意：历史记录现在由store的统一机制自动处理，无需手动添加
};

// 属性更新函数 - 历史记录由统一机制自动处理
const updateEventProperty = (propertyPath: string, value: number) => {
  if (!selectedEvent.value) return;

  const currentSelectedEvent = selectedEvent.value;
  let updatedEvent: SelectedEvent = { ...currentSelectedEvent };

  if (propertyPath.startsWith("Parameters.")) {
    updatedEvent.Parameters = { ...currentSelectedEvent.Parameters };
  }

  const setNestedProperty = (obj: any, path: string, val: number) => {
    const keys = path.split(".");
    let current = obj;
    while (keys.length > 1) {
      const key = keys.shift();
      if (!key) continue;
      if (!current[key] || typeof current[key] !== "object") {
        current[key] = {};
      }
      current = current[key];
    }
    current[keys[0]] = val;
  };

  setNestedProperty(updatedEvent, propertyPath, value);

  // 更新 store - 历史记录由统一机制自动处理
  updateSelectedEvent(updatedEvent);
};

// 曲线点强度更新函数 - 历史记录由统一机制自动处理
const updateCurvePointRawIntensity = (value: number) => {
  if (!selectedEvent.value || selectedEvent.value.Type !== "continuous" || selectedCurvePointIndex.value < 0 || !selectedCurvePoint.value) return;
  if (isFirstOrLastPoint.value) return; // 首尾点不能更新强度

  // 确保 rawIntensity 值保持两位小数精度
  const formattedValue = Number(value.toFixed(2));

  const updatePayload = {
    Type: "continuous",
    updateType: "UPDATE_CURVE_POINT",
    rawIntensity: formattedValue,
    curveIndex: selectedCurvePointIndex.value,
  };

  // 更新 store - 历史记录由统一机制自动处理
  updateCurvePoint(updatePayload);
};

// 简化的曲线点频率更新函数（不再处理历史记录）
const updateCurvePointRelativeFrequency = (value: number) => {
  if (!selectedEvent.value || selectedEvent.value.Type !== "continuous" || selectedCurvePointIndex.value < 0 || !selectedCurvePoint.value) return;

  const updatePayload = {
    Type: "continuous",
    updateType: "UPDATE_CURVE_POINT",
    relativeCurveFrequency: value,
    curveIndex: selectedCurvePointIndex.value,
  };

  // 直接更新 store
  updateCurvePoint(updatePayload);
};
</script>

<style scoped>
.event-adjust-panel {
  height: 100%;
  overflow: hidden;
  width: 260px;
  min-width: 260px;
}

.panel-card {
  background-color: transparent;
  border-radius: 0px 14px 14px 0px;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-left: 1px solid rgba(255, 255, 255, 0.06);
  box-shadow:
    inset 1px 0 0 rgba(255, 255, 255, 0.02),
    0 2px 12px rgba(0, 0, 0, 0.08);
}

.panel-header {
  font-size: 14px;
  font-weight: 500;
  color: #e0e0e0;
  padding: 2px 0;
}

:deep(.n-card-header) {
  height: 60px;
  min-height: 60px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.adjustments-container {
  padding: 10px 5px;
}

.adjust-section {
  margin-bottom: 15px;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #36ad6a;
  margin: 5px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #36ad6a35;
}

.adjust-subsection {
  background-color: #293042;
  border-radius: 8px;
  padding: 10px;
  margin-top: 10px;
}

.subsection-title {
  font-size: 12px;
  font-weight: 600;
  color: #a0a0a0;
  margin: 0 0 10px 0;
}

.curve-point-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: #ff6767;
}

.computed-intensity-display,
.computed-frequency-display {
  font-size: 12px;
  color: #a0a0a0;
  margin: 5px 0 10px 0;
  text-align: right;
}

.point-hint {
  font-size: 11px;
  font-style: italic;
  color: #78e9b1;
  margin: 8px 0;
}

.intensity-zero-hint {
  color: #e07676;
}

.no-event-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #7d8899;
  font-style: italic;
  padding: 20px;
  text-align: center;
}

/* 避免滚动条出现时的布局跳动 */
:deep(.n-scrollbar-rail) {
  right: 1px !important;
}
</style>
