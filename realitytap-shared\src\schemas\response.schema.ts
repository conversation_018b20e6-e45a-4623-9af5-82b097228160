import { z } from 'zod';

/**
 * 基础 API 响应 Schema
 */
export const BaseAPIResponseSchema = z.object({
  success: z.boolean(),
  timestamp: z.string().datetime(),
  version: z.string(),
});

/**
 * 成功响应 Schema
 */
export const SuccessResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  BaseAPIResponseSchema.extend({
    success: z.literal(true),
    data: dataSchema,
  });

/**
 * 错误响应 Schema
 */
export const ErrorResponseSchema = BaseAPIResponseSchema.extend({
  success: z.literal(false),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional(),
  }),
});

/**
 * 分页信息 Schema
 */
export const PaginationSchema = z.object({
  page: z.number().positive(),
  limit: z.number().positive(),
  total: z.number().min(0),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});

/**
 * 分页响应 Schema
 */
export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  BaseAPIResponseSchema.extend({
    success: z.literal(true),
    data: z.array(itemSchema),
    pagination: PaginationSchema,
  });

/**
 * 健康检查响应 Schema
 */
export const HealthResponseSchema = z.object({
  status: z.enum(['healthy', 'unhealthy']),
  timestamp: z.string().datetime(),
  uptime: z.number().min(0),
  version: z.string(),
  storage: z.object({
    available: z.boolean(),
    freeSpace: z.number().optional(),
    totalSpace: z.number().optional(),
  }).optional(),
  memory: z.object({
    used: z.number().min(0),
    total: z.number().positive(),
    percentage: z.number().min(0).max(100),
  }).optional(),
});

/**
 * 就绪检查响应 Schema
 */
export const ReadinessResponseSchema = z.object({
  status: z.enum(['ready', 'not ready']),
  timestamp: z.string().datetime(),
  error: z.string().optional(),
});

/**
 * 存活检查响应 Schema
 */
export const LivenessResponseSchema = z.object({
  status: z.literal('alive'),
  timestamp: z.string().datetime(),
  uptime: z.number().min(0),
});

/**
 * 版本统计响应 Schema
 */
export const VersionStatsResponseSchema = z.object({
  totalChannels: z.number().min(0),
  enabledChannels: z.number().min(0),
  totalVersions: z.number().min(0),
  platforms: z.array(z.string()),
  architectures: z.array(z.string()),
  deprecatedVersions: z.array(z.string()),
  minimumVersions: z.record(z.string()),
});

/**
 * 缓存清除响应 Schema
 */
export const CacheClearResponseSchema = z.object({
  message: z.string(),
});

/**
 * 校验和响应 Schema
 */
export const ChecksumResponseSchema = z.object({
  filename: z.string(),
  checksum: z.string().regex(/^(sha256|md5):[a-fA-F0-9]+$/),
});
