import { computed, type Ref } from "vue";
import { h } from "vue";
import { NIcon } from "naive-ui";
import type { HapticFile, HapticsGroup, RealityTapProject } from "@/types/haptic-project";
import type { TreeNode } from "@/utils/tree/treeUtils";
import { sortTreeNodes } from "@/utils/tree/treeUtils";
import { Folder20Regular, MusicNote220Regular, DocumentText20Regular, Video20Regular } from "@vicons/fluent";

/**
 * Composable for building tree data structure from project data
 */
export function useTreeDataBuilder(
  activeProject: Ref<RealityTapProject | null>,
  isCreatingNewGroup: Ref<boolean>,
  newGroupTemporaryKey: Ref<string | null>,
  newGroupName: Ref<string>,
  newGroupParentUuid: Ref<string | null>
) {
  /**
   * Helper to render icons for tree nodes
   */
  const renderNodeIcon = (item: HapticFile | HapticsGroup, isGroup: boolean) => {
    return () => {
      if (isGroup) {
        return h(NIcon, null, { default: () => h(Folder20Regular) });
      }
      // It's a file
      const file = item as HapticFile;

      // 检查是否有关联的视频文件
      if (file.associatedVideo && file.associatedVideo.trim() !== "") {
        return h(NIcon, null, { default: () => h(Video20Regular) });
      }

      // 检查是否有关联的音频文件
      if (file.associatedAudio && file.associatedAudio.trim() !== "") {
        return h(NIcon, null, { default: () => h(MusicNote220Regular) });
      }

      // 默认文档图标
      return h(NIcon, null, { default: () => h(DocumentText20Regular) });
    };
  };

  /**
   * Build group nodes and create mapping
   */
  const buildGroupNodes = (projectGroups: HapticsGroup[]): Map<string, TreeNode> => {
    const groupMap = new Map<string, TreeNode>();

    // 第一次遍历：为每个分组创建TreeNode并加入映射表
    projectGroups.forEach((group) => {
      const groupNode: TreeNode = {
        key: `group-${group.groupUuid}`,
        label: group.name,
        isGroup: true,
        item: group,
        children: [],
        prefix: renderNodeIcon(group, true),
      };

      groupMap.set(group.groupUuid, groupNode);
    });

    return groupMap;
  };

  /**
   * Build hierarchy relationships between groups
   */
  const buildGroupHierarchy = (projectGroups: HapticsGroup[], groupMap: Map<string, TreeNode>): TreeNode[] => {
    const topLevelNodes: TreeNode[] = [];

    // 第二次遍历：根据parentGroupUuid构建层级关系
    projectGroups.forEach((group) => {
      if (group.parentGroupUuid) {
        // 如果有父分组，找到父节点并将当前节点添加为子节点
        const parentNode = groupMap.get(group.parentGroupUuid);
        if (parentNode) {
          if (!parentNode.children) parentNode.children = [];
          parentNode.children.push(groupMap.get(group.groupUuid)!);
        } else {
          // 如果找不到父节点（可能是数据不一致），作为顶层节点处理
          topLevelNodes.push(groupMap.get(group.groupUuid)!);
        }
      } else {
        // 如果是顶层节点（parentGroupUuid为null），直接添加到结果数组
        topLevelNodes.push(groupMap.get(group.groupUuid)!);
      }
    });

    return topLevelNodes;
  };

  /**
   * Attach files to their corresponding groups
   */
  const attachFilesToGroups = (projectFiles: HapticFile[], groupMap: Map<string, TreeNode>): TreeNode[] => {
    const orphanFiles: TreeNode[] = [];

    projectFiles.forEach((file) => {
      // 只显示 fileType === 'he' 或（无 fileType 时扩展名为 .he）的文件
      const isHeFile = (file as any).fileType === "he" || (!(file as any).fileType && file.name && file.name.toLowerCase().endsWith(".he"));
      if (!isHeFile) return;

      const fileNode: TreeNode = {
        key: `file-${file.fileUuid}`,
        label: file.name,
        isGroup: false,
        item: file,
        prefix: renderNodeIcon(file, false),
      };

      if (file.group && groupMap.has(file.group)) {
        // 文件有所属分组且分组存在
        const groupNode = groupMap.get(file.group)!;
        if (!groupNode.children) groupNode.children = [];
        groupNode.children.push(fileNode);
      } else {
        // 无分组文件，添加到顶层
        orphanFiles.push(fileNode);
      }
    });

    return orphanFiles;
  };

  /**
   * Add temporary new group node if creating
   */
  const addTemporaryGroupNode = (nodes: TreeNode[], groupMap: Map<string, TreeNode>): void => {
    if (isCreatingNewGroup.value && newGroupTemporaryKey.value) {
      const tempNewGroupNode: TreeNode = {
        key: newGroupTemporaryKey.value,
        label: newGroupName.value,
        isGroup: true,
        item: {
          groupUuid: newGroupTemporaryKey.value,
          name: newGroupName.value,
          parentGroupUuid: newGroupParentUuid.value || undefined,
          files: [],
          subgroups: [],
          collapsed: false,
        } as unknown as HapticsGroup,
        children: [],
        prefix: () => h(NIcon, null, { default: () => h(Folder20Regular) }),
      };

      if (newGroupParentUuid.value) {
        // 找到父节点并插入
        const parentNode = groupMap.get(newGroupParentUuid.value);
        if (parentNode) {
          if (!parentNode.children) parentNode.children = [];
          parentNode.children.unshift(tempNewGroupNode);
        } else {
          nodes.unshift(tempNewGroupNode);
        }
      } else {
        // 作为顶层节点
        nodes.unshift(tempNewGroupNode);
      }
    }
  };

  /**
   * Main tree data computed property
   */
  const treeData = computed<TreeNode[]>(() => {
    if (!activeProject.value) {
      return [];
    }

    const projectFiles = activeProject.value.files || [];
    const projectGroups = activeProject.value.groups || [];

    // 创建UUID到TreeNode的映射表
    const groupMap = buildGroupNodes(projectGroups);

    // 构建分组层级关系
    let nodes = buildGroupHierarchy(projectGroups, groupMap);

    // 将文件添加到对应分组的children中
    const orphanFiles = attachFilesToGroups(projectFiles, groupMap);
    nodes = nodes.concat(orphanFiles);

    // 排序所有分组的子节点
    groupMap.forEach((groupNode) => {
      if (groupNode.children && groupNode.children.length > 0) {
        sortTreeNodes(groupNode.children);
      }
    });

    // 排序顶层节点
    sortTreeNodes(nodes);

    // 注入临时节点用于新建分组
    addTemporaryGroupNode(nodes, groupMap);

    return nodes;
  });

  return {
    treeData,
  };
}
