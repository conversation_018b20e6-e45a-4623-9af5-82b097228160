/**
 * 更新安装工具
 * 处理不同平台的更新安装逻辑
 */

import type { InstallOptions } from '@realitytap/shared';

/**
 * 更新安装器类
 */
export class UpdateInstaller {
  /**
   * 检查是否支持自动安装
   */
  static isAutoInstallSupported(): boolean {
    const platform = navigator.platform.toLowerCase();
    
    // Windows支持自动安装
    if (platform.includes('win')) {
      return true;
    }
    
    // macOS部分支持
    if (platform.includes('mac')) {
      return true;
    }
    
    // Linux需要用户权限
    if (platform.includes('linux')) {
      return false;
    }
    
    return false;
  }

  /**
   * 获取平台特定的安装说明
   */
  static getInstallInstructions(filePath: string): string[] {
    const platform = navigator.platform.toLowerCase();
    
    if (platform.includes('win')) {
      return [
        '1. 关闭当前应用程序',
        '2. 运行下载的安装程序',
        '3. 按照安装向导完成更新',
        '4. 重新启动应用程序',
      ];
    }
    
    if (platform.includes('mac')) {
      return [
        '1. 关闭当前应用程序',
        '2. 打开下载的DMG文件',
        '3. 将应用程序拖拽到Applications文件夹',
        '4. 重新启动应用程序',
      ];
    }
    
    if (platform.includes('linux')) {
      const ext = filePath.split('.').pop()?.toLowerCase();
      
      if (ext === 'deb') {
        return [
          '1. 关闭当前应用程序',
          '2. 打开终端',
          '3. 运行: sudo dpkg -i ' + filePath,
          '4. 重新启动应用程序',
        ];
      }
      
      if (ext === 'rpm') {
        return [
          '1. 关闭当前应用程序',
          '2. 打开终端',
          '3. 运行: sudo rpm -i ' + filePath,
          '4. 重新启动应用程序',
        ];
      }
      
      if (ext === 'appimage') {
        return [
          '1. 关闭当前应用程序',
          '2. 替换现有的AppImage文件',
          '3. 确保文件具有执行权限',
          '4. 重新启动应用程序',
        ];
      }
    }
    
    return [
      '1. 关闭当前应用程序',
      '2. 手动安装下载的更新文件',
      '3. 重新启动应用程序',
    ];
  }

  /**
   * 获取推荐的安装选项
   */
  static getRecommendedOptions(): InstallOptions {
    return {
      silent: true,
      force: false,
      restartApp: true,
      backup: true,
    };
  }

  /**
   * 验证安装文件
   */
  static validateInstallFile(filePath: string): {
    isValid: boolean;
    reason?: string;
  } {
    const platform = navigator.platform.toLowerCase();
    const ext = filePath.split('.').pop()?.toLowerCase();
    
    if (platform.includes('win')) {
      if (!ext || !['exe', 'msi'].includes(ext)) {
        return {
          isValid: false,
          reason: 'Windows平台需要.exe或.msi文件',
        };
      }
    } else if (platform.includes('mac')) {
      if (!ext || !['dmg', 'pkg'].includes(ext)) {
        return {
          isValid: false,
          reason: 'macOS平台需要.dmg或.pkg文件',
        };
      }
    } else if (platform.includes('linux')) {
      if (!ext || !['deb', 'rpm', 'appimage', 'tar.gz'].includes(ext)) {
        return {
          isValid: false,
          reason: 'Linux平台需要.deb、.rpm、.appimage或.tar.gz文件',
        };
      }
    }
    
    return { isValid: true };
  }

  /**
   * 获取安装前检查项
   */
  static getPreInstallChecks(): {
    name: string;
    description: string;
    required: boolean;
  }[] {
    return [
      {
        name: 'closeApp',
        description: '关闭当前应用程序',
        required: true,
      },
      {
        name: 'backupData',
        description: '备份重要数据',
        required: false,
      },
      {
        name: 'checkPermissions',
        description: '确认管理员权限',
        required: true,
      },
      {
        name: 'checkDiskSpace',
        description: '检查磁盘空间',
        required: true,
      },
    ];
  }

  /**
   * 估算安装时间
   */
  static estimateInstallTime(fileSize: number): number {
    // 基于文件大小估算安装时间（秒）
    const baseSizeInMB = fileSize / (1024 * 1024);
    
    // 假设安装速度为每MB需要2秒
    const estimatedSeconds = Math.max(30, baseSizeInMB * 2);
    
    return Math.round(estimatedSeconds);
  }

  /**
   * 生成安装报告
   */
  static generateInstallReport(
    success: boolean,
    startTime: Date,
    endTime: Date,
    error?: string
  ): {
    success: boolean;
    duration: number;
    timestamp: string;
    error?: string;
  } {
    const duration = endTime.getTime() - startTime.getTime();
    
    return {
      success,
      duration,
      timestamp: new Date().toISOString(),
      error,
    };
  }
}

/**
 * 安装进度跟踪器
 */
export class InstallProgressTracker {
  private startTime: Date;
  private estimatedDuration: number;
  private onProgress?: (progress: number) => void;
  private progressInterval?: number;

  constructor(
    estimatedDuration: number,
    onProgress?: (progress: number) => void
  ) {
    this.startTime = new Date();
    this.estimatedDuration = estimatedDuration * 1000; // 转换为毫秒
    this.onProgress = onProgress;
  }

  /**
   * 开始跟踪进度
   */
  start(): void {
    this.progressInterval = window.setInterval(() => {
      const elapsed = Date.now() - this.startTime.getTime();
      const progress = Math.min(95, (elapsed / this.estimatedDuration) * 100);
      
      if (this.onProgress) {
        this.onProgress(progress);
      }
    }, 1000);
  }

  /**
   * 完成安装
   */
  complete(): void {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }
    
    if (this.onProgress) {
      this.onProgress(100);
    }
  }

  /**
   * 停止跟踪
   */
  stop(): void {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }
  }
}

/**
 * 安装状态管理器
 */
export class InstallStateManager {
  private static readonly STORAGE_KEY = 'ota_install_state';

  /**
   * 保存安装状态
   */
  static saveInstallState(state: {
    sessionId: string;
    filePath: string;
    version: string;
    startTime: string;
    status: 'preparing' | 'installing' | 'completed' | 'failed';
  }): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save install state:', error);
    }
  }

  /**
   * 获取安装状态
   */
  static getInstallState(): any | null {
    try {
      const state = localStorage.getItem(this.STORAGE_KEY);
      return state ? JSON.parse(state) : null;
    } catch (error) {
      console.warn('Failed to get install state:', error);
      return null;
    }
  }

  /**
   * 清除安装状态
   */
  static clearInstallState(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear install state:', error);
    }
  }

  /**
   * 检查是否有未完成的安装
   */
  static hasPendingInstall(): boolean {
    const state = this.getInstallState();
    return state && ['preparing', 'installing'].includes(state.status);
  }
}
