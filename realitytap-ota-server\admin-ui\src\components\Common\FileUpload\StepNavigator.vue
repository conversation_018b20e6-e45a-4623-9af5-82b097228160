<template>
  <div class="step-navigator">
    <n-steps :current="currentStep" :status="stepStatus">
      <n-step title="选择文件" description="选择安装文件、签名文件和Hash文件">
        <template #icon>
          <n-icon>
            <FolderOpenOutline />
          </n-icon>
        </template>
      </n-step>
      
      <n-step title="文件信息" description="确认文件信息和上传设置">
        <template #icon>
          <n-icon>
            <InformationCircleOutline />
          </n-icon>
        </template>
      </n-step>
      
      <n-step title="上传进度" description="批量上传文件到服务器">
        <template #icon>
          <n-icon>
            <CloudUploadOutline />
          </n-icon>
        </template>
      </n-step>
      
      <n-step title="元数据编辑" description="编辑版本信息和发布设置">
        <template #icon>
          <n-icon>
            <DocumentTextOutline />
          </n-icon>
        </template>
      </n-step>
    </n-steps>
  </div>
</template>

<script setup lang="ts">
import { NSteps, NStep, NIcon } from 'naive-ui';
import { 
  FolderOpenOutline, 
  InformationCircleOutline, 
  CloudUploadOutline, 
  DocumentTextOutline 
} from '@vicons/ionicons5';

interface Props {
  currentStep: number;
  stepStatus?: 'process' | 'finish' | 'error' | 'wait';
}

defineProps<Props>();
</script>

<style scoped>
.step-navigator {
  padding: 16px 0;
  margin-bottom: 24px;
}

:deep(.n-steps) {
  --n-step-header-font-size: 14px;
}

:deep(.n-step) {
  --n-step-splitter-color: #e0e0e6;
}

:deep(.n-step--finish .n-step-indicator) {
  --n-step-indicator-color: #18a058;
}

:deep(.n-step--process .n-step-indicator) {
  --n-step-indicator-color: #2080f0;
}

:deep(.n-step--error .n-step-indicator) {
  --n-step-indicator-color: #d03050;
}
</style>
