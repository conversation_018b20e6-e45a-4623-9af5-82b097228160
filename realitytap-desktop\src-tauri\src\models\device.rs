// Device models for the device management system

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// === Device Type Enums ===

#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "lowercase")]
pub enum DeviceType {
    Usb,
    Wifi,
    Bluetooth,
}

#[derive(Debug, <PERSON>lone, Copy, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ConnectionType {
    Usb,
    Wifi,
    Bluetooth,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum DeviceStatus {
    Connected,
    Disconnected,
    Connecting,
    Disconnecting,
    Error,
    Unknown,
}

// === Device Metadata ===

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct ConnectionInfo {
    pub ip_address: Option<String>,
    pub port: Option<u16>,
    pub mac_address: Option<String>,
    pub usb_path: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct DeviceMetadata {
    pub manufacturer: Option<String>,
    pub model: Option<String>,
    pub version: Option<String>,
    pub serial_number: Option<String>,
    pub capabilities: Option<Vec<String>>,
    pub last_error: Option<String>,
    pub connection_info: Option<ConnectionInfo>,
}

// === Main Device Structure ===

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Device {
    pub device_id: String,
    pub name: String,
    #[serde(rename = "type")]
    pub device_type: DeviceType,
    pub connection_type: ConnectionType,
    pub status: DeviceStatus,
    pub last_connected: Option<String>,
    pub last_disconnected: Option<String>,
    pub is_default: bool,
    pub metadata: DeviceMetadata,
    pub created_at: String,
    pub updated_at: String,
}

// === Device Configuration ===

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviceConnectionConfig {
    pub device_id: String,
    pub timeout: Option<u64>,
    pub retry_count: Option<u32>,
    pub auto_reconnect: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviceScanOptions {
    pub types: Option<Vec<DeviceType>>,
    pub timeout: Option<u64>,
    pub include_disconnected: Option<bool>,
}

// === Device Events ===

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum DeviceEventType {
    DeviceDiscovered,
    DeviceConnected,
    DeviceDisconnected,
    DeviceError,
    DeviceStatusChanged,
    ScanStarted,
    ScanCompleted,
    ScanError,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviceEvent {
    #[serde(rename = "type")]
    pub event_type: DeviceEventType,
    pub device_id: Option<String>,
    pub device: Option<Device>,
    pub error: Option<String>,
    pub timestamp: String,
    pub data: Option<serde_json::Value>,
}

// === Device Operation Results ===

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviceOperationResult {
    pub success: bool,
    pub device_id: String,
    pub message: Option<String>,
    pub error: Option<String>,
    pub data: Option<serde_json::Value>,
}

// === Device Statistics ===

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviceStatistics {
    pub total_devices: usize,
    pub connected_devices: usize,
    pub disconnected_devices: usize,
    pub error_devices: usize,
    pub devices_by_type: HashMap<DeviceType, usize>,
    pub last_scan_time: Option<String>,
    pub last_connection_time: Option<String>,
}

// === Device Manager Configuration ===

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviceManagerConfig {
    pub auto_scan_interval: Option<u64>,
    pub max_devices: Option<usize>,
    pub enable_auto_reconnect: Option<bool>,
    pub connection_timeout: Option<u64>,
    pub scan_timeout: Option<u64>,
}

// === Device Discovery Info ===

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviceDiscoveryInfo {
    pub device_type: DeviceType,
    pub identifier: String,
    pub name: Option<String>,
    pub metadata: Option<DeviceMetadata>,
}

// === Device Manager Data ===

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviceManagerData {
    pub devices: Vec<Device>,
    pub default_device_id: Option<String>,
    pub config: DeviceManagerConfig,
    pub last_updated: String,
}

// === Implementation blocks ===

impl Device {
    pub fn new(
        device_id: String,
        name: String,
        device_type: DeviceType,
    ) -> Self {
        let now = chrono::Utc::now().to_rfc3339();
        
        Self {
            device_id,
            name,
            connection_type: match device_type {
                DeviceType::Usb => ConnectionType::Usb,
                DeviceType::Wifi => ConnectionType::Wifi,
                DeviceType::Bluetooth => ConnectionType::Bluetooth,
            },
            device_type,
            status: DeviceStatus::Disconnected,
            last_connected: None,
            last_disconnected: None,
            is_default: false,
            metadata: DeviceMetadata::default(),
            created_at: now.clone(),
            updated_at: now,
        }
    }

    pub fn update_status(&mut self, status: DeviceStatus) {
        self.status = status;
        self.updated_at = chrono::Utc::now().to_rfc3339();
        
        match status {
            DeviceStatus::Connected => {
                self.last_connected = Some(self.updated_at.clone());
            }
            DeviceStatus::Disconnected => {
                self.last_disconnected = Some(self.updated_at.clone());
            }
            _ => {}
        }
    }

    pub fn set_error(&mut self, error: String) {
        self.status = DeviceStatus::Error;
        self.metadata.last_error = Some(error);
        self.updated_at = chrono::Utc::now().to_rfc3339();
    }

    pub fn clear_error(&mut self) {
        if self.status == DeviceStatus::Error {
            self.status = DeviceStatus::Disconnected;
        }
        self.metadata.last_error = None;
        self.updated_at = chrono::Utc::now().to_rfc3339();
    }

    pub fn is_connected(&self) -> bool {
        self.status == DeviceStatus::Connected
    }

    pub fn is_connecting(&self) -> bool {
        matches!(self.status, DeviceStatus::Connecting | DeviceStatus::Disconnecting)
    }

    pub fn can_connect(&self) -> bool {
        matches!(self.status, DeviceStatus::Disconnected | DeviceStatus::Error)
    }

    pub fn can_disconnect(&self) -> bool {
        self.status == DeviceStatus::Connected
    }
}

impl DeviceType {
    pub fn as_str(&self) -> &'static str {
        match self {
            DeviceType::Usb => "usb",
            DeviceType::Wifi => "wifi",
            DeviceType::Bluetooth => "bluetooth",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "usb" => Some(DeviceType::Usb),
            "wifi" => Some(DeviceType::Wifi),
            "bluetooth" => Some(DeviceType::Bluetooth),
            _ => None,
        }
    }
}

impl DeviceStatus {
    pub fn as_str(&self) -> &'static str {
        match self {
            DeviceStatus::Connected => "connected",
            DeviceStatus::Disconnected => "disconnected",
            DeviceStatus::Connecting => "connecting",
            DeviceStatus::Disconnecting => "disconnecting",
            DeviceStatus::Error => "error",
            DeviceStatus::Unknown => "unknown",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "connected" => Some(DeviceStatus::Connected),
            "disconnected" => Some(DeviceStatus::Disconnected),
            "connecting" => Some(DeviceStatus::Connecting),
            "disconnecting" => Some(DeviceStatus::Disconnecting),
            "error" => Some(DeviceStatus::Error),
            "unknown" => Some(DeviceStatus::Unknown),
            _ => None,
        }
    }
}

impl Default for DeviceManagerConfig {
    fn default() -> Self {
        Self {
            auto_scan_interval: Some(30000),  // 30 seconds
            max_devices: Some(50),
            enable_auto_reconnect: Some(true),
            connection_timeout: Some(10000),  // 10 seconds
            scan_timeout: Some(15000),        // 15 seconds
        }
    }
}

impl DeviceOperationResult {
    pub fn success(device_id: String, message: Option<String>) -> Self {
        Self {
            success: true,
            device_id,
            message,
            error: None,
            data: None,
        }
    }

    pub fn error(device_id: String, error: String) -> Self {
        Self {
            success: false,
            device_id,
            message: None,
            error: Some(error),
            data: None,
        }
    }

    pub fn with_data(mut self, data: serde_json::Value) -> Self {
        self.data = Some(data);
        self
    }
}
