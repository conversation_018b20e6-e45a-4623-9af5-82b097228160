import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 禁用生产环境下的组件实例键枚举警告
          isCustomElement: () => false,
        },
      },
      script: {
        defineModel: true,
        propsDestructure: true,
      },
    }) as any,
  ],
  base: '/admin/',
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3100,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          'naive-ui': ['naive-ui'],
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
        },
      },
    },
    // 减少构建警告
    chunkSizeWarningLimit: 1000,
  },
  // 定义全局常量，减少运行时警告
  define: {
    __VUE_OPTIONS_API__: false,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
    // 禁用开发环境警告（防止扩展干扰）
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
  },
  // 优化构建，减少扩展冲突
  optimizeDeps: {
    include: ['vue', 'vue-router', 'pinia', 'naive-ui'],
    exclude: ['@vicons/ionicons5', '@vicons/material'],
  },
});
