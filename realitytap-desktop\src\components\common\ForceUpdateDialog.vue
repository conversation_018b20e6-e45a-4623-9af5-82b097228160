<template>
  <NModal
    :show="visible"
    preset="dialog"
    :title="t('forceUpdate.title')"
    :closable="false"
    :mask-closable="false"
    :close-on-esc="false"
    :style="{ width: '520px', maxWidth: '90vw' }"
    class="force-update-dialog"
  >
    <div class="force-update-content">
      <!-- 强制更新说明 -->
      <div class="force-update-notice">
        <NAlert type="warning" :title="t('forceUpdate.notice')">
          {{ t('forceUpdate.noticeMessage') }}
        </NAlert>
      </div>

      <!-- 更新信息展示 -->
      <div v-if="updateInfo && !updater.isDownloading.value && !updater.isInstalling.value" class="update-info">
        <div class="version-info">
          <h3 class="new-version">{{ updateStatusTitle }}</h3>
          <div class="version-details">
            <p class="version-item">
              <span class="version-label">📱 {{ t('update.currentVersion') }}:</span>
              <span class="version-value">{{ props.currentVersion }}</span>
            </p>
            <p class="version-item">
              <span class="version-label">🚀 {{ t('update.latestVersion') }}:</span>
              <span class="version-value">{{ updateInfo.version }}</span>
            </p>
            <p v-if="updateInfo.file_size && updateInfo.file_size > 0" class="version-item">
              <span class="version-label">📦 {{ t('update.fileSize') }}:</span>
              <span class="version-value">{{ formatFileSize(updateInfo.file_size) }}</span>
            </p>
          </div>
        </div>

        <div v-if="updateInfo.body" class="release-notes">
          <h4 class="notes-title">📋 {{ t('update.releaseNotes') }}</h4>
          <NScrollbar class="notes-scrollbar">
            <div class="notes-content">{{ updateInfo.body }}</div>
          </NScrollbar>
        </div>
      </div>

      <!-- 下载/安装进度 -->
      <div v-if="updater.isDownloading.value || updater.isInstalling.value" class="progress-section">
        <div class="progress-header">
          <h3>{{ updater.isInstalling.value ? t("update.installing") : t("update.downloading") }}</h3>
          <span v-if="updater.isDownloading.value" class="progress-percentage">{{ downloadProgressPercentage }}%</span>
          <NSpin v-if="updater.isInstalling.value" size="small" />
        </div>

        <NProgress
          v-if="updater.isDownloading.value"
          type="line"
          :percentage="downloadProgressPercentage"
          :show-indicator="false"
          status="info"
          class="download-progress"
        />

        <div v-if="updater.isDownloading.value" class="progress-details">
          <div class="progress-stats">
            <span class="progress-stat">
              <span class="stat-value">{{
                formatFileSize(updater.downloadProgress.value.downloaded || 0)
              }}</span>
              <span class="stat-separator"> / </span>
              <span class="stat-total">{{
                formatFileSize(getExpectedFileSize())
              }}</span>
            </span>
          </div>
        </div>

        <p v-if="updater.isInstalling.value" class="install-message">
          {{ t("update.installingMessage") }}
        </p>
      </div>

      <!-- 错误信息 -->
      <div v-if="updater.error.value" class="error-section">
        <NAlert type="error" :title="t('update.error')">
          {{ updater.error.value }}
        </NAlert>
      </div>
    </div>

    <!-- 对话框按钮 -->
    <template #action>
      <div class="dialog-actions">
        <!-- 初始状态：开始下载 + 关闭程序 -->
        <template v-if="!updater.isProcessing.value && !updater.isDownloaded.value && !updater.error.value">
          <NButton @click="handleExitApplication" type="default">
            {{ t("forceUpdate.exitApplication") }}
          </NButton>
          <NButton @click="handleStartDownload" type="primary">
            {{ t("forceUpdate.startDownload") }}
          </NButton>
        </template>

        <!-- 下载中状态：只显示关闭程序 -->
        <template v-if="updater.isDownloading.value">
          <NButton @click="handleExitApplication" type="default">
            {{ t("forceUpdate.exitApplication") }}
          </NButton>
        </template>

        <!-- 安装中状态：只显示关闭程序 -->
        <template v-if="updater.isInstalling.value">
          <NButton @click="handleExitApplication" type="default">
            {{ t("forceUpdate.exitApplication") }}
          </NButton>
        </template>

        <!-- 下载完成状态：立即安装 + 关闭程序 -->
        <template v-if="!updater.isProcessing.value && updater.isDownloaded.value && !updater.error.value">
          <NButton @click="handleExitApplication" type="default">
            {{ t("forceUpdate.exitApplication") }}
          </NButton>
          <NButton @click="handleStartInstall" type="primary">
            {{ t("forceUpdate.installNow") }}
          </NButton>
        </template>

        <!-- 下载失败状态：重试下载 + 关闭程序 -->
        <template v-if="!updater.isProcessing.value && updater.error.value">
          <NButton @click="handleExitApplication" type="default">
            {{ t("forceUpdate.exitApplication") }}
          </NButton>
          <NButton @click="handleRetryDownload" type="primary">
            {{ t("forceUpdate.retryDownload") }}
          </NButton>
        </template>
      </div>
    </template>
  </NModal>
</template>

<script setup lang="ts">
import { NScrollbar } from "naive-ui";
import { useUpdateDialog, type UpdateDialogProps } from "@/composables/useUpdateDialog";
import { formatFileSize } from "@/utils/format";

// === Props ===
interface Props extends UpdateDialogProps {}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  updateInfo: null,
  currentVersion: "1.0.0",
});

// === 使用共享的更新对话框逻辑 ===
const {
  t,
  updater,
  downloadProgressPercentage,
  forceUpdateStatusTitle,
  getExpectedFileSize,
  handleStartDownload,
  handleStartInstall,
  handleRetryDownload,
  handleExitApplication,
} = useUpdateDialog(props);

// === 强制更新特定的计算属性 ===
const updateStatusTitle = forceUpdateStatusTitle;

// 所有函数都已从 useUpdateDialog 中获取，无需重复定义
</script>

<style scoped>
.force-update-content {
  padding: 16px 0;
}

.force-update-notice {
  margin-bottom: 20px;
}

.update-info {
  margin-bottom: 20px;
}

.version-info {
  margin-bottom: 16px;
}

.new-version {
  margin: 0 0 12px 0;
  color: #f0a020;
  font-size: 18px;
  font-weight: 600;
}

/* 版本信息样式 */
.version-item {
  margin: 10px 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.version-label {
  color: var(--n-text-color-2);
  font-weight: 500;
  min-width: 80px;
}

.version-value {
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background: var(--n-color-embedded);
  color: var(--n-text-color);
  border: 1px solid var(--n-divider-color);
}

.release-notes {
  margin-top: 16px;
  max-height: 300px;
  height: 300px;
}

.notes-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--n-text-color);
}

.notes-scrollbar {
  max-height: 200px;
  border-radius: 8px;
  background: var(--n-color-embedded);
  border: 1px solid var(--n-divider-color);
}

/* 确保 Naive UI 滚动条样式正确应用 */
.notes-scrollbar :deep(.n-scrollbar-container) {
  /* 隐藏原生滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.notes-scrollbar :deep(.n-scrollbar-container::-webkit-scrollbar) {
  display: none;
}

.notes-scrollbar :deep(.n-scrollbar-rail) {
  right: 2px;
  width: 6px;
}

.notes-scrollbar :deep(.n-scrollbar-rail .n-scrollbar-rail__scrollbar) {
  border-radius: 3px;
  background: var(--n-scrollbar-color);
  transition: background-color 0.2s ease;
  width: 6px;
}

.notes-scrollbar :deep(.n-scrollbar-rail .n-scrollbar-rail__scrollbar:hover) {
  background: var(--n-scrollbar-color-hover);
}

.notes-content {
  padding: 12px;
  font-size: 13px;
  line-height: 1.6;
  color: var(--n-text-color-2);
  white-space: pre-wrap;
}

/* 进度相关样式 */
.progress-section {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.progress-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: var(--n-primary-color);
}

.download-progress {
  margin-bottom: 12px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-stats {
  display: flex;
  gap: 16px;
}

.progress-stat {
  font-size: 12px;
  color: var(--n-text-color-2);
}

.stat-value {
  font-weight: 600;
  color: var(--n-text-color);
}

.stat-separator {
  color: var(--n-text-color-3);
}

.stat-total {
  color: var(--n-text-color-2);
}

.install-message {
  margin: 12px 0 0 0;
  font-size: 14px;
  color: var(--n-text-color-2);
  text-align: center;
}

.error-section {
  margin-bottom: 20px;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 深色主题适配 */
:deep(.n-dialog.n-modal) {
  background: var(--n-color);
}

/* 强制更新对话框特殊样式 */
.force-update-dialog :deep(.n-dialog__title) {
  color: #f0a020;
  font-weight: 600;
}
</style>
