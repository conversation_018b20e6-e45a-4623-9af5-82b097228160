import { adminApi } from '@/api/admin';
import {
  UploadStatus,
  type FileUploadRequest,
  type UploadProgress,
  type BatchUploadInitRequest,
  type BatchUploadFileInfo,
} from '@/types/upload';
import { calculateFileHash } from '@/utils/crypto';
import {
  detectPlatformFromFileName,
  detectArchitectureFromFileName,
  detectChannelFromFileName,
  extractVersionFromFileName,
  throttle,
} from '@/utils/fileUpload';
import { useMessage } from 'naive-ui';
import { ref, computed } from 'vue';

export function useFileUpload() {
  const message = useMessage();

  // 响应式状态
  const isUploading = ref(false);
  const batchUploadSessionId = ref<string | null>(null);
  const uploadProgress = ref<UploadProgress>({
    sessionId: '',
    uploadedBytes: 0,
    totalBytes: 0,
    uploadedChunks: 0,
    totalChunks: 0,
    percentage: 0,
    speed: 0,
    estimatedTimeRemaining: 0,
    status: UploadStatus.INITIALIZING,
  });

  const uploadResult = ref<{
    success: boolean;
    message: string;
  } | null>(null);

  // 计算属性
  const canCancel = computed(() => {
    return [UploadStatus.UPLOADING, UploadStatus.INITIALIZING].includes(uploadProgress.value.status);
  });

  // 解析文件元数据
  const parseFileMetadata = (filename: string): FileUploadRequest => {
    const version = extractVersionFromFileName(filename);
    const platform = detectPlatformFromFileName(filename);
    const architecture = detectArchitectureFromFileName(filename);
    const channel = detectChannelFromFileName(filename);

    return {
      version,
      platform,
      architecture,
      channel,
      releaseNotes: `Release ${version}`,
    };
  };

  // 开始批量上传
  const startBatchUpload = async (selectedFiles: File[], hashValue: string | null, signatureValue: string | null) => {
    console.log('🚀 开始批量上传', {
      filesCount: selectedFiles.length,
      hasHashValue: !!hashValue,
      hashValueLength: hashValue ? hashValue.length : 0,
      hasSignatureValue: !!signatureValue,
      signatureValueLength: signatureValue ? signatureValue.length : 0,
      signaturePreview: signatureValue ? signatureValue.substring(0, 50) + '...' : null,
    });

    if (selectedFiles.length === 0) {
      const errorMessage = '没有选择要上传的文件';
      message.error(errorMessage);
      uploadResult.value = { success: false, message: errorMessage };
      return false;
    }

    try {
      isUploading.value = true;
      uploadResult.value = null;

      // 重置上传进度
      uploadProgress.value = {
        sessionId: '',
        uploadedBytes: 0,
        totalBytes: selectedFiles.reduce((sum, file) => sum + file.size, 0),
        uploadedChunks: 0,
        totalChunks: 0,
        percentage: 0,
        speed: 0,
        estimatedTimeRemaining: 0,
        status: UploadStatus.INITIALIZING,
      };

      // 准备文件信息
      const files: BatchUploadFileInfo[] = [];

      for (const file of selectedFiles) {
        // 确定文件哈希值
        let fileHash: string;

        if (hashValue) {
          // 如果有.hash文件，直接使用.hash文件中的哈希值
          fileHash = hashValue;
          console.log(`使用.hash文件中的哈希值: ${file.name}`);
        } else {
          // 否则计算文件哈希值
          fileHash = await calculateFileHash(file);
          console.log(`计算文件哈希: ${file.name}`);
        }

        files.push({
          filename: file.name,
          fileSize: file.size,
          fileHash: fileHash,
          fileType: 'installer',
        });
      }

      // 从第一个安装包文件解析元数据
      const installerFile = selectedFiles.find(f => !f.name.toLowerCase().endsWith('.sig'));
      if (!installerFile) {
        throw new Error('未找到安装包文件');
      }

      const metadata = parseFileMetadata(installerFile.name);

      // 如果有hash值，添加到metadata中
      if (hashValue) {
        metadata.fileHash = hashValue;
        console.log('✅ 添加哈希值到metadata', { hashLength: hashValue.length });
      } else {
        console.log('⚠️ 没有哈希值');
      }

      // 如果有签名内容，添加到metadata中
      if (signatureValue) {
        metadata.signature = signatureValue;
        console.log('✅ 添加签名值到metadata', {
          signatureLength: signatureValue.length,
          signaturePreview: signatureValue.substring(0, 50) + '...'
        });
      } else {
        console.log('❌ 没有签名值！这是问题所在！');
      }

      console.log('📦 最终metadata', {
        hasFileHash: !!metadata.fileHash,
        hasSignature: !!metadata.signature,
        metadata: JSON.stringify(metadata, null, 2)
      });

      // 初始化批量上传
      const initRequest: BatchUploadInitRequest = {
        files,
        metadata,
      };

      const initResponse = await adminApi.initBatchUpload(initRequest);
      if (!initResponse.success || !initResponse.data) {
        throw new Error(initResponse.error?.message || '初始化批量上传失败');
      }

      batchUploadSessionId.value = initResponse.data.sessionId;
      uploadProgress.value.sessionId = batchUploadSessionId.value;
      uploadProgress.value.status = UploadStatus.UPLOADING;

      // 上传文件
      const startTime = Date.now();

      // 使用节流来限制进度更新频率，避免过于频繁的UI更新
      const throttledProgressUpdate = throttle((progress: { uploadedBytes: number; totalBytes: number; percentage: number }) => {
        console.log('🔄 Throttled progress update called with:', progress);

        const currentTime = Date.now();
        const elapsedTime = (currentTime - startTime) / 1000; // 秒

        // 直接使用传递过来的进度数据
        const oldPercentage = uploadProgress.value.percentage;
        uploadProgress.value.uploadedBytes = progress.uploadedBytes;
        uploadProgress.value.percentage = progress.percentage;

        console.log('📊 Progress updated:', {
          oldPercentage,
          newPercentage: uploadProgress.value.percentage,
          uploadedBytes: uploadProgress.value.uploadedBytes,
          totalBytes: uploadProgress.value.totalBytes
        });

        // 计算上传速度
        if (elapsedTime > 0) {
          uploadProgress.value.speed = uploadProgress.value.uploadedBytes / elapsedTime;

          // 计算剩余时间
          const remainingBytes = uploadProgress.value.totalBytes - uploadProgress.value.uploadedBytes;
          if (uploadProgress.value.speed > 0) {
            uploadProgress.value.estimatedTimeRemaining = remainingBytes / uploadProgress.value.speed;
          }
        }
      }, 100); // 限制为每100ms最多更新一次

      console.log('🚀 Starting file upload with progress callback');
      await adminApi.uploadBatchFiles(batchUploadSessionId.value, selectedFiles, throttledProgressUpdate);

      // 完成上传
      console.log('✅ File upload completed, setting status to MERGING');
      uploadProgress.value.status = UploadStatus.MERGING;
      await adminApi.completeBatchUpload(batchUploadSessionId.value);

      console.log('🎉 Batch upload completed, setting progress to 100%');
      uploadProgress.value.status = UploadStatus.COMPLETED;
      uploadProgress.value.percentage = 100;

      uploadResult.value = {
        success: true,
        message: `批量上传成功，共上传 ${selectedFiles.length} 个文件`,
      };

      return true;
    } catch (error: any) {
      console.error('批量上传失败:', error);

      // 提取友好的错误消息
      let errorMessage = '批量上传失败';

      if (error.response?.data?.error?.message) {
        // API 返回的结构化错误消息
        errorMessage = error.response.data.error.message;
      } else if (error.response?.data?.message) {
        // API 返回的简单错误消息
        errorMessage = error.response.data.message;
      } else if (error.message) {
        // JavaScript 错误消息
        errorMessage = error.message;
      }

      // 检查是否是文件已存在的错误
      if (errorMessage.includes('已存在') || errorMessage.includes('already exists')) {
        errorMessage = `${errorMessage}\n\n建议：\n1. 检查是否需要更新现有版本\n2. 使用不同的文件名\n3. 删除现有文件后重新上传`;
      }

      uploadResult.value = {
        success: false,
        message: errorMessage,
      };
      return false;
    } finally {
      isUploading.value = false;
    }
  };

  // 取消上传
  const cancelUpload = async () => {
    try {
      if (batchUploadSessionId.value) {
        try {
          await adminApi.cancelBatchUpload(batchUploadSessionId.value);
        } catch (error: any) {
          console.error('取消批量上传会话失败:', error);
          // 继续执行，不阻止取消操作
        }
        batchUploadSessionId.value = null;
      }

      uploadResult.value = { success: false, message: '上传已取消' };
      isUploading.value = false;
    } catch (error: any) {
      console.error('取消上传失败:', error);
      message.error('取消上传失败: ' + error.message);
    }
  };

  // 重置状态
  const resetUploadState = () => {
    isUploading.value = false;
    batchUploadSessionId.value = null;
    uploadResult.value = null;
    uploadProgress.value = {
      sessionId: '',
      uploadedBytes: 0,
      totalBytes: 0,
      uploadedChunks: 0,
      totalChunks: 0,
      percentage: 0,
      speed: 0,
      estimatedTimeRemaining: 0,
      status: UploadStatus.INITIALIZING,
    };
  };

  return {
    // 状态
    isUploading,
    uploadProgress,
    uploadResult,
    batchUploadSessionId,

    // 计算属性
    canCancel,

    // 方法
    startBatchUpload,
    cancelUpload,
    resetUploadState,
    parseFileMetadata,
  };
}
