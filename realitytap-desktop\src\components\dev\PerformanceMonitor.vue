<!--
  性能监控组件
  显示内存使用情况和页面加载时间等性能信息
-->
<template>
  <div class="performance-monitor">
    <n-card title="性能信息">
      <n-space vertical>
        <div>
          <strong>内存使用:</strong>
          <div v-if="memoryInfo">
            <p>已使用: {{ formatBytes(memoryInfo.usedJSHeapSize) }}</p>
            <p>总计: {{ formatBytes(memoryInfo.totalJSHeapSize) }}</p>
            <p>限制: {{ formatBytes(memoryInfo.jsHeapSizeLimit) }}</p>
          </div>
          <p v-else>内存信息不可用</p>
        </div>
        
        <div>
          <strong>页面加载时间:</strong>
          <p v-if="loadTime">{{ loadTime }}ms</p>
          <p v-else>加载时间不可用</p>
        </div>
        
        <n-button @click="refreshPerformance">刷新性能信息</n-button>
      </n-space>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { NCard, NSpace, NButton } from "naive-ui";

const memoryInfo = ref<any>(null);
const loadTime = ref<number | null>(null);

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const refreshPerformance = () => {
  // 获取内存信息
  if ("memory" in performance) {
    memoryInfo.value = (performance as any).memory;
  }

  // 获取页面加载时间
  if ("timing" in performance) {
    const timing = (performance as any).timing;
    loadTime.value = timing.loadEventEnd - timing.navigationStart;
  }
};

onMounted(() => {
  refreshPerformance();
});
</script>

<style scoped>
.performance-monitor p {
  margin: 4px 0;
}
</style>
