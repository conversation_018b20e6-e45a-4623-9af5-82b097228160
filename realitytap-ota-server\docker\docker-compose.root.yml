# Docker Compose 配置 - Root 权限运行
# 此配置使用 root 用户运行容器，避免权限问题
# 适用于开发环境和简单部署场景

version: '3.8'

services:
  realitytap-ota-server:
    build:
      context: ..
      dockerfile: docker/Dockerfile.production
    container_name: realitytap-ota-server
    restart: unless-stopped
    
    # 使用 root 用户运行容器内的应用程序（覆盖 Dockerfile 中的 USER 设置）
    user: "0:0"
    
    ports:
      - "${PORT:-3000}:3000"
    
    environment:
      # 基本配置
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      - BASE_URL=${BASE_URL:-http://localhost:3000}
      
      # 管理员配置
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
      - JWT_SECRET=${JWT_SECRET:-simple_jwt_secret_for_development_only_32_chars}
      
      # 数据库配置
      - DB_ENABLED=true
      - DB_PATH=/app/storage/database/ota.db
      - DB_BACKUP_PATH=/app/storage/backup/database
      
      # 存储配置
      - STORAGE_PATH=/app/storage
      - RELEASES_PATH=/app/storage/releases
      - METADATA_PATH=/app/storage/metadata
      - LOGS_PATH=/app/storage/logs
      - TEMP_PATH=/app/storage/temp
      
      # 安全配置
      - CORS_ORIGIN=*
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - SESSION_TIMEOUT=${SESSION_TIMEOUT:-3600000}
      
      # HTTPS 配置（可选）
      - ENABLE_HTTPS=${ENABLE_HTTPS:-false}
      - SSL_CERT_PATH=/app/ssl/cert.pem
      - SSL_KEY_PATH=/app/ssl/key.pem
      
    volumes:
      # 数据存储
      - ./data/storage:/app/storage
      - ./data/logs:/app/storage/logs
      
      # SSL 证书（如果启用 HTTPS）
      - ./ssl:/app/ssl:ro
      
    networks:
      - realitytap-network
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
      
    # 资源限制（可选）
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

networks:
  realitytap-network:
    driver: bridge

# 注意事项：
# 1. 此配置使用 root 用户运行，避免了权限问题
# 2. 请确保在生产环境中评估安全风险
# 3. 建议在防火墙后面运行，限制网络访问
# 4. 定期更新容器镜像和依赖项
# 5. 使用强密码和 JWT 密钥
