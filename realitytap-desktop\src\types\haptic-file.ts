import { z, ZodError } from "zod";

// ---------------------------
// TypeScript Type Definitions
// ---------------------------

// Base Metadata structure
interface MetadataBase {
  Created: string;
  Description: string;
  TotalDuration?: number; // 可选的总时长字段（毫秒），用于保存用户设定的时长
}

// V1 Metadata
export interface MetadataV1 extends MetadataBase {
  Version: 1;
}

// V2 Metadata
export interface MetadataV2 extends MetadataBase {
  Version: 2;
}

// Curve Point structure (common for V1 & V2 continuous events)
export interface CurvePoint {
  Time: number; // ms, integer
  Intensity: number; // [0, 1.0], float
  Frequency: number; // [-100, 100], integer
}

// Base Parameters (common structure)
interface ParametersBase {
  Frequency: number; // [0, 100], integer
  Intensity: number; // [0, 100], integer
}

// Transient Event Parameters (common for V1 & V2)
interface TransientEventParameters extends ParametersBase {}

// Continuous Event Parameters (common for V1)
export interface ContinuousEventParameters extends ParametersBase {
  Curve: [CurvePoint, CurvePoint, CurvePoint, CurvePoint]; // Exactly 4 points
}

// Continuous Event Parameters for V2 (supports 4-16 points)
export interface ContinuousEventParametersV2 extends ParametersBase {
  Curve: CurvePoint[]; // 4-16 points
}

// Base Event structure
interface EventBase {
  RelativeTime: number; // ms, integer
}

// V1 Transient Event
export interface TransientEventV1 extends EventBase {
  Type: "transient";
  Parameters: TransientEventParameters;
}

// V1 Continuous Event
export interface ContinuousEventV1 extends EventBase {
  Type: "continuous";
  Duration: number; // ms, integer
  Parameters: ContinuousEventParameters;
}

// V1 Event Union
export type EventV1 = TransientEventV1 | ContinuousEventV1;

// New: WrappedEventV1 interface
export interface WrappedEventV1 {
  Event: EventV1;
}

// V2 Transient Event
export interface TransientEventV2 extends EventBase {
  Type: "transient";
  Index: 0 | 1; // Motor index
  Parameters: TransientEventParameters;
}

// V2 Continuous Event
export interface ContinuousEventV2 extends EventBase {
  Type: "continuous";
  Index: 0 | 1; // Motor index
  Duration: number; // ms, integer
  Parameters: ContinuousEventParametersV2;
}

// V2 Event Union
export type EventV2 = TransientEventV2 | ContinuousEventV2;

// V2 PatternListItem structure
export interface PatternListItem {
  AbsoluteTime: number; // ms, integer
  Pattern: EventV2[];
}

// V1 Top Level Structure
export interface RealityTapEffectV1 {
  Metadata: MetadataV1;
  Pattern: WrappedEventV1[]; // Changed from EventV1[]
}

// V2 Top Level Structure
export interface RealityTapEffectV2 {
  Metadata: MetadataV2;
  PatternList: PatternListItem[];
}

// Combined Type for function signatures and external use
export type RealityTapEffect = RealityTapEffectV1 | RealityTapEffectV2;

// ---------------------------
// Zod Schema Definitions
// ---------------------------

// Base Metadata Schema
const MetadataBaseSchema = z.object({
  Created: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  Description: z.string(),
  TotalDuration: z.number().int().positive().optional(), // 可选的总时长字段
});

// V1 Metadata Schema
const MetadataV1Schema = MetadataBaseSchema.extend({
  Version: z.literal(1),
});

// V2 Metadata Schema
const MetadataV2Schema = MetadataBaseSchema.extend({
  Version: z.literal(2),
});

// Curve Point Schema
const CurvePointSchema = z.object({
  Time: z.number().int("Time must be an integer"),
  Intensity: z
    .number()
    .min(0.0)
    .max(1.0, "Intensity must be between 0.0 and 1.0"),
  Frequency: z
    .number()
    .int("Frequency must be an integer")
    .min(-100)
    .max(100, "Frequency must be between -100 and 100"),
});

// Base Parameters Schema
const ParametersBaseSchema = z.object({
  Frequency: z
    .number()
    .int("Frequency must be an integer")
    .min(0)
    .max(100, "Frequency must be between 0 and 100"),
  Intensity: z
    .number()
    .int("Intensity must be an integer")
    .min(0)
    .max(100, "Intensity must be between 0 and 100"),
});

// Transient Event Parameters Schema
const TransientEventParametersSchema = ParametersBaseSchema;

// Continuous Event Parameters Schema
const ContinuousEventParametersSchema = ParametersBaseSchema.extend({
  Curve: z
    .tuple([
      CurvePointSchema,
      CurvePointSchema,
      CurvePointSchema,
      CurvePointSchema,
    ])
    .refine(
      (
        curve: [
          z.infer<typeof CurvePointSchema>,
          z.infer<typeof CurvePointSchema>,
          z.infer<typeof CurvePointSchema>,
          z.infer<typeof CurvePointSchema>
        ]
      ) => curve[0].Time === 0 && curve[0].Intensity === 0,
      { message: "First curve point Time and Intensity must be 0" }
    )
    .refine(
      (
        curve: [
          z.infer<typeof CurvePointSchema>,
          z.infer<typeof CurvePointSchema>,
          z.infer<typeof CurvePointSchema>,
          z.infer<typeof CurvePointSchema>
        ]
      ) => curve[3].Intensity === 0,
      { message: "Last curve point Intensity must be 0" }
    )
    .refine(
      (
        curve: [
          z.infer<typeof CurvePointSchema>,
          z.infer<typeof CurvePointSchema>,
          z.infer<typeof CurvePointSchema>,
          z.infer<typeof CurvePointSchema>
        ]
      ) => {
        const times = curve.map(
          (p: z.infer<typeof CurvePointSchema>) => p.Time
        );
        return new Set(times).size === times.length;
      },
      { message: "Curve points must have unique Time values" }
    ),
});

// Continuous Event Parameters Schema for V2
const ContinuousEventParametersV2Schema = ParametersBaseSchema.extend({
  Curve: z
    .array(CurvePointSchema)
    .min(4, "Curve must have at least 4 points")
    .max(16, "Curve cannot have more than 16 points")
    .refine(
      (curve: z.infer<typeof CurvePointSchema>[]) =>
        curve.length > 0 && curve[0].Time === 0 && curve[0].Intensity === 0,
      { message: "First curve point Time and Intensity must be 0" }
    )
    .refine(
      (curve: z.infer<typeof CurvePointSchema>[]) =>
        curve.length > 0 && curve[curve.length - 1].Intensity === 0,
      { message: "Last curve point Intensity must be 0" }
    )
    .refine(
      (curve: z.infer<typeof CurvePointSchema>[]) => {
        const times = curve.map((p) => p.Time);
        return new Set(times).size === times.length;
      },
      { message: "Curve points must have unique Time values" }
    ),
});

// Base Event Schema
const EventBaseSchema = z.object({
  RelativeTime: z
    .number()
    .int("RelativeTime must be an integer")
    .nonnegative("RelativeTime cannot be negative"),
});

// V1 Transient Event Schema
const TransientEventV1Schema = EventBaseSchema.extend({
  Type: z.literal("transient"),
  Parameters: TransientEventParametersSchema,
});

// V1 Continuous Event Schema
const ContinuousEventV1Schema = EventBaseSchema.extend({
  Type: z.literal("continuous"),
  Duration: z
    .number()
    .int("Duration must be an integer")
    .positive("Duration must be positive"),
  Parameters: ContinuousEventParametersSchema,
});

// V1 Event Schema Union
const EventV1Schema = z
  .discriminatedUnion("Type", [TransientEventV1Schema, ContinuousEventV1Schema])
;

// WrappedEventV1Schema 只保留一次定义
const WrappedEventV1Schema = z.object({
  Event: EventV1Schema,
});

// V2 Transient Event Schema
const TransientEventV2Schema = EventBaseSchema.extend({
  Type: z.literal("transient"),
  Index: z.union([z.literal(0), z.literal(1)], {
    errorMap: () => ({ message: "Index must be 0 or 1" }),
  }),
  Parameters: TransientEventParametersSchema,
});

// V2 Continuous Event Schema
const ContinuousEventV2Schema = EventBaseSchema.extend({
  Type: z.literal("continuous"),
  Index: z.union([z.literal(0), z.literal(1)], {
    errorMap: () => ({ message: "Index must be 0 or 1" }),
  }),
  Duration: z
    .number()
    .int("Duration must be an integer")
    .positive("Duration must be positive"),
  Parameters: ContinuousEventParametersV2Schema,
});

// V2 Event Schema Union
const EventV2Schema = z
  .discriminatedUnion("Type", [TransientEventV2Schema, ContinuousEventV2Schema])
;

// V2 PatternListItem Schema
const PatternListItemSchema = z.object({
  AbsoluteTime: z
    .number()
    .int("AbsoluteTime must be an integer")
    .nonnegative("AbsoluteTime cannot be negative"),
  Pattern: z.array(EventV2Schema).nonempty("Pattern cannot be empty"),
});

// V1 Top Level Schema
const RealityTapEffectV1Schema = z.object({
  Metadata: MetadataV1Schema,
  Pattern: z.array(WrappedEventV1Schema).nonempty("Pattern cannot be empty"), // Changed from EventV1Schema
});

// V2 Top Level Schema
const RealityTapEffectV2Schema = z.object({
  Metadata: MetadataV2Schema,
  PatternList: z
    .array(PatternListItemSchema)
    .nonempty("PatternList cannot be empty"),
});

// Helper schema to extract version before full parsing
const VersionExtractorSchema = z
  .object({
    Metadata: z
      .object({
        Version: z.union([z.literal(1), z.literal(2), z.number().int()]),
      })
      .optional(),
  })
  .passthrough();

// ---------------------------
// Functions
// ---------------------------

/**
 * Parses a RealityTap JSON string (.he file content) into a validated V1 or V2 Effect object.
 * Throws SyntaxError for invalid JSON, ZodError for schema violations, or Error for missing/invalid version.
 *
 * @param jsonString The JSON string to parse.
 * @returns A validated RealityTapEffectV1 or RealityTapEffectV2 object.
 */
export function parseRealityTap(
  jsonString: string
): RealityTapEffectV1 | RealityTapEffectV2 {
  let parsedObject: unknown;
  try {
    parsedObject = JSON.parse(jsonString);
  } catch (e) {
    if (e instanceof SyntaxError) {
      throw new SyntaxError(`Invalid JSON: ${e.message}`);
    }
    if (e instanceof Error) {
      throw new Error(`Unexpected parsing error: ${e.message}`);
    }
    throw new Error("An unknown parsing error occurred");
  }

  const versionResult = VersionExtractorSchema.safeParse(parsedObject);

  if (!versionResult.success || !versionResult.data.Metadata?.Version) {
    throw new Error(
      "Invalid RealityTap JSON: Missing or invalid Metadata or Metadata.Version field."
    );
  }

  const version = versionResult.data.Metadata.Version;

  try {
    if (version === 1) {
      return RealityTapEffectV1Schema.parse(parsedObject);
    } else if (version === 2) {
      return RealityTapEffectV2Schema.parse(parsedObject);
    } else {
      throw new Error(`Unsupported RealityTap version: ${version}`);
    }
  } catch (error: unknown) {
    if (error instanceof ZodError) {
      const formattedError = error.errors
        .map((err: z.ZodIssue) => `${err.path.join(".")}: ${err.message}`)
        .join("; ");
      throw new Error(
        `Schema validation failed (Version ${version}): ${formattedError}`
      );
    }
    if (error instanceof Error) {
      throw new Error(`Schema validation or other error: ${error.message}`);
    }
    throw new Error("An unknown error occurred during schema validation.");
  }
}

/**
 * Converts a RealityTapEffectV1 or RealityTapEffectV2 object into a JSON string.
 * Validates the object structure against the corresponding schema before stringifying.
 * Throws ZodError if the object structure is invalid.
 *
 * @param effect The RealityTapEffectV1 or RealityTapEffectV2 object.
 * @param prettyPrint Whether to format the output JSON string with indentation (default: false).
 * @returns The JSON string representation of the effect.
 */
export function stringifyRealityTap(
  effect: RealityTapEffectV1 | RealityTapEffectV2,
  prettyPrint: boolean = false
): string {
  const version = effect.Metadata.Version;

  try {
    if (version === 1) {
      RealityTapEffectV1Schema.parse(effect);
    } else if (version === 2) {
      RealityTapEffectV2Schema.parse(effect);
    } else {
      throw new Error(
        `Invalid version (${version}) in RealityTapEffect object for stringify.`
      );
    }
  } catch (error: unknown) {
    if (error instanceof ZodError) {
      const formattedError = error.errors
        .map((err: z.ZodIssue) => `${err.path.join(".")}: ${err.message}`)
        .join("; ");
      throw new Error(
        `Input object failed schema validation (Version ${version}): ${formattedError}`
      );
    }
    if (error instanceof Error) {
      throw new Error(
        `Input object validation or other error: ${error.message}`
      );
    }
    throw new Error(
      "An unknown error occurred during input object validation."
    );
  }

  const indent = prettyPrint ? 2 : undefined;
  return JSON.stringify(effect, null, indent);
}

/**
 * 计算transient类型事件的时长（ms）。
 * 频率为0时为25ms，100时为8ms，线性插值。
 * 超出范围自动裁剪。
 */
export function getTransientDuration(frequency: number): number {
  const clamped = Math.max(0, Math.min(100, frequency));
  // 线性插值：0=>25, 100=>8
  return 25 - (clamped / 100) * (25 - 8);
}

/**
 * 计算RealityTapEffectV1的总时长（ms）。
 * 总时长=所有Event的(RelativeTime+时长)的最大值。
 */
export function getTotalDurationV1(effect: RealityTapEffectV1): number {
  if (!effect.Pattern || effect.Pattern.length === 0) return 0;
  return Math.max(
    ...effect.Pattern.map((wrappedEvent) => {
      // Renamed 'event' to 'wrappedEvent'
      const event = wrappedEvent.Event; // Access the actual event
      const base = event.RelativeTime;
      if (event.Type === "continuous") {
        return base + event.Duration;
      } else {
        return base + getTransientDuration(event.Parameters.Frequency);
      }
    })
  );
}

/**
 * 计算RealityTapEffectV2的总时长（ms）。
 * 总时长=所有PatternListItem的(AbsoluteTime+Pattern中每个Event的(RelativeTime+时长))的最大值。
 */
export function getTotalDurationV2(effect: RealityTapEffectV2): number {
  if (!effect.PatternList || effect.PatternList.length === 0) return 0;
  let maxEnd = 0;
  for (const item of effect.PatternList) {
    if (!item.Pattern || item.Pattern.length === 0) continue;
    const localMax = Math.max(
      ...item.Pattern.map((event) => {
        const base = item.AbsoluteTime + event.RelativeTime;
        if (event.Type === "continuous") {
          return base + event.Duration;
        } else {
          return base + getTransientDuration(event.Parameters.Frequency);
        }
      })
    );
    if (localMax > maxEnd) maxEnd = localMax;
  }
  return maxEnd;
}

/**
 * 计算RealityTapEffect（V1或V2）的总时长（ms）。
 */
export function getTotalDuration(effect: RealityTapEffect): number {
  if (effect.Metadata.Version === 1) {
    return getTotalDurationV1(effect as RealityTapEffectV1);
  } else if (effect.Metadata.Version === 2) {
    return getTotalDurationV2(effect as RealityTapEffectV2);
  } else {
    throw new Error(
      "Unsupported RealityTapEffect version for duration calculation."
    );
  }
}

// ---------------------------
// Exports (already done inline for interfaces/types)
// ---------------------------
// Functions are exported above
