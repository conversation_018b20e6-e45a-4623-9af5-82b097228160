<template>
  <div class="file-selector">
    <n-card size="small">
      <template #header>
        <n-space align="center">
          <n-icon size="20">
            <CloudUploadOutline />
          </n-icon>
          <span>选择上传文件</span>
        </n-space>
      </template>

      <n-space vertical>
        <n-alert type="info" :show-icon="false" style="margin-bottom: 16px">
          <template #header>
            <n-space align="center">
              <n-icon size="16"><InformationCircleOutline /></n-icon>
              <span>文件上传说明</span>
            </n-space>
          </template>
          请按顺序选择以下三个文件，所有文件都是必需的：
          <ol style="margin: 8px 0; padding-left: 20px">
            <li>安装包文件（.exe, .dmg, .deb 等）</li>
            <li>对应的签名文件（.sig）</li>
            <li>对应的哈希文件（.hash，包含 SHA256 哈希值）</li>
          </ol>
        </n-alert>

        <!-- 安装包文件选择 -->
        <div class="file-input-group">
          <n-space align="center" justify="space-between">
            <n-text strong>1. 选择安装包文件</n-text>
            <n-upload
              ref="installerUploadRef"
              :custom-request="customRequestHandler"
              :show-file-list="false"
              :max="1"
              accept=".exe,.dmg,.deb,.rpm,.tar.gz,.zip,.msi,.pkg"
              @before-upload="handleInstallerFileSelect"
            >
              <n-button>
                <template #icon>
                  <n-icon><DocumentOutline /></n-icon>
                </template>
                选择安装包
              </n-button>
            </n-upload>
          </n-space>

          <!-- 显示选中的安装包文件 -->
          <div v-if="installerFile" class="selected-file">
            <n-space align="center" justify="space-between">
              <n-space align="center">
                <n-icon size="16" color="#18a058">
                  <DocumentOutline />
                </n-icon>
                <span>{{ installerFile.name }}</span>
                <n-tag size="small" type="success">安装包</n-tag>
              </n-space>
              <n-space align="center">
                <n-text depth="3">{{ formatBytes(installerFile.size) }}</n-text>
                <n-button size="tiny" quaternary type="error" @click="clearInstallerFile">
                  <template #icon>
                    <n-icon><TrashOutline /></n-icon>
                  </template>
                </n-button>
              </n-space>
            </n-space>
          </div>
        </div>

        <!-- 签名文件选择 -->
        <div class="file-input-group">
          <n-space align="center" justify="space-between">
            <n-text strong>2. 选择签名文件</n-text>
            <n-upload
              ref="signatureUploadRef"
              :custom-request="customRequestHandler"
              :show-file-list="false"
              :max="1"
              accept=".sig"
              @before-upload="handleSignatureFileSelect"
            >
              <n-button :disabled="!installerFile">
                <template #icon>
                  <n-icon><DocumentOutline /></n-icon>
                </template>
                选择签名文件
              </n-button>
            </n-upload>
          </n-space>

          <!-- 显示选中的签名文件 -->
          <div v-if="signatureFile && signatureValue" class="selected-file">
            <n-space align="center" justify="space-between">
              <n-space align="center">
                <n-icon size="16" color="#f0a020">
                  <DocumentOutline />
                </n-icon>
                <n-tag size="small" type="warning">签名内容</n-tag>
              </n-space>
              <n-space align="center">
                <n-text depth="3">{{ formatBytes(signatureFile.size) }}</n-text>
                <n-button size="tiny" quaternary type="error" @click="clearSignatureFile">
                  <template #icon>
                    <n-icon><TrashOutline /></n-icon>
                  </template>
                </n-button>
              </n-space>
            </n-space>
            <!-- 显示签名内容 -->
            <div class="signature-content">
              <n-input
                type="textarea"
                :value="signatureValue"
                readonly
                :rows="6"
                placeholder="签名内容将在这里显示"
                style="margin-top: 8px; font-family: monospace; font-size: 12px"
              />
            </div>
          </div>
        </div>

        <!-- 哈希文件选择 -->
        <div class="file-input-group">
          <n-space align="center" justify="space-between">
            <n-text strong>3. 选择哈希文件（必需）</n-text>
            <n-upload
              ref="hashUploadRef"
              :custom-request="customRequestHandler"
              :show-file-list="false"
              :max="1"
              accept=".hash"
              @before-upload="handleHashFileSelect"
            >
              <n-button :disabled="!installerFile">
                <template #icon>
                  <n-icon><DocumentOutline /></n-icon>
                </template>
                选择哈希文件
              </n-button>
            </n-upload>
          </n-space>

          <!-- 显示选中的哈希文件 -->
          <div v-if="hashFile && hashValue" class="selected-file">
            <n-space align="center" justify="space-between">
              <n-space align="center">
                <n-icon size="16" color="#2080f0">
                  <DocumentOutline />
                </n-icon>
                <span style="font-family: monospace; font-size: 12px">{{ hashValue }}</span>
                <n-tag size="small" type="info">哈希值</n-tag>
              </n-space>
              <n-space align="center">
                <n-text depth="3">{{ formatBytes(hashFile.size) }}</n-text>
                <n-button size="tiny" quaternary type="error" @click="clearHashFile">
                  <template #icon>
                    <n-icon><TrashOutline /></n-icon>
                  </template>
                </n-button>
              </n-space>
            </n-space>
          </div>
        </div>
      </n-space>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { CloudUploadOutline, DocumentOutline, TrashOutline, InformationCircleOutline } from '@vicons/ionicons5';
import {
  NAlert,
  NButton,
  NCard,
  NIcon,
  NInput,
  NSpace,
  NTag,
  NText,
  NUpload,
  useMessage,
  type UploadFileInfo,
} from 'naive-ui';
import { ref, computed } from 'vue';
import {
  formatBytes,
  validateFileType,
  validateFileSize,
  validateHashFormat,
  checkFileNameMatch,
  readFileAsText,
  FILE_TYPE_MAP,
} from '@/utils/fileUpload';

// Props
interface Props {
  installerFile: File | null;
  signatureFile: File | null;
  hashFile: File | null;
  signatureValue: string | null;
  hashValue: string | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:installerFile': [file: File | null];
  'update:signatureFile': [file: File | null];
  'update:hashFile': [file: File | null];
  'update:signatureValue': [value: string | null];
  'update:hashValue': [value: string | null];
}>();

const message = useMessage();

// Refs
const installerUploadRef = ref();
const signatureUploadRef = ref();
const hashUploadRef = ref();

// 计算属性
const installerFile = computed(() => props.installerFile);
const signatureFile = computed(() => props.signatureFile);
const hashFile = computed(() => props.hashFile);
const signatureValue = computed(() => props.signatureValue);
const hashValue = computed(() => props.hashValue);

// 自定义请求处理器
const customRequestHandler = () => {
  return Promise.resolve({});
};

// 验证文件
const validateFile = (file: File, allowedTypes: string[], fileType: string = '文件'): boolean => {
  if (!validateFileSize(file.size)) {
    message.error(`${fileType}大小不能超过 5GB，当前文件大小: ${formatBytes(file.size)}`);
    return false;
  }

  if (file.size === 0) {
    message.error(`${fileType}不能为空`);
    return false;
  }

  if (!validateFileType(file.name, allowedTypes)) {
    message.error(`不支持的${fileType}类型，当前文件: ${file.name}，支持的类型: ${allowedTypes.join(', ')}`);
    return false;
  }

  return true;
};

// 文件选择处理
const handleInstallerFileSelect = (data: { file: UploadFileInfo }): boolean => {
  const file = data.file.file;
  if (!file) return false;

  if (!validateFile(file, [...FILE_TYPE_MAP.installer.extensions], FILE_TYPE_MAP.installer.description)) {
    return false;
  }

  // 清除之前的签名和哈希文件，因为它们应该与新的安装包匹配
  if (props.signatureFile || props.hashFile) {
    emit('update:signatureFile', null);
    emit('update:signatureValue', null);
    emit('update:hashFile', null);
    emit('update:hashValue', null);
    message.info('已清除之前的签名和哈希文件，请重新选择与新安装包匹配的文件');
  }

  emit('update:installerFile', file);
  message.success(`安装包文件选择成功: ${file.name}`);
  return false;
};

const handleSignatureFileSelect = async (data: { file: UploadFileInfo }): Promise<boolean> => {
  const file = data.file.file;
  if (!file) return false;

  if (!props.installerFile) {
    message.error('请先选择安装包文件');
    return false;
  }

  if (!validateFile(file, [...FILE_TYPE_MAP.signature.extensions], FILE_TYPE_MAP.signature.description)) {
    return false;
  }

  // 检查文件名是否与安装包匹配
  if (!checkFileNameMatch(props.installerFile.name, file.name, '.sig')) {
    message.warning(
      `签名文件名 "${file.name}" 与安装包文件名 "${props.installerFile.name}" 不匹配，建议使用匹配的文件名`,
    );
  }

  try {
    message.loading('正在读取签名文件...', { duration: 0 });
    const signatureContent = await readFileAsText(file);
    const trimmedSignature = signatureContent.trim();

    if (!trimmedSignature) {
      message.destroyAll();
      message.error('签名文件内容为空，请检查文件是否正确');
      return false;
    }

    // 基本的签名格式验证
    if (trimmedSignature.length < 10) {
      message.destroyAll();
      message.error('签名内容过短，可能不是有效的签名文件');
      return false;
    }

    message.destroyAll();
    emit('update:signatureFile', file);
    emit('update:signatureValue', trimmedSignature);

    console.log('✅ 签名文件处理完成', {
      fileName: file.name,
      signatureLength: trimmedSignature.length,
      signaturePreview: trimmedSignature.substring(0, 50) + '...',
      emittedValue: trimmedSignature
    });

    message.success(`签名文件读取成功: ${file.name} (${trimmedSignature.length} 字符)`);
  } catch (error: any) {
    message.destroyAll();
    console.error('读取签名文件失败:', error);
    message.error(`读取签名文件失败: ${error.message || '未知错误'}`);
    return false;
  }

  return false;
};

const handleHashFileSelect = async (data: { file: UploadFileInfo }): Promise<boolean> => {
  const file = data.file.file;
  if (!file) return false;

  if (!props.installerFile) {
    message.error('请先选择安装包文件');
    return false;
  }

  if (!validateFile(file, [...FILE_TYPE_MAP.hash.extensions], FILE_TYPE_MAP.hash.description)) {
    return false;
  }

  // 检查文件名是否与安装包匹配
  if (!checkFileNameMatch(props.installerFile.name, file.name, '.hash')) {
    message.warning(
      `哈希文件名 "${file.name}" 与安装包文件名 "${props.installerFile.name}" 不匹配，建议使用匹配的文件名`,
    );
  }

  try {
    message.loading('正在读取哈希文件...', { duration: 0 });
    const hashContent = await readFileAsText(file);
    const trimmedHash = hashContent.trim();

    // 验证哈希格式
    if (!trimmedHash) {
      message.destroyAll();
      message.error('哈希文件内容为空，请检查文件是否正确');
      return false;
    }

    // 验证哈希格式
    const hashValidation = validateHashFormat(trimmedHash);
    if (!hashValidation.isValid) {
      message.destroyAll();
      message.error('哈希格式无效。支持的格式：SHA256 (64位)、SHA1 (40位)、MD5 (32位) 十六进制字符串');
      return false;
    }

    // 对非 SHA256 哈希给出警告
    if (hashValidation.type === 'SHA1') {
      message.warning('检测到 SHA1 哈希，建议使用更安全的 SHA256 哈希');
    } else if (hashValidation.type === 'MD5') {
      message.warning('检测到 MD5 哈希，建议使用更安全的 SHA256 哈希');
    }

    message.destroyAll();
    emit('update:hashFile', file);
    emit('update:hashValue', trimmedHash);
    message.success(`哈希文件读取成功: ${file.name} (${hashValidation.type})`);
  } catch (error: any) {
    message.destroyAll();
    console.error('读取哈希文件失败:', error);
    message.error(`读取哈希文件失败: ${error.message || '未知错误'}`);
    return false;
  }

  return false;
};

// 清除文件
const clearInstallerFile = () => {
  emit('update:installerFile', null);
};

const clearSignatureFile = () => {
  emit('update:signatureFile', null);
  emit('update:signatureValue', null);
};

const clearHashFile = () => {
  emit('update:hashFile', null);
  emit('update:hashValue', null);
};
</script>

<style scoped>
.file-selector {
  margin-bottom: 16px;
}

.file-input-group {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  background-color: var(--n-card-color);
}

.selected-file {
  margin-top: 8px;
  padding: 8px;
  background-color: var(--n-modal-color);
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}
</style>
