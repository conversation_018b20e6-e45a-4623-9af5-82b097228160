# RealityTap Desktop Haptic 模块架构分析

## 概述

RealityTap Desktop 的 haptic 模块采用分层架构设计，实现了从 C++ 核心库到 TypeScript 前端的完整触觉反馈处理链路。该架构通过 FFI 绑定、异步消息传递和事件驱动机制，提供了高性能、内存安全的触觉反馈处理系统。

## 整体架构

### 核心组件结构

```
src-tauri/src/haptic/
├── mod.rs                 # 模块导出
├── ffi.rs                 # librtcore FFI 绑定
├── handler.rs             # 触觉输出处理器
├── commands.rs            # Tauri 命令接口
├── message.rs             # 消息类型定义
├── channel.rs             # 通道管理
├── processor.rs           # 消息处理器
├── filter.rs              # 消息过滤器
├── aggregation.rs         # 消息聚合器
├── event.rs               # 事件系统
├── error.rs               # 错误处理
└── lifecycle.rs           # 生命周期管理
```

### 架构层次

1. **C++ 核心库层 (librtcore.dll)**
   - 提供核心的触觉反馈处理功能
   - 定义 IHapticOutputHandler 接口
   - 通过回调函数与 Rust 层通信

2. **Rust 后端层**
   - FFI 绑定：与 C++ 库的安全接口
   - Handler 实现：IHapticOutputHandler 接口的 Rust 实现
   - 消息系统：高性能异步消息处理
   - Tauri 命令：前端调用接口

3. **Tauri 通信层**
   - 命令调用：前端到后端的函数调用
   - 事件系统：后端到前端的消息推送

4. **TypeScript 前端层**
   - Vue Composable：响应式触觉反馈管理
   - API 封装：简化的前端接口
   - 事件监听：实时接收后端消息

## FFI 交互机制

### 1. 动态库加载

```rust
/// 加载 RealityTap 核心库
pub fn load_library() -> HapticResult<()> {
    let lib_path = "librtcore.dll";
    let library = unsafe {
        Library::new(lib_path).map_err(|e| {
            HapticError::DllLoadFailed(format!("无法加载 {}: {}", lib_path, e))
        })?
    };
    
    // 加载所有 API 函数
    let api = load_api_functions(&library)?;
    // ...
}
```

### 2. 虚函数表机制

```rust
/// 输出处理器虚函数表
#[repr(C)]
pub struct HapticOutputHandlerVTable {
    pub on_haptic_output_start: OnHapticOutputStartFn,
    pub on_haptic_output_complete: OnHapticOutputCompleteFn,
    pub on_haptic_output_stop: OnHapticOutputStopFn,
    pub on_waveform_chunk_start: OnWaveformChunkStartFn,
    pub process_waveform_sample: ProcessWaveformSampleFn,
    pub wait_chunk_processing_complete: WaitChunkProcessingCompleteFn,
    pub set_output_amplitude: SetOutputAmplitudeFn,
}
```

### 3. 安全回调实现

```rust
extern "C" fn on_haptic_output_start_c(data: *mut c_void) {
    let result = catch_unwind(AssertUnwindSafe(|| {
        if data.is_null() {
            log::error!("FFI 调用传入空指针");
            return;
        }

        let handler_ptr = data as *mut HapticOutputHandler;
        
        // 检查 handler 是否仍然有效
        if !is_handler_valid(handler_ptr) {
            log::warn!("尝试回调已释放的 handler，忽略回调");
            return;
        }

        if let Some(handler) = unsafe { handler_ptr.as_mut() } {
            handler.on_haptic_output_start();
        }
    }));

    if let Err(panic_info) = result {
        log::error!("FFI 回调中发生 panic: {:?}", panic_info);
        GLOBAL_ERROR_STATE.store(true, Ordering::SeqCst);
    }
}
```

## 消息传递系统

### 1. 消息类型定义

```rust
/// 触觉消息类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum HapticMessage {
    // === 生命周期事件（高优先级，实时发送） ===
    OutputStart {
        device_id: u32,
        timestamp: u64,
    },
    
    OutputComplete {
        device_id: u32,
        timestamp: u64,
        sample_count: u64,
    },
    
    // === 数据事件（低优先级，可聚合发送） ===
    WaveformSample {
        device_id: u32,
        timestamp: u64,
        sample: i8,
        sample_index: u64,
    },
    
    // 其他消息类型...
}
```

### 2. 消息优先级处理

```rust
impl HapticMessage {
    /// 获取消息的优先级
    pub fn priority(&self) -> MessagePriority {
        match self {
            // 高优先级：生命周期事件和错误事件
            HapticMessage::OutputStart { .. } |
            HapticMessage::OutputComplete { .. } |
            HapticMessage::OutputStop { .. } |
            HapticMessage::ProcessorError { .. } => MessagePriority::High,
            
            // 中优先级：状态变更事件
            HapticMessage::AmplitudeChange { .. } |
            HapticMessage::ProcessingStatus { .. } => MessagePriority::Medium,
            
            // 低优先级：高频数据事件
            HapticMessage::ChunkStart { .. } |
            HapticMessage::WaveformSample { .. } => MessagePriority::Low,
        }
    }
}
```

### 3. Channel 管理

```rust
/// 触觉消息通道管理器
pub struct HapticChannelManager {
    /// 消息发送器
    message_sender: Sender<HapticMessage>,
    /// 消息接收器
    message_receiver: Receiver<HapticMessage>,
    /// 后台消息处理器
    message_processor: Option<HapticMessageProcessor>,
    /// 配置
    config: ChannelConfig,
}
```

## 性能优化策略

### 1. 消息聚合机制

- **时间窗口聚合**：将指定时间窗口内的高频消息合并
- **数量聚合**：达到指定数量后批量发送
- **优先级分离**：生命周期事件实时发送，数据事件聚合发送

### 2. 内存管理

- **有界队列**：防止消息积压导致内存溢出
- **消息丢弃策略**：队列满时丢弃低优先级消息
- **指针跟踪**：防止访问已释放的 Handler 内存

```rust
/// 全局有效 handler 指针跟踪
static VALID_HANDLERS: Lazy<Arc<Mutex<HashSet<usize>>>> =
    Lazy::new(|| Arc::new(Mutex::new(HashSet::new())));

/// 检查 handler 指针是否有效
pub fn is_handler_valid(handler_ptr: *mut HapticOutputHandler) -> bool {
    let handler_addr = handler_ptr as usize;
    if let Ok(valid_handlers) = VALID_HANDLERS.lock() {
        valid_handlers.contains(&handler_addr)
    } else {
        false
    }
}
```

### 3. 错误处理

- **Panic 捕获**：使用 `catch_unwind` 防止 Rust panic 跨越 FFI 边界
- **全局错误状态**：跟踪系统错误状态
- **自动恢复机制**：支持连接断开后的自动重连

## TypeScript 前端接口

### 1. 事件监听

```typescript
// 监听生命周期事件
await listen('haptic:lifecycle:start', (event) => {
  console.log('触觉输出开始:', event.payload);
});

await listen('haptic:lifecycle:complete', (event) => {
  console.log('触觉输出完成:', event.payload);
});

// 监听聚合数据事件
await listen('haptic:data:waveform_samples', (event) => {
  const data = event.payload as HapticEventData;
  console.log(`接收到 ${data.metadata.aggregation_count} 个聚合样本`);
});

// 监听错误事件
await listen('haptic:error:system', (event) => {
  console.error('系统错误:', event.payload);
});
```

### 2. Vue Composable 使用

```typescript
export default defineComponent({
  setup() {
    const hapticManager = useHapticManager();

    const initHaptic = async () => {
      // 启动消息处理器
      await hapticManager.startMessageProcessor();
      
      // 创建执行器配置
      const config = hapticUtils.createDefaultConfig(0);
      
      // 初始化并启动
      await hapticManager.initialize([config]);
      await hapticManager.start();
    };

    const playEffect = async () => {
      await hapticManager.playEffect(1, 80); // 轻点击，80% 强度
    };

    return {
      hapticManager,
      initHaptic,
      playEffect,
    };
  }
});
```

## 关键设计模式

1. **适配器模式**：Rust Handler 适配 C++ IHapticOutputHandler 接口
2. **观察者模式**：事件系统实现回调消息的分发
3. **生产者-消费者模式**：Channel 系统实现异步消息处理
4. **策略模式**：消息分类和处理策略的可配置化

## 总结

该架构通过分层设计和异步消息传递，实现了：

- **高性能**：异步消息处理和智能聚合机制
- **内存安全**：指针跟踪和 panic 捕获
- **可扩展性**：模块化设计和策略模式
- **实时性**：优先级处理和事件驱动架构

这种设计确保了从 C++ 核心库到 TypeScript 前端的高效、安全通信。
