import { getVersionService } from '@/services/service-factory';
import { ErrorResponse } from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import { getRequestIPInfo, logOperationIP } from '@/middleware/ip-logging.middleware';
import { NextFunction, Request, Response, Router } from 'express';
import { z } from 'zod';

const router: Router = Router();

// 获取版本服务实例
const versionService = getVersionService();

// Target 平台映射
const TARGET_PLATFORM_MAP: Record<string, { platform: string; architecture: string }> = {
  'windows-x86_64': { platform: 'windows', architecture: 'x86_64' },
  'windows-i686': { platform: 'windows', architecture: 'x86' },
  'darwin-x86_64': { platform: 'macos', architecture: 'x86_64' },
  'darwin-aarch64': { platform: 'macos', architecture: 'aarch64' },
  'linux-x86_64': { platform: 'linux', architecture: 'x86_64' },
  'linux-aarch64': { platform: 'linux', architecture: 'aarch64' },
  // 兼容性映射：支持简化的target格式
  'windows': { platform: 'windows', architecture: 'x86_64' },
  'macos': { platform: 'macos', architecture: 'x86_64' },
  'linux': { platform: 'linux', architecture: 'x86_64' },
  'darwin': { platform: 'macos', architecture: 'x86_64' },
};

// 验证模式
const UpdateRequestSchema = z.object({
  target: z.string().refine(target => target in TARGET_PLATFORM_MAP, {
    message: 'Invalid target platform',
  }),
  current_version: z.string().regex(/^\d+\.\d+\.\d+/, 'Invalid version format'),
});

/**
 * Tauri Plugin Updater 兼容的更新检查 API
 * GET /api/v1/updates/:target/:current_version
 * 
 * 支持的 target 格式：
 * - windows-x86_64
 * - windows-i686  
 * - darwin-x86_64
 * - darwin-aarch64
 * - linux-x86_64
 * - linux-aarch64
 */
router.get('/:target/:current_version', async (req: Request, res: Response, next: NextFunction) => {
  try {
    // 验证路径参数
    const validationResult = UpdateRequestSchema.safeParse({
      target: req.params.target,
      current_version: req.params.current_version,
    });

    if (!validationResult.success) {
      logger.warn('Invalid updater request parameters', {
        params: req.params,
        errors: validationResult.error.errors,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
      });
      res.status(400).json({
        error: 'Invalid request parameters',
        details: validationResult.error.errors,
      });
      return;
    }

    const { target, current_version } = validationResult.data;
    const platformInfo = TARGET_PLATFORM_MAP[target];
    if (!platformInfo) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'UNSUPPORTED_TARGET',
          message: `Unsupported target platform: ${target}`,
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }
    const { platform, architecture } = platformInfo;

    // 从查询参数获取渠道，默认为 stable
    const channel = (req.query.channel as string) || 'stable';

    // 获取客户端IP信息
    const ipInfo = getRequestIPInfo(req);

    // 记录更新检查操作
    logOperationIP(req, 'UPDATE_CHECK', {
      target,
      current_version,
      platform,
      architecture,
      channel,
      userAgent: req.get('User-Agent'),
    });

    // 记录详细的请求日志
    logger.info('客户端检查更新请求', {
      target,
      current_version,
      platform,
      architecture,
      channel,
      userAgent: req.get('User-Agent'),
      clientIP: ipInfo.ip,
      isPrivateIP: ipInfo.isPrivate,
      anonymizedIP: ipInfo.anonymized,
      module: 'ota_function',
      operation: 'update_check',
    });

    // 执行版本检查
    const updateInfo = await versionService.checkForUpdates({
      currentVersion: current_version,
      platform: platform as 'windows' | 'macos' | 'linux',
      architecture: architecture as 'x86_64' | 'aarch64' | 'x86',
      channel: channel as 'stable' | 'beta' | 'alpha',
    });

    // 如果没有更新，返回 204 No Content
    if (!updateInfo.hasUpdate) {
      logger.info('No update available', {
        target,
        current_version,
        latest_version: updateInfo.latestVersion,
        clientIP: ipInfo.ip,
      });
      res.status(204).end();
      return;
    }

    // 构建 tauri-plugin-updater 兼容的响应格式
    const tauriResponse = {
      url: updateInfo.downloadUrl,
      version: updateInfo.latestVersion!,
      notes: updateInfo.releaseNotes || '',
      pub_date: updateInfo.releaseDate || new Date().toISOString(),
      signature: updateInfo.signature || '',
      force_update: updateInfo.isForced || false,
      file_size: updateInfo.fileSize || 0, // 添加文件大小字段
    };

    // 记录更新可用的操作
    logOperationIP(req, 'UPDATE_AVAILABLE', {
      target,
      current_version,
      latest_version: tauriResponse.version,
      download_url: tauriResponse.url,
    });

    logger.info('发现可用更新', {
      target,
      current_version,
      latest_version: tauriResponse.version,
      download_url: tauriResponse.url,
      clientIP: ipInfo.ip,
      module: 'ota_function',
      operation: 'update_available',
    });

    res.json(tauriResponse);
  } catch (error) {
    const errorIPInfo = getRequestIPInfo(req);
    logger.error('Tauri updater request failed', {
      target: req.params.target,
      current_version: req.params.current_version,
      error,
      userAgent: req.get('User-Agent'),
      clientIP: errorIPInfo.ip,
    });
    next(error);
  }
});

/**
 * 获取支持的平台列表
 * GET /api/v1/updates/platforms
 */
router.get('/platforms', (req: Request, res: Response) => {
  const platforms = Object.keys(TARGET_PLATFORM_MAP).map(target => {
    const platformInfo = TARGET_PLATFORM_MAP[target];
    if (!platformInfo) {
      throw new Error(`Invalid platform configuration for target: ${target}`);
    }
    const { platform, architecture } = platformInfo;
    return {
      target,
      platform,
      architecture,
    };
  });

  res.json({
    platforms,
    supported_targets: Object.keys(TARGET_PLATFORM_MAP),
  });
});

export default router;
