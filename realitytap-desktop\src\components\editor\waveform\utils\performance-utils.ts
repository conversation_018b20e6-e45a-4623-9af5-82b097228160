/**
 * 性能优化工具函数
 * 专门处理节流、防抖等性能相关功能
 */

/**
 * 节流函数
 * 限制函数在指定时间间隔内只能执行一次
 */
export function throttle<T extends (...args: any[]) => any>(fn: T, delay: number): T {
  let lastCall = 0;
  return function (this: any, ...args: any[]) {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return fn.apply(this, args);
    }
  } as T;
}

/**
 * 防抖函数
 * 延迟执行函数，如果在延迟期间再次调用，则重新计时
 */
export function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): T {
  let timeoutId: number | undefined;
  return function (this: any, ...args: any[]) {
    clearTimeout(timeoutId);
    timeoutId = window.setTimeout(() => fn.apply(this, args), delay);
  } as T;
}

/**
 * 请求动画帧节流
 * 确保函数在每个动画帧中最多执行一次
 */
export function rafThrottle<T extends (...args: any[]) => any>(fn: T): T {
  let rafId: number | null = null;
  let lastArgs: any[] | null = null;

  return function (this: any, ...args: any[]) {
    lastArgs = args;
    
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        if (lastArgs) {
          fn.apply(this, lastArgs);
        }
        rafId = null;
        lastArgs = null;
      });
    }
  } as T;
}

/**
 * 性能监控器
 * 用于监控函数执行时间
 */
export class PerformanceMonitor {
  private measurements: Map<string, number[]> = new Map();
  private maxSamples = 100;

  /**
   * 开始测量
   */
  start(name: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.addMeasurement(name, duration);
    };
  }

  /**
   * 添加测量结果
   */
  private addMeasurement(name: string, duration: number): void {
    if (!this.measurements.has(name)) {
      this.measurements.set(name, []);
    }
    
    const samples = this.measurements.get(name)!;
    samples.push(duration);
    
    // 保持样本数量在限制内
    if (samples.length > this.maxSamples) {
      samples.shift();
    }
  }

  /**
   * 获取平均执行时间
   */
  getAverage(name: string): number {
    const samples = this.measurements.get(name);
    if (!samples || samples.length === 0) return 0;
    
    const sum = samples.reduce((acc, val) => acc + val, 0);
    return sum / samples.length;
  }

  /**
   * 获取最近的执行时间
   */
  getLatest(name: string): number {
    const samples = this.measurements.get(name);
    if (!samples || samples.length === 0) return 0;
    
    return samples[samples.length - 1];
  }

  /**
   * 获取统计信息
   */
  getStats(name: string): { avg: number; min: number; max: number; count: number } {
    const samples = this.measurements.get(name);
    if (!samples || samples.length === 0) {
      return { avg: 0, min: 0, max: 0, count: 0 };
    }
    
    const min = Math.min(...samples);
    const max = Math.max(...samples);
    const avg = samples.reduce((acc, val) => acc + val, 0) / samples.length;
    
    return { avg, min, max, count: samples.length };
  }

  /**
   * 清除测量数据
   */
  clear(name?: string): void {
    if (name) {
      this.measurements.delete(name);
    } else {
      this.measurements.clear();
    }
  }

  /**
   * 获取所有测量名称
   */
  getNames(): string[] {
    return Array.from(this.measurements.keys());
  }
}

/**
 * 全局性能监控器实例
 */
export const globalPerformanceMonitor = new PerformanceMonitor();

/**
 * 性能装饰器
 * 用于自动监控方法执行时间
 */
export function performanceMonitor(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const monitorName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      const end = globalPerformanceMonitor.start(monitorName);
      try {
        const result = originalMethod.apply(this, args);
        if (result instanceof Promise) {
          return result.finally(end);
        } else {
          end();
          return result;
        }
      } catch (error) {
        end();
        throw error;
      }
    };

    return descriptor;
  };
}
