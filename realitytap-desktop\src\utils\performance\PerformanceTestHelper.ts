/**
 * 性能测试辅助工具
 * 用于验证第一阶段优化效果，检测卡顿问题是否得到解决
 */

import { getDebounceManager } from './UnifiedDebounceManager';
import { logger, LogModule } from '@/utils/logger/logger';

export interface PerformanceMetrics {
  frameRate: number;
  averageFrameTime: number;
  maxFrameTime: number;
  minFrameTime: number;
  droppedFrames: number;
  totalFrames: number;
  testDuration: number;
}

export interface InteractionTestResult {
  sliderToCanvasDelay: number;
  canvasToSliderDelay: number;
  averageResponseTime: number;
  maxResponseTime: number;
  isSmooth: boolean;
  issues: string[];
}

/**
 * 性能测试辅助类
 */
export class PerformanceTestHelper {
  private frameTimings: number[] = [];
  private lastFrameTime = 0;
  private testStartTime = 0;
  private isRecording = false;
  private animationFrameId: number | null = null;

  /**
   * 开始性能监控
   */
  startMonitoring(): void {
    this.frameTimings = [];
    this.testStartTime = performance.now();
    this.lastFrameTime = this.testStartTime;
    this.isRecording = true;
    
    this.recordFrame();
  }

  /**
   * 停止性能监控并返回结果
   */
  stopMonitoring(): PerformanceMetrics {
    this.isRecording = false;
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    const testDuration = performance.now() - this.testStartTime;
    const totalFrames = this.frameTimings.length;
    
    if (totalFrames === 0) {
      return {
        frameRate: 0,
        averageFrameTime: 0,
        maxFrameTime: 0,
        minFrameTime: 0,
        droppedFrames: 0,
        totalFrames: 0,
        testDuration
      };
    }

    const averageFrameTime = this.frameTimings.reduce((a, b) => a + b, 0) / totalFrames;
    const maxFrameTime = Math.max(...this.frameTimings);
    const minFrameTime = Math.min(...this.frameTimings);
    const frameRate = 1000 / averageFrameTime;
    
    // 计算掉帧数（超过16.67ms的帧）
    const droppedFrames = this.frameTimings.filter(time => time > 16.67).length;

    return {
      frameRate,
      averageFrameTime,
      maxFrameTime,
      minFrameTime,
      droppedFrames,
      totalFrames,
      testDuration
    };
  }

  /**
   * 记录帧时间
   */
  private recordFrame(): void {
    if (!this.isRecording) return;

    const currentTime = performance.now();
    const frameTime = currentTime - this.lastFrameTime;
    
    if (this.frameTimings.length > 0) { // 跳过第一帧
      this.frameTimings.push(frameTime);
    }
    
    this.lastFrameTime = currentTime;
    
    this.animationFrameId = requestAnimationFrame(() => this.recordFrame());
  }

  /**
   * 测试滑块到Canvas的响应延迟
   */
  async testSliderToCanvasResponse(): Promise<number> {
    return new Promise((resolve) => {
      const startTime = performance.now();
      
      // 模拟滑块值变化
      const testEvent = new CustomEvent('slider-change', {
        detail: { value: Math.random() * 100 }
      });
      
      // 监听Canvas重绘完成
      const observer = new MutationObserver(() => {
        const endTime = performance.now();
        observer.disconnect();
        resolve(endTime - startTime);
      });
      
      // 开始观察Canvas变化
      const canvas = document.querySelector('canvas');
      if (canvas) {
        observer.observe(canvas, { attributes: true, childList: true });
      }
      
      // 触发事件
      document.dispatchEvent(testEvent);
      
      // 超时保护
      setTimeout(() => {
        observer.disconnect();
        resolve(-1); // 表示超时
      }, 1000);
    });
  }

  /**
   * 测试Canvas到滑块的响应延迟
   */
  async testCanvasToSliderResponse(): Promise<number> {
    return new Promise((resolve) => {
      const startTime = performance.now();
      
      // 模拟Canvas拖拽事件
      const canvas = document.querySelector('canvas');
      if (!canvas) {
        resolve(-1);
        return;
      }
      
      // 监听滑块值变化
      const sliderObserver = new MutationObserver(() => {
        const endTime = performance.now();
        sliderObserver.disconnect();
        resolve(endTime - startTime);
      });
      
      // 开始观察滑块变化
      const slider = document.querySelector('.n-slider');
      if (slider) {
        sliderObserver.observe(slider, { attributes: true, subtree: true });
      }
      
      // 模拟鼠标拖拽
      const rect = canvas.getBoundingClientRect();
      const mouseDown = new MouseEvent('mousedown', {
        clientX: rect.left + rect.width / 2,
        clientY: rect.top + rect.height / 2,
        bubbles: true
      });
      
      const mouseMove = new MouseEvent('mousemove', {
        clientX: rect.left + rect.width / 2 + 10,
        clientY: rect.top + rect.height / 2,
        bubbles: true
      });
      
      const mouseUp = new MouseEvent('mouseup', {
        clientX: rect.left + rect.width / 2 + 10,
        clientY: rect.top + rect.height / 2,
        bubbles: true
      });
      
      canvas.dispatchEvent(mouseDown);
      setTimeout(() => canvas.dispatchEvent(mouseMove), 10);
      setTimeout(() => canvas.dispatchEvent(mouseUp), 20);
      
      // 超时保护
      setTimeout(() => {
        sliderObserver.disconnect();
        resolve(-1); // 表示超时
      }, 1000);
    });
  }

  /**
   * 综合交互测试
   */
  async runInteractionTest(iterations: number = 10): Promise<InteractionTestResult> {
    const sliderToCanvasDelays: number[] = [];
    const canvasToSliderDelays: number[] = [];
    const issues: string[] = [];

    for (let i = 0; i < iterations; i++) {
      // 测试滑块到Canvas
      const sliderDelay = await this.testSliderToCanvasResponse();
      if (sliderDelay > 0) {
        sliderToCanvasDelays.push(sliderDelay);
      } else {
        issues.push(`滑块到Canvas响应超时 (iteration ${i + 1})`);
      }

      // 等待一段时间避免测试干扰
      await new Promise(resolve => setTimeout(resolve, 100));

      // 测试Canvas到滑块
      const canvasDelay = await this.testCanvasToSliderResponse();
      if (canvasDelay > 0) {
        canvasToSliderDelays.push(canvasDelay);
      } else {
        issues.push(`Canvas到滑块响应超时 (iteration ${i + 1})`);
      }

      // 等待一段时间避免测试干扰
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const allDelays = [...sliderToCanvasDelays, ...canvasToSliderDelays];
    const averageSliderToCanvas = sliderToCanvasDelays.length > 0 
      ? sliderToCanvasDelays.reduce((a, b) => a + b, 0) / sliderToCanvasDelays.length 
      : 0;
    const averageCanvasToSlider = canvasToSliderDelays.length > 0 
      ? canvasToSliderDelays.reduce((a, b) => a + b, 0) / canvasToSliderDelays.length 
      : 0;
    const averageResponseTime = allDelays.length > 0 
      ? allDelays.reduce((a, b) => a + b, 0) / allDelays.length 
      : 0;
    const maxResponseTime = allDelays.length > 0 ? Math.max(...allDelays) : 0;

    // 判断是否流畅（平均响应时间小于50ms，最大响应时间小于100ms）
    const isSmooth = averageResponseTime < 50 && maxResponseTime < 100 && issues.length === 0;

    return {
      sliderToCanvasDelay: averageSliderToCanvas,
      canvasToSliderDelay: averageCanvasToSlider,
      averageResponseTime,
      maxResponseTime,
      isSmooth,
      issues
    };
  }

  /**
   * 获取防抖管理器统计信息
   */
  getDebounceStats() {
    return getDebounceManager().getAllStats();
  }

  /**
   * 生成性能报告
   */
  generateReport(metrics: PerformanceMetrics, interactionTest: InteractionTestResult): string {
    const report = [
      '=== 第一阶段优化效果测试报告 ===',
      '',
      '## 帧率性能',
      `平均帧率: ${metrics.frameRate.toFixed(2)} FPS`,
      `平均帧时间: ${metrics.averageFrameTime.toFixed(2)} ms`,
      `最大帧时间: ${metrics.maxFrameTime.toFixed(2)} ms`,
      `最小帧时间: ${metrics.minFrameTime.toFixed(2)} ms`,
      `掉帧数: ${metrics.droppedFrames}/${metrics.totalFrames} (${(metrics.droppedFrames / metrics.totalFrames * 100).toFixed(2)}%)`,
      `测试时长: ${(metrics.testDuration / 1000).toFixed(2)} 秒`,
      '',
      '## 交互响应性',
      `滑块→Canvas平均延迟: ${interactionTest.sliderToCanvasDelay.toFixed(2)} ms`,
      `Canvas→滑块平均延迟: ${interactionTest.canvasToSliderDelay.toFixed(2)} ms`,
      `整体平均响应时间: ${interactionTest.averageResponseTime.toFixed(2)} ms`,
      `最大响应时间: ${interactionTest.maxResponseTime.toFixed(2)} ms`,
      `交互流畅度: ${interactionTest.isSmooth ? '✅ 流畅' : '❌ 存在卡顿'}`,
      '',
      '## 防抖统计',
      JSON.stringify(this.getDebounceStats(), null, 2),
      '',
      '## 问题列表',
      ...interactionTest.issues.map(issue => `- ${issue}`),
      '',
      '## 优化建议',
      metrics.frameRate < 30 ? '- 帧率过低，需要进一步优化渲染性能' : '',
      metrics.droppedFrames / metrics.totalFrames > 0.1 ? '- 掉帧率过高，需要优化重绘策略' : '',
      interactionTest.averageResponseTime > 50 ? '- 响应时间过长，需要优化防抖策略' : '',
      interactionTest.maxResponseTime > 100 ? '- 最大响应时间过长，存在阻塞问题' : '',
      interactionTest.issues.length > 0 ? '- 存在超时问题，需要检查事件处理机制' : ''
    ].filter(line => line !== '').join('\n');

    return report;
  }
}

/**
 * 便捷的测试函数
 */
export async function runPerformanceTest(duration: number = 5000): Promise<string> {
  const tester = new PerformanceTestHelper();

  logger.info(LogModule.PERFORMANCE, '开始性能测试...');

  // 开始监控
  tester.startMonitoring();

  // 等待指定时间
  await new Promise(resolve => setTimeout(resolve, duration));

  // 停止监控并获取指标
  const metrics = tester.stopMonitoring();

  logger.info(LogModule.PERFORMANCE, '开始交互测试...');

  // 运行交互测试
  const interactionTest = await tester.runInteractionTest(5);

  // 生成报告
  const report = tester.generateReport(metrics, interactionTest);

  logger.info(LogModule.PERFORMANCE, '测试完成！');
  logger.info(LogModule.PERFORMANCE, '性能测试报告', { report });

  return report;
}
