/**
 * 文件上传相关的工具函数
 */

// 防抖函数，用于优化频繁的进度更新
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 节流函数，用于限制进度更新频率
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 格式化字节数
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化上传速度
export function formatSpeed(bytesPerSecond: number): string {
  return formatBytes(bytesPerSecond) + '/s';
}

// 格式化时间
export function formatTime(seconds: number): string {
  if (seconds === 0 || !isFinite(seconds)) return '--';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

// 验证文件类型
export function validateFileType(fileName: string, allowedTypes: string[]): boolean {
  const lowerFileName = fileName.toLowerCase();
  return allowedTypes.some(type => lowerFileName.endsWith(type.toLowerCase()));
}

// 验证文件大小
export function validateFileSize(fileSize: number, maxSize: number = 5 * 1024 * 1024 * 1024): boolean {
  return fileSize > 0 && fileSize <= maxSize;
}

// 验证哈希格式
export function validateHashFormat(hash: string): { isValid: boolean; type: string } {
  const trimmedHash = hash.trim();
  
  if (/^[a-fA-F0-9]{64}$/.test(trimmedHash)) {
    return { isValid: true, type: 'SHA256' };
  } else if (/^[a-fA-F0-9]{40}$/.test(trimmedHash)) {
    return { isValid: true, type: 'SHA1' };
  } else if (/^[a-fA-F0-9]{32}$/.test(trimmedHash)) {
    return { isValid: true, type: 'MD5' };
  }
  
  return { isValid: false, type: 'UNKNOWN' };
}

// 检查文件名匹配
export function checkFileNameMatch(baseFileName: string, targetFileName: string, extension: string): boolean {
  const baseName = baseFileName.replace(/\.[^/.]+$/, '');
  const targetBaseName = targetFileName.replace(new RegExp(`\\${extension}$`), '');
  return baseName === targetBaseName;
}

// 读取文件内容为文本
export function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('读取文件失败'));
      }
    };
    reader.onerror = () => reject(new Error('读取文件失败'));
    reader.readAsText(file);
  });
}

// 计算上传进度百分比
export function calculateProgress(uploadedBytes: number, totalBytes: number): number {
  if (totalBytes === 0) return 0;
  return Math.round((uploadedBytes / totalBytes) * 100);
}

// 计算上传速度（字节/秒）
export function calculateSpeed(uploadedBytes: number, elapsedTimeSeconds: number): number {
  if (elapsedTimeSeconds === 0) return 0;
  return uploadedBytes / elapsedTimeSeconds;
}

// 计算剩余时间（秒）
export function calculateRemainingTime(remainingBytes: number, speed: number): number {
  if (speed === 0) return 0;
  return remainingBytes / speed;
}

// 生成唯一的上传会话ID
export function generateSessionId(): string {
  return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 检查浏览器是否支持文件API
export function checkFileAPISupport(): boolean {
  return !!(window.File && window.FileReader && window.FileList && window.Blob);
}

// 检查是否支持拖拽上传
export function checkDragDropSupport(): boolean {
  const div = document.createElement('div');
  return ('draggable' in div) || ('ondragstart' in div && 'ondrop' in div);
}

// 文件类型映射
export const FILE_TYPE_MAP = {
  installer: {
    extensions: ['.exe', '.dmg', '.deb', '.rpm', '.tar.gz', '.zip', '.msi', '.pkg', '.appimage'],
    description: '安装包文件',
  },
  signature: {
    extensions: ['.sig'],
    description: '签名文件',
  },
  hash: {
    extensions: ['.hash'],
    description: '哈希文件',
  },
} as const;

// 平台检测
export function detectPlatformFromFileName(fileName: string): 'windows' | 'macos' | 'linux' {
  const lowerName = fileName.toLowerCase();
  
  if (/windows|win|\.exe|\.msi/.test(lowerName)) {
    return 'windows';
  } else if (/macos|mac|osx|\.dmg|\.pkg/.test(lowerName)) {
    return 'macos';
  } else if (/linux|\.deb|\.rpm|\.appimage/.test(lowerName)) {
    return 'linux';
  }
  
  return 'windows'; // 默认
}

// 架构检测
export function detectArchitectureFromFileName(fileName: string): 'x86_64' | 'aarch64' | 'x86' {
  const lowerName = fileName.toLowerCase();
  
  if (/x86_64|x64|amd64/.test(lowerName)) {
    return 'x86_64';
  } else if (/aarch64|arm64/.test(lowerName)) {
    return 'aarch64';
  } else if (/x86|i386|i686/.test(lowerName)) {
    return 'x86';
  }
  
  return 'x86_64'; // 默认
}

// 渠道检测
export function detectChannelFromFileName(fileName: string): 'stable' | 'beta' | 'alpha' {
  const lowerName = fileName.toLowerCase();
  
  if (/alpha/.test(lowerName)) {
    return 'alpha';
  } else if (/beta/.test(lowerName)) {
    return 'beta';
  }
  
  return 'stable'; // 默认
}

// 版本号提取
export function extractVersionFromFileName(fileName: string): string {
  const versionMatch = fileName.match(/(\d+\.\d+\.\d+)/);
  return versionMatch?.[1] || '1.0.0';
}

// 错误类型定义
export enum FileUploadErrorType {
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  FILE_EMPTY = 'FILE_EMPTY',
  READ_ERROR = 'READ_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
}

// 错误消息映射
export const ERROR_MESSAGES = {
  [FileUploadErrorType.FILE_TOO_LARGE]: '文件大小超过限制',
  [FileUploadErrorType.INVALID_FILE_TYPE]: '不支持的文件类型',
  [FileUploadErrorType.FILE_EMPTY]: '文件不能为空',
  [FileUploadErrorType.READ_ERROR]: '读取文件失败',
  [FileUploadErrorType.NETWORK_ERROR]: '网络连接错误',
  [FileUploadErrorType.SERVER_ERROR]: '服务器错误',
  [FileUploadErrorType.VALIDATION_ERROR]: '文件验证失败',
} as const;
