import { API_ENDPOINTS, API_BASE_URL } from '../constants/api-endpoints';
import type { Platform, Architecture, ReleaseChannel } from '../types/platform';

/**
 * URL 构建配置
 */
export interface URLBuilderConfig {
  baseUrl?: string;
  apiVersion?: string;
}

/**
 * URL 构建器类
 */
export class URLBuilder {
  private baseUrl: string;

  constructor(config: URLBuilderConfig = {}) {
    this.baseUrl = config.baseUrl || this.getDefaultBaseUrl();
    // apiVersion is stored but not currently used in URL building
    const _apiVersion = config.apiVersion || 'v1';
    void _apiVersion; // Suppress unused variable warning
  }

  /**
   * 获取默认基础 URL
   */
  private getDefaultBaseUrl(): string {
    const proc = (globalThis as any).process;
    if (typeof proc !== 'undefined' && proc.env['NODE_ENV'] === 'production') {
      return API_BASE_URL.PRODUCTION;
    } else if (typeof proc !== 'undefined' && proc.env['NODE_ENV'] === 'staging') {
      return API_BASE_URL.STAGING;
    } else {
      return API_BASE_URL.DEVELOPMENT;
    }
  }

  /**
   * 构建完整的 API URL
   */
  private buildApiUrl(endpoint: string): string {
    const cleanBaseUrl = this.baseUrl.replace(/\/$/, '');
    const cleanEndpoint = endpoint.replace(/^\//, '');
    return `${cleanBaseUrl}/${cleanEndpoint}`;
  }

  /**
   * 替换 URL 参数
   */
  private replaceUrlParams(url: string, params: Record<string, string>): string {
    let result = url;
    for (const [key, value] of Object.entries(params)) {
      result = result.replace(`:${key}`, encodeURIComponent(value));
    }
    return result;
  }

  /**
   * 添加查询参数
   */
  private addQueryParams(url: string, params: Record<string, string | number | boolean>): string {
    const urlObj = new URL(url);
    for (const [key, value] of Object.entries(params)) {
      urlObj.searchParams.set(key, String(value));
    }
    return urlObj.toString();
  }

  /**
   * 构建版本检查 URL
   */
  buildVersionCheckUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.VERSION_CHECK);
  }

  /**
   * 构建可用版本 URL
   */
  buildAvailableVersionsUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.VERSION_AVAILABLE);
  }

  /**
   * 构建渠道信息 URL
   */
  buildChannelsUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.VERSION_CHANNELS);
  }

  /**
   * 构建最新版本 URL
   */
  buildLatestVersionUrl(channel: ReleaseChannel): string {
    const endpoint = this.replaceUrlParams(API_ENDPOINTS.VERSION_LATEST, { channel });
    return this.buildApiUrl(endpoint);
  }

  /**
   * 构建版本统计 URL
   */
  buildVersionStatsUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.VERSION_STATS);
  }

  /**
   * 构建缓存清除 URL
   */
  buildCacheClearUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.VERSION_CACHE_CLEAR);
  }

  /**
   * 构建文件下载 URL
   */
  buildDownloadUrl(filename: string): string {
    const endpoint = this.replaceUrlParams(API_ENDPOINTS.DOWNLOAD_FILE, { filename });
    return this.buildApiUrl(endpoint);
  }

  /**
   * 构建文件列表 URL
   */
  buildFileListUrl(channel?: ReleaseChannel): string {
    const endpoint = channel 
      ? this.replaceUrlParams(API_ENDPOINTS.DOWNLOAD_LIST, { channel })
      : API_ENDPOINTS.DOWNLOAD_LIST.replace('/:channel?', '');
    return this.buildApiUrl(endpoint);
  }

  /**
   * 构建文件验证 URL
   */
  buildFileVerifyUrl(filename: string): string {
    const endpoint = this.replaceUrlParams(API_ENDPOINTS.DOWNLOAD_VERIFY, { filename });
    return this.buildApiUrl(endpoint);
  }

  /**
   * 构建文件校验和 URL
   */
  buildFileChecksumUrl(filename: string): string {
    const endpoint = this.replaceUrlParams(API_ENDPOINTS.DOWNLOAD_CHECKSUM, { filename });
    return this.buildApiUrl(endpoint);
  }

  /**
   * 构建健康检查 URL
   */
  buildHealthUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.HEALTH);
  }

  /**
   * 构建详细健康检查 URL
   */
  buildDetailedHealthUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.HEALTH_DETAILED);
  }

  /**
   * 构建就绪检查 URL
   */
  buildReadinessUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.HEALTH_READY);
  }

  /**
   * 构建存活检查 URL
   */
  buildLivenessUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.HEALTH_LIVE);
  }

  /**
   * 构建根 URL
   */
  buildRootUrl(): string {
    return this.buildApiUrl(API_ENDPOINTS.ROOT);
  }

  /**
   * 构建带查询参数的 URL
   */
  buildUrlWithQuery(endpoint: string, params: Record<string, any>): string {
    const baseUrl = this.buildApiUrl(endpoint);
    return this.addQueryParams(baseUrl, params);
  }
}

/**
 * 默认 URL 构建器实例
 */
export const urlBuilder = new URLBuilder();

/**
 * 便捷函数：构建版本检查 URL
 */
export function buildVersionCheckUrl(baseUrl?: string): string {
  const builder = baseUrl ? new URLBuilder({ baseUrl }) : urlBuilder;
  return builder.buildVersionCheckUrl();
}

/**
 * 便捷函数：构建下载 URL
 */
export function buildDownloadUrl(filename: string, baseUrl?: string): string {
  const builder = baseUrl ? new URLBuilder({ baseUrl }) : urlBuilder;
  return builder.buildDownloadUrl(filename);
}

/**
 * 便捷函数：构建文件名
 */
export function buildFileName(
  appName: string,
  version: string,
  platform: Platform,
  architecture: Architecture,
  channel?: ReleaseChannel
): string {
  const channelSuffix = channel && channel !== 'stable' ? `-${channel}` : '';
  const extension = getFileExtension(platform);
  
  return `${appName}-${version}${channelSuffix}-${platform}-${architecture}${extension}`;
}

/**
 * 获取平台对应的文件扩展名
 */
export function getFileExtension(platform: Platform): string {
  const extensions: Record<Platform, string> = {
    windows: '.exe',
    macos: '.dmg',
    linux: '.tar.gz',
  };
  
  return extensions[platform] || '.bin';
}

/**
 * 解析文件名获取信息
 */
export function parseFileName(filename: string): {
  appName?: string;
  version?: string;
  platform?: Platform;
  architecture?: Architecture;
  channel?: ReleaseChannel;
} {
  // 移除文件扩展名
  const nameWithoutExt = filename.replace(/\.(exe|dmg|tar\.gz|deb|rpm|appimage)$/, '');
  
  // 尝试解析格式：appname-version-channel-platform-architecture
  const parts = nameWithoutExt.split('-');
  
  if (parts.length < 4) {
    return {};
  }

  const platforms: Platform[] = ['windows', 'macos', 'linux'];
  const architectures: Architecture[] = ['x86_64', 'aarch64', 'x86'];
  const channels: ReleaseChannel[] = ['stable', 'beta', 'alpha'];

  // 从后往前解析
  const architecture = parts[parts.length - 1] as Architecture;
  const platform = parts[parts.length - 2] as Platform;
  
  let channel: ReleaseChannel | undefined;

  // 检查是否有渠道信息
  if (parts.length > 4 && channels.includes(parts[parts.length - 3] as ReleaseChannel)) {
    channel = parts[parts.length - 3] as ReleaseChannel;
  }

  const version = parts[1]; // 假设版本在第二个位置
  const appName = parts[0];

  return {
    appName: platforms.includes(platform) && architectures.includes(architecture) ? appName : undefined,
    version: platforms.includes(platform) && architectures.includes(architecture) ? version : undefined,
    platform: platforms.includes(platform) ? platform : undefined,
    architecture: architectures.includes(architecture) ? architecture : undefined,
    channel,
  };
}
