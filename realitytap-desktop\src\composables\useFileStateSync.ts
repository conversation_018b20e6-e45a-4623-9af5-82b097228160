/**
 * 文件级状态同步管理
 * 确保 WaveformEditor 和 EventAdjustPanel 之间的状态一致性
 */

import { computed, watch, nextTick } from "vue";
import { useFileWaveformEditorStore, validateFileUuid } from "@/stores/haptics-editor-store";
import { debounce } from "@/utils/performance/UnifiedDebounceManager";
import { globalDataFlowManager } from "@/utils/performance/BidirectionalDataFlowManager";
import { logger, LogModule } from "@/utils/logger/logger";

export interface FileStateSyncOptions {
  fileUuid: string | null;
  enableLogging?: boolean;
}

/**
 * 文件级状态同步组合函数
 * 为指定文件提供统一的状态管理接口
 */
export function useFileStateSync(options: FileStateSyncOptions) {
  const { fileUuid, enableLogging = false } = options;

  // 获取文件级 store
  const waveformStore = computed(() => {
    if (!fileUuid) {
      // 当没有选中文件时，返回一个空的store接口
      return createEmptyStoreInterface();
    }

    try {
      const validatedFileUuid = validateFileUuid(fileUuid);
      const store = useFileWaveformEditorStore(validatedFileUuid);

      if (enableLogging) {
        logger.debug(LogModule.PROJECT, "使用文件级别store", { fileUuid: validatedFileUuid });
      }

      return store;
    } catch (error) {
      if (enableLogging) {
        logger.warn(LogModule.PROJECT, "fileUuid验证失败，使用空store", error);
      }
      return createEmptyStoreInterface();
    }
  });

  // 创建空的 store 接口（用于无文件状态）
  function createEmptyStoreInterface() {
    return {
      selectedEvent: null,
      selectedRenderableEvent: null,
      selectedCurvePoint: null,
      $state: { selectedCurvePointIndex: -1 },
      events: [],
      totalDuration: 0,
      currentTotalDuration: 0,
      isTimelineDurationAdjustable: false,
      setAdjustingProperties: () => {},
      updateSelectedEvent: () => {},
      selectEvent: () => {},
      selectCurvePoint: () => {},
      setEvents: () => {},
      deleteSelectedEvent: () => {},
      createNewEvent: () => {},
      increaseTotalDurationByUser: () => {},
      updateBaseDurations: () => {},
      // 历史管理相关方法
      getHistorySystem: () => null,
      initHistorySystem: () => null,
      undo: () => false,
      redo: () => false,
      canUndo: () => false,
      canRedo: () => false,
      destroyHistorySystem: () => {},
    };
  }

  // 状态监听和同步
  const setupStateSync = () => {
    if (!fileUuid) return;

    // 监听选中事件变化
    watch(
      () => waveformStore.value.selectedEvent,
      (newEvent, oldEvent) => {
        if (enableLogging && newEvent !== oldEvent) {
          logger.debug(LogModule.PROJECT, "选中事件变化", {
            fileUuid,
            from: oldEvent?.Type || "null",
            to: newEvent?.Type || "null",
          });
        }
      },
      { deep: true }
    );

    // 监听曲线点选择变化
    watch(
      () => waveformStore.value.$state.selectedCurvePointIndex,
      (newIndex, oldIndex) => {
        if (enableLogging && newIndex !== oldIndex) {
          logger.debug(LogModule.PROJECT, "曲线点选择变化", {
            fileUuid,
            from: oldIndex,
            to: newIndex,
          });
        }
      }
    );

    // 监听事件列表变化
    watch(
      () => waveformStore.value.events,
      (newEvents, oldEvents) => {
        if (enableLogging && newEvents.length !== oldEvents?.length) {
          logger.debug(LogModule.PROJECT, "事件列表变化", {
            fileUuid,
            from: oldEvents?.length || 0,
            to: newEvents.length,
          });
        }
      },
      { deep: true }
    );
  };

  // 提供统一的操作接口
  const stateOperations = computed(() => ({
    // 事件选择操作
    selectEvent: (eventId: string | null) => {
      waveformStore.value.selectEvent(eventId);
      if (enableLogging) {
        logger.debug(LogModule.PROJECT, "选择事件", { fileUuid, eventId });
      }
    },

    // 曲线点选择操作
    selectCurvePoint: (index: number) => {
      waveformStore.value.selectCurvePoint(index);
      if (enableLogging) {
        logger.debug(LogModule.PROJECT, "选择曲线点", { fileUuid, index });
      }
    },

    // 更新事件属性
    updateEvent: async (eventDetail: any) => {
      waveformStore.value.setAdjustingProperties(true);
      waveformStore.value.updateSelectedEvent(eventDetail);

      // 使用统一防抖管理器来协调状态重置，确保与其他组件同步
      debounce(
        `state-reset-${fileUuid}`,
        async () => {
          // 等待下一个 tick 确保所有响应式更新完成
          await nextTick();
          waveformStore.value.setAdjustingProperties(false);
        },
        16, // 使用16ms延迟，与60fps保持一致
        'high' // 状态重置是高优先级操作
      );

      if (enableLogging) {
        logger.debug(LogModule.PROJECT, "更新事件属性", { fileUuid, eventDetail });
      }
    },

    // 创建新事件
    createEvent: (
      eventType: "transient" | "continuous",
      startTime: number,
      options?: { intensity?: number; frequency?: number; duration?: number; availableSpace?: number }
    ) => {
      const eventConfig = {
        type: eventType,
        startTime,
        intensity: options?.intensity ?? 50,
        frequency: options?.frequency ?? 50,
        duration: options?.duration ?? 100,
        availableSpace: options?.availableSpace,
      };

      waveformStore.value.createNewEvent(eventConfig);
      if (enableLogging) {
        logger.debug(LogModule.PROJECT, "创建新事件", { fileUuid, eventConfig });
      }
    },

    // 删除选中事件
    deleteSelectedEvent: () => {
      waveformStore.value.deleteSelectedEvent();
      if (enableLogging) {
        logger.debug(LogModule.PROJECT, "删除选中事件", { fileUuid });
      }
    },
  }));

  // 注册数据流处理器
  globalDataFlowManager.registerHandler(`panel-property-${fileUuid}`, async (update) => {
    try {
      if (update.data.eventDetail) {
        waveformStore.value.setAdjustingProperties(true);
        waveformStore.value.updateSelectedEvent(update.data.eventDetail);

        // 使用 nextTick 确保状态同步
        await nextTick();
        waveformStore.value.setAdjustingProperties(false);

        if (enableLogging) {
          logger.debug(LogModule.PROJECT, "处理面板属性更新", { fileUuid, data: update.data });
        }
      }
      return true;
    } catch (error) {
      logger.error(LogModule.PROJECT, "处理面板属性更新失败", error);
      return false;
    }
  });

  // 初始化状态同步
  setupStateSync();

  return {
    // 状态访问
    store: waveformStore,

    // 操作接口
    operations: stateOperations,

    // 便捷访问器
    selectedEvent: computed(() => waveformStore.value.selectedEvent),
    selectedRenderableEvent: computed(() => waveformStore.value.selectedRenderableEvent),
    selectedCurvePoint: computed(() => waveformStore.value.selectedCurvePoint),
    selectedCurvePointIndex: computed(() => waveformStore.value.$state.selectedCurvePointIndex),
    events: computed(() => waveformStore.value.events),
    totalDuration: computed(() => waveformStore.value.totalDuration),
    currentTotalDuration: computed(() => waveformStore.value.currentTotalDuration),
    isTimelineDurationAdjustable: computed(() => waveformStore.value.isTimelineDurationAdjustable),

    // 工具方法
    isValidFile: computed(() => !!fileUuid),
    fileUuid: computed(() => fileUuid),
  };
}

/**
 * 文件级状态同步的类型定义
 */
export type FileStateSyncReturn = ReturnType<typeof useFileStateSync>;
