// 设备管理组合函数

import { ref, computed, onMounted, onUnmounted, watch, readonly } from "vue";
import { useDeviceManagerStore } from "@/stores/device-manager-store";
import { useDeviceTransmissionStore } from "@/stores/device-transmission-store";
import type { DeviceScanOptions, DeviceConnectionConfig, DeviceOperationResult, DeviceListFilter, DeviceListSort } from "@/types/device-types";
import { DeviceStatus } from "@/types/device-types";
import { TransmissionType, TransmissionPriority } from "@/types/device-transmission";
import { validateDeviceConnectionConfig } from "@/utils/device/deviceValidation";
import { deviceLogger } from "@/utils/logger/logger";

export interface UseDeviceManagerOptions {
  autoInitialize?: boolean;
  enableAutoScan?: boolean;
  enableLogging?: boolean;
}

export function useDeviceManager(options: UseDeviceManagerOptions = {}) {
  const { autoInitialize = true, enableAutoScan = true, enableLogging = false } = options;

  // === Stores ===
  const deviceStore = useDeviceManagerStore();
  const transmissionStore = useDeviceTransmissionStore();

  // === 本地状态 ===
  const isInitializing = ref(false);
  const lastError = ref<string | null>(null);
  const filter = ref<DeviceListFilter>({});
  const sort = ref<DeviceListSort>({
    field: "name",
    order: "asc",
  });

  // === 计算属性 ===
  const filteredDevices = computed(() => {
    let devices = deviceStore.deviceList;

    // 应用过滤器
    if (filter.value.types?.length) {
      devices = devices.filter((device) => filter.value.types!.includes(device.type));
    }

    if (filter.value.statuses?.length) {
      devices = devices.filter((device) => filter.value.statuses!.includes(device.status));
    }

    if (filter.value.searchText) {
      const searchText = filter.value.searchText.toLowerCase();
      devices = devices.filter(
        (device) =>
          device.name.toLowerCase().includes(searchText) ||
          device.deviceId.toLowerCase().includes(searchText) ||
          device.metadata.manufacturer?.toLowerCase().includes(searchText) ||
          device.metadata.model?.toLowerCase().includes(searchText)
      );
    }

    if (filter.value.showOnlyConnected) {
      devices = devices.filter((device) => device.status === DeviceStatus.CONNECTED);
    }

    if (filter.value.showOnlyDefault) {
      devices = devices.filter((device) => device.isDefault);
    }

    // 应用排序
    devices.sort((a, b) => {
      const field = sort.value.field;
      const order = sort.value.order;

      let aValue: any = a[field];
      let bValue: any = b[field];

      // 处理时间字段
      if (field === "lastConnected" || field === "createdAt") {
        aValue = aValue ? new Date(aValue).getTime() : 0;
        bValue = bValue ? new Date(bValue).getTime() : 0;
      }

      // 处理字符串字段
      if (typeof aValue === "string" && typeof bValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return order === "asc" ? -1 : 1;
      if (aValue > bValue) return order === "asc" ? 1 : -1;
      return 0;
    });

    return devices;
  });

  const connectedDeviceCount = computed(() => deviceStore.connectedDevices.length);
  const totalDeviceCount = computed(() => deviceStore.deviceList.length);
  const hasDevices = computed(() => totalDeviceCount.value > 0);
  const hasConnectedDevices = computed(() => connectedDeviceCount.value > 0);
  const defaultDevice = computed(() => deviceStore.defaultDevice);

  // === 设备管理操作 ===

  /**
   * 初始化设备管理器
   */
  const initialize = async (): Promise<void> => {
    if (isInitializing.value || deviceStore.isInitialized) return;

    isInitializing.value = true;
    lastError.value = null;

    try {
      if (enableLogging) {
        deviceLogger.info("开始初始化设备管理器");
      }

      await deviceStore.initialize();

      if (enableAutoScan) {
        deviceStore.startAutoScan();
      }

      if (enableLogging) {
        deviceLogger.info("设备管理器初始化完成");
      }
    } catch (error: any) {
      lastError.value = error.message || "初始化失败";
      if (enableLogging) {
        deviceLogger.error("初始化失败", error);
      }
      throw error;
    } finally {
      isInitializing.value = false;
    }
  };

  /**
   * 扫描设备
   */
  const scanDevices = async (options?: DeviceScanOptions): Promise<DeviceOperationResult> => {
    lastError.value = null;

    try {
      if (enableLogging) {
        deviceLogger.info("开始扫描设备", options);
      }

      const result = await deviceStore.scanDevices(options);

      if (enableLogging) {
        deviceLogger.info("设备扫描完成", result);
      }

      return result;
    } catch (error: any) {
      lastError.value = error.message || "扫描失败";
      if (enableLogging) {
        deviceLogger.error("扫描失败", error);
      }
      throw error;
    }
  };

  /**
   * 连接设备
   */
  const connectDevice = async (deviceId: string, config?: DeviceConnectionConfig): Promise<DeviceOperationResult> => {
    lastError.value = null;

    try {
      // 验证连接配置
      if (config) {
        const validation = validateDeviceConnectionConfig(config);
        if (!validation.isValid) {
          throw new Error(validation.errors.join("; "));
        }
      }

      if (enableLogging) {
        deviceLogger.info("开始连接设备", { deviceId, config });
      }

      const result = await deviceStore.connectDevice(deviceId, config);

      if (enableLogging) {
        deviceLogger.info("设备连接结果", result);
      }

      return result;
    } catch (error: any) {
      lastError.value = error.message || "连接失败";
      if (enableLogging) {
        deviceLogger.error("连接失败", error);
      }
      throw error;
    }
  };

  /**
   * 断开设备连接
   */
  const disconnectDevice = async (deviceId: string): Promise<DeviceOperationResult> => {
    lastError.value = null;

    try {
      if (enableLogging) {
        deviceLogger.info("开始断开设备", { deviceId });
      }

      const result = await deviceStore.disconnectDevice(deviceId);

      if (enableLogging) {
        deviceLogger.info("设备断开结果", result);
      }

      return result;
    } catch (error: any) {
      lastError.value = error.message || "断开失败";
      if (enableLogging) {
        deviceLogger.error("断开失败", error);
      }
      throw error;
    }
  };

  /**
   * 设置默认设备
   */
  const setDefaultDevice = async (deviceId: string | null): Promise<DeviceOperationResult> => {
    lastError.value = null;

    try {
      if (enableLogging) {
        deviceLogger.info("设置默认设备", { deviceId });
      }

      const result = await deviceStore.setDefaultDevice(deviceId);

      if (enableLogging) {
        deviceLogger.info("默认设备设置结果", result);
      }

      return result;
    } catch (error: any) {
      lastError.value = error.message || "设置默认设备失败";
      if (enableLogging) {
        deviceLogger.error("设置默认设备失败", error);
      }
      throw error;
    }
  };

  /**
   * 删除设备
   */
  const removeDevice = async (deviceId: string): Promise<boolean> => {
    lastError.value = null;

    try {
      // 先断开连接
      const device = deviceStore.devices.get(deviceId);
      if (device && device.status === DeviceStatus.CONNECTED) {
        await disconnectDevice(deviceId);
      }

      if (enableLogging) {
        deviceLogger.info("删除设备", { deviceId });
      }

      const result = deviceStore.removeDevice(deviceId);

      if (enableLogging) {
        deviceLogger.info("设备删除结果", { deviceId, result });
      }

      return result;
    } catch (error: any) {
      lastError.value = error.message || "删除设备失败";
      if (enableLogging) {
        deviceLogger.error("删除设备失败", error);
      }
      return false;
    }
  };

  /**
   * 重命名设备
   */
  const renameDevice = async (deviceId: string, newName: string): Promise<boolean> => {
    lastError.value = null;

    try {
      if (enableLogging) {
        deviceLogger.info("重命名设备", { deviceId, newName });
      }

      const result = deviceStore.updateDevice(deviceId, {
        name: newName,
        updatedAt: new Date().toISOString(),
      });

      if (enableLogging) {
        deviceLogger.info("设备重命名结果", { deviceId, newName, result });
      }

      return result;
    } catch (error: any) {
      lastError.value = error.message || "重命名设备失败";
      if (enableLogging) {
        deviceLogger.error("重命名设备失败", error);
      }
      return false;
    }
  };

  /**
   * 添加设备
   */
  const addDevice = (device: any): void => {
    lastError.value = null;

    try {
      if (enableLogging) {
        deviceLogger.info("添加设备", device);
      }

      deviceStore.addDevice(device);

      if (enableLogging) {
        deviceLogger.info("设备添加成功");
      }
    } catch (error: any) {
      lastError.value = error.message || "添加设备失败";
      if (enableLogging) {
        deviceLogger.error("添加设备失败", error);
      }
      throw error;
    }
  };

  // === 数据传输操作 ===

  /**
   * 发送文件到设备
   */
  const sendFileToDevice = async (
    deviceId: string,
    filePath: string,
    fileName: string,
    fileSize: number,
    type: TransmissionType = TransmissionType.HE_FILE,
    priority: TransmissionPriority = TransmissionPriority.NORMAL
  ): Promise<string> => {
    lastError.value = null;

    try {
      // 检查设备是否连接
      const device = deviceStore.devices.get(deviceId);
      if (!device) {
        throw new Error("设备不存在");
      }
      if (device.status !== DeviceStatus.CONNECTED) {
        throw new Error("设备未连接");
      }

      if (enableLogging) {
        deviceLogger.info("开始发送文件", {
          deviceId,
          filePath,
          fileName,
          fileSize,
          type,
          priority,
        });
      }

      // 创建传输任务
      const task = transmissionStore.createTask(deviceId, type, filePath, fileName, fileSize, priority);

      // 启动传输
      await transmissionStore.startTask(task.taskId);

      if (enableLogging) {
        deviceLogger.info("文件发送任务创建", { taskId: task.taskId });
      }

      return task.taskId;
    } catch (error: any) {
      lastError.value = error.message || "发送文件失败";
      if (enableLogging) {
        deviceLogger.error("发送文件失败", error);
      }
      throw error;
    }
  };

  /**
   * 发送文件到默认设备
   */
  const sendFileToDefaultDevice = async (
    filePath: string,
    fileName: string,
    fileSize: number,
    type: TransmissionType = TransmissionType.HE_FILE,
    priority: TransmissionPriority = TransmissionPriority.NORMAL
  ): Promise<string> => {
    if (!defaultDevice.value) {
      throw new Error("未设置默认设备");
    }

    return sendFileToDevice(defaultDevice.value.deviceId, filePath, fileName, fileSize, type, priority);
  };

  // === 过滤和排序 ===

  /**
   * 设置设备列表过滤器
   */
  const setFilter = (newFilter: Partial<DeviceListFilter>): void => {
    filter.value = { ...filter.value, ...newFilter };

    if (enableLogging) {
      deviceLogger.info("设置过滤器", filter.value);
    }
  };

  /**
   * 清除过滤器
   */
  const clearFilter = (): void => {
    filter.value = {};

    if (enableLogging) {
      deviceLogger.info("清除过滤器");
    }
  };

  /**
   * 设置排序选项
   */
  const setSort = (newSort: DeviceListSort): void => {
    sort.value = newSort;

    if (enableLogging) {
      deviceLogger.info("设置排序", sort.value);
    }
  };

  /**
   * 清除错误状态
   */
  const clearError = (): void => {
    lastError.value = null;

    if (enableLogging) {
      deviceLogger.info("清除错误状态");
    }
  };

  // === 生命周期管理 ===
  onMounted(async () => {
    if (autoInitialize) {
      try {
        await initialize();
      } catch (error) {
        deviceLogger.error("设备管理器自动初始化失败", error);
      }
    }
  });

  onUnmounted(() => {
    if (enableLogging) {
      deviceLogger.info("组件卸载，清理资源");
    }

    // 清理资源
    deviceStore.stopAutoScan();
  });

  // === 监听器 ===
  if (enableLogging) {
    watch(
      () => deviceStore.deviceList.length,
      (newCount, oldCount) => {
        deviceLogger.info("设备数量变化", { from: oldCount, to: newCount });
      }
    );

    watch(
      () => deviceStore.connectedDevices.length,
      (newCount, oldCount) => {
        deviceLogger.info("连接设备数量变化", { from: oldCount, to: newCount });
      }
    );
  }

  return {
    // 状态
    isInitializing: readonly(isInitializing),
    lastError: readonly(lastError),
    filter: readonly(filter),
    sort: readonly(sort),

    // 计算属性
    devices: filteredDevices,
    allDevices: deviceStore.deviceList,
    connectedDevices: deviceStore.connectedDevices,
    disconnectedDevices: deviceStore.disconnectedDevices,
    devicesByType: deviceStore.devicesByType,
    statistics: deviceStore.statistics,
    connectedDeviceCount,
    totalDeviceCount,
    hasDevices,
    hasConnectedDevices,
    defaultDevice,

    // Store状态
    isScanning: deviceStore.isScanning,
    isInitialized: deviceStore.isInitialized,
    lastScanTime: deviceStore.lastScanTime,

    // 操作方法
    initialize,
    scanDevices,
    connectDevice,
    disconnectDevice,
    setDefaultDevice,
    addDevice,
    removeDevice,
    renameDevice,
    sendFileToDevice,
    sendFileToDefaultDevice,
    setFilter,
    clearFilter,
    setSort,
    clearError,
  };
}
