import { config } from '@/config/server.config';
import { ConfigChangeLog, SystemConfig } from '@/types/server.types';
import { ConfigUtil } from '@/utils/config.util';
import { FileUtil } from '@/utils/file.util';
import { logger } from '@/utils/logger.util';
import path from 'path';

/**
 * 配置服务类
 * 负责处理系统配置相关的业务逻辑
 */
class ConfigService {
  private readonly configBasePath: string;
  private readonly configFilePath: string;
  private readonly changeLogPath: string;
  private cachedConfig: SystemConfig | null = null;
  private cacheTimestamp: number = 0;
  private readonly cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

  constructor() {
    this.configBasePath = path.join(config.storage.basePath, 'config');
    this.configFilePath = path.join(this.configBasePath, 'server-config.json');
    this.changeLogPath = path.join(this.configBasePath, 'config-changes.log');
  }

  /**
   * 初始化配置存储目录
   */
  async initializeStorage(): Promise<void> {
    try {
      await FileUtil.ensureDir(this.configBasePath);
      
      // 创建默认配置文件
      if (!(await FileUtil.exists(this.configFilePath))) {
        const defaultConfig = ConfigUtil.getDefaultConfig();
        await FileUtil.writeJSONAtomic(this.configFilePath, defaultConfig);
        
        // 记录初始化日志
        const changeLog = ConfigUtil.createChangeLog(
          [{ field: 'system', oldValue: null, newValue: 'initialized' }],
          'system',
          'update',
          'Initial configuration setup'
        );
        await this.logConfigChange(changeLog);
      }
      
      logger.info('Config storage initialized');
    } catch (error) {
      logger.error('Failed to initialize config storage', { error });
      throw new Error(`Failed to initialize config storage: ${error}`);
    }
  }

  /**
   * 获取当前系统配置
   * @param useCache 是否使用缓存
   * @returns 系统配置
   */
  async getConfig(useCache: boolean = true): Promise<SystemConfig> {
    try {
      // 检查缓存
      if (useCache && this.cachedConfig && (Date.now() - this.cacheTimestamp) < this.cacheTimeout) {
        return this.cachedConfig;
      }

      let systemConfig: SystemConfig;

      if (await FileUtil.exists(this.configFilePath)) {
        systemConfig = await FileUtil.readJSON<SystemConfig>(this.configFilePath);
        
        // 验证配置
        const validation = ConfigUtil.validateConfig(systemConfig);
        if (!validation.isValid) {
          logger.warn('Invalid config found, using default config', { errors: validation.errors });
          systemConfig = ConfigUtil.getDefaultConfig();
          await this.saveConfig(systemConfig, 'system', 'Invalid config detected, reset to default');
        }
      } else {
        systemConfig = ConfigUtil.getDefaultConfig();
        await this.saveConfig(systemConfig, 'system', 'Config file not found, created default');
      }

      // 更新缓存
      this.cachedConfig = systemConfig;
      this.cacheTimestamp = Date.now();

      return systemConfig;
    } catch (error) {
      logger.error('Failed to get config', { error });
      throw new Error(`Failed to get config: ${error}`);
    }
  }

  /**
   * 更新系统配置
   * @param newConfig 新配置
   * @param userId 用户ID
   * @param reason 变更原因
   * @returns 更新后的配置
   */
  async updateConfig(newConfig: Partial<SystemConfig>, userId?: string, reason?: string): Promise<SystemConfig> {
    try {
      // 获取当前配置
      const currentConfig = await this.getConfig(false);
      
      // 合并配置
      const mergedConfig = this.mergeConfig(currentConfig, newConfig);
      
      // 验证新配置
      const validation = ConfigUtil.validateConfig(mergedConfig);
      if (!validation.isValid) {
        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
      }

      // 比较配置变更
      const changes = ConfigUtil.compareConfigs(currentConfig, mergedConfig);
      if (changes.length === 0) {
        logger.info('No configuration changes detected');
        return currentConfig;
      }

      // 保存配置
      await this.saveConfig(mergedConfig, userId, reason);

      // 记录变更日志
      const changeLog = ConfigUtil.createChangeLog(changes, userId, 'update', reason);
      await this.logConfigChange(changeLog);

      logger.info('Configuration updated', {
        userId,
        changesCount: changes.length,
        reason,
      });

      return mergedConfig;
    } catch (error) {
      logger.error('Failed to update config', { error, userId, reason });
      throw new Error(`Failed to update config: ${error}`);
    }
  }

  /**
   * 重置配置到默认值
   * @param userId 用户ID
   * @param reason 重置原因
   * @returns 默认配置
   */
  async resetConfig(userId?: string, reason?: string): Promise<SystemConfig> {
    try {
      const currentConfig = await this.getConfig(false);
      const defaultConfig = ConfigUtil.getDefaultConfig();
      
      // 比较配置变更
      const changes = ConfigUtil.compareConfigs(currentConfig, defaultConfig);
      
      // 保存默认配置
      await this.saveConfig(defaultConfig, userId, reason);

      // 记录重置日志
      const changeLog = ConfigUtil.createChangeLog(changes, userId, 'reset', reason);
      await this.logConfigChange(changeLog);

      logger.info('Configuration reset to default', {
        userId,
        changesCount: changes.length,
        reason,
      });

      return defaultConfig;
    } catch (error) {
      logger.error('Failed to reset config', { error, userId, reason });
      throw new Error(`Failed to reset config: ${error}`);
    }
  }

  /**
   * 获取配置模式（用于前端验证）
   * @returns 配置模式
   */
  getConfigSchema(): any {
    return ConfigUtil.getConfigSchema();
  }

  /**
   * 获取默认配置
   * @returns 默认配置
   */
  getDefaultConfig(): SystemConfig {
    return ConfigUtil.getDefaultConfig();
  }

  /**
   * 获取配置变更历史
   * @param limit 限制数量
   * @returns 变更历史
   */
  async getConfigHistory(limit: number = 100): Promise<ConfigChangeLog[]> {
    try {
      if (!(await FileUtil.exists(this.changeLogPath))) {
        return [];
      }

      const logContent = await FileUtil.readFile(this.changeLogPath);
      const lines = logContent.split('\n').filter(line => line.trim());
      
      const changeLogs: ConfigChangeLog[] = [];
      
      for (const line of lines.slice(-limit)) {
        try {
          const changeLog = JSON.parse(line);
          changeLogs.push(changeLog);
        } catch (error) {
          logger.warn('Failed to parse config change log line', { line, error });
        }
      }
      
      return changeLogs.reverse(); // 最新的在前面
    } catch (error) {
      logger.error('Failed to get config history', { error });
      throw new Error(`Failed to get config history: ${error}`);
    }
  }

  /**
   * 验证配置
   * @param config 配置对象
   * @returns 验证结果
   */
  async validateConfig(config: any): Promise<{ isValid: boolean; errors: string[] }> {
    return ConfigUtil.validateConfig(config);
  }

  /**
   * 获取单个配置值
   * @param key 配置键
   * @param defaultValue 默认值
   * @returns 配置值
   */
  async getConfigValue<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    try {
      const config = await this.getConfig();
      const keys = key.split('.');
      let value: any = config;

      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          return defaultValue;
        }
      }

      return value !== undefined ? value : defaultValue;
    } catch (error) {
      logger.error('Failed to get config value', { key, error });
      return defaultValue;
    }
  }

  /**
   * 设置单个配置值
   * @param key 配置键
   * @param value 配置值
   * @param userId 用户ID
   * @param reason 变更原因
   */
  async setConfigValue(key: string, value: any, userId?: string, reason?: string): Promise<void> {
    try {
      const currentConfig = await this.getConfig(false);
      const keys = key.split('.');
      const newConfig = JSON.parse(JSON.stringify(currentConfig)); // 深拷贝

      let current: any = newConfig;
      for (let i = 0; i < keys.length - 1; i++) {
        const k = keys[i];
        if (!k) {
          throw new Error(`Invalid key at index ${i} in path: ${key}`);
        }
        if (!(k in current) || typeof current[k] !== 'object') {
          current[k] = {};
        }
        current = current[k];
      }

      const lastKey = keys[keys.length - 1];
      if (!lastKey) {
        throw new Error(`Invalid last key in path: ${key}`);
      }
      current[lastKey] = value;

      await this.updateConfig(newConfig, userId, reason);
    } catch (error) {
      logger.error('Failed to set config value', { key, value, error });
      throw new Error(`Failed to set config value: ${error}`);
    }
  }

  /**
   * 清除配置缓存
   */
  clearCache(): void {
    this.cachedConfig = null;
    this.cacheTimestamp = 0;
    logger.debug('Config cache cleared');
  }

  /**
   * 保存配置到文件
   * @param config 配置对象
   * @param userId 用户ID
   * @param reason 保存原因
   */
  private async saveConfig(config: SystemConfig, userId?: string, reason?: string): Promise<void> {
    try {
      await FileUtil.writeJSONAtomic(this.configFilePath, config);
      
      // 清除缓存
      this.clearCache();
      
      logger.debug('Config saved to file', { userId, reason });
    } catch (error) {
      logger.error('Failed to save config to file', { error, userId, reason });
      throw new Error(`Failed to save config: ${error}`);
    }
  }

  /**
   * 记录配置变更日志
   * @param changeLog 变更日志
   */
  private async logConfigChange(changeLog: ConfigChangeLog): Promise<void> {
    try {
      const logLine = JSON.stringify(changeLog) + '\n';
      await FileUtil.appendFile(this.changeLogPath, logLine);
      
      // 同时记录到应用日志
      logger.info('Config change logged', {
        changeId: changeLog.id,
        userId: changeLog.userId,
        action: changeLog.action,
        changesCount: changeLog.changes.length,
      });
    } catch (error) {
      logger.error('Failed to log config change', { error, changeLog });
      // 不抛出错误，避免影响配置更新
    }
  }

  /**
   * 合并配置对象
   * @param currentConfig 当前配置
   * @param newConfig 新配置
   * @returns 合并后的配置
   */
  private mergeConfig(currentConfig: SystemConfig, newConfig: Partial<SystemConfig>): SystemConfig {
    const merged = JSON.parse(JSON.stringify(currentConfig)); // 深拷贝
    
    // 递归合并配置
    this.deepMerge(merged, newConfig);
    
    return merged;
  }

  /**
   * 深度合并对象
   * @param target 目标对象
   * @param source 源对象
   */
  private deepMerge(target: any, source: any): void {
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          if (!target[key] || typeof target[key] !== 'object') {
            target[key] = {};
          }
          this.deepMerge(target[key], source[key]);
        } else {
          target[key] = source[key];
        }
      }
    }
  }
}

export const configService = new ConfigService();
