/**
 * librtcore 全局配置管理工具
 * 提供默认配置获取和配置验证功能
 */

import { computed } from "vue";
import { useAppConfig } from "@/composables/useAppConfig";
import { HAPTIC_CONSTANTS } from "@/utils/api/haptic-api";
import { SamplingRateType } from "@/types/haptic-types";
import type { MotorModel } from "@/types/play-effect-dialog";
import { DEFAULT_MOTOR_MODELS } from "@/types/play-effect-dialog";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 默认配置接口
 */
export interface LibrtcoreDefaultConfig {
  motor: MotorModel | null;
  samplingRate: SamplingRateType;
  hasValidConfig: boolean;
}

/**
 * 全局配置管理器
 */
export function useLibrtcoreGlobalConfig() {
  const { playEffectDialogConfig } = useAppConfig();

  /**
   * 获取默认马达配置
   */
  const getDefaultMotor = (): MotorModel | null => {
    // 1. 尝试从用户配置获取
    const lastSelectedMotorId = playEffectDialogConfig.value?.lastSelectedMotorId;
    if (lastSelectedMotorId) {
      const motor = DEFAULT_MOTOR_MODELS.find(m => m.id === lastSelectedMotorId);
      if (motor) {
        logger.debug(LogModule.GENERAL, "使用用户配置的默认马达", { motorId: lastSelectedMotorId });
        return motor;
      }
    }

    // 2. 使用第一个可用的马达作为默认值
    if (DEFAULT_MOTOR_MODELS.length > 0) {
      const defaultMotor = DEFAULT_MOTOR_MODELS[0];
      logger.debug(LogModule.GENERAL, "使用系统默认马达", { motorId: defaultMotor.id });
      return defaultMotor;
    }

    logger.warn(LogModule.GENERAL, "没有可用的默认马达配置");
    return null;
  };

  /**
   * 获取默认采样率
   */
  const getDefaultSamplingRate = (): SamplingRateType => {
    // 1. 尝试从用户配置获取
    const lastSelectedSamplingRate = playEffectDialogConfig.value?.lastSelectedSamplingRate;
    if (lastSelectedSamplingRate) {
      logger.debug(LogModule.GENERAL, "使用用户配置的默认采样率", { samplingRate: lastSelectedSamplingRate });
      return lastSelectedSamplingRate;
    }

    // 2. 使用系统默认采样率
    const defaultSamplingRate = HAPTIC_CONSTANTS.DEFAULT_SAMPLING_RATE;
    logger.debug(LogModule.GENERAL, "使用系统默认采样率", { samplingRate: defaultSamplingRate });
    return defaultSamplingRate;
  };

  /**
   * 获取默认配置
   */
  const getDefaultConfig = (): LibrtcoreDefaultConfig => {
    const motor = getDefaultMotor();
    const samplingRate = getDefaultSamplingRate();
    const hasValidConfig = motor !== null;

    return {
      motor,
      samplingRate,
      hasValidConfig,
    };
  };

  /**
   * 验证马达配置
   */
  const validateMotor = (motor: MotorModel): boolean => {
    if (!motor) return false;
    if (!motor.id || !motor.name || !motor.configPath) return false;
    if (typeof motor.resonantFreq !== 'number' || motor.resonantFreq <= 0) return false;
    return true;
  };

  /**
   * 验证采样率配置
   */
  const validateSamplingRate = (samplingRate: SamplingRateType): boolean => {
    return Object.values(SamplingRateType).includes(samplingRate);
  };

  /**
   * 验证完整配置
   */
  const validateConfig = (motor: MotorModel, samplingRate: SamplingRateType): boolean => {
    return validateMotor(motor) && validateSamplingRate(samplingRate);
  };

  /**
   * 获取可用马达列表
   */
  const getAvailableMotors = (): MotorModel[] => {
    return DEFAULT_MOTOR_MODELS.filter(validateMotor);
  };

  /**
   * 获取可用采样率列表
   */
  const getAvailableSamplingRates = (): SamplingRateType[] => {
    return Object.values(SamplingRateType) as SamplingRateType[];
  };

  /**
   * 查找马达配置
   */
  const findMotorById = (motorId: string): MotorModel | null => {
    return DEFAULT_MOTOR_MODELS.find(m => m.id === motorId) || null;
  };

  /**
   * 检查是否有有效的默认配置
   */
  const hasValidDefaultConfig = computed(() => {
    const config = getDefaultConfig();
    return config.hasValidConfig;
  });

  /**
   * 响应式的默认配置
   */
  const defaultConfig = computed(() => getDefaultConfig());

  return {
    // 配置获取
    getDefaultMotor,
    getDefaultSamplingRate,
    getDefaultConfig,
    
    // 配置验证
    validateMotor,
    validateSamplingRate,
    validateConfig,
    
    // 配置查询
    getAvailableMotors,
    getAvailableSamplingRates,
    findMotorById,
    
    // 响应式状态
    hasValidDefaultConfig,
    defaultConfig,
  };
}

/**
 * 全局配置常量
 */
export const LIBRTCORE_CONFIG = {
  // 后台初始化延迟时间（毫秒）
  BACKGROUND_INIT_DELAY: 2000,
  
  // 配置变化防抖时间（毫秒）
  CONFIG_CHANGE_DEBOUNCE: 300,
  
  // 重试延迟时间（毫秒）
  RETRY_DELAY: 1000,
  
  // 最大重试次数
  MAX_RETRY_COUNT: 3,
  
  // 错误显示时间（毫秒）
  ERROR_DISPLAY_DURATION: 5000,
} as const;
