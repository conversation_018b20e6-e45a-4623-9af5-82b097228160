/**
 * @file realitytap_interfaces.h
 * @brief RealityTap触觉反馈库 - 核心接口定义
 *
 * 该头文件定义了RealityTap触觉反馈库的核心接口，包括：
 * - 触觉输出处理器接口 (IHapticOutputHandler)
 * - 采样率类型枚举 (SamplingRateType)
 * - 波形处理状态枚举 (WaveformProcessingStatus)
 * - 触觉执行器参数结构体 (HapticActuatorParams)
 *
 * ## 设计目标
 * - **硬件抽象**：提供统一的触觉设备访问接口
 * - **标准化协议**：定义标准的触觉数据传输协议
 * - **模块化设计**：支持不同输出处理器的扩展
 * - **跨平台支持**：确保在不同平台的兼容性
 *
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 * @copyright Copyright (c) 2025 AWA Technology
 *
 * @note 该文件遵循Google C++编程规范
 * @note 接口设计考虑了低延迟要求
 */

#ifndef AWA_REALITYTAP_INTERFACES_H
#define AWA_REALITYTAP_INTERFACES_H

#include <cstdint>

// ========== API 导出宏定义 ==========

#if defined(_MSC_VER)
    #define AWA_API_EXPORT __declspec(dllexport)
    #define AWA_API_IMPORT __declspec(dllimport)
#elif defined(__GNUC__)
    #define AWA_API_EXPORT __attribute__((visibility("default")))
    #define AWA_API_IMPORT
#else
    #define AWA_API_EXPORT
    #define AWA_API_IMPORT
#endif

#ifdef BUILDING_REALITYTAP_LIB
    #define AWA_API AWA_API_EXPORT
#else
    #define AWA_API AWA_API_IMPORT
#endif

/**
 * @enum SamplingRateType
 * @brief 线性马达驱动芯片采样频率类型枚举
 *
 * 定义了线性马达驱动芯片支持的采样频率类型。驱动芯片采样频率影响：
 * - **信号质量**：更高的采样率提供更精确的触觉效果
 * - **系统性能**：更高的采样率需要更多的计算资源
 * - **功耗控制**：更高的采样率会增加功耗
 *
 * ## 采样率选择指南
 * - **SAMPLING_6KHZ**：适用于基础触觉效果，低功耗应用
 * - **SAMPLING_8KHZ**：通用选择，平衡性能和质量
 * - **SAMPLING_12KHZ**：适用于中等精度的触觉效果
 * - **SAMPLING_24KHZ**：适用于高精度触觉效果，专业应用
 *
 * @note 具体的采样率值由驱动芯片内部定义
 */
enum SamplingRateType {
    SAMPLING_6KHZ = 4,   ///< 6KHz驱动芯片采样率，适用于基础振动效果
    SAMPLING_8KHZ = 5,   ///< 8KHz驱动芯片采样率，通用平衡选择
    SAMPLING_12KHZ = 6,  ///< 12KHz驱动芯片采样率，中等精度效果
    SAMPLING_24KHZ = 8   ///< 24KHz驱动芯片采样率，高精度触觉效果
};

/**
 * @enum WaveformProcessingStatus
 * @brief 波形处理状态枚举
 *
 * 定义了波形处理过程中的状态，主要用于waitChunkProcessingComplete()的返回值：
 * - **状态监控**：跟踪处理进度
 * - **错误处理**：识别异常情况（特别是超时错误）
 * - **流程控制**：协调核心库与调用者之间的处理流程
 *
 * ## 状态说明
 * - **PROCESSING_READY**：调用者处理完成，核心库可以继续
 * - **PROCESSING_ACTIVE**：正在处理波形数据（内部状态）
 * - **PROCESSING_STOPPED**：处理已完成或停止（内部状态）
 * - **PROCESSING_ERROR**：调用者处理超时或发生错误，核心库将触发错误处理
 *
 * ## waitChunkProcessingComplete()返回值
 * - **PROCESSING_READY**：正常情况，表示调用者已完成数据块处理
 * - **PROCESSING_ERROR**：异常情况，表示调用者处理超时，核心库会进入错误处理流程
 */
enum WaveformProcessingStatus {
    PROCESSING_ERROR = 0xFF9,    ///< 处理错误状态（超时），核心库将触发错误处理流程
    PROCESSING_STOPPED = 0xFF4,  ///< 处理已停止，可以开始新的处理任务（内部状态）
    PROCESSING_ACTIVE = 0xFF2,   ///< 正在处理中，系统繁忙状态（内部状态）
    PROCESSING_READY = 0xFF0     ///< 系统就绪，调用者处理完成，核心库可以继续
};

/**
 * @interface IHapticOutputHandler
 * @brief 触觉输出处理器接口
 *
 * 该接口定义了触觉输出处理器的标准协议，用于：
 * - **硬件抽象**：提供统一的硬件访问接口
 * - **数据传输**：处理触觉数据的传输
 * - **事件通知**：接收触觉输出的生命周期事件
 * - **实时控制**：支持振幅调节和同步控制
 *
 * ## 调用时序和方向
 * ```
 * 核心算法库 → 调用者 (通知回调)：
 * 1. onHapticOutputStart()     ← 核心库通知：输出序列开始
 * 2. onWaveformChunkStart()    ← 核心库通知：数据块开始
 * 3. processWaveformSample()   ← 核心库发送：逐个采样点数据 (循环调用)
 *
 * 核心算法库 ← 调用者 (等待响应)：
 * 4. waitChunkProcessingComplete() ← 核心库等待：调用者处理完成确认
 *
 * 核心算法库 → 调用者 (通知回调)：
 * 5. onHapticOutputComplete()  ← 核心库通知：输出序列完成
 *    或 onHapticOutputStop()   ← 核心库通知：输出序列停止
 * ```
 *
 * ## 性能要求
 * - **低延迟**：方法执行时间应尽可能短
 * - **线程安全**：支持多线程并发调用
 * - **实时性**：processWaveformSample()必须支持实时处理
 *
 * @note 实现类必须保证线程安全
 * @note 所有虚函数都是纯虚函数，必须在派生类中实现
 * @warning 实现类不应在回调函数中执行耗时操作
 */
class IHapticOutputHandler {
public:
    /**
     * @brief 触觉输出开始回调 (核心库→调用者通知)
     *
     * 当触觉输出序列开始时，核心算法库主动调用此方法通知调用者，用于：
     * - **初始化输出设备**：准备设备进入工作状态
     * - **分配资源**：分配必要的缓冲区和内存
     * - **重置状态**：清除之前的状态，准备新的输出序列
     *
     * ## 调用方向
     * **核心算法库 → 调用者**：这是一个通知回调，由核心库主动调用
     *
     * @note 该方法在输出序列开始前被核心库调用一次
     * @note 实现应该是非阻塞的，避免影响性能
     * @warning 不应在此方法中执行耗时操作
     */
    virtual void onHapticOutputStart() = 0;

    /**
     * @brief 触觉输出完成回调 (核心库→调用者通知)
     *
     * 当触觉输出序列正常完成时，核心算法库主动调用此方法通知调用者，用于：
     * - **完成数据传输**：确保所有数据都已传输完成
     * - **清理资源**：释放临时分配的资源和缓冲区
     * - **更新状态**：标记输出序列已完成
     *
     * ## 调用方向
     * **核心算法库 → 调用者**：这是一个通知回调，由核心库主动调用
     *
     * @note 该方法在输出序列正常完成后被核心库调用一次
     * @note 与onHapticOutputStop()互斥，只会调用其中一个
     * @see onHapticOutputStop()
     */
    virtual void onHapticOutputComplete() = 0;

    /**
     * @brief 触觉输出停止回调 (核心库→调用者通知)
     *
     * 当触觉输出序列被中断或强制停止时，核心算法库主动调用此方法通知调用者，用于：
     * - **立即停止输出**：停止数据传输
     * - **清理未完成的操作**：取消正在进行的传输
     * - **重置状态**：将设备恢复到空闲状态
     * - **释放资源**：清理所有相关资源
     *
     * ## 调用方向
     * **核心算法库 → 调用者**：这是一个通知回调，由核心库主动调用
     *
     * @note 该方法在输出被中断时被核心库调用
     * @note 与onHapticOutputComplete()互斥，只会调用其中一个
     * @note 实现应该能够安全地处理重复调用
     * @see onHapticOutputComplete()
     */
    virtual void onHapticOutputStop() = 0;

    /**
     * @brief 波形数据块开始回调 (核心库→调用者通知)
     *
     * 当新的波形数据块开始传输时，核心算法库主动调用此方法通知调用者，用于：
     * - **准备接收数据**：准备缓冲区接收新的数据块
     * - **同步标记**：标记数据块的边界
     * - **状态切换**：从空闲状态切换到数据接收状态
     *
     * ## 数据块概念
     * 波形数据以块(chunk)为单位进行传输，每个数据块包含连续的采样点数据。
     *
     * ## 调用方向
     * **核心算法库 → 调用者**：这是一个通知回调，由核心库主动调用
     *
     * @note 该方法在每个数据块开始前被核心库调用
     * @note 调用后会有多次processWaveformSample()调用
     * @see processWaveformSample()
     * @see waitChunkProcessingComplete()
     */
    virtual void onWaveformChunkStart() = 0;

    /**
     * @brief 处理单个波形采样点 (核心库→调用者发送数据)
     * @param sample 8位有符号采样值，范围[-128, 127]
     *
     * 核心算法库通过此方法向调用者按字节发送波形采样点数据，用于：
     * - **实时数据传输**：核心库逐个发送采样点数据给调用者
     * - **格式转换**：调用者将数据转换为硬件需要的格式
     * - **缓冲管理**：调用者将数据存储到输出缓冲区
     *
     * ## 数据格式说明
     * - **数据类型**：8位有符号整数 (char/int8_t)
     * - **数值范围**：[-128, 127]
     * - **物理意义**：触觉强度，0表示静止，正负值表示不同方向
     *
     * ## 调用方向
     * **核心算法库 → 调用者**：这是数据发送接口，核心库按字节发送数据
     *
     * ## 性能要求
     * - **低延迟**：单次调用时间应尽可能短
     * - **高频调用**：可能每秒调用数千到数万次
     * - **内存效率**：避免动态内存分配
     * - **线程安全**：支持多线程并发调用
     *
     * @note 这是性能关键的方法，需要高度优化
     * @note 实现必须是线程安全的
     * @warning 不应在此方法中执行耗时操作
     * @warning 不应在此方法中进行动态内存分配
     */
    virtual void processWaveformSample(char sample) = 0;

    /**
     * @brief 等待数据块处理完成 (核心库←调用者等待响应)
     * @param sampleCount 当前数据块中的采样点数量
     * @return 处理结果状态码，PROCESSING_READY表示成功，PROCESSING_ERROR表示超时错误
     *
     * 核心算法库等待调用者将通过processWaveformSample()发送的数据块处理完成，用于：
     * - **同步控制**：确保调用者完整处理数据块后核心库再继续
     * - **流量控制**：防止核心库数据生产速度超过调用者处理能力
     * - **错误检测**：检查调用者处理过程中的错误和超时
     *
     * ## 调用方向
     * **核心算法库 ← 调用者**：这是等待响应接口，核心库等待调用者确认处理完成
     *
     * ## 超时处理
     * - **超时机制**：调用者实现应该包含超时机制，避免无限等待
     * - **超时后果**：如果调用者处理超时，应返回PROCESSING_ERROR
     * - **核心库行为**：核心库收到PROCESSING_ERROR后会触发错误处理流程
     *
     * @param sampleCount 数据块中的采样点总数，用于验证完整性
     * @return PROCESSING_READY=处理完成，PROCESSING_ERROR=超时或错误
     *
     * @note 该方法在每个数据块结束时被核心库调用一次
     * @note 实现应该包含合理的超时机制
     * @warning 不应无限期阻塞，必须有超时保护
     * @warning 超时后必须返回PROCESSING_ERROR，否则会影响核心库正常运行
     * @see WaveformProcessingStatus
     */
    virtual int32_t waitChunkProcessingComplete(int32_t sampleCount) = 0;

    /**
     * @brief 设置输出振幅
     * @param amplitude 振幅值，范围[0, 100]，表示百分比强度
     *
     * 动态调整输出振幅，用于：
     * - **实时强度控制**：根据用户设置调整强度
     * - **自适应调节**：根据环境或设备状态自动调整
     * - **功耗管理**：在低电量时降低振幅节省电力
     *
     * ## 振幅映射
     * - **0**：无输出
     * - **1-25**：轻微强度
     * - **26-50**：中等强度
     * - **51-75**：较强强度
     * - **76-100**：最强强度
     *
     * @param amplitude 振幅百分比，有效范围[0, 100]
     *
     * @note 该方法可以在输出过程中随时调用
     * @note 设置立即生效，影响后续的采样点输出
     * @note 实现应该验证参数范围并进行必要的限制
     */
    virtual void setOutputAmplitude(uint32_t amplitude) = 0;

    /**
     * @brief 虚析构函数
     *
     * 提供虚析构函数确保派生类对象能够正确析构，遵循C++最佳实践。
     *
     * @note 使用default关键字提供默认实现
     * @note 确保通过基类指针删除派生类对象时的正确行为
     */
    virtual ~IHapticOutputHandler() = default;
};

/**
 * @struct HapticActuatorParams
 * @brief 触觉执行器参数结构体
 *
 * 该结构体包含了初始化触觉执行器所需的所有参数，用于：
 * - **设备标识**：唯一标识触觉设备
 * - **物理参数**：描述设备的物理特性
 * - **配置文件**：指定设备特定的配置文件路径
 * - **采样设置**：定义信号采样率
 * - **输出处理**：指定输出处理器实例
 *
 * ## 使用场景
 * - **系统初始化**：初始化触觉反馈库
 * - **多设备支持**：支持同时管理多个触觉设备
 * - **设备配置**：为每个设备指定特定的参数和配置
 *
 * ## 参数验证
 * 使用该结构体时应注意：
 * - **id唯一性**：确保每个设备的ID唯一
 * - **f0合理性**：谐振频率应在合理范围内
 * - **文件有效性**：配置文件路径必须存在且可读
 * - **处理器有效性**：outputHandler不能为空指针
 *
 * @note 该结构体使用聚合初始化
 * @note 所有指针成员的生命周期必须超过库的使用期间
 * @warning file和outputHandler指针必须在库生命周期内保持有效
 */
struct HapticActuatorParams {
    /**
     * @brief 触觉设备唯一标识符
     *
     * 用于在多设备环境中唯一标识每个触觉设备：
     * - **唯一性**：在同一系统中必须唯一
     * - **索引作用**：用作设备索引
     * - **调试标识**：用于日志记录和错误报告
     *
     * ## 分配策略
     * - **单设备系统**：通常使用0
     * - **多设备系统**：从0开始连续分配
     *
     * @note 在多设备环境中必须确保唯一性
     */
    uint32_t id;

    /**
     * @brief 线性马达实际驱动频率 (Hz)
     *
     * 线性马达在实际应用中被驱动的频率，这是每个马达个体的实际工作参数：
     * - **实际频率**：马达在当前应用场景下的实际驱动频率
     * - **个体差异**：每个线性马达个体的实际驱动频率不一定相同
     * - **动态参数**：可能根据应用需求和马达状态进行调整
     * - **性能影响**：直接影响触觉效果的频率特性和强度
     *
     * ## 常见驱动频率范围
     * - **低频应用**：通常50-150Hz，适用于重击感效果
     * - **中频应用**：通常150-300Hz，适用于一般触觉反馈
     * - **高频应用**：通常300-500Hz，适用于精细触觉效果
     *
     * @unit 赫兹 (Hz)
     * @range 通常 50-500Hz，具体取决于应用场景和马达特性
     * @note 该值反映马达在当前应用中的实际驱动频率
     * @note 不同于设计频率或谐振频率，是实际工作频率
     * @warning 驱动频率应在马达的安全工作范围内
     */
    uint32_t f0;

    /**
     * @brief 配置文件路径
     *
     * 指向设备特定配置文件的路径，配置文件包含设备的各种参数和设置。
     *
     * ## 路径示例
     * - **Android**："/odm/etc/device.config"
     * - **Windows**："./config/device_config.bin"
     * - **相对路径**："../configs/default.config"
     *
     * ## 文件验证
     * 库会验证配置文件：
     * - **存在性检查**：文件是否存在且可读
     * - **格式验证**：文件格式是否正确
     * - **版本兼容性**：配置文件版本是否兼容
     *
     * @note 路径可以是绝对路径或相对路径
     * @note 文件必须在库初始化时可访问
     * @warning 配置文件损坏或不匹配会导致初始化失败
     */
    const char* file;

    /**
     * @brief 驱动芯片采样率类型
     *
     * 指定线性马达驱动芯片的采样率，影响：
     * - **信号质量**：更高采样率提供更精确的效果
     * - **计算负载**：更高采样率需要更多计算资源
     * - **内存使用**：更高采样率需要更多缓冲区
     *
     * ## 选择指南
     * - **SAMPLING_6KHZ**：基础应用，低功耗
     * - **SAMPLING_8KHZ**：通用选择，平衡性能和质量
     * - **SAMPLING_12KHZ**：高质量应用
     * - **SAMPLING_24KHZ**：专业应用，高质量
     *
     * @see SamplingRateType
     * @note 必须与硬件驱动芯片和outputHandler的能力匹配
     */
    SamplingRateType samplingRate;

    /**
     * @brief 输出处理器指针
     *
     * 指向实现了IHapticOutputHandler接口的对象，负责：
     * - **硬件适配**：将输出适配到具体硬件
     * - **数据传输**：将触觉数据传输到设备
     * - **事件处理**：处理触觉输出生命周期事件
     *
     * ## 生命周期管理
     * - **创建时机**：在初始化库之前创建
     * - **生命周期**：必须在库使用期间保持有效
     * - **销毁时机**：在库销毁之后才能销毁
     *
     * ## 线程安全
     * - **并发访问**：outputHandler可能被多个线程访问
     * - **同步保护**：实现类必须提供适当的同步保护
     *
     * @note 指针必须在库生命周期内保持有效
     * @note 实现类必须是线程安全的
     * @warning 不能为空指针，否则初始化会失败
     * @see IHapticOutputHandler
     */
    IHapticOutputHandler* outputHandler;
};

// ========== 公开 API 函数声明 ==========

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @defgroup CoreAPI 核心 API 函数
 * @brief RealityTap 核心库的公开 API 函数
 *
 * 这些函数提供了 RealityTap 触觉反馈库的完整功能，包括：
 * - 初始化和配置
 * - 播放控制
 * - 预定义效果
 * - 队列式播放
 *
 * @{
 */

// ========== 初始化和配置 ==========

/**
 * @brief 初始化 RealityTap 核心库
 * @param params 触觉执行器参数数组
 * @param count 参数数组长度
 * @return 0=成功，负数=错误码
 *
 * 初始化触觉反馈库，配置触觉设备参数。
 * 必须在使用其他 API 之前调用此函数。
 *
 * @note 支持多设备初始化，通过 count 参数指定设备数量
 * @warning params 和 outputHandler 指针必须在库生命周期内保持有效
 */
AWA_API int32_t awa_realitytap_init(HapticActuatorParams *params, ssize_t count);

/**
 * @brief 重新初始化 RealityTap 核心库
 * @param params 新的触觉执行器参数数组
 * @param count 参数数组长度
 * @return 0=成功，负数=错误码
 *
 * 使用新的参数重新初始化库，用于运行时更改配置。
 */
AWA_API int32_t awa_realitytap_reinit(HapticActuatorParams *params, ssize_t count);

/**
 * @brief 设置全局振幅
 * @param amplitude 振幅值 (0-255)
 * @return 0=成功，负数=错误码
 *
 * 设置所有触觉输出的全局振幅强度。
 */
AWA_API int32_t awa_realitytap_set_amplitude(uint32_t amplitude);

/**
 * @brief 立即停止所有振动
 * @return 0=成功，负数=错误码
 *
 * 立即停止当前所有的触觉输出，清空播放队列。
 */
AWA_API int32_t awa_realitytap_stop_vibration();

// ========== 直接播放功能 ==========

/**
 * @brief 播放预定义效果
 * @param effect_id 效果ID
 * @param timeoutMs 超时时间 (毫秒)
 * @return 0=成功，负数=错误码
 *
 * 播放系统预定义的触觉效果。
 */
AWA_API int32_t awa_realitytap_play_effect(int32_t effect_id, int32_t timeoutMs);

/**
 * @brief 播放 RTP 流数据
 * @param fd 文件描述符
 * @param timeoutMs 超时时间指针 (毫秒)
 * @return 0=成功，负数=错误码
 *
 * 播放实时传输协议 (RTP) 格式的触觉数据流。
 */
AWA_API int32_t awa_realitytap_play_rtp(int32_t fd, uint32_t *timeoutMs);

/**
 * @brief 播放包络数据
 * @param data 包络数据
 * @param fast_flag 快速模式标志
 * @return 0=成功，负数=错误码
 *
 * 播放包络格式的触觉数据。
 */
AWA_API int32_t awa_realitytap_play_envelope(const char *data, char fast_flag);

/**
 * @brief 播放触觉数据
 * @param data 触觉数据数组 (HE格式)
 * @param length 数据长度 (元素个数)
 * @return 0=成功，负数=错误码
 *
 * 播放 HE (Haptic Event) 格式的触觉数据。
 */
AWA_API int32_t awa_realitytap_play_haptics(const int32_t *data, uint32_t length);

// ========== 队列式播放功能 ==========

/**
 * @brief 初始化播放队列
 * @return 0=成功，负数=错误码
 *
 * 初始化播放队列系统，必须在使用 append 系列函数之前调用。
 */
AWA_API int32_t awa_realitytap_append_init();

/**
 * @brief 启动播放线程
 * @return 0=成功，负数=错误码
 *
 * 启动后台播放线程，开始处理队列中的播放任务。
 */
AWA_API int32_t awa_realitytap_append_start();

/**
 * @brief 添加包络数据到播放队列
 * @param envelope_data 包络数据
 * @param data_length 数据长度
 * @param fast_mode 快速模式
 * @return 0=成功，负数=错误码
 *
 * 将包络数据添加到播放队列中。
 */
AWA_API int32_t awa_realitytap_append_envelope(const int32_t *envelope_data,
                                               uint32_t data_length, bool fast_mode);

/**
 * @brief 添加参数设置到播放队列
 * @param intervalMs 间隔时间 (毫秒)
 * @param amplitude 振幅 (0-255)
 * @param frequency 频率偏移
 * @return 0=成功，负数=错误码
 *
 * 添加播放参数设置任务到队列中。
 */
AWA_API int32_t awa_realitytap_append_param(int32_t intervalMs,
                                            int32_t amplitude, int32_t frequency);

/**
 * @brief 添加触觉数据到播放队列
 * @param data 触觉数据数组 (HE格式)
 * @param patternLen 数据长度 (元素个数)
 * @param intervalMs 循环间隔时间 (毫秒)
 * @param loopNum 循环次数 (1=播放一次)
 * @param amplitude 振幅 (0-255)
 * @param frequency 频率偏移
 * @return 0=成功，负数=错误码
 *
 * 将 HE 格式的触觉数据添加到播放队列中，支持循环播放。
 */
AWA_API int32_t awa_realitytap_append_haptics(const int32_t *data, uint32_t patternLen,
                                              uint32_t intervalMs, uint32_t loopNum,
                                              int32_t amplitude, int32_t frequency);

/**
 * @brief 添加预烘焙效果到播放队列
 * @param effect_id 效果ID
 * @param strength 强度 (0-100)
 * @return 0=成功，负数=错误码
 *
 * 将预烘焙效果添加到播放队列中。
 */
AWA_API int32_t awa_realitytap_append_prebak(uint32_t effect_id, uint32_t strength);

/**
 * @brief 添加 RTP 流到播放队列
 * @param fd 文件描述符
 * @return 0=成功，负数=错误码
 *
 * 将 RTP 流数据添加到播放队列中。
 */
AWA_API int32_t awa_realitytap_append_rtp(int32_t fd);

/**
 * @brief 添加停止命令到播放队列
 * @return 0=成功，负数=错误码
 *
 * 在播放队列中添加停止命令，停止当前播放。
 */
AWA_API int32_t awa_realitytap_append_stop();

/**
 * @brief 添加简单振动到播放队列
 * @param timeoutMs 振动持续时间 (毫秒)
 * @return 0=成功，负数=错误码
 *
 * 添加指定时长的简单振动到播放队列中。
 */
AWA_API int32_t awa_realitytap_append_on(uint32_t timeoutMs);

/** @} */ // end of CoreAPI group

#ifdef __cplusplus
}
#endif

#endif //AWA_REALITYTAP_INTERFACES_H
