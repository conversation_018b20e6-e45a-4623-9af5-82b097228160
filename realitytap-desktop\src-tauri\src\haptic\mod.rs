// RealityTap 触觉反馈库 Rust 集成模块
//
// 该模块提供了与 librtcore.dll 的集成，包括：
// - FFI 绑定和类型定义
// - 触觉输出处理器实现
// - Tauri 命令函数
// - 错误处理
// - 消息传递和事件系统
// - 消息过滤和聚合
// - 后台任务处理

pub mod error;
pub mod ffi;
pub mod handler;

// 生命周期管理模块
pub mod lifecycle;

// 统一命令模块（整合了简化架构和统一路由）
pub mod commands;

// 测试模块

// 重导出统一命令模块（包含所有命令和类型）
pub use commands::*;

// 常量定义
pub const DEFAULT_MOTOR_DRIVE_FREQ: u32 = 170; // 默认线性马达实际驱动频率 (Hz)
pub const DEFAULT_SAMPLING_RATE: u32 = 8; // 默认驱动芯片采样率 (SAMPLING_24KHZ)
pub const DEFAULT_CONFIG_FILE: &str = "motors/LRA_0809_normal.conf"; // 默认配置文件名
pub const CONFIG_DIR: &str = "motors"; // 配置文件目录
