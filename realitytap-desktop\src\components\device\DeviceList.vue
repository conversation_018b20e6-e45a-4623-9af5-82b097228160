<template>
  <div class="device-list">
    <n-empty v-if="!loading && devices.length === 0" :description="t('device.status.noDevices')" style="margin-top: 60px">
      <template #extra>
        <n-button size="small" @click="$emit('scan')"> {{ t('device.actions.scan') }} </n-button>
      </template>
    </n-empty>

    <n-spin v-else-if="loading" style="width: 100%; margin-top: 60px">
      <div style="height: 200px"></div>
    </n-spin>

    <div v-else class="device-grid">
      <DeviceCard
        v-for="device in validDevices"
        :key="device.deviceId"
        :device="device"
        @connect="$emit('connect', device.deviceId)"
        @disconnect="$emit('disconnect', device.deviceId)"
        @set-default="$emit('set-default', device.deviceId)"
        @remove="$emit('remove', device.deviceId)"
        @rename="handleRename"
        @send-file="$emit('send-file', device.deviceId, $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { NEmpty, NButton, NSpin } from "naive-ui";
import DeviceCard from "./DeviceCard.vue";
import type { Device } from "@/types/device-types";
import { useI18n } from "@/composables/useI18n";

// === Props ===
interface Props {
  devices: Device[];
  loading?: boolean;
}

const props = defineProps<Props>();
const { t } = useI18n();

// === 计算属性 ===
const validDevices = computed(() => {
  return props.devices.filter(device => device && device.deviceId);
});

// === Events ===
interface Emits {
  connect: [deviceId: string];
  disconnect: [deviceId: string];
  "set-default": [deviceId: string];
  remove: [deviceId: string];
  rename: [deviceId: string, newName: string];
  "send-file": [deviceId: string, filePath: string];
  scan: [];
}

const emit = defineEmits<Emits>();

// === 事件处理 ===
const handleRename = (deviceId: string, newName: string) => {
  emit("rename", deviceId, newName);
};
</script>

<style scoped>
.device-list {
  height: 100%;
  overflow-y: auto;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  padding: 8px;
}

@media (max-width: 768px) {
  .device-grid {
    grid-template-columns: 1fr;
  }
}
</style>
