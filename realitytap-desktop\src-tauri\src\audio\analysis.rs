// Audio analysis utilities
use crate::models::audio::AudioInfo;
use rustfft::{FftPlanner, num_complex::Complex};
use serde::Serialize;
use std::f32::consts::PI;

// Audio amplitude data structure
#[derive(Serial<PERSON>, <PERSON>lone, Debug)]
pub struct AmplitudeData {
    pub samples: Vec<f32>,
    pub sample_rate: u32,
    pub duration_ms: u64,
    pub max_amplitude: f32,
    pub min_amplitude: f32,
}

// Audio frequency data structure
#[derive(Serial<PERSON>, <PERSON>lone, Debug)]
pub struct FrequencyData {
    pub frequencies: Vec<f32>,    // 频率值 (Hz)
    pub magnitudes: Vec<f32>,     // 对应的幅度值
    pub sample_rate: u32,
    pub fft_size: usize,
    pub frequency_resolution: f32, // 频率分辨率 (Hz/bin)
}

// Combined audio analysis data structure
#[derive(Serialize, <PERSON>lone, Debug)]
pub struct AudioAnalysisData {
    pub amplitude_data: AmplitudeData,
    pub frequency_data: FrequencyData,
    pub audio_info: AudioInfo,
}

/// Create amplitude data from audio samples
pub fn create_amplitude_data(samples: &[f32], sample_rate: u32, duration_ms: u64) -> AmplitudeData {
    let max_amplitude = samples.iter().cloned().fold(f32::NEG_INFINITY, f32::max);
    let min_amplitude = samples.iter().cloned().fold(f32::INFINITY, f32::min);

    AmplitudeData {
        samples: samples.to_vec(),
        sample_rate,
        duration_ms,
        max_amplitude,
        min_amplitude,
    }
}

/// Compute frequency spectrum using FFT
pub fn compute_frequency_spectrum(samples: &[f32], sample_rate: u32, fft_size: Option<usize>) -> FrequencyData {
    let fft_size = fft_size.unwrap_or(2048).min(samples.len()).next_power_of_two();

    // 创建FFT规划器
    let mut planner = FftPlanner::new();
    let fft = planner.plan_fft_forward(fft_size);

    // 准备输入数据
    let mut buffer: Vec<Complex<f32>> = samples
        .iter()
        .take(fft_size)
        .map(|&x| Complex::new(x, 0.0))
        .collect();

    // 如果样本数不足，用零填充
    buffer.resize(fft_size, Complex::new(0.0, 0.0));

    // 应用汉宁窗函数以减少频谱泄漏
    for (i, sample) in buffer.iter_mut().enumerate() {
        let window = 0.5 * (1.0 - (2.0 * PI * i as f32 / (fft_size - 1) as f32).cos());
        sample.re *= window;
    }

    // 执行FFT
    fft.process(&mut buffer);

    // 计算频率和幅度
    let frequency_resolution = sample_rate as f32 / fft_size as f32;
    let nyquist_freq = sample_rate as f32 / 2.0;

    // 只取前一半的结果（由于对称性）
    let half_size = fft_size / 2;
    let mut frequencies = Vec::with_capacity(half_size);
    let mut magnitudes = Vec::with_capacity(half_size);

    for i in 0..half_size {
        let freq = i as f32 * frequency_resolution;
        if freq <= nyquist_freq {
            frequencies.push(freq);

            // 计算幅度（模长）
            let magnitude = buffer[i].norm();
            // 转换为分贝（dB）
            let magnitude_db = if magnitude > 0.0 {
                20.0 * magnitude.log10()
            } else {
                -100.0 // 最小值
            };
            magnitudes.push(magnitude_db);
        }
    }

    FrequencyData {
        frequencies,
        magnitudes,
        sample_rate,
        fft_size,
        frequency_resolution,
    }
}

/// Calculate optimal sample count based on audio duration
pub fn calculate_optimal_sample_count(duration_ms: u64) -> usize {
    match duration_ms {
        0..=30_000 => 2048,        // ≤30秒：高精度
        30_001..=60_000 => 1536,   // 30秒-1分钟：较高精度
        60_001..=300_000 => 1024,  // 1-5分钟：中等精度
        300_001..=600_000 => 512,  // 5-10分钟：较低精度
        600_001..=1_800_000 => 256, // 10-30分钟：低精度
        _ => 128,                   // >30分钟：最低精度
    }
}

/// Apply simple downsampling to audio samples (legacy method)
pub fn downsample_audio(samples: Vec<f32>, max_samples: usize) -> Vec<f32> {
    if samples.len() <= max_samples {
        return samples;
    }

    let step = samples.len() / max_samples;
    samples.into_iter().step_by(step).take(max_samples).collect()
}

/// Apply downsampling with peak preservation to maintain waveform characteristics
pub fn downsample_audio_with_peak_preservation(samples: Vec<f32>, max_samples: usize) -> Vec<f32> {
    if samples.len() <= max_samples {
        return samples;
    }

    let chunk_size = samples.len() / max_samples;
    let mut result = Vec::with_capacity(max_samples);

    for i in 0..max_samples {
        let start_idx = i * chunk_size;
        let end_idx = if i == max_samples - 1 {
            samples.len() // 最后一个块包含所有剩余样本
        } else {
            (i + 1) * chunk_size
        };

        if start_idx < samples.len() {
            let chunk = &samples[start_idx..end_idx.min(samples.len())];

            if chunk.is_empty() {
                result.push(0.0);
                continue;
            }

            // 在每个块中找到绝对值最大的样本（保留峰值特征）
            let peak_sample = chunk.iter()
                .max_by(|a, b| a.abs().partial_cmp(&b.abs()).unwrap_or(std::cmp::Ordering::Equal))
                .copied()
                .unwrap_or(0.0);

            result.push(peak_sample);
        }
    }

    result
}

/// Create complete audio analysis data
pub fn create_audio_analysis(
    samples: &[f32],
    audio_info: AudioInfo,
    max_amplitude_samples: Option<usize>,
    fft_size: Option<usize>,
) -> AudioAnalysisData {
    // Create amplitude data (possibly downsampled)
    let amplitude_samples = if let Some(max_count) = max_amplitude_samples {
        downsample_audio(samples.to_vec(), max_count)
    } else {
        samples.to_vec()
    };
    let amplitude_data = create_amplitude_data(&amplitude_samples, audio_info.sample_rate, audio_info.duration_ms);

    // Create frequency data (using original samples)
    let frequency_data = compute_frequency_spectrum(samples, audio_info.sample_rate, fft_size);

    AudioAnalysisData {
        amplitude_data,
        frequency_data,
        audio_info,
    }
}
