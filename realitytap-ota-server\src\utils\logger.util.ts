import { config } from '@/config/server.config';
import path from 'path';
import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { DatabaseTransport } from './database-transport';

// 日志去重缓存 - 防止短时间内重复记录相同的日志
const logCache = new Map<string, number>();
const CACHE_DURATION = 60000; // 1分钟内相同日志只记录一次

// 清理过期的缓存条目
setInterval(() => {
  const now = Date.now();
  for (const [key, timestamp] of logCache.entries()) {
    if (now - timestamp > CACHE_DURATION) {
      logCache.delete(key);
    }
  }
}, CACHE_DURATION);

// 日志去重过滤器
const deduplicationFilter = winston.format((info: any) => {
  // 对于认证相关的重复性日志进行去重
  if (typeof info.message === 'string') {
    // 认证失败和JWT过期日志去重
    if (
      info.level === 'warn' &&
      (info.message.includes('Token verification failed') ||
        info.message.includes('Authentication failed') ||
        info.message.includes('jwt expired'))
    ) {
      const cacheKey = `${info.level}:${info.message}:${info.ip || 'unknown'}`;
      const now = Date.now();

      if (logCache.has(cacheKey)) {
        const lastLogged = logCache.get(cacheKey)!;
        if (now - lastLogged < CACHE_DURATION) {
          return false; // 跳过重复日志
        }
      }

      logCache.set(cacheKey, now);
    }

    // 认证成功日志去重（针对高频API请求）
    if (
      (info.level === 'debug' || info.level === 'info') &&
      (info.message.includes('认证成功') || info.message.includes('Authentication successful'))
    ) {
      // 为认证成功日志创建更细粒度的缓存键，包含路径信息
      const cacheKey = `${info.level}:${info.message}:${info.username || 'unknown'}:${info.path || 'unknown'}:${info.clientIP || info.ip || 'unknown'}`;
      const now = Date.now();

      if (logCache.has(cacheKey)) {
        const lastLogged = logCache.get(cacheKey)!;
        // 对于认证成功日志，使用较短的去重时间（30秒）
        if (now - lastLogged < 30000) {
          return false; // 跳过重复日志
        }
      }

      logCache.set(cacheKey, now);
    }
  }

  return info;
});

// Custom log format
const logFormat = winston.format.combine(
  deduplicationFilter(),
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
);

// Console format for development
const consoleFormat = winston.format.combine(
  deduplicationFilter(),
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss',
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta)}`;
    }
    return msg;
  }),
);

// Create transports
const transports: winston.transport[] = [];

// Console transport
if (config.server.nodeEnv === 'development') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: config.logging.level,
    }),
  );
} else {
  transports.push(
    new winston.transports.Console({
      format: logFormat,
      level: config.logging.level,
    }),
  );
}

// File transports for both development and production
// 确保日志目录存在
const fs = require('fs-extra');
fs.ensureDirSync(config.storage.logsPath);

// Error log file
transports.push(
  new DailyRotateFile({
    filename: path.join(config.storage.logsPath, 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    level: 'error',
    format: logFormat,
    maxSize: config.logging.maxSize,
    maxFiles: config.logging.maxFiles,
    zippedArchive: config.server.nodeEnv === 'production',
  }),
);

// Combined log file
transports.push(
  new DailyRotateFile({
    filename: path.join(config.storage.logsPath, 'combined-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    format: logFormat,
    maxSize: config.logging.maxSize,
    maxFiles: config.logging.maxFiles,
    zippedArchive: config.server.nodeEnv === 'production',
  }),
);

// Access log file
transports.push(
  new DailyRotateFile({
    filename: path.join(config.storage.logsPath, 'access-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    format: logFormat,
    maxSize: config.logging.maxSize,
    maxFiles: config.logging.maxFiles,
    zippedArchive: config.server.nodeEnv === 'production',
  }),
);

// 开发环境下也添加一个简单的 app.log 文件用于兼容性
if (config.server.nodeEnv === 'development') {
  transports.push(
    new winston.transports.File({
      filename: path.join(config.storage.logsPath, 'app.log'),
      format: logFormat,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
    }),
  );
}

// Database transport (if enabled)
if (process.env.DB_ENABLED === 'true') {
  transports.push(
    new DatabaseTransport({
      level: config.logging.level,
    })
  );
}

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  transports,
  exitOnError: false,
});

// Add request ID to logs
(logger as any).addContext = (context: Record<string, any>) => {
  return logger.child(context);
};

export { logger };
