#!/usr/bin/env tsx

/**
 * 发布管理脚本
 * 用于管理OTA更新的发布文件和版本信息
 */

import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import { Command } from 'commander';

interface PlatformRelease {
  filename: string;
  size: number;
  checksum: string;
  releaseDate: string;
  releaseNotes: string;
}

interface VersionInfo {
  version: string;
  platforms: Record<string, Record<string, PlatformRelease>>;
}

interface VersionsConfig {
  channels: Record<string, VersionInfo>;
  minimumVersions: Record<string, string>;
  deprecatedVersions: string[];
}

const STORAGE_PATH = path.join(__dirname, '../storage');
const RELEASES_PATH = path.join(STORAGE_PATH, 'releases');
const METADATA_PATH = path.join(STORAGE_PATH, 'metadata');
const VERSIONS_FILE = path.join(METADATA_PATH, 'versions.json');

/**
 * 计算文件的SHA256校验和
 */
async function calculateChecksum(filePath: string): Promise<string> {
  const fileBuffer = await fs.readFile(filePath);
  const hashSum = crypto.createHash('sha256');
  hashSum.update(fileBuffer);
  return `sha256:${hashSum.digest('hex')}`;
}

/**
 * 获取文件大小
 */
async function getFileSize(filePath: string): Promise<number> {
  const stats = await fs.stat(filePath);
  return stats.size;
}

/**
 * 读取版本配置
 */
async function readVersionsConfig(): Promise<VersionsConfig> {
  try {
    const content = await fs.readFile(VERSIONS_FILE, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    console.error('Failed to read versions config:', error);
    throw error;
  }
}

/**
 * 写入版本配置
 */
async function writeVersionsConfig(config: VersionsConfig): Promise<void> {
  try {
    const content = JSON.stringify(config, null, 2);
    await fs.writeFile(VERSIONS_FILE, content, 'utf-8');
    console.log('✅ 版本配置已更新');
  } catch (error) {
    console.error('Failed to write versions config:', error);
    throw error;
  }
}

/**
 * 添加新发布
 */
async function addRelease(
  channel: string,
  version: string,
  platform: string,
  architecture: string,
  filePath: string,
  releaseNotes: string
): Promise<void> {
  console.log(`📦 添加发布: ${channel}/${version}/${platform}/${architecture}`);
  
  // 检查文件是否存在
  try {
    await fs.access(filePath);
  } catch (error) {
    throw new Error(`文件不存在: ${filePath}`);
  }

  // 计算文件信息
  const filename = path.basename(filePath);
  const size = await getFileSize(filePath);
  const checksum = await calculateChecksum(filePath);
  const releaseDate = new Date().toISOString();

  // 复制文件到发布目录
  const targetDir = path.join(RELEASES_PATH, channel);
  await fs.mkdir(targetDir, { recursive: true });
  const targetPath = path.join(targetDir, filename);
  await fs.copyFile(filePath, targetPath);

  // 更新版本配置
  const config = await readVersionsConfig();
  
  if (!config.channels[channel]) {
    config.channels[channel] = {
      version,
      platforms: {},
    };
  }

  if (!config.channels[channel].platforms[platform]) {
    config.channels[channel].platforms[platform] = {};
  }

  config.channels[channel].version = version;
  config.channels[channel].platforms[platform][architecture] = {
    filename,
    size,
    checksum,
    releaseDate,
    releaseNotes,
  };

  await writeVersionsConfig(config);
  console.log(`✅ 发布添加成功: ${filename}`);
}

/**
 * 列出所有发布
 */
async function listReleases(): Promise<void> {
  console.log('📋 当前发布列表:\n');
  
  const config = await readVersionsConfig();
  
  for (const [channel, channelInfo] of Object.entries(config.channels)) {
    console.log(`🔸 ${channel.toUpperCase()} (${channelInfo.version})`);
    
    for (const [platform, platformInfo] of Object.entries(channelInfo.platforms)) {
      for (const [arch, release] of Object.entries(platformInfo)) {
        console.log(`  └─ ${platform}/${arch}: ${release.filename} (${formatFileSize(release.size)})`);
      }
    }
    console.log();
  }
}

/**
 * 删除发布
 */
async function removeRelease(
  channel: string,
  platform: string,
  architecture: string
): Promise<void> {
  console.log(`🗑️ 删除发布: ${channel}/${platform}/${architecture}`);
  
  const config = await readVersionsConfig();
  
  if (!config.channels[channel]?.platforms[platform]?.[architecture]) {
    throw new Error('发布不存在');
  }

  const release = config.channels[channel].platforms[platform][architecture];
  const filePath = path.join(RELEASES_PATH, channel, release.filename);
  
  // 删除文件
  try {
    await fs.unlink(filePath);
    console.log(`✅ 文件已删除: ${release.filename}`);
  } catch (error) {
    console.warn(`⚠️ 文件删除失败: ${error}`);
  }

  // 更新配置
  delete config.channels[channel].platforms[platform][architecture];
  
  // 如果平台下没有架构了，删除平台
  if (Object.keys(config.channels[channel].platforms[platform]).length === 0) {
    delete config.channels[channel].platforms[platform];
  }
  
  // 如果渠道下没有平台了，删除渠道
  if (Object.keys(config.channels[channel].platforms).length === 0) {
    delete config.channels[channel];
  }

  await writeVersionsConfig(config);
  console.log('✅ 发布删除成功');
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * 主程序
 */
async function main() {
  const program = new Command();

  program
    .name('manage-release')
    .description('OTA发布管理工具')
    .version('1.0.0');

  program
    .command('add')
    .description('添加新发布')
    .requiredOption('-c, --channel <channel>', '发布渠道 (stable/beta/alpha)')
    .requiredOption('-v, --version <version>', '版本号')
    .requiredOption('-p, --platform <platform>', '平台 (windows/macos/linux)')
    .requiredOption('-a, --arch <arch>', '架构 (x86_64/aarch64)')
    .requiredOption('-f, --file <file>', '发布文件路径')
    .requiredOption('-n, --notes <notes>', '发布说明')
    .action(async (options) => {
      try {
        await addRelease(
          options.channel,
          options.version,
          options.platform,
          options.arch,
          options.file,
          options.notes
        );
      } catch (error) {
        console.error('❌ 添加发布失败:', error.message);
        process.exit(1);
      }
    });

  program
    .command('list')
    .description('列出所有发布')
    .action(async () => {
      try {
        await listReleases();
      } catch (error) {
        console.error('❌ 列出发布失败:', error.message);
        process.exit(1);
      }
    });

  program
    .command('remove')
    .description('删除发布')
    .requiredOption('-c, --channel <channel>', '发布渠道')
    .requiredOption('-p, --platform <platform>', '平台')
    .requiredOption('-a, --arch <arch>', '架构')
    .action(async (options) => {
      try {
        await removeRelease(options.channel, options.platform, options.arch);
      } catch (error) {
        console.error('❌ 删除发布失败:', error.message);
        process.exit(1);
      }
    });

  await program.parseAsync();
}

if (require.main === module) {
  main().catch(console.error);
}
