<template>
  <div class="log-viewer">
    <n-page-header title="系统日志" subtitle="查看和管理系统运行日志">
      <template #extra>
        <n-space>
          <n-button :loading="loading" @click="refreshLogs">
            <template #icon>
              <n-icon :component="RefreshOutline" />
            </template>
            刷新
          </n-button>

          <n-button type="primary" @click="exportLogs" :loading="exportLoading">
            <template #icon>
              <n-icon :component="DownloadOutline" />
            </template>
            导出日志
          </n-button>

          <n-button type="error" @click="clearLogs" :loading="clearLoading">
            <template #icon>
              <n-icon :component="TrashOutline" />
            </template>
            清空日志
          </n-button>
        </n-space>
      </template>
    </n-page-header>

    <n-space vertical size="large">
      <!-- 过滤器 -->
      <n-card title="日志过滤">
        <n-space>
          <n-select
            v-model:value="selectedModule"
            :options="moduleOptions"
            placeholder="选择功能模块"
            style="width: 150px"
            @update:value="handleModuleChange"
          />

          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索日志内容、IP地址..."
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <n-icon :component="SearchOutline" />
            </template>
          </n-input>

          <n-switch v-model:value="showIPColumn" @update:value="handleIPColumnToggle">
            <template #checked>显示IP</template>
            <template #unchecked>隐藏IP</template>
          </n-switch>

          <n-button @click="clearFilters"> 清除过滤 </n-button>
        </n-space>
      </n-card>

      <!-- 日志列表 -->
      <n-card title="日志记录">
        <template #header-extra>
          <n-space>
            <n-text depth="3"> 共 {{ totalLogs }} 条记录 </n-text>
            <n-switch v-model:value="autoRefresh" @update:value="handleAutoRefreshChange">
              <template #checked> 自动刷新 </template>
              <template #unchecked> 手动刷新 </template>
            </n-switch>
          </n-space>
        </template>

        <n-data-table
          :columns="columns"
          :data="logs"
          :loading="loading"
          :pagination="paginationConfig"
          :row-key="(row: LogEntry) => `${row.timestamp}-${row.message}`"
          size="small"
          striped
          remote
        />
      </n-card>
    </n-space>

    <!-- 日志详情模态框 -->
    <n-modal v-model:show="showLogDetail" preset="card" title="日志详情" style="width: 800px">
      <n-descriptions v-if="selectedLog" :column="1" label-placement="left" bordered>
        <n-descriptions-item label="时间">
          {{ formatTimestamp(selectedLog.timestamp) }}
        </n-descriptions-item>
        <n-descriptions-item label="级别">
          <n-tag :type="getLogLevelType(selectedLog.level)">
            {{ selectedLog.level.toUpperCase() }}
          </n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="消息">
          {{ selectedLog.message }}
        </n-descriptions-item>
        <n-descriptions-item label="客户端IP" v-if="selectedLog.clientIP">
          <n-space>
            <n-tag :type="selectedLog.isPrivateIP ? 'warning' : 'info'">
              {{ selectedLog.clientIP }}
            </n-tag>
            <n-text depth="3" v-if="selectedLog.isPrivateIP">(内网IP)</n-text>
          </n-space>
        </n-descriptions-item>
        <n-descriptions-item label="操作类型" v-if="selectedLog.operation">
          <n-tag type="primary">{{ operationMap[selectedLog.operation] || selectedLog.operation }}</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="请求路径" v-if="selectedLog.path">
          <n-text code>{{ selectedLog.method || 'GET' }} {{ selectedLog.path }}</n-text>
        </n-descriptions-item>
        <n-descriptions-item label="用户代理" v-if="selectedLog.userAgent">
          <n-text depth="2" style="font-size: 12px">{{ selectedLog.userAgent }}</n-text>
        </n-descriptions-item>
        <n-descriptions-item label="元数据" v-if="selectedLog.meta && Object.keys(selectedLog.meta).length > 0">
          <n-code :code="JSON.stringify(selectedLog.meta, null, 2)" language="json" show-line-numbers />
        </n-descriptions-item>
      </n-descriptions>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { adminApi } from '@/api/admin';
import type { LogEntry } from '@/api/types';
import { DownloadOutline, RefreshOutline, SearchOutline, TrashOutline } from '@vicons/ionicons5';
import {
  NButton,
  NCard,
  NCode,
  NDataTable,
  NDescriptions,
  NDescriptionsItem,
  NIcon,
  NInput,
  NModal,
  NPageHeader,
  NSelect,
  NSpace,
  NSwitch,
  NTag,
  NText,
  useMessage,
  useDialog,
  type DataTableColumns,
} from 'naive-ui';
import { computed, h, onMounted, onUnmounted, ref } from 'vue';

const message = useMessage();
const dialog = useDialog();

// 状态
const loading = ref(false);
const exportLoading = ref(false);
const clearLoading = ref(false);
const logs = ref<LogEntry[]>([]);
const totalLogs = ref(0);
const selectedModule = ref<string>(''); // 功能模块过滤
const searchKeyword = ref('');
const autoRefresh = ref(false);
const showLogDetail = ref(false);
const selectedLog = ref<LogEntry | null>(null);
const showIPColumn = ref(true);

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null;

// 功能模块选项
const moduleOptions = [
  { label: '全部日志', value: '' },
  { label: '用户操作', value: 'user_operation' },
  { label: '版本管理', value: 'version_management' },
  { label: 'OTA功能', value: 'ota_function' },
  { label: '系统日志', value: 'system' },
];

// 操作类型中文映射
const operationMap: Record<string, string> = {
  // HTTP相关
  http_request: 'HTTP请求',

  // 用户操作
  auth_operation: '认证操作',
  admin_management: '管理操作',
  admin_access: '管理访问',

  // 版本管理
  version_view: '版本查看',
  version_operation: '版本操作',
  file_download: '文件下载',

  // OTA功能
  ota_request: 'OTA请求',
  health_check: '健康检查',

  // 系统日志
  system_log: '系统日志',

  // 兼容旧格式
  admin_view: '管理查看',
  admin_operation: '管理操作',
  auth_request: '认证请求',
  file_download_request: '文件下载',
  chunk_upload_init: '分块上传初始化',
  chunk_upload_complete: '分块上传完成',
  clear_logs: '清空日志',
  clear_logs_complete: '清空日志完成',
};

// 分页状态
const currentPage = ref(1);
const pageSize = ref(50);

// 分页配置
const paginationConfig = computed(() => {
  const totalPages = Math.ceil(totalLogs.value / pageSize.value);

  return {
    page: currentPage.value,
    pageSize: pageSize.value,
    itemCount: totalLogs.value,
    pageCount: totalPages,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    showQuickJumper: true,
    prefix: (info: { startIndex: number; endIndex: number; page: number; pageSize: number; pageCount: number; itemCount: number | undefined }) => `共 ${info.itemCount || 0} 条`,
    suffix: (info: { startIndex: number; endIndex: number; page: number; pageSize: number; pageCount: number; itemCount: number | undefined }) => `共 ${info.pageCount} 页`,
    onChange: (page: number) => {
      currentPage.value = page;
      fetchLogs();
    },
    onUpdatePageSize: (newPageSize: number) => {
      pageSize.value = newPageSize;
      currentPage.value = 1;
      fetchLogs();
    },
  };
});

// 搜索关键词变化时的防抖处理
let searchTimeout: NodeJS.Timeout | null = null;

// 表格列定义
const columns = computed<DataTableColumns<LogEntry>>(() => {
  const baseColumns: DataTableColumns<LogEntry> = [
    {
      title: '时间',
      key: 'timestamp',
      width: 180,
      render: row => formatTimestamp(row.timestamp),
    },
    {
      title: '级别',
      key: 'level',
      width: 90,
      render: row => h(NTag, { type: getLogLevelType(row.level) }, () => row.level.toUpperCase()),
    },
  ];

  // 条件性添加IP列
  if (showIPColumn.value) {
    baseColumns.push({
      title: '客户端IP',
      key: 'clientIP',
      width: 220,
      render: row => {
        if (!row.clientIP) return '-';
        return h('div', { style: 'display: flex; align-items: center; gap: 4px;' }, [
          h(
            NTag,
            {
              type: row.isPrivateIP ? 'warning' : 'info',
              size: 'small',
            },
            () => row.clientIP,
          ),
          row.operation &&
            h(
              NTag,
              {
                type: 'primary',
                size: 'small',
              },
              () => (row.operation ? operationMap[row.operation] || row.operation : ''),
            ),
        ]);
      },
    });
  }

  baseColumns.push(
    {
      title: '消息',
      key: 'message',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: row =>
        h(
          NButton,
          {
            size: 'small',
            onClick: () => showLogDetails(row),
          },
          () => '查看详情',
        ),
    },
  );

  return baseColumns;
});

// 获取日志级别对应的标签类型
const getLogLevelType = (level: string) => {
  switch (level.toLowerCase()) {
    case 'error':
      return 'error';
    case 'warn':
      return 'warning';
    case 'info':
      return 'info';
    case 'debug':
      return 'default';
    default:
      return 'default';
  }
};

// 格式化时间戳
const formatTimestamp = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

// 获取日志数据
const fetchLogs = async () => {
  loading.value = true;
  try {
    const offset = (currentPage.value - 1) * pageSize.value;
    const response = await adminApi.getLogs({
      module: selectedModule.value || undefined,
      limit: pageSize.value,
      offset,
      search: searchKeyword.value || undefined,
    });

    if (response.success && response.data) {
      logs.value = response.data.logs;
      totalLogs.value = response.data.total;
    }
  } catch (error) {
    message.error('获取日志失败');
  } finally {
    loading.value = false;
  }
};

// 刷新日志
const refreshLogs = () => {
  fetchLogs();
};

// 处理模块变化
const handleModuleChange = () => {
  currentPage.value = 1;
  fetchLogs();
};

// 处理搜索
const handleSearch = () => {
  // 清除之前的搜索定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  // 设置新的搜索定时器，防抖处理
  searchTimeout = setTimeout(() => {
    currentPage.value = 1;
    fetchLogs();
  }, 500);
};

// 清除过滤器
const clearFilters = () => {
  selectedModule.value = '';
  searchKeyword.value = '';
  currentPage.value = 1;
  fetchLogs();
};

// 处理自动刷新变化
const handleAutoRefreshChange = (value: boolean) => {
  if (value) {
    refreshTimer = setInterval(() => {
      fetchLogs();
    }, 10000); // 每10秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  }
};

// 处理IP列显示切换
const handleIPColumnToggle = (value: boolean) => {
  showIPColumn.value = value;
};

// 显示日志详情
const showLogDetails = (log: LogEntry) => {
  selectedLog.value = log;
  showLogDetail.value = true;
};

// 导出日志
const exportLogs = async () => {
  exportLoading.value = true;
  try {
    // TODO: 实现日志导出功能
    message.info('日志导出功能开发中...');
  } catch (error) {
    message.error('导出失败');
  } finally {
    exportLoading.value = false;
  }
};

// 清空日志
const clearLogs = () => {
  dialog.warning({
    title: '确认清空日志',
    content: '此操作将永久删除所有日志文件，无法恢复。确定要继续吗？',
    positiveText: '确定清空',
    negativeText: '取消',
    onPositiveClick: async () => {
      clearLoading.value = true;
      try {
        const response = await adminApi.clearLogs();
        if (response.success) {
          message.success('日志已清空');
          // 重置分页到第一页并刷新
          currentPage.value = 1;
          fetchLogs();
        } else {
          message.error('清空日志失败');
        }
      } catch (error) {
        message.error('清空日志失败');
      } finally {
        clearLoading.value = false;
      }
    },
  });
};

onMounted(() => {
  fetchLogs();
});

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});
</script>

<style scoped>
.log-viewer {
  margin: 0 auto;
}
:deep(.n-descriptions-table-header) {
  width: 100px !important;
}
</style>
