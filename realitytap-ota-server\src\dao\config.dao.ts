import { BaseDAO } from './base.dao';
import { SystemConfig } from '@/types/server.types';

export interface ConfigEntity {
  id: number;
  config_key: string;
  config_value: string;
  config_type: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateConfigData {
  config_key: string;
  config_value: string;
  config_type?: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
}

export interface UpdateConfigData {
  config_value?: string;
  config_type?: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
}

export class ConfigDAO extends BaseDAO {
  constructor() {
    super('system_config');
  }

  /**
   * 创建配置项
   */
  async createConfig(data: CreateConfigData): Promise<number> {
    this.logOperation('createConfig', data);
    
    const configData = {
      ...data,
      config_type: data.config_type || 'string'
    };
    
    return await this.insert(configData);
  }

  /**
   * 根据键获取配置
   */
  async getConfigByKey(key: string): Promise<ConfigEntity | undefined> {
    this.logOperation('getConfigByKey', { key });
    return await this.findOneWhere<ConfigEntity>({ config_key: key });
  }

  /**
   * 获取所有配置
   */
  async getAllConfigs(): Promise<ConfigEntity[]> {
    this.logOperation('getAllConfigs');
    return await this.findAll<ConfigEntity>('config_key ASC');
  }

  /**
   * 更新配置
   */
  async updateConfig(key: string, data: UpdateConfigData): Promise<boolean> {
    this.logOperation('updateConfig', { key, data });
    
    const config = await this.getConfigByKey(key);
    if (!config) {
      return false;
    }

    return await this.update(config.id, data);
  }

  /**
   * 删除配置
   */
  async deleteConfig(key: string): Promise<boolean> {
    this.logOperation('deleteConfig', { key });
    
    const config = await this.getConfigByKey(key);
    if (!config) {
      return false;
    }

    return await this.delete(config.id);
  }

  /**
   * 设置配置值（如果不存在则创建）
   */
  async setConfig(key: string, value: any, type?: 'string' | 'number' | 'boolean' | 'json', description?: string): Promise<void> {
    this.logOperation('setConfig', { key, value, type });
    
    const configValue = this.serializeValue(value, type);
    const configType = type || this.inferType(value);
    
    const existingConfig = await this.getConfigByKey(key);
    
    if (existingConfig) {
      await this.updateConfig(key, {
        config_value: configValue,
        config_type: configType,
        description
      });
    } else {
      await this.createConfig({
        config_key: key,
        config_value: configValue,
        config_type: configType,
        description
      });
    }
  }

  /**
   * 获取配置值（自动反序列化）
   */
  async getConfigValue<T = any>(key: string, defaultValue?: T): Promise<T | undefined> {
    this.logOperation('getConfigValue', { key });
    
    const config = await this.getConfigByKey(key);
    if (!config) {
      return defaultValue;
    }

    return this.deserializeValue<T>(config.config_value, config.config_type);
  }

  /**
   * 批量设置配置
   */
  async batchSetConfigs(configs: Record<string, any>): Promise<void> {
    this.logOperation('batchSetConfigs', { count: Object.keys(configs).length });
    
    await this.transaction(async () => {
      for (const [key, value] of Object.entries(configs)) {
        await this.setConfig(key, value);
      }
    });
  }

  /**
   * 获取系统配置（转换为旧格式）
   */
  async getSystemConfig(): Promise<SystemConfig> {
    this.logOperation('getSystemConfig');
    
    const configs = await this.getAllConfigs();
    const systemConfig: any = {};
    
    for (const config of configs) {
      const value = this.deserializeValue(config.config_value, config.config_type);
      this.setNestedValue(systemConfig, config.config_key, value);
    }
    
    // 确保返回完整的 SystemConfig 结构
    return this.mergeWithDefaultConfig(systemConfig);
  }

  /**
   * 更新系统配置
   */
  async updateSystemConfig(config: SystemConfig): Promise<void> {
    this.logOperation('updateSystemConfig', config);
    
    const flatConfig = this.flattenConfig(config);
    await this.batchSetConfigs(flatConfig);
  }

  /**
   * 获取配置统计信息
   */
  async getConfigStats(): Promise<{
    totalConfigs: number;
    configsByType: Record<string, number>;
  }> {
    this.logOperation('getConfigStats');

    const totalConfigs = await this.count();
    
    const sql = `
      SELECT config_type, COUNT(*) as count
      FROM system_config
      GROUP BY config_type
    `;
    
    const typeStats = await this.query<{ config_type: string; count: number }>(sql);
    
    const configsByType: Record<string, number> = {};
    for (const stat of typeStats) {
      configsByType[stat.config_type] = stat.count;
    }

    return {
      totalConfigs,
      configsByType
    };
  }

  /**
   * 序列化值
   */
  private serializeValue(value: any, type?: string): string {
    if (type === 'json' || (typeof value === 'object' && value !== null)) {
      return JSON.stringify(value);
    }
    return String(value);
  }

  /**
   * 反序列化值
   */
  private deserializeValue<T>(value: string, type: string): T {
    switch (type) {
      case 'number':
        return Number(value) as T;
      case 'boolean':
        return (value === 'true' || value === '1') as T;
      case 'json':
        try {
          return JSON.parse(value) as T;
        } catch {
          return value as T;
        }
      default:
        return value as T;
    }
  }

  /**
   * 推断值类型
   */
  private inferType(value: any): 'string' | 'number' | 'boolean' | 'json' {
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'object' && value !== null) return 'json';
    return 'string';
  }

  /**
   * 设置嵌套值
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!key) {
        throw new Error(`Invalid key at index ${i} in path: ${path}`);
      }
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    const lastKey = keys[keys.length - 1];
    if (!lastKey) {
      throw new Error(`Invalid last key in path: ${path}`);
    }
    current[lastKey] = value;
  }

  /**
   * 扁平化配置对象
   */
  private flattenConfig(config: any, prefix: string = ''): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(config)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        Object.assign(result, this.flattenConfig(value, fullKey));
      } else {
        result[fullKey] = value;
      }
    }
    
    return result;
  }

  /**
   * 与默认配置合并
   */
  private mergeWithDefaultConfig(config: any): SystemConfig {
    const defaultConfig: SystemConfig = {
      upload: {
        maxFileSize: 524288000,
        allowedExtensions: ['.exe', '.dmg', '.deb', '.rpm', '.zip', '.tar.gz'],
        tempDir: './storage/temp',
        cleanupInterval: 86400000
      },
      download: {
        rateLimit: {
          windowMs: 900000,
          maxRequests: 100
        },
        enableStats: true,
        enableCompression: true
      },
      storage: {
        basePath: './storage',
        cleanupInterval: 604800000,
        maxStorageSize: 10737418240
      },
      security: {
        enableCORS: true,
        allowedOrigins: ['http://localhost:3000', 'http://localhost:5173'],
        enableHelmet: true
      },
      logging: {
        level: 'info',
        enableFileLogging: true,
        maxLogFiles: 10,
        maxLogSize: '10m'
      }
    };

    return this.deepMerge(defaultConfig, config);
  }

  /**
   * 深度合并对象
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }
}
