# InteractiveWaveformCanvas 编程式数据更新 API

## 概述

本文档描述了 `InteractiveWaveformCanvas.vue` 组件的编程式数据更新方案，该方案允许外部组件直接传入完整的 `RenderableEvent` 数组数据，并在现有波形基础上进行重新绘制，而无需依赖UI交互。

## 架构设计

### 数据流架构

```
外部组件调用
    ↓
组件暴露的API方法 (defineExpose)
    ↓
Store的批量更新方法 (setBatchEvents/updateBatchEvents)
    ↓
响应式数据更新 (this.events = newEvents)
    ↓
Watcher监听触发 (watch(() => waveformStore.events))
    ↓
重绘函数调用 (updateAndDrawCanvas)
    ↓
波形重新渲染
```

### 核心组件

1. **Store层批量更新方法** - 负责数据验证、状态管理和持久化
2. **组件暴露接口** - 提供易用的编程式API
3. **响应式更新机制** - 自动触发波形重绘

## API 接口

### 类型定义

```typescript
// 批量更新选项
interface BatchUpdateOptions {
  preserveSelection?: boolean; // 是否保持当前选中状态，默认 true
  skipValidation?: boolean;    // 是否跳过数据验证，默认 false
  forceRedraw?: boolean;       // 是否强制重绘，默认 true
  skipFileStateSync?: boolean; // 是否跳过文件状态同步，默认 false
}

// 事件更新项
interface EventUpdateItem {
  id: string;
  data: Partial<RenderableEvent>;
}

// 数据验证结果
interface ValidationResult {
  valid: boolean;
  errors: string[];
}
```

### 主要方法

#### 1. setEventsData - 完整数据替换

```typescript
async setEventsData(
  events: RenderableEvent[], 
  options?: BatchUpdateOptions
): Promise<void>
```

**功能**: 完全替换当前的事件数据
**适用场景**: 外部传入全新的事件数组
**示例**:
```typescript
await waveformCanvasRef.value?.setEventsData(newEvents, {
  preserveSelection: true,
  forceRedraw: true
});
```

#### 2. updateEventsData - 增量数据更新

```typescript
async updateEventsData(
  updates: Array<{id: string, data: Partial<RenderableEvent>}>, 
  options?: BatchUpdateOptions
): Promise<void>
```

**功能**: 只修改指定事件的部分属性
**适用场景**: 批量更新多个事件的特定属性
**示例**:
```typescript
await waveformCanvasRef.value?.updateEventsData([
  {id: 'event1', data: {intensity: 80}},
  {id: 'event2', data: {frequency: 60}}
], {
  preserveSelection: true
});
```

#### 3. replaceEventsInRange - 范围替换

```typescript
async replaceEventsInRange(
  startTime: number, 
  endTime: number, 
  newEvents: RenderableEvent[], 
  options?: BatchUpdateOptions
): Promise<void>
```

**功能**: 替换指定时间范围内的所有事件
**适用场景**: 局部时间段的事件重新生成
**示例**:
```typescript
await waveformCanvasRef.value?.replaceEventsInRange(
  1000, 3000, newEvents, {
    preserveSelection: true
  }
);
```

#### 4. getCurrentEventsData - 获取当前数据

```typescript
getCurrentEventsData(): RenderableEvent[]
```

**功能**: 获取当前波形的所有事件数据
**返回**: 当前事件数组的副本

#### 5. forceRedraw - 强制重绘

```typescript
async forceRedraw(): Promise<void>
```

**功能**: 强制触发波形重绘
**适用场景**: 手动控制重绘时机

#### 6. validateEventsData - 数据验证

```typescript
validateEventsData(events: RenderableEvent[]): ValidationResult
```

**功能**: 验证事件数据的有效性
**返回**: 验证结果，包含错误信息

## 使用示例

### 基础用法

```vue
<template>
  <InteractiveWaveformCanvas
    ref="waveformCanvasRef"
    :fileUuid="currentFileUuid"
    :totalEffectDuration="totalDuration"
    :baselineDuration="baselineDuration"
    :availableParentWidth="containerWidth"
    @event-selected="handleEventSelected"
  />
</template>

<script setup>
import { ref } from 'vue';

const waveformCanvasRef = ref();

// 完整数据更新
const updateWaveformData = async (newEvents) => {
  if (waveformCanvasRef.value) {
    try {
      await waveformCanvasRef.value.setEventsData(newEvents, {
        preserveSelection: true,
        forceRedraw: true
      });
      console.log('波形数据更新成功');
    } catch (error) {
      console.error('波形数据更新失败:', error);
    }
  }
};

// 增量更新
const updateSpecificEvents = async (eventUpdates) => {
  if (waveformCanvasRef.value) {
    await waveformCanvasRef.value.updateEventsData(eventUpdates);
  }
};
</script>
```

### 高级用法

```typescript
// 批量操作示例
const performBatchOperations = async () => {
  const canvas = waveformCanvasRef.value;
  if (!canvas) return;

  try {
    // 1. 获取当前数据
    const currentEvents = canvas.getCurrentEventsData();
    
    // 2. 数据验证
    const validation = canvas.validateEventsData(newEvents);
    if (!validation.valid) {
      console.error('数据验证失败:', validation.errors);
      return;
    }
    
    // 3. 执行更新
    await canvas.setEventsData(newEvents, {
      preserveSelection: false,
      skipValidation: true, // 已经验证过了
      forceRedraw: true
    });
    
    // 4. 手动重绘（如果需要）
    await canvas.forceRedraw();
    
  } catch (error) {
    console.error('批量操作失败:', error);
  }
};
```

## 数据更新策略

### 1. 完整替换策略
- **触发条件**: 调用 `setEventsData`
- **更新方式**: 直接替换整个事件数组
- **性能影响**: 会触发完整的重绘
- **适用场景**: 数据完全变化时

### 2. 增量更新策略
- **触发条件**: 调用 `updateEventsData`
- **更新方式**: 只修改指定事件的属性
- **性能影响**: 相对较小的重绘开销
- **适用场景**: 部分事件属性调整

### 3. 智能差异策略
- **触发条件**: Store内部自动判断
- **更新方式**: 根据变化程度选择重绘策略
- **性能影响**: 自适应优化
- **适用场景**: 不确定变化范围时

## 性能优化

### 批量操作优化
- 使用 `BatchUpdateOptions.forceRedraw = false` 延迟重绘
- 多个连续操作后统一调用 `forceRedraw()`
- 利用 `skipValidation` 跳过重复验证

### 内存管理
- 事件数据采用浅拷贝策略
- 自动清理无效的选中状态
- 响应式更新机制避免内存泄漏

## 错误处理

### 数据验证错误
```typescript
try {
  await canvas.setEventsData(invalidEvents);
} catch (error) {
  if (error.message.includes('Invalid events data')) {
    // 处理数据验证错误
    console.error('数据格式错误:', error.message);
  }
}
```

### 异步操作错误
```typescript
try {
  await canvas.updateEventsData(updates);
} catch (error) {
  console.error('更新失败，可能的原因:', error);
  // 实施错误恢复策略
}
```

## 与现有功能的兼容性

### 交互功能保持
- ✅ 拖拽操作正常工作
- ✅ 事件选择功能保持
- ✅ 右键菜单功能正常
- ✅ 缩放和滚动状态维护

### 状态管理集成
- ✅ 文件未保存状态自动标记
- ✅ 撤销/重做系统兼容
- ✅ 多文件状态隔离

## 注意事项

1. **异步操作**: 所有更新方法都是异步的，需要使用 `await`
2. **数据验证**: 建议在生产环境中保持数据验证开启
3. **性能考虑**: 大量数据更新时考虑分批处理
4. **错误处理**: 务必添加适当的错误处理逻辑
5. **状态同步**: 更新后的数据会自动同步到文件状态管理器

## 示例项目

完整的使用示例请参考：
`src/components/editor/waveform/examples/ProgrammaticUpdateExample.vue`
