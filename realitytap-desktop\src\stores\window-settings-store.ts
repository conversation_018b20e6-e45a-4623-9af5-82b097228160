import { defineStore } from "pinia";
import { ref } from "vue";
import { invoke } from "@tauri-apps/api/core";
import { getCurrentWindow, PhysicalSize, PhysicalPosition } from "@tauri-apps/api/window";
import { logger, LogModule } from "@/utils/logger/logger";

// Window settings interface matching the Rust struct
export interface WindowSettings {
  width: number;
  height: number;
  x?: number;
  y?: number;
  maximized: boolean;
  lastSaved: string; // ISO date string
}

export const useWindowSettingsStore = defineStore("windowSettings", () => {
  const isLoading = ref(false);
  const currentSettings = ref<WindowSettings | null>(null);

  // Default settings matching Rust defaults
  const defaultSettings: WindowSettings = {
    width: 1280,
    height: 980,
    x: undefined,
    y: undefined,
    maximized: false,
    lastSaved: new Date().toISOString(),
  };

  // Load window settings from backend
  async function loadSettings(): Promise<WindowSettings> {
    isLoading.value = true;
    try {
      const settings = await invoke<WindowSettings>("get_window_settings");
      currentSettings.value = settings;
      return settings;
    } catch (error) {
      logger.error(LogModule.GENERAL, "Failed to load window settings", error);
      currentSettings.value = defaultSettings;
      return defaultSettings;
    } finally {
      isLoading.value = false;
    }
  }

  // Save window settings to backend
  async function saveSettings(settings: WindowSettings): Promise<void> {
    isLoading.value = true;
    try {
      await invoke("save_window_settings_command", { settings });
      currentSettings.value = settings;
    } catch (error) {
      logger.error(LogModule.GENERAL, "Failed to save window settings", error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  }

  // Get current window state and save it
  async function saveCurrentWindowState(): Promise<void> {
    try {
      const window = getCurrentWindow();

      // Get window size and position
      const size = await window.innerSize();
      const position = await window.innerPosition();
      const isMaximized = await window.isMaximized();

      const settings: WindowSettings = {
        width: size.width,
        height: size.height,
        x: position.x,
        y: position.y,
        maximized: isMaximized,
        lastSaved: new Date().toISOString(),
      };

      await saveSettings(settings);
    } catch (error) {
      logger.error(LogModule.GENERAL, "Failed to save current window state", error);
    }
  }

  // Apply settings to current window
  async function applySettings(settings: WindowSettings): Promise<void> {
    try {
      const window = getCurrentWindow();

      // If window should be maximized, maximize it directly
      // Don't set size/position as maximizing will override them
      if (settings.maximized) {
        await window.maximize();
      } else {
        // Only apply size and position if not maximized
        await window.setSize(new PhysicalSize(settings.width, settings.height));

        // Apply position if available
        if (settings.x !== undefined && settings.y !== undefined) {
          await window.setPosition(new PhysicalPosition(settings.x, settings.y));
        }
      }
    } catch (error) {
      logger.error(LogModule.GENERAL, "Failed to apply window settings", error);
      throw error;
    }
  }

  // Initialize window settings on app startup
  async function initializeWindowSettings(): Promise<void> {
    try {
      const settings = await loadSettings();

      // Apply settings if they exist and are not the exact default values
      // Check if settings have been saved before (not default timestamp)
      const hasCustomSettings = settings.lastSaved !== defaultSettings.lastSaved;

      if (hasCustomSettings) {
        // Add a small delay to ensure window is fully initialized
        await new Promise(resolve => setTimeout(resolve, 100));
        await applySettings(settings);
      }
    } catch (error) {
      logger.error(LogModule.GENERAL, "Failed to initialize window settings", error);
    }
  }

  // Setup window event listeners for automatic saving
  async function setupWindowListeners(): Promise<void> {
    try {
      const window = getCurrentWindow();

      // Debounce function to avoid too frequent saves
      let saveTimeout: number | null = null;
      const debouncedSave = () => {
        if (saveTimeout) {
          clearTimeout(saveTimeout);
        }
        saveTimeout = setTimeout(() => {
          saveCurrentWindowState();
        }, 500); // Save 500ms after last resize/move
      };

      // Listen for window resize events
      await window.onResized(() => {
        debouncedSave();
      });

      // Listen for window move events
      await window.onMoved(() => {
        debouncedSave();
      });
    } catch (error) {
      logger.error(LogModule.GENERAL, "Failed to setup window listeners", error);
    }
  }

  return {
    isLoading,
    currentSettings,
    loadSettings,
    saveSettings,
    saveCurrentWindowState,
    applySettings,
    initializeWindowSettings,
    setupWindowListeners,
  };
});
