[Unit]
Description=RealityTap OTA Server
Documentation=https://github.com/realitytap/realitytap-ota-server
Requires=docker.service
After=docker.service
Wants=network-online.target
After=network-online.target

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/realitytap-ota-server
ExecStart=/usr/local/bin/docker-compose -f docker/docker-compose.http.yml up -d
ExecStop=/usr/local/bin/docker-compose -f docker/docker-compose.http.yml down
ExecReload=/usr/local/bin/docker-compose -f docker/docker-compose.http.yml restart
TimeoutStartSec=0
Restart=on-failure
RestartSec=10

# Environment
Environment=COMPOSE_PROJECT_NAME=realitytap-ota

# Security
User=root
Group=docker

[Install]
WantedBy=multi-user.target
