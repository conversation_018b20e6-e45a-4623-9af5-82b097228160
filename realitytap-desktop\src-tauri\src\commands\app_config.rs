// Application configuration management commands
use crate::{
    commands::app_data::get_app_data_dir,
    error::{Error, Result},
};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use std::fs::{self, File};
use std::io::Write;
use std::path::PathBuf;
use std::sync::Mutex;
use tauri;


// Global mutex for thread-safe app config file writing
static APP_CONFIG_WRITE_MUTEX: Mutex<()> = Mutex::new(());

// Configuration file constants
const APP_CONFIG_FILE: &str = ".app_config.json";
const CONFIG_BACKUP_DIR: &str = "config/backups";

/// Get the application configuration file path
fn get_app_config_path() -> Result<PathBuf> {
    let app_data_dir = get_app_data_dir()?;
    Ok(app_data_dir.join(APP_CONFIG_FILE))
}

/// Get the configuration backup directory path
fn get_config_backup_dir() -> Result<PathBuf> {
    let app_data_dir = get_app_data_dir()?;
    let backup_dir = app_data_dir.join(CONFIG_BACKUP_DIR);
    
    // Ensure backup directory exists
    fs::create_dir_all(&backup_dir)
        .map_err(|e| Error::Io(format!("无法创建配置备份目录: {}", e)))?;
    
    Ok(backup_dir)
}

/// PlayEffectDialog configuration - simplified
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PlayEffectDialogConfig {
    pub last_selected_motor_id: Option<String>,
    pub last_selected_sampling_rate: u32,
}



/// Application configuration root structure - simplified
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct AppConfig {
    pub version: String,
    pub last_modified: String,
    pub play_effect_dialog: PlayEffectDialogConfig,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            version: "1.0.0".to_string(),
            last_modified: Utc::now().to_rfc3339(),
            play_effect_dialog: PlayEffectDialogConfig::default(),
        }
    }
}

impl Default for PlayEffectDialogConfig {
    fn default() -> Self {
        Self {
            last_selected_motor_id: None,
            last_selected_sampling_rate: 8, // SAMPLING_24KHZ - 默认高精度
        }
    }
}

/// Read application configuration from file
fn read_app_config() -> Result<AppConfig> {
    let config_path = get_app_config_path()?;
    
    if !config_path.exists() {
        // Create default configuration if file doesn't exist
        let default_config = AppConfig::default();
        save_app_config(&default_config)?;
        return Ok(default_config);
    }
    
    let content = fs::read_to_string(&config_path)
        .map_err(|e| Error::Io(format!("无法读取应用配置文件: {}", e)))?;
    
    let config: AppConfig = serde_json::from_str(&content)
        .map_err(|e| Error::Json(format!("解析应用配置文件失败: {}", e)))?;
    
    Ok(config)
}

/// Save application configuration to file with robust protection
fn save_app_config(config: &AppConfig) -> Result<()> {
    let config_path = get_app_config_path()?;
    
    // Ensure parent directory exists
    if let Some(parent) = config_path.parent() {
        fs::create_dir_all(parent)
            .map_err(|e| Error::Io(format!("无法创建配置目录: {}", e)))?;
    }
    
    // Thread-safe writing with JSON validation and retry mechanism
    let _lock = APP_CONFIG_WRITE_MUTEX.lock().map_err(|e| {
        Error::Io(format!("Failed to acquire app config write lock: {}", e))
    })?;
    
    // Update last modified time
    let mut updated_config = config.clone();
    updated_config.last_modified = Utc::now().to_rfc3339();
    
    // Step 1: Serialize to JSON with pretty formatting
    let json_content = serde_json::to_string_pretty(&updated_config)
        .map_err(|e| Error::Json(format!("序列化应用配置失败: {}", e)))?;
    
    // Step 2: Validate JSON by parsing it back
    let _: AppConfig = serde_json::from_str(&json_content)
        .map_err(|e| Error::Json(format!("生成的JSON无法反序列化: {}", e)))?;
    
    // Step 3: Write to file with explicit UTF-8 encoding
    let mut file = File::create(&config_path)
        .map_err(|e| Error::Io(format!("创建应用配置文件失败: {}", e)))?;
    
    file.write_all(json_content.as_bytes())
        .map_err(|e| Error::Io(format!("写入应用配置文件失败: {}", e)))?;
    
    log::info!("应用配置已保存到: {:?}", config_path);
    Ok(())
}

/// Create backup of current configuration
fn create_config_backup() -> Result<String> {
    let config_path = get_app_config_path()?;
    let backup_dir = get_config_backup_dir()?;
    
    if !config_path.exists() {
        return Err(Error::NotFound("配置文件不存在，无法创建备份".to_string()));
    }
    
    let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
    let backup_filename = format!("app_config_backup_{}.json", timestamp);
    let backup_path = backup_dir.join(&backup_filename);
    
    fs::copy(&config_path, &backup_path)
        .map_err(|e| Error::Io(format!("创建配置备份失败: {}", e)))?;
    
    log::info!("配置备份已创建: {:?}", backup_path);
    Ok(backup_path.to_string_lossy().to_string())
}

// === Tauri Commands ===

#[tauri::command]
pub async fn get_app_config() -> Result<AppConfig> {
    log::info!("获取应用配置");
    read_app_config()
}

#[tauri::command]
pub async fn save_app_config_command(config: AppConfig) -> Result<()> {
    log::info!("保存应用配置");
    save_app_config(&config)
}

#[tauri::command]
pub async fn reset_app_config() -> Result<AppConfig> {
    log::info!("重置应用配置为默认值");
    
    // Create backup before reset
    if let Err(e) = create_config_backup() {
        log::warn!("创建配置备份失败: {}", e);
    }
    
    let default_config = AppConfig::default();
    save_app_config(&default_config)?;
    Ok(default_config)
}

#[tauri::command]
pub async fn create_app_config_backup() -> Result<String> {
    log::info!("创建应用配置备份");
    create_config_backup()
}

#[tauri::command]
pub async fn update_play_effect_dialog_config(config: PlayEffectDialogConfig) -> Result<()> {
    log::info!("更新 PlayEffectDialog 配置");

    let mut app_config = read_app_config()?;
    app_config.play_effect_dialog = config;
    save_app_config(&app_config)
}

#[tauri::command]
pub async fn update_last_selected_motor(motor_id: String) -> Result<()> {
    log::info!("更新最后选择的马达: {}", motor_id);

    let mut app_config = read_app_config()?;
    app_config.play_effect_dialog.last_selected_motor_id = Some(motor_id);
    save_app_config(&app_config)
}

#[tauri::command]
pub async fn update_last_selected_sampling_rate(sampling_rate: u32) -> Result<()> {
    log::info!("更新最后选择的采样率: {}", sampling_rate);

    let mut app_config = read_app_config()?;
    app_config.play_effect_dialog.last_selected_sampling_rate = sampling_rate;
    save_app_config(&app_config)
}


