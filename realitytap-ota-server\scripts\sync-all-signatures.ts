#!/usr/bin/env tsx

/**
 * 同步所有现有文件的签名信息到数据库
 * 扫描文件系统中的所有.sig文件，并将签名内容更新到数据库中对应的记录
 */

import fs from 'fs-extra';
import path from 'path';
import sqlite3 from 'sqlite3';
import { promisify } from 'util';

// 配置
const CONFIG = {
  dbPath: './storage/database/ota.db',
  releasesPath: './storage/releases',
};

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

interface SignatureFile {
  signaturePath: string;
  installerFilename: string;
  channel: string;
  signature: string;
}

async function main() {
  log('🔄 开始同步所有签名文件到数据库...', 'blue');

  try {
    // 1. 连接数据库
    const db = new sqlite3.Database(CONFIG.dbPath);
    const dbAll = promisify(db.all.bind(db));
    const dbRun = promisify(db.run.bind(db));

    // 2. 扫描所有签名文件
    log('\n📁 扫描签名文件...', 'blue');
    const signatureFiles = await scanSignatureFiles();
    
    log(`找到 ${signatureFiles.length} 个签名文件`, 'yellow');

    if (signatureFiles.length === 0) {
      log('⚠️  没有找到任何签名文件', 'yellow');
      db.close();
      return;
    }

    // 3. 处理每个签名文件
    let updatedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const sigFile of signatureFiles) {
      log(`\n处理: ${sigFile.installerFilename}`, 'blue');
      log(`  渠道: ${sigFile.channel}`, 'blue');
      log(`  签名长度: ${sigFile.signature.length} 字符`, 'blue');

      try {
        // 查找数据库中对应的记录
        const records = await dbAll(`
          SELECT id, filename, signature 
          FROM platform_releases 
          WHERE filename = ?
        `, [sigFile.installerFilename]) as any[];

        if (records.length === 0) {
          log(`  ⚠️  数据库中未找到对应记录`, 'yellow');
          skippedCount++;
          continue;
        }

        if (records.length > 1) {
          log(`  ⚠️  找到多个匹配记录 (${records.length}个)，将更新所有记录`, 'yellow');
        }

        // 更新所有匹配的记录
        for (const record of records) {
          if (record.signature && record.signature.trim()) {
            log(`  ✅ 记录 ${record.id} 已有签名，跳过`, 'green');
            continue;
          }

          await dbRun(`
            UPDATE platform_releases 
            SET signature = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
          `, [sigFile.signature, record.id]);

          log(`  ✅ 记录 ${record.id} 签名已更新`, 'green');
          updatedCount++;
        }

      } catch (error) {
        log(`  ❌ 处理失败: ${error}`, 'red');
        errorCount++;
      }
    }

    // 4. 关闭数据库连接
    db.close();

    // 5. 输出结果
    log('\n📊 同步结果:', 'blue');
    log(`✅ 成功更新: ${updatedCount} 个记录`, 'green');
    log(`⚠️  跳过: ${skippedCount} 个文件`, 'yellow');
    log(`❌ 错误: ${errorCount} 个文件`, 'red');

    if (updatedCount > 0) {
      log('\n🎉 签名同步完成！建议重启OTA服务器以确保更改生效。', 'green');
    } else {
      log('\n⚠️  没有更新任何记录。', 'yellow');
    }

  } catch (error) {
    log(`❌ 同步过程中发生错误: ${error}`, 'red');
    process.exit(1);
  }
}

/**
 * 扫描所有签名文件
 */
async function scanSignatureFiles(): Promise<SignatureFile[]> {
  const signatureFiles: SignatureFile[] = [];

  try {
    // 扫描根目录
    await scanDirectory(CONFIG.releasesPath, 'stable', signatureFiles);

    // 扫描渠道目录
    const items = await fs.readdir(CONFIG.releasesPath);
    for (const item of items) {
      const itemPath = path.join(CONFIG.releasesPath, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isDirectory()) {
        await scanDirectory(itemPath, item, signatureFiles);
      }
    }
  } catch (error) {
    log(`扫描目录时发生错误: ${error}`, 'red');
  }

  return signatureFiles;
}

/**
 * 扫描指定目录中的签名文件
 */
async function scanDirectory(dirPath: string, channel: string, signatureFiles: SignatureFile[]): Promise<void> {
  try {
    const files = await fs.readdir(dirPath);
    
    for (const file of files) {
      if (file.endsWith('.sig')) {
        const signaturePath = path.join(dirPath, file);
        const installerFilename = file.replace('.sig', '');
        
        try {
          const signature = (await fs.readFile(signaturePath, 'utf8')).trim();
          
          if (signature) {
            signatureFiles.push({
              signaturePath,
              installerFilename,
              channel,
              signature
            });
          }
        } catch (error) {
          log(`读取签名文件失败: ${signaturePath} - ${error}`, 'red');
        }
      }
    }
  } catch (error) {
    log(`扫描目录失败: ${dirPath} - ${error}`, 'red');
  }
}

// 运行同步脚本
if (require.main === module) {
  main().catch(console.error);
}
