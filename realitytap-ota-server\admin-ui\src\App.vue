<template>
  <div v-if="appError" class="app-error">
    <h2>应用加载失败</h2>
    <p>{{ appError }}</p>
    <button @click="reloadApp">重新加载</button>
  </div>

  <n-config-provider v-else :theme="theme" :locale="zhCN" :date-locale="dateZhCN">
    <n-global-style />
    <n-loading-bar-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <n-message-provider>
            <router-view />
          </n-message-provider>
        </n-notification-provider>
      </n-dialog-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/stores/theme';
import {
  NConfigProvider,
  NDialogProvider,
  NGlobalStyle,
  NLoadingBarProvider,
  NMessageProvider,
  NNotificationProvider,
  darkTheme,
  dateZhCN,
  zhCN,
} from 'naive-ui';
import { computed, onErrorCaptured, ref } from 'vue';

console.log('📱 App.vue 组件开始初始化...');

const appError = ref<string | null>(null);

let themeStore: any = null;
let theme: any = null;

try {
  console.log('🎨 初始化主题 store...');
  themeStore = useThemeStore();

  theme = computed(() => {
    try {
      return themeStore.isDark ? darkTheme : null;
    } catch (error) {
      console.warn('主题计算出错，使用默认主题:', error);
      return null;
    }
  });

  console.log('✅ 主题 store 初始化成功');
} catch (error: any) {
  console.error('❌ 主题 store 初始化失败:', error);
  appError.value = `主题初始化失败: ${error.message}`;

  // 提供默认主题
  theme = computed(() => null);
}

// 捕获子组件错误
onErrorCaptured((error, instance, info) => {
  console.error('❌ 子组件错误:', error);
  console.error('📍 错误信息:', info);
  console.error('🔧 组件实例:', instance);

  appError.value = `组件错误: ${error.message}`;
  return false; // 阻止错误继续传播
});

const reloadApp = () => {
  window.location.reload();
};
</script>

<style>
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.app-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 20px;
  text-align: center;
  background: #f5f5f5;
  color: #333;
}

.app-error h2 {
  color: #e74c3c;
  margin-bottom: 16px;
}

.app-error button {
  margin-top: 16px;
  padding: 8px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.app-error button:hover {
  background: #2980b9;
}
</style>
