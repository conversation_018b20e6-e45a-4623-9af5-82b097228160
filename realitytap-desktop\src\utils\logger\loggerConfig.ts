/**
 * RealityTap Studio 统一日志管理配置
 * 
 * 功能特性：
 * - 环境变量控制日志级别
 * - 模块化日志分类
 * - 性能敏感区域特殊处理
 * - 生产环境自动优化
 */

// 日志级别枚举
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

// 日志模块分类
export enum LogModule {
  WAVEFORM = 'waveform',
  HISTORY = 'history',
  DEVICE = 'device',
  AUDIO = 'audio',
  PROJECT = 'project',
  PERFORMANCE = 'performance',
  DRAG = 'drag',
  HAPTIC = 'haptic',
  GENERAL = 'general'
}

// 日志配置接口
interface LoggerConfig {
  globalLevel: LogLevel;
  moduleSettings: Record<LogModule, LogLevel>;
  enablePerformanceLogging: boolean;
  enableDragLogging: boolean;
  maxLogBuffer: number;
  productionMode: boolean;
}

// 默认配置
const DEFAULT_CONFIG: LoggerConfig = {
  globalLevel: LogLevel.INFO, // 全局日志级别
  // 模块特定日志级别设置
  moduleSettings: {
    [LogModule.WAVEFORM]: LogLevel.DEBUG,     // 波形模块启用调试级别
    [LogModule.HISTORY]: LogLevel.WARN,      // 历史记录管理记录调试以上
    [LogModule.DEVICE]: LogLevel.INFO,        // 设备管理记录信息以上
    [LogModule.AUDIO]: LogLevel.WARN,         // 音频处理只记录警告以上
    [LogModule.PROJECT]: LogLevel.INFO,       // 项目管理记录信息以上
    [LogModule.PERFORMANCE]: LogLevel.NONE,   // 性能监控默认关闭
    [LogModule.DRAG]: LogLevel.WARN,          // 拖拽日志只记录警告以上
    [LogModule.HAPTIC]: LogLevel.INFO,        // 触觉反馈记录信息以上
    [LogModule.GENERAL]: LogLevel.INFO        // 通用日志记录信息以上
  },
  enablePerformanceLogging: false,
  enableDragLogging: false,
  maxLogBuffer: 1000,
  productionMode: import.meta.env.PROD
};

// 环境变量配置覆盖
const getConfigFromEnv = (): Partial<LoggerConfig> => {
  const config: Partial<LoggerConfig> = {};
  
  // 从环境变量读取全局日志级别
  const envLogLevel = import.meta.env.VITE_LOG_LEVEL;
  if (envLogLevel) {
    const level = LogLevel[envLogLevel.toUpperCase() as keyof typeof LogLevel];
    if (level !== undefined) {
      config.globalLevel = level;
    }
  }
  
  // 开发环境特殊配置
  if (import.meta.env.DEV) {
    config.productionMode = false;
    config.enablePerformanceLogging = import.meta.env.VITE_ENABLE_PERFORMANCE_LOG === 'true';
    config.enableDragLogging = import.meta.env.VITE_ENABLE_DRAG_LOG === 'true';
  }
  
  return config;
};

// 合并配置
export const LOGGER_CONFIG: LoggerConfig = {
  ...DEFAULT_CONFIG,
  ...getConfigFromEnv()
};

// 性能敏感模块列表
export const PERFORMANCE_SENSITIVE_MODULES = [
  LogModule.WAVEFORM,
  LogModule.DRAG,
  LogModule.PERFORMANCE
];

// 高频调用模块列表
export const HIGH_FREQUENCY_MODULES = [
  LogModule.WAVEFORM,
  LogModule.DRAG,
  LogModule.AUDIO
];

/**
 * 检查是否应该记录日志
 * @param module 日志模块
 * @param level 日志级别
 * @returns 是否应该记录
 */
export const shouldLog = (module: LogModule, level: LogLevel): boolean => {
  // 生产环境下，性能敏感模块只记录错误
  if (LOGGER_CONFIG.productionMode && PERFORMANCE_SENSITIVE_MODULES.includes(module)) {
    return level >= LogLevel.ERROR;
  }
  
  // 检查模块特定设置
  const moduleLevel = LOGGER_CONFIG.moduleSettings[module];
  if (moduleLevel !== undefined) {
    return level >= moduleLevel;
  }
  
  // 使用全局设置
  return level >= LOGGER_CONFIG.globalLevel;
};

/**
 * 格式化日志消息
 * @param module 日志模块
 * @param level 日志级别
 * @param message 消息内容
 * @param data 附加数据
 * @returns 格式化后的消息
 */
export const formatLogMessage = (
  module: LogModule,
  level: LogLevel,
  message: string,
  data?: any
): string => {
  const timestamp = new Date().toISOString().slice(11, 23); // HH:mm:ss.SSS
  const levelStr = LogLevel[level].padEnd(5);
  const moduleStr = module.toUpperCase().padEnd(11);
  
  let formattedMessage = `[${timestamp}] ${levelStr} [${moduleStr}] ${message}`;
  
  // 在开发环境下添加更多详细信息
  if (!LOGGER_CONFIG.productionMode && data !== undefined) {
    formattedMessage += ` | Data: ${JSON.stringify(data)}`;
  }
  
  return formattedMessage;
};

/**
 * 日志缓冲区管理
 */
class LogBuffer {
  private buffer: string[] = [];
  private maxSize: number;
  
  constructor(maxSize: number = LOGGER_CONFIG.maxLogBuffer) {
    this.maxSize = maxSize;
  }
  
  add(message: string): void {
    this.buffer.push(message);
    if (this.buffer.length > this.maxSize) {
      this.buffer.shift();
    }
  }
  
  getAll(): string[] {
    return [...this.buffer];
  }
  
  clear(): void {
    this.buffer.length = 0;
  }
  
  export(): string {
    return this.buffer.join('\n');
  }
}

export const logBuffer = new LogBuffer();

/**
 * 性能监控装饰器
 * 用于自动记录函数执行时间
 */
export function logPerformance(module: LogModule = LogModule.PERFORMANCE) {
  return function (_target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    if (!LOGGER_CONFIG.enablePerformanceLogging) {
      return descriptor;
    }
    
    const originalMethod = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      const startTime = performance.now();
      const result = originalMethod.apply(this, args);
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const endTime = performance.now();
          const duration = endTime - startTime;
          if (shouldLog(module, LogLevel.DEBUG)) {
            console.debug(formatLogMessage(
              module,
              LogLevel.DEBUG,
              `${propertyKey} 执行时间: ${duration.toFixed(2)}ms`
            ));
          }
        });
      } else {
        const endTime = performance.now();
        const duration = endTime - startTime;
        if (shouldLog(module, LogLevel.DEBUG)) {
          console.debug(formatLogMessage(
            module,
            LogLevel.DEBUG,
            `${propertyKey} 执行时间: ${duration.toFixed(2)}ms`
          ));
        }
        return result;
      }
    };
    
    return descriptor;
  };
}
