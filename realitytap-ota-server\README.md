# RealityTap OTA 更新服务器

## 项目概述

RealityTap OTA (Over-The-Air) 更新服务器是为 RealityTap 桌面应用提供自动更新服务的**高效后端系统**。该服务器专注于核心功能，采用 SQLite 数据库存储，提供高性能的数据访问和并发安全性，确保易于维护和部署。

### 核心功能
- 🔍 **版本检查**: 支持多平台、多架构的版本比较和更新检测
- 📦 **文件分发**: 高效的更新包下载服务
- 🔒 **安全验证**: 文件完整性校验（SHA256）
- 📊 **简单统计**: 基于日志的更新统计
- 🚀 **多渠道支持**: stable/beta/alpha 发布渠道管理

### 设计原则
- **高效架构**: 使用 SQLite 数据库提供高性能数据访问
- **数据库存储**: 采用 SQLite 存储元数据，确保数据一致性和并发安全
- **零配置启动**: 开箱即用，数据库自动初始化
- **容器友好**: 单一进程，易于容器化部署

## 技术架构

### 极简技术栈

基于 RealityTap 生态系统的技术栈分析和简化需求，OTA 服务器采用以下**极简架构**：

#### 后端框架: **Node.js + TypeScript + Express**
**选择理由:**
- 与现有 TypeScript 技术栈保持一致，便于代码共享和维护
- Express 成熟稳定，社区支持丰富，学习成本低
- 与现有 @realitytap/shared 模块完美集成
- 简单直接，无需复杂的插件系统

#### 数据存储: **文件系统 + JSON**
**选择理由:**
- 无需数据库，降低部署和维护复杂度
- JSON 文件易于阅读和调试
- 版本控制友好，可直接纳入 Git 管理
- 支持原子性文件操作，保证数据一致性

#### 文件存储: **本地文件系统**
**选择理由:**
- 简单直接，无需对象存储服务
- 支持断点续传和范围请求
- 易于备份和迁移
- 可通过反向代理实现 CDN 功能

### 架构图

```
┌─────────────────┐     ┌─────────────────┐
│   RealityTap         │     │        OTA Server    │
│   Desktop App        │◄──►│        (Node.js)     │
└─────────────────┘     └─────────────────┘
                                │
                        ┌───────▼───────┐
                        │  File System       │
                        │   Storage          │
                        │                    │
                        │ ├── releases/     │
                        │ ├── metadata/     │
                        │ └── logs/         │
                        └───────────────┘
```

## 项目结构

### 极简目录结构
```
realitytap-ota-server/
├── src/                             # 源代码
│   ├── controllers/                 # API 控制器
│   │   ├── version.controller.ts    # 版本检查 API
│   │   ├── download.controller.ts   # 文件下载 API
│   │   └── health.controller.ts     # 健康检查 API
│   ├── services/                    # 业务逻辑服务
│   │   ├── version.service.ts       # 版本管理逻辑
│   │   ├── file.service.ts          # 文件管理服务
│   │   └── logger.service.ts        # 日志服务
│   ├── middleware/                  # 中间件
│   │   ├── cors.middleware.ts       # CORS 中间件
│   │   ├── error.middleware.ts      # 错误处理中间件
│   │   └── rate-limit.middleware.ts # 限流中间件
│   ├── utils/                       # 工具函数
│   │   ├── crypto.util.ts           # 加密工具
│   │   ├── version.util.ts          # 版本比较工具
│   │   └── file.util.ts             # 文件处理工具
│   ├── types/                       # 类型定义
│   │   └── server.types.ts          # 服务器专用类型
│   ├── config/                      # 配置文件
│   │   └── server.config.ts         # 服务器配置
│   └── app.ts                       # 应用入口
├── storage/                         # 数据存储目录
│   ├── releases/                    # 发布文件存储
│   │   ├── stable/                  # 稳定版本
│   │   ├── beta/                    # 测试版本
│   │   └── alpha/                   # 开发版本
│   ├── database/                    # 数据库文件
│   │   └── ota.db                   # SQLite 数据库
│   ├── backups/                     # 数据库备份
│   └── logs/                        # 日志文件
├── scripts/                         # 脚本文件
│   ├── start.sh                     # 启动脚本
│   └── deploy.sh                    # 部署脚本
├── package.json                     # NPM 配置
├── tsconfig.json                    # TypeScript 配置
├── Dockerfile                       # Docker 配置
├── docker-compose.yml               # Docker Compose 配置
└── README.md                        # 项目说明
```

## 核心功能模块

### 1. 版本管理模块
- **版本比较**: 使用语义化版本比较算法
- **发布渠道**: 支持 stable/beta/alpha 三个发布渠道
- **平台支持**: Windows、macOS、Linux 多平台支持
- **架构支持**: x86_64、aarch64 多架构支持
- **Tauri 兼容**: 完全兼容 tauri-plugin-updater 规范

### 2. 文件分发模块
- **文件下载**: 支持 HTTP 范围请求和断点续传
- **完整性校验**: SHA256 文件完整性验证
- **压缩传输**: 支持 gzip 压缩减少传输大小
- **并发控制**: 限制同时下载连接数

### 3. 安全验证模块
- **文件校验**: SHA256 哈希值验证
- **数字签名**: Ed25519 签名验证（Tauri 兼容）
- **访问控制**: 基于 IP 的简单访问控制
- **限流保护**: 防止恶意请求和 DDoS 攻击
- **CORS 配置**: 跨域资源共享安全策略

### 4. 日志统计模块
- **访问日志**: 记录所有 API 请求
- **下载统计**: 统计下载次数和流量
- **错误监控**: 记录和分析错误信息
- **性能指标**: 响应时间和吞吐量统计

## API 接口设计

### Tauri Plugin Updater 兼容 API (推荐)
```http
GET /api/v1/updates/{target}/{current_version}?channel=stable
```

**支持的 target 格式:**
- `windows-x86_64` - Windows 64位
- `windows-i686` - Windows 32位
- `darwin-x86_64` - macOS Intel
- `darwin-aarch64` - macOS Apple Silicon
- `linux-x86_64` - Linux 64位
- `linux-aarch64` - Linux ARM64

**有更新时响应 (200 OK):**
```json
{
  "url": "https://releases.realitytap.com/api/v1/download/realitytap-1.1.0-windows-x86_64.msi",
  "version": "1.1.0",
  "notes": "修复了若干问题，提升了性能",
  "pub_date": "2024-01-15T10:30:00Z",
  "signature": "dW50cnVzdGVkIGNvbW1lbnQ6IHNpZ25hdHVyZSBmcm9tIG1pbmlzaWduIHNlY3JldCBrZXkK..."
}
```

**无更新时响应 (204 No Content):**
```
(空响应体)
```

### 管理 API
```http
# 获取所有可用版本信息
GET /api/v1/version/available

# 获取渠道信息
GET /api/v1/version/channels

# 获取特定渠道的最新版本
GET /api/v1/version/latest/{channel}

# 获取版本统计信息
GET /api/v1/version/stats

# 清除版本缓存
POST /api/v1/version/cache/clear
```

### 文件下载 API
```http
GET /api/v1/download/{filename}
Range: bytes=0-1023  # 可选，支持断点续传
```

### 健康检查 API
```http
GET /health
```

**响应示例:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "uptime": 3600,
  "version": "1.0.0"
}
```

## 数据存储设计

### 版本信息存储 (storage/metadata/versions.json)
```json
{
  "channels": {
    "stable": {
      "version": "1.0.0",
      "platforms": {
        "windows": {
          "x86_64": {
            "filename": "realitytap-1.0.0-windows-x86_64.exe",
            "size": 52428800,
            "checksum": "sha256:abc123...",
            "releaseDate": "2024-01-15T10:00:00Z",
            "releaseNotes": "稳定版本发布"
          }
        },
        "macos": {
          "x86_64": {
            "filename": "realitytap-1.0.0-macos-x86_64.dmg",
            "size": 48234567,
            "checksum": "sha256:def456...",
            "releaseDate": "2024-01-15T10:00:00Z",
            "releaseNotes": "稳定版本发布"
          },
          "aarch64": {
            "filename": "realitytap-1.0.0-macos-aarch64.dmg",
            "size": 45123456,
            "checksum": "sha256:ghi789...",
            "releaseDate": "2024-01-15T10:00:00Z",
            "releaseNotes": "稳定版本发布"
          }
        }
      }
    },
    "beta": {
      "version": "1.1.0-beta.1",
      "platforms": {
        // 类似结构
      }
    }
  },
  "minimumVersions": {
    "stable": "0.9.0",
    "beta": "1.0.0",
    "alpha": "1.0.0"
  },
  "deprecatedVersions": ["0.8.0", "0.8.1"]
}
```

### 渠道配置存储 (storage/metadata/channels.json)
```json
{
  "stable": {
    "enabled": true,
    "description": "稳定版本，推荐生产环境使用",
    "autoUpdate": true,
    "rolloutPercentage": 100
  },
  "beta": {
    "enabled": true,
    "description": "测试版本，包含最新功能",
    "autoUpdate": false,
    "rolloutPercentage": 50
  },
  "alpha": {
    "enabled": false,
    "description": "开发版本，仅供内部测试",
    "autoUpdate": false,
    "rolloutPercentage": 10
  }
}
```

## 数据存储架构

### SQLite 数据库
项目已从 JSON 文件存储迁移到 SQLite 数据库，提供以下优势：

- **数据一致性**: 事务支持确保数据完整性
- **并发安全**: 支持多用户同时访问
- **查询性能**: 索引优化提高查询速度
- **数据备份**: 自动备份和恢复机制

### 数据库表结构
- `channels` - 发布渠道配置
- `versions` - 版本信息
- `platform_releases` - 平台发布详情
- `system_config` - 系统配置
- `download_records` - 下载统计
- `minimum_versions` - 最小版本要求
- `deprecated_versions` - 废弃版本信息

### 数据库管理
```bash
# 备份数据库
npx tsx scripts/backup-database.ts

# 验证数据完整性
npx tsx scripts/final-verification.ts

# 清理存储空间
npx tsx scripts/cleanup-storage.ts safe --dry-run
```

## 开发环境搭建

### 前置要求
- Node.js 18+
- npm 或 yarn
- TypeScript 5+

### 快速开始

1. **安装依赖**
```bash
cd realitytap-ota-server
npm install
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

3. **启动开发服务器**
```bash
# 开发模式（热重载）
npm run dev

# 或者构建后启动
npm run build
npm start
```

4. **验证服务**
```bash
# 检查服务状态
curl http://localhost:3000/health

# 测试版本检查 API
curl -X POST http://localhost:3000/api/v1/version/check \
  -H "Content-Type: application/json" \
  -d '{
    "currentVersion": "1.0.0",
    "platform": "windows",
    "architecture": "x86_64",
    "channel": "stable"
  }'
```

### 环境配置

#### 基础配置 (.env)
```bash
# 服务器配置
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# 存储配置
STORAGE_PATH=./storage
RELEASES_PATH=./storage/releases
METADATA_PATH=./storage/metadata
LOGS_PATH=./storage/logs

# 安全配置
CORS_ORIGIN=*

# 速率限制配置
RATE_LIMIT_WINDOW_MS=300000  # 5分钟（开发环境）
RATE_LIMIT_MAX_REQUESTS=500  # 每个窗口最大请求数

# 管理API速率限制配置
ADMIN_RATE_LIMIT_WINDOW_MS=300000  # 5分钟
ADMIN_RATE_LIMIT_MAX_REQUESTS=500  # 管理API请求限制

# 公共API速率限制配置
PUBLIC_RATE_LIMIT_WINDOW_MS=300000  # 5分钟
PUBLIC_RATE_LIMIT_MAX_REQUESTS=200  # 公共API请求限制

# 速率限制IP白名单
RATE_LIMIT_WHITELIST_IPS=127.0.0.1,::1,localhost

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
```

#### 生产环境配置
```bash
# 服务器配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 安全配置
CORS_ORIGIN=https://realitytap.com

# 速率限制配置（生产环境更严格）
RATE_LIMIT_WINDOW_MS=900000  # 15分钟
RATE_LIMIT_MAX_REQUESTS=100

# 管理API速率限制配置
ADMIN_RATE_LIMIT_WINDOW_MS=900000  # 15分钟
ADMIN_RATE_LIMIT_MAX_REQUESTS=300  # 生产环境管理API限制

# 公共API速率限制配置
PUBLIC_RATE_LIMIT_WINDOW_MS=900000  # 15分钟
PUBLIC_RATE_LIMIT_MAX_REQUESTS=100  # 生产环境公共API限制

# 速率限制IP白名单（可配置管理员IP）
# RATE_LIMIT_WHITELIST_IPS=*************,*********

# 日志配置
LOG_LEVEL=warn
LOG_FORMAT=json
```

## 部署指南

### Docker 部署（推荐）

1. **构建镜像**
```bash
docker build -t realitytap-ota-server:latest .
```

2. **运行容器**
```bash
docker run -d \
  --name realitytap-ota \
  -p 3000:3000 \
  -v $(pwd)/storage:/app/storage \
  -e NODE_ENV=production \
  realitytap-ota-server:latest
```

3. **使用 Docker Compose**
```bash
docker-compose up -d
```

### 传统部署

1. **构建项目**
```bash
npm run build
```

2. **启动服务**
```bash
# 使用 PM2 管理进程
npm install -g pm2
pm2 start ecosystem.config.js

# 或直接启动
npm run start:prod
```

### 反向代理配置

#### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name ota.realitytap.com;

    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ota.realitytap.com;

    # SSL 配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    # 上传大小限制
    client_max_body_size 100M;

    # 代理到 OTA 服务器
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 文件下载优化
    location /api/v1/download/ {
        proxy_pass http://localhost:3000;
        proxy_buffering off;
        proxy_request_buffering off;
    }
}
```

## 运维和监控

### 健康检查

服务器提供多层次的健康检查：

```bash
# 基础健康检查
curl http://localhost:3000/health

# 详细健康检查（包含存储状态）
curl http://localhost:3000/health/detailed
```

### 日志管理

#### 日志级别
- **ERROR**: 系统错误和异常
- **WARN**: 警告信息和潜在问题
- **INFO**: 一般信息和操作记录
- **DEBUG**: 调试信息（仅开发环境）

#### 日志文件
```
storage/logs/
├── access.log          # 访问日志
├── error.log           # 错误日志
├── download.log        # 下载统计日志
└── app.log             # 应用日志
```

#### 日志轮转
```bash
# 使用 logrotate 配置
/etc/logrotate.d/realitytap-ota
```

### 性能监控

#### 关键指标
- **响应时间**: API 请求响应时间
- **吞吐量**: 每秒处理的请求数
- **错误率**: 4xx/5xx 错误比例
- **下载速度**: 文件下载传输速度
- **存储使用**: 磁盘空间使用情况

#### 监控脚本
```bash
# 检查服务状态
./scripts/health-check.sh

# 查看性能统计
./scripts/stats.sh

# 清理旧日志
./scripts/cleanup-logs.sh
```

### 备份和恢复

#### 备份策略
```bash
# 备份元数据
tar -czf backup-metadata-$(date +%Y%m%d).tar.gz storage/metadata/

# 备份发布文件（可选，文件较大）
tar -czf backup-releases-$(date +%Y%m%d).tar.gz storage/releases/

# 自动备份脚本
./scripts/backup.sh
```

#### 恢复流程
```bash
# 停止服务
pm2 stop realitytap-ota

# 恢复元数据
tar -xzf backup-metadata-20240115.tar.gz

# 重启服务
pm2 start realitytap-ota
```

### 版本发布流程

#### 1. 准备发布文件
```bash
# 创建发布目录
mkdir -p storage/releases/stable/1.1.0/

# 复制发布文件
cp realitytap-1.1.0-*.* storage/releases/stable/1.1.0/

# 计算文件校验和
sha256sum storage/releases/stable/1.1.0/* > checksums.txt
```

#### 2. 更新版本信息
```bash
# 编辑版本配置
nano storage/metadata/versions.json

# 验证配置格式
npm run validate-config
```

#### 3. 测试发布
```bash
# 测试版本检查
curl -X POST http://localhost:3000/api/v1/version/check \
  -H "Content-Type: application/json" \
  -d '{"currentVersion":"1.0.0","platform":"windows","architecture":"x86_64","channel":"stable"}'

# 测试文件下载
curl -I http://localhost:3000/api/v1/download/realitytap-1.1.0-windows-x86_64.exe
```

#### 4. 灰度发布
```bash
# 设置发布百分比
# 编辑 storage/metadata/channels.json
{
  "stable": {
    "rolloutPercentage": 10  # 先发布给 10% 的用户
  }
}

# 逐步增加发布比例
# 10% -> 25% -> 50% -> 100%
```

## 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口占用
netstat -tlnp | grep 3000

# 检查日志
tail -f storage/logs/error.log

# 检查配置文件
npm run validate-config
```

#### 2. 文件下载失败
```bash
# 检查文件权限
ls -la storage/releases/

# 检查磁盘空间
df -h

# 检查文件完整性
sha256sum storage/releases/stable/1.0.0/*
```

#### 3. 版本检查异常
```bash
# 验证版本配置
cat storage/metadata/versions.json | jq .

# 检查文件路径
find storage/releases/ -name "*.exe" -o -name "*.dmg"
```

### 性能优化

#### 1. 文件服务优化
- 启用 gzip 压缩
- 设置适当的缓存头
- 使用 CDN 加速下载

#### 2. 内存优化
- 限制并发下载数
- 使用流式文件传输
- 定期清理临时文件

#### 3. 网络优化
- 配置反向代理
- 启用 HTTP/2
- 优化 TCP 参数

## 安全考虑

### 文件完整性
- **SHA256 校验**: 所有发布文件必须提供校验和
- **文件签名**: 建议对发布文件进行数字签名
- **访问控制**: 限制文件上传和修改权限

### API 安全
- **HTTPS 强制**: 生产环境必须使用 HTTPS
- **限流保护**: 防止 API 滥用和 DDoS 攻击
- **CORS 配置**: 严格控制跨域访问
- **输入验证**: 验证所有 API 输入参数

### 基础设施安全
- **防火墙配置**: 只开放必要的端口
- **定期更新**: 及时更新依赖包和系统补丁
- **访问日志**: 记录所有访问和操作日志
- **备份加密**: 敏感数据备份加密存储

---

## 总结

RealityTap OTA 服务器采用高效的 SQLite 数据库架构，具有以下优势：

### ✅ 优势
- **高性能存储**: SQLite 数据库提供高效的数据访问
- **数据一致性**: 事务支持确保数据完整性和并发安全
- **部署便捷**: 单一进程，内嵌数据库，容器化友好
- **成本低廉**: 无需额外的数据库服务器
- **扩展灵活**: 可根据需要逐步增加功能
- **备份简单**: 单文件数据库，备份和恢复便捷

### 🔄 扩展方向
- **缓存层**: 高并发场景下可添加 Redis 缓存
- **CDN 集成**: 全球用户可考虑 CDN 加速
- **监控系统**: 集成 Prometheus + Grafana 监控
- **管理界面**: 开发 Web 管理后台
- **数据库升级**: 需要时可迁移到 PostgreSQL 或 MySQL

这个高效的数据库架构能够满足 RealityTap 桌面应用的 OTA 更新需求，同时提供优秀的性能和可维护性。