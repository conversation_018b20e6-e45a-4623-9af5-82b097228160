# RealityTap 外部安装器构建脚本（简化版）
# 用于构建独立的安装器程序

param(
    [string]$Configuration = "release",
    [switch]$ForBundle = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取脚本目录和项目根目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$InstallerDir = Join-Path $ProjectRoot "installer"

Write-Host "🚀 RealityTap 外部安装器构建脚本" -ForegroundColor Green
Write-Host "📁 项目根目录: $ProjectRoot" -ForegroundColor Cyan
Write-Host "📦 安装器源码: $InstallerDir" -ForegroundColor Cyan
Write-Host "⚙️ 构建配置: $Configuration" -ForegroundColor Cyan

# 检查安装器目录是否存在
if (-not (Test-Path $InstallerDir)) {
    Write-Host "❌ 错误: 安装器目录不存在: $InstallerDir" -ForegroundColor Red
    exit 1
}

# 检查 Cargo.toml 是否存在
$CargoToml = Join-Path $InstallerDir "Cargo.toml"
if (-not (Test-Path $CargoToml)) {
    Write-Host "❌ 错误: Cargo.toml 不存在: $CargoToml" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 安装器项目验证通过" -ForegroundColor Green

# 切换到安装器目录
Set-Location $InstallerDir

try {
    # 构建安装器
    Write-Host "🔨 开始构建安装器..." -ForegroundColor Yellow
    
    if ($Configuration -eq "release") {
        Write-Host "🔧 执行命令: cargo build --release" -ForegroundColor Cyan
        cargo build --release
    } else {
        Write-Host "🔧 执行命令: cargo build" -ForegroundColor Cyan
        cargo build
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "构建失败，退出代码: $LASTEXITCODE"
    }
    
    Write-Host "✅ 安装器构建成功" -ForegroundColor Green
    
    # 确定源文件路径
    $SourceBinDir = if ($Configuration -eq "release") {
        Join-Path $InstallerDir "target/release"
    } else {
        Join-Path $InstallerDir "target/debug"
    }
    
    $ExeName = "updater.exe"
    $SourceExe = Join-Path $SourceBinDir $ExeName
    
    if (-not (Test-Path $SourceExe)) {
        Write-Host "❌ 错误: 构建产物不存在: $SourceExe" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ 找到构建产物: $SourceExe" -ForegroundColor Green
    
    # 如果是为打包准备，复制到 src-tauri 目录
    if ($ForBundle) {
        $BundleTargetDir = Join-Path $ProjectRoot "src-tauri/installer"
        $BundleTargetExe = Join-Path $BundleTargetDir $ExeName
        
        Write-Host "📦 为打包复制到: $BundleTargetExe" -ForegroundColor Cyan
        
        # 确保目录存在
        if (-not (Test-Path $BundleTargetDir)) {
            New-Item -ItemType Directory -Path $BundleTargetDir -Force | Out-Null
            Write-Host "📁 创建目录: $BundleTargetDir" -ForegroundColor Yellow
        }
        
        Copy-Item $SourceExe $BundleTargetExe -Force
        Write-Host "✅ 已复制到打包目录" -ForegroundColor Green
        
        # 验证复制结果
        if (Test-Path $BundleTargetExe) {
            $FileInfo = Get-Item $BundleTargetExe
            $FileSize = [math]::Round($FileInfo.Length / 1MB, 2)
            Write-Host "📊 文件大小: $FileSize MB" -ForegroundColor Cyan
            Write-Host "📅 修改时间: $($FileInfo.LastWriteTime)" -ForegroundColor Cyan
        } else {
            Write-Host "❌ 错误: 复制失败，目标文件不存在" -ForegroundColor Red
            exit 1
        }
    }
    
    # 复制到默认输出目录
    $DefaultOutputDir = Join-Path $ProjectRoot "src-tauri/installer"
    $DefaultTargetExe = Join-Path $DefaultOutputDir $ExeName
    
    if (-not (Test-Path $DefaultOutputDir)) {
        New-Item -ItemType Directory -Path $DefaultOutputDir -Force | Out-Null
    }
    
    Copy-Item $SourceExe $DefaultTargetExe -Force
    Write-Host "✅ 已复制到默认输出目录: $DefaultTargetExe" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 构建失败: $_" -ForegroundColor Red
    Set-Location $ProjectRoot
    exit 1
} finally {
    # 返回项目根目录
    Set-Location $ProjectRoot
}

Write-Host ""
Write-Host "🎉 安装器构建完成！" -ForegroundColor Green
Write-Host "📁 源文件: $SourceExe" -ForegroundColor Cyan

if ($ForBundle) {
    Write-Host "📦 打包文件: $BundleTargetExe" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "💡 下一步: 运行 npm run tauri build 构建主应用程序" -ForegroundColor Yellow
Write-Host ""
