import type { RenderableEvent } from "@/types/haptic-editor";
import { useWaveformSelection } from "./useWaveformSelection";
import { useFileWaveformEditorStore } from "@/stores/haptics-editor-store";

/**
 * 波形图点击事件处理 Composable
 * 负责处理画布点击、右键菜单等交互事件
 *
 * @param waveformStore 文件级别的 waveform store 实例（必需）
 */
export function useWaveformClickHandler(waveformStore: ReturnType<typeof useFileWaveformEditorStore>) {
  const selection = useWaveformSelection(waveformStore);

  /**
   * 处理画布点击事件
   */
  const handleCanvasClick = (
    mouseEvent: MouseEvent,
    canvas: HTMLCanvasElement,
    events: RenderableEvent[],
    mapTimeToX: (time: number) => number,
    mapIntensityToY: (intensity: number) => number,
    emit: (event: string, ...args: any[]) => void,
    isDragging: boolean,
    longPressTimer: any,
    updateVirtualScrollOffset: () => void,
    smartDrawWaveform: (forceRedraw?: boolean) => void,
    resetDragState: () => void
  ) => {
    // 检查是否是拖动操作后立即发生的 "幽灵"点击
    if (selection.shouldIgnoreClick()) {
      return;
    }

    if (isDragging) {
      return;
    }

    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }

    // 强制同步虚拟滚动偏移量和Canvas位置
    updateVirtualScrollOffset();

    if (!canvas || !events) return;

    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;
    const cssX = mouseEvent.clientX - rect.left;
    const cssY = mouseEvent.clientY - rect.top;
    const clickX = cssX * dpr;
    const clickY = cssY * dpr;

    // 执行命中检测
    const hitResult = selection.performHitTest(clickX, clickY, events, mapTimeToX, mapIntensityToY);

    // 处理选择逻辑
    selection.handleEventSelection(hitResult, emit);

    // 强制重绘以立即显示选择状态
    smartDrawWaveform(true);

    // 重置拖动状态
    resetDragState();
  };

  /**
   * 处理右键菜单事件
   */
  const handleCanvasContextMenu = (
    mouseEvent: MouseEvent,
    canvas: HTMLCanvasElement,
    events: RenderableEvent[],
    mapTimeToX: (time: number) => number,
    mapIntensityToY: (intensity: number) => number,
    convertXToTime: (x: number) => number,
    closeAddMenu: () => void
  ) => {
    mouseEvent.preventDefault();
    mouseEvent.stopPropagation();

    if (!canvas) return null;

    const rect = canvas.getBoundingClientRect();
    const cssX = mouseEvent.clientX - rect.left;
    const cssY = mouseEvent.clientY - rect.top;
    const dpr = window.devicePixelRatio || 1;
    const clickX = cssX * dpr;
    const clickY = cssY * dpr;
    const timePoint = convertXToTime(cssX);

    closeAddMenu();

    // 执行命中检测
    const hitResult = selection.handleContextMenuHitTest(clickX, clickY, events, mapTimeToX, mapIntensityToY);

    return {
      hitResult,
      timePoint,
      position: {
        x: mouseEvent.clientX + 5, // 向右偏移5px，避免覆盖垂直辅助线
        y: mouseEvent.clientY + 5, // 向下偏移5px，避免覆盖水平辅助线
      },
      canvasPosition: {
        x: clickX,
        y: clickY,
      },
    };
  };

  /**
   * 处理文档点击事件（用于关闭菜单）
   */
  const handleDocumentClick = (event: MouseEvent, isAddMenuVisible: boolean, waveformCanvas: HTMLCanvasElement | null, closeAddMenu: () => void) => {
    // 只有当菜单已显示时才处理
    if (isAddMenuVisible) {
      // 检查点击是否在菜单外部
      const menu = document.getElementById("add-event-menu");
      // 确保不是从菜单触发的点击事件，或者来自画布本身
      if (menu && !menu.contains(event.target as Node) && !(waveformCanvas && waveformCanvas.contains(event.target as Node))) {
        closeAddMenu();
      }
    }
  };

  return {
    // 选择相关状态和方法
    ...selection,

    // 点击处理方法
    handleCanvasClick,
    handleCanvasContextMenu,
    handleDocumentClick,
  };
}
