# RealityTap OTA 服务器部署指南

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [开发环境部署](#开发环境部署)
- [生产环境部署](#生产环境部署)
- [Docker部署](#docker部署)
- [配置说明](#配置说明)
- [发布管理](#发布管理)
- [监控和维护](#监控和维护)
- [故障排查](#故障排查)

## 🔧 系统要求

### 最低要求
- **Node.js**: 18.0.0 或更高版本
- **NPM**: 9.0.0 或更高版本
- **内存**: 512MB RAM
- **存储**: 10GB 可用空间（用于存储发布文件）
- **网络**: 稳定的网络连接

### 推荐配置
- **Node.js**: 20.x LTS
- **内存**: 2GB RAM
- **存储**: 50GB SSD
- **CPU**: 2核心或更多

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/awa/realitytap-ecosystem.git
cd realitytap-ecosystem/realitytap-ota-server
```

### 2. 安装依赖
```bash
npm install
```

### 3. 初始化存储结构
```bash
npm run init-storage
```

### 4. 启动开发服务器
```bash
npm run start-dev
```

服务器将在 `http://localhost:3000` 启动。

## 🛠️ 开发环境部署

### 1. 环境配置
复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# 存储配置
STORAGE_PATH=./storage
RELEASES_PATH=./storage/releases
METADATA_PATH=./storage/metadata
LOGS_PATH=./storage/logs

# 安全配置
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=json
```

### 2. 启动服务
```bash
# 使用便捷脚本
npm run start-dev

# 或者直接启动
npm run dev
```

### 3. 验证部署
```bash
# 健康检查
curl http://localhost:3000/health

# 测试API
npm run test-ota
```

## 🏭 生产环境部署

### 1. 构建项目
```bash
npm run build
```

### 2. 生产环境配置
编辑 `.env` 文件：
```env
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 安全配置
CORS_ORIGIN=https://your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
```

### 3. 使用PM2部署
```bash
# 安装PM2
npm install -g pm2

# 启动服务
pm2 start dist/app.js --name "realitytap-ota"

# 设置开机自启
pm2 startup
pm2 save
```

### 4. 反向代理配置（Nginx）
```nginx
server {
    listen 80;
    server_name your-ota-server.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🐳 Docker部署

### 1. 构建镜像
```bash
npm run docker:build
```

### 2. 运行容器
```bash
npm run docker:run
```

### 3. 使用Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  ota-server:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    environment:
      - NODE_ENV=production
      - PORT=3000
    restart: unless-stopped
```

启动：
```bash
docker-compose up -d
```

## ⚙️ 配置说明

### 环境变量
| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `NODE_ENV` | `development` | 运行环境 |
| `PORT` | `3000` | 服务端口 |
| `HOST` | `0.0.0.0` | 绑定地址 |
| `STORAGE_PATH` | `./storage` | 存储根目录 |
| `LOG_LEVEL` | `info` | 日志级别 |
| `CORS_ORIGIN` | `*` | CORS允许的源 |

### 存储结构
```
storage/
├── releases/           # 发布文件
│   ├── stable/        # 稳定版本
│   ├── beta/          # 测试版本
│   └── alpha/         # 开发版本
├── metadata/          # 元数据
│   ├── versions.json  # 版本信息
│   └── channels.json  # 渠道配置
└── logs/              # 日志文件
```

## 📦 发布管理

### 添加新发布
```bash
npm run manage-release add \
  --channel stable \
  --version 1.1.0 \
  --platform windows \
  --arch x86_64 \
  --file /path/to/realitytap-1.1.0-windows-x86_64.exe \
  --notes "修复了若干问题，提升了性能"
```

### 列出所有发布
```bash
npm run manage-release list
```

### 删除发布
```bash
npm run manage-release remove \
  --channel stable \
  --platform windows \
  --arch x86_64
```

## 📊 监控和维护

### 健康检查
```bash
# 基本健康检查
curl http://localhost:3000/health

# 详细健康检查
curl http://localhost:3000/health/detailed
```

### 日志监控
```bash
# 查看实时日志
tail -f storage/logs/app.log

# 使用PM2查看日志
pm2 logs realitytap-ota
```

### 性能监控
```bash
# PM2监控
pm2 monit

# 系统资源
htop
df -h
```

## 🔧 故障排查

### 常见问题

#### 1. 服务无法启动
- 检查端口是否被占用：`netstat -tulpn | grep 3000`
- 检查Node.js版本：`node --version`
- 查看错误日志：`npm run dev`

#### 2. 文件下载失败
- 检查文件是否存在：`ls storage/releases/`
- 验证文件权限：`ls -la storage/releases/`
- 检查磁盘空间：`df -h`

#### 3. 版本检查失败
- 验证配置文件：`npm run validate-config`
- 检查JSON格式：`cat storage/metadata/versions.json | jq`

#### 4. 网络连接问题
- 检查防火墙设置
- 验证CORS配置
- 测试网络连通性：`curl -I http://localhost:3000`

### 调试模式
```bash
# 启用详细日志
LOG_LEVEL=debug npm run dev

# 启用性能日志
VITE_ENABLE_PERFORMANCE_LOG=true npm run dev
```

### 备份和恢复
```bash
# 备份存储目录
tar -czf ota-backup-$(date +%Y%m%d).tar.gz storage/

# 恢复备份
tar -xzf ota-backup-20241215.tar.gz
```

## 📞 支持

如果遇到问题，请：

1. 查看本文档的故障排查部分
2. 检查项目的 [Issues](https://github.com/awa/realitytap-ecosystem/issues)
3. 提交新的Issue并提供详细信息

---

**注意**: 在生产环境中，请确保定期备份存储目录，并监控服务器性能和日志。
