#!/usr/bin/env node

/**
 * 更新器测试脚本
 * Updater Testing Script
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step} ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// 检查文件是否存在
function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    logSuccess(`${description} 存在: ${filePath}`);
    return true;
  } else {
    logError(`${description} 不存在: ${filePath}`);
    return false;
  }
}

// 检查配置
function checkConfiguration() {
  logStep('1️⃣', '检查配置文件');
  
  const configFiles = [
    { path: 'src-tauri/tauri.conf.json', desc: 'Tauri 配置文件' },
    { path: 'src-tauri/Cargo.toml', desc: 'Cargo 配置文件' },
    { path: '.env.development', desc: '开发环境变量' },
    { path: '.env.example', desc: '环境变量示例' }
  ];
  
  let allExist = true;
  configFiles.forEach(file => {
    if (!checkFile(file.path, file.desc)) {
      allExist = false;
    }
  });
  
  if (allExist) {
    logSuccess('所有配置文件检查通过');
  } else {
    logError('配置文件检查失败');
    return false;
  }
  
  // 检查 tauri.conf.json 中的更新配置
  try {
    const tauriConfig = JSON.parse(fs.readFileSync('src-tauri/tauri.conf.json', 'utf8'));
    if (tauriConfig.plugins && tauriConfig.plugins.updater) {
      logSuccess('Tauri 更新插件配置已启用');
      logInfo(`更新端点: ${tauriConfig.plugins.updater.endpoints.join(', ')}`);
    } else {
      logError('Tauri 更新插件配置未找到');
      return false;
    }
  } catch (error) {
    logError(`解析 tauri.conf.json 失败: ${error.message}`);
    return false;
  }
  
  return true;
}

// 检查依赖
function checkDependencies() {
  logStep('2️⃣', '检查依赖项');
  
  try {
    const cargoToml = fs.readFileSync('src-tauri/Cargo.toml', 'utf8');
    
    const requiredDeps = [
      'tauri-plugin-updater',
      'tauri-plugin-process'
    ];
    
    let allFound = true;
    requiredDeps.forEach(dep => {
      if (cargoToml.includes(dep)) {
        logSuccess(`依赖项已安装: ${dep}`);
      } else {
        logError(`依赖项缺失: ${dep}`);
        allFound = false;
      }
    });
    
    return allFound;
  } catch (error) {
    logError(`检查依赖项失败: ${error.message}`);
    return false;
  }
}

// 检查源代码
function checkSourceCode() {
  logStep('3️⃣', '检查源代码文件');
  
  const sourceFiles = [
    { path: 'src/composables/useUpdater.ts', desc: '更新器组合函数' },
    { path: 'src/composables/useGlobalUpdate.ts', desc: '全局更新管理' },
    { path: 'src/components/common/UpdateDialog.vue', desc: '更新对话框组件' },
    { path: 'src/config/updater.config.ts', desc: '更新器配置' },
    { path: 'src/utils/updater-test.ts', desc: '更新器测试工具' }
  ];
  
  let allExist = true;
  sourceFiles.forEach(file => {
    if (!checkFile(file.path, file.desc)) {
      allExist = false;
    }
  });
  
  return allExist;
}

// 检查国际化
function checkInternationalization() {
  logStep('4️⃣', '检查国际化支持');
  
  const localeFiles = [
    'src/locales/zh-CN.ts',
    'src/locales/en-US.ts',
    'src/locales/ja-JP.ts',
    'src/locales/ko-KR.ts'
  ];
  
  let allExist = true;
  localeFiles.forEach(file => {
    if (!checkFile(file, `语言文件 ${path.basename(file)}`)) {
      allExist = false;
    }
  });
  
  // 检查更新相关的翻译键
  try {
    const zhCN = fs.readFileSync('src/locales/zh-CN.ts', 'utf8');
    const enUS = fs.readFileSync('src/locales/en-US.ts', 'utf8');
    
    const requiredKeys = [
      'update.checkingForUpdates',
      'update.downloadingUpdate',
      'update.installingUpdate',
      'update.updateAvailable',
      'update.installNow'
    ];
    
    let keysFound = true;
    requiredKeys.forEach(key => {
      if (zhCN.includes(key) && enUS.includes(key)) {
        logSuccess(`翻译键存在: ${key}`);
      } else {
        logError(`翻译键缺失: ${key}`);
        keysFound = false;
      }
    });
    
    allExist = allExist && keysFound;
  } catch (error) {
    logError(`检查国际化文件失败: ${error.message}`);
    allExist = false;
  }
  
  return allExist;
}

// 运行构建测试
function runBuildTest() {
  logStep('5️⃣', '运行构建测试');
  
  try {
    logInfo('正在运行 TypeScript 类型检查...');
    execSync('npx vue-tsc --noEmit', { stdio: 'pipe' });
    logSuccess('TypeScript 类型检查通过');
    
    logInfo('正在运行 ESLint 检查...');
    try {
      execSync('npx eslint src --ext .ts,.vue', { stdio: 'pipe' });
      logSuccess('ESLint 检查通过');
    } catch (error) {
      logWarning('ESLint 检查有警告，但不影响构建');
    }
    
    return true;
  } catch (error) {
    logError(`构建测试失败: ${error.message}`);
    return false;
  }
}

// 生成测试报告
function generateReport(results) {
  logStep('📊', '生成测试报告');
  
  const report = {
    timestamp: new Date().toISOString(),
    results,
    summary: {
      total: Object.keys(results).length,
      passed: Object.values(results).filter(Boolean).length,
      failed: Object.values(results).filter(r => !r).length
    }
  };
  
  const reportPath = 'updater-test-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log('\n' + '='.repeat(50), 'bright');
  log('📋 测试报告摘要', 'bright');
  log('='.repeat(50), 'bright');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败';
    const color = passed ? 'green' : 'red';
    log(`${test}: ${status}`, color);
  });
  
  log('\n📊 统计信息:', 'bright');
  log(`总计: ${report.summary.total}`);
  log(`通过: ${report.summary.passed}`, 'green');
  log(`失败: ${report.summary.failed}`, 'red');
  log(`成功率: ${Math.round((report.summary.passed / report.summary.total) * 100)}%`);
  
  logInfo(`详细报告已保存到: ${reportPath}`);
  
  return report.summary.failed === 0;
}

// 主函数
function main() {
  log('🚀 RealityTap 更新器测试开始', 'bright');
  log('='.repeat(50), 'bright');
  
  const results = {
    '配置检查': checkConfiguration(),
    '依赖检查': checkDependencies(),
    '源代码检查': checkSourceCode(),
    '国际化检查': checkInternationalization(),
    '构建测试': runBuildTest()
  };
  
  const allPassed = generateReport(results);
  
  if (allPassed) {
    log('\n🎉 所有测试通过！更新器迁移成功完成。', 'green');
    log('💡 提示: 运行 `npm run dev` 启动应用并测试更新功能', 'cyan');
    process.exit(0);
  } else {
    log('\n💥 部分测试失败，请检查上述错误并修复。', 'red');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}
