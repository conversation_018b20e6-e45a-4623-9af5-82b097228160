# RealityTap OTA 存储目录

## 目录结构

- `releases/` - 发布文件存储
  - `stable/` - 稳定版本
  - `beta/` - 测试版本
  - `alpha/` - 开发版本
- `metadata/` - 元数据文件
  - `versions.json` - 版本配置
  - `channels.json` - 渠道配置
- `logs/` - 日志文件
- `temp/` - 临时文件
- `backup/` - 备份文件

## 使用说明

1. 将发布文件放入对应的 `releases/` 子目录
2. 更新 `metadata/versions.json` 中的版本信息
3. 根据需要调整 `metadata/channels.json` 中的渠道配置

## 注意事项

- 确保文件名与配置中的 `filename` 字段一致
- 计算并更新正确的文件校验和
- 定期清理临时文件和旧版本
