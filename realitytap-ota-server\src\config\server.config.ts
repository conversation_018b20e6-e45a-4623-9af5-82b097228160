import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface ServerConfig {
  server: {
    nodeEnv: string;
    port: number;
    host: string;
    baseUrl?: string; // 可选的外部访问基础URL，用于生成完整的下载链接
  };
  database: {
    enabled: boolean;
    type: 'sqlite'; // 只支持SQLite
    path: string;
    backupPath: string;
    maxConnections: number;
    busyTimeout: number;
    enableWAL: boolean;
  };
  storage: {
    basePath: string;
    releasesPath: string;
    metadataPath: string;
    logsPath: string;
    tempPath: string;
  };
  security: {
    corsOrigin: string | string[];
    rateLimitWindowMs: number;
    rateLimitMaxRequests: number;
    adminRateLimitWindowMs: number;
    adminRateLimitMaxRequests: number;
    publicRateLimitWindowMs: number;
    publicRateLimitMaxRequests: number;
    rateLimitWhitelistIps: string[];
  };
  admin: {
    username: string;
    password: string;
    jwtSecret: string;
    jwtExpiresIn: string;
    sessionTimeout: number;
    maxFileSize: number;
    allowedFileTypes: string[];
  };
  upload: {
    chunkSize: number;
    maxConcurrentChunks: number;
    sessionTimeout: number;
    tempCleanupInterval: number;
    maxRetries: number;
    retryDelay: number;
  };
  logging: {
    level: string;
    format: string;
    maxSize: string;
    maxFiles: string;
  };
  download: {
    enableRangeRequests: boolean;
    enableCompression: boolean;
    timeout: number;
    maxFileSize: string;
    allowedFileTypes: string[];
  };
  healthCheck: {
    interval: number;
    timeout: number;
  };
}

const config: ServerConfig = {
  server: {
    nodeEnv: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3000', 10),
    host: process.env.HOST || '0.0.0.0',
    baseUrl: process.env.BASE_URL, // 例如: http://localhost:3000 或 https://ota.example.com
  },
  database: {
    enabled: true, // 始终启用数据库
    type: 'sqlite' as const, // 始终使用SQLite
    path: process.env.DB_PATH || './storage/database/ota.db',
    backupPath: process.env.DB_BACKUP_PATH || './storage/backup/database',
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
    busyTimeout: parseInt(process.env.DB_BUSY_TIMEOUT || '30000'),
    enableWAL: process.env.DB_ENABLE_WAL !== 'false',
  },
  storage: {
    basePath: process.env.STORAGE_PATH || './storage',
    releasesPath: process.env.RELEASES_PATH || './storage/releases',
    metadataPath: process.env.METADATA_PATH || './storage/metadata',
    logsPath: process.env.LOGS_PATH || './storage/logs',
    tempPath: process.env.TEMP_PATH || './storage/temp',
  },
  security: {
    corsOrigin: process.env.CORS_ORIGIN?.split(',') || '*',
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
    // 管理API速率限制配置
    adminRateLimitWindowMs: parseInt(process.env.ADMIN_RATE_LIMIT_WINDOW_MS || (process.env.NODE_ENV === 'development' ? '300000' : '900000'), 10), // 开发环境5分钟，生产环境15分钟
    adminRateLimitMaxRequests: parseInt(process.env.ADMIN_RATE_LIMIT_MAX_REQUESTS || (process.env.NODE_ENV === 'development' ? '500' : '300'), 10), // 开发环境500，生产环境300
    // 公共API速率限制配置
    publicRateLimitWindowMs: parseInt(process.env.PUBLIC_RATE_LIMIT_WINDOW_MS || (process.env.NODE_ENV === 'development' ? '300000' : '900000'), 10), // 开发环境5分钟，生产环境15分钟
    publicRateLimitMaxRequests: parseInt(process.env.PUBLIC_RATE_LIMIT_MAX_REQUESTS || (process.env.NODE_ENV === 'development' ? '200' : '100'), 10), // 开发环境200，生产环境100
    // IP白名单配置
    rateLimitWhitelistIps: process.env.RATE_LIMIT_WHITELIST_IPS?.split(',') || ['127.0.0.1', '::1', 'localhost'],
  },
  admin: {
    username: process.env.ADMIN_USERNAME || 'admin',
    password: process.env.ADMIN_PASSWORD || 'admin123',
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-min-32-chars',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1h',
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '3600000', 10), // 1 hour
    maxFileSize: parseInt(process.env.ADMIN_MAX_FILE_SIZE || '104857600', 10), // 100MB
    allowedFileTypes: process.env.ADMIN_ALLOWED_FILE_TYPES?.split(',') || [
      '.exe',
      '.msi',
      '.dmg',
      '.deb',
      '.rpm',
      '.tar.gz',
      '.zip',
      '.msi',
      '.pkg',
    ],
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d',
  },
  download: {
    enableRangeRequests: process.env.ENABLE_RANGE_REQUESTS === 'true',
    enableCompression: process.env.ENABLE_COMPRESSION !== 'false',
    timeout: parseInt(process.env.DOWNLOAD_TIMEOUT || '300000', 10), // 5 minutes
    maxFileSize: process.env.MAX_FILE_SIZE || '100MB',
    allowedFileTypes: ['.exe', '.msi', '.dmg', '.deb', '.rpm', '.tar.gz', '.zip', '.appimage', '.pkg'],
  },
  healthCheck: {
    interval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000', 10),
    timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT || '5000', 10),
  },
  upload: {
    chunkSize: parseInt(process.env.UPLOAD_CHUNK_SIZE || '1048576', 10), // 1MB
    maxConcurrentChunks: parseInt(process.env.UPLOAD_MAX_CONCURRENT_CHUNKS || '3', 10),
    sessionTimeout: parseInt(process.env.UPLOAD_SESSION_TIMEOUT || '3600000', 10), // 1 hour
    tempCleanupInterval: parseInt(process.env.UPLOAD_TEMP_CLEANUP_INTERVAL || '300000', 10), // 5 minutes
    maxRetries: parseInt(process.env.UPLOAD_MAX_RETRIES || '3', 10),
    retryDelay: parseInt(process.env.UPLOAD_RETRY_DELAY || '1000', 10), // 1 second
  },
};

// Validate configuration
function validateConfig(): void {
  const errors: string[] = [];

  if (config.server.port < 1 || config.server.port > 65535) {
    errors.push('PORT must be between 1 and 65535');
  }

  if (!config.storage.basePath) {
    errors.push('STORAGE_PATH is required');
  }

  if (config.security.rateLimitWindowMs < 1000) {
    errors.push('RATE_LIMIT_WINDOW_MS must be at least 1000ms');
  }

  if (config.security.rateLimitMaxRequests < 1) {
    errors.push('RATE_LIMIT_MAX_REQUESTS must be at least 1');
  }

  if (!['error', 'warn', 'info', 'debug'].includes(config.logging.level)) {
    errors.push('LOG_LEVEL must be one of: error, warn, info, debug');
  }

  // Admin configuration validation
  if (!config.admin.username || config.admin.username.length < 3) {
    errors.push('ADMIN_USERNAME must be at least 3 characters long');
  }

  if (!config.admin.password || config.admin.password.length < 6) {
    errors.push('ADMIN_PASSWORD must be at least 6 characters long');
  }

  if (!config.admin.jwtSecret || config.admin.jwtSecret.length < 32) {
    errors.push('JWT_SECRET must be at least 32 characters long');
  }

  if (config.admin.sessionTimeout < 60000) {
    errors.push('SESSION_TIMEOUT must be at least 60000ms (1 minute)');
  }

  if (config.admin.maxFileSize < 1024) {
    errors.push('ADMIN_MAX_FILE_SIZE must be at least 1024 bytes');
  }

  // Upload configuration validation
  if (config.upload.chunkSize < 1024) {
    errors.push('UPLOAD_CHUNK_SIZE must be at least 1024 bytes');
  }

  if (config.upload.maxConcurrentChunks < 1) {
    errors.push('UPLOAD_MAX_CONCURRENT_CHUNKS must be at least 1');
  }

  if (config.upload.sessionTimeout < 60000) {
    errors.push('UPLOAD_SESSION_TIMEOUT must be at least 60000ms (1 minute)');
  }

  if (config.upload.maxRetries < 0) {
    errors.push('UPLOAD_MAX_RETRIES must be at least 0');
  }

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
}

// Validate configuration on import
validateConfig();

export { config };
export type { ServerConfig };
