/**
 * 浏览器环境的最小类型声明
 * 仅包含平台检测所需的基本接口
 */

declare global {
  /**
   * 浏览器环境中的 Navigator 接口（最小定义）
   */
  interface Navigator {
    readonly userAgent: string;
    readonly language: string;
  }

  /**
   * 浏览器环境中的 Window 接口（最小定义）
   */
  interface Window {
    readonly navigator: Navigator;
  }

  /**
   * 全局 navigator 对象（仅在浏览器环境中可用）
   */
  declare const navigator: Navigator | undefined;

  /**
   * 全局 window 对象（仅在浏览器环境中可用）
   */
  declare const window: Window | undefined;
}

export {};
