import { config } from '@/config/server.config';
import { CryptoUtil } from '@/utils/crypto.util';
import { FileUtil } from '@/utils/file.util';
import { logger } from '@/utils/logger.util';
import { Request, Response } from 'express';
import fs from 'fs-extra';
import path from 'path';

/**
 * 文件管理服务
 */
export class FileService {
  private releasesPath: string;

  constructor() {
    this.releasesPath = config.storage.releasesPath;
  }

  /**
   * 获取文件路径
   * @param filename 文件名
   * @returns 文件完整路径
   */
  private async getFilePath(filename: string): Promise<string> {
    // 防止路径遍历攻击
    const safeName = path.basename(filename);

    // 定义渠道优先级（新的存储方式优先）
    const channels = ['stable', 'beta', 'alpha'];

    // 根据文件名推断渠道
    let preferredChannel = 'stable';
    if (safeName.includes('-beta')) {
      preferredChannel = 'beta';
    } else if (safeName.includes('-alpha')) {
      preferredChannel = 'alpha';
    }

    // 优先检查推断的渠道子目录（新的存储方式）
    const preferredPath = path.join(this.releasesPath, preferredChannel, safeName);
    if (await FileUtil.exists(preferredPath)) {
      return preferredPath;
    }

    // 检查其他渠道子目录
    for (const channel of channels) {
      if (channel !== preferredChannel) {
        const channelPath = path.join(this.releasesPath, channel, safeName);
        if (await FileUtil.exists(channelPath)) {
          return channelPath;
        }
      }
    }

    // 最后检查根目录（向后兼容旧的存储方式）
    const rootPath = path.join(this.releasesPath, safeName);
    if (await FileUtil.exists(rootPath)) {
      return rootPath;
    }

    // 如果都找不到，返回推断渠道的路径（用于错误处理）
    return preferredPath;
  }

  /**
   * 检查文件是否存在
   * @param filename 文件名
   * @returns 是否存在
   */
  async fileExists(filename: string): Promise<boolean> {
    try {
      const filePath = await this.getFilePath(filename);
      return await FileUtil.exists(filePath);
    } catch (error) {
      logger.error('Failed to check file existence', { filename, error });
      return false;
    }
  }

  /**
   * 获取文件信息
   * @param filename 文件名
   * @returns 文件信息
   */
  async getFileInfo(filename: string): Promise<{
    size: number;
    mtime: Date;
    exists: boolean;
  }> {
    try {
      const filePath = await this.getFilePath(filename);

      if (!(await FileUtil.exists(filePath))) {
        return {
          size: 0,
          mtime: new Date(),
          exists: false,
        };
      }

      const info = await FileUtil.getFileInfo(filePath);
      return {
        size: info.size,
        mtime: info.mtime,
        exists: true,
      };
    } catch (error) {
      logger.error('Failed to get file info', { filename, error });
      throw new Error(`Failed to get file info: ${error}`);
    }
  }

  /**
   * 流式下载文件
   * @param filename 文件名
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  async streamFile(filename: string, req: Request, res: Response): Promise<void> {
    try {
      const filePath = await this.getFilePath(filename);

      // 检查文件是否存在
      if (!(await FileUtil.exists(filePath))) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FILE_NOT_FOUND',
            message: 'File not found',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        });
        return;
      }

      // 检查文件类型
      if (!FileUtil.isAllowedFileType(filename)) {
        res.status(403).json({
          success: false,
          error: {
            code: 'FILE_TYPE_NOT_ALLOWED',
            message: 'File type not allowed',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        });
        return;
      }

      const fileInfo = await FileUtil.getFileInfo(filePath);
      const fileSize = fileInfo.size;

      // 设置响应头
      res.setHeader('Content-Type', 'application/octet-stream');

      // 正确处理包含特殊字符的文件名
      const encodedFilename = encodeURIComponent(filename);
      const asciiFilename = filename.replace(/[^\x00-\x7F]/g, '_'); // 替换非ASCII字符
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${asciiFilename}"; filename*=UTF-8''${encodedFilename}`,
      );

      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Length', fileSize.toString());
      res.setHeader('Last-Modified', fileInfo.mtime.toUTCString());

      // 处理范围请求（断点续传）
      const range = req.headers.range;
      if (range && config.download.enableRangeRequests) {
        await this.handleRangeRequest(filePath, range, fileSize, res);
      } else {
        // 完整文件下载
        await this.handleFullDownload(filePath, res);
      }

      // 记录下载日志
      logger.info('版本文件下载完成', {
        filename,
        fileSize,
        userAgent: req.get('User-Agent'),
        clientIP: req.ip,
        range: range || 'full',
        module: 'version_management',
        operation: 'file_download_complete',
      });
    } catch (error) {
      logger.error('Failed to stream file', { filename, error });

      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: {
            code: 'DOWNLOAD_FAILED',
            message: 'Failed to download file',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        });
      }
    }
  }

  /**
   * 处理范围请求
   */
  private async handleRangeRequest(filePath: string, range: string, fileSize: number, res: Response): Promise<void> {
    const parts = range.replace(/bytes=/, '').split('-');
    const start = parseInt(parts[0] || '0', 10);
    const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;

    if (start >= fileSize || end >= fileSize || start > end) {
      res.status(416).setHeader('Content-Range', `bytes */${fileSize}`);
      res.end();
      return;
    }

    const chunkSize = end - start + 1;
    res.status(206);
    res.setHeader('Content-Range', `bytes ${start}-${end}/${fileSize}`);
    res.setHeader('Content-Length', chunkSize.toString());

    const stream = fs.createReadStream(filePath, { start, end });
    stream.pipe(res);

    return new Promise((resolve, reject) => {
      stream.on('end', resolve);
      stream.on('error', reject);
      res.on('close', () => {
        stream.destroy();
        resolve();
      });
    });
  }

  /**
   * 处理完整文件下载
   */
  private async handleFullDownload(filePath: string, res: Response): Promise<void> {
    const stream = fs.createReadStream(filePath);
    stream.pipe(res);

    return new Promise((resolve, reject) => {
      stream.on('end', resolve);
      stream.on('error', reject);
      res.on('close', () => {
        stream.destroy();
        resolve();
      });
    });
  }

  /**
   * 验证文件完整性
   * @param filename 文件名
   * @param expectedChecksum 期望的校验和
   * @returns 是否有效
   */
  async verifyFileIntegrity(filename: string, expectedChecksum: string): Promise<boolean> {
    try {
      const filePath = await this.getFilePath(filename);

      if (!(await FileUtil.exists(filePath))) {
        return false;
      }

      return await CryptoUtil.verifyFileHash(filePath, expectedChecksum);
    } catch (error) {
      logger.error('Failed to verify file integrity', { filename, expectedChecksum, error });
      return false;
    }
  }

  /**
   * 计算文件校验和
   * @param filename 文件名
   * @returns 校验和
   */
  async calculateFileChecksum(filename: string): Promise<string> {
    try {
      const filePath = await this.getFilePath(filename);

      if (!(await FileUtil.exists(filePath))) {
        throw new Error('File not found');
      }

      return await CryptoUtil.calculateFileHash(filePath);
    } catch (error) {
      logger.error('Failed to calculate file checksum', { filename, error });
      throw new Error(`Failed to calculate file checksum: ${error}`);
    }
  }

  /**
   * 列出所有发布文件
   * @param channel 发布渠道
   * @returns 文件列表
   */
  async listReleaseFiles(channel?: string): Promise<string[]> {
    try {
      const searchPath = channel ? path.join(this.releasesPath, channel) : this.releasesPath;

      if (!(await FileUtil.exists(searchPath))) {
        return [];
      }

      const files = await FileUtil.listFiles(searchPath, !channel);
      return files.map(file => path.basename(file));
    } catch (error) {
      logger.error('Failed to list release files', { channel, error });
      return [];
    }
  }

  /**
   * 删除发布文件
   * @param filename 文件名
   */
  async deleteReleaseFile(filename: string): Promise<void> {
    try {
      const filePath = await this.getFilePath(filename);

      if (!(await FileUtil.exists(filePath))) {
        throw new Error('File not found');
      }

      await FileUtil.remove(filePath);
      logger.info('发布文件删除成功', {
        filename,
        module: 'version_management',
        operation: 'release_file_delete',
      });
    } catch (error) {
      logger.error('Failed to delete release file', { filename, error });
      throw new Error(`Failed to delete release file: ${error}`);
    }
  }

  /**
   * 清理旧的发布文件
   * @param maxAge 最大年龄（毫秒）
   * @returns 删除的文件数量
   */
  async cleanupOldReleases(maxAge: number): Promise<number> {
    try {
      return await FileUtil.cleanupOldFiles(this.releasesPath, maxAge);
    } catch (error) {
      logger.error('Failed to cleanup old releases', { maxAge, error });
      return 0;
    }
  }
}

// 导出单例实例
export const fileService = new FileService();
