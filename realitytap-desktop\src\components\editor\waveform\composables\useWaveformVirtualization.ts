/**
 * 波形虚拟化处理
 * 实现事件的虚拟化渲染，只渲染可见区域的事件，提高大量事件数据的性能
 */

import { ref, computed, watch, type Ref } from "vue";
import type { RenderableEvent } from "@/types/haptic-editor";
import { waveformLogger } from "@/utils/logger/logger";

/**
 * 虚拟化配置
 */
export interface VirtualizationConfig {
  // 视口配置
  viewportWidth: number;
  viewportHeight: number;

  // 缓冲区配置（在可见区域外额外渲染的区域）
  bufferBefore?: number; // 前缓冲区大小（时间单位：ms）
  bufferAfter?: number; // 后缓冲区大小（时间单位：ms）

  // 性能配置
  maxVisibleEvents?: number; // 最大可见事件数量
  enableCaching?: boolean; // 是否启用缓存
  enableLogging?: boolean; // 是否启用日志

  // 优化配置
  enableEventGrouping?: boolean; // 是否启用事件分组
  groupingThreshold?: number; // 分组阈值（像素）
}

/**
 * 虚拟化结果
 */
export interface VirtualizationResult {
  visibleEvents: RenderableEvent[];
  totalEvents: number;
  visibleCount: number;
  startIndex: number;
  endIndex: number;
  renderingStats: {
    timeRange: { start: number; end: number };
    bufferRange: { start: number; end: number };
    culledEvents: number;
    groupedEvents: number;
  };
}

/**
 * 事件分组信息
 */
interface EventGroup {
  events: RenderableEvent[];
  startTime: number;
  endTime: number;
  averageIntensity: number;
  representativeEvent: RenderableEvent;
}

/**
 * 波形虚拟化Hook
 */
export function useWaveformVirtualization(getEvents: () => RenderableEvent[], virtualScrollOffset: Ref<number>, config: VirtualizationConfig) {
  const {
    viewportWidth,
    viewportHeight,
    bufferBefore = 1000, // 1秒前缓冲
    bufferAfter = 1000, // 1秒后缓冲
    maxVisibleEvents = 500,
    enableCaching = true,
    enableLogging = false,
    enableEventGrouping = false,
    groupingThreshold = 5, // 5像素内的事件可以分组
  } = config;

  // 缓存
  const cache = ref<Map<string, VirtualizationResult>>(new Map());
  const lastCacheKey = ref<string>("");

  // 性能统计
  const performanceStats = ref({
    totalCalls: 0,
    cacheHits: 0,
    averageProcessingTime: 0,
    lastProcessingTime: 0,
  });

  /**
   * 生成缓存键
   */
  const generateCacheKey = (events: RenderableEvent[], scrollOffset: number, viewport: { width: number; height: number }): string => {
    const eventsHash = events.length > 0 ? `${events.length}-${events[0]?.id}-${events[events.length - 1]?.id}` : "empty";
    return `${eventsHash}-${scrollOffset}-${viewport.width}x${viewport.height}`;
  };

  /**
   * 计算时间范围
   */
  const calculateTimeRange = (scrollOffset: number, duration: number) => {
    const startTime = Math.max(0, scrollOffset - bufferBefore);
    const endTime = scrollOffset + duration + bufferAfter;

    return {
      visible: { start: scrollOffset, end: scrollOffset + duration },
      buffer: { start: startTime, end: endTime },
    };
  };

  /**
   * 事件分组处理
   */
  const groupNearbyEvents = (events: RenderableEvent[], mapTimeToX: (time: number) => number): EventGroup[] => {
    if (!enableEventGrouping || events.length === 0) {
      return events.map((event) => ({
        events: [event],
        startTime: event.startTime,
        endTime: event.startTime + (event.type === "continuous" ? event.duration : 0),
        averageIntensity: event.type === "transient" ? event.intensity : event.eventIntensity,
        representativeEvent: event,
      }));
    }

    const groups: EventGroup[] = [];
    const sortedEvents = [...events].sort((a, b) => a.startTime - b.startTime);

    let currentGroup: RenderableEvent[] = [];
    let lastX = -Infinity;

    for (const event of sortedEvents) {
      const eventX = mapTimeToX(event.startTime);

      if (currentGroup.length === 0 || Math.abs(eventX - lastX) <= groupingThreshold) {
        currentGroup.push(event);
        lastX = eventX;
      } else {
        // 完成当前分组
        if (currentGroup.length > 0) {
          groups.push(createEventGroup(currentGroup));
        }

        // 开始新分组
        currentGroup = [event];
        lastX = eventX;
      }
    }

    // 处理最后一个分组
    if (currentGroup.length > 0) {
      groups.push(createEventGroup(currentGroup));
    }

    return groups;
  };

  /**
   * 创建事件分组
   */
  const createEventGroup = (events: RenderableEvent[]): EventGroup => {
    const startTime = Math.min(...events.map((e) => e.startTime));
    const endTime = Math.max(...events.map((e) => e.startTime + (e.type === "continuous" ? e.duration : 0)));

    const totalIntensity = events.reduce((sum, event) => sum + (event.type === "transient" ? event.intensity : event.eventIntensity), 0);
    const averageIntensity = totalIntensity / events.length;

    // 选择最具代表性的事件（强度最接近平均值的）
    const representativeEvent = events.reduce((best, current) => {
      const currentIntensity = current.type === "transient" ? current.intensity : current.eventIntensity;
      const bestIntensity = best.type === "transient" ? best.intensity : best.eventIntensity;

      return Math.abs(currentIntensity - averageIntensity) < Math.abs(bestIntensity - averageIntensity) ? current : best;
    });

    return {
      events,
      startTime,
      endTime,
      averageIntensity,
      representativeEvent,
    };
  };

  /**
   * 过滤可见事件
   */
  const filterVisibleEvents = (events: RenderableEvent[], timeRange: { start: number; end: number }): RenderableEvent[] => {
    return events.filter((event) => {
      const eventStartTime = event.startTime;
      const eventEndTime = event.startTime + (event.type === "continuous" ? event.duration : 0);

      // 检查事件是否与时间范围重叠
      return eventStartTime <= timeRange.end && eventEndTime >= timeRange.start;
    });
  };

  /**
   * 主要的虚拟化处理函数
   */
  const getVisibleEvents = computed((): VirtualizationResult => {
    const startTime = performance.now();
    performanceStats.value.totalCalls++;

    const events = getEvents();
    const scrollOffset = virtualScrollOffset.value;
    const viewport = { width: viewportWidth, height: viewportHeight };

    // 检查缓存
    const cacheKey = generateCacheKey(events, scrollOffset, viewport);
    if (enableCaching && cache.value.has(cacheKey)) {
      performanceStats.value.cacheHits++;
      const cached = cache.value.get(cacheKey)!;

      if (enableLogging) {
        waveformLogger.debug(`Cache hit for key: ${cacheKey}`);
      }

      return cached;
    }

    // 计算时间范围
    const timeRanges = calculateTimeRange(scrollOffset, viewportWidth);

    // 过滤可见事件
    const visibleEvents = filterVisibleEvents(events, timeRanges.buffer);

    // 限制最大事件数量
    const limitedEvents = visibleEvents.slice(0, maxVisibleEvents);

    // 计算统计信息
    const culledEvents = events.length - visibleEvents.length;
    const groupedEvents = enableEventGrouping ? groupNearbyEvents(limitedEvents, (time) => time).length : limitedEvents.length;

    const result: VirtualizationResult = {
      visibleEvents: limitedEvents,
      totalEvents: events.length,
      visibleCount: limitedEvents.length,
      startIndex: 0, // 简化实现，不计算精确索引
      endIndex: limitedEvents.length - 1,
      renderingStats: {
        timeRange: timeRanges.visible,
        bufferRange: timeRanges.buffer,
        culledEvents,
        groupedEvents,
      },
    };

    // 更新缓存
    if (enableCaching) {
      cache.value.set(cacheKey, result);
      lastCacheKey.value = cacheKey;

      // 限制缓存大小
      if (cache.value.size > 10) {
        const firstKey = cache.value.keys().next().value;
        if (firstKey !== undefined) {
          cache.value.delete(firstKey);
        }
      }
    }

    // 更新性能统计
    const processingTime = performance.now() - startTime;
    performanceStats.value.lastProcessingTime = processingTime;
    performanceStats.value.averageProcessingTime =
      (performanceStats.value.averageProcessingTime * (performanceStats.value.totalCalls - 1) + processingTime) / performanceStats.value.totalCalls;

    if (enableLogging) {
      waveformLogger.debug(`Processed ${events.length} events -> ${limitedEvents.length} visible (${processingTime.toFixed(2)}ms)`);
    }

    return result;
  });

  /**
   * 清除缓存
   */
  const clearCache = () => {
    cache.value.clear();
    lastCacheKey.value = "";

    if (enableLogging) {
      waveformLogger.debug(`Cache cleared`);
    }
  };

  /**
   * 获取性能统计
   */
  const getPerformanceStats = () => {
    const stats = performanceStats.value;
    return {
      ...stats,
      cacheHitRate: stats.totalCalls > 0 ? ((stats.cacheHits / stats.totalCalls) * 100).toFixed(2) + "%" : "0%",
      cacheSize: cache.value.size,
    };
  };

  // 监听配置变化，清除缓存
  watch(
    () => [viewportWidth, viewportHeight, bufferBefore, bufferAfter, maxVisibleEvents],
    () => {
      clearCache();
    }
  );

  return {
    // 主要功能
    getVisibleEvents,

    // 缓存管理
    clearCache,

    // 性能监控
    getPerformanceStats,
    performanceStats: computed(() => getPerformanceStats()),

    // 配置信息
    config: computed(() => ({
      viewportWidth,
      viewportHeight,
      bufferBefore,
      bufferAfter,
      maxVisibleEvents,
      enableCaching,
      enableEventGrouping,
    })),
  };
}
