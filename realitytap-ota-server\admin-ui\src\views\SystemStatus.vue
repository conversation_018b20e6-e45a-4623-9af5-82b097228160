<template>
  <div class="system-status">
    <n-page-header title="系统状态" subtitle="监控系统健康状况和性能指标">
      <template #extra>
        <n-space>
          <n-button :loading="loading" @click="refreshData">
            <template #icon>
              <n-icon :component="RefreshOutline" />
            </template>
            刷新
          </n-button>
        </n-space>
      </template>
    </n-page-header>

    <n-space vertical size="large">
      <!-- 健康状态 -->
      <n-card title="系统健康状态">
        <n-grid :cols="3" :x-gap="16">
          <n-grid-item>
            <n-statistic label="服务状态">
              <template #prefix>
                <n-icon
                  :component="healthStatus?.status === 'healthy' ? CheckmarkCircleOutline : CloseCircleOutline"
                  :color="healthStatus?.status === 'healthy' ? '#18a058' : '#d03050'"
                />
              </template>
              <n-tag :type="healthStatus?.status === 'healthy' ? 'success' : 'error'" size="large">
                {{ healthStatus?.status === 'healthy' ? '正常' : '异常' }}
              </n-tag>
              <!-- 显示异常时的详细信息 -->
              <div v-if="healthStatus?.status === 'unhealthy' && healthStatus?.diagnostics" class="health-details">
                <n-divider style="margin: 12px 0;" />
                <div class="health-check-items">
                  <div class="health-check-item">
                    <n-icon
                      :component="healthStatus.checks?.storage ? CheckmarkCircleOutline : CloseCircleOutline"
                      :color="healthStatus.checks?.storage ? '#18a058' : '#d03050'"
                      size="14"
                    />
                    <span>存储路径: {{ healthStatus.checks?.storage ? '正常' : '异常' }}</span>
                  </div>
                  <div class="health-check-item">
                    <n-icon
                      :component="healthStatus.checks?.disk ? CheckmarkCircleOutline : CloseCircleOutline"
                      :color="healthStatus.checks?.disk ? '#18a058' : '#d03050'"
                      size="14"
                    />
                    <span>磁盘访问: {{ healthStatus.checks?.disk ? '正常' : '异常' }}</span>
                  </div>
                  <div class="health-check-item">
                    <n-icon
                      :component="healthStatus.checks?.memory ? CheckmarkCircleOutline : CloseCircleOutline"
                      :color="healthStatus.checks?.memory ? '#18a058' : '#d03050'"
                      size="14"
                    />
                    <span>内存使用: {{ healthStatus.diagnostics.memory.usagePercentage }}% (限制: {{ healthStatus.diagnostics.thresholds.memoryLimit }}%)</span>
                  </div>
                </div>
              </div>
            </n-statistic>
          </n-grid-item>

          <n-grid-item>
            <n-statistic label="运行时间" :value="formatUptime(healthStatus?.uptime || 0)">
              <template #prefix>
                <n-icon :component="TimeOutline" color="#2080f0" />
              </template>
            </n-statistic>
          </n-grid-item>

          <n-grid-item>
            <n-statistic label="服务版本" :value="healthStatus?.version || 'N/A'">
              <template #prefix>
                <n-icon :component="InformationCircleOutline" color="#f0a020" />
              </template>
            </n-statistic>
          </n-grid-item>
        </n-grid>
      </n-card>

      <!-- 系统资源 -->
      <n-card title="系统资源">
        <n-grid :cols="2" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <div class="resource-item">
              <h4>内存使用情况</h4>
              <n-progress
                type="circle"
                :percentage="adminStore.stats?.system.memory.percentage || 0"
                :color="getMemoryColor(adminStore.stats?.system.memory.percentage || 0)"
              >
                {{ adminStore.stats?.system.memory.percentage || 0 }}%
              </n-progress>
              <p class="resource-detail">
                {{ formatBytes(adminStore.stats?.system.memory.used || 0) }} /
                {{ formatBytes(adminStore.stats?.system.memory.total || 0) }}
              </p>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="resource-item">
              <h4>存储使用情况</h4>
              <n-progress
                type="circle"
                :percentage="storageUsagePercentage"
                :color="getStorageColor(storageUsagePercentage)"
              >
                {{ storageUsagePercentage }}%
              </n-progress>
              <p class="resource-detail">{{ formatBytes(adminStore.stats?.ota.totalSize || 0) }} 已使用</p>
            </div>
          </n-grid-item>
        </n-grid>
      </n-card>

      <!-- 系统信息详情 -->
      <n-card title="系统详细信息">
        <n-descriptions v-if="adminStore.stats" :column="2" label-placement="left" bordered>
          <n-descriptions-item label="Node.js 版本">
            {{ adminStore.stats.system.nodeVersion }}
          </n-descriptions-item>
          <n-descriptions-item label="操作系统">
            {{ adminStore.stats.system.platform }}
          </n-descriptions-item>
          <n-descriptions-item label="系统架构">
            {{ adminStore.stats.system.architecture }}
          </n-descriptions-item>
          <n-descriptions-item label="进程 PID">
            {{ adminStore.stats?.system.pid || 'N/A' }}
          </n-descriptions-item>
          <n-descriptions-item label="OTA 版本总数">
            {{ adminStore.stats.ota.totalVersions }}
          </n-descriptions-item>
          <n-descriptions-item label="总下载次数">
            {{ adminStore.stats.ota.totalDownloads }}
          </n-descriptions-item>
        </n-descriptions>

        <n-skeleton v-else text :repeat="3" />
      </n-card>

      <!-- 操作面板 -->
      <n-card title="系统操作">
        <n-space>
          <n-button type="warning" @click="handleCleanup" :loading="cleanupLoading">
            <template #icon>
              <n-icon :component="TrashOutline" />
            </template>
            清理临时文件
          </n-button>

          <n-button @click="handleExportLogs" :loading="exportLoading">
            <template #icon>
              <n-icon :component="DocumentTextOutline" />
            </template>
            导出日志
          </n-button>

          <n-button type="info" @click="showSystemInfo = true">
            <template #icon>
              <n-icon :component="InformationCircleOutline" />
            </template>
            查看详细信息
          </n-button>
        </n-space>
      </n-card>
    </n-space>

    <!-- 系统详细信息模态框 -->
    <n-modal v-model:show="showSystemInfo" preset="card" title="系统详细信息" style="width: 800px">
      <n-code :code="JSON.stringify(systemInfo, null, 2)" language="json" show-line-numbers />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { adminApi } from '@/api/admin';
import type { HealthStatus } from '@/api/types';
import { useAdminStore } from '@/stores/admin';
import {
  CheckmarkCircleOutline,
  CloseCircleOutline,
  DocumentTextOutline,
  InformationCircleOutline,
  RefreshOutline,
  TimeOutline,
  TrashOutline,
} from '@vicons/ionicons5';
import {
  NButton,
  NCard,
  NCode,
  NDescriptions,
  NDescriptionsItem,
  NGrid,
  NGridItem,
  NIcon,
  NModal,
  NPageHeader,
  NProgress,
  NSkeleton,
  NSpace,
  NStatistic,
  NTag,
  useMessage,
} from 'naive-ui';
import { computed, onMounted, ref } from 'vue';

const message = useMessage();
const adminStore = useAdminStore();

const loading = ref(false);
const cleanupLoading = ref(false);
const exportLoading = ref(false);
const showSystemInfo = ref(false);
const healthStatus = ref<HealthStatus | null>(null);

// 存储使用百分比
const storageUsagePercentage = computed(() => {
  const totalSize = adminStore.stats?.ota.totalSize || 0;
  // 从配置中获取最大存储容量，默认10GB
  const maxStorage = 10 * 1024 * 1024 * 1024; // 10GB
  if (totalSize === 0) return 0;
  return Math.min(Math.round((totalSize / maxStorage) * 100), 100);
});

// 系统信息
const systemInfo = computed(() => ({
  health: healthStatus.value,
  stats: adminStore.stats,
  timestamp: new Date().toISOString(),
}));

// 格式化运行时间
const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

// 格式化字节数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取内存使用颜色
const getMemoryColor = (percentage: number): string => {
  if (percentage < 60) return '#18a058';
  if (percentage < 80) return '#f0a020';
  return '#d03050';
};

// 获取存储使用颜色
const getStorageColor = (percentage: number): string => {
  if (percentage < 70) return '#18a058';
  if (percentage < 90) return '#f0a020';
  return '#d03050';
};

// 刷新数据
const refreshData = async () => {
  loading.value = true;
  try {
    await Promise.all([adminStore.refreshData(), fetchHealthStatus()]);
  } finally {
    loading.value = false;
  }
};

// 获取健康状态
const fetchHealthStatus = async () => {
  try {
    const response = await adminApi.getHealth();
    if (response.success && response.data) {
      healthStatus.value = response.data;
    }
  } catch (error) {
    console.error('Failed to fetch health status:', error);
  }
};

// 清理临时文件
const handleCleanup = async () => {
  cleanupLoading.value = true;
  try {
    const response = await adminApi.cleanup();
    if (response.success) {
      message.success('临时文件清理成功');
      await refreshData();
    } else {
      message.error('清理失败');
    }
  } catch (error) {
    message.error('清理操作失败');
  } finally {
    cleanupLoading.value = false;
  }
};

// 导出日志
const handleExportLogs = async () => {
  exportLoading.value = true;
  try {
    // 获取日志数据
    const response = await adminApi.getLogs({ limit: 1000 });
    if (response.success && response.data) {
      // 创建CSV内容
      const csvContent = [
        ['时间', '级别', '消息', '元数据'].join(','),
        ...response.data.logs.map((log: any) =>
          [
            log.timestamp,
            log.level,
            `"${log.message.replace(/"/g, '""')}"`,
            `"${JSON.stringify(log.meta || {}).replace(/"/g, '""')}"`,
          ].join(','),
        ),
      ].join('\n');

      // 创建下载链接
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `system-logs-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success('日志导出成功');
    } else {
      message.error('获取日志数据失败');
    }
  } catch (error) {
    message.error('导出失败');
  } finally {
    exportLoading.value = false;
  }
};

onMounted(() => {
  refreshData();
});
</script>

<style scoped>
.system-status {
  max-width: 1200px;
  margin: 0 auto;
}

.resource-item {
  text-align: center;
  padding: 16px;
}

.resource-item h4 {
  margin: 0 0 16px 0;
  color: var(--n-text-color);
}

.resource-detail {
  margin: 16px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.health-details {
  margin-top: 8px;
}

.health-check-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.health-check-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--n-text-color-2);
}
</style>
