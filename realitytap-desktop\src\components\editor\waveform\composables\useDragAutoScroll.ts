import type { RenderableEvent } from "@/types/haptic-editor";
import type { Ref } from "vue";
import { ref } from "vue";
import type { AutoScrollConfig } from "./useDragConfig";
import type { DragTarget } from "./useDragState";
import {
  AUTO_SCROLL_TRIGGER_THRESHOLD,
  AUTO_SCROLL_BASE_SPEED,
  AUTO_SCROLL_MAX_SPEED,
  AUTO_SCROLL_THROTTLE_MS,
  AUTO_SCROLL_SPEED_FACTOR,
} from "../config/waveform-constants";
import { dragLogger } from "@/utils/logger/logger";

/**
 * 拖拽自动滚动 composable
 * 负责在拖拽过程中处理自动滚动逻辑
 */
export function useDragAutoScroll(
  autoScrollConfig: AutoScrollConfig | undefined,
  draggingTarget: Ref<DragTarget | null>,
  draggedEvent: Ref<RenderableEvent | null>,
  _dragStartEventTime: Ref<number>,
  currentDraggedTimeOffset: Ref<number>
) {
  // 节流控制
  const lastScrollTime = ref<number>(0);

  /**
   * 检查是否存在水平滚动条
   */
  const hasHorizontalScrollbar = (): boolean => {
    if (!autoScrollConfig) return false;

    const logicalWidth = autoScrollConfig.logicalCanvasWidth.value;
    const physicalWidth = getParentWidth();

    return logicalWidth > physicalWidth;
  };

  /**
   * 获取滚动容器的屏幕边界
   */
  const getScrollContainerScreenBounds = (): { left: number; right: number } | null => {
    if (!autoScrollConfig) return null;

    // 直接通过 CSS 类名查找滚动容器
    let scrollbarElement = null;

    // 通过 CSS 类名 .waveform-scrollbar 查找
    if (autoScrollConfig.graphContainerRef?.value) {
      scrollbarElement = autoScrollConfig.graphContainerRef.value.querySelector(".waveform-scrollbar");
    }

    // 验证元素是否为有效的 DOM 元素
    if (!scrollbarElement || typeof scrollbarElement.getBoundingClientRect !== "function") {
      dragLogger.error("[AutoScroll] 获取到的不是有效的DOM元素", {
        element: scrollbarElement,
        type: typeof scrollbarElement,
        hasGetBoundingClientRect: scrollbarElement && typeof scrollbarElement.getBoundingClientRect === "function",
      });
      return null;
    }

    const rect = scrollbarElement.getBoundingClientRect();

    return {
      left: rect.left,
      right: rect.right,
    };
  };

  const getParentWidth = (): number => {
    const bounds = getScrollContainerScreenBounds();
    return bounds ? bounds.right - bounds.left : 0;
  };

  /**
   * 获取Event在屏幕上的边界坐标
   */
  const getEventScreenBounds = (event: RenderableEvent): { left: number; right: number } | null => {
    if (!autoScrollConfig?.mapTimeToXLocal) {
      dragLogger.debug("[AutoScroll] 缺少必要的配置函数");
      return null;
    }

    // 计算Event的时间边界
    const eventStartTime = event.startTime + currentDraggedTimeOffset.value;
    const eventStopTime = event.stopTime + currentDraggedTimeOffset.value;

    // 转换为Canvas坐标
    const startX = autoScrollConfig.mapTimeToXLocal(eventStartTime);
    const stopX = autoScrollConfig.mapTimeToXLocal(eventStopTime);

    // 获取容器屏幕边界
    const containerBounds = getScrollContainerScreenBounds();
    if (!containerBounds) return null;

    // 转换为屏幕坐标（简化处理，假设Canvas与容器左对齐）
    const screenStartX = containerBounds.left + startX;
    const screenStopX = containerBounds.left + stopX;

    return {
      left: screenStartX,
      right: screenStopX,
    };
  };

  /**
   * 计算滚动量
   */
  const calculateScrollAmount = (distanceToEdge: number, direction: "left" | "right"): number => {
    if (distanceToEdge >= AUTO_SCROLL_TRIGGER_THRESHOLD) return 0;

    // 计算滚动速度：距离边界越近，速度越快
    const speedRatio = Math.max(0, (AUTO_SCROLL_TRIGGER_THRESHOLD - distanceToEdge) / AUTO_SCROLL_TRIGGER_THRESHOLD);
    const speed = AUTO_SCROLL_BASE_SPEED + (AUTO_SCROLL_MAX_SPEED - AUTO_SCROLL_BASE_SPEED) * Math.pow(speedRatio, AUTO_SCROLL_SPEED_FACTOR);

    return direction === "left" ? -speed : speed;
  };

  /**
   * 执行自动滚动
   */
  const performAutoScroll = (scrollAmount: number): void => {
    if (!autoScrollConfig?.horizontalScrollbarRef.value || scrollAmount === 0) return;

    const currentTime = Date.now();
    if (currentTime - lastScrollTime.value < AUTO_SCROLL_THROTTLE_MS) return;

    const scrollbar = autoScrollConfig.horizontalScrollbarRef.value;
    const currentScrollLeft = autoScrollConfig.scrollLeftValue.value;

    // 计算滚动边界
    const logicalWidth = autoScrollConfig.logicalCanvasWidth.value;
    const physicalWidth = getParentWidth();
    const maxScrollLeft = Math.max(0, logicalWidth - physicalWidth);

    // 检查滚动边界
    const newScrollLeft = Math.max(0, Math.min(maxScrollLeft, currentScrollLeft + scrollAmount));

    if (newScrollLeft !== currentScrollLeft) {
      scrollbar.scrollTo({ left: newScrollLeft, behavior: "auto" });
      lastScrollTime.value = currentTime;
    }
  };

  /**
   * 处理自动滚动主逻辑
   */
  const handleAutoScroll = (mouseX?: number, _mouseY?: number) => {
    // 基础检查
    if (!autoScrollConfig || !autoScrollConfig.horizontalScrollbarRef.value) {
      dragLogger.debug("[AutoScroll] 基础检查失败: 缺少配置或滚动条引用");
      return;
    }
    if (!(draggingTarget.value === "event" || draggingTarget.value === "continuousCurvePoint")) {
      dragLogger.debug("[AutoScroll] 拖拽目标不匹配", { draggingTarget: draggingTarget.value });
      return;
    }
    if (!hasHorizontalScrollbar()) {
      dragLogger.debug("[AutoScroll] 无水平滚动条，跳过自动滚动");
      return;
    }

    // 获取容器屏幕边界
    const containerBounds = getScrollContainerScreenBounds();
    if (!containerBounds) {
      dragLogger.debug("[AutoScroll] 无法获取容器边界");
      return;
    }

    let shouldScrollLeft = false;
    let shouldScrollRight = false;
    let leftDistance = 0;
    let rightDistance = 0;

    if (draggingTarget.value === "event" && draggedEvent.value) {
      // Event整体拖拽：检查Event边界
      const eventBounds = getEventScreenBounds(draggedEvent.value);
      if (!eventBounds) return;

      // 检查左边界
      leftDistance = eventBounds.left - containerBounds.left;
      if (leftDistance < AUTO_SCROLL_TRIGGER_THRESHOLD && leftDistance >= 0) {
        shouldScrollLeft = true;
      }

      // 检查右边界
      rightDistance = containerBounds.right - eventBounds.right;
      if (rightDistance < AUTO_SCROLL_TRIGGER_THRESHOLD && rightDistance >= 0) {
        shouldScrollRight = true;
      }
    } else if (draggingTarget.value === "continuousCurvePoint" && mouseX !== undefined) {
      // Curve点拖拽：检查鼠标位置
      leftDistance = mouseX - containerBounds.left;
      rightDistance = containerBounds.right - mouseX;

      if (leftDistance < AUTO_SCROLL_TRIGGER_THRESHOLD && leftDistance >= 0) {
        shouldScrollLeft = true;
      }
      if (rightDistance < AUTO_SCROLL_TRIGGER_THRESHOLD && rightDistance >= 0) {
        shouldScrollRight = true;
      }
    }

    // 执行滚动
    if (shouldScrollLeft && !shouldScrollRight) {
      const scrollAmount = calculateScrollAmount(leftDistance, "left");
      performAutoScroll(scrollAmount);
    } else if (shouldScrollRight && !shouldScrollLeft) {
      const scrollAmount = calculateScrollAmount(rightDistance, "right");
      performAutoScroll(scrollAmount);
    } else {
      // 无需滚动
    }
  };

  return {
    handleAutoScroll,
  };
}
