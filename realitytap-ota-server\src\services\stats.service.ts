import { config } from '@/config/server.config';
import { DownloadRecord, DownloadStatsData, DownloadStatsQuery } from '@/types/server.types';
import { FileUtil } from '@/utils/file.util';
import { logger } from '@/utils/logger.util';
import { StatsUtil } from '@/utils/stats.util';
import path from 'path';

/**
 * 统计服务类
 * 负责处理下载统计相关的业务逻辑
 */
class StatsService {
  private readonly statsBasePath: string;
  private readonly summaryFilePath: string;

  constructor() {
    this.statsBasePath = path.join(config.storage.basePath, 'stats');
    this.summaryFilePath = path.join(this.statsBasePath, 'summary.json');
  }

  /**
   * 初始化统计存储目录
   */
  async initializeStorage(): Promise<void> {
    try {
      await FileUtil.ensureDir(this.statsBasePath);
      await FileUtil.ensureDir(path.join(this.statsBasePath, 'downloads'));

      // 创建默认摘要文件
      if (!(await FileUtil.exists(this.summaryFilePath))) {
        const defaultSummary: DownloadStatsData = {
          totalDownloads: 0,
          totalBytes: 0,
          byVersion: {},
          byPlatform: {},
          byChannel: {},
          byDate: {},
          recentDownloads: [],
        };
        await FileUtil.writeJSONAtomic(this.summaryFilePath, defaultSummary);
      }

      logger.info('Stats storage initialized');
    } catch (error) {
      logger.error('Failed to initialize stats storage', { error });
      throw new Error(`Failed to initialize stats storage: ${error}`);
    }
  }

  /**
   * 记录下载统计
   * @param downloadInfo 下载信息
   */
  async recordDownload(downloadInfo: {
    filename: string;
    version: string;
    platform: string;
    architecture: string;
    channel: string;
    clientIP: string;
    userAgent: string;
    fileSize: number;
    downloadDuration?: number;
  }): Promise<void> {
    try {
      const record: DownloadRecord = {
        id: StatsUtil.generateRecordId(),
        filename: downloadInfo.filename,
        version: downloadInfo.version,
        platform: downloadInfo.platform,
        architecture: downloadInfo.architecture,
        channel: downloadInfo.channel,
        downloadTime: new Date().toISOString(),
        clientIP: StatsUtil.anonymizeIP(downloadInfo.clientIP),
        userAgent: downloadInfo.userAgent,
        fileSize: downloadInfo.fileSize,
        downloadDuration: downloadInfo.downloadDuration,
      };

      // 异步记录，不阻塞下载响应
      setImmediate(async () => {
        try {
          await this.saveDownloadRecord(record);
          await this.updateSummary(record);

          logger.debug('Download recorded', {
            id: record.id,
            filename: record.filename,
            version: record.version,
            platform: record.platform,
          });
        } catch (error) {
          logger.error('记录下载统计失败', {
            error: {
              name: error instanceof Error ? error.name : 'Unknown',
              message: error instanceof Error ? error.message : String(error),
              code: (error as any)?.code,
              stack: error instanceof Error ? error.stack : undefined,
              type: typeof error,
            },
            downloadRecord: {
              id: record.id,
              filename: record.filename,
              version: record.version,
              platform: record.platform,
              architecture: record.architecture,
              channel: record.channel,
              fileSize: record.fileSize,
              downloadTime: record.downloadTime,
              clientIP: record.clientIP,
              downloadDuration: record.downloadDuration,
            },
            systemInfo: {
              statsBasePath: this.statsBasePath,
              summaryFilePath: this.summaryFilePath,
              timestamp: new Date().toISOString(),
            },
            timing: {
              timestamp: new Date().toISOString(),
            },
            operation: 'record_download_failed',
            module: 'version_management',
          });
        }
      });
    } catch (error) {
      logger.error('创建下载记录失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          stack: error instanceof Error ? error.stack : undefined,
          type: typeof error,
        },
        downloadInfo: {
          filename: downloadInfo.filename,
          version: downloadInfo.version,
          platform: downloadInfo.platform,
          architecture: downloadInfo.architecture,
          channel: downloadInfo.channel,
          fileSize: downloadInfo.fileSize,
          clientIP: StatsUtil.anonymizeIP(downloadInfo.clientIP),
          userAgent: downloadInfo.userAgent?.substring(0, 100) + (downloadInfo.userAgent?.length > 100 ? '...' : ''),
          downloadDuration: downloadInfo.downloadDuration,
        },
        systemInfo: {
          statsBasePath: this.statsBasePath,
          summaryFilePath: this.summaryFilePath,
          timestamp: new Date().toISOString(),
        },
        timing: {
          timestamp: new Date().toISOString(),
        },
        operation: 'create_download_record_failed',
        module: 'version_management',
      });
      // 不抛出错误，避免影响下载功能
    }
  }

  /**
   * 获取下载统计数据
   * @param query 查询条件
   * @returns 统计数据
   */
  async getDownloadStats(query: DownloadStatsQuery = {}): Promise<DownloadStatsData> {
    try {
      // 验证查询参数
      const validation = StatsUtil.validateStatsQuery(query);
      if (!validation.isValid) {
        throw new Error(`Invalid query parameters: ${validation.errors.join(', ')}`);
      }

      // 如果没有特定查询条件，返回摘要数据
      if (!query.startDate && !query.endDate && !query.version && !query.platform && !query.channel) {
        return this.getSummaryStats();
      }

      // 根据查询条件获取详细数据
      const records = await this.getDownloadRecords(query);
      return StatsUtil.aggregateStats(records);
    } catch (error) {
      logger.error('Failed to get download stats', { error, query });
      throw new Error(`Failed to get download stats: ${error}`);
    }
  }

  /**
   * 获取下载记录
   * @param query 查询条件
   * @returns 下载记录数组
   */
  async getDownloadRecords(query: DownloadStatsQuery = {}): Promise<DownloadRecord[]> {
    try {
      const validation = StatsUtil.validateStatsQuery(query);
      if (!validation.isValid) {
        throw new Error(`Invalid query parameters: ${validation.errors.join(', ')}`);
      }

      let allRecords: DownloadRecord[] = [];

      // 确定需要读取的日期范围
      const dateRange = this.getDateRange(query);

      for (const date of dateRange) {
        const dailyRecords = await this.getDailyRecords(date);
        allRecords = allRecords.concat(dailyRecords);
      }

      // 应用过滤条件
      return StatsUtil.filterRecords(allRecords, query);
    } catch (error) {
      logger.error('Failed to get download records', { error, query });
      throw new Error(`Failed to get download records: ${error}`);
    }
  }

  /**
   * 获取统计摘要
   * @returns 统计摘要数据
   */
  async getSummaryStats(): Promise<DownloadStatsData> {
    try {
      if (await FileUtil.exists(this.summaryFilePath)) {
        return await FileUtil.readJSON<DownloadStatsData>(this.summaryFilePath);
      }

      // 如果摘要文件不存在，返回默认数据
      return {
        totalDownloads: 0,
        totalBytes: 0,
        byVersion: {},
        byPlatform: {},
        byChannel: {},
        byDate: {},
        recentDownloads: [],
      };
    } catch (error) {
      logger.error('Failed to get summary stats', { error });
      throw new Error(`Failed to get summary stats: ${error}`);
    }
  }

  /**
   * 清理旧的统计数据
   * @param daysToKeep 保留天数
   */
  async cleanupOldStats(daysToKeep: number = 90): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const downloadsDir = path.join(this.statsBasePath, 'downloads');
      const files = await FileUtil.listFiles(downloadsDir);

      let deletedCount = 0;
      for (const file of files) {
        if (file.endsWith('.json')) {
          const dateStr = file.replace('.json', '');
          const fileDate = new Date(dateStr);

          if (fileDate < cutoffDate) {
            await FileUtil.remove(path.join(downloadsDir, file));
            deletedCount++;
          }
        }
      }

      logger.info('Cleaned up old stats files', { deletedCount, daysToKeep });
    } catch (error) {
      logger.error('Failed to cleanup old stats', { error, daysToKeep });
      throw new Error(`Failed to cleanup old stats: ${error}`);
    }
  }

  /**
   * 保存下载记录到日期文件
   * @param record 下载记录
   */
  private async saveDownloadRecord(record: DownloadRecord): Promise<void> {
    const dateStr = StatsUtil.getDateString(new Date(record.downloadTime));
    const dailyFilePath = path.join(this.statsBasePath, 'downloads', `${dateStr}.json`);

    let dailyRecords: DownloadRecord[] = [];

    // 读取现有记录
    if (await FileUtil.exists(dailyFilePath)) {
      dailyRecords = await FileUtil.readJSON<DownloadRecord[]>(dailyFilePath);
    }

    // 添加新记录
    dailyRecords.push(record);

    // 保存到文件
    await FileUtil.writeJSONAtomic(dailyFilePath, dailyRecords);
  }

  /**
   * 更新统计摘要
   * @param record 新的下载记录
   */
  private async updateSummary(record: DownloadRecord): Promise<void> {
    const summary = await this.getSummaryStats();

    // 更新总计数据
    summary.totalDownloads++;
    summary.totalBytes += record.fileSize;

    // 更新分类统计
    summary.byVersion[record.version] = (summary.byVersion[record.version] || 0) + 1;
    summary.byPlatform[record.platform] = (summary.byPlatform[record.platform] || 0) + 1;
    summary.byChannel[record.channel] = (summary.byChannel[record.channel] || 0) + 1;

    const dateKey = record.downloadTime.split('T')[0];
    if (dateKey) {
      summary.byDate[dateKey] = (summary.byDate[dateKey] || 0) + 1;
    }

    // 更新最近下载记录
    summary.recentDownloads.unshift(record);
    summary.recentDownloads = summary.recentDownloads.slice(0, 100); // 保留最近100条

    // 保存摘要
    await FileUtil.writeJSONAtomic(this.summaryFilePath, summary);
  }

  /**
   * 获取指定日期的下载记录
   * @param date 日期字符串 (YYYY-MM-DD)
   * @returns 下载记录数组
   */
  private async getDailyRecords(date: string): Promise<DownloadRecord[]> {
    const dailyFilePath = path.join(this.statsBasePath, 'downloads', `${date}.json`);

    if (await FileUtil.exists(dailyFilePath)) {
      return await FileUtil.readJSON<DownloadRecord[]>(dailyFilePath);
    }

    return [];
  }

  /**
   * 根据查询条件确定日期范围
   * @param query 查询条件
   * @returns 日期字符串数组
   */
  private getDateRange(query: DownloadStatsQuery): string[] {
    const dates: string[] = [];

    const startDate = query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 默认30天前
    const endDate = query.endDate ? new Date(query.endDate) : new Date(); // 默认今天

    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      dates.push(StatsUtil.getDateString(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  }

  /**
   * 清除缓存（JSON文件服务不需要缓存，但为了接口兼容性）
   */
  clearCache(): void {
    logger.debug('Stats JSON service cache cleared (no-op)');
  }
}

export const statsService = new StatsService();
