import type { RenderableEvent, BatchUpdateOptions, EventUpdateItem } from "../types";
import { validateEventsData } from "../utils/validation";
import { sortEventsByStartTime, calculateEventsTotalDuration, filterEventsOutsideTimeRange } from "../utils/converters";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 批量操作相关接口
 */
export interface BatchActions {
  setBatchEvents: (events: RenderableEvent[], options?: BatchUpdateOptions) => void;
  updateBatchEvents: (updates: EventUpdateItem[], options?: BatchUpdateOptions) => void;
  replaceEventsInRange: (startTime: number, endTime: number, newEvents: RenderableEvent[], options?: BatchUpdateOptions) => void;
}

/**
 * 创建批量操作
 */
export function createBatchActions(
  state: {
    events: RenderableEvent[];
    selectedEventId: string | null;
    selectedCurvePointIndex: number;
    totalDuration: number;
    isDurationLockedByAudio: boolean;
  },
  setState: (updates: Partial<typeof state>, options?: { skipHistoryRecord?: boolean }) => void,
  fileUuid: string,
  onFileStateChange?: (fileUuid: string, events: RenderableEvent[]) => void,
  ensureHistoryInitialized?: () => void
): BatchActions {
  const markFileAsUnsaved = () => {
    try {
      import("../../haptics-project-store").then(({ useProjectStore }) => {
        const projectStore = useProjectStore();
        if (projectStore.selectedFileUuid) {
          projectStore.markFileAsUnsaved(projectStore.selectedFileUuid);
          projectStore.updateFileCacheEvents(projectStore.selectedFileUuid, state.events);
        }
      });
    } catch (error) {
      logger.warn(LogModule.WAVEFORM, "Failed to mark file as unsaved or update cache", error);
    }
  };

  return {
    /**
     * 批量设置事件数据
     */
    setBatchEvents(events: RenderableEvent[], options: BatchUpdateOptions = {}) {
      const { preserveSelection = true, skipValidation = false, skipFileStateSync = false, skipHistoryRecord = false } = options;

      logger.debug(LogModule.WAVEFORM, `批量设置事件: ${events.length} 个事件`, options);

      // 数据验证
      if (!skipValidation) {
        const validation = validateEventsData(events);
        if (!validation.valid) {
          throw new Error(`Invalid events data: ${validation.errors.join(", ")}`);
        }
      }

      // 确保历史记录系统在有数据时正确初始化
      if (events.length > 0 && ensureHistoryInitialized) {
        ensureHistoryInitialized();
      }

      // 保存当前选中状态
      const currentSelectedId = preserveSelection ? state.selectedEventId : null;
      const currentCurvePointIndex = preserveSelection ? state.selectedCurvePointIndex : -1;

      // 排序事件数据
      const sortedEvents = sortEventsByStartTime([...events]);

      // 恢复选中状态（如果事件仍存在）
      let newSelectedEventId = null;
      let newSelectedCurvePointIndex = -1;

      if (currentSelectedId && preserveSelection) {
        const stillExists = sortedEvents.find((e) => e.id === currentSelectedId);
        if (stillExists) {
          newSelectedEventId = currentSelectedId;
          newSelectedCurvePointIndex = currentCurvePointIndex;
        }
      }

      // 重新计算总时长（如果音频未锁定）
      let newTotalDuration = state.totalDuration;
      if (!state.isDurationLockedByAudio) {
        const calculatedDuration = calculateEventsTotalDuration(sortedEvents);
        if (calculatedDuration > state.totalDuration) {
          newTotalDuration = calculatedDuration;
        }
      }

      // 标记文件为未保存状态
      markFileAsUnsaved();

      // 更新状态
      setState({
        events: sortedEvents,
        selectedEventId: newSelectedEventId,
        selectedCurvePointIndex: newSelectedCurvePointIndex,
        totalDuration: newTotalDuration,
      }, { skipHistoryRecord });

      // 同步到文件状态管理器
      if (!skipFileStateSync) {
        onFileStateChange?.(fileUuid, sortedEvents);
      }

      logger.debug(LogModule.WAVEFORM, `批量设置完成: ${sortedEvents.length} 个事件`);
    },

    /**
     * 增量更新事件数据
     */
    updateBatchEvents(updates: EventUpdateItem[], options: BatchUpdateOptions = {}) {
      if (!updates || updates.length === 0) {
        logger.debug(LogModule.WAVEFORM, "批量更新：无更新项");
        return;
      }

      logger.debug(LogModule.WAVEFORM, `批量更新事件: ${updates.length} 个更新项`);

      const updatedEvents = [...state.events];
      let hasChanges = false;

      updates.forEach((update) => {
        const index = updatedEvents.findIndex((e) => e.id === update.id);
        if (index !== -1) {
          // 使用类型断言来避免严格的类型检查，因为我们知道这是安全的合并操作
          updatedEvents[index] = { ...updatedEvents[index], ...update.data } as RenderableEvent;
          hasChanges = true;
          logger.debug(LogModule.WAVEFORM, `更新事件: ${update.id}`, update.data);
        } else {
          logger.warn(LogModule.WAVEFORM, `事件不存在，跳过更新: ${update.id}`);
        }
      });

      if (hasChanges) {
        this.setBatchEvents(updatedEvents, options);
      }
    },

    /**
     * 替换指定时间范围内的事件
     */
    replaceEventsInRange(startTime: number, endTime: number, newEvents: RenderableEvent[], options: BatchUpdateOptions = {}) {
      logger.debug(LogModule.WAVEFORM, `替换时间范围内的事件: ${startTime}-${endTime}ms, 新事件: ${newEvents.length} 个`);

      // 过滤掉指定时间范围内的事件
      const filteredEvents = filterEventsOutsideTimeRange(state.events, startTime, endTime);

      // 合并新事件
      const mergedEvents = [...filteredEvents, ...newEvents];

      // 批量设置合并后的事件
      this.setBatchEvents(mergedEvents, options);

      logger.debug(LogModule.WAVEFORM, `时间范围替换完成: 保留 ${filteredEvents.length} 个事件，添加 ${newEvents.length} 个新事件`);
    },
  };
}
