import type { Ref } from "vue";
import { unref } from "vue";
import type { RenderableEvent, RenderableTransientEvent, RenderableContinuousEvent } from "@/types/haptic-editor";
import { DEBUG_EXPERIMENTS } from "../config/waveform-constants";
import { waveformLogger } from "@/utils/logger/logger";
import { createSmartRedrawManager, type RedrawType, type RedrawStrategy } from "@/utils/performance/SmartRedrawManager";
import { createRenderPriorityQueue } from "@/utils/performance/RenderPriorityQueue";

// 使用 ReturnType 来获取正确的 Store 类型
type FileWaveformEditorStore = ReturnType<typeof import("@/stores/haptics-editor-store").useFileWaveformEditorStore>;

/**
 * 波形图绘制编排器 Composable
 * 统一管理所有绘制函数和调度逻辑，减少主组件的复杂度
 */

export interface DrawingOrchestratorConfig {
  // 基础状态
  canvasCtx: Ref<CanvasRenderingContext2D | null>;
  waveformCanvas: Ref<HTMLCanvasElement | null>;
  canvasWidth: Ref<number>;
  canvasHeight: Ref<number>;
  waveformStore: FileWaveformEditorStore;
  
  // 绘制函数
  drawTransientEvent: (ctx: CanvasRenderingContext2D, event: RenderableTransientEvent) => void;
  drawContinuousEvent: (ctx: CanvasRenderingContext2D, event: RenderableContinuousEvent) => void;
  drawTransientEventSimplified: (ctx: CanvasRenderingContext2D, event: RenderableTransientEvent) => void;
  drawContinuousEventSimplified: (ctx: CanvasRenderingContext2D, event: RenderableContinuousEvent) => void;
  drawFullGrid: (ctx: CanvasRenderingContext2D) => void;
  drawGrid: (ctx: CanvasRenderingContext2D, forceRedraw?: boolean) => void;
  drawAudioWaveform: (ctx: CanvasRenderingContext2D) => void;
  drawGuideLines: (ctx: CanvasRenderingContext2D, isDragging: boolean) => void;
  
  // 状态检查函数
  shouldRedraw: () => boolean;
  hasAudioData: Ref<boolean>;
  isDragging: Ref<boolean>;
  draggedEvent: Ref<RenderableEvent | null>;
  
  // 工具函数
  // getVisibleEvents 已移除，直接使用传入的getEvents函数
  getGraphAreaWidth: () => number;
  getGraphAreaHeight: () => number;
  // updatePerformanceStats 已移除
  createPreviewEvent: () => RenderableEvent | null;
  
  // 常量
  PADDING: { top: number; right: number; bottom: number; left: number };
}

export interface DrawingFunctions {
  drawWaveformMain: (forceRedraw?: boolean) => void;
  drawWaveformLightweight: (forceRedraw?: boolean) => void;
  drawEventsWithDragOptimization: (ctx: CanvasRenderingContext2D, events: RenderableEvent[]) => void;
  drawEventsLightweight: (ctx: CanvasRenderingContext2D, events: RenderableEvent[]) => void;
  // 新增智能重绘接口
  requestSmartRedraw: (type: RedrawType, force?: boolean, context?: any) => void;
  immediateRedraw: (type: RedrawType, force?: boolean, context?: any) => void;
  cancelRedraw: (type: RedrawType) => void;
  getPerformanceMetrics: () => any;
}

export function useWaveformDrawingOrchestrator(
  config: DrawingOrchestratorConfig,
  props: {
    showAudioWaveform?: boolean | Ref<boolean>;
  },
  getEvents: () => RenderableEvent[]
): DrawingFunctions {

  // 创建渲染优先级队列
  const renderQueue = createRenderPriorityQueue(16); // 16ms时间片

  // 缓存清理函数
  const clearCache = () => {
    // 这里可以添加具体的缓存清理逻辑
    // 例如清理事件绘制缓存等
  };

  // 策略化的绘制函数
  const drawWaveformWithStrategy = (strategy: RedrawStrategy) => {
    if (!config.canvasCtx.value || !config.waveformCanvas.value ||
        config.canvasWidth.value <= 0 || config.canvasHeight.value <= 0) return;

    const ctx = config.canvasCtx.value;
    const startTime = performance.now();

    // 清除画布
    ctx.clearRect(0, 0, config.canvasWidth.value, config.canvasHeight.value);

    // 根据策略绘制网格
    if (!strategy.skipGrid) {
      if (strategy.simplifiedEvents) {
        config.drawGrid(ctx, false);
      } else {
        config.drawFullGrid(ctx);
      }
    }

    // 根据策略绘制音频波形
    const showAudioWaveformValue = unref(props.showAudioWaveform);
    const shouldDrawAudio = !strategy.skipAudio && config.hasAudioData.value && showAudioWaveformValue;

    if (shouldDrawAudio) {
      config.drawAudioWaveform(ctx);
    }

    // 绘制事件
    const events = getEvents();
    if (events && events.length > 0) {
      if (strategy.simplifiedEvents) {
        drawEventsLightweight(ctx, events);
      } else {
        drawEventsWithDragOptimization(ctx, events);
      }
    }

    // 根据策略绘制辅助线
    if (!strategy.skipGuides) {
      config.drawGuideLines(ctx, config.isDragging.value);
    }

    // 检查是否超出时间限制
    const frameTime = performance.now() - startTime;
    if (frameTime > strategy.maxFrameTime) {
      waveformLogger.warn(`绘制超时: ${frameTime.toFixed(2)}ms > ${strategy.maxFrameTime}ms`);
    }
  };

  const drawWaveformLightweightWithStrategy = (strategy: RedrawStrategy) => {
    if (!config.canvasCtx.value || !config.waveformCanvas.value ||
        config.canvasWidth.value <= 0 || config.canvasHeight.value <= 0) return;

    const ctx = config.canvasCtx.value;
    const startTime = performance.now();

    // 清除画布
    ctx.clearRect(0, 0, config.canvasWidth.value, config.canvasHeight.value);

    // 轻量级网格绘制
    if (!strategy.skipGrid) {
      config.drawGrid(ctx, false);
    }

    // 音频波形（如果策略允许）
    const showAudioWaveformValue = unref(props.showAudioWaveform);
    if (!strategy.skipAudio && config.hasAudioData.value && showAudioWaveformValue) {
      config.drawAudioWaveform(ctx);
    }

    // 轻量级事件绘制
    const events = getEvents();
    if (events && events.length > 0) {
      drawEventsLightweight(ctx, events);
    }

    // 轻量级辅助线
    if (!strategy.skipGuides) {
      config.drawGuideLines(ctx, config.isDragging.value);
    }

    // 检查性能
    const frameTime = performance.now() - startTime;
    if (frameTime > strategy.maxFrameTime) {
      waveformLogger.warn(`轻量级绘制超时: ${frameTime.toFixed(2)}ms > ${strategy.maxFrameTime}ms`);
    }
  };

  // 创建智能重绘管理器
  const smartRedrawManager = createSmartRedrawManager(
    drawWaveformWithStrategy,
    drawWaveformLightweightWithStrategy,
    clearCache
  );
  
  // 拖动时的分层绘制优化
  const drawEventsWithDragOptimization = (ctx: CanvasRenderingContext2D, events: RenderableEvent[]) => {
    if (DEBUG_EXPERIMENTS.DISABLE_DRAG_LAYER_OPTIMIZATION) {
      waveformLogger.debug("🔍 实验1: 禁用拖拽分层优化");
      // 直接使用传入的events，移除getVisibleEvents包装
      for (const event of events) {
        if (event.type === "transient") {
          config.drawTransientEvent(ctx, event as RenderableTransientEvent);
        } else if (event.type === "continuous") {
          config.drawContinuousEvent(ctx, event as RenderableContinuousEvent);
        }
      }
      return;
    }

    // 原始拖动状态分层绘制逻辑
    if (!config.isDragging.value || !config.draggedEvent.value) {
      // 直接使用传入的events，移除getVisibleEvents包装
      for (const event of events) {
        if (event.type === "transient") {
          config.drawTransientEvent(ctx, event as RenderableTransientEvent);
        } else if (event.type === "continuous") {
          config.drawContinuousEvent(ctx, event as RenderableContinuousEvent);
        }
      }
      return;
    }

    // 拖动状态：分层绘制优化
    const draggedEventId = config.draggedEvent.value.id;
    // 直接使用传入的events，移除getVisibleEvents包装
    const previewEvent = typeof config.createPreviewEvent === 'function' ? config.createPreviewEvent() : null;

    // 分离静态事件和动态事件
    const staticEvents: RenderableEvent[] = [];
    let dynamicEvent: RenderableEvent | null = null;

    for (const event of events) {
      if (event.id === draggedEventId) {
        dynamicEvent = previewEvent || event;
      } else {
        staticEvents.push(event);
      }
    }

    // 先绘制静态事件
    for (const event of staticEvents) {
      if (event.type === "transient") {
        config.drawTransientEvent(ctx, event as RenderableTransientEvent);
      } else if (event.type === "continuous") {
        config.drawContinuousEvent(ctx, event as RenderableContinuousEvent);
      }
    }

    // 最后绘制动态事件
    if (dynamicEvent) {
      if (dynamicEvent.type === "transient") {
        config.drawTransientEvent(ctx, dynamicEvent as RenderableTransientEvent);
      } else if (dynamicEvent.type === "continuous") {
        config.drawContinuousEvent(ctx, dynamicEvent as RenderableContinuousEvent);
      }
    }
  };

  // 轻量级事件绘制函数
  const drawEventsLightweight = (ctx: CanvasRenderingContext2D, events: RenderableEvent[]) => {
    // 直接使用传入的events，移除getVisibleEvents包装
    const selectedEventId = config.waveformStore.selectedEventId;

    for (const event of events) {
      const isSelected = event.id === selectedEventId;

      // 在拖拽状态下，所有事件都使用完整绘制以保持视觉一致性
      if (config.isDragging.value || isSelected) {
        // 选中事件或拖拽状态下使用完整绘制
        if (event.type === "transient") {
          config.drawTransientEvent(ctx, event as RenderableTransientEvent);
        } else if (event.type === "continuous") {
          config.drawContinuousEvent(ctx, event as RenderableContinuousEvent);
        }
      } else {
        // 非拖拽状态下的非选中事件使用简化绘制
        if (event.type === "transient") {
          config.drawTransientEventSimplified(ctx, event as RenderableTransientEvent);
        } else if (event.type === "continuous") {
          config.drawContinuousEventSimplified(ctx, event as RenderableContinuousEvent);
        }
      }
    }
  };

  // 轻量级重绘函数（保持向后兼容）
  const drawWaveformLightweight = (forceRedraw: boolean = false) => {
    // 使用智能重绘管理器，根据当前状态选择合适的重绘类型
    let redrawType: RedrawType = 'selection';

    if (config.isDragging.value) {
      redrawType = 'drag';
    } else if (config.waveformStore.isAdjustingProperties) {
      redrawType = 'property';
    }

    smartRedrawManager.requestRedraw(redrawType, forceRedraw);
  };

  // 主绘制函数（保持向后兼容）
  const drawWaveformMain = (forceRedraw: boolean = false) => {
    // 检查是否需要重绘
    if (!forceRedraw && !config.shouldRedraw()) {
      return;
    }

    // 使用智能重绘管理器，根据当前状态选择合适的重绘类型
    let redrawType: RedrawType = 'init';

    if (config.isDragging.value) {
      redrawType = 'drag';
    } else if (config.waveformStore.isAdjustingProperties) {
      redrawType = 'property';
    } else {
      redrawType = 'selection';
    }

    smartRedrawManager.requestRedraw(redrawType, forceRedraw);
  };

  return {
    drawWaveformMain,
    drawWaveformLightweight,
    drawEventsWithDragOptimization,
    drawEventsLightweight,

    // 新增智能重绘接口
    requestSmartRedraw: (type: RedrawType, force: boolean = false, context?: any) => {
      smartRedrawManager.requestRedraw(type, force, 'normal', context);
    },

    immediateRedraw: (type: RedrawType, force: boolean = true, context?: any) => {
      smartRedrawManager.immediateRedraw(type, force, context);
    },

    cancelRedraw: (type: RedrawType) => {
      smartRedrawManager.cancelRedraw(type);
    },

    getPerformanceMetrics: () => {
      return {
        smartRedraw: smartRedrawManager.getPerformanceMetrics(),
        renderQueue: renderQueue.getQueueStatus()
      };
    }
  };
}
