#!/usr/bin/env node

/**
 * 同步所有语言文件的翻译键，确保每个语言都有相同的键
 */

const fs = require('fs');
const path = require('path');

// 语言文件路径
const localeFiles = {
  'zh-CN': path.join(__dirname, '../src/locales/zh-CN.ts'),
  'en-US': path.join(__dirname, '../src/locales/en-US.ts'),
  'ja-JP': path.join(__dirname, '../src/locales/ja-JP.ts'),
  'ko-KR': path.join(__dirname, '../src/locales/ko-KR.ts'),
};

// 缺失键的翻译映射
const MISSING_TRANSLATIONS = {
  // 中文缺失的键
  'zh-CN': {
    'update.cancelling': '取消中',
    'update.processManagement.title': '进程管理确认',
    'update.processManagement.warningTitle': '警告',
    'update.processManagement.warningMessage': '安装更新需要关闭以下相关进程。请确保您已保存所有重要工作。',
    'update.processManagement.processListTitle': '需要关闭的进程',
    'update.processManagement.closeAndInstall': '关闭进程并安装',
    'update.processManagement.closeStrategyTitle': '关闭策略',
    'update.processManagement.gracefulClose': '优雅关闭',
    'update.processManagement.gracefulCloseDesc': '尝试正常关闭进程，给程序时间保存数据',
    'update.processManagement.forceClose': '强制关闭',
    'update.processManagement.forceCloseDesc': '立即终止进程，可能导致数据丢失',
    'update.processManagement.critical': '关键',
    'update.processManagement.noticeTitle': '重要提示',
    'update.processManagement.notice1': '请在关闭进程前保存所有重要工作',
    'update.processManagement.notice2': '关闭关键进程可能影响系统稳定性',
    'update.processManagement.notice3': '安装完成后应用程序将自动重启',
    'update.processManagement.processTypes.MainApplication': '主应用',
    'update.processManagement.processTypes.EditorWindow': '编辑器',
    'update.processManagement.processTypes.RenderProcess': '渲染器',
    'update.processManagement.processTypes.AudioService': '音频服务',
    'update.processManagement.processTypes.FileMonitor': '文件监控',
    'update.processManagement.processTypes.BackgroundService': '后台服务',
    'update.processManagement.processTypes.ChildProcess': '子进程',
    'update.processManagement.processTypes.Unknown': '未知进程',
  },
  
  // 英文缺失的键
  'en-US': {
    'demo.installProcessTitle': 'Installation Process',
    'demo.step1Title': 'User Confirms Installation',
    'demo.step1Description': 'Display installation confirmation dialog, explaining that the app will close and restart automatically',
    'demo.step2Title': 'Create Installation Script',
    'demo.step2Description': 'Generate independent batch script containing installation and restart logic',
    'demo.step3Title': 'Launch Independent Installation',
    'demo.step3Description': 'Start independent installer, current app exits',
    'demo.step4Title': 'Auto Restart Application',
    'demo.step4Description': 'Automatically start new version after installation completes',
    'update.processManagement.title': 'Process Management Confirmation',
    'update.processManagement.warningTitle': 'Warning',
    'update.processManagement.warningMessage': 'Installing the update requires closing the following related processes. Please ensure you have saved all important work.',
    'update.processManagement.processListTitle': 'Processes to Close',
    'update.processManagement.closeAndInstall': 'Close Processes and Install',
    'update.processManagement.closeStrategyTitle': 'Close Strategy',
    'update.processManagement.gracefulClose': 'Graceful Close',
    'update.processManagement.gracefulCloseDesc': 'Attempt to close processes normally, giving programs time to save data',
    'update.processManagement.forceClose': 'Force Close',
    'update.processManagement.forceCloseDesc': 'Immediately terminate processes, may cause data loss',
    'update.processManagement.critical': 'Critical',
    'update.processManagement.noticeTitle': 'Important Notes',
    'update.processManagement.notice1': 'Please save all important work before closing processes',
    'update.processManagement.notice2': 'Closing critical processes may affect system stability',
    'update.processManagement.notice3': 'The application will restart automatically after installation',
    'update.processManagement.processTypes.MainApplication': 'Main App',
    'update.processManagement.processTypes.EditorWindow': 'Editor',
    'update.processManagement.processTypes.RenderProcess': 'Renderer',
    'update.processManagement.processTypes.AudioService': 'Audio Service',
    'update.processManagement.processTypes.FileMonitor': 'File Monitor',
    'update.processManagement.processTypes.BackgroundService': 'Background Service',
    'update.processManagement.processTypes.ChildProcess': 'Child Process',
    'update.processManagement.processTypes.Unknown': 'Unknown Process',
  },
  
  // 日文缺失的键
  'ja-JP': {
    'common.retry': '再試行',
    'common.dismiss': '無視',
    'demo.installProcessTitle': 'インストールプロセス',
    'demo.step1Title': 'ユーザーがインストールを確認',
    'demo.step1Description': 'インストール確認ダイアログを表示し、アプリが閉じて自動的に再起動することを説明',
    'demo.step2Title': 'インストールスクリプトの作成',
    'demo.step2Description': 'インストールと再起動ロジックを含む独立したバッチスクリプトを生成',
    'demo.step3Title': '独立インストールの起動',
    'demo.step3Description': '独立したインストーラーを開始し、現在のアプリを終了',
    'demo.step4Title': 'アプリケーションの自動再起動',
    'demo.step4Description': 'インストール完了後、新しいバージョンを自動的に開始',
    'installer.ready': '準備完了',
    'installer.preparing': '準備中',
    'installer.installing': 'インストール中',
    'installer.success': 'インストール成功',
    'installer.failed': 'インストール失敗',
    'installer.cancelled': 'キャンセル済み',
    'installer.operations.initializing': '初期化中',
    'installer.operations.validating': 'パッケージを検証中',
    'installer.operations.waitingForExit': 'アプリの終了を待機中',
    'installer.operations.backingUp': 'ファイルをバックアップ中',
    'installer.operations.installing': 'インストール中',
    'installer.operations.restarting': 'アプリケーションを再起動中',
    'installer.operations.completed': 'インストール完了',
    'installer.operations.failed': 'インストール失敗',
    'installer.operations.cancelled': 'キャンセル済み',
    'installer.initializing': 'インストーラーを初期化中',
    'installer.installSuccess': 'インストールが正常に完了しました',
    'installer.unknownError': '不明なエラー',
    'installer.preparingExit': 'アプリケーションの終了を準備中',
    'installer.cancelling': 'キャンセル中',
    'installer.installCancelled': 'インストールがキャンセルされました',
  },
  
  // 韩文缺失的键
  'ko-KR': {
    'common.retry': '다시 시도',
    'common.dismiss': '무시',
    'demo.installProcessTitle': '설치 프로세스',
    'demo.step1Title': '사용자 설치 확인',
    'demo.step1Description': '설치 확인 대화상자를 표시하고 앱이 닫히고 자동으로 재시작됨을 설명',
    'demo.step2Title': '설치 스크립트 생성',
    'demo.step2Description': '설치 및 재시작 로직을 포함하는 독립적인 배치 스크립트 생성',
    'demo.step3Title': '독립 설치 시작',
    'demo.step3Description': '독립적인 설치 프로그램을 시작하고 현재 앱 종료',
    'demo.step4Title': '애플리케이션 자동 재시작',
    'demo.step4Description': '설치 완료 후 새 버전을 자동으로 시작',
    'installer.ready': '준비 완료',
    'installer.preparing': '준비 중',
    'installer.installing': '설치 중',
    'installer.success': '설치 성공',
    'installer.failed': '설치 실패',
    'installer.cancelled': '취소됨',
    'installer.operations.initializing': '초기화 중',
    'installer.operations.validating': '패키지 검증 중',
    'installer.operations.waitingForExit': '앱 종료 대기 중',
    'installer.operations.backingUp': '파일 백업 중',
    'installer.operations.installing': '설치 중',
    'installer.operations.restarting': '애플리케이션 재시작 중',
    'installer.operations.completed': '설치 완료',
    'installer.operations.failed': '설치 실패',
    'installer.operations.cancelled': '취소됨',
    'installer.initializing': '설치 프로그램 초기화 중',
    'installer.installSuccess': '설치가 성공적으로 완료되었습니다',
    'installer.unknownError': '알 수 없는 오류',
    'installer.preparingExit': '애플리케이션 종료 준비 중',
    'installer.cancelling': '취소 중',
    'installer.installCancelled': '설치가 취소되었습니다',
  }
};

// 提取翻译键的函数
function extractKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

// 加载语言文件
function loadLocaleFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const match = content.match(/export default\s+({[\s\S]*})\s*(?:as const)?;?\s*$/);
    if (!match) {
      throw new Error('无法解析语言文件格式');
    }
    
    const objStr = match[1];
    const obj = new Function('return ' + objStr)();
    
    return obj;
  } catch (error) {
    console.error(`❌ 加载语言文件失败: ${filePath}`);
    console.error(`   错误: ${error.message}`);
    return null;
  }
}

// 设置嵌套对象的值
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
}

// 格式化对象为 TypeScript 代码
function formatToTypeScript(obj, indent = 0) {
  const spaces = '  '.repeat(indent);
  let result = '{\n';
  
  const entries = Object.entries(obj);
  for (let i = 0; i < entries.length; i++) {
    const [key, value] = entries[i];
    const isLast = i === entries.length - 1;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      result += `${spaces}  // === ${key} ===\n`;
      result += `${spaces}  ${key}: ${formatToTypeScript(value, indent + 1)}`;
    } else {
      const escapedValue = typeof value === 'string' 
        ? `"${value.replace(/"/g, '\\"')}"` 
        : JSON.stringify(value);
      result += `${spaces}  ${key}: ${escapedValue}`;
    }
    
    if (!isLast) {
      result += ',\n';
    } else {
      result += '\n';
    }
  }
  
  result += `${spaces}}`;
  return result;
}

// 同步翻译键
function syncI18nKeys() {
  console.log('🔄 开始同步所有语言文件的翻译键...\n');
  
  for (const [locale, filePath] of Object.entries(localeFiles)) {
    console.log(`📝 处理 ${locale} 语言文件...`);
    
    const data = loadLocaleFile(filePath);
    if (!data) {
      continue;
    }
    
    const originalKeys = extractKeys(data);
    const missingTranslations = MISSING_TRANSLATIONS[locale] || {};
    
    // 添加缺失的翻译
    let addedCount = 0;
    for (const [key, translation] of Object.entries(missingTranslations)) {
      setNestedValue(data, key, translation);
      addedCount++;
    }
    
    const newKeys = extractKeys(data);
    
    console.log(`   原始键数量: ${originalKeys.length}`);
    console.log(`   添加键数量: ${addedCount}`);
    console.log(`   最终键数量: ${newKeys.length}`);
    
    if (addedCount > 0) {
      // 生成新的文件内容
      const header = `/**
 * ${locale === 'zh-CN' ? '简体中文语言包' : 
        locale === 'en-US' ? 'English Language Pack' :
        locale === 'ja-JP' ? '日本語言語パック' :
        locale === 'ko-KR' ? '한국어 언어팩' : '语言包'}
 */

export default `;
      
      const newContent = header + formatToTypeScript(data) + ' as const;\n';
      
      // 备份原文件
      const backupPath = filePath + '.backup-sync-' + Date.now();
      fs.copyFileSync(filePath, backupPath);
      console.log(`   ✅ 原文件已备份到: ${path.basename(backupPath)}`);
      
      // 写入新文件
      fs.writeFileSync(filePath, newContent);
      console.log(`   ✅ 已更新语言文件`);
    } else {
      console.log(`   ✅ 无需更新`);
    }
    
    console.log();
  }
  
  console.log('🎉 同步完成！');
}

// 运行同步
if (require.main === module) {
  syncI18nKeys();
}

module.exports = { syncI18nKeys };
