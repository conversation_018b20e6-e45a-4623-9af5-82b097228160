/**
 * 波形数据变更检测
 * 实现智能的数据变更检测，避免不必要的重新渲染
 */

import { ref, computed, watch, type Ref } from "vue";
import type { RenderableEvent } from "@/types/haptic-editor";
import { waveformLogger } from "@/utils/logger/logger";

/**
 * 生成事件数组的哈希值
 * 用于快速检测数据是否发生变化
 */
function generateEventsHash(events: RenderableEvent[]): string {
  if (!events || events.length === 0) {
    return "empty";
  }
  
  // 使用事件的关键属性生成哈希
  const keyData = events.map(event => {
    const baseData = `${event.id}-${event.type}-${event.startTime}`;
    
    if (event.type === "transient") {
      return `${baseData}-${event.intensity}-${event.frequency}-${event.peakTime}`;
    } else {
      return `${baseData}-${event.duration}-${event.eventIntensity}-${event.eventFrequency}-${event.curves.length}`;
    }
  }).join("|");
  
  // 简单的哈希函数
  let hash = 0;
  for (let i = 0; i < keyData.length; i++) {
    const char = keyData.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  
  return hash.toString(36);
}

/**
 * 生成单个事件的哈希值
 */
function generateEventHash(event: RenderableEvent): string {
  const baseData = `${event.id}-${event.type}-${event.startTime}`;
  
  if (event.type === "transient") {
    return `${baseData}-${event.intensity}-${event.frequency}-${event.peakTime}`;
  } else {
    const curvesHash = event.curves.map(curve => 
      `${curve.timeOffset}-${curve.drawIntensity}-${curve.curveFrequency}`
    ).join(",");
    return `${baseData}-${event.duration}-${event.eventIntensity}-${event.eventFrequency}-${curvesHash}`;
  }
}

/**
 * 变更检测配置
 */
export interface ChangeDetectionConfig {
  enableLogging?: boolean;
  enableEventLevelDetection?: boolean; // 是否启用事件级别的变更检测
  debounceMs?: number; // 防抖延迟
}

/**
 * 变更检测结果
 */
export interface ChangeDetectionResult {
  hasChanged: boolean;
  changedEventIds: string[];
  addedEventIds: string[];
  removedEventIds: string[];
  modifiedEventIds: string[];
  totalEvents?: number;
  changeTimestamp?: number;
}

/**
 * 智能变更检测Hook
 */
export function useWaveformChangeDetection(config: ChangeDetectionConfig = {}) {
  const {
    enableLogging = false,
    enableEventLevelDetection = true,
    debounceMs = 16
  } = config;
  
  // 存储上次的哈希值
  const lastEventsHash = ref<string>('');
  const lastEventHashes = ref<Map<string, string>>(new Map());
  
  // 变更统计
  const changeStats = ref({
    totalChecks: 0,
    changesDetected: 0,
    lastChangeTime: 0,
  });
  
  /**
   * 检测事件数组是否发生变化
   */
  const hasEventsChanged = (events: RenderableEvent[]): boolean => {
    changeStats.value.totalChecks++;
    
    const currentHash = generateEventsHash(events);
    const hasChanged = currentHash !== lastEventsHash.value;
    
    if (hasChanged) {
      changeStats.value.changesDetected++;
      changeStats.value.lastChangeTime = Date.now();
      lastEventsHash.value = currentHash;
      
      if (enableLogging) {
        waveformLogger.debug(`Events changed. New hash: ${currentHash}`);
      }
    }
    
    return hasChanged;
  };
  
  /**
   * 详细的变更检测，返回具体的变更信息
   */
  const detectDetailedChanges = (events: RenderableEvent[]): ChangeDetectionResult => {
    // 如果未启用事件级别检测，返回简化结果
    if (!enableEventLevelDetection) {
      return {
        hasChanged: hasEventsChanged(events),
        addedEventIds: [],
        modifiedEventIds: [],
        removedEventIds: [],
        changedEventIds: [],
        totalEvents: events.length,
        changeTimestamp: Date.now()
      };
    }
    const currentEventHashes = new Map<string, string>();
    const changedEventIds: string[] = [];
    const addedEventIds: string[] = [];
    const modifiedEventIds: string[] = [];
    
    // 生成当前事件的哈希映射
    events.forEach(event => {
      const hash = generateEventHash(event);
      currentEventHashes.set(event.id, hash);
      
      if (!lastEventHashes.value.has(event.id)) {
        // 新增的事件
        addedEventIds.push(event.id);
        changedEventIds.push(event.id);
      } else if (lastEventHashes.value.get(event.id) !== hash) {
        // 修改的事件
        modifiedEventIds.push(event.id);
        changedEventIds.push(event.id);
      }
    });
    
    // 检测删除的事件
    const removedEventIds: string[] = [];
    lastEventHashes.value.forEach((_hash, eventId) => {
      if (!currentEventHashes.has(eventId)) {
        removedEventIds.push(eventId);
        changedEventIds.push(eventId);
      }
    });
    
    // 更新哈希映射
    lastEventHashes.value = currentEventHashes;
    
    const hasChanged = changedEventIds.length > 0;
    
    if (hasChanged && enableLogging) {
      waveformLogger.debug(`Detailed changes:`, {
        added: addedEventIds.length,
        modified: modifiedEventIds.length,
        removed: removedEventIds.length,
        total: changedEventIds.length
      });
    }
    
    return {
      hasChanged,
      changedEventIds,
      addedEventIds,
      removedEventIds,
      modifiedEventIds,
    };
  };
  
  /**
   * 重置变更检测状态
   */
  const resetChangeDetection = () => {
    lastEventsHash.value = '';
    lastEventHashes.value.clear();
    changeStats.value = {
      totalChecks: 0,
      changesDetected: 0,
      lastChangeTime: 0,
    };
    
    if (enableLogging) {
      waveformLogger.debug(`State reset`);
    }
  };
  
  /**
   * 强制标记为已变更
   */
  const forceMarkAsChanged = (events: RenderableEvent[]) => {
    lastEventsHash.value = generateEventsHash(events);
    
    const newEventHashes = new Map<string, string>();
    events.forEach(event => {
      newEventHashes.set(event.id, generateEventHash(event));
    });
    lastEventHashes.value = newEventHashes;
    
    changeStats.value.changesDetected++;
    changeStats.value.lastChangeTime = Date.now();
    
    if (enableLogging) {
      waveformLogger.debug(`Forced mark as changed`);
    }
  };
  
  /**
   * 获取变更统计信息
   */
  const getChangeStats = () => {
    return {
      ...changeStats.value,
      changeRate: changeStats.value.totalChecks > 0 
        ? (changeStats.value.changesDetected / changeStats.value.totalChecks * 100).toFixed(2) + '%'
        : '0%',
      timeSinceLastChange: changeStats.value.lastChangeTime > 0 
        ? Date.now() - changeStats.value.lastChangeTime
        : 0,
    };
  };
  
  return {
    // 基础变更检测
    hasEventsChanged,
    
    // 详细变更检测
    detectDetailedChanges,
    
    // 状态管理
    resetChangeDetection,
    forceMarkAsChanged,
    
    // 统计信息
    getChangeStats,
    changeStats: computed(() => getChangeStats()),
    
    // 内部状态（用于调试）
    lastEventsHash: computed(() => lastEventsHash.value),
    lastEventHashes: computed(() => lastEventHashes.value),

    // 配置信息
    config: {
      enableLogging,
      enableEventLevelDetection,
      debounceMs
    },
  };
}

/**
 * 防抖变更检测Hook
 * 在频繁变更的场景下提供防抖功能
 */
export function useDebouncedChangeDetection(
  events: Ref<RenderableEvent[]>,
  config: ChangeDetectionConfig = {}
) {
  const { debounceMs = 16 } = config;
  const changeDetection = useWaveformChangeDetection(config);
  
  const debouncedHasChanged = ref(false);
  const debouncedChangeResult = ref<ChangeDetectionResult | null>(null);
  
  let debounceTimer: number | null = null;
  
  // 监听事件变化，应用防抖
  watch(
    events,
    (newEvents) => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      
      debounceTimer = setTimeout(() => {
        const hasChanged = changeDetection.hasEventsChanged(newEvents);
        debouncedHasChanged.value = hasChanged;
        
        if (hasChanged && config.enableEventLevelDetection) {
          debouncedChangeResult.value = changeDetection.detectDetailedChanges(newEvents);
        }
        
        debounceTimer = null;
      }, debounceMs);
    },
    { immediate: true, deep: true }
  );
  
  return {
    ...changeDetection,
    debouncedHasChanged: computed(() => debouncedHasChanged.value),
    debouncedChangeResult: computed(() => debouncedChangeResult.value),
  };
}
