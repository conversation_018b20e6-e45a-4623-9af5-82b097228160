/**
 * 优雅关闭工具
 * 提供应用程序优雅关闭的功能
 */

import { invoke } from '@tauri-apps/api/core';
import { emit, listen } from '@tauri-apps/api/event';
import { logger, LogModule } from '@/utils/logger/logger';

export interface ShutdownOptions {
  /** 保存用户数据 */
  saveUserData?: boolean;
  /** 清理临时文件 */
  cleanupTempFiles?: boolean;
  /** 关闭前延迟时间（毫秒） */
  delay?: number;
  /** 强制关闭超时时间（毫秒） */
  forceTimeout?: number;
}

export interface ShutdownState {
  /** 是否正在关闭 */
  isShuttingDown: boolean;
  /** 当前步骤 */
  currentStep: string;
  /** 进度百分比 */
  progress: number;
  /** 错误信息 */
  error?: string;
}

/**
 * 优雅关闭管理器
 */
export class GracefulShutdownManager {
  private isShuttingDown = false;
  private shutdownCallbacks: Array<() => Promise<void>> = [];
  private state: ShutdownState = {
    isShuttingDown: false,
    currentStep: '',
    progress: 0,
  };

  constructor() {
    this.setupShutdownListeners();
  }

  /**
   * 注册关闭回调
   */
  registerShutdownCallback(callback: () => Promise<void>): void {
    this.shutdownCallbacks.push(callback);
  }

  /**
   * 移除关闭回调
   */
  unregisterShutdownCallback(callback: () => Promise<void>): void {
    const index = this.shutdownCallbacks.indexOf(callback);
    if (index > -1) {
      this.shutdownCallbacks.splice(index, 1);
    }
  }

  /**
   * 开始优雅关闭
   */
  async startGracefulShutdown(options: ShutdownOptions = {}): Promise<void> {
    if (this.isShuttingDown) {
      logger.warn(LogModule.GENERAL, '应用程序已在关闭过程中');
      return;
    }

    this.isShuttingDown = true;
    this.updateState({
      isShuttingDown: true,
      currentStep: '准备关闭',
      progress: 0,
    });

    logger.info(LogModule.GENERAL, '开始优雅关闭应用程序');

    try {
      // 1. 延迟（如果指定）
      if (options.delay && options.delay > 0) {
        this.updateState({
          currentStep: `等待 ${options.delay}ms`,
          progress: 10,
        });
        await this.delay(options.delay);
      }

      // 2. 执行注册的关闭回调
      this.updateState({
        currentStep: '执行关闭回调',
        progress: 20,
      });
      await this.executeShutdownCallbacks();

      // 3. 保存用户数据
      if (options.saveUserData !== false) {
        this.updateState({
          currentStep: '保存用户数据',
          progress: 40,
        });
        await this.saveUserData();
      }

      // 4. 清理临时文件
      if (options.cleanupTempFiles !== false) {
        this.updateState({
          currentStep: '清理临时文件',
          progress: 60,
        });
        await this.cleanupTempFiles();
      }

      // 5. 通知后端准备关闭
      this.updateState({
        currentStep: '通知后端关闭',
        progress: 80,
      });
      await this.notifyBackendShutdown();

      // 6. 最终关闭
      this.updateState({
        currentStep: '关闭应用程序',
        progress: 100,
      });

      logger.info(LogModule.GENERAL, '优雅关闭完成');

      // 实际关闭应用
      await invoke('graceful_exit');

    } catch (error) {
      logger.error(LogModule.GENERAL, '优雅关闭失败', error);
      this.updateState({
        error: error instanceof Error ? error.message : String(error),
      });

      // 如果优雅关闭失败，设置强制关闭超时
      const forceTimeout = options.forceTimeout || 5000;
      setTimeout(async () => {
        logger.warn(LogModule.GENERAL, '强制关闭应用程序');
        await invoke('force_exit');
      }, forceTimeout);
    }
  }

  /**
   * 获取当前关闭状态
   */
  getShutdownState(): ShutdownState {
    return { ...this.state };
  }

  /**
   * 检查是否正在关闭
   */
  isShuttingDownNow(): boolean {
    return this.isShuttingDown;
  }

  // === 私有方法 ===

  /**
   * 设置关闭监听器
   */
  private setupShutdownListeners(): void {
    // 监听窗口关闭事件
    window.addEventListener('beforeunload', (event) => {
      if (this.isShuttingDown) {
        return;
      }

      // 阻止默认关闭行为
      event.preventDefault();
      event.returnValue = '';

      // 开始优雅关闭
      this.startGracefulShutdown().catch(error => logger.error(LogModule.GENERAL, '优雅关闭失败', error));
    });

    // 监听 Tauri 关闭请求
    listen('tauri://close-requested', () => {
      if (!this.isShuttingDown) {
        this.startGracefulShutdown().catch(error => logger.error(LogModule.GENERAL, '优雅关闭失败', error));
      }
    }).catch(error => logger.error(LogModule.GENERAL, '监听关闭请求失败', error));
  }

  /**
   * 更新关闭状态
   */
  private updateState(updates: Partial<ShutdownState>): void {
    this.state = { ...this.state, ...updates };
    
    // 发送状态更新事件
    emit('shutdown-state-changed', this.state).catch(error => logger.error(LogModule.GENERAL, '发送关闭状态事件失败', error));
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 执行关闭回调
   */
  private async executeShutdownCallbacks(): Promise<void> {
    logger.info(LogModule.GENERAL, `执行 ${this.shutdownCallbacks.length} 个关闭回调`);

    for (let i = 0; i < this.shutdownCallbacks.length; i++) {
      try {
        await this.shutdownCallbacks[i]();
        logger.info(LogModule.GENERAL, `关闭回调 ${i + 1} 执行完成`);
      } catch (error) {
        logger.error(LogModule.GENERAL, `关闭回调 ${i + 1} 执行失败`, error);
        // 继续执行其他回调
      }
    }
  }

  /**
   * 保存用户数据
   */
  private async saveUserData(): Promise<void> {
    try {
      await invoke('save_user_data');
      logger.info(LogModule.GENERAL, '用户数据已保存');
    } catch (error) {
      logger.error(LogModule.GENERAL, '保存用户数据失败', error);
      throw error;
    }
  }

  /**
   * 清理临时文件
   */
  private async cleanupTempFiles(): Promise<void> {
    try {
      await invoke('cleanup_temp_files');
      logger.info(LogModule.GENERAL, '临时文件已清理');
    } catch (error) {
      logger.error(LogModule.GENERAL, '清理临时文件失败', error);
      throw error;
    }
  }

  /**
   * 通知后端准备关闭
   */
  private async notifyBackendShutdown(): Promise<void> {
    try {
      await invoke('prepare_shutdown');
      logger.info(LogModule.GENERAL, '后端已准备关闭');
    } catch (error) {
      logger.error(LogModule.GENERAL, '通知后端关闭失败', error);
      throw error;
    }
  }
}

// 导出单例实例
export const gracefulShutdownManager = new GracefulShutdownManager();

// 导出便捷函数
export const startGracefulShutdown = (options?: ShutdownOptions) => {
  return gracefulShutdownManager.startGracefulShutdown(options);
};

export const registerShutdownCallback = (callback: () => Promise<void>) => {
  gracefulShutdownManager.registerShutdownCallback(callback);
};

export const unregisterShutdownCallback = (callback: () => Promise<void>) => {
  gracefulShutdownManager.unregisterShutdownCallback(callback);
};

export const getShutdownState = () => {
  return gracefulShutdownManager.getShutdownState();
};

export const isShuttingDown = () => {
  return gracefulShutdownManager.isShuttingDownNow();
};
