#!/usr/bin/env pwsh

<#
.SYNOPSIS
    测试构建环境 - RealityTap Desktop
    Test build environment for RealityTap Desktop

.DESCRIPTION
    此脚本用于测试构建环境是否正确配置。
    This script tests if the build environment is properly configured.

.EXAMPLE
    .\scripts\test-build-environment.ps1
#>

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 获取项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot

Write-ColorOutput "🔍 RealityTap Desktop 构建环境测试" "Cyan"
Write-ColorOutput "═══════════════════════════════════════" "Cyan"
Write-ColorOutput ""

$AllPassed = $true

# 检查 Node.js
Write-ColorOutput "检查 Node.js..." "Yellow"
try {
    $NodeVersion = node --version 2>$null
    if ($NodeVersion) {
        Write-ColorOutput "✅ Node.js: $NodeVersion" "Green"
    } else {
        Write-ColorOutput "❌ Node.js 未安装或不在 PATH 中" "Red"
        $AllPassed = $false
    }
} catch {
    Write-ColorOutput "❌ Node.js 未安装或不在 PATH 中" "Red"
    $AllPassed = $false
}

# 检查 npm
Write-ColorOutput "检查 npm..." "Yellow"
try {
    $NpmVersion = npm --version 2>$null
    if ($NpmVersion) {
        Write-ColorOutput "✅ npm: v$NpmVersion" "Green"
    } else {
        Write-ColorOutput "❌ npm 未安装或不在 PATH 中" "Red"
        $AllPassed = $false
    }
} catch {
    Write-ColorOutput "❌ npm 未安装或不在 PATH 中" "Red"
    $AllPassed = $false
}

# 检查 PowerShell 版本
Write-ColorOutput "检查 PowerShell..." "Yellow"
$PSVersion = $PSVersionTable.PSVersion
if ($PSVersion.Major -ge 5) {
    Write-ColorOutput "✅ PowerShell: v$($PSVersion.Major).$($PSVersion.Minor)" "Green"
} else {
    Write-ColorOutput "❌ PowerShell 版本过低，建议使用 5.1 或更高版本" "Red"
    $AllPassed = $false
}

# 检查项目文件
Write-ColorOutput "检查项目文件..." "Yellow"
$RequiredFiles = @(
    "package.json",
    "src-tauri/tauri.conf.json",
    "build-servers.json",
    "scripts/build-interactive.ps1"
)

foreach ($File in $RequiredFiles) {
    if (Test-Path $File) {
        Write-ColorOutput "✅ $File" "Green"
    } else {
        Write-ColorOutput "❌ 缺少文件: $File" "Red"
        $AllPassed = $false
    }
}

# 检查 keys 目录
Write-ColorOutput "检查签名密钥..." "Yellow"
if (Test-Path "keys") {
    $KeyFiles = Get-ChildItem "keys" -Filter "*.key" | Where-Object { -not $_.Name.EndsWith(".pub") }
    if ($KeyFiles.Count -gt 0) {
        Write-ColorOutput "✅ 找到 $($KeyFiles.Count) 个签名密钥文件" "Green"
        foreach ($KeyFile in $KeyFiles) {
            Write-ColorOutput "   • $($KeyFile.Name)" "Gray"
        }
    } else {
        Write-ColorOutput "❌ keys 目录中没有找到签名密钥文件" "Red"
        $AllPassed = $false
    }
} else {
    Write-ColorOutput "❌ 缺少 keys 目录" "Red"
    $AllPassed = $false
}

# 检查依赖项
Write-ColorOutput "检查依赖项..." "Yellow"
if (Test-Path "node_modules") {
    Write-ColorOutput "✅ node_modules 目录存在" "Green"
    
    # 检查关键依赖项
    $KeyDependencies = @(
        "@tauri-apps/cli",
        "@tauri-apps/api",
        "vue",
        "vite"
    )
    
    foreach ($Dep in $KeyDependencies) {
        if (Test-Path "node_modules/$Dep") {
            Write-ColorOutput "✅ $Dep" "Green"
        } else {
            Write-ColorOutput "❌ 缺少依赖项: $Dep" "Red"
            $AllPassed = $false
        }
    }
} else {
    Write-ColorOutput "❌ node_modules 目录不存在，请运行 npm install" "Red"
    $AllPassed = $false
}

Write-ColorOutput ""
Write-ColorOutput "═══════════════════════════════════════" "Cyan"

if ($AllPassed) {
    Write-ColorOutput "🎉 所有检查通过！构建环境配置正确。" "Green"
    Write-ColorOutput ""
    Write-ColorOutput "您现在可以运行交互式构建工具:" "White"
    Write-ColorOutput "  • 双击 build-interactive.bat" "Gray"
    Write-ColorOutput "  • 或运行 npm run build:interactive" "Gray"
} else {
    Write-ColorOutput "❌ 发现问题，请解决后再运行构建工具。" "Red"
    Write-ColorOutput ""
    Write-ColorOutput "建议的解决步骤:" "Yellow"
    Write-ColorOutput "1. 安装 Node.js (https://nodejs.org/)" "White"
    Write-ColorOutput "2. 运行 npm install 安装依赖项" "White"
    Write-ColorOutput "3. 确保 keys 目录中有签名密钥文件" "White"
    exit 1
}

Write-ColorOutput ""
