/**
 * 控制台警告过滤器
 * 用于过滤由浏览器扩展引起的Vue警告
 */

// 保存原始的console方法
const originalWarn = console.warn;
const originalError = console.error;

// 需要过滤的警告模式
const FILTERED_WARNINGS = [
  // Vue编译器选项警告（由扩展引起）
  /The `compilerOptions` config option is only respected when using a build of Vue\.js/,
  // 组件实例键枚举警告（由扩展引起）
  /Avoid app logic that relies on enumerating keys on a component instance/,
  // 其他可能的扩展相关警告
  /content_injected\.js/,
  /injected\.js/,
  /extension.*\.js/,
];

// 检查是否应该过滤警告
function shouldFilterWarning(message: string): boolean {
  return FILTERED_WARNINGS.some(pattern => pattern.test(message));
}

// 检查调用栈是否来自扩展
function isFromExtension(stack?: string): boolean {
  if (!stack) return false;
  
  return /content_injected\.js|injected\.js|extension.*\.js|chrome-extension:|moz-extension:/.test(stack);
}

// 过滤后的console.warn
function filteredWarn(...args: any[]): void {
  const message = args.join(' ');
  const stack = new Error().stack;
  
  // 如果是需要过滤的警告或来自扩展，则不显示
  if (shouldFilterWarning(message) || isFromExtension(stack)) {
    return;
  }
  
  // 否则正常显示
  originalWarn.apply(console, args);
}

// 过滤后的console.error
function filteredError(...args: any[]): void {
  const message = args.join(' ');
  const stack = new Error().stack;
  
  // 如果来自扩展，则不显示
  if (isFromExtension(stack)) {
    return;
  }
  
  // 否则正常显示
  originalError.apply(console, args);
}

/**
 * 启用控制台过滤器
 */
export function enableConsoleFilter(): void {
  if (import.meta.env.DEV) {
    console.warn = filteredWarn;
    console.error = filteredError;
    
    console.log('🔇 控制台过滤器已启用，将过滤浏览器扩展引起的Vue警告');
  }
}

/**
 * 禁用控制台过滤器
 */
export function disableConsoleFilter(): void {
  console.warn = originalWarn;
  console.error = originalError;
  
  console.log('🔊 控制台过滤器已禁用');
}

/**
 * 显示过滤统计信息
 */
export function showFilterStats(): void {
  console.group('📊 控制台过滤器统计');
  console.log('过滤规则数量:', FILTERED_WARNINGS.length);
  console.log('当前状态:', console.warn === filteredWarn ? '已启用' : '已禁用');
  console.groupEnd();
}

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  (window as any).consoleFilter = {
    enable: enableConsoleFilter,
    disable: disableConsoleFilter,
    stats: showFilterStats,
  };
}
