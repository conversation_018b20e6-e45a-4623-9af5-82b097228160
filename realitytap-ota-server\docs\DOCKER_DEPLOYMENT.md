# RealityTap OTA Server Docker 部署指南

## 🚀 快速开始

### 1. 准备环境

确保您的系统已安装：
- Docker (版本 20.10+)
- Docker Compose (版本 1.29+)

### 2. 配置环境变量

#### 方法一：使用配置向导（推荐）

**Linux/macOS:**
```bash
# 运行交互式配置向导
./scripts/setup-env.sh
```

**Windows (需要 Git Bash):**
```bash
# 在 Git Bash 中运行
bash scripts/setup-env.sh

# 或者使用完整路径
"C:\Program Files\Git\bin\bash.exe" scripts/setup-env.sh
```

配置向导会自动：
- 引导您输入所有必要的配置项
- 自动生成安全的 JWT 密钥（32字符）
- 自动检测服务器公网 IP 地址
- 支持 HTTP 和 HTTPS 模式配置
- 自动备份现有 .env 文件
- 生成完整的 .env 配置文件

#### 方法二：手动配置

```bash
# 复制环境变量模板
cp docker/.env.example docker/.env

# 编辑配置文件（必须修改密码和密钥）
nano docker/.env
```

**必须配置的关键变量：**
```bash
# 管理员认证
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password_here

# JWT 密钥（至少32字符）
JWT_SECRET=your_super_secret_jwt_key_min_32_chars_long

# 基础URL
BASE_URL=http://localhost:3000
```

### 3. 部署服务

```bash
# HTTP 模式部署
./scripts/docker-deploy.sh

# HTTPS 模式部署（需要SSL证书）
./scripts/docker-deploy.sh -m https

# 强制重建镜像部署
./scripts/docker-deploy.sh -b
```

部署完成后访问：http://localhost:3000/admin

## 📋 环境变量配置

### 核心配置
| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `ADMIN_USERNAME` | ✅ | admin | 管理员用户名 |
| `ADMIN_PASSWORD` | ✅ | - | 管理员密码 |
| `JWT_SECRET` | ✅ | - | JWT密钥（≥32字符） |
| `BASE_URL` | ❌ | http://localhost:3000 | 外部访问URL |
| `HOST_PORT` | ❌ | 3000 | 宿主机端口 |

### HTTPS 配置
| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `SSL_CERT_HOST_PATH` | ✅* | ./ssl/cert.pem | SSL证书路径 |
| `SSL_KEY_HOST_PATH` | ✅* | ./ssl/key.pem | SSL私钥路径 |

*仅在 HTTPS 模式时必需

### 目录路径配置
| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `STORAGE_HOST_PATH` | ❌ | ./data/storage | 主机存储目录路径 |
| `KEYS_HOST_PATH` | ❌ | ./keys | 主机密钥目录路径 |

## 🗄️ 数据持久化

### 主机目录映射

部署脚本会自动创建以下目录结构并设置权限：

| 主机路径 | 容器路径 | 用途 | 环境变量 |
|---------|----------|------|----------|
| `./data/storage` | `/app/storage` | 应用数据存储 | `STORAGE_HOST_PATH` |
| `./keys` | `/app/keys` | 签名密钥文件 | `KEYS_HOST_PATH` |
| `./ssl/` | `/app/ssl/` | SSL证书文件 | `SSL_CERT_HOST_PATH`, `SSL_KEY_HOST_PATH` |

### 目录结构

```
docker/
├── data/storage/              # 应用数据目录
│   ├── database/              # 数据库文件
│   ├── releases/              # 应用安装包
│   ├── logs/                  # 日志文件
│   ├── backup/                # 备份文件
│   └── metadata/              # 元数据文件
├── keys/                      # 签名密钥目录
└── ssl/                       # SSL证书目录（HTTPS模式）
```

### 数据备份

```bash
# 备份数据库
docker exec realitytap-ota-server cp /app/storage/database/ota.db /app/storage/backup/backup_$(date +%Y%m%d).db

# 备份整个存储目录
tar czf storage-backup-$(date +%Y%m%d).tar.gz docker/data/
```

## 🔄 升级服务

```bash
# 升级到最新版本
./scripts/docker-upgrade.sh

# 升级到指定版本
./scripts/docker-upgrade.sh -v 1.2.3

# HTTPS模式升级
./scripts/docker-upgrade.sh -m https

# 回滚到上一版本
./scripts/docker-upgrade.sh --rollback
```

## 🧹 清理部署

```bash
# 完全清理部署（会提示是否删除数据）
./scripts/docker-cleanup.sh

# 手动清理
cd docker
docker-compose -f docker-compose.http.yml down
docker rmi realitytap-ota-server:latest
```


## 🔒 HTTPS 配置

### 准备 SSL 证书

```bash
# 创建 SSL 目录
mkdir -p ssl

# 自签名证书（开发环境）
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes

# 配置环境变量
echo "SSL_CERT_HOST_PATH=./ssl/cert.pem" >> docker/.env
echo "SSL_KEY_HOST_PATH=./ssl/key.pem" >> docker/.env

# HTTPS 模式部署
./scripts/docker-deploy.sh -m https
```

## 🐛 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看日志
docker-compose -f docker/docker-compose.http.yml logs realitytap-ota-server

# 重建服务
./scripts/docker-rebuild.sh
```

#### 2. 目录权限问题
```bash
# 检查目录权限
ls -la docker/data/storage/
ls -la keys/

# 重新设置权限
chmod -R 755 docker/data/storage/
chmod -R 755 keys/

# 重新部署
./scripts/docker-deploy.sh -b
```

#### 3. 目录权限问题
```bash
# 容器无法创建目录时的错误：
# mkdir: can't create directory '/app/storage/releases/stable': Permission denied

# 解决方案1：使用权限修复脚本（推荐）
./scripts/fix-permissions.sh

# 解决方案2：手动修复权限
sudo chown -R 1001:1001 /path/to/your/storage/directory
sudo chown -R 1001:1001 /path/to/your/keys/directory

# 解决方案3：如果无法设置所有者，使用777权限
chmod -R 777 /path/to/your/storage/directory
chmod -R 777 /path/to/your/keys/directory

# 重新启动容器
docker-compose -f docker/docker-compose.http.yml restart
```

#### 4. 升级失败
```bash
# 回滚到上一版本
./scripts/docker-upgrade.sh --rollback

# 强制重建后升级
./scripts/docker-rebuild.sh
./scripts/docker-upgrade.sh
```

## 📋 脚本使用指南

### 环境配置脚本

**Linux/macOS:**
```bash
./scripts/setup-env.sh
```

**Windows (Git Bash):**
```bash
bash scripts/setup-env.sh
```

**功能特性：**
- 交互式配置向导，引导用户输入所有必要配置
- 自动生成安全的 JWT 密钥（32字符）
- 自动检测服务器公网 IP 地址
- 支持 HTTP 和 HTTPS 模式配置
- 自动备份现有 .env 文件
- 生成完整的 .env 配置文件
- 密码强度验证（至少8个字符）
- 配置摘要确认

### 部署脚本选项
```bash
./scripts/docker-deploy.sh [OPTIONS]

-m, --mode MODE         # 部署模式: http 或 https (默认: http)
-e, --env-file FILE     # 环境变量文件 (默认: .env)
-b, --build             # 强制重建Docker镜像
-h, --help              # 显示帮助信息
```

### 升级脚本选项
```bash
./scripts/docker-upgrade.sh [OPTIONS]

-m, --mode MODE         # 部署模式: http 或 https (默认: http)
-v, --version VERSION   # 指定升级版本 (默认: latest)
--rollback              # 回滚到上一版本
-h, --help              # 显示帮助信息
```


