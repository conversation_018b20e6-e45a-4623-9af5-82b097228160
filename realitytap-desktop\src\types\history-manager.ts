/**
 * 历史记录管理系统的 TypeScript 类型定义
 * 简化版本，仅保留必要的类型
 */

import type { RenderableEvent } from './haptic-editor';

/**
 * 历史记录项接口
 */
export interface HistoryRecord {
  /** 记录的唯一标识符 */
  id: string;
  /** 记录创建时间戳 */
  timestamp: number;
  /** 完整的事件数据状态（深拷贝） */
  data: RenderableEvent[];
  /** 操作描述（可选，用于调试） */
  description?: string;
}

/**
 * 历史管理器配置选项
 */
export interface HistoryManagerConfig {
  /** 最大历史记录数量，默认50 */
  maxRecords?: number;
  /** 是否启用调试模式 */
  enableDebug?: boolean;
  /** 自定义深拷贝函数（可选） */
  customDeepClone?: (data: RenderableEvent[]) => RenderableEvent[];
  /** 数据变更检测函数（可选） */
  dataChangeDetector?: (oldData: RenderableEvent[], newData: RenderableEvent[]) => boolean;
}

/**
 * 默认配置常量（向后兼容）
 */
/**
 * 高效的事件数组深度克隆（避免JSON序列化）
 */
function fastDeepCloneEvents(events: RenderableEvent[]): RenderableEvent[] {
  return events.map(event => {
    const clonedEvent: any = {
      id: event.id,
      startTime: event.startTime,
      type: event.type
    };

    if (event.type === 'transient') {
      const transientEvent = event as any;
      clonedEvent.intensity = transientEvent.intensity;
      clonedEvent.frequency = transientEvent.frequency;
      clonedEvent.peakTime = transientEvent.peakTime;
      clonedEvent.stopTime = transientEvent.stopTime;

      // 克隆其他可能的属性
      if (transientEvent.waveform) clonedEvent.waveform = transientEvent.waveform;
      if (transientEvent.sharpness !== undefined) clonedEvent.sharpness = transientEvent.sharpness;

    } else if (event.type === 'continuous') {
      const continuousEvent = event as any;
      clonedEvent.eventIntensity = continuousEvent.eventIntensity;
      clonedEvent.eventFrequency = continuousEvent.eventFrequency;
      clonedEvent.duration = continuousEvent.duration;
      clonedEvent.stopTime = continuousEvent.stopTime;

      // 深度克隆curves数组，包含所有必要属性
      if (continuousEvent.curves && Array.isArray(continuousEvent.curves)) {
        clonedEvent.curves = continuousEvent.curves.map((curve: any) => ({
          timeOffset: curve.timeOffset,
          drawIntensity: curve.drawIntensity,
          rawIntensity: curve.rawIntensity,
          relativeCurveFrequency: curve.relativeCurveFrequency,
          curveFrequency: curve.curveFrequency,
          // 保持向后兼容性
          intensity: curve.intensity,
          frequency: curve.frequency
        }));
      }

      // 克隆其他可能的属性
      if (continuousEvent.waveform) clonedEvent.waveform = continuousEvent.waveform;
    }

    return clonedEvent as RenderableEvent;
  });
}

/**
 * 高效的事件比较（避免JSON序列化）
 */
function areEventsEqual(events1: RenderableEvent[], events2: RenderableEvent[]): boolean {
  if (events1.length !== events2.length) {
    return false;
  }

  return events1.every((event1, index) => {
    const event2 = events2[index];

    // 基础字段比较
    if (event1.id !== event2.id ||
        Math.abs(event1.startTime - event2.startTime) >= 0.001 ||
        event1.type !== event2.type) {
      return false;
    }

    // 根据事件类型比较特定字段
    if (event1.type === 'transient' && event2.type === 'transient') {
      const t1 = event1 as any;
      const t2 = event2 as any;
      return Math.abs((t1.intensity || 0) - (t2.intensity || 0)) < 0.001 &&
             Math.abs((t1.frequency || 0) - (t2.frequency || 0)) < 0.001;
    } else if (event1.type === 'continuous' && event2.type === 'continuous') {
      const c1 = event1 as any;
      const c2 = event2 as any;
      return Math.abs((c1.eventIntensity || 0) - (c2.eventIntensity || 0)) < 0.001 &&
             Math.abs((c1.eventFrequency || 0) - (c2.eventFrequency || 0)) < 0.001 &&
             Math.abs((c1.duration || 0) - (c2.duration || 0)) < 0.001;
    }

    return true;
  });
}

export const DEFAULT_HISTORY_CONFIG: Required<HistoryManagerConfig> = {
  maxRecords: 50,
  enableDebug: false,
  customDeepClone: (data: RenderableEvent[]) => fastDeepCloneEvents(data),
  dataChangeDetector: (oldData: RenderableEvent[], newData: RenderableEvent[]) => {
    if (oldData.length !== newData.length) return true;
    return oldData.some((event, index) => {
      const newEvent = newData[index];
      return !newEvent || event.id !== newEvent.id || !areEventsEqual([event], [newEvent]);
    });
  }
};
