// 生成标准.he文件内容
export function generateHeFileContent(durationMs: number | null, options?: {
  description?: string;
}): string {
  const duration = Math.max(durationMs || 88, 25);
  const today = new Date();
  const dateStr = `${today.getFullYear()}-${String(
    today.getMonth() + 1
  ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;
  const content = {
    Metadata: {
      Version: 1,
      Created: dateStr,
      Description: options?.description || "AWA RealityTap Haptics Effect File",
    },
    Pattern: [
      {
        Event: {
          Type: "continuous",
          RelativeTime: 0,
          Duration: duration,
          Parameters: {
            Frequency: 50,
            Intensity: 50,
            Curve: [
              { Time: 0, Intensity: 0, Frequency: 0 },
              { Time: 13, Intensity: 0.7, Frequency: 0 },
              { Time: 18, Intensity: 0.7, Frequency: 0 },
              { Time: 25, Intensity: 0, Frequency: 0 },
            ],
          },
        },
      },
    ],
  };
  return JSON.stringify(content, null, 2);
}

// 生成唯一.he文件名
export function getUniqueHeFileName(
  baseName: string,
  existingNames: Set<string>
): string {
  let counter = 1;
  let newFileName = `${baseName}_${counter}.he`;
  while (existingNames.has(newFileName)) {
    counter++;
    newFileName = `${baseName}_${counter}.he`;
  }
  return newFileName;
}
