import winston from 'winston';
import { Writable } from 'stream';
import { getSystemLogService, SystemLogService } from '@/services/system-log.service';

export interface DatabaseTransportOptions {
  level?: string;
  tableName?: string;
}

/**
 * Winston transport for writing logs to database
 */
export class DatabaseTransport extends winston.transports.Stream {
  private logService: SystemLogService;
  private isDbEnabled: boolean;

  constructor(opts: DatabaseTransportOptions = {}) {
    // 创建一个可写流
    const stream = new Writable({
      write(chunk, encoding, callback) {
        // 不实际写入任何地方，只是为了满足接口
        callback();
      }
    });

    super({ stream, ...opts });
    this.logService = getSystemLogService();
    this.isDbEnabled = process.env.DB_ENABLED === 'true';
  }

  /**
   * 检查数据库是否准备就绪
   */
  private isDatabaseReady(): boolean {
    try {
      // 尝试获取数据库连接来检查是否已初始化
      const { dbConnection } = require('@/database/connection');
      return dbConnection && dbConnection.getConnection && dbConnection.isInitialized;
    } catch (error) {
      // 如果模块还没有加载或初始化，返回false
      return false;
    }
  }

  override log(info: any, callback: () => void) {
    setImmediate(() => {
      this.emit('logged', info);
    });

    // 如果数据库未启用，直接返回
    if (!this.isDbEnabled) {
      callback();
      return;
    }

    // 检查数据库是否已准备就绪
    if (!this.isDatabaseReady()) {
      // 数据库未准备好，静默跳过
      callback();
      return;
    }

    // 异步写入数据库，不阻塞日志记录
    this.writeToDatabase(info).catch(error => {
      // 数据库写入失败不应该影响日志记录
      // 只在非连接错误时输出错误信息，避免启动时的噪音
      if (!error.message?.includes('Database not connected') &&
          !error.message?.includes('Database not initialized')) {
        console.error('数据库传输错误:', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        logInfo: {
          level: info.level,
          message: info.message?.substring(0, 100) + (info.message?.length > 100 ? '...' : ''),
          timestamp: info.timestamp,
          module: info.module || info.meta?.module,
          operation: info.operation || info.meta?.operation,
          clientIP: info.clientIP || info.meta?.clientIP || info.meta?.ip,
          path: info.path || info.meta?.path,
          method: info.method || info.meta?.method,
          statusCode: info.statusCode || info.meta?.statusCode || info.meta?.status,
        },
        transportInfo: {
          isDbEnabled: this.isDbEnabled,
          timestamp: new Date().toISOString(),
        },
          operation: 'database_transport_error',
          module: 'system',
        });
      }
    });

    callback();
  }

  private async writeToDatabase(info: any): Promise<void> {
    // 解析日志信息（在 try 块外定义，以便在 catch 块中使用）
    const timestamp = info.timestamp || new Date().toISOString();
    const level = info.level || 'info';
    const message = info.message || '';

    // 过滤掉数据库操作相关的日志，避免循环记录
    if (message.includes('DAO Operation') ||
        message.includes('Database transport') ||
        message.includes('SQL execution') ||
        message.includes('createSystemLog') ||
        message.includes('system_logs')) {
      return;
    }

    // 从meta中提取信息
    const meta = info.meta || {};
    const clientIP = info.clientIP || meta.clientIP || meta.ip;
    const userAgent = info.userAgent || meta.userAgent;
    const path = info.path || meta.path;
    const method = info.method || meta.method;
    const statusCode = info.statusCode || meta.statusCode || meta.status;
    const module = info.module || meta.module;
    const operation = info.operation || meta.operation;

    // 创建日志数据
    const logData = SystemLogService.createLogData(level, message, {
      module,
      operation,
      clientIP,
      userAgent,
      path,
      method,
      statusCode,
      meta: this.cleanMeta(meta)
    });

    try {
      // 写入数据库
      await this.logService.logToDatabase(logData);
    } catch (error) {
      // 静默处理错误，避免影响主要功能
      console.error('数据库传输写入失败:', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        originalLogInfo: {
          level: info.level,
          message: info.message?.substring(0, 100) + (info.message && info.message.length > 100 ? '...' : ''),
          timestamp: info.timestamp,
        },
        processedLogData: {
          level: logData.level,
          message: logData.message?.substring(0, 100) + (logData.message && logData.message.length > 100 ? '...' : ''),
          module: logData.module,
          operation: logData.operation,
          client_ip: logData.client_ip,
          path: logData.path,
          method: logData.method,
          status_code: logData.status_code,
          timestamp: logData.timestamp,
        },
        transportInfo: {
          isDbEnabled: this.isDbEnabled,
          hasLogService: !!this.logService,
          timestamp: new Date().toISOString(),
        },
        operation: 'database_transport_write_failed',
        module: 'system',
      });
    }
  }

  /**
   * 清理meta对象，移除已经提取的字段
   */
  private cleanMeta(meta: any): any {
    if (!meta || typeof meta !== 'object') {
      return meta;
    }

    const cleaned = { ...meta };
    
    // 移除已经作为单独字段存储的属性
    delete cleaned.clientIP;
    delete cleaned.ip;
    delete cleaned.userAgent;
    delete cleaned.path;
    delete cleaned.method;
    delete cleaned.statusCode;
    delete cleaned.status;
    delete cleaned.module;
    delete cleaned.operation;
    
    // 如果清理后的对象为空，返回null
    return Object.keys(cleaned).length > 0 ? cleaned : null;
  }
}

export default DatabaseTransport;
