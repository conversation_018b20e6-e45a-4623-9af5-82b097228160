<template>
  <div class="dashboard">
    <n-page-header title="仪表板" subtitle="系统概览和统计信息">
      <template #extra>
        <n-button
          type="primary"
          :loading="adminStore.loading"
          @click="adminStore.refreshData()"
        >
          <template #icon>
            <n-icon :component="RefreshOutline" />
          </template>
          刷新数据
        </n-button>
      </template>
    </n-page-header>

    <n-space vertical size="large">
      <!-- 统计卡片 -->
      <n-grid :cols="4" :x-gap="16" responsive="screen">
        <n-grid-item :span="1">
          <n-card>
            <n-statistic label="系统运行时间" :value="adminStore.systemUptime">
              <template #prefix>
                <n-icon :component="TimeOutline" color="#18a058" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>

        <n-grid-item :span="1">
          <n-card>
            <n-statistic label="版本总数" :value="adminStore.totalVersions">
              <template #prefix>
                <n-icon :component="FolderOutline" color="#2080f0" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>

        <n-grid-item :span="1">
          <n-card>
            <n-statistic 
              label="内存使用率" 
              :value="adminStore.stats?.system.memory.percentage || 0"
              suffix="%"
            >
              <template #prefix>
                <n-icon :component="HardwareChipOutline" color="#f0a020" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>

        <n-grid-item :span="1">
          <n-card>
            <n-statistic 
              label="总下载次数" 
              :value="adminStore.stats?.ota.totalDownloads || 0"
            >
              <template #prefix>
                <n-icon :component="DownloadOutline" color="#d03050" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 系统信息 -->
      <n-card title="系统信息">
        <template #header-extra>
          <n-tag type="success" v-if="adminStore.stats">
            运行正常
          </n-tag>
        </template>

        <n-descriptions
          v-if="adminStore.stats"
          :column="2"
          label-placement="left"
          bordered
        >
          <n-descriptions-item label="Node.js 版本">
            {{ adminStore.stats.system.nodeVersion }}
          </n-descriptions-item>
          <n-descriptions-item label="操作系统">
            {{ adminStore.stats.system.platform }}
          </n-descriptions-item>
          <n-descriptions-item label="系统架构">
            {{ adminStore.stats.system.architecture }}
          </n-descriptions-item>
          <n-descriptions-item label="内存使用">
            {{ formatBytes(adminStore.stats.system.memory.used) }} / 
            {{ formatBytes(adminStore.stats.system.memory.total) }}
          </n-descriptions-item>
          <n-descriptions-item label="OTA 版本数">
            {{ adminStore.stats.ota.totalVersions }}
          </n-descriptions-item>
          <n-descriptions-item label="存储大小">
            {{ formatBytes(adminStore.stats.ota.totalSize) }}
          </n-descriptions-item>
        </n-descriptions>

        <n-skeleton v-else text :repeat="3" />
      </n-card>

      <!-- 最近版本 -->
      <n-card title="最近版本">
        <template #header-extra>
          <router-link to="/versions">
            <n-button text type="primary">
              查看全部
              <template #icon>
                <n-icon :component="ArrowForwardOutline" />
              </template>
            </n-button>
          </router-link>
        </template>

        <n-data-table
          v-if="recentVersions.length > 0"
          :columns="versionColumns"
          :data="recentVersions"
          :pagination="false"
          size="small"
        />

        <n-empty v-else description="暂无版本数据" />
      </n-card>

      <!-- 快速操作 -->
      <n-card title="快速操作">
        <n-space>
          <router-link to="/versions">
            <n-button type="primary">
              <template #icon>
                <n-icon :component="CloudUploadOutline" />
              </template>
              上传新版本
            </n-button>
          </router-link>

          <router-link to="/system">
            <n-button>
              <template #icon>
                <n-icon :component="SettingsOutline" />
              </template>
              系统设置
            </n-button>
          </router-link>

          <n-button @click="handleCleanup">
            <template #icon>
              <n-icon :component="TrashOutline" />
            </template>
            清理缓存
          </n-button>
        </n-space>
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, h } from 'vue'
import {
  NPageHeader,
  NSpace,
  NGrid,
  NGridItem,
  NCard,
  NStatistic,
  NTag,
  NDescriptions,
  NDescriptionsItem,
  NSkeleton,
  NDataTable,
  NEmpty,
  NButton,
  NIcon,
  useMessage,
  type DataTableColumns,
} from 'naive-ui'
import {
  RefreshOutline,
  TimeOutline,
  FolderOutline,
  HardwareChipOutline,
  DownloadOutline,
  ArrowForwardOutline,
  CloudUploadOutline,
  SettingsOutline,
  TrashOutline,
} from '@vicons/ionicons5'
import { useAdminStore } from '@/stores/admin'
import { adminApi } from '@/api/admin'
import type { VersionInfo } from '@/api/types'

const message = useMessage()
const adminStore = useAdminStore()

// 最近版本（取前5个）
const recentVersions = computed(() => {
  return adminStore.versions.slice(0, 5)
})

// 版本表格列定义
const versionColumns: DataTableColumns<VersionInfo> = [
  {
    title: '版本',
    key: 'version',
    width: 100,
  },
  {
    title: '平台',
    key: 'platform',
    width: 80,
  },
  {
    title: '架构',
    key: 'architecture',
    width: 80,
  },
  {
    title: '文件大小',
    key: 'fileSize',
    width: 100,
    render: (row) => formatBytes(row.fileSize),
  },
  {
    title: '上传时间',
    key: 'uploadTime',
    render: (row) => new Date(row.uploadTime).toLocaleString(),
  },
]

// 格式化字节数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 清理缓存
const handleCleanup = async () => {
  try {
    const response = await adminApi.cleanup()
    if (response.success) {
      message.success('缓存清理成功')
      adminStore.refreshData()
    } else {
      message.error('缓存清理失败')
    }
  } catch (error) {
    message.error('清理操作失败')
  }
}

onMounted(() => {
  // 页面加载时刷新数据
  adminStore.refreshData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
