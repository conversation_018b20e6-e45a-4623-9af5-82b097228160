# RealityTap 完整构建脚本（简化版）
param(
    [string]$Configuration = "release",
    [switch]$Clean = $false,
    [switch]$SkipInstaller = $false
)

$ErrorActionPreference = "Stop"

# 获取项目根目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

Write-Host "🚀 RealityTap 完整构建脚本" -ForegroundColor Green
Write-Host "📁 项目根目录: $ProjectRoot" -ForegroundColor Cyan
Write-Host "⚙️ 构建配置: $Configuration" -ForegroundColor Cyan

# 切换到项目根目录
Set-Location $ProjectRoot

try {
    # 1. 构建 installer（如果需要）
    if (-not $SkipInstaller) {
        Write-Host ""
        Write-Host "🔨 第一步: 构建外部安装器..." -ForegroundColor Yellow
        
        # 切换到 installer 目录并构建
        Set-Location "installer"
        
        if ($Configuration -eq "release") {
            cargo build --release
        } else {
            cargo build
        }
        
        if ($LASTEXITCODE -ne 0) {
            throw "安装器构建失败"
        }
        
        # 返回项目根目录
        Set-Location $ProjectRoot
        
        # 复制 installer 到打包目录
        $SourcePath = if ($Configuration -eq "release") {
            "installer/target/release/updater.exe"
        } else {
            "installer/target/debug/updater.exe"
        }
        
        $TargetDir = "src-tauri/installer"
        $TargetPath = "$TargetDir/updater.exe"
        
        # 确保目录存在
        if (-not (Test-Path $TargetDir)) {
            New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
        }
        
        Copy-Item $SourcePath $TargetPath -Force
        Write-Host "✅ 安装器构建完成并复制到: $TargetPath" -ForegroundColor Green
    } else {
        Write-Host "⏭️ 跳过安装器构建" -ForegroundColor Yellow
    }
    
    # 2. 验证安装器文件存在
    $InstallerPath = "src-tauri/installer/updater.exe"
    if (-not (Test-Path $InstallerPath)) {
        Write-Host "⚠️ 警告: 安装器文件不存在: $InstallerPath" -ForegroundColor Yellow
        Write-Host "   这可能导致打包失败。请确保安装器已正确构建。" -ForegroundColor Yellow
    } else {
        Write-Host "✅ 安装器文件已就绪: $InstallerPath" -ForegroundColor Green
    }
    
    # 3. 构建主应用程序
    Write-Host ""
    Write-Host "🔨 第二步: 构建主应用程序..." -ForegroundColor Yellow
    
    # 清理构建（如果需要）
    if ($Clean) {
        Write-Host "🧹 清理构建缓存..." -ForegroundColor Yellow
        if (Test-Path "dist") {
            Remove-Item -Recurse -Force "dist"
        }
        if (Test-Path "node_modules/.vite") {
            Remove-Item -Recurse -Force "node_modules/.vite"
        }
        
        Set-Location "src-tauri"
        cargo clean
        Set-Location $ProjectRoot
    }
    
    # 执行构建
    if ($Configuration -eq "release") {
        Write-Host "🔧 执行: npm run tauri build" -ForegroundColor Cyan
        npm run tauri build
    } else {
        Write-Host "🔧 执行: npm run tauri build --debug" -ForegroundColor Cyan
        npm run tauri build -- --debug
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "主应用程序构建失败"
    }
    
    Write-Host "✅ 主应用程序构建完成" -ForegroundColor Green
    
    # 4. 显示构建结果
    Write-Host ""
    Write-Host "🎉 构建完成！" -ForegroundColor Green
    
    if ($Configuration -eq "release") {
        $OutputDir = "src-tauri/target/release/bundle"
        if (Test-Path $OutputDir) {
            Write-Host "📦 构建产物位置:" -ForegroundColor Cyan
            Get-ChildItem $OutputDir -Recurse -File | Where-Object { $_.Extension -in @('.msi', '.exe', '.deb', '.dmg', '.app') } | ForEach-Object {
                Write-Host "   $($_.FullName)" -ForegroundColor White
            }
        }
    }
    
} catch {
    Write-Host "❌ 构建失败: $_" -ForegroundColor Red
    exit 1
} finally {
    # 确保返回到项目根目录
    Set-Location $ProjectRoot
}

Write-Host ""
Write-Host "💡 使用说明:" -ForegroundColor Yellow
Write-Host "   构建发布版本: .\scripts\build-full-simple.ps1 -Configuration release" -ForegroundColor White
Write-Host "   构建调试版本: .\scripts\build-full-simple.ps1 -Configuration debug" -ForegroundColor White
Write-Host "   清理构建:     .\scripts\build-full-simple.ps1 -Clean" -ForegroundColor White
Write-Host "   跳过安装器:   .\scripts\build-full-simple.ps1 -SkipInstaller" -ForegroundColor White
Write-Host ""
