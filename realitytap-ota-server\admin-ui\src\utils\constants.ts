// API 基础配置
export const API_BASE_URL = '/api/v1'

// 本地存储键名
export const STORAGE_KEYS = {
  ADMIN_TOKEN: 'admin_token',
  ADMIN_USER: 'admin_user',
  THEME: 'theme',
} as const

// 文件类型配置
export const FILE_TYPES = {
  ALLOWED_EXTENSIONS: ['.exe', '.dmg', '.deb', '.rpm', '.tar.gz', '.zip'],
  MAX_FILE_SIZE: 500 * 1024 * 1024, // 500MB
} as const

// 平台配置
export const PLATFORMS = {
  WINDOWS: 'windows',
  MACOS: 'macos',
  LINUX: 'linux',
} as const

// 架构配置
export const ARCHITECTURES = {
  X86_64: 'x86_64',
  AARCH64: 'aarch64',
  X86: 'x86',
} as const

// 渠道配置
export const CHANNELS = {
  STABLE: 'stable',
  BETA: 'beta',
  ALPHA: 'alpha',
} as const

// 主题配置
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
} as const

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZES: [10, 20, 50, 100],
} as const

// 时间格式
export const DATE_FORMATS = {
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
} as const

// 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '权限不足',
  NOT_FOUND: '请求的资源不存在',
  SERVER_ERROR: '服务器内部错误',
  UPLOAD_FAILED: '文件上传失败',
  DELETE_FAILED: '删除失败',
  INVALID_FILE_TYPE: '不支持的文件类型',
  FILE_TOO_LARGE: '文件大小超出限制',
} as const
