#!/bin/bash

# RealityTap OTA Server Docker Upgrade Script
# This script helps upgrade the OTA server with zero downtime

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_DIR="$PROJECT_ROOT/docker"

# Default values
MODE="http"
PROFILE=""
ENV_FILE=""
BACKUP=true
ROLLBACK=false
VERSION=""

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
RealityTap OTA Server Docker Upgrade Script

Usage: $0 [OPTIONS]

Options:
    -m, --mode MODE         Deployment mode: http or https (default: http)
    -p, --profile PROFILE   Docker Compose profile to use (e.g., nginx)
    -e, --env-file FILE     Environment file to use (default: .env)
    -v, --version VERSION   Specific version to upgrade to (default: latest)
    --no-backup             Skip database backup before upgrade
    --rollback              Rollback to previous version
    -h, --help              Show this help message

Examples:
    $0                                  # Upgrade to latest version with HTTP mode
    $0 -m https                         # Upgrade with HTTPS mode
    $0 -v 1.2.3                        # Upgrade to specific version
    $0 --rollback                       # Rollback to previous version
    $0 --no-backup                      # Upgrade without backup

Upgrade Process:
    1. Create database backup (unless --no-backup)
    2. Pull new Docker images
    3. Stop current services gracefully
    4. Start new services
    5. Verify health status
    6. Clean up old images (default: yes)

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                MODE="$2"
                shift 2
                ;;
            -p|--profile)
                PROFILE="$2"
                shift 2
                ;;
            -e|--env-file)
                ENV_FILE="$2"
                shift 2
                ;;
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            --no-backup)
                BACKUP=false
                shift
                ;;
            --rollback)
                ROLLBACK=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Validate arguments
validate_args() {
    if [[ "$MODE" != "http" && "$MODE" != "https" ]]; then
        log_error "Invalid mode: $MODE. Must be 'http' or 'https'"
        exit 1
    fi
    
    if [[ -n "$ENV_FILE" && ! -f "$ENV_FILE" ]]; then
        log_error "Environment file not found: $ENV_FILE"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_info "Prerequisites check passed"
}

# Setup environment
setup_environment() {
    log_info "Setting up environment..."
    
    cd "$DOCKER_DIR"
    
    # Use default .env file if not specified
    if [[ -z "$ENV_FILE" ]]; then
        ENV_FILE=".env"
    fi
    
    # Check if environment file exists
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "Environment file $ENV_FILE not found"
        exit 1
    fi
    
    # Source environment file
    set -a
    source "$ENV_FILE"
    set +a
    
    log_info "Environment setup completed"
}

# Create backup
create_backup() {
    if [[ "$BACKUP" == false ]]; then
        log_info "Skipping backup as requested"
        return
    fi
    
    log_info "Creating database backup..."
    
    # Determine compose file
    COMPOSE_FILE="docker-compose.$MODE.yml"
    COMPOSE_CMD="docker-compose -f $COMPOSE_FILE"
    
    if [[ -n "$ENV_FILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --env-file $ENV_FILE"
    fi
    
    if [[ -n "$PROFILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --profile $PROFILE"
    fi
    
    # Create backup directory with timestamp
    BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database if container is running
    if $COMPOSE_CMD ps realitytap-ota-server | grep -q "Up"; then
        log_info "Backing up database..."
        $COMPOSE_CMD exec -T realitytap-ota-server sh -c "
            if [ -f /app/storage/database/ota.db ]; then
                cp /app/storage/database/ota.db /app/storage/backup/ota_backup_\$(date +%Y%m%d_%H%M%S).db
                echo 'Database backup created'
            else
                echo 'No database file found to backup'
            fi
        "
        
        # Copy backup to host
        docker cp realitytap-ota-server:/app/storage/backup "$BACKUP_DIR/"
        log_info "Backup created in: $BACKUP_DIR"
    else
        log_warn "Container is not running, skipping database backup"
    fi
}

# Upgrade application
upgrade_application() {
    log_info "Starting upgrade process..."
    
    # Determine compose file
    COMPOSE_FILE="docker-compose.$MODE.yml"
    COMPOSE_CMD="docker-compose -f $COMPOSE_FILE"
    
    if [[ -n "$ENV_FILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --env-file $ENV_FILE"
    fi
    
    if [[ -n "$PROFILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --profile $PROFILE"
    fi
    
    # Set version if specified
    if [[ -n "$VERSION" ]]; then
        export APP_VERSION="$VERSION"
        log_info "Upgrading to version: $VERSION"
    else
        log_info "Upgrading to latest version"
    fi
    
    # Pull new images
    log_info "Pulling new Docker images..."
    $COMPOSE_CMD pull
    
    # Rebuild images
    log_info "Building updated images..."
    $COMPOSE_CMD build --no-cache
    
    # Gracefully stop current services
    log_info "Stopping current services..."
    $COMPOSE_CMD down --timeout 30
    
    # Start new services
    log_info "Starting updated services..."
    $COMPOSE_CMD up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 10
    
    # Check health status
    check_health
    
    log_info "Upgrade completed successfully!"
}

# Rollback application
rollback_application() {
    log_info "Starting rollback process..."
    
    # Determine compose file
    COMPOSE_FILE="docker-compose.$MODE.yml"
    COMPOSE_CMD="docker-compose -f $COMPOSE_FILE"
    
    if [[ -n "$ENV_FILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --env-file $ENV_FILE"
    fi
    
    if [[ -n "$PROFILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --profile $PROFILE"
    fi
    
    # Find previous backup
    LATEST_BACKUP=$(find ./backups -type d -name "20*" | sort -r | head -n 1)
    
    if [[ -z "$LATEST_BACKUP" ]]; then
        log_error "No backup found for rollback"
        exit 1
    fi
    
    log_info "Rolling back using backup: $LATEST_BACKUP"
    
    # Stop current services
    log_info "Stopping current services..."
    $COMPOSE_CMD down --timeout 30
    
    # Restore database backup
    if [[ -d "$LATEST_BACKUP/backup" ]]; then
        log_info "Restoring database backup..."
        # This would require more complex logic to restore the database
        log_warn "Database restore not implemented in this script"
        log_warn "Please manually restore database from: $LATEST_BACKUP/backup"
    fi
    
    # Start services with previous configuration
    log_info "Starting services..."
    $COMPOSE_CMD up -d
    
    # Check health status
    check_health
    
    log_info "Rollback completed!"
}

# Check health status
check_health() {
    log_info "Checking service health..."
    
    # Determine compose file
    COMPOSE_FILE="docker-compose.$MODE.yml"
    COMPOSE_CMD="docker-compose -f $COMPOSE_FILE"
    
    if [[ -n "$ENV_FILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --env-file $ENV_FILE"
    fi
    
    if [[ -n "$PROFILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --profile $PROFILE"
    fi
    
    # Wait for health check to pass
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if $COMPOSE_CMD ps realitytap-ota-server | grep -q "healthy"; then
            log_info "Service is healthy!"
            break
        elif [[ $attempt -eq $max_attempts ]]; then
            log_error "Service failed to become healthy after $max_attempts attempts"
            log_error "Check logs: $COMPOSE_CMD logs realitytap-ota-server"
            exit 1
        else
            log_info "Waiting for service to become healthy... (attempt $attempt/$max_attempts)"
            sleep 10
            ((attempt++))
        fi
    done
    
    # Show service status
    log_info "Service Status:"
    $COMPOSE_CMD ps
}

# Clean up old images
cleanup_images() {
    log_info "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    log_info "Cleanup completed"
}

# Main function
main() {
    log_info "RealityTap OTA Server Docker Upgrade"
    log_info "===================================="
    
    parse_args "$@"
    validate_args
    check_prerequisites
    setup_environment
    
    if [[ "$ROLLBACK" == true ]]; then
        rollback_application
    else
        create_backup
        upgrade_application
        
        # Ask user if they want to clean up old images (default: yes)
        read -p "Do you want to clean up old Docker images? (Y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Nn]$ ]]; then
            log_info "Skipping cleanup of old Docker images"
        else
            cleanup_images
        fi
    fi
    
    log_info "Operation completed successfully!"
}

# Run main function
main "$@"
