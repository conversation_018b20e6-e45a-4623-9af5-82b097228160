import { getVersionService } from '@/services/service-factory';
import { ErrorResponse, SuccessResponse, ChannelConfig, ServerVersionInfo } from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import { getRequestIPInfo, logOperationIP } from '@/middleware/ip-logging.middleware';
import { NextFunction, Request, Response, Router } from 'express';

const router: Router = Router();

// 获取版本服务实例
const versionService = getVersionService();

/**
 * 获取所有可用版本信息
 * GET /api/v1/version/available
 */
router.get('/available', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const versions = await versionService.getAvailableVersions();

    const response: SuccessResponse = {
      success: true,
      data: versions,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error) {
    logger.error('Failed to get available versions', { error });
    next(error);
  }
});

/**
 * 获取渠道信息
 * GET /api/v1/version/channels
 */
router.get('/channels', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const channels = await versionService.getChannelInfo();

    const response: SuccessResponse = {
      success: true,
      data: channels,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error) {
    logger.error('Failed to get channel info', { error });
    next(error);
  }
});

/**
 * 获取特定渠道的最新版本
 * GET /api/v1/version/latest/:channel
 */
router.get('/latest/:channel', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { channel } = req.params;

    // 获取客户端IP信息
    const ipInfo = getRequestIPInfo(req);

    // 记录版本查询操作
    logOperationIP(req, 'VERSION_CHECK', {
      channel,
      userAgent: req.get('User-Agent'),
    });

    // 验证渠道参数
    if (!channel || !['stable', 'beta', 'alpha'].includes(channel)) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_CHANNEL',
          message: 'Invalid channel specified',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }

    const versions = await versionService.getAvailableVersions();
    const channelInfo = versions.channels[channel as keyof typeof versions.channels];

    if (!channelInfo) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'CHANNEL_NOT_FOUND',
          message: 'Channel not found',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(404).json(response);
      return;
    }

    const response: SuccessResponse = {
      success: true,
      data: {
        channel,
        version: channelInfo.version,
        platforms: channelInfo.platforms,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error) {
    logger.error('获取最新版本失败', {
      error: {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        code: (error as any)?.code,
        stack: error instanceof Error ? error.stack : undefined,
        type: typeof error,
      },
      versionQuery: {
        channel: req.params.channel,
        validChannels: ['stable', 'beta', 'alpha'],
      },
      request: {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.url,
        params: req.params,
      },
      timing: {
        timestamp: new Date().toISOString(),
      },
      module: 'ota_function',
      operation: 'get_latest_version_failed',
    });
    next(error);
  }
});

/**
 * 清除版本缓存（管理员功能）
 * POST /api/v1/version/cache/clear
 */
router.post('/cache/clear', async (req: Request, res: Response, next: NextFunction) => {
  try {
    // 注意：在生产环境中，这个端点应该有适当的认证和授权
    versionService.clearCache();

    const response: SuccessResponse = {
      success: true,
      data: {
        message: 'Cache cleared successfully',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    const ipInfo = getRequestIPInfo(req);
    logOperationIP(req, 'CACHE_CLEAR', {
      userAgent: req.get('User-Agent'),
    });

    logger.info('Version cache cleared', {
      userAgent: req.get('User-Agent'),
      clientIP: ipInfo.ip,
    });

    res.json(response);
  } catch (error) {
    logger.error('清除版本缓存失败', {
      error: {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        code: (error as any)?.code,
        stack: error instanceof Error ? error.stack : undefined,
        type: typeof error,
      },
      cacheOperation: {
        operation: 'clear_cache',
        requestedAt: new Date().toISOString(),
      },
      request: {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.url,
      },
      timing: {
        timestamp: new Date().toISOString(),
      },
      module: 'ota_function',
      operation: 'clear_cache_failed',
    });
    next(error);
  }
});

/**
 * 获取版本统计信息
 * GET /api/v1/version/stats
 */
router.get('/stats', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const versions = await versionService.getAvailableVersions();
    const channels = await versionService.getChannelInfo();

    // 计算统计信息
    const stats = {
      totalChannels: Object.keys(channels).length,
      enabledChannels: Object.values(channels).filter((c: any) => c && c.enabled).length,
      totalVersions: Object.keys(versions.channels).length,
      platforms: new Set<string>(),
      architectures: new Set<string>(),
    };

    // 收集平台和架构信息
    Object.values(versions.channels).forEach((channel): void => {
      const serverVersionInfo = channel as ServerVersionInfo;
      if (serverVersionInfo && serverVersionInfo.platforms) {
        Object.keys(serverVersionInfo.platforms).forEach(platform => {
          stats.platforms.add(platform);
          const platformData = serverVersionInfo.platforms[platform];
          if (platformData) {
            Object.keys(platformData).forEach(arch => {
              stats.architectures.add(arch);
            });
          }
        });
      }
    });

    const response: SuccessResponse = {
      success: true,
      data: {
        ...stats,
        platforms: Array.from(stats.platforms),
        architectures: Array.from(stats.architectures),
        deprecatedVersions: versions.deprecatedVersions,
        minimumVersions: versions.minimumVersions,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error) {
    logger.error('获取版本统计信息失败', {
      error: {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        code: (error as any)?.code,
        stack: error instanceof Error ? error.stack : undefined,
        type: typeof error,
      },
      statsOperation: {
        operation: 'get_version_stats',
        requestedAt: new Date().toISOString(),
      },
      request: {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.url,
      },
      systemInfo: {
        versionServiceAvailable: !!versionService,
        timestamp: new Date().toISOString(),
      },
      timing: {
        timestamp: new Date().toISOString(),
      },
      module: 'ota_function',
      operation: 'get_stats_failed',
    });
    next(error);
  }
});

/**
 * 兼容性API：旧版本检查端点
 * POST /api/v1/version/check
 *
 * 为了兼容旧版本的桌面程序，提供这个端点
 * 将请求转换为新的Tauri格式
 */
router.post('/check', async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('🔍 收到兼容性API请求:', {
      body: req.body,
      headers: req.headers,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    });

    const { currentVersion, platform, architecture, channel = 'stable' } = req.body;

    // 获取客户端IP信息
    const ipInfo = getRequestIPInfo(req);

    // 记录兼容性API使用
    logger.info('使用兼容性版本检查API', {
      currentVersion,
      platform,
      architecture,
      channel,
      userAgent: req.get('User-Agent'),
      clientIP: ipInfo.ip,
      module: 'ota_function',
      operation: 'legacy_version_check',
    });

    // 执行版本检查
    const updateInfo = await versionService.checkForUpdates({
      currentVersion,
      platform: platform as 'windows' | 'macos' | 'linux',
      architecture: architecture as 'x86_64' | 'aarch64' | 'x86',
      channel: channel as 'stable' | 'beta' | 'alpha',
    });

    // 构建兼容的响应格式
    const response = {
      version: updateInfo.latestVersion || currentVersion,
      date: updateInfo.releaseDate || new Date().toISOString(),
      body: updateInfo.releaseNotes || '',
      force_update: updateInfo.isForced || false,
      file_size: updateInfo.fileSize || 0, // 添加文件大小字段
    };

    console.log('📤 兼容性API响应:', {
      updateInfo,
      response,
      timestamp: new Date().toISOString()
    });

    logger.info('兼容性API响应', {
      currentVersion,
      latestVersion: response.version,
      hasUpdate: updateInfo.hasUpdate,
      force_update: response.force_update,
      clientIP: ipInfo.ip,
      module: 'ota_function',
      operation: 'legacy_version_check_response',
    });

    res.json(response);
  } catch (error) {
    logger.error('兼容性版本检查失败', {
      error,
      body: req.body,
      userAgent: req.get('User-Agent'),
      clientIP: getRequestIPInfo(req).ip,
    });
    next(error);
  }
});

export { router as versionController };
