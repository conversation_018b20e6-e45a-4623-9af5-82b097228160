<template>
  <div class="chunk-file-upload">
    <!-- 步骤导航 -->
    <StepNavigator :current-step="currentStep" :step-status="stepStatus" />

    <!-- 主要内容区域 -->
    <div class="upload-content">
      <!-- 步骤 1: 文件选择 -->
      <div v-show="currentStep === 1" class="step-content">
        <n-card title="选择文件" size="small">
          <FileSelector
            v-model:installer-file="fileState.installerFile.value"
            v-model:signature-file="fileState.signatureFile.value"
            v-model:hash-file="fileState.hashFile.value"
            v-model:signature-value="fileState.signatureValue.value"
            v-model:hash-value="fileState.hashValue.value"
          />
        </n-card>
      </div>

      <!-- 步骤 2: 文件信息确认 -->
      <div v-show="currentStep === 2" class="step-content">
        <n-card title="文件信息确认" size="small">
          <FileSummary
            :installer-file="fileState.installerFile.value"
            :signature-file="fileState.signatureFile.value"
            :hash-file="fileState.hashFile.value"
            :signature-value="fileState.signatureValue.value"
            :hash-value="fileState.hashValue.value"
          />
        </n-card>
      </div>

      <!-- 步骤 3: 上传进度 -->
      <div v-show="currentStep === 3" class="step-content">
        <n-card title="上传进度" size="small">
          <ProgressDisplay
            :progress="uploadState.uploadProgress.value"
            :is-uploading="uploadState.isUploading.value"
            @cancel="uploadState.cancelUpload"
          />

          <!-- 上传结果 -->
          <div v-if="uploadState.uploadResult.value" class="upload-result">
            <n-alert
              :type="uploadState.uploadResult.value.success ? 'success' : 'error'"
              :title="uploadState.uploadResult.value.success ? '上传成功' : '上传失败'"
              :show-icon="true"
              style="margin-top: 16px"
            >
              {{ uploadState.uploadResult.value.message }}
            </n-alert>
          </div>
        </n-card>
      </div>

      <!-- 步骤 4: 元数据编辑 -->
      <div v-show="currentStep === 4" class="step-content">
        <MetadataForm v-model="metadataForm" @save="saveMetadata" @cancel="cancelMetadataEdit" />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="currentStep < 4" class="upload-actions">
      <n-space justify="space-between">
        <div>
          <n-button v-if="currentStep > 1" @click="goToPreviousStep">
            上一步
          </n-button>
        </div>

        <n-space>
          <n-button @click="handleCancel">取消</n-button>

          <!-- 步骤 1: 下一步按钮 -->
          <n-button
            v-if="currentStep === 1 && fileState.canStartUpload.value"
            type="primary"
            @click="goToNextStep"
          >
            下一步
          </n-button>

          <!-- 步骤 2: 开始上传按钮 -->
          <n-button
            v-if="currentStep === 2"
            type="primary"
            :loading="uploadState.isUploading.value"
            @click="startUpload"
          >
            开始批量上传
          </n-button>

          <!-- 步骤 3: 下一步按钮（上传成功后） -->
          <n-button
            v-if="currentStep === 3 && uploadState.uploadResult.value?.success"
            type="primary"
            @click="goToNextStep"
          >
            下一步
          </n-button>
        </n-space>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { adminApi } from '@/api/admin';
import FileSelector from './FileUpload/FileSelector.vue';
import FileSummary from './FileUpload/FileSummary.vue';
import MetadataForm from './FileUpload/MetadataForm.vue';
import ProgressDisplay from './FileUpload/ProgressDisplay.vue';
import StepNavigator from './FileUpload/StepNavigator.vue';
import { useFileState } from '@/composables/useFileState';
import { useFileUpload } from '@/composables/useFileUpload';
import { type FileUploadRequest } from '@/types/upload';

import { NAlert, NButton, NCard, NSpace, useMessage } from 'naive-ui';
import { ref, computed } from 'vue';

const emit = defineEmits<{
  success: [];
  cancel: [];
}>();

// 使用 inject 来获取 message 实例，避免在组件外部调用
const message = useMessage();

// 使用 composables
const fileState = useFileState();
const uploadState = useFileUpload();

// 步骤管理
const currentStep = ref(1);

// 步骤状态计算
const stepStatus = computed(() => {
  if (uploadState.uploadResult.value?.success === false) {
    return 'error';
  }
  if (currentStep.value === 4) {
    return 'finish';
  }
  return 'process';
});

// 元数据表单相关
const showMetadataForm = ref(false);
const metadataForm = ref<FileUploadRequest & { filename: string }>({
  filename: '',
  version: '',
  platform: 'windows',
  architecture: 'x86_64',
  channel: 'stable',
  isForced: false,
  releaseNotes: '',
});

// 步骤导航方法
const goToNextStep = () => {
  if (currentStep.value < 4) {
    currentStep.value++;
  }
};

const goToPreviousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

// 开始上传
const startUpload = async () => {
  // 切换到上传进度步骤
  currentStep.value = 3;

  const success = await uploadState.startBatchUpload(
    fileState.selectedFiles.value,
    fileState.hashValue.value,
    fileState.signatureValue.value,
  );

  if (success) {
    showMetadataFormAfterUpload();
  }
};

// 显示元数据表单
const showMetadataFormAfterUpload = () => {
  const referenceFile = fileState.selectedFiles.value[0];
  if (!referenceFile) {
    console.warn('未找到参考文件');
    return;
  }

  const parsedMetadata = uploadState.parseFileMetadata(referenceFile.name);
  metadataForm.value = {
    filename: referenceFile.name,
    version: parsedMetadata.version,
    platform: parsedMetadata.platform,
    architecture: parsedMetadata.architecture,
    channel: parsedMetadata.channel || 'stable',
    isForced: parsedMetadata.isForced || false,
    releaseNotes: parsedMetadata.releaseNotes || `Release ${parsedMetadata.version}`,
  };

  // 不再使用 showMetadataForm，而是切换到步骤 4
  // showMetadataForm.value = true;
};

// 保存元数据
const saveMetadata = async (data: FileUploadRequest & { filename: string }) => {
  try {
    message.loading('正在保存元数据...', { duration: 0 });

    const updateData = {
      version: data.version,
      platform: data.platform,
      architecture: data.architecture,
      channel: data.channel || 'stable',
      isForced: data.isForced || false,
      releaseNotes: data.releaseNotes || '',
    };

    await adminApi.updateVersionMetadata(data.filename, updateData);
    message.destroyAll();
    message.success('元数据保存成功');

    // 完成所有步骤
    emit('success');
  } catch (apiError: any) {
    message.destroyAll();
    message.error(`保存元数据失败: ${apiError.response?.data?.error?.message || apiError.message || '未知错误'}`);
  }
};

// 取消元数据编辑
const cancelMetadataEdit = async () => {
  try {
    if (uploadState.uploadResult.value?.success) {
      message.loading('正在删除已上传的文件...', { duration: 0 });

      try {
        const filesToDelete = fileState.selectedFiles.value.map(f => f.name);

        if (filesToDelete.length > 0) {
          const deletePromises = filesToDelete.map(async (filename: string) => {
            try {
              await adminApi.deleteUploadedFile(filename);
              return { success: true, filename };
            } catch (error: any) {
              console.error(`删除文件 ${filename} 失败:`, error);
              return { success: false, filename, error: error.message };
            }
          });

          const deleteResults = await Promise.all(deletePromises);
          const successCount = deleteResults.filter(r => r.success).length;

          message.destroyAll();
          if (successCount === filesToDelete.length) {
            message.success(`已删除所有上传的文件 (${successCount} 个)`);
          } else {
            message.warning(`已删除 ${successCount} 个文件，${filesToDelete.length - successCount} 个文件删除失败`);
          }
        }
      } catch (deleteError: any) {
        message.destroyAll();
        console.error('删除文件过程中发生错误:', deleteError);
        message.warning(`删除文件失败: ${deleteError.message || '未知错误'}，但已取消元数据编辑`);
      }
    }

    // 重置所有状态
    currentStep.value = 1;
    showMetadataForm.value = false;
    fileState.clearAllFiles();
    uploadState.resetUploadState();
    metadataForm.value = {
      filename: '',
      version: '',
      platform: 'windows',
      architecture: 'x86_64',
      channel: 'stable',
      isForced: false,
      releaseNotes: '',
    };

    emit('cancel');
  } catch (error: any) {
    console.error('取消操作失败:', error);
    message.error('取消操作失败: ' + error.message);
  }
};

// 处理取消
const handleCancel = () => {
  // 重置到第一步
  currentStep.value = 1;
  fileState.clearAllFiles();
  uploadState.resetUploadState();
  emit('cancel');
};
</script>

<style scoped>
.chunk-file-upload {
  max-width: 900px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.upload-content {
  flex: 1;
  min-height: 500px;
  max-height: 600px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.step-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.step-content :deep(.n-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.step-content :deep(.n-card__content) {
  flex: 1;
  overflow-y: auto;
}

.upload-actions {
  padding: 16px 0;
  border-top: 1px solid #e0e0e6;
  background: #fafafa;
  margin-top: auto;
}

.upload-result {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-height: 800px) {
  .upload-content {
    min-height: 400px;
    max-height: 450px;
  }
}

@media (max-height: 600px) {
  .upload-content {
    min-height: 300px;
    max-height: 350px;
  }
}
</style>
