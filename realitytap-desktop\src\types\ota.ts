/**
 * OTA 更新相关类型定义
 */

export interface UpdateCheckRequest {
  currentVersion: string;
  platform: string;
  architecture: string;
  channel: 'stable' | 'beta' | 'alpha';
  locale?: string;
}

export interface UpdateCheckResponse {
  hasUpdate: boolean;
  latestVersion?: string;
  downloadUrl?: string;
  releaseNotes?: string;
  fileSize?: number;
  checksum?: string;
  isForced?: boolean;
  minimumVersion?: string;
}

export interface DownloadProgress {
  sessionId: string;
  progress: number;
  downloadedBytes: number;
  totalBytes: number;
  speed: number;
  estimatedTimeRemaining: number;
  status: 'downloading' | 'paused' | 'completed' | 'failed';
}

export interface UpdateSession {
  sessionId: string;
  currentVersion: string;
  targetVersion: string;
  status: 'downloading' | 'paused' | 'completed' | 'failed' | 'verifying' | 'installing' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  error?: UpdateError;
  download?: DownloadSession;
}

export interface DownloadSession {
  url: string;
  filePath: string;
  totalSize: number;
  downloadedSize: number;
  status: 'downloading' | 'paused' | 'completed' | 'failed';
  speed: number;
  estimatedTimeRemaining: number;
  startTime: Date;
  pauseTime?: Date;
  resumeTime?: Date;
}

export interface UpdateError {
  code: string;
  message: string;
  timestamp: Date;
  retryable: boolean;
}

export interface SystemInfo {
  platform: string;
  architecture: string;
  osVersion: string;
  appVersion: string;
}

export type UpdateStatus = 
  | 'checking' 
  | 'available' 
  | 'not_available' 
  | 'downloading' 
  | 'downloaded' 
  | 'verifying' 
  | 'installing' 
  | 'installed' 
  | 'failed';

export type DownloadStatus = 
  | 'idle' 
  | 'downloading' 
  | 'paused' 
  | 'completed' 
  | 'failed';
