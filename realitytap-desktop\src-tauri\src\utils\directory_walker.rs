// Directory walking utilities for project generation
use crate::{error::Result, models};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::path::Path;
use uuid::Uuid;

/// Recursively walks through directories to generate group structures
pub fn walk_groups(
    dir: &Path,
    rel_path: &str,
    parent_uuid: Option<Uuid>,
    groups: &mut Vec<models::Group>,
    group_path_map: &mut HashMap<String, Uuid>,
    group_parent_map: &mut HashMap<String, Option<Uuid>>,
) {
    walk_groups_with_existing(dir, rel_path, parent_uuid, groups, group_path_map, group_parent_map, None);
}

/// Enhanced walk_groups function that can preserve existing group UUIDs
pub fn walk_groups_with_existing(
    dir: &Path,
    rel_path: &str,
    parent_uuid: Option<Uuid>,
    groups: &mut Vec<models::Group>,
    group_path_map: &mut HashMap<String, Uuid>,
    group_parent_map: &mut HashMap<String, Option<Uuid>>,
    existing_groups_map: Option<&HashMap<String, &models::Group>>,
) {
    // Try to find existing group UUID by path, otherwise generate new one
    let group_uuid = if let Some(existing_map) = existing_groups_map {
        if let Some(existing_group) = existing_map.get(rel_path) {
            existing_group.group_uuid
        } else {
            Uuid::new_v4()
        }
    } else {
        Uuid::new_v4()
    };
    let name = dir
        .file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("")
        .to_string();
    let path_str = rel_path.to_string();
    
    groups.push(models::Group {
        group_uuid,
        name,
        path: path_str.clone(),
        description: String::new(),
        parent_group_uuid: parent_uuid,
    });
    
    group_path_map.insert(path_str.clone(), group_uuid);
    group_parent_map.insert(path_str.clone(), parent_uuid);
    
    // Recursively process subdirectories
    if let Ok(entries) = std::fs::read_dir(dir) {
        for entry in entries.flatten() {
            let path = entry.path();
            if path.is_dir() {
                let sub_rel_path = if rel_path.is_empty() {
                    entry.file_name().to_string_lossy().to_string()
                } else {
                    format!("{}/{}", rel_path, entry.file_name().to_string_lossy())
                };
                walk_groups_with_existing(
                    &path,
                    &sub_rel_path,
                    Some(group_uuid),
                    groups,
                    group_path_map,
                    group_parent_map,
                    existing_groups_map,
                );
            }
        }
    }
}

/// Recursively walks through directories to generate file structures
pub fn walk_files(
    dir: &Path,
    rel_path: &str,
    group_path_map: &HashMap<String, Uuid>,
    files: &mut Vec<models::HapticFile>,
    audio_dir: &Path,
    audio_formats: &[&str],
    now: DateTime<Utc>,
    existing_files_map: Option<&HashMap<String, &models::HapticFile>>,
) -> Result<()> {
    if let Ok(entries) = std::fs::read_dir(dir) {
        for entry in entries.flatten() {
            let path = entry.path();
            if path.is_dir() {
                let sub_rel_path = if rel_path.is_empty() {
                    entry.file_name().to_string_lossy().to_string()
                } else {
                    format!("{}/{}", rel_path, entry.file_name().to_string_lossy())
                };
                walk_files(
                    &path,
                    &sub_rel_path,
                    group_path_map,
                    files,
                    audio_dir,
                    audio_formats,
                    now,
                    existing_files_map,
                )?;
            } else if let Some(ext) = path.extension().and_then(|e| e.to_str()) {
                if ext.eq_ignore_ascii_case("he") {
                    let file_name = path
                        .file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("")
                        .to_string();
                    let file_rel_path = if rel_path.is_empty() {
                        format!("haptics/{}", file_name)
                    } else {
                        format!("haptics/{}/{}", rel_path, file_name)
                    };
                    
                    // Determine group UUID
                    let group_uuid = if rel_path.is_empty() {
                        None
                    } else {
                        group_path_map.get(rel_path).copied()
                    };
                    
                    // Auto-associate audio
                    let mut associated_audio = None;
                    let base_name = path.file_stem().and_then(|n| n.to_str()).unwrap_or("");
                    for fmt in audio_formats {
                        let audio_file = audio_dir.join(format!("{}.{}", base_name, fmt));
                        if audio_file.exists() {
                            associated_audio = Some(format!("{}.{}", base_name, fmt));
                            break;
                        }
                    }
                    
                    // Check if file exists in existing files map (for refresh operations)
                    if let Some(existing_map) = existing_files_map {
                        if let Some(existing_file) = existing_map.get(&file_rel_path) {
                            // Check if file metadata has changed
                            let metadata_changed = existing_file.group != group_uuid
                                || existing_file.associated_audio != associated_audio;

                            // Preserve existing file metadata, only update last_modified_time if something changed
                            files.push(models::HapticFile {
                                file_uuid: existing_file.file_uuid,
                                name: file_name,
                                path: file_rel_path,
                                group: group_uuid,
                                format_version: existing_file.format_version.clone(),
                                create_time: existing_file.create_time,
                                last_modified_time: if metadata_changed { now } else { existing_file.last_modified_time },
                                description: existing_file.description.clone(),
                                version: existing_file.version.clone(),
                                associated_audio,
                                associated_video: existing_file.associated_video.clone(),
                                tags: existing_file.tags.clone(),
                                file_type: existing_file.file_type.clone(),
                                audio_info: existing_file.audio_info.clone(),
                                zoom_level: existing_file.zoom_level,
                            });
                        } else {
                            // New file
                            create_new_file_entry(file_name, file_rel_path, group_uuid, associated_audio, now, files);
                        }
                    } else {
                        // New file (for generate operations)
                        create_new_file_entry(file_name, file_rel_path, group_uuid, associated_audio, now, files);
                    }
                }
            }
        }
    }
    Ok(())
}

fn create_new_file_entry(
    file_name: String,
    file_rel_path: String,
    group_uuid: Option<Uuid>,
    associated_audio: Option<String>,
    now: DateTime<Utc>,
    files: &mut Vec<models::HapticFile>,
) {
    files.push(models::HapticFile {
        file_uuid: Uuid::new_v4(),
        name: file_name,
        path: file_rel_path,
        group: group_uuid,
        format_version: models::HeFormatVersion::V2,
        create_time: now,
        last_modified_time: now,
        description: String::new(),
        version: "1.0.0".to_string(),
        associated_audio,
        associated_video: None,
        tags: vec![],
        file_type: "he".to_string(),
        audio_info: None,
        zoom_level: None,
    });
}
