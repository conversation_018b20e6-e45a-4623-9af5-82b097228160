/**
 * 国际化配置入口文件
 * Internationalization Configuration Entry
 */

import { createI18n } from "vue-i18n";
import zhCN from "./zh-CN";
import enUS from "./en-US";
import jaJP from "./ja-JP";
import koKR from "./ko-KR";

// 支持的语言列表
export const SUPPORTED_LOCALES = {
  "zh-CN": "简体中文",
  "en-US": "English",
  "ja-JP": "日本語",
  "ko-KR": "한국어",
} as const;

export type SupportedLocale = keyof typeof SUPPORTED_LOCALES;

// 语言资源
const messages = {
  "zh-CN": zhCN,
  "en-US": enUS,
  "ja-JP": jaJP,
  "ko-KR": koKR,
};

// 默认语言
export const DEFAULT_LOCALE: SupportedLocale = "en-US";

// 语言检测函数
export function detectBrowserLanguage(): SupportedLocale {
  // 获取浏览器语言
  const browserLang = navigator.language || navigator.languages?.[0] || DEFAULT_LOCALE;

  // 精确匹配
  if (browserLang in SUPPORTED_LOCALES) {
    return browserLang as SupportedLocale;
  }

  // 语言代码匹配（例如：zh 匹配 zh-CN）
  const langCode = browserLang.split("-")[0];
  const matchedLocale = Object.keys(SUPPORTED_LOCALES).find((locale) => locale.startsWith(langCode)) as SupportedLocale;

  return matchedLocale || DEFAULT_LOCALE;
}

// 从存储中获取语言偏好
export function getStoredLanguage(): SupportedLocale | null {
  try {
    const stored = localStorage.getItem("realitytap-language");
    if (stored && stored in SUPPORTED_LOCALES) {
      return stored as SupportedLocale;
    }
  } catch (error) {
    console.warn("Failed to read language preference from localStorage:", error);
  }
  return null;
}

// 保存语言偏好到存储
export function setStoredLanguage(locale: SupportedLocale): void {
  try {
    localStorage.setItem("realitytap-language", locale);
  } catch (error) {
    console.warn("Failed to save language preference to localStorage:", error);
  }
}

// 智能语言选择
export function getInitialLanguage(): SupportedLocale {
  // 1. 优先使用用户手动设置的语言
  const storedLang = getStoredLanguage();
  if (storedLang) {
    return storedLang;
  }

  // 2. 自动检测浏览器语言
  const detectedLang = detectBrowserLanguage();

  // 3. 保存检测到的语言作为用户偏好
  setStoredLanguage(detectedLang);

  return detectedLang;
}

// 创建 i18n 实例
export const i18n = createI18n({
  legacy: false, // 使用 Composition API 模式
  locale: getInitialLanguage(),
  fallbackLocale: DEFAULT_LOCALE,
  messages,
  globalInjection: true, // 全局注入 $t 函数
  silentTranslationWarn: true, // 在生产环境中静默翻译警告
  silentFallbackWarn: true, // 在生产环境中静默回退警告
});

// 切换语言函数
export function switchLanguage(locale: SupportedLocale): void {
  if (locale in SUPPORTED_LOCALES) {
    i18n.global.locale.value = locale;
    setStoredLanguage(locale);

    // 触发自定义事件，通知其他组件语言已切换
    window.dispatchEvent(
      new CustomEvent("language-changed", {
        detail: { locale },
      })
    );
  } else {
    console.warn(`Unsupported locale: ${locale}`);
  }
}

// 获取当前语言
export function getCurrentLanguage(): SupportedLocale {
  return i18n.global.locale.value as SupportedLocale;
}

// 检查是否为支持的语言
export function isSupportedLocale(locale: string): locale is SupportedLocale {
  return locale in SUPPORTED_LOCALES;
}

// 导出类型
export type MessageSchema = typeof zhCN;
