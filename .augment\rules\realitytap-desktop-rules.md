---
type: "agent_requested"
description: "适用于 realitytap-desktop 的开发指南，涵盖项目架构、技术栈、国际化要求、核心开发原则等。"
---
# RealityTap Haptics Studio - 开发指南

## 项目架构
- **应用**: 跨平台桌面应用 (Win/Mac/Linux)
- **核心**: Tauri v2 + Vue3 + TypeScript + Rust
- **环境**: Windows + PowerShell + VSCode

## 技术栈要求
- 使用 context7
- 具体的数字或者字符串比如使用常量定义

## 基本原则
- 请记住，代码是写给人看的，只是机器恰好可以运行
- 随意更改代码是要扣工资的
- 使用现代化和专业化的日志系统

### 前端 (必须)
- Vue3 Composition API + `<script setup>`
- TypeScript 严格模式
- Pinia 状态管理
- Naive UI v2 组件库
- Vite 构建工具
- Vue I18n (国际化支持)

### 后端 (必须)
- Rust + Tauri v2
- Serde JSON 序列化

## 国际化要求 (I18n)
### 支持语言
- **简体中文** (zh-CN)
- **英文** (en-US) - 默认fallback语言
- **日语** (ja-JP)
- **韩语** (ko-KR)

### 默认语言策略
1. **优先级**: 操作系统语言 > 用户手动设置 > 英文fallback
2. **自动检测**: 应用启动时检测操作系统语言
3. **Fallback机制**: 不支持的操作系统语言自动使用英文

### 国际化范围
- ✅ **需要国际化**: 所有UI/UX可见的文本内容
- ❌ **不需要国际化**: console输出、日志信息、开发调试信息

### 国际化开发原则
1. **UI可见内容必须国际化**: 按钮文本、标题、提示信息、错误消息等
2. **Console输出保持原样**: 开发调试信息、日志等不需要国际化
3. **翻译key统一命名**: 使用有意义的嵌套key结构
4. **默认语言策略**: 操作系统语言优先，不支持时使用英文fallback
5. **动态参数支持**: 使用参数化翻译处理动态内容
6. **语言检测**: 应用启动时自动检测操作系统语言

## 核心开发原则

### 1. 逻辑分离 (强制)
- **Vue 文件**: 仅模板 + 样式 + 基本导入
- **复杂逻辑**: 全部提取到 `.ts` 文件中
- **合理颗粒化**: 功能内聚，避免过度拆分
- **DRY原则**:  Don't Repeat Yourself，提高代码可维护性和一致性

### 2. 文件结构
```
src/
├── components/     # Vue组件
├── composables/    # 组合函数
├── utils/         # 工具函数库
│   ├── validation/ # 验证函数
│   ├── format/     # 格式化函数
│   ├── api/       # API函数
│   └── helpers/   # 通用函数
├── stores/        # Pinia stores
├── types/         # TS类型定义
└── views/         # 页面组件
└── tests/         # 测试文件
    ├── unit/      # 单元测试
    ├── integration/ # 集成测试
    └── utils/     # 测试工具
    └── temp/      # 临时测试文件 (待清理)
```

### 3. 代码示例
**Vue 组件 (简洁)**

```vue
<template>
  <n-card :title="title">
    <n-button @click="handleClick">{{ buttonText }}</n-button>
  </n-card>
</template>

<script setup lang="ts">
import type { Props } from "./types";
import { useTableLogic } from "./composables/useTableLogic";

defineProps<Props>();
const { buttonText, handleClick } = useTableLogic();
</script>
```

**Composable (合理组合)**

```typescript
// composables/useTableLogic.ts
import { validateTableData } from "@/utils/validation/dataValidation";
import { formatTableData } from "@/utils/format/dataFormat";
import { fetchTableData } from "@/utils/api/dataApi";

export function useTableLogic() {
  const buttonText = ref("处理数据");
  const loading = ref(false);
  const errors = ref<string[]>([]);

  const handleClick = async () => {
    loading.value = true;
    errors.value = [];

    try {
      // 获取数据
      const rawData = await fetchTableData();

      // 验证数据
      const validation = validateTableData(rawData);
      if (!validation.isValid) {
        errors.value = validation.errors;
        return;
      }

      // 格式化数据
      const formattedData = formatTableData(rawData);

      // 处理结果
      await processFormattedData(formattedData);
    } catch (error) {
      errors.value = ["处理失败: " + error.message];
    } finally {
      loading.value = false;
    }
  };

  const processFormattedData = async (data: any[]) => {
    // 具体的业务处理逻辑
    console.log("处理数据:", data);
  };

  return {
    buttonText,
    loading: readonly(loading),
    errors: readonly(errors),
    handleClick,
  };
}
```

**工具函数 (合理颗粒化)**

```typescript
// utils/validation/dataValidation.ts
export const validateTableData = (data: any[]): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!Array.isArray(data)) {
    errors.push("数据必须为数组格式");
    return { isValid: false, errors };
  }

  const invalidRows = data.filter((row, index) => {
    if (!row.name || !row.age) {
      errors.push(`第${index + 1}行缺少必填字段`);
      return true;
    }
    return false;
  });

  return { isValid: invalidRows.length === 0, errors };
};

// utils/format/dataFormat.ts
export const formatTableData = (data: any[]) => {
  return data.map((row) => ({
    name: row.name?.trim().toLowerCase() || "",
    age: Math.max(0, parseInt(row.age) || 0),
    displayName: `${row.name} (${row.age}岁)`,
  }));
};
```

## 开发要求

### 代码规范
1. **Vue 文件极简**: 只含模板和基本导入
2. **逻辑全分离**: 复杂代码必须在 `.ts` 文件中
3. **合理颗粒化**: 相关功能组合在一起，避免过度拆分
4. **功能内聚**: 一个函数完成一个完整的业务逻辑
5. **类型完整**: 所有代码必须有完整类型定义
6. **智能测试**: 复杂/关键功能需编写测试代码
7. **国际化完整**: 所有UI可见文本必须支持国际化

### 颗粒化原则
**适度拆分** (推荐):
- 按业务功能模块拆分 (推荐)
- 复杂函数内部逻辑适当拆分
- 可复用的通用逻辑单独提取
- 保持函数的完整性和可读性

**避免过度拆分**:
- 不要将简单的 3-5 行逻辑单独成函数
- 避免创建只被调用一次的微小函数
- 相关的处理步骤保持在同一函数内
- 优先考虑代码的可读性和维护性

### 测试要求 (自行判断，非复杂或者重要功能不需要进行测试)
**何时编写测试**:
- 复杂业务逻辑函数
- 数据验证/转换函数
- API 调用相关函数
- 核心工具函数
- 易出错的边界情况
- UI/UX 交互逻辑

**测试规范**:
- 单元测试优先，必要时集成测试
- 必要时创建 UI/UX 交互模拟 HTML 进行模拟
- 测试覆盖核心逻辑和边界情况

### 测试资源管理 (强制要求)
**临时文件管理**:
- 测试中生成的文件必须存放在 `src/__tests__/` 目录

**清理要求** (强制执行):

1. **测试完成后立即清理**: 每个测试套件结束后必须清理生成的文件
2. **异常处理**: 即使测试失败也要确保资源被清理

### 函数命名规范

- 验证: `isValid*()`, `validate*()`, `check*()`
- 转换: `transform*()`, `format*()`, `parse*()`
- 处理: `handle*()`, `process*()`, `execute*()`
- 创建: `create*()`, `build*()`, `make*()`

### 测试要求

### 开发检查清单

- [ ] Vue 文件是否简洁 (仅模板+导入)
- [ ] 逻辑是否分离到独立 `.ts` 文件
- [ ] 函数是否合理颗粒化且保持可读性
- [ ] 类型定义是否完整
- [ ] 是否遵循功能内聚原则
- [ ] 是否遵循单一职责原则
- [ ] 是否使用推荐的技术栈
- [ ] 关键功能是否需要测试覆盖
- [ ] 测试生成的临时文件是否已经清理
- [ ] 所有UI可见文本是否已国际化
- [ ] 是否支持4种目标语言 (zh-CN, en-US, ja-JP, ko-KR)
- [ ] Console输出是否保持非国际化状态

**重要**:

- 优先考虑代码的可读性和维护性，合理进行功能拆分。
- 每个函数应该完成一个完整的业务逻辑，避免过度颗粒化导致代码分散。
- 对于复杂逻辑、数据处理、API 调用等关键功能，判断并编写相应测试。
- 所有测试必须在完成后清理生成的临时文件和资源，确保项目目录的整洁性。
- 所有用户可见的文本内容必须支持国际化，console输出和调试信息除外。
