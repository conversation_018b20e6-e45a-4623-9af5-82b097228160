import rateLimit from 'express-rate-limit';
import { config } from '@/config/server.config';
import { logger } from '@/utils/logger.util';
import type { Request, Response } from 'express';

/**
 * 检查IP是否在白名单中
 */
function isWhitelistedIP(ip: string): boolean {
  const whitelistIps = config.security.rateLimitWhitelistIps;
  return whitelistIps.includes(ip) || whitelistIps.includes('*');
}

/**
 * 创建速率限制中间件的通用函数
 */
function createRateLimiter(options: {
  windowMs: number;
  max: number;
  skipCondition?: (req: Request) => boolean;
  logPrefix?: string;
}) {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests from this IP, please try again later.',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req: Request, res: Response) => {
      const logPrefix = options.logPrefix || '访问频率限制';
      logger.warn(`${logPrefix}超出限制`, {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        windowMs: options.windowMs,
        maxRequests: options.max,
        module: 'system',
        operation: 'rate_limit_exceeded',
      });

      res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests from this IP, please try again later.',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      });
    },
    skip: (req: Request) => {
      // 检查IP白名单
      if (req.ip && isWhitelistedIP(req.ip)) {
        return true;
      }

      // 跳过健康检查
      if (req.path === '/health' || req.path === '/api/v1/health') {
        return true;
      }

      // 自定义跳过条件
      if (options.skipCondition && options.skipCondition(req)) {
        return true;
      }

      return false;
    },
  });
}

// 管理API速率限制中间件
export const adminRateLimitMiddleware = createRateLimiter({
  windowMs: config.security.adminRateLimitWindowMs,
  max: config.security.adminRateLimitMaxRequests,
  logPrefix: 'Admin API rate limit',
});

// 公共API速率限制中间件
export const publicRateLimitMiddleware = createRateLimiter({
  windowMs: config.security.publicRateLimitWindowMs,
  max: config.security.publicRateLimitMaxRequests,
  logPrefix: 'Public API rate limit',
});

// 通用速率限制中间件（向后兼容）
export const rateLimitMiddleware = createRateLimiter({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.rateLimitMaxRequests,
  logPrefix: 'General rate limit',
});
