# Multi-stage build for production optimization
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Set environment variable to indicate Docker build
ENV DOCKER_BUILD=true

# Copy package files and npm configuration
COPY package*.json ./
COPY tsconfig.json ./
COPY .npmrc ./

# Configure npm for better network handling
RUN npm config set registry https://registry.npmjs.org/ && \
    npm config set fetch-retries 5 && \
    npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000

# Install all dependencies (including devDependencies for building)
# Use --no-optional to avoid potential binary issues and add retry logic
RUN npm ci --no-optional || (sleep 5 && npm ci --no-optional) || (sleep 10 && npm ci --no-optional)
RUN npm cache clean --force

# Copy source code
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY admin-ui/ ./admin-ui/

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine AS production

# Install system dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    sqlite \
    && rm -rf /var/cache/apk/*

# Create app user and group (optional, will use root)
RUN addgroup -g 1001 -S realitytap && \
    adduser -S -D -H -u 1001 -s /sbin/nologin -G realitytap realitytap

# Set working directory
WORKDIR /app

# Copy package files and npm configuration for production dependencies
COPY package*.json ./
COPY .npmrc ./

# Configure npm for better network handling
RUN npm config set registry https://registry.npmjs.org/ && \
    npm config set fetch-retries 5 && \
    npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000

# Install production dependencies with retry logic
RUN npm ci --only=production --no-optional || (sleep 5 && npm ci --only=production --no-optional) || (sleep 10 && npm ci --only=production --no-optional)
RUN npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/admin-ui/dist ./admin-ui/dist

# Copy scripts and configuration
COPY docker/entrypoint.sh ./entrypoint.sh
COPY storage/ ./storage/

# Create necessary directories with proper permissions
RUN mkdir -p \
    /app/storage/database \
    /app/storage/releases \
    /app/storage/logs \
    /app/storage/temp \
    /app/storage/backup \
    /app/storage/metadata \
    /app/ssl \
    && chmod +x /app/entrypoint.sh \
    && chmod -R 755 /app/storage

# Set environment variables
ENV NODE_ENV=production \
    PORT=3000 \
    HOST=0.0.0.0 \
    DB_ENABLED=true \
    DB_PATH=/app/storage/database/ota.db \
    DB_BACKUP_PATH=/app/storage/backup/database \
    STORAGE_PATH=/app/storage \
    RELEASES_PATH=/app/storage/releases \
    METADATA_PATH=/app/storage/metadata \
    LOGS_PATH=/app/storage/logs \
    TEMP_PATH=/app/storage/temp

# Switch to non-root user (can be overridden by docker-compose)
USER realitytap

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application with custom entrypoint
CMD ["./entrypoint.sh"]
