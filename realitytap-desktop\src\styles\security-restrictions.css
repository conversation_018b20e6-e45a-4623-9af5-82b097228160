/**
 * 生产环境安全限制样式
 * 禁用文本选择、拖拽等用户交互功能
 */

/* 生产环境安全限制样式 */
.security-restrictions-enabled {
  /* 禁用文本选择 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  
  /* 禁用拖拽 */
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
  
  /* 禁用高亮选择 */
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 应用到所有子元素 */
.security-restrictions-enabled * {
  /* 禁用文本选择 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  
  /* 禁用拖拽 */
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
  
  /* 禁用高亮选择 */
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 特殊元素的例外处理 */
.security-restrictions-enabled input,
.security-restrictions-enabled textarea,
.security-restrictions-enabled [contenteditable="true"],
.security-restrictions-enabled .allow-text-selection {
  /* 允许输入框和可编辑元素的文本选择 */
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

/* 禁用图片和媒体元素的拖拽 */
.security-restrictions-enabled img,
.security-restrictions-enabled video,
.security-restrictions-enabled audio,
.security-restrictions-enabled canvas {
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
  
  /* 禁用右键菜单 */
  pointer-events: none !important;
}

/* 恢复必要元素的指针事件 */
.security-restrictions-enabled button,
.security-restrictions-enabled input,
.security-restrictions-enabled textarea,
.security-restrictions-enabled select,
.security-restrictions-enabled a,
.security-restrictions-enabled [role="button"],
.security-restrictions-enabled .clickable {
  pointer-events: auto !important;
}

/* 禁用开发者工具相关的样式调试 */
.security-restrictions-enabled {
  /* 防止通过 CSS 注入调试信息 */
  outline: none !important;
  border: none !important;
}

/* 隐藏可能暴露调试信息的元素 */
.security-restrictions-enabled .debug-info,
.security-restrictions-enabled .dev-tools,
.security-restrictions-enabled [data-debug],
.security-restrictions-enabled [data-testid] {
  display: none !important;
  visibility: hidden !important;
}

/* 防止通过伪元素显示调试信息 */
.security-restrictions-enabled *::before,
.security-restrictions-enabled *::after {
  content: none !important;
}

/* 特殊情况：允许某些伪元素（如图标） */
.security-restrictions-enabled .icon::before,
.security-restrictions-enabled .icon::after,
.security-restrictions-enabled .allow-pseudo::before,
.security-restrictions-enabled .allow-pseudo::after {
  content: attr(data-content) !important;
}
