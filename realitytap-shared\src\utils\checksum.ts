/**
 * 校验和相关工具函数
 */

import type { ChecksumAlgorithm } from '../constants/ota';

/**
 * 解析校验和字符串
 */
export interface ParsedChecksum {
  algorithm: ChecksumAlgorithm;
  hash: string;
}

/**
 * 解析校验和字符串
 */
export function parseChecksum(checksumString: string): ParsedChecksum {
  const parts = checksumString.split(':');
  if (parts.length !== 2) {
    throw new Error('Invalid checksum format. Expected "algorithm:hash"');
  }

  const [algorithm, hash] = parts;
  if (!algorithm || !hash) {
    throw new Error('Invalid checksum format. Algorithm or hash is empty');
  }

  const supportedAlgorithms: ChecksumAlgorithm[] = ['sha256', 'md5', 'sha1', 'sha512'];
  if (!supportedAlgorithms.includes(algorithm as ChecksumAlgorithm)) {
    throw new Error(`Unsupported checksum algorithm: ${algorithm}`);
  }

  return {
    algorithm: algorithm as ChecksumAlgorithm,
    hash: hash.toLowerCase(),
  };
}

/**
 * 验证校验和格式
 */
export function isValidChecksumFormat(checksumString: string): boolean {
  try {
    const { algorithm, hash } = parseChecksum(checksumString);

    // 检查哈希长度
    const expectedLengths: Record<ChecksumAlgorithm, number> = {
      md5: 32,
      sha1: 40,
      sha256: 64,
      sha512: 128,
    };

    const expectedLength = expectedLengths[algorithm];
    if (hash.length !== expectedLength) {
      return false;
    }

    // 检查是否为有效的十六进制字符串
    return /^[a-fA-F0-9]+$/.test(hash);
  } catch (error) {
    return false;
  }
}

/**
 * 在浏览器环境中计算 SHA256
 */
export async function calculateSHA256Browser(data: ArrayBuffer): Promise<string> {
  if (!isBrowserEnvironment()) {
    throw new Error('Web Crypto API not available');
  }

  const hashBuffer = await globalThis.crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return `sha256:${hashHex}`;
}

/**
 * 在 Node.js 环境中计算 SHA256
 */
export function calculateSHA256Node(_data: any): string {
  // 注意：这个函数需要在 Node.js 环境中使用 crypto 模块
  // 在实际使用时需要动态导入 crypto 模块
  throw new Error('This function should be implemented in Node.js environment');
}

/**
 * 检测是否为浏览器环境
 */
function isBrowserEnvironment(): boolean {
  return typeof globalThis !== 'undefined' &&
         'crypto' in globalThis &&
         globalThis.crypto &&
         'subtle' in globalThis.crypto;
}

/**
 * 检测是否为 Node.js 环境
 */
function isNodeEnvironment(): boolean {
  return typeof (globalThis as any).process !== 'undefined' &&
         (globalThis as any).process.versions !== undefined &&
         (globalThis as any).process.versions.node !== undefined;
}

/**
 * 通用的 SHA256 计算函数
 */
export async function calculateSHA256(data: ArrayBuffer | any): Promise<string> {
  // 检测环境
  if (isBrowserEnvironment()) {
    // 浏览器环境
    return calculateSHA256Browser(data as ArrayBuffer);
  } else if (isNodeEnvironment()) {
    // Node.js 环境
    const crypto = await import('crypto' as any);
    const Buffer = (globalThis as any).Buffer;
    const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
    const hash = crypto.createHash('sha256').update(buffer).digest('hex');
    return `sha256:${hash}`;
  } else {
    throw new Error('Unsupported environment for checksum calculation');
  }
}

/**
 * 验证校验和
 */
export async function verifyChecksum(
  data: ArrayBuffer | any,
  expectedChecksum: string
): Promise<boolean> {
  try {
    const { algorithm } = parseChecksum(expectedChecksum);

    let actualChecksum: string;

    switch (algorithm) {
      case 'sha256':
        actualChecksum = await calculateSHA256(data);
        break;
      default:
        throw new Error(`Checksum algorithm ${algorithm} not implemented`);
    }

    return actualChecksum.toLowerCase() === expectedChecksum.toLowerCase();
  } catch (error) {
    return false;
  }
}

/**
 * 比较两个校验和
 */
export function compareChecksums(checksum1: string, checksum2: string): boolean {
  try {
    const parsed1 = parseChecksum(checksum1);
    const parsed2 = parseChecksum(checksum2);

    return parsed1.algorithm === parsed2.algorithm &&
           parsed1.hash === parsed2.hash;
  } catch (error) {
    return false;
  }
}

/**
 * 格式化校验和字符串
 */
export function formatChecksum(algorithm: ChecksumAlgorithm, hash: string): string {
  return `${algorithm}:${hash.toLowerCase()}`;
}
