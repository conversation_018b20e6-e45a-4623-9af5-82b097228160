import { SystemConfig } from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import { ConfigDAO } from '@/dao/config.dao';
import { ConfigChangeLogDAO } from '@/dao/config-change-log.dao';

/**
 * 基于数据库的配置管理服务
 */
export class ConfigDatabaseService {
  private configDAO: ConfigDAO;
  private configChangeLogDAO: ConfigChangeLogDAO;
  private configCache: SystemConfig | null = null;
  private lastCacheUpdate: number = 0;
  private readonly cacheTimeout = 300000; // 5分钟缓存

  constructor() {
    this.configDAO = new ConfigDAO();
    this.configChangeLogDAO = new ConfigChangeLogDAO();
  }

  /**
   * 获取系统配置
   */
  async getConfig(): Promise<SystemConfig> {
    const now = Date.now();
    if (this.configCache && now - this.lastCacheUpdate < this.cacheTimeout) {
      return this.configCache;
    }

    try {
      this.configCache = await this.configDAO.getSystemConfig();
      this.lastCacheUpdate = now;
      logger.debug('System config loaded from database');
      return this.configCache;
    } catch (error) {
      logger.error('Failed to load system config from database', { error });
      throw new Error(`Failed to load system config: ${error}`);
    }
  }

  /**
   * 更新系统配置
   */
  async updateConfig(config: SystemConfig, userId?: string, reason?: string): Promise<SystemConfig> {
    try {
      // 记录配置变更
      if (this.configCache) {
        await this.logConfigChanges(this.configCache, config, userId, reason);
      }

      // 更新配置
      await this.configDAO.updateSystemConfig(config);

      // 清除缓存
      this.clearCache();

      logger.info('System config updated in database', { userId, reason });

      // 返回更新后的配置
      return config;
    } catch (error) {
      logger.error('Failed to update system config in database', { error, userId, reason });
      throw new Error(`Failed to update system config: ${error}`);
    }
  }

  /**
   * 获取单个配置值
   */
  async getConfigValue<T = any>(key: string, defaultValue?: T): Promise<T | undefined> {
    try {
      return await this.configDAO.getConfigValue<T>(key, defaultValue);
    } catch (error) {
      logger.error('Failed to get config value from database', { key, error });
      throw new Error(`Failed to get config value: ${error}`);
    }
  }

  /**
   * 设置单个配置值
   */
  async setConfigValue(key: string, value: any, userId?: string, reason?: string): Promise<void> {
    try {
      // 记录旧值
      const oldValue = await this.configDAO.getConfigValue(key);
      
      // 设置新值
      await this.configDAO.setConfig(key, value);
      
      // 记录变更日志
      if (oldValue !== undefined) {
        await this.logSingleConfigChange(key, oldValue, value, userId, reason);
      }
      
      // 清除缓存
      this.clearCache();
      
      logger.debug('Config value updated in database', { key, userId, reason });
    } catch (error) {
      logger.error('Failed to set config value in database', { key, error, userId, reason });
      throw new Error(`Failed to set config value: ${error}`);
    }
  }

  /**
   * 批量更新配置
   */
  async batchUpdateConfig(configs: Record<string, any>, userId?: string, reason?: string): Promise<void> {
    try {
      await this.configDAO.batchSetConfigs(configs);
      this.clearCache();
      
      logger.info('Batch config update completed in database', { 
        count: Object.keys(configs).length, 
        userId, 
        reason 
      });
    } catch (error) {
      logger.error('Failed to batch update config in database', { error, userId, reason });
      throw new Error(`Failed to batch update config: ${error}`);
    }
  }

  /**
   * 重置配置为默认值
   */
  async resetConfig(userId?: string, reason?: string): Promise<SystemConfig> {
    try {
      const defaultConfig = this.getDefaultConfig();
      await this.updateConfig(defaultConfig, userId, reason || 'Reset to defaults');

      logger.info('Config reset to defaults in database', { userId, reason });
      return defaultConfig;
    } catch (error) {
      logger.error('Failed to reset config to defaults in database', { error, userId, reason });
      throw new Error(`Failed to reset config to defaults: ${error}`);
    }
  }

  /**
   * 验证配置
   */
  async validateConfig(config: SystemConfig): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      // 验证上传配置
      if (config.upload) {
        if (config.upload.maxFileSize < 1024 * 1024) {
          errors.push('Upload max file size must be at least 1MB');
        }
        if (config.upload.maxFileSize > 2 * 1024 * 1024 * 1024) {
          errors.push('Upload max file size cannot exceed 2GB');
        }
        if (!Array.isArray(config.upload.allowedExtensions) || config.upload.allowedExtensions.length === 0) {
          errors.push('Upload allowed extensions must be a non-empty array');
        }
      }

      // 验证下载配置
      if (config.download?.rateLimit) {
        if (config.download.rateLimit.windowMs < 60000) {
          errors.push('Download rate limit window must be at least 1 minute');
        }
        if (config.download.rateLimit.maxRequests < 1) {
          errors.push('Download rate limit max requests must be at least 1');
        }
      }

      // 验证存储配置
      if (config.storage) {
        if (config.storage.maxStorageSize < 100 * 1024 * 1024) {
          errors.push('Storage max size must be at least 100MB');
        }
      }

      // 验证安全配置
      if (config.security?.allowedOrigins) {
        if (!Array.isArray(config.security.allowedOrigins)) {
          errors.push('Security allowed origins must be an array');
        }
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      logger.error('Config validation failed', { error });
      return {
        isValid: false,
        errors: [`Validation error: ${error}`]
      };
    }
  }

  /**
   * 获取配置模式（用于前端验证）
   */
  getConfigSchema(): any {
    // 返回配置的 JSON Schema
    return {
      type: 'object',
      properties: {
        upload: {
          type: 'object',
          properties: {
            maxFileSize: { type: 'number', minimum: 1048576 },
            allowedExtensions: { type: 'array', items: { type: 'string' } },
            tempDir: { type: 'string' },
            cleanupInterval: { type: 'number', minimum: 0 }
          },
          required: ['maxFileSize', 'allowedExtensions', 'tempDir', 'cleanupInterval']
        },
        download: {
          type: 'object',
          properties: {
            rateLimit: {
              type: 'object',
              properties: {
                windowMs: { type: 'number', minimum: 60000 },
                maxRequests: { type: 'number', minimum: 1 }
              },
              required: ['windowMs', 'maxRequests']
            },
            enableStats: { type: 'boolean' },
            enableCompression: { type: 'boolean' }
          },
          required: ['rateLimit', 'enableStats', 'enableCompression']
        },
        storage: {
          type: 'object',
          properties: {
            basePath: { type: 'string' },
            cleanupInterval: { type: 'number', minimum: 0 },
            maxStorageSize: { type: 'number', minimum: 104857600 }
          },
          required: ['basePath', 'cleanupInterval', 'maxStorageSize']
        },
        security: {
          type: 'object',
          properties: {
            enableCORS: { type: 'boolean' },
            allowedOrigins: { type: 'array', items: { type: 'string' } },
            enableHelmet: { type: 'boolean' }
          },
          required: ['enableCORS', 'allowedOrigins', 'enableHelmet']
        },
        logging: {
          type: 'object',
          properties: {
            level: { type: 'string', enum: ['error', 'warn', 'info', 'debug'] },
            enableFileLogging: { type: 'boolean' },
            maxLogFiles: { type: 'number', minimum: 1 },
            maxLogSize: { type: 'string' }
          },
          required: ['level', 'enableFileLogging', 'maxLogFiles', 'maxLogSize']
        }
      },
      required: ['upload', 'download', 'storage', 'security', 'logging']
    };
  }

  /**
   * 获取配置变更历史
   */
  async getConfigHistory(limit?: number): Promise<any> {
    return this.getConfigChangeLogs(undefined, limit);
  }

  /**
   * 获取配置统计信息
   */
  async getConfigStats(): Promise<{
    totalConfigs: number;
    configsByType: Record<string, number>;
    lastUpdated?: string;
  }> {
    try {
      const stats = await this.configDAO.getConfigStats();

      return {
        ...stats,
        lastUpdated: new Date(this.lastCacheUpdate).toISOString()
      };
    } catch (error) {
      logger.error('Failed to get config stats from database', { error });
      throw new Error(`Failed to get config stats: ${error}`);
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.configCache = null;
    this.lastCacheUpdate = 0;
    logger.debug('Config database service cache cleared');
  }

  /**
   * 记录配置变更
   */
  private async logConfigChanges(oldConfig: SystemConfig, newConfig: SystemConfig, userId?: string, reason?: string): Promise<void> {
    try {
      const changes = this.detectConfigChanges(oldConfig, newConfig);
      
      for (const change of changes) {
        await this.logSingleConfigChange(change.field, change.oldValue, change.newValue, userId, reason);
      }
    } catch (error) {
      logger.warn('Failed to log config changes', { error });
      // 不抛出错误，因为这不应该阻止配置更新
    }
  }

  /**
   * 记录单个配置变更
   */
  private async logSingleConfigChange(key: string, oldValue: any, newValue: any, userId?: string, reason?: string): Promise<void> {
    try {
      await this.configChangeLogDAO.createConfigChangeLog({
        config_key: key,
        old_value: oldValue,
        new_value: newValue,
        user_id: userId,
        change_reason: reason
      });

      logger.info('Config change logged to database', { key, userId, reason });
    } catch (error) {
      logger.warn('Failed to log single config change to database', { key, error });
    }
  }

  /**
   * 检测配置变更
   */
  private detectConfigChanges(oldConfig: SystemConfig, newConfig: SystemConfig): Array<{
    field: string;
    oldValue: any;
    newValue: any;
  }> {
    const changes: Array<{ field: string; oldValue: any; newValue: any }> = [];
    
    const flatOld = this.flattenConfig(oldConfig);
    const flatNew = this.flattenConfig(newConfig);
    
    // 检查变更和新增
    for (const [key, newValue] of Object.entries(flatNew)) {
      const oldValue = flatOld[key];
      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        changes.push({
          field: key,
          oldValue,
          newValue
        });
      }
    }
    
    // 检查删除
    for (const [key, oldValue] of Object.entries(flatOld)) {
      if (!(key in flatNew)) {
        changes.push({
          field: key,
          oldValue,
          newValue: undefined
        });
      }
    }
    
    return changes;
  }

  /**
   * 扁平化配置对象
   */
  private flattenConfig(config: any, prefix: string = ''): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(config)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        Object.assign(result, this.flattenConfig(value, fullKey));
      } else {
        result[fullKey] = value;
      }
    }
    
    return result;
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(): SystemConfig {
    return {
      upload: {
        maxFileSize: 524288000,
        allowedExtensions: ['.exe', '.dmg', '.deb', '.rpm', '.zip', '.tar.gz'],
        tempDir: './storage/temp',
        cleanupInterval: 86400000
      },
      download: {
        rateLimit: {
          windowMs: 900000,
          maxRequests: 100
        },
        enableStats: true,
        enableCompression: true
      },
      storage: {
        basePath: './storage',
        cleanupInterval: 604800000,
        maxStorageSize: 10737418240
      },
      security: {
        enableCORS: true,
        allowedOrigins: ['http://localhost:3000', 'http://localhost:5173'],
        enableHelmet: true
      },
      logging: {
        level: 'info',
        enableFileLogging: true,
        maxLogFiles: 10,
        maxLogSize: '10m'
      }
    };
  }



  /**
   * 获取配置变更日志
   */
  async getConfigChangeLogs(configKey?: string, limit?: number) {
    try {
      if (configKey) {
        return await this.configChangeLogDAO.getChangeLogsByKey(configKey, limit);
      } else {
        return await this.configChangeLogDAO.getAllChangeLogs(limit);
      }
    } catch (error) {
      logger.error('Failed to get config change logs', { error, configKey, limit });
      throw error;
    }
  }

  /**
   * 获取用户的配置变更日志
   */
  async getUserConfigChangeLogs(userId: string, limit?: number) {
    try {
      return await this.configChangeLogDAO.getChangeLogsByUserId(userId, limit);
    } catch (error) {
      logger.error('Failed to get user config change logs', { error, userId, limit });
      throw error;
    }
  }

  /**
   * 清理旧的配置变更日志
   */
  async cleanupOldChangeLogs(olderThanDays: number = 90): Promise<number> {
    try {
      const deletedCount = await this.configChangeLogDAO.deleteOldChangeLogs(olderThanDays);
      logger.info('Cleaned up old config change logs', { deletedCount, olderThanDays });
      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup old config change logs', { error, olderThanDays });
      throw error;
    }
  }
}

// 导出单例实例
export const configDatabaseService = new ConfigDatabaseService();
