/**
 * 版本相关常量
 */

/**
 * 版本格式正则表达式（支持完整的语义化版本格式）
 */
export const VERSION_REGEX = /^\d+\.\d+\.\d+(?:-[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*)?(?:\+[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*)?$/;

/**
 * 语义化版本正则表达式
 */
export const SEMVER_REGEX = /^(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*))?(?:\+([a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*))?$/;

/**
 * 预发布版本标识符
 */
export const PRERELEASE_IDENTIFIERS = ['alpha', 'beta', 'rc'] as const;

/**
 * 版本比较结果
 */
export const VERSION_COMPARISON = {
  LESS_THAN: -1,
  EQUAL: 0,
  GREATER_THAN: 1,
} as const;

/**
 * 更新类型
 */
export const UPDATE_TYPES = {
  MAJOR: 'major',
  MINOR: 'minor',
  PATCH: 'patch',
  PRERELEASE: 'prerelease',
  NONE: 'none',
} as const;

/**
 * 版本状态
 */
export const VERSION_STATUS = {
  CURRENT: 'current',
  OUTDATED: 'outdated',
  DEPRECATED: 'deprecated',
  UNSUPPORTED: 'unsupported',
} as const;

/**
 * 最小支持版本
 */
export const MINIMUM_SUPPORTED_VERSIONS = {
  stable: '1.0.0',
  beta: '1.0.0',
  alpha: '1.0.0',
} as const;

/**
 * 版本生命周期阶段
 */
export const VERSION_LIFECYCLE = {
  DEVELOPMENT: 'development',
  ALPHA: 'alpha',
  BETA: 'beta',
  RELEASE_CANDIDATE: 'rc',
  STABLE: 'stable',
  MAINTENANCE: 'maintenance',
  END_OF_LIFE: 'eol',
} as const;

/**
 * 版本优先级
 */
export const VERSION_PRIORITY = {
  CRITICAL: 1,
  HIGH: 2,
  MEDIUM: 3,
  LOW: 4,
} as const;

/**
 * 强制更新阈值（版本差异）
 */
export const FORCE_UPDATE_THRESHOLDS = {
  MAJOR: 2, // 超过2个主版本强制更新
  MINOR: 10, // 超过10个次版本强制更新
  PATCH: 50, // 超过50个补丁版本强制更新
} as const;

/**
 * 版本检查间隔（毫秒）
 */
export const VERSION_CHECK_INTERVALS = {
  STABLE: 24 * 60 * 60 * 1000, // 24小时
  BETA: 12 * 60 * 60 * 1000,   // 12小时
  ALPHA: 6 * 60 * 60 * 1000,   // 6小时
} as const;

/**
 * 版本缓存时间（毫秒）
 */
export const VERSION_CACHE_TTL = {
  CLIENT: 5 * 60 * 1000,  // 5分钟
  SERVER: 60 * 1000,      // 1分钟
} as const;
