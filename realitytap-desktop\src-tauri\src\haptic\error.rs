// RealityTap 触觉反馈库错误类型定义

use thiserror::Error;

/// RealityTap 触觉反馈库错误类型
#[derive(Error, Debug, Clone)]
pub enum HapticError {
    #[error("库初始化失败: {0}")]
    InitializationFailed(String),

    #[error("DLL 加载失败: {0}")]
    DllLoadFailed(String),

    #[error("函数符号未找到: {0}")]
    SymbolNotFound(String),

    #[error("配置文件错误: {0}")]
    ConfigFileError(String),

    #[error("设备连接失败: {0}")]
    DeviceConnectionFailed(String),

    #[error("播放失败: {0}")]
    PlaybackFailed(String),

    #[error("参数无效: {0}")]
    InvalidParameter(String),

    #[error("内存分配失败")]
    MemoryAllocationFailed,

    #[error("线程同步错误: {0}")]
    ThreadSyncError(String),

    #[error("处理超时")]
    ProcessingTimeout,

    #[error("库未初始化")]
    LibraryNotInitialized,

    #[error("设备未连接")]
    DeviceNotConnected,

    #[error("不支持的操作: {0}")]
    UnsupportedOperation(String),

    #[error("API 未加载: {0}")]
    ApiNotLoaded(String),

    #[error("资源清理失败: {0}")]
    CleanupFailed(String),

    #[error("设备未初始化: {0}")]
    DeviceNotInitialized(String),

    #[error("IO 错误: {0}")]
    IoError(String),

    #[error("FFI 错误: {0}")]
    FfiError(String),

    #[error("Channel 错误: {0}")]
    ChannelError(String),

    #[error("无效状态: {0}")]
    InvalidState(String),

    #[error("未知错误: {0}")]
    Unknown(String),
}

// 手动实现 From<std::io::Error> 转换
impl From<std::io::Error> for HapticError {
    fn from(error: std::io::Error) -> Self {
        HapticError::IoError(error.to_string())
    }
}

/// RealityTap 操作结果类型
pub type HapticResult<T> = Result<T, HapticError>;

/// librtcore 错误码常量定义
pub mod error_codes {
    // 成功状态
    pub const SUCCESS: i32 = 0;

    // 标准错误码（负数）
    pub const GENERAL_INIT_ERROR: i32 = -1;
    pub const CONFIG_FILE_ERROR: i32 = -2;
    pub const DEVICE_CONNECTION_ERROR: i32 = -3;
    pub const INVALID_PARAMETER: i32 = -4;
    pub const MEMORY_ALLOCATION_ERROR: i32 = -5;
    pub const PROCESSING_TIMEOUT: i32 = -6;
    pub const LIBRARY_NOT_LOADED: i32 = -7;
    pub const INVALID_STATE: i32 = -8;
    pub const RESOURCE_BUSY: i32 = -9;
    pub const PERMISSION_DENIED: i32 = -10;

    // librtcore 特定错误码（正数）
    pub const DEVICE_DISCONNECTED: i32 = 1;
    pub const OPERATION_PARAMETER_INVALID: i32 = 2;
    pub const OPERATION_TIMEOUT: i32 = 3;
    pub const SYSTEM_NOT_INITIALIZED: i32 = 4;
    pub const DEVICE_BUSY: i32 = 5;
    pub const QUEUE_FULL: i32 = 6;
    pub const INSUFFICIENT_RESOURCES: i32 = 7;
    pub const OPERATION_INTERRUPTED: i32 = 8;
    pub const HARDWARE_FAULT: i32 = 9;
    pub const CALIBRATION_FAILED: i32 = 10;
    pub const FIRMWARE_ERROR: i32 = 11;
    pub const COMMUNICATION_ERROR: i32 = 12;
    pub const BUFFER_OVERFLOW: i32 = 13;
    pub const BUFFER_UNDERFLOW: i32 = 14;
    pub const SAMPLE_RATE_MISMATCH: i32 = 15;
    pub const FREQUENCY_OUT_OF_RANGE: i32 = 16;
    pub const AMPLITUDE_OUT_OF_RANGE: i32 = 17;
    pub const EFFECT_NOT_FOUND: i32 = 18;
    pub const EFFECT_CORRUPTED: i32 = 19;
    pub const PLAYBACK_FAILED: i32 = 20;
}

/// 错误严重程度
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ErrorSeverity {
    /// 信息性错误，不影响正常操作
    Info,
    /// 警告，可能影响性能但不会导致失败
    Warning,
    /// 错误，操作失败但系统可恢复
    Error,
    /// 严重错误，需要重新初始化
    Critical,
    /// 致命错误，系统无法恢复
    Fatal,
}

/// 错误恢复建议
#[derive(Debug, Clone, PartialEq)]
pub enum RecoveryAction {
    /// 无需操作
    None,
    /// 重试操作
    Retry,
    /// 重新初始化设备
    Reinitialize,
    /// 重新连接设备
    Reconnect,
    /// 检查配置文件
    CheckConfig,
    /// 检查硬件连接
    CheckHardware,
    /// 重启应用程序
    RestartApplication,
    /// 联系技术支持
    ContactSupport,
}

/// 增强的错误信息
#[derive(Debug, Clone)]
pub struct EnhancedErrorInfo {
    pub error_code: i32,
    pub error_type: HapticError,
    pub severity: ErrorSeverity,
    pub recovery_action: RecoveryAction,
    pub user_message: String,
    pub technical_details: String,
    pub is_retryable: bool,
}

/// 将 C API 错误码转换为 Rust 错误类型
pub fn convert_c_error(error_code: i32) -> HapticError {
    use error_codes::*;

    match error_code {
        SUCCESS => panic!("不应该为成功状态调用错误转换"),

        // 标准错误码（负数）
        GENERAL_INIT_ERROR => HapticError::InitializationFailed("一般初始化错误".to_string()),
        CONFIG_FILE_ERROR => HapticError::ConfigFileError("配置文件无效或不存在".to_string()),
        DEVICE_CONNECTION_ERROR => HapticError::DeviceConnectionFailed("设备连接失败".to_string()),
        INVALID_PARAMETER => HapticError::InvalidParameter("参数无效".to_string()),
        MEMORY_ALLOCATION_ERROR => HapticError::MemoryAllocationFailed,
        PROCESSING_TIMEOUT => HapticError::ProcessingTimeout,
        LIBRARY_NOT_LOADED => HapticError::ApiNotLoaded("库未加载".to_string()),
        INVALID_STATE => HapticError::InvalidState("系统状态无效".to_string()),
        RESOURCE_BUSY => HapticError::Unknown("资源忙碌".to_string()),
        PERMISSION_DENIED => HapticError::Unknown("权限被拒绝".to_string()),

        // librtcore 特定错误码（正数）
        DEVICE_DISCONNECTED => HapticError::DeviceConnectionFailed("设备未连接或已断开".to_string()),
        OPERATION_PARAMETER_INVALID => HapticError::InvalidParameter("操作参数无效".to_string()),
        OPERATION_TIMEOUT => HapticError::ProcessingTimeout,
        SYSTEM_NOT_INITIALIZED => HapticError::InitializationFailed("系统未初始化".to_string()),
        DEVICE_BUSY => HapticError::Unknown("设备忙碌，无法执行操作".to_string()),
        QUEUE_FULL => HapticError::Unknown("队列已满".to_string()),
        INSUFFICIENT_RESOURCES => HapticError::Unknown("资源不足".to_string()),
        OPERATION_INTERRUPTED => HapticError::Unknown("操作被中断".to_string()),
        HARDWARE_FAULT => HapticError::DeviceConnectionFailed("硬件故障".to_string()),
        CALIBRATION_FAILED => HapticError::InitializationFailed("设备校准失败".to_string()),
        FIRMWARE_ERROR => HapticError::DeviceConnectionFailed("固件错误".to_string()),
        COMMUNICATION_ERROR => HapticError::DeviceConnectionFailed("通信错误".to_string()),
        BUFFER_OVERFLOW => HapticError::Unknown("缓冲区溢出".to_string()),
        BUFFER_UNDERFLOW => HapticError::Unknown("缓冲区下溢".to_string()),
        SAMPLE_RATE_MISMATCH => HapticError::InvalidParameter("采样率不匹配".to_string()),
        FREQUENCY_OUT_OF_RANGE => HapticError::InvalidParameter("频率超出范围".to_string()),
        AMPLITUDE_OUT_OF_RANGE => HapticError::InvalidParameter("振幅超出范围".to_string()),
        EFFECT_NOT_FOUND => HapticError::ConfigFileError("效果未找到".to_string()),
        EFFECT_CORRUPTED => HapticError::ConfigFileError("效果数据损坏".to_string()),
        PLAYBACK_FAILED => HapticError::PlaybackFailed("播放失败".to_string()),

        _ => HapticError::Unknown(format!("未知错误码: {}", error_code)),
    }
}

/// 获取增强的错误信息
pub fn get_enhanced_error_info(error_code: i32) -> EnhancedErrorInfo {
    use error_codes::*;

    let error_type = convert_c_error(error_code);

    let (severity, recovery_action, user_message, technical_details, is_retryable) = match error_code {
        // 标准错误码
        GENERAL_INIT_ERROR => (
            ErrorSeverity::Critical,
            RecoveryAction::Reinitialize,
            "初始化失败，请重试".to_string(),
            "一般初始化错误，可能是系统资源不足或配置问题".to_string(),
            true,
        ),
        CONFIG_FILE_ERROR => (
            ErrorSeverity::Error,
            RecoveryAction::CheckConfig,
            "配置文件错误，请检查配置".to_string(),
            "配置文件无效、损坏或不存在".to_string(),
            false,
        ),
        DEVICE_CONNECTION_ERROR => (
            ErrorSeverity::Error,
            RecoveryAction::CheckHardware,
            "设备连接失败，请检查硬件连接".to_string(),
            "无法连接到触觉设备，检查USB连接或驱动程序".to_string(),
            true,
        ),
        INVALID_PARAMETER => (
            ErrorSeverity::Warning,
            RecoveryAction::Retry,
            "参数错误，请检查输入".to_string(),
            "传入的参数不符合要求".to_string(),
            true,
        ),
        MEMORY_ALLOCATION_ERROR => (
            ErrorSeverity::Critical,
            RecoveryAction::RestartApplication,
            "内存不足，请重启应用".to_string(),
            "系统内存不足，无法分配所需内存".to_string(),
            false,
        ),
        PROCESSING_TIMEOUT => (
            ErrorSeverity::Warning,
            RecoveryAction::Retry,
            "操作超时，请重试".to_string(),
            "操作执行时间超过预期".to_string(),
            true,
        ),

        // librtcore 特定错误码
        DEVICE_DISCONNECTED => (
            ErrorSeverity::Error,
            RecoveryAction::Reconnect,
            "设备已断开，请重新连接".to_string(),
            "触觉设备在操作过程中断开连接".to_string(),
            true,
        ),
        HARDWARE_FAULT => (
            ErrorSeverity::Critical,
            RecoveryAction::ContactSupport,
            "硬件故障，请联系技术支持".to_string(),
            "检测到硬件故障，设备可能需要维修".to_string(),
            false,
        ),
        FIRMWARE_ERROR => (
            ErrorSeverity::Critical,
            RecoveryAction::ContactSupport,
            "固件错误，请联系技术支持".to_string(),
            "设备固件出现错误，可能需要固件更新".to_string(),
            false,
        ),
        QUEUE_FULL => (
            ErrorSeverity::Warning,
            RecoveryAction::Retry,
            "系统繁忙，请稍后重试".to_string(),
            "命令队列已满，系统正在处理大量请求".to_string(),
            true,
        ),

        _ => (
            ErrorSeverity::Error,
            RecoveryAction::Retry,
            "发生未知错误，请重试".to_string(),
            format!("未知错误码: {}", error_code),
            true,
        ),
    };

    EnhancedErrorInfo {
        error_code,
        error_type,
        severity,
        recovery_action,
        user_message,
        technical_details,
        is_retryable,
    }
}

/// 将 Rust 错误转换为前端可用的错误信息
impl From<HapticError> for String {
    fn from(error: HapticError) -> Self {
        error.to_string()
    }
}

/// 为 Tauri 命令提供的错误转换
impl serde::Serialize for HapticError {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_str(&self.to_string())
    }
}
