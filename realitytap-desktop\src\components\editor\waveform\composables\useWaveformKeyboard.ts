import { ref, type Ref } from "vue";
import { FREQUENCY_ADJUSTMENT_KEY } from "../config/waveform-constants";
import { waveformLogger } from "@/utils/logger/logger";

/**
 * 键盘事件处理配置接口
 */
export interface KeyboardConfig {
  // Store 和状态 - 支持文件级别的store
  waveformStore: {
    selectedEventId: string | null;
    deleteSelectedEvent: () => void;
    canUndo: () => boolean;
    canRedo: () => boolean;
    undo: () => boolean;
    redo: () => boolean;
    // addHistoryRecord 已移除 - 现在由统一机制自动处理
  };

  // Canvas 引用
  waveformCanvas: Ref<HTMLCanvasElement | null>;

  // 拖拽状态
  draggingTarget: Ref<string | null>;
  draggedEvent: Ref<any>;
  draggedCurveIndex: Ref<number>;

  // 右键菜单状态
  isCanvasMenuVisible: Ref<boolean>;
  closeCanvasMenuFromContextMenu: () => void;

  // 绘制函数
  resetDrawState: () => void;
  throttledDrawWaveform: (forceRedraw?: boolean) => void;

  // 事件tooltip控制函数（可选）
  hideEventTooltip?: () => void;
  showEventTooltip?: (x: number, y: number) => void;
}

/**
 * 键盘事件处理 Composable
 *
 * 负责处理波形图中的所有键盘交互，包括：
 * - 频率调节快捷键状态管理（用于拖拽模式切换）
 * - Delete键删除选中事件
 * - Ctrl+Z/Cmd+Z撤销操作（完整的撤销/重做系统）
 * - Ctrl+Y/Cmd+Y/Ctrl+Shift+Z重做操作
 * - Escape键关闭菜单
 */
export function useWaveformKeyboard(config: KeyboardConfig) {
  // 频率调节快捷键状态管理
  const isFrequencyAdjustmentKeyPressed = ref<boolean>(false);

  /**
   * 检查当前焦点是否在输入框中
   */
  const isInputFocused = (): boolean => {
    const activeElement = document.activeElement;
    if (!activeElement) return false;

    // 检查是否是输入相关的元素
    const inputTags = ['INPUT', 'TEXTAREA', 'SELECT'];
    const isInputTag = inputTags.includes(activeElement.tagName);

    // 检查是否是可编辑的元素
    const isContentEditable = activeElement.getAttribute('contenteditable') === 'true';

    // 检查是否是数字输入框（Naive UI的输入框可能有特殊的class或属性）
    const isNumberInput = activeElement.classList.contains('n-input__input-el') ||
                         activeElement.closest('.n-input-number') !== null ||
                         activeElement.closest('.value-input') !== null;

    return isInputTag || isContentEditable || isNumberInput;
  };

  /**
   * 处理键盘按下事件
   */
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === FREQUENCY_ADJUSTMENT_KEY) {
      isFrequencyAdjustmentKeyPressed.value = true;
      if (config.waveformCanvas.value && config.draggingTarget.value === "continuousCurvePoint") {
        config.waveformCanvas.value.style.cursor = "ns-resize"; // 垂直拖动指示
        // tooltip已经在拖拽开始时显示了，这里不需要再次显示
      }
    }
    // 添加对 Delete键的处理
    else if (event.key === "Delete") {
      // 检查当前焦点是否在输入框中，如果是则不处理Delete键
      if (isInputFocused()) {
        return; // 让输入框自己处理Delete键
      }

      // 如果有选中的事件，则删除它
      if (config.waveformStore.selectedEventId) {
        // 删除事件 - 历史记录由统一机制自动处理
        config.waveformStore.deleteSelectedEvent();

        config.resetDrawState(); // 重置绘制状态
        config.throttledDrawWaveform(true); // 强制重绘波形以反映删除
      }
    }
    // 撤销功能快捷键 (Ctrl+Z 或 Command+Z)
    else if ((event.ctrlKey || event.metaKey) && event.key === "z" && !event.shiftKey) {
      // 检查当前焦点是否在输入框中，如果是则不处理撤销快捷键
      if (isInputFocused()) {
        return; // 让输入框自己处理快捷键（如果有的话）
      }

      // 阻止默认行为
      event.preventDefault();
      event.stopPropagation();

      waveformLogger.info("🔄 检测到撤销快捷键 CTRL+Z", {
        hasWaveformStore: !!config.waveformStore,
        canUndo: config.waveformStore?.canUndo?.(),
        timestamp: new Date().toLocaleTimeString()
      });

      if (config.waveformStore) {
        if (config.waveformStore.canUndo()) {
          waveformLogger.info("🔄 开始执行撤销操作...");
          const success = config.waveformStore.undo();
          if (success) {
            waveformLogger.info("✅ 撤销操作成功");
            // 重绘画布
            config.resetDrawState();
            config.throttledDrawWaveform(true);
          } else {
            waveformLogger.error("❌ 撤销操作失败");
          }
        } else {
          waveformLogger.warn("⚠️ 没有可撤销的操作");
        }
      } else {
        waveformLogger.error("❌ 波形Store未初始化");
      }
    }
    // 重做功能快捷键 (Ctrl+Y 或 Command+Y 或 Ctrl+Shift+Z)
    else if (((event.ctrlKey || event.metaKey) && event.key === "y") ||
             ((event.ctrlKey || event.metaKey) && event.key === "z" && event.shiftKey)) {
      // 检查当前焦点是否在输入框中，如果是则不处理重做快捷键
      if (isInputFocused()) {
        return; // 让输入框自己处理快捷键（如果有的话）
      }

      // 阻止默认行为
      event.preventDefault();
      event.stopPropagation();

      if (config.waveformStore) {
        if (config.waveformStore.canRedo()) {
          const success = config.waveformStore.redo();
          if (success) {
            waveformLogger.debug("重做操作成功");
            // 重绘画布
            config.resetDrawState();
            config.throttledDrawWaveform(true);
          } else {
            waveformLogger.warn("重做操作失败");
          }
        } else {
          waveformLogger.debug("没有可重做的操作");
        }
      } else {
        waveformLogger.warn("波形Store未初始化");
      }
    }
    // 添加ESC键隐藏菜单
    else if (event.key === "Escape" && config.isCanvasMenuVisible.value) {
      config.closeCanvasMenuFromContextMenu();
    }
  };

  /**
   * 处理键盘释放事件
   */
  const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key === FREQUENCY_ADJUSTMENT_KEY) {
      isFrequencyAdjustmentKeyPressed.value = false;

      // 不在这里隐藏事件tooltip，让它在拖拽结束时才隐藏

      if (config.waveformCanvas.value && config.draggingTarget.value === "continuousCurvePoint") {
        // 如果是首尾点，恢复到 default 光标，因为非频率调节键垂直拖动无效
        if (
          config.draggedCurveIndex.value === 0 ||
          (config.draggedEvent.value?.type === "continuous" && config.draggedCurveIndex.value === config.draggedEvent.value.curves.length - 1)
        ) {
          config.waveformCanvas.value.style.cursor = "default";
        } else {
          config.waveformCanvas.value.style.cursor = "move"; // 恢复到自由拖动指示
        }
      }
    }
  };

  return {
    // 状态
    isFrequencyAdjustmentKeyPressed,

    // 事件处理函数
    handleKeyDown,
    handleKeyUp,
  };
}
