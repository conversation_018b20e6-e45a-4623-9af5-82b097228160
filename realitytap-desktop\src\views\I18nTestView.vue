<template>
  <div class="i18n-test-view">
    <div class="test-header">
      <h1>{{ t("app.title") }} - {{ t("i18nTest.title") }}</h1>
      <div class="language-controls">
        <h3>{{ t("i18nTest.languageSwitchTest") }}</h3>
        <div class="language-buttons">
          <n-button
            v-for="lang in availableLanguages"
            :key="lang.code"
            :type="lang.isCurrent ? 'primary' : 'default'"
            @click="switchLanguage(lang.code)"
            style="margin-right: 8px"
          >
            {{ lang.name }}
          </n-button>
        </div>
        <p>{{ t("i18nTest.currentLanguage") }}: {{ currentLanguageName }}</p>
      </div>
    </div>

    <div class="test-sections">
      <div class="test-section">
        <h2>{{ t("i18nTest.commonTextTest") }}</h2>
        <div class="test-items">
          <div class="test-item">
            <span class="label">{{ t("i18nTest.labels.confirm") }}:</span>
            <span class="value">{{ t("common.confirm") }}</span>
          </div>
          <div class="test-item">
            <span class="label">{{ t("i18nTest.labels.cancel") }}:</span>
            <span class="value">{{ t("common.cancel") }}</span>
          </div>
          <div class="test-item">
            <span class="label">{{ t("i18nTest.labels.save") }}:</span>
            <span class="value">{{ t("common.save") }}</span>
          </div>
          <div class="test-item">
            <span class="label">{{ t("i18nTest.labels.delete") }}:</span>
            <span class="value">{{ t("common.delete") }}</span>
          </div>
          <div class="test-item">
            <span class="label">{{ t("i18nTest.labels.loading") }}:</span>
            <span class="value">{{ t("common.loading") }}</span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>项目管理测试</h2>
        <div class="test-items">
          <div class="test-item">
            <span class="label">新建项目:</span>
            <span class="value">{{ t("dashboard.projects.newProject") }}</span>
          </div>
          <div class="test-item">
            <span class="label">打开项目:</span>
            <span class="value">{{ t("dashboard.projects.openProject") }}</span>
          </div>
          <div class="test-item">
            <span class="label">最近项目:</span>
            <span class="value">{{ t("dashboard.projects.recentProjects") }}</span>
          </div>
          <div class="test-item">
            <span class="label">项目创建成功:</span>
            <span class="value">{{ t("project.create.success") }}</span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>设备管理测试</h2>
        <div class="test-items">
          <div class="test-item">
            <span class="label">已连接:</span>
            <span class="value">{{ t("device.status.connected") }}</span>
          </div>
          <div class="test-item">
            <span class="label">未连接:</span>
            <span class="value">{{ t("device.status.disconnected") }}</span>
          </div>
          <div class="test-item">
            <span class="label">无设备:</span>
            <span class="value">{{ t("device.status.noDevices") }}</span>
          </div>
          <div class="test-item">
            <span class="label">设备数量 (3):</span>
            <span class="value">{{ t("device.status.deviceCount", { count: 3 }) }}</span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>学习资源测试</h2>
        <div class="test-items">
          <div class="test-item">
            <span class="label">学习资源标题:</span>
            <span class="value">{{ t("learning.title") }}</span>
          </div>
          <div class="test-item">
            <span class="label">触觉设计入门:</span>
            <span class="value">{{ t("learning.gettingStarted.title") }}</span>
          </div>
          <div class="test-item">
            <span class="label">音频到触觉转换:</span>
            <span class="value">{{ t("learning.audioToHaptics.title") }}</span>
          </div>
          <div class="test-item">
            <span class="label">初学者标签:</span>
            <span class="value">{{ t("learning.gettingStarted.tags.beginner") }}</span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>{{ t("i18nTest.errorMessageTest") }}</h2>
        <div class="test-items">
          <div class="test-item">
            <span class="label">{{ t("i18nTest.labels.unknownError") }}:</span>
            <span class="value">{{ t("errors.unknown") }}</span>
          </div>
          <div class="test-item">
            <span class="label">{{ t("i18nTest.labels.networkError") }}:</span>
            <span class="value">{{ t("errors.networkError") }}</span>
          </div>
          <div class="test-item">
            <span class="label">{{ t("i18nTest.labels.fileNotFound") }}:</span>
            <span class="value">{{ t("errors.fileNotFound") }}</span>
          </div>
          <div class="test-item">
            <span class="label">{{ t("i18nTest.labels.saveFailedExample") }}:</span>
            <span class="value">{{ t("project.save.failed", { error: t("i18nTest.exampleError") }) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="test-footer">
      <p>{{ t("i18nTest.languageDetectionInfo") }}:</p>
      <pre>{{ JSON.stringify(getLanguageStats(), null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NButton } from "naive-ui";
import { useI18n } from "@/composables/useI18n";
import { useLanguageStore } from "@/stores/language-store";
import type { SupportedLocale } from "@/locales";

const { t, currentLanguageName, availableLanguages } = useI18n();
const languageStore = useLanguageStore();

const switchLanguage = async (locale: SupportedLocale) => {
  await languageStore.setLanguage(locale);
};

const getLanguageStats = () => {
  return languageStore.getLanguageStats();
};
</script>

<style scoped>
.i18n-test-view {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  color: #e6e6e6;
}

.test-header {
  margin-bottom: 30px;
  padding: 20px;
  background: #2a2a2a;
  border-radius: 8px;
}

.language-controls {
  margin-top: 20px;
}

.language-buttons {
  margin: 10px 0;
}

.test-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.test-section {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
}

.test-section h2 {
  margin-bottom: 15px;
  color: #4785eb;
  border-bottom: 1px solid #333;
  padding-bottom: 8px;
}

.test-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #333;
}

.test-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #aaa;
  min-width: 120px;
}

.value {
  color: #e6e6e6;
  text-align: right;
  flex: 1;
}

.test-footer {
  background: #1a1a1a;
  padding: 20px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
}

.test-footer pre {
  margin: 10px 0 0 0;
  color: #4785eb;
}
</style>
