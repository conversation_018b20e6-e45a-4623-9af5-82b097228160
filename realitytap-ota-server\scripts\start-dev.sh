#!/bin/bash

# RealityTap OTA 服务器开发启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查Node.js版本
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js 18 或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js 版本过低，需要 18 或更高版本，当前版本: $(node -v)"
        exit 1
    fi
    
    print_success "Node.js 版本检查通过: $(node -v)"
}

# 检查依赖
check_dependencies() {
    print_info "检查项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        print_warning "依赖未安装，正在安装..."
        npm install
    else
        print_success "依赖已安装"
    fi
}

# 初始化存储结构
init_storage() {
    print_info "检查存储结构..."
    
    if [ ! -d "storage" ]; then
        print_warning "存储结构未初始化，正在初始化..."
        npm run init-storage
    else
        print_success "存储结构已存在"
    fi
}

# 检查环境变量
check_env() {
    print_info "检查环境配置..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_warning "环境配置文件不存在，从示例文件创建..."
            cp .env.example .env
            print_success "已创建 .env 文件，请根据需要修改配置"
        else
            print_warning "未找到环境配置文件"
        fi
    else
        print_success "环境配置文件已存在"
    fi
}

# 启动服务器
start_server() {
    print_info "启动 OTA 服务器..."
    print_info "服务器将在 http://localhost:3000 启动"
    print_info "按 Ctrl+C 停止服务器"
    echo ""
    
    # 设置开发环境变量
    export NODE_ENV=development
    export LOG_LEVEL=debug
    
    # 启动服务器
    npm run dev
}

# 主函数
main() {
    echo ""
    print_info "🚀 RealityTap OTA 服务器开发启动脚本"
    echo ""
    
    # 检查当前目录
    if [ ! -f "package.json" ]; then
        print_error "请在 realitytap-ota-server 目录中运行此脚本"
        exit 1
    fi
    
    # 执行检查和初始化
    check_node
    check_dependencies
    check_env
    init_storage
    
    echo ""
    print_success "所有检查完成，准备启动服务器..."
    echo ""
    
    # 启动服务器
    start_server
}

# 处理中断信号
trap 'print_info "正在停止服务器..."; exit 0' INT TERM

# 运行主函数
main "$@"
