#!/usr/bin/env pwsh

<#
.SYNOPSIS
    生产环境构建脚本 - RealityTap Desktop
    Production build script for RealityTap Desktop

.DESCRIPTION
    此脚本用于生产环境构建，包含完整的安全检查和签名流程。
    This script is for production builds with complete security checks and signing process.

.PARAMETER LoadEnvFile
    从 .env.production.signing 文件加载环境变量

.PARAMETER SkipFrontend
    跳过前端构建

.PARAMETER Verbose
    显示详细输出

.EXAMPLE
    .\scripts\build-production.ps1
    .\scripts\build-production.ps1 -LoadEnvFile
    .\scripts\build-production.ps1 -SkipFrontend -Verbose
#>

param(
    [switch]$LoadEnvFile,
    [switch]$SkipFrontend,
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 获取项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot

Write-ColorOutput "🏭 RealityTap Desktop 生产环境构建" "Cyan"
Write-ColorOutput "📁 项目目录: $ProjectRoot" "Gray"

# 加载环境变量
if ($LoadEnvFile) {
    $EnvFile = ".env.production.signing"
    
    if (Test-Path $EnvFile) {
        Write-ColorOutput "`n📋 从 $EnvFile 加载环境变量..." "Yellow"
        
        Get-Content $EnvFile | ForEach-Object {
            if ($_ -match '^([^=]+)=(.*)$' -and -not $_.StartsWith('#')) {
                $name = $matches[1].Trim()
                $value = $matches[2].Trim()
                
                if ($value) {
                    [Environment]::SetEnvironmentVariable($name, $value, 'Process')
                    if ($name -eq "TAURI_SIGNING_PRIVATE_KEY_PASSWORD") {
                        Write-ColorOutput "✅ 设置 $name = [HIDDEN]" "Green"
                    } else {
                        Write-ColorOutput "✅ 设置 $name = $value" "Green"
                    }
                }
            }
        }
    } else {
        Write-ColorOutput "❌ 错误: 未找到 $EnvFile 文件" "Red"
        Write-ColorOutput "请先创建生产环境配置文件" "Yellow"
        exit 1
    }
}

# 检查生产环境签名配置
Write-ColorOutput "`n🔐 检查生产环境签名配置..." "Yellow"

$PrivateKey = $env:TAURI_SIGNING_PRIVATE_KEY
$PrivateKeyPassword = $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD

if (-not $PrivateKey) {
    Write-ColorOutput "❌ 错误: 未设置 TAURI_SIGNING_PRIVATE_KEY 环境变量" "Red"
    Write-ColorOutput "请设置生产环境签名密钥：" "Yellow"
    Write-ColorOutput "  方法1: 使用 -LoadEnvFile 参数从配置文件加载" "Gray"
    Write-ColorOutput "  方法2: 手动设置环境变量" "Gray"
    exit 1
}

if (-not $PrivateKeyPassword) {
    Write-ColorOutput "⚠️  警告: 未设置 TAURI_SIGNING_PRIVATE_KEY_PASSWORD" "Yellow"
    Write-ColorOutput "生产环境密钥通常需要密码保护" "Yellow"
}

# 验证私钥文件存在
if ($PrivateKey -and (Test-Path $PrivateKey)) {
    Write-ColorOutput "✅ 找到私钥文件: $PrivateKey" "Green"
} elseif ($PrivateKey -and $PrivateKey.StartsWith("-----BEGIN")) {
    Write-ColorOutput "✅ 使用私钥内容" "Green"
} else {
    Write-ColorOutput "❌ 错误: 私钥文件不存在或格式错误" "Red"
    exit 1
}

# 安全提醒
Write-ColorOutput "`n⚠️  生产环境安全提醒:" "Yellow"
Write-ColorOutput "1. 确保私钥文件安全存储" "White"
Write-ColorOutput "2. 不要将私钥提交到版本控制" "White"
Write-ColorOutput "3. 定期备份和轮换密钥" "White"
Write-ColorOutput "4. 构建完成后清理临时文件" "White"

$continue = Read-Host "`n是否继续生产环境构建? (y/N)"
if ($continue -ne "y" -and $continue -ne "Y") {
    Write-ColorOutput "构建已取消" "Yellow"
    exit 0
}

try {
    # 1. 构建前端（如果需要）
    if (-not $SkipFrontend) {
        Write-ColorOutput "`n🔨 第一步: 构建前端应用..." "Yellow"
        
        if ($Verbose) {
            npm run build
        } else {
            npm run build | Out-Null
        }
        
        if ($LASTEXITCODE -ne 0) {
            throw "前端构建失败"
        }
        
        Write-ColorOutput "✅ 前端构建完成" "Green"
    } else {
        Write-ColorOutput "`n⏭️  跳过前端构建" "Yellow"
    }
    
    # 2. 构建 Tauri 应用并生成更新文件
    Write-ColorOutput "`n🔨 第二步: 构建 Tauri 应用并生成更新文件..." "Yellow"
    
    if ($Verbose) {
        npm run tauri:build
    } else {
        npm run tauri:build
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Tauri 应用构建失败"
    }
    
    Write-ColorOutput "✅ Tauri 应用构建完成" "Green"
    
    # 3. 验证生成的文件
    Write-ColorOutput "`n📦 验证生产构建结果:" "Green"
    
    $BundleDir = Join-Path $ProjectRoot "src-tauri/target/release/bundle"
    
    if (Test-Path $BundleDir) {
        Write-ColorOutput "📁 构建产物位置: $BundleDir" "Cyan"
        
        # 查找 MSI 文件和签名文件
        $MsiFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.msi"
        $SigFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.sig"
        
        if ($MsiFiles.Count -eq 0) {
            throw "未找到 MSI 安装包文件"
        }
        
        if ($SigFiles.Count -eq 0) {
            throw "未找到签名文件"
        }
        
        foreach ($MsiFile in $MsiFiles) {
            Write-ColorOutput "📦 安装包: $($MsiFile.FullName)" "White"
            
            $SigFile = "$($MsiFile.FullName).sig"
            if (Test-Path $SigFile) {
                Write-ColorOutput "🔐 签名文件: $SigFile" "Green"
                
                # 验证签名文件不为空
                $SigContent = Get-Content $SigFile -Raw
                if ($SigContent.Length -gt 0) {
                    Write-ColorOutput "   签名验证: 通过" "Green"
                } else {
                    throw "签名文件为空"
                }
            } else {
                throw "未找到对应的签名文件: $SigFile"
            }
            
            # 显示文件信息
            $FileSize = [math]::Round($MsiFile.Length / 1MB, 2)
            Write-ColorOutput "   文件大小: $FileSize MB" "Gray"
            Write-ColorOutput "   修改时间: $($MsiFile.LastWriteTime)" "Gray"
        }
        
        Write-ColorOutput "`n✅ 所有文件验证通过" "Green"
    } else {
        throw "未找到构建产物目录: $BundleDir"
    }
    
    # 4. 生产环境部署提醒
    Write-ColorOutput "`n🚀 生产环境部署步骤:" "Yellow"
    Write-ColorOutput "1. 将 MSI 文件和 .sig 签名文件上传到生产服务器" "White"
    Write-ColorOutput "2. 更新 OTA 服务器的版本信息" "White"
    Write-ColorOutput "3. 测试更新功能" "White"
    Write-ColorOutput "4. 通知用户新版本可用" "White"
    
    Write-ColorOutput "`n🎉 生产环境构建完成！" "Green"
    
} catch {
    Write-ColorOutput "`n❌ 生产环境构建失败: $_" "Red"
    exit 1
} finally {
    # 清理敏感信息（可选）
    Write-ColorOutput "`n🧹 清理环境变量..." "Gray"
    $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD = $null
}

Write-ColorOutput "`n✨ 生产环境构建流程完成！" "Green"
