<template>
  <div class="learning-view" @contextmenu.prevent>
    <main class="main-content">
      <div class="nav-tabs">
        <div class="tab" @click="router.push('/')">{{ t('dashboard.tabs.projects') }}</div>
        <div class="tab active" @click="router.push('/learning')">
          {{ t('dashboard.tabs.learning') }}
        </div>
      </div>

      <div class="learning-content">
        <h1>{{ t('learning.title') }}</h1>

        <div class="resources-grid">
          <div class="resource-card">
            <div class="resource-icon">📚</div>
            <div class="resource-content">
              <h3>{{ t('learning.gettingStarted.title') }}</h3>
              <p>
                {{ t('learning.gettingStarted.description') }}
              </p>
              <div class="resource-tags">
                <span class="tag">{{ t('learning.gettingStarted.tags.beginner') }}</span>
                <span class="tag">{{ t('learning.gettingStarted.tags.tutorial') }}</span>
              </div>
            </div>
          </div>

          <div class="resource-card">
            <div class="resource-icon">🔊</div>
            <div class="resource-content">
              <h3>{{ t('learning.audioToHaptics.title') }}</h3>
              <p>
                {{ t('learning.audioToHaptics.description') }}
              </p>
              <div class="resource-tags">
                <span class="tag">{{ t('learning.audioToHaptics.tags.intermediate') }}</span>
                <span class="tag">{{ t('learning.audioToHaptics.tags.tutorial') }}</span>
              </div>
            </div>
          </div>

          <div class="resource-card">
            <div class="resource-icon">🎮</div>
            <div class="resource-content">
              <h3>{{ t('learning.gaming.title') }}</h3>
              <p>
                {{ t('learning.gaming.description') }}
              </p>
              <div class="resource-tags">
                <span class="tag">{{ t('learning.gaming.tags.gaming') }}</span>
                <span class="tag">{{ t('learning.gaming.tags.caseStudy') }}</span>
              </div>
            </div>
          </div>

          <div class="resource-card">
            <div class="resource-icon">📱</div>
            <div class="resource-content">
              <h3>{{ t('learning.uxDesign.title') }}</h3>
              <p>
                {{ t('learning.uxDesign.description') }}
              </p>
              <div class="resource-tags">
                <span class="tag">{{ t('learning.uxDesign.tags.ux') }}</span>
                <span class="tag">{{ t('learning.uxDesign.tags.mobile') }}</span>
              </div>
            </div>
          </div>

          <div class="resource-card">
            <div class="resource-icon">🎓</div>
            <div class="resource-content">
              <h3>{{ t('learning.advanced.title') }}</h3>
              <p>
                {{ t('learning.advanced.description') }}
              </p>
              <div class="resource-tags">
                <span class="tag">{{ t('learning.advanced.tags.advanced') }}</span>
                <span class="tag">{{ t('learning.advanced.tags.tutorial') }}</span>
              </div>
            </div>
          </div>

          <div class="resource-card">
            <div class="resource-icon">📊</div>
            <div class="resource-content">
              <h3>{{ t('learning.research.title') }}</h3>
              <p>
                {{ t('learning.research.description') }}
              </p>
              <div class="resource-tags">
                <span class="tag">{{ t('learning.research.tags.research') }}</span>
                <span class="tag">{{ t('learning.research.tags.academic') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { useI18n } from "@/composables/useI18n";

const router = useRouter();
const { t } = useI18n();
</script>

<style scoped>
.learning-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-y: auto;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.25rem;
  background-color: #1a1a1a;
  border-bottom: 1px solid #333;
}

.title {
  font-size: 1rem;
  font-weight: 500;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #888;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ff3636;
}

.main-content {
  flex: 1;
  padding: 1.25rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.nav-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #333;
}

.tab {
  padding: 0.625rem 1.25rem;
  cursor: pointer;
  font-weight: 500;
  color: #888;
  transition: color 0.2s;
}

.tab:hover {
  color: #e6e6e6;
}

.tab.active {
  color: #fff;
  border-bottom: 2px solid #fff;
}

.learning-content {
  padding: 1rem 0;
}

h1 {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.25rem;
}

.resource-card {
  display: flex;
  background-color: #2a2a2a;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s, background-color 0.2s;
  cursor: pointer;
}

.resource-card:hover {
  background-color: #333;
  transform: translateY(-2px);
}

.resource-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 60px;
  font-size: 1.75rem;
  padding: 1.25rem;
  background-color: rgba(71, 133, 235, 0.1);
}

.resource-content {
  padding: 1.25rem;
  flex: 1;
}

h3 {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 0.625rem;
}

p {
  font-size: 0.875rem;
  color: #aaa;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  background-color: rgba(71, 133, 235, 0.2);
  border-radius: 4px;
  color: #4785eb;
}
</style>
