{"version": "2.0.0", "environment": "production", "last_updated": "2024-01-15T10:00:00Z", "description": "RealityTap 触觉反馈系统生产环境配置 - 事件驱动架构", "filter": {"sampling": {"waveform_sample_rate": 128, "chunk_start_rate": 20, "adaptive_sampling": true, "adaptive_threshold": 0.7, "enable_high_freq_optimization": true, "optimized_waveform_rate": 128, "batch_size": 64}, "threshold": {"amplitude_change_threshold": 8, "sample_value_threshold": 4, "time_interval_threshold": 15}, "deduplication": {"window_size": 512, "timeout_ms": 3000, "content_dedup": true}}, "aggregation": {"strategy": "HighFrequencyOptimized", "time_window": {"waveform_sample_window_ms": 150, "chunk_window_ms": 750, "amplitude_change_window_ms": 300, "sliding_window": false}, "count_threshold": {"waveform_sample_count": 50, "chunk_count": 10, "amplitude_change_count": 5}, "smart_aggregation": {"enabled": true, "load_threshold": 0.6, "memory_threshold": 0.75, "adaptive_window_sizing": true, "min_window_size_ms": 50, "max_window_size_ms": 500, "enable_fast_aggregation": true, "preallocated_buffer_size": 1024, "batch_aggregation_size": 128, "enable_compression": true, "compression_threshold": 200}}, "processor": {"thread_config": {"worker_threads": 2, "batch_size": 100, "queue_size": 10000, "timeout_ms": 1000}, "performance": {"enable_metrics": true, "metrics_interval_ms": 5000, "enable_profiling": false}, "error_handling": {"max_retries": 3, "retry_delay_ms": 1000, "circuit_breaker_threshold": 10, "circuit_breaker_timeout_ms": 30000}}, "logging": {"level": "warn", "enable_file_logging": true, "log_file_path": "logs/realitytap.log", "max_file_size_mb": 10, "max_files": 5, "enable_console_logging": false, "enable_structured_logging": true}, "monitoring": {"enable_performance_monitoring": true, "performance_sample_rate": 0.1, "enable_health_checks": true, "health_check_interval_ms": 60000, "enable_alerts": true, "alert_thresholds": {"throughput_min": 500, "latency_max_us": 2000, "memory_max_mb": 500, "cpu_max_percent": 80, "error_rate_max": 0.05}}, "security": {"enable_input_validation": true, "enable_rate_limiting": true, "rate_limit_requests_per_second": 1000, "enable_audit_logging": true}, "features": {"enable_benchmarking": false, "enable_stability_testing": false, "enable_debug_mode": false, "enable_detailed_events": false, "enable_experimental_features": false}, "hardware": {"default_device_config": {"motor_drive_frequency": 175.0, "sampling_rate": 8000, "max_amplitude": 255, "enable_auto_calibration": true}, "device_timeout_ms": 5000, "max_concurrent_devices": 4, "enable_device_hot_swap": true}, "ui": {"enable_performance_overlay": false, "enable_debug_panel": false, "default_theme": "system", "enable_animations": true, "animation_duration_ms": 200}, "updates": {"enable_auto_update": true, "update_check_interval_hours": 24, "update_channel": "stable", "enable_beta_updates": false}, "telemetry": {"enable_usage_analytics": true, "enable_crash_reporting": true, "enable_performance_telemetry": true, "telemetry_endpoint": "https://api.realitytap.com/telemetry", "anonymize_data": true, "data_retention_days": 30}}