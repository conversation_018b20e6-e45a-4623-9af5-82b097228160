import { config } from '@/config/server.config';
import { ServerHealth } from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import { Request, Response, Router } from 'express';
import fs from 'fs-extra';
import path from 'path';

const router: Router = Router();

// Basic health check
router.get('/', async (req: Request, res: Response) => {
  try {
    const health: ServerHealth = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
    };

    res.json(health);
  } catch (error) {
    logger.error('Health check failed', { error });
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      error: 'Health check failed',
    });
  }
});

// Readiness probe
router.get('/ready', async (req: Request, res: Response) => {
  try {
    // Check if all required directories exist
    const requiredPaths = [
      config.storage.basePath,
      config.storage.releasesPath,
      config.storage.metadataPath,
      config.storage.logsPath,
    ];

    for (const dirPath of requiredPaths) {
      await fs.access(dirPath);
    }

    // Check if metadata files exist
    const metadataFiles = [
      path.join(config.storage.metadataPath, 'versions.json'),
      path.join(config.storage.metadataPath, 'channels.json'),
    ];

    for (const filePath of metadataFiles) {
      await fs.access(filePath);
    }

    res.json({
      status: 'ready',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Readiness check failed', { error });
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: 'Required resources not available',
    });
  }
});

// Liveness probe
router.get('/live', (req: Request, res: Response) => {
  res.json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// Detailed health check
router.get('/detailed', async (req: Request, res: Response) => {
  try {
    const memUsage = process.memoryUsage();

    // 检查存储路径
    const storageChecks = {
      basePath: await fs.pathExists(config.storage.basePath),
      releasesPath: await fs.pathExists(config.storage.releasesPath),
      metadataPath: await fs.pathExists(config.storage.metadataPath),
      logsPath: await fs.pathExists(config.storage.logsPath),
    };

    // 检查磁盘空间（简化版本）
    let diskUsage = null;
    try {
      const stats = await fs.stat(config.storage.basePath);
      diskUsage = {
        available: true,
        lastAccess: stats.atime.toISOString(),
      };
    } catch (error) {
      diskUsage = {
        available: false,
        error: 'Cannot access storage directory',
      };
    }

    // 计算健康状态 - 调整内存阈值为95%，更加宽松
    const allStoragePathsExist = Object.values(storageChecks).every(Boolean);
    const memoryUsagePercentage = Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100);
    const isHealthy = allStoragePathsExist && diskUsage?.available && memoryUsagePercentage < 95;

    // 收集详细的诊断信息
    const diagnostics = {
      storage: {
        allPathsExist: allStoragePathsExist,
        paths: storageChecks,
        basePath: config.storage.basePath,
      },
      disk: diskUsage,
      memory: {
        usagePercentage: memoryUsagePercentage,
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        rss: Math.round(memUsage.rss / 1024 / 1024), // MB
      },
      thresholds: {
        memoryLimit: 95, // 内存使用率阈值
      },
    };

    // 返回符合前端期望的HealthStatus格式
    const healthData = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      checks: {
        storage: allStoragePathsExist,
        metadata: storageChecks.metadataPath,
        logs: storageChecks.logsPath,
        disk: diskUsage?.available || false,
        memory: memoryUsagePercentage < 95,
      },
      diagnostics, // 添加详细诊断信息
    };

    // 记录健康检查结果
    if (!isHealthy) {
      logger.warn('Health check failed', {
        diagnostics,
        operation: 'health_check_failed',
        module: 'health',
        timestamp: new Date().toISOString(),
      });
    }

    res.json({
      success: true,
      data: healthData,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    });
  } catch (error) {
    logger.error('Detailed health check failed', { error });
    res.status(503).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Detailed health check failed',
        details: error instanceof Error ? error.message : String(error),
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    });
  }
});

export { router as healthController };
