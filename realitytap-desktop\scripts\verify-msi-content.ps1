# 验证 MSI 内容的脚本
param(
    [string]$MsiPath
)

if (-not $MsiPath -or -not (Test-Path $MsiPath)) {
    Write-Host "❌ MSI 文件不存在: $MsiPath" -ForegroundColor Red
    exit 1
}

Write-Host "🔍 验证 MSI 内容: $MsiPath" -ForegroundColor Cyan

# 使用 msiexec 提取文件到临时目录
$TempDir = Join-Path $env:TEMP "msi_extract_$(Get-Random)"
New-Item -ItemType Directory -Path $TempDir -Force | Out-Null

try {
    # 提取 MSI 内容
    $ExtractArgs = @("/a", "`"$MsiPath`"", "/qn", "TARGETDIR=`"$TempDir`"")
    Start-Process -FilePath "msiexec.exe" -ArgumentList $ExtractArgs -Wait -NoNewWindow
    
    # 检查 DLL 文件
    $DllFiles = Get-ChildItem $TempDir -Recurse -Filter "*.dll"
    
    if ($DllFiles.Count -gt 0) {
        Write-Host "✅ 发现 $($DllFiles.Count) 个 DLL 文件:" -ForegroundColor Green
        foreach ($dll in $DllFiles) {
            $RelativePath = $dll.FullName.Replace($TempDir, "")
            Write-Host "   📦 $RelativePath" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️  未发现 DLL 文件" -ForegroundColor Yellow
    }
    
} finally {
    # 清理临时目录
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force -ErrorAction SilentlyContinue
    }
}