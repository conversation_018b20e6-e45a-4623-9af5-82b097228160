# Server Configuration
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
# BASE_URL=http://localhost:3000  # 可选：外部访问的完整URL，用于生成完整的下载链接

# Database Configuration
DB_ENABLED=false
DB_TYPE=json
DB_PATH=./storage/database/ota.db
DB_BACKUP_PATH=./storage/backup/database
DB_MAX_CONNECTIONS=10
DB_BUSY_TIMEOUT=30000
DB_ENABLE_WAL=true

# Migration Configuration
MIGRATION_BACKUP_JSON=true
MIGRATION_VALIDATE_DATA=true
MIGRATION_BATCH_SIZE=1000

# Storage Configuration
STORAGE_PATH=./storage
RELEASES_PATH=./storage/releases
METADATA_PATH=./storage/metadata
LOGS_PATH=./storage/logs
TEMP_PATH=./storage/temp

# Security Configuration
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Admin Configuration (Web Management Interface)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
JWT_SECRET=your-super-secret-jwt-key-min-32-chars-long-please-change-this
JWT_EXPIRES_IN=1h
SESSION_TIMEOUT=3600000
ADMIN_MAX_FILE_SIZE=104857600
ADMIN_ALLOWED_FILE_TYPES=.exe,.dmg,.deb,.rpm,.tar.gz,.zip,.msi,.pkg

# Tauri Updater Signature Configuration
# 私钥文件路径（推荐方式）
TAURI_PRIVATE_KEY_PATH=./keys/private.key
# 或者直接使用私钥内容（不推荐，安全性较低）
# TAURI_PRIVATE_KEY=your-private-key-content-here
# 私钥密码（如果私钥有密码保护）
TAURI_KEY_PASSWORD=your-private-key-password

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# File Upload Configuration
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=.exe,.dmg,.deb,.rpm,.tar.gz,.zip

# Download Configuration
ENABLE_RANGE_REQUESTS=true
ENABLE_COMPRESSION=true
DOWNLOAD_TIMEOUT=300000

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Development Configuration (only for development)
ENABLE_DEBUG_ROUTES=false
ENABLE_MOCK_DATA=false
