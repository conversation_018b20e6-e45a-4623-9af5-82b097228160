// 波形图画布生命周期管理组合式函数
// 负责画布初始化、事件监听器管理、资源清理等生命周期相关功能

import { onMounted, onBeforeUnmount, nextTick, type Ref } from "vue";
import type { ScrollbarInst } from "naive-ui";
import { waveformLogger } from "@/utils/logger/logger";

export interface LifecycleEventHandlers {
  // Canvas 事件处理器
  handleCanvasClick: (event: MouseEvent) => void;
  handleCanvasContextMenu: (event: MouseEvent) => void;
  handleMouseLeave: (event: MouseEvent) => void;
  handleMouseHover: (event: MouseEvent) => void;

  // 新的鼠标事件处理器（用于右键菜单增强）
  handleMouseDown: (event: MouseEvent) => void;
  handleMouseMove: (event: MouseEvent) => void;
  handleMouseUp: (event: MouseEvent) => void;

  // 拖拽事件处理器（保留用于兼容性）
  dragHandleMouseDown: (event: MouseEvent) => void;
  dragHandleMouseUp: (event: MouseEvent) => void;
  dragHandleMouseMove: (event: MouseEvent) => void;

  // 键盘事件处理器
  handleKeyDown: (event: KeyboardEvent) => void;
  handleKeyUp: (event: KeyboardEvent) => void;

  // 滚动事件处理器
  handleGraphWheelScroll: (event: WheelEvent) => void;

  // 文档事件处理器
  handleDocumentClick: (event: MouseEvent) => void;
}

export interface LifecycleRefs {
  graphContainer: Ref<HTMLDivElement | null>;
  canvasContainer: Ref<HTMLDivElement | null>;
  waveformCanvas: Ref<HTMLCanvasElement | null>;
  canvasCtx: Ref<CanvasRenderingContext2D | null>;
  yAxisAreaRef: Ref<any | null>;
  horizontalScrollbarRef: Ref<ScrollbarInst | null>;
  longPressTimer: Ref<number | null>;
}

export interface LifecycleConfig {
  updateAndDrawCanvas: () => void;
  updateCanvasPosition: (canvas: HTMLCanvasElement | null) => void;
  contextMenuCleanup: () => void;
  // 新增：Canvas 尺寸调整函数
  resizeCanvasLocal?: (targetWidth: number) => void;
}

export function useWaveformLifecycle(refs: LifecycleRefs, handlers: LifecycleEventHandlers, config: LifecycleConfig) {
  // 存储事件监听器信息，用于清理
  const eventListeners: Array<{
    target: EventTarget;
    type: string;
    listener: EventListenerOrEventListenerObject;
    options?: boolean | AddEventListenerOptions;
  }> = [];

  // Resize 事件防抖定时器
  let resizeDebounceTimer: number | null = null;

  /**
   * 初始化画布上下文和基本设置
   */
  const initializeCanvas = () => {
    if (!refs.graphContainer.value || !refs.waveformCanvas.value) {
      waveformLogger.error("Canvas or container not found on mount.");
      return false;
    }

    // 初始化画布上下文
    refs.canvasCtx.value = refs.waveformCanvas.value.getContext("2d");
    if (!refs.canvasCtx.value) {
      waveformLogger.error("Failed to get canvas context.");
      return false;
    }

    // 执行初始绘制
    config.updateAndDrawCanvas();

    // 确保Canvas位置正确初始化
    nextTick(() => {
      config.updateCanvasPosition(refs.waveformCanvas.value);
    });

    return true;
  };

  /**
   * 添加事件监听器的辅助函数
   */
  const addEventListenerWithTracking = (target: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) => {
    target.addEventListener(type, listener, options);
    eventListeners.push({ target, type, listener, options });
  };

  /**
   * 处理窗体 resize 事件（带防抖优化）
   */
  const handleWindowResize = () => {
    if (!refs.graphContainer.value || !refs.waveformCanvas.value) return;

    // 清除之前的防抖定时器
    if (resizeDebounceTimer) {
      clearTimeout(resizeDebounceTimer);
    }

    // 设置防抖定时器，避免频繁触发
    resizeDebounceTimer = window.setTimeout(() => {
      waveformLogger.debug("窗体 resize 事件触发，更新 Canvas 尺寸");

      // 立即更新 Canvas 高度（从容器获取最新高度）
      const containerHeight = refs.graphContainer.value?.getBoundingClientRect().height || 0;

      // 如果有 resizeCanvasLocal 函数，先调用它更新 Canvas 尺寸
      if (config.resizeCanvasLocal && refs.canvasContainer.value) {
        const containerWidth = refs.canvasContainer.value.getBoundingClientRect().width;
        config.resizeCanvasLocal(containerWidth);
      }

      // 立即更新 Canvas 尺寸和重绘
      nextTick(() => {
        // 触发完整的画布更新和重绘
        config.updateAndDrawCanvas();

        // 确保 Canvas 位置正确
        config.updateCanvasPosition(refs.waveformCanvas.value);

        waveformLogger.debug(`窗体 resize 处理完成，容器高度: ${containerHeight}px`);
      });

      resizeDebounceTimer = null;
    }, 16); // 约 60fps 的响应速度，既保证流畅又避免过度频繁
  };

  /**
   * 添加所有事件监听器
   */
  const addEventListeners = () => {
    const canvas = refs.waveformCanvas.value;
    const container = refs.graphContainer.value;

    if (!canvas || !container) return;

    // 添加 Canvas 事件监听器
    addEventListenerWithTracking(canvas, "click", handlers.handleCanvasClick as EventListener);
    addEventListenerWithTracking(canvas, "mousedown", handlers.handleMouseDown as EventListener);
    addEventListenerWithTracking(canvas, "mouseleave", handlers.handleMouseLeave as EventListener);
    addEventListenerWithTracking(canvas, "mousemove", handlers.handleMouseMove as EventListener);
    addEventListenerWithTracking(canvas, "contextmenu", handlers.handleCanvasContextMenu as EventListener);

    // 添加 Window 事件监听器（用于捕获全局事件）
    addEventListenerWithTracking(window, "mouseup", handlers.handleMouseUp as EventListener);
    addEventListenerWithTracking(window, "keydown", handlers.handleKeyDown as EventListener);
    addEventListenerWithTracking(window, "keyup", handlers.handleKeyUp as EventListener);

    // 【修复】添加窗体 resize 事件监听器，确保窗体大小变化时立即响应
    addEventListenerWithTracking(window, "resize", handleWindowResize as EventListener);

    // 添加 Container 事件监听器
    addEventListenerWithTracking(container, "wheel", handlers.handleGraphWheelScroll as EventListener, {
      passive: false,
    });

    // 添加 Document 事件监听器（用于关闭菜单）
    addEventListenerWithTracking(document, "click", handlers.handleDocumentClick as EventListener);

    waveformLogger.debug(`已添加 ${eventListeners.length} 个事件监听器`);
  };

  /**
   * 移除所有事件监听器
   */
  const removeEventListeners = () => {
    waveformLogger.debug(`开始清理事件监听器，当前活跃监听器数量: ${eventListeners.length}`);

    // 清理所有事件监听器
    eventListeners.forEach(({ target, type, listener, options }) => {
      try {
        target.removeEventListener(type, listener, options);
      } catch (error) {
        waveformLogger.warn(`移除事件监听器失败 ${type}`, error);
      }
    });

    // 清空数组
    eventListeners.length = 0;

    waveformLogger.debug("事件监听器清理完成");
  };

  /**
   * 清理定时器和其他资源
   */
  const cleanupResources = () => {
    // 清理长按定时器
    if (refs.longPressTimer.value) {
      clearTimeout(refs.longPressTimer.value);
      refs.longPressTimer.value = null;
    }

    // 清理 resize 防抖定时器
    if (resizeDebounceTimer) {
      clearTimeout(resizeDebounceTimer);
      resizeDebounceTimer = null;
    }

    // 清理右键菜单相关资源
    config.contextMenuCleanup();
  };

  /**
   * 组件挂载时的初始化逻辑
   */
  onMounted(() => {
    // 初始化画布
    const canvasInitialized = initializeCanvas();

    if (canvasInitialized) {
      // 添加事件监听器
      addEventListeners();
    }
  });

  /**
   * 组件卸载前的清理逻辑
   */
  onBeforeUnmount(() => {
    // 移除事件监听器
    removeEventListeners();

    // 清理资源
    cleanupResources();
  });

  return {
    initializeCanvas,
    addEventListeners,
    removeEventListeners,
    cleanupResources,
    handleWindowResize, // 导出 resize 处理函数，供外部调用
    // 事件监听器相关
    getActiveListenerCount: () => eventListeners.length,
    getListenerStats: () => ({
      total: eventListeners.length,
      byTarget: eventListeners.reduce((acc, { target }) => {
        const targetName = target === window ? "window" : target === document ? "document" : target instanceof Element ? target.tagName.toLowerCase() : "unknown";
        acc[targetName] = (acc[targetName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    }),
  };
}
