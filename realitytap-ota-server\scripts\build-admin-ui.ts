#!/usr/bin/env tsx

import { execSync } from 'child_process';
import fs from 'fs-extra';
import path from 'path';

/**
 * 构建管理界面脚本
 * 自动构建前端项目并集成到后端
 */

const PROJECT_ROOT = path.resolve(__dirname, '..');
const ADMIN_UI_PATH = path.join(PROJECT_ROOT, 'admin-ui');
const ADMIN_UI_DIST = path.join(ADMIN_UI_PATH, 'dist');
const BACKEND_DIST = path.join(PROJECT_ROOT, 'dist');
const BACKEND_ADMIN_UI = path.join(BACKEND_DIST, 'admin-ui');

async function main() {
  try {
    console.log('🚀 开始构建管理界面...');

    // 检查 admin-ui 目录是否存在
    if (!fs.existsSync(ADMIN_UI_PATH)) {
      console.error('❌ admin-ui 目录不存在');
      process.exit(1);
    }

    // 进入 admin-ui 目录
    process.chdir(ADMIN_UI_PATH);

    // 检查是否已安装依赖
    if (!fs.existsSync(path.join(ADMIN_UI_PATH, 'node_modules'))) {
      console.log('📦 安装前端依赖...');

      // 配置 npm 以提高网络稳定性
      try {
        execSync('npm config set registry https://registry.npmjs.org/', { stdio: 'inherit' });
        execSync('npm config set fetch-retries 5', { stdio: 'inherit' });
        execSync('npm config set fetch-retry-mintimeout 20000', { stdio: 'inherit' });
        execSync('npm config set fetch-retry-maxtimeout 120000', { stdio: 'inherit' });
      } catch (configError) {
        console.warn('⚠️ npm 配置设置失败，继续安装...');
      }

      // 尝试安装依赖，如果失败则重试
      let installSuccess = false;
      const maxRetries = 3;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`📦 尝试安装依赖 (第 ${attempt}/${maxRetries} 次)...`);

          // 在 Docker 环境中使用特殊的安装策略
          const isDocker = process.env.DOCKER_BUILD === 'true' || fs.existsSync('/.dockerenv');

          if (isDocker) {
            console.log('🐳 检测到 Docker 环境，使用特殊安装策略...');
            // 在 Docker 中使用 npm ci 并跳过脚本
            execSync('npm ci', { stdio: 'inherit' });

            // 手动安装必要的平台特定依赖
            console.log('🔧 安装平台特定依赖...');
            try {
              // 检测当前平台并安装对应的 rollup 二进制文件
              const platform = process.platform;
              const arch = process.arch;

              console.log(`📋 检测到平台: ${platform}-${arch}`);

              // 安装 rollup 平台特定依赖
              if (platform === 'linux' && arch === 'arm64') {
                execSync('npm install @rollup/rollup-linux-arm64-musl --no-save --ignore-scripts', { stdio: 'inherit' });
              } else if (platform === 'linux' && arch === 'x64') {
                execSync('npm install @rollup/rollup-linux-x64-musl --no-save --ignore-scripts', { stdio: 'inherit' });
              }

              // 手动处理 esbuild
              const esbuildPath = path.join(ADMIN_UI_PATH, 'node_modules', 'esbuild');
              if (fs.existsSync(esbuildPath)) {
                const esbuildBin = path.join(esbuildPath, 'bin', 'esbuild');
                if (fs.existsSync(esbuildBin)) {
                  execSync(`chmod +x ${esbuildBin}`, { stdio: 'inherit' });
                }
              }
            } catch (platformError) {
              console.warn('⚠️ 平台特定依赖安装失败，但继续构建...');
            }
          } else {
            // 非 Docker 环境使用标准安装
            execSync('npm install', { stdio: 'inherit' });
          }

          installSuccess = true;
          break;
        } catch (installError: any) {
          console.error(`❌ 第 ${attempt} 次安装失败:`, installError.message);
          if (attempt === maxRetries) {
            throw new Error(`依赖安装失败，已重试 ${maxRetries} 次`);
          }
          console.log('⏳ 等待 5 秒后重试...');
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
      }

      if (!installSuccess) {
        throw new Error('依赖安装失败');
      }
    }

    // 构建前端项目
    console.log('🔨 构建前端项目...');
    execSync('npm run build', { stdio: 'inherit' });

    // 检查构建结果
    if (!fs.existsSync(ADMIN_UI_DIST)) {
      console.error('❌ 前端构建失败，dist 目录不存在');
      process.exit(1);
    }

    // 确保后端 dist 目录存在
    fs.ensureDirSync(BACKEND_DIST);

    // 清理旧的管理界面文件
    if (fs.existsSync(BACKEND_ADMIN_UI)) {
      console.log('🧹 清理旧的管理界面文件...');
      fs.removeSync(BACKEND_ADMIN_UI);
    }

    // 复制构建结果到后端
    console.log('📁 复制构建结果到后端...');
    fs.copySync(ADMIN_UI_DIST, BACKEND_ADMIN_UI);

    // 验证复制结果
    const indexPath = path.join(BACKEND_ADMIN_UI, 'index.html');
    if (!fs.existsSync(indexPath)) {
      console.error('❌ 复制失败，index.html 不存在');
      process.exit(1);
    }

    console.log('✅ 管理界面构建完成！');
    console.log(`📂 构建文件位置: ${BACKEND_ADMIN_UI}`);

    // 显示构建统计
    const stats = getDirectoryStats(BACKEND_ADMIN_UI);
    console.log(`📊 构建统计: ${stats.files} 个文件，总大小 ${formatBytes(stats.size)}`);

  } catch (error: any) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
}

/**
 * 获取目录统计信息
 */
function getDirectoryStats(dirPath: string): { files: number; size: number } {
  let files = 0;
  let size = 0;

  function traverse(currentPath: string) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const itemPath = path.join(currentPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        traverse(itemPath);
      } else {
        files++;
        size += stat.size;
      }
    }
  }

  traverse(dirPath);
  return { files, size };
}

/**
 * 格式化字节数
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 运行脚本
if (require.main === module) {
  main();
}