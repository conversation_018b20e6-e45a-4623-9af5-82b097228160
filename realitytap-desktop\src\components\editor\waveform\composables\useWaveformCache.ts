import type { RenderableEvent } from "@/types/haptic-editor";
import { ref, type Ref } from "vue";
import { DEBUG_EXPERIMENTS } from "../config/waveform-constants";
import { waveformLogger } from "@/utils/logger/logger";

// 绘制状态缓存接口
export interface DrawState {
  lastEventCount: number;
  lastSelectedEventId: string | null;
  lastEffectiveDuration: number;
  lastScrollLeft: number;
}

// 缓存配置接口
export interface WaveformCacheConfig {
  // Store 状态
  waveformStore: {
    selectedEventId: string | null;
    isAdjustingProperties: boolean;
  };

  // 事件和持续时间获取函数
  getEvents: () => RenderableEvent[] | undefined;
  getEffectiveDuration: () => number;

  // 滚动状态
  scrollLeftValue: Ref<number>;

  // 拖拽状态
  isDragging: Ref<boolean>;

  // 事件缓存清理函数（来自 useWaveformEventDrawing）
  continuousEventCacheClear?: () => void;
}

/**
 * 波形图缓存管理 Composable
 * 负责管理绘制状态缓存、重绘检查逻辑等
 */
export function useWaveformCache(config: WaveformCacheConfig) {
  const { waveformStore, getEvents, getEffectiveDuration, scrollLeftValue, isDragging, continuousEventCacheClear } = config;

  // 绘制状态缓存，避免不必要的重绘
  const drawState = ref<DrawState>({
    lastEventCount: 0,
    lastSelectedEventId: null,
    lastEffectiveDuration: 0,
    lastScrollLeft: 0,
  });

  /**
   * 检查是否需要重绘
   * @returns 是否需要重绘
   */
  const shouldRedraw = (): boolean => {
    if (DEBUG_EXPERIMENTS.DISABLE_DRAW_CACHE) {
      // 实验2: 禁用缓存检查，总是返回true强制重绘
      waveformLogger.debug("🔍 实验2: 禁用缓存检查，强制重绘");
      return true;
    }

    // 原始缓存检查逻辑
    // 拖动时总是需要重绘，避免闪烁
    if (isDragging.value) {
      return true;
    }

    const events = getEvents();
    const currentEventCount = events?.length || 0;
    const currentSelectedEventId = waveformStore.selectedEventId;
    const currentEffectiveDuration = getEffectiveDuration();
    const currentScrollLeft = scrollLeftValue.value;

    const needsRedraw =
      drawState.value.lastEventCount !== currentEventCount ||
      drawState.value.lastSelectedEventId !== currentSelectedEventId ||
      drawState.value.lastEffectiveDuration !== currentEffectiveDuration ||
      Math.abs(drawState.value.lastScrollLeft - currentScrollLeft) > 10; // 滚动距离阈值

    if (needsRedraw) {
      drawState.value.lastEventCount = currentEventCount;
      drawState.value.lastSelectedEventId = currentSelectedEventId;
      drawState.value.lastEffectiveDuration = currentEffectiveDuration;
      drawState.value.lastScrollLeft = currentScrollLeft;
    }

    return needsRedraw;
  };

  /**
   * 重置绘制状态缓存的辅助函数
   */
  const resetDrawState = () => {
    drawState.value.lastEventCount = 0;
    drawState.value.lastSelectedEventId = null;
    drawState.value.lastEffectiveDuration = 0;
    drawState.value.lastScrollLeft = 0;

    // 清理坐标缓存 - 使用 composable 中的缓存清理函数
    if (continuousEventCacheClear) {
      continuousEventCacheClear();
    }
  };

  /**
   * 更新绘制状态缓存（手动更新）
   */
  const updateDrawState = () => {
    const events = getEvents();
    drawState.value.lastEventCount = events?.length || 0;
    drawState.value.lastSelectedEventId = waveformStore.selectedEventId;
    drawState.value.lastEffectiveDuration = getEffectiveDuration();
    drawState.value.lastScrollLeft = scrollLeftValue.value;
  };

  /**
   * 检查特定状态是否发生变化
   */
  const hasStateChanged = (type: keyof DrawState): boolean => {
    switch (type) {
      case "lastEventCount":
        return drawState.value.lastEventCount !== (getEvents()?.length || 0);
      case "lastSelectedEventId":
        return drawState.value.lastSelectedEventId !== waveformStore.selectedEventId;
      case "lastEffectiveDuration":
        return drawState.value.lastEffectiveDuration !== getEffectiveDuration();
      case "lastScrollLeft":
        return Math.abs(drawState.value.lastScrollLeft - scrollLeftValue.value) > 10;
      default:
        return false;
    }
  };

  return {
    // 状态
    drawState: drawState.value,

    // 方法
    shouldRedraw,
    resetDrawState,
    updateDrawState,
    hasStateChanged,
  };
}
