/**
 * 波形数据访问统一接口
 * 提供统一的数据访问方式，减少对props.events的直接依赖
 */

import type { RenderableEvent } from "@/types/haptic-editor";
import { waveformLogger } from "@/utils/logger/logger";

/**
 * 波形数据访问接口
 */
export interface WaveformDataAccess {
  // 基础数据访问
  getEvents: () => RenderableEvent[];
  getSelectedEvent: () => RenderableEvent | null;
  getSelectedEventId: () => string | null;
  getEventById: (id: string) => RenderableEvent | null;

  // 事件查询
  getEventsInTimeRange: (startTime: number, endTime: number) => RenderableEvent[];
  getTransientEvents: () => RenderableEvent[];
  getContinuousEvents: () => RenderableEvent[];

  // 事件统计
  getEventsCount: () => number;
  getTotalDuration: () => number;

  // 数据验证
  isEventSelected: (eventId: string) => boolean;
  hasEvents: () => boolean;
}

/**
 * 创建波形数据访问实例
 */
export function useWaveformDataAccess(waveformStore: any): WaveformDataAccess {
  return {
    // 基础数据访问
    getEvents: () => waveformStore.events || [],

    getSelectedEvent: () => {
      const selectedId = waveformStore.selectedEventId;
      if (!selectedId) return null;
      return waveformStore.events?.find((e: RenderableEvent) => e.id === selectedId) || null;
    },

    getSelectedEventId: () => waveformStore.selectedEventId || null,

    getEventById: (id: string) => {
      return waveformStore.events?.find((e: RenderableEvent) => e.id === id) || null;
    },

    // 事件查询
    getEventsInTimeRange: (startTime: number, endTime: number) => {
      const events = waveformStore.events || [];
      return events.filter((event: RenderableEvent) => {
        const eventEndTime = event.startTime + (event.type === "continuous" ? event.duration : 0);
        return event.startTime <= endTime && eventEndTime >= startTime;
      });
    },

    getTransientEvents: () => {
      const events = waveformStore.events || [];
      return events.filter((event: RenderableEvent) => event.type === "transient");
    },

    getContinuousEvents: () => {
      const events = waveformStore.events || [];
      return events.filter((event: RenderableEvent) => event.type === "continuous");
    },

    // 事件统计
    getEventsCount: () => {
      return waveformStore.events?.length || 0;
    },

    getTotalDuration: () => {
      const events = waveformStore.events || [];
      if (events.length === 0) return 0;

      let maxEndTime = 0;
      events.forEach((event: RenderableEvent) => {
        const endTime = event.startTime + (event.type === "continuous" ? event.duration : 0);
        maxEndTime = Math.max(maxEndTime, endTime);
      });

      return maxEndTime;
    },

    // 数据验证
    isEventSelected: (eventId: string) => {
      return waveformStore.selectedEventId === eventId;
    },

    hasEvents: () => {
      return (waveformStore.events?.length || 0) > 0;
    },
  };
}

/**
 * 数据访问缓存装饰器
 * 为频繁访问的数据提供缓存支持
 */
export function useWaveformDataAccessWithCache(waveformStore: any): WaveformDataAccess {
  const baseAccess = useWaveformDataAccess(waveformStore);

  // 缓存选中事件，避免重复查找
  let cachedSelectedEvent: RenderableEvent | null = null;
  let cachedSelectedEventId: string | null = null;

  return {
    ...baseAccess,

    getSelectedEvent: () => {
      const currentSelectedId = waveformStore.selectedEventId;

      // 如果选中ID没有变化，返回缓存的事件
      if (currentSelectedId === cachedSelectedEventId && cachedSelectedEvent) {
        return cachedSelectedEvent;
      }

      // 更新缓存
      cachedSelectedEventId = currentSelectedId;
      cachedSelectedEvent = baseAccess.getSelectedEvent();

      return cachedSelectedEvent;
    },
  };
}

/**
 * 数据访问日志装饰器
 * 用于调试和性能监控
 */
export function useWaveformDataAccessWithLogging(waveformStore: any, enableLogging: boolean = false): WaveformDataAccess {
  const baseAccess = useWaveformDataAccess(waveformStore);

  if (!enableLogging) {
    return baseAccess;
  }

  const logAccess = (method: string, result: any) => {
    waveformLogger.debug(`${method}:`, result);
  };

  return {
    getEvents: () => {
      const result = baseAccess.getEvents();
      logAccess("getEvents", `${result.length} events`);
      return result;
    },

    getSelectedEvent: () => {
      const result = baseAccess.getSelectedEvent();
      logAccess("getSelectedEvent", result?.id || "null");
      return result;
    },

    getSelectedEventId: () => {
      const result = baseAccess.getSelectedEventId();
      logAccess("getSelectedEventId", result);
      return result;
    },

    getEventById: (id: string) => {
      const result = baseAccess.getEventById(id);
      logAccess("getEventById", `${id} -> ${result?.id || "null"}`);
      return result;
    },

    getEventsInTimeRange: (startTime: number, endTime: number) => {
      const result = baseAccess.getEventsInTimeRange(startTime, endTime);
      logAccess("getEventsInTimeRange", `${startTime}-${endTime} -> ${result.length} events`);
      return result;
    },

    getTransientEvents: () => {
      const result = baseAccess.getTransientEvents();
      logAccess("getTransientEvents", `${result.length} events`);
      return result;
    },

    getContinuousEvents: () => {
      const result = baseAccess.getContinuousEvents();
      logAccess("getContinuousEvents", `${result.length} events`);
      return result;
    },

    getEventsCount: () => {
      const result = baseAccess.getEventsCount();
      logAccess("getEventsCount", result);
      return result;
    },

    getTotalDuration: () => {
      const result = baseAccess.getTotalDuration();
      logAccess("getTotalDuration", result);
      return result;
    },

    isEventSelected: (eventId: string) => {
      const result = baseAccess.isEventSelected(eventId);
      logAccess("isEventSelected", `${eventId} -> ${result}`);
      return result;
    },

    hasEvents: () => {
      const result = baseAccess.hasEvents();
      logAccess("hasEvents", result);
      return result;
    },
  };
}
