/**
 * 新的更新管理组合函数
 * 使用 tauri-plugin-updater 提供标准化的更新功能
 */

import { ref, computed, readonly } from 'vue'
import { logger, LogModule } from '@/utils/logger/logger'

// 类型定义
export interface UpdateInfo {
  version: string
  date?: string
  body?: string
  signature?: string
  force_update?: boolean
  file_size?: number // 添加文件大小字段
}

export interface DownloadProgress {
  downloaded: number
  total: number
  percentage: number
}

export type UpdateStatus = 
  | 'idle' 
  | 'checking' 
  | 'available' 
  | 'not_available' 
  | 'downloading' 
  | 'downloaded'
  | 'installing' 
  | 'completed' 
  | 'failed'

/**
 * 更新管理 Composable
 */
export function useUpdater() {

  // 状态管理
  const isChecking = ref(false)
  const isDownloading = ref(false)
  const isInstalling = ref(false)
  const isDownloaded = ref(false)
  const updateInfo = ref<UpdateInfo | null>(null)
  const downloadProgress = ref<DownloadProgress>({ downloaded: 0, total: 0, percentage: 0 })
  const error = ref<string | null>(null)
  const status = ref<UpdateStatus>('idle')
  const isForceUpdate = ref(false)

  // 内部 update 对象
  let updateObj: any = null
  
  // 计算属性
  const hasUpdate = computed(() => updateInfo.value !== null)
  const canDownload = computed(() => hasUpdate.value && !isDownloading.value && !isInstalling.value && !isDownloaded.value)
  const canInstall = computed(() => hasUpdate.value && isDownloaded.value && !isInstalling.value)
  const isProcessing = computed(() => isChecking.value || isDownloading.value || isInstalling.value)



  /**
   * 检查更新
   */
  const checkForUpdates = async (silent = false): Promise<UpdateInfo | null> => {
    if (isChecking.value) return null

    isChecking.value = true
    status.value = 'checking'
    error.value = null

    try {
      if (!silent) {
        logger.info(LogModule.GENERAL, '检查更新中');

        // 记录调试信息到日志文件
        try {
          const { invoke } = await import("@tauri-apps/api/core");
          const debugConfig = await invoke("get_debug_mode") as { enabled: boolean; logOtaOperations: boolean };
          if (debugConfig.enabled && debugConfig.logOtaOperations) {
            logger.debug(LogModule.GENERAL, '调试模式已启用，记录详细OTA操作日志');
          }
        } catch (debugErr) {
          logger.warn(LogModule.GENERAL, '获取调试配置失败', debugErr);
        }
      }

      // 动态导入 tauri-plugin-updater
      const { check } = await import('@tauri-apps/plugin-updater')

      const update = await check()

      if (update) {
        // 检查是否为强制更新（从服务器响应的 rawJson 中获取）
        const forceUpdate = (update as any).rawJson?.force_update || false
        // 获取文件大小（从服务器响应的 rawJson 中获取）
        const fileSize = (update as any).rawJson?.file_size || 0

        updateInfo.value = {
          version: update.version,
          date: update.date,
          body: update.body,
          force_update: forceUpdate,
          file_size: fileSize,
        }
        status.value = 'available'
        updateObj = update
        isDownloaded.value = false
        isForceUpdate.value = forceUpdate

        if (!silent) {
          logger.info(LogModule.GENERAL, '发现新版本', {
            currentVersion: await getCurrentVersion(),
            newVersion: update.version,
            releaseDate: update.date,
            releaseNotes: update.body,
            forceUpdate: forceUpdate
          });

          // 输出完整的更新检查响应用于调试
          logger.debug(LogModule.GENERAL, '更新检查响应', {
            version: update.version,
            date: update.date,
            body: update.body,
            force_update: forceUpdate
          });

          // 输出原始响应数据用于调试
          logger.debug(LogModule.GENERAL, '原始响应数据 (rawJson)', (update as any).rawJson);
        }

        return updateInfo.value
      } else {
        status.value = 'not_available'
        updateObj = null
        isDownloaded.value = false
        if (!silent) {
          logger.info(LogModule.GENERAL, '当前已是最新版本');
        }
        return null
      }
    } catch (err: any) {
      // 详细的错误处理
      let userFriendlyMessage = '检查更新时发生错误，请稍后重试'
      let troubleshootingTips: string[] = []

      if (err?.message) {
        if (err.message.includes('Invalid encoding in minisign data')) {
          userFriendlyMessage = '更新配置有误，请联系技术支持'
          troubleshootingTips = [
            '检查 tauri.conf.json 中的 pubkey 配置',
            '确认公钥格式正确',
            '联系管理员重新配置签名'
          ]
          logger.error(LogModule.GENERAL, '更新配置错误详情', {
            error: err.message,
            suggestion: '请检查 tauri.conf.json 中的 pubkey 配置是否正确'
          });
        } else if (err.message.includes('network') || err.message.includes('fetch')) {
          userFriendlyMessage = '网络连接失败，请检查网络设置后重试'
          troubleshootingTips = [
            '检查网络连接是否正常',
            '确认防火墙设置允许应用访问网络',
            '尝试切换网络环境'
          ]
          logger.error(LogModule.GENERAL, '网络错误', err.message);
        } else if (err.message.includes('endpoint')) {
          userFriendlyMessage = '更新服务器暂时不可用，请稍后重试'
          troubleshootingTips = [
            '检查更新服务器是否正常运行',
            '确认服务器地址配置正确',
            '联系管理员检查服务器状态'
          ]
          logger.error(LogModule.GENERAL, '端点错误', err.message);
        } else {
          userFriendlyMessage = `检查更新失败: ${err.message}`
          troubleshootingTips = [
            '重启应用后重试',
            '检查应用权限设置',
            '联系技术支持获取帮助'
          ]
        }
      }

      error.value = userFriendlyMessage
      status.value = 'failed'
      updateObj = null
      isDownloaded.value = false

      // 记录详细错误信息到日志
      logger.error(LogModule.GENERAL, '更新检查失败', {
        error: err,
        message: err?.message,
        stack: err?.stack,
        userMessage: userFriendlyMessage,
        troubleshooting: troubleshootingTips
      });

      // 如果启用了调试模式，记录更多信息
      try {
        const { invoke } = await import("@tauri-apps/api/core");
        const debugConfig = await invoke("get_debug_mode") as { enabled: boolean; logOtaOperations: boolean };
        if (debugConfig.enabled && debugConfig.logOtaOperations) {
          logger.error(LogModule.GENERAL, '调试信息 - 更新检查失败详情', {
            timestamp: new Date().toISOString(),
            errorType: err.constructor.name,
            errorCode: err.code,
            errorStack: err.stack,
            troubleshootingTips
          });
        }
      } catch (debugErr) {
        logger.warn(LogModule.GENERAL, '记录调试信息失败', debugErr);
      }

      return null
    } finally {
      isChecking.value = false
    }
  }
  
  /**
   * 仅下载更新
   */
  const downloadUpdate = async (): Promise<boolean> => {
    if (!updateInfo.value || isDownloading.value || isDownloaded.value) return false
    isDownloading.value = true
    status.value = 'downloading'
    error.value = null

    try {
      if (!updateObj) {
        logger.info(LogModule.GENERAL, '重新检查更新信息');

        // 重新检查更新以获取 updateObj
        const result = await checkForUpdates(true)
        if (!result || !updateObj) {
          throw new Error('无法获取更新信息')
        }
      }

      if (!updateObj) {
        throw new Error('无法获取更新信息')
      }

      // 记录下载开始到后端日志
      try {
        const { invoke } = await import("@tauri-apps/api/core");
        await invoke("log_ota_operation", {
          operation: "DOWNLOAD_START",
          message: `开始下载更新版本 ${updateObj.version}`,
          details: {
            version: updateObj.version,
            date: updateObj.date,
            timestamp: new Date().toISOString()
          }
        });
      } catch (logErr) {
        logger.warn(LogModule.GENERAL, '记录下载开始日志失败', logErr);
      }

      logger.info(LogModule.GENERAL, '[DOWNLOAD] 开始下载更新', {
        version: updateObj.version,
        date: updateObj.date,
        timestamp: new Date().toISOString()
      });

      let lastLoggedPercent = 0;
      let lastProgressUpdate = Date.now();
      let totalDownloaded = 0; // 累积下载字节数
      const downloadStartTime = Date.now();

      await updateObj.download((progress: any) => {
        logger.debug(LogModule.GENERAL, '[DOWNLOAD] 原始进度数据', progress);

        // 处理不同格式的进度数据
        let chunkSize = 0;
        let total = 0;

        if (progress.event === 'Progress' && progress.data) {
          // 新格式：事件驱动的进度数据（分块下载）
          const data = progress.data;
          chunkSize = data.chunkLength || 0;
          total = data.contentLength || updateInfo.value?.file_size || 0;
          // 累积下载字节数
          totalDownloaded += chunkSize;
        } else if (typeof progress.downloaded === 'number') {
          // 旧格式：直接的进度数据（累积值）
          totalDownloaded = progress.downloaded;
          total = progress.total || updateInfo.value?.file_size || 0;
        } else {
          // 未知格式，尝试从服务器获取的文件大小
          total = updateInfo.value?.file_size || 0;
        }

        const currentPercent = total > 0 ? Math.round((totalDownloaded / total) * 100) : 0;

        downloadProgress.value = {
          downloaded: totalDownloaded,
          total: total,
          percentage: currentPercent
        }

        // 更频繁的进度日志输出（每次都输出，但限制频率）
        const now = Date.now();
        const timeSinceLastUpdate = now - lastProgressUpdate;

        if (timeSinceLastUpdate >= 100) { // 每100ms输出一次
          lastProgressUpdate = now;
          logger.debug(LogModule.GENERAL, '[DOWNLOAD] 实时进度', {
            percentage: currentPercent,
            downloaded: totalDownloaded,
            total: total,
            chunkSize: chunkSize
          });
        }

        // 输出详细下载进度日志
        if (totalDownloaded && total > 0) {
          const percent = currentPercent;
          const downloadedMB = (totalDownloaded / 1024 / 1024).toFixed(2);
          const totalMB = (total / 1024 / 1024).toFixed(2);

          // 每5%输出一次详细日志，提高进度显示频率
          const shouldLog = percent % 5 === 0 && percent !== lastLoggedPercent;

          if (shouldLog) {
            lastLoggedPercent = percent;
            const elapsedSeconds = Math.round((now - downloadStartTime) / 1000);
            const speed = totalDownloaded / (elapsedSeconds || 1) / 1024; // KB/s

            logger.info(LogModule.GENERAL, '[DOWNLOAD] 下载进度', {
              percentage: percent,
              downloadedMB,
              totalMB,
              speedKBps: speed.toFixed(1),
              elapsedSeconds
            });

            // 记录进度到后端日志（异步执行，不阻塞下载）
            import("@tauri-apps/api/core").then(({ invoke }) => {
              invoke("log_ota_operation", {
                operation: "DOWNLOAD_PROGRESS",
                message: `下载进度: ${percent}%`,
                details: {
                  percentage: percent,
                  downloaded: totalDownloaded,
                  total: total,
                  downloadedMB,
                  totalMB,
                  speedKBps: speed.toFixed(1),
                  elapsedSeconds
                }
              }).catch(err => logger.warn(LogModule.GENERAL, '记录下载进度日志失败', err));
            }).catch(() => {
              // 忽略日志错误，不影响下载
            });
          }
        }
      })

      const downloadDuration = Math.round((Date.now() - downloadStartTime) / 1000);

      isDownloaded.value = true
      status.value = 'downloaded'

      // 记录下载完成到后端日志
      try {
        const { invoke } = await import("@tauri-apps/api/core");
        await invoke("log_ota_operation", {
          operation: "DOWNLOAD_COMPLETE",
          message: `更新下载完成，版本: ${updateObj.version}`,
          details: {
            version: updateObj.version,
            duration: downloadDuration,
            timestamp: new Date().toISOString()
          }
        });
      } catch (logErr) {
        logger.warn(LogModule.GENERAL, '记录下载完成日志失败', logErr);
      }

      logger.info(LogModule.GENERAL, '[DOWNLOAD] 更新下载完成', {
        version: updateObj.version,
        duration: `${downloadDuration}秒`,
        timestamp: new Date().toISOString()
      });
      return true
    } catch (err: any) {
      // 详细的错误处理
      let errorMessage = '更新下载失败'
      let errorCategory = 'UNKNOWN_ERROR'

      if (err?.message) {
        if (err.message.includes('Invalid encoding in minisign data')) {
          errorMessage = '更新签名验证失败: 签名数据编码无效'
          errorCategory = 'SIGNATURE_ENCODING_ERROR'
          logger.error(LogModule.GENERAL, '[DOWNLOAD] 签名验证错误详情', {
            error: err.message,
            suggestion: '请检查公钥配置是否正确，或联系管理员重新生成签名'
          });
        } else if (err.message.includes('signature')) {
          errorMessage = '更新签名验证失败'
          errorCategory = 'SIGNATURE_VERIFICATION_ERROR'
          logger.error(LogModule.GENERAL, '[DOWNLOAD] 签名验证失败', err.message);
        } else if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = '网络连接失败'
          errorCategory = 'NETWORK_ERROR'
          logger.error(LogModule.GENERAL, '[DOWNLOAD] 网络错误', err.message);
        } else {
          errorMessage = `更新下载失败: ${err.message}`
          errorCategory = 'DOWNLOAD_ERROR'
        }
      }

      error.value = errorMessage
      status.value = 'failed'
      isDownloaded.value = false

      // 记录下载失败到后端日志
      try {
        const { invoke } = await import("@tauri-apps/api/core");
        await invoke("log_ota_operation", {
          operation: "DOWNLOAD_FAILED",
          message: errorMessage,
          details: {
            errorCategory,
            errorMessage: err?.message,
            errorStack: err?.stack,
            errorName: err?.name,
            errorCode: err?.code,
            fullError: JSON.stringify(err, Object.getOwnPropertyNames(err)),
            timestamp: new Date().toISOString()
          }
        });
      } catch (logErr) {
        logger.warn(LogModule.GENERAL, '记录下载失败日志失败', logErr);
      }

      logger.error(LogModule.GENERAL, '[DOWNLOAD] 更新下载失败', {
        category: errorCategory,
        error: err,
        message: err?.message,
        stack: err?.stack,
        timestamp: new Date().toISOString()
      });

      return false
    } finally {
      isDownloading.value = false
    }
  }
  
  /**
   * 仅安装更新（需已下载）
   */
  const installUpdate = async (): Promise<boolean> => {
    if (!updateObj || !isDownloaded.value || isInstalling.value) return false
    isInstalling.value = true
    status.value = 'installing'
    error.value = null

    try {
      // 记录安装开始到后端日志
      try {
        const { invoke } = await import("@tauri-apps/api/core");
        await invoke("log_ota_operation", {
          operation: "INSTALL_START",
          message: `开始安装更新版本 ${updateObj.version}`,
          details: {
            version: updateObj.version,
            timestamp: new Date().toISOString()
          }
        });
      } catch (logErr) {
        logger.warn(LogModule.GENERAL, '记录安装开始日志失败', logErr);
      }

      logger.info(LogModule.GENERAL, '[INSTALL] 开始安装更新', {
        version: updateObj.version,
        timestamp: new Date().toISOString()
      });

      await updateObj.install()

      // 记录安装完成到后端日志
      try {
        const { invoke } = await import("@tauri-apps/api/core");
        await invoke("log_ota_operation", {
          operation: "INSTALL_COMPLETE",
          message: `更新安装完成，版本: ${updateObj.version}`,
          details: {
            version: updateObj.version,
            timestamp: new Date().toISOString()
          }
        });
      } catch (logErr) {
        logger.warn(LogModule.GENERAL, '记录安装完成日志失败', logErr);
      }

      status.value = 'completed'
      logger.info(LogModule.GENERAL, '[INSTALL] 更新安装完成，应用即将重启', {
        version: updateObj.version,
        timestamp: new Date().toISOString()
      });
      return true
    } catch (err: any) {
      const errorMessage = err?.message || 'Update installation failed'
      error.value = errorMessage
      status.value = 'failed'

      // 记录安装失败到后端日志
      try {
        const { invoke } = await import("@tauri-apps/api/core");
        await invoke("log_ota_operation", {
          operation: "INSTALL_FAILED",
          message: `更新安装失败: ${errorMessage}`,
          details: {
            errorMessage: err?.message,
            errorStack: err?.stack,
            timestamp: new Date().toISOString()
          }
        });
      } catch (logErr) {
        logger.warn(LogModule.GENERAL, '记录安装失败日志失败', logErr);
      }

      logger.error(LogModule.GENERAL, '[INSTALL] 更新安装失败', {
        error: err,
        message: err?.message,
        stack: err?.stack,
        timestamp: new Date().toISOString()
      });
      return false
    } finally {
      isInstalling.value = false
      // 安装后重置下载状态
      isDownloaded.value = false
      updateObj = null
    }
  }
  
  /**
   * 下载并安装（兼容旧逻辑）
   */
  const downloadAndInstall = async (): Promise<boolean> => {
    const downloaded = await downloadUpdate()
    if (!downloaded) return false
    return await installUpdate()
  }
  
  /**
   * 重置状态
   */
  const reset = () => {
    updateInfo.value = null
    downloadProgress.value = { downloaded: 0, total: 0, percentage: 0 }
    error.value = null
    status.value = 'idle'
    isDownloaded.value = false
    updateObj = null
  }

  /**
   * 清除错误信息（保持其他状态）
   */
  const clearError = () => {
    error.value = null
  }
  
  /**
   * 获取当前应用版本
   */
  const getCurrentVersion = async (): Promise<string> => {
    try {
      const { getVersion } = await import('@tauri-apps/api/app')
      return await getVersion()
    } catch (err) {
      logger.error(LogModule.GENERAL, '获取应用版本失败', err);
      return '1.0.0'
    }
  }
  
  /**
   * 外部设置 updateInfo（用于弹窗 props 同步）
   */
  const setUpdateInfo = (info: UpdateInfo) => {
    updateInfo.value = info;
    // 如果当前状态是 'downloaded'，保持该状态，否则设置为 'available'
    if (status.value !== 'downloaded') {
      status.value = 'available';
      isDownloaded.value = false;
    }
    // 设置强制更新状态
    isForceUpdate.value = info.force_update || false;
    // 如果已经有相同版本的更新且已下载，保持下载状态
    // 这样可以避免在"稍后提醒"后重新打开时丢失下载状态
  };

  /**
   * 退出应用程序
   */
  const exitApplication = async (): Promise<void> => {
    try {
      logger.info(LogModule.GENERAL, '用户选择退出应用程序');
      const { invoke } = await import("@tauri-apps/api/core");
      await invoke("exit_application_gracefully");
    } catch (err) {
      logger.error(LogModule.GENERAL, '退出应用程序失败', err);
      // 如果优雅退出失败，尝试强制退出
      try {
        // 使用 window.close() 作为备选方案
        window.close();
      } catch (forceExitErr) {
        logger.error(LogModule.GENERAL, '强制退出应用程序失败', forceExitErr);
      }
    }
  };
  
  return {
    // 状态
    isChecking: readonly(isChecking),
    isDownloading: readonly(isDownloading),
    isInstalling: readonly(isInstalling),
    isDownloaded: readonly(isDownloaded),
    updateInfo: readonly(updateInfo),
    downloadProgress: readonly(downloadProgress),
    error: readonly(error),
    status: readonly(status),
    isForceUpdate: readonly(isForceUpdate),

    // 计算属性
    hasUpdate,
    canDownload,
    canInstall,
    isProcessing,

    // 方法
    checkForUpdates,
    downloadUpdate,
    installUpdate,
    downloadAndInstall,
    reset,
    clearError,
    getCurrentVersion,
    setUpdateInfo,
    exitApplication,
  }
}
