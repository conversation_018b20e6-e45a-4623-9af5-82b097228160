// 设备管理相关常量定义

import { DeviceType, DeviceStatus } from "@/types/device-types";
import { TransmissionType, TransmissionPriority } from "@/types/device-transmission";

// === 设备类型相关常量 ===
// 注意：这些标签现在通过国际化系统提供，这里保留作为类型键
export const DEVICE_TYPE_KEYS: Record<DeviceType, string> = {
  [DeviceType.USB]: 'device.types.usb',
  [DeviceType.WIFI]: 'device.types.wifi',
  [DeviceType.BLUETOOTH]: 'device.types.bluetooth'
};

// 为了向后兼容，保留旧的导出名称
export const DEVICE_TYPE_LABELS = DEVICE_TYPE_KEYS;

export const DEVICE_TYPE_ICONS: Record<DeviceType, string> = {
  [DeviceType.USB]: 'usb',
  [DeviceType.WIFI]: 'wifi',
  [DeviceType.BLUETOOTH]: 'bluetooth'
};

export const DEVICE_TYPE_COLORS: Record<DeviceType, string> = {
  [DeviceType.USB]: '#2080f0',
  [DeviceType.WIFI]: '#18a058',
  [DeviceType.BLUETOOTH]: '#f0a020'
};

// === 设备状态相关常量 ===
// 注意：这些标签现在通过国际化系统提供，这里保留作为类型键
export const DEVICE_STATUS_KEYS: Record<DeviceStatus, string> = {
  [DeviceStatus.CONNECTED]: 'device.status.connected',
  [DeviceStatus.DISCONNECTED]: 'device.status.disconnected',
  [DeviceStatus.CONNECTING]: 'device.status.connecting',
  [DeviceStatus.DISCONNECTING]: 'device.status.disconnecting',
  [DeviceStatus.ERROR]: 'device.status.error',
  [DeviceStatus.UNKNOWN]: 'device.status.unknown'
};

// 为了向后兼容，保留旧的导出名称
export const DEVICE_STATUS_LABELS = DEVICE_STATUS_KEYS;

export const DEVICE_STATUS_COLORS: Record<DeviceStatus, string> = {
  [DeviceStatus.CONNECTED]: '#18a058',
  [DeviceStatus.DISCONNECTED]: '#909399',
  [DeviceStatus.CONNECTING]: '#2080f0',
  [DeviceStatus.DISCONNECTING]: '#f0a020',
  [DeviceStatus.ERROR]: '#d03050',
  [DeviceStatus.UNKNOWN]: '#606266'
};

export const DEVICE_STATUS_ICONS: Record<DeviceStatus, string> = {
  [DeviceStatus.CONNECTED]: 'checkmark-circle',
  [DeviceStatus.DISCONNECTED]: 'close-circle',
  [DeviceStatus.CONNECTING]: 'time',
  [DeviceStatus.DISCONNECTING]: 'time',
  [DeviceStatus.ERROR]: 'warning',
  [DeviceStatus.UNKNOWN]: 'help-circle'
};

// === 传输类型相关常量 ===
// 注意：这些标签现在通过国际化系统提供，这里保留作为类型键
export const TRANSMISSION_TYPE_KEYS: Record<TransmissionType, string> = {
  [TransmissionType.HE_FILE]: 'device.transmission.heFile',
  [TransmissionType.AUDIO_FILE]: 'device.transmission.audioFile',
  [TransmissionType.PROJECT_DATA]: 'device.transmission.projectData',
  [TransmissionType.DEVICE_CONFIG]: 'device.transmission.deviceConfig'
};

// 为了向后兼容，保留旧的导出名称
export const TRANSMISSION_TYPE_LABELS = TRANSMISSION_TYPE_KEYS;

export const TRANSMISSION_TYPE_ICONS: Record<TransmissionType, string> = {
  [TransmissionType.HE_FILE]: 'document',
  [TransmissionType.AUDIO_FILE]: 'musical-note',
  [TransmissionType.PROJECT_DATA]: 'folder',
  [TransmissionType.DEVICE_CONFIG]: 'settings'
};

// 传输优先级国际化键值
export const TRANSMISSION_PRIORITY_KEYS: Record<TransmissionPriority, string> = {
  [TransmissionPriority.LOW]: 'device.transmission.priority.low',
  [TransmissionPriority.NORMAL]: 'device.transmission.priority.normal',
  [TransmissionPriority.HIGH]: 'device.transmission.priority.high',
  [TransmissionPriority.URGENT]: 'device.transmission.priority.urgent'
};

// 为了向后兼容，保留旧的导出名称
export const TRANSMISSION_PRIORITY_LABELS = TRANSMISSION_PRIORITY_KEYS;

export const TRANSMISSION_PRIORITY_COLORS: Record<TransmissionPriority, string> = {
  [TransmissionPriority.LOW]: '#909399',
  [TransmissionPriority.NORMAL]: '#2080f0',
  [TransmissionPriority.HIGH]: '#f0a020',
  [TransmissionPriority.URGENT]: '#d03050'
};

// === 设备配置常量 ===
export const DEFAULT_DEVICE_CONFIG = {
  // 连接配置
  CONNECTION_TIMEOUT: 10000,           // 10秒
  RECONNECTION_TIMEOUT: 5000,          // 5秒
  MAX_RECONNECTION_ATTEMPTS: 3,
  HEARTBEAT_INTERVAL: 30000,           // 30秒

  // 扫描配置
  SCAN_TIMEOUT: 15000,                 // 15秒
  AUTO_SCAN_INTERVAL: 30000,           // 30秒
  SCAN_RETRY_DELAY: 2000,              // 2秒
  ENABLE_AUTO_SCAN: false,             // 禁用自动扫描（功能未完全实现）
  
  // 传输配置
  CHUNK_SIZE: 1024 * 64,               // 64KB
  TRANSMISSION_TIMEOUT: 30000,         // 30秒
  MAX_TRANSMISSION_RETRIES: 3,
  TRANSMISSION_RETRY_DELAY: 1000,      // 1秒
  MAX_CONCURRENT_TRANSMISSIONS: 3,
  
  // 缓存配置
  MAX_EVENT_HISTORY: 100,
  MAX_TRANSMISSION_HISTORY: 1000,
  CACHE_CLEANUP_INTERVAL: 300000,      // 5分钟
  
  // 验证配置
  DEVICE_NAME_MIN_LENGTH: 1,
  DEVICE_NAME_MAX_LENGTH: 50,
  MAX_DEVICES: 50,
  
  // UI配置
  REFRESH_INTERVAL: 1000,              // 1秒
  PROGRESS_UPDATE_INTERVAL: 500,       // 0.5秒
  STATUS_CHECK_INTERVAL: 5000,         // 5秒
};

// === USB设备相关常量 ===
export const USB_DEVICE_CONFIG = {
  VENDOR_IDS: [0x1234, 0x5678],        // 支持的厂商ID
  PRODUCT_IDS: [0xABCD, 0xEF12],       // 支持的产品ID
  INTERFACE_CLASS: 0xFF,                // 自定义接口类
  ENDPOINT_IN: 0x81,                    // 输入端点
  ENDPOINT_OUT: 0x02,                   // 输出端点
  TRANSFER_TIMEOUT: 5000,               // 传输超时
  MAX_PACKET_SIZE: 64,                  // 最大包大小
};

// === WIFI设备相关常量 ===
export const WIFI_DEVICE_CONFIG = {
  DEFAULT_PORT: 8080,                   // 默认端口
  DISCOVERY_PORT: 8081,                 // 发现端口
  DISCOVERY_TIMEOUT: 5000,              // 发现超时
  KEEP_ALIVE_INTERVAL: 10000,           // 保活间隔
  SOCKET_TIMEOUT: 30000,                // Socket超时
  MAX_CONNECTIONS: 10,                  // 最大连接数
  PROTOCOL_VERSION: '1.0',              // 协议版本
};

// === 蓝牙设备相关常量 ===
export const BLUETOOTH_DEVICE_CONFIG = {
  SERVICE_UUID: '12345678-1234-1234-1234-123456789abc',
  CHARACTERISTIC_UUID: '*************-4321-4321-cba987654321',
  SCAN_TIMEOUT: 10000,                  // 扫描超时
  CONNECTION_TIMEOUT: 15000,            // 连接超时
  MTU_SIZE: 512,                        // MTU大小
  RSSI_THRESHOLD: -80,                  // RSSI阈值
  ADVERTISING_INTERVAL: 1000,           // 广播间隔
};

// === 错误代码常量 ===
export const DEVICE_ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  DEVICE_NOT_FOUND: 'DEVICE_NOT_FOUND',
  DEVICE_BUSY: 'DEVICE_BUSY',
  INVALID_PARAMETER: 'INVALID_PARAMETER',
  
  // 连接错误
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  CONNECTION_LOST: 'CONNECTION_LOST',
  ALREADY_CONNECTED: 'ALREADY_CONNECTED',
  NOT_CONNECTED: 'NOT_CONNECTED',
  
  // 传输错误
  TRANSMISSION_FAILED: 'TRANSMISSION_FAILED',
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  INVALID_FILE_FORMAT: 'INVALID_FILE_FORMAT',
  CHECKSUM_MISMATCH: 'CHECKSUM_MISMATCH',
  
  // USB错误
  USB_DEVICE_NOT_FOUND: 'USB_DEVICE_NOT_FOUND',
  USB_PERMISSION_DENIED: 'USB_PERMISSION_DENIED',
  USB_TRANSFER_ERROR: 'USB_TRANSFER_ERROR',
  
  // WIFI错误
  WIFI_NETWORK_ERROR: 'WIFI_NETWORK_ERROR',
  WIFI_CONNECTION_REFUSED: 'WIFI_CONNECTION_REFUSED',
  WIFI_TIMEOUT: 'WIFI_TIMEOUT',
  
  // 蓝牙错误
  BLUETOOTH_NOT_AVAILABLE: 'BLUETOOTH_NOT_AVAILABLE',
  BLUETOOTH_PERMISSION_DENIED: 'BLUETOOTH_PERMISSION_DENIED',
  BLUETOOTH_PAIRING_FAILED: 'BLUETOOTH_PAIRING_FAILED',
};

// === 错误消息键映射 ===
// 注意：这些错误消息现在通过国际化系统提供，这里保留作为键映射
export const ERROR_MESSAGE_KEYS: Record<string, string> = {
  [DEVICE_ERROR_CODES.UNKNOWN_ERROR]: 'device.errors.unknownError',
  [DEVICE_ERROR_CODES.TIMEOUT_ERROR]: 'device.errors.timeoutError',
  [DEVICE_ERROR_CODES.PERMISSION_DENIED]: 'device.errors.permissionDenied',
  [DEVICE_ERROR_CODES.DEVICE_NOT_FOUND]: 'device.errors.deviceNotFound',
  [DEVICE_ERROR_CODES.DEVICE_BUSY]: 'device.errors.deviceBusy',
  [DEVICE_ERROR_CODES.INVALID_PARAMETER]: 'device.errors.invalidParameter',

  [DEVICE_ERROR_CODES.CONNECTION_FAILED]: 'device.errors.connectionFailed',
  [DEVICE_ERROR_CODES.CONNECTION_LOST]: 'device.errors.connectionLost',
  [DEVICE_ERROR_CODES.ALREADY_CONNECTED]: 'device.errors.alreadyConnected',
  [DEVICE_ERROR_CODES.NOT_CONNECTED]: 'device.errors.notConnected',

  [DEVICE_ERROR_CODES.TRANSMISSION_FAILED]: 'device.errors.transmissionFailed',
  [DEVICE_ERROR_CODES.FILE_NOT_FOUND]: 'device.errors.fileNotFound',
  [DEVICE_ERROR_CODES.INVALID_FILE_FORMAT]: 'device.errors.invalidFileFormat',
  [DEVICE_ERROR_CODES.CHECKSUM_MISMATCH]: 'device.errors.checksumMismatch',

  [DEVICE_ERROR_CODES.USB_DEVICE_NOT_FOUND]: 'device.errors.usbDeviceNotFound',
  [DEVICE_ERROR_CODES.USB_PERMISSION_DENIED]: 'device.errors.usbPermissionDenied',
  [DEVICE_ERROR_CODES.USB_TRANSFER_ERROR]: 'device.errors.usbTransferError',

  [DEVICE_ERROR_CODES.WIFI_NETWORK_ERROR]: 'device.errors.wifiNetworkError',
  [DEVICE_ERROR_CODES.WIFI_CONNECTION_REFUSED]: 'device.errors.wifiConnectionRefused',
  [DEVICE_ERROR_CODES.WIFI_TIMEOUT]: 'device.errors.wifiTimeout',

  [DEVICE_ERROR_CODES.BLUETOOTH_NOT_AVAILABLE]: 'device.errors.bluetoothNotAvailable',
  [DEVICE_ERROR_CODES.BLUETOOTH_PERMISSION_DENIED]: 'device.errors.bluetoothPermissionDenied',
  [DEVICE_ERROR_CODES.BLUETOOTH_PAIRING_FAILED]: 'device.errors.bluetoothPairingFailed',
};

// 为了向后兼容，保留旧的导出名称
export const ERROR_MESSAGES = ERROR_MESSAGE_KEYS;

/**
 * 获取设备错误的国际化键
 * @param errorCode 错误代码
 * @returns 国际化键，如果未找到则返回通用错误键
 */
export const getDeviceErrorKey = (errorCode: string): string => {
  return ERROR_MESSAGE_KEYS[errorCode] || 'device.errors.unknownError';
};

/**
 * 获取设备类型的国际化键
 * @param deviceType 设备类型
 * @returns 国际化键
 */
export const getDeviceTypeKey = (deviceType: DeviceType): string => {
  return DEVICE_TYPE_KEYS[deviceType];
};

/**
 * 获取传输类型的国际化键
 * @param transmissionType 传输类型
 * @returns 国际化键
 */
export const getTransmissionTypeKey = (transmissionType: TransmissionType): string => {
  return TRANSMISSION_TYPE_KEYS[transmissionType];
};

/**
 * 获取设备状态的国际化键
 * @param deviceStatus 设备状态
 * @returns 国际化键
 */
export const getDeviceStatusKey = (deviceStatus: DeviceStatus): string => {
  return DEVICE_STATUS_KEYS[deviceStatus];
};

/**
 * 获取传输优先级的国际化键
 * @param priority 传输优先级
 * @returns 国际化键
 */
export const getTransmissionPriorityKey = (priority: TransmissionPriority): string => {
  return TRANSMISSION_PRIORITY_KEYS[priority];
};

// === 文件类型支持 ===
export const SUPPORTED_FILE_TYPES = {
  HE_FILES: ['.he'],
  AUDIO_FILES: ['.wav', '.mp3', '.ogg'],
  PROJECT_FILES: ['.arpf'],
  CONFIG_FILES: ['.json', '.xml'],
};

export const FILE_TYPE_MAPPING: Record<string, TransmissionType> = {
  '.he': TransmissionType.HE_FILE,
  '.wav': TransmissionType.AUDIO_FILE,
  '.mp3': TransmissionType.AUDIO_FILE,
  '.ogg': TransmissionType.AUDIO_FILE,
  '.arpf': TransmissionType.PROJECT_DATA,
  '.json': TransmissionType.DEVICE_CONFIG,
  '.xml': TransmissionType.DEVICE_CONFIG,
};

// === 性能配置 ===
export const PERFORMANCE_CONFIG = {
  // 列表虚拟化阈值
  VIRTUAL_LIST_THRESHOLD: 100,
  
  // 批量操作大小
  BATCH_SIZE: 50,
  
  // 防抖延迟
  DEBOUNCE_DELAY: 300,
  
  // 节流间隔
  THROTTLE_INTERVAL: 100,
  
  // 缓存大小
  CACHE_SIZE: 1000,
  
  // 清理间隔
  CLEANUP_INTERVAL: 300000,           // 5分钟
};

// === 主题配置 ===
export const THEME_CONFIG = {
  // 设备卡片样式
  DEVICE_CARD: {
    borderRadius: '8px',
    padding: '16px',
    margin: '8px',
    minHeight: '120px',
  },
  
  // 状态指示器样式
  STATUS_INDICATOR: {
    size: '12px',
    borderRadius: '50%',
    animation: 'pulse 2s infinite',
  },
  
  // 进度条样式
  PROGRESS_BAR: {
    height: '4px',
    borderRadius: '2px',
    animationDuration: '0.3s',
  },
};

// === 国际化键值 ===
export const I18N_KEYS = {
  DEVICE_MANAGER: 'deviceManager',
  DEVICE_LIST: 'deviceList',
  DEVICE_CONNECTION: 'deviceConnection',
  DATA_TRANSMISSION: 'dataTransmission',
  DEVICE_SETTINGS: 'deviceSettings',
  ERROR_MESSAGES: 'errorMessages',
  STATUS_LABELS: 'statusLabels',
  ACTION_BUTTONS: 'actionButtons',
};
