import type { FileEditorState } from "./types";
import { DEFAULT_DURATION_MS } from "./types";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 文件级别的编辑器状态管理器
 * 负责管理多个文件的独立编辑状态，确保文件间状态隔离
 */
export class FileEditorStateManager {
  private fileStates = new Map<string, FileEditorState>();
  private readonly maxCacheSize = 50; // 最大缓存文件数量

  /**
   * 获取文件的编辑状态
   */
  getFileState(fileUuid: string): FileEditorState {
    if (!this.fileStates.has(fileUuid)) {
      // 创建新的文件状态
      const newState: FileEditorState = {
        selectedEventId: null,
        selectedCurvePointIndex: -1,
        events: [],
        totalDuration: DEFAULT_DURATION_MS,
        isDurationLockedByAudio: false,
        isAdjustingProperties: false,
        // UI 状态初始化
        scrollPosition: 0,
        zoomLevel: 1.0,
        viewportStart: 0,
        viewportEnd: DEFAULT_DURATION_MS,
        // Canvas 状态初始化
        canvasScrollLeft: 0,
        canvasScrollTop: 0,
        waveformContainerWidth: 800,
        waveformContainerHeight: 400,
        // 历史状态初始化
        undoStack: [],
        redoStack: [],
        // 音频相关状态初始化
        showAudioWaveform: false, // 默认隐藏音频波形
        audioAmplitudeData: null,
        audioDuration: null,
        // 活跃时间
        lastActiveTime: Date.now(),
      };

      this.fileStates.set(fileUuid, newState);
      logger.debug(LogModule.WAVEFORM, `创建新的文件编辑状态: ${fileUuid}`);

      // 检查缓存大小，必要时清理
      this.cleanupOldStates();
    } else {
      // 更新最后活跃时间
      const state = this.fileStates.get(fileUuid)!;
      state.lastActiveTime = Date.now();
    }

    return this.fileStates.get(fileUuid)!;
  }

  /**
   * 设置文件的编辑状态
   */
  setFileState(fileUuid: string, partialState: Partial<FileEditorState>): void {
    const currentState = this.getFileState(fileUuid);
    const updatedState = { ...currentState, ...partialState, lastActiveTime: Date.now() };
    this.fileStates.set(fileUuid, updatedState);

    logger.debug(LogModule.WAVEFORM, `更新文件编辑状态: ${fileUuid}`, {
      updatedFields: Object.keys(partialState),
    });
  }

  /**
   * 移除文件状态
   */
  removeFileState(fileUuid: string): void {
    if (this.fileStates.has(fileUuid)) {
      this.fileStates.delete(fileUuid);
      logger.debug(LogModule.WAVEFORM, `移除文件编辑状态: ${fileUuid}`);
    }
  }

  /**
   * 清理所有文件状态
   */
  clearAllStates(): void {
    this.fileStates.clear();
    logger.debug(LogModule.WAVEFORM, "清理所有文件编辑状态");
  }

  /**
   * 获取当前缓存的文件数量
   */
  getCacheSize(): number {
    return this.fileStates.size;
  }

  /**
   * 获取所有缓存的文件UUID
   */
  getCachedFileUuids(): string[] {
    return Array.from(this.fileStates.keys());
  }

  /**
   * 检查文件状态是否存在
   */
  hasFileState(fileUuid: string): boolean {
    return this.fileStates.has(fileUuid);
  }

  /**
   * 清理最久未使用的状态（LRU策略）
   */
  private cleanupOldStates(): void {
    if (this.fileStates.size <= this.maxCacheSize) {
      return;
    }

    // 按最后活跃时间排序，移除最久未使用的状态
    const sortedEntries = Array.from(this.fileStates.entries()).sort(([, a], [, b]) => a.lastActiveTime - b.lastActiveTime);

    const toRemove = sortedEntries.slice(0, this.fileStates.size - this.maxCacheSize);
    toRemove.forEach(([fileUuid]) => {
      this.fileStates.delete(fileUuid);
      logger.debug(LogModule.WAVEFORM, `LRU清理文件状态: ${fileUuid}`);
    });
  }

  /**
   * 更新UI状态的便捷方法
   */
  updateUIState(
    fileUuid: string,
    uiState: {
      scrollPosition?: number;
      zoomLevel?: number;
      viewportStart?: number;
      viewportEnd?: number;
      canvasScrollLeft?: number;
      canvasScrollTop?: number;
      waveformContainerWidth?: number;
      waveformContainerHeight?: number;
    }
  ) {
    this.setFileState(fileUuid, uiState);
  }

  /**
   * 获取状态管理器的统计信息
   */
  getStats() {
    return {
      totalFiles: this.fileStates.size,
      maxCacheSize: this.maxCacheSize,
      oldestActiveTime: Math.min(...Array.from(this.fileStates.values()).map((s) => s.lastActiveTime)),
      newestActiveTime: Math.max(...Array.from(this.fileStates.values()).map((s) => s.lastActiveTime)),
    };
  }
}

// 创建全局状态管理器实例
export const fileEditorStateManager = new FileEditorStateManager();
