<template>
  <n-dropdown
    :options="languageOptions"
    @select="handleLanguageSelect"
    placement="bottom-end"
    trigger="click"
  >
    <n-button text class="language-switcher">
      <template #icon>
        <n-icon :component="LanguageIcon" />
      </template>
      {{ currentLanguageName }}
    </n-button>
  </n-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NDropdown, NButton, NIcon } from 'naive-ui';
import { Language as LanguageIcon } from '@vicons/ionicons5';
import { useLanguageStore } from '@/stores/language-store';

import type { SupportedLocale } from '@/locales';

// === Props ===
interface Props {
  compact?: boolean;
  showIcon?: boolean;
  showText?: boolean;
}

withDefaults(defineProps<Props>(), {
  compact: false,
  showIcon: true,
  showText: true,
});

// === 组合函数 ===
const languageStore = useLanguageStore();

// === 计算属性 ===
const currentLanguageName = computed(() => languageStore.currentLanguageName);

const languageOptions = computed(() => {
  return languageStore.availableLanguages.map(lang => ({
    label: lang.name,
    key: lang.code,
    disabled: lang.isCurrent,
  }));
});

// === 事件处理 ===
const handleLanguageSelect = async (key: string) => {
  try {
    await languageStore.setLanguage(key as SupportedLocale);
  } catch (error) {
    console.error('Failed to switch language:', error);
  }
};
</script>

<style scoped>
.language-switcher {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: #e6e6e6;
  font-size: 12px;
}

.language-switcher:hover {
  background: rgba(255, 255, 255, 0.1);
}

.language-switcher.compact {
  padding: 2px 6px;
  font-size: 11px;
}
</style>
