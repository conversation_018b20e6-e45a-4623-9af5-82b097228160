# 文件上传组件架构

这是一个重构后的模块化文件上传系统，专门用于 realitytap-ota-server 的版本管理功能。

## 📁 组件结构

```
FileUpload/
├── ChunkFileUpload.vue      # 主组件 (230行)
├── FileSelector.vue         # 文件选择组件 (300行)
├── ProgressDisplay.vue      # 进度显示组件 (100行)
├── MetadataForm.vue         # 元数据表单组件 (150行)
├── FileSummary.vue          # 文件摘要组件 (100行)
├── __tests__/               # 测试文件
│   └── FileSelector.test.ts
└── README.md               # 本文件
```

## 🎯 设计原则

### 单一职责原则
每个组件都有明确的职责：
- **FileSelector**: 处理文件选择、验证和读取
- **ProgressDisplay**: 显示上传进度和状态
- **MetadataForm**: 处理版本元数据编辑
- **FileSummary**: 显示文件信息摘要
- **ChunkFileUpload**: 组合所有子组件，处理业务流程

### 可复用性
- 所有子组件都可以独立使用
- Composables 可以在其他组件中复用
- 工具函数提供通用的文件处理功能

### 可测试性
- 每个组件都有对应的单元测试
- 业务逻辑与UI逻辑分离
- 使用依赖注入便于测试

## 🔧 Composables

### useFileState
管理文件状态的 composable：

```typescript
const fileState = useFileState();

// 状态
fileState.installerFile.value    // 安装包文件
fileState.signatureFile.value    // 签名文件
fileState.hashFile.value          // 哈希文件
fileState.signatureValue.value   // 签名内容
fileState.hashValue.value         // 哈希值

// 计算属性
fileState.selectedFiles.value     // 选中的文件列表
fileState.canStartUpload.value    // 是否可以开始上传

// 方法
fileState.clearAllFiles()         // 清除所有文件
fileState.updateInstallerFile(file) // 更新安装包文件
```

### useFileUpload
处理文件上传逻辑的 composable：

```typescript
const uploadState = useFileUpload();

// 状态
uploadState.isUploading.value     // 是否正在上传
uploadState.uploadProgress.value  // 上传进度
uploadState.uploadResult.value    // 上传结果

// 方法
uploadState.startBatchUpload(files, hash, signature) // 开始批量上传
uploadState.cancelUpload()        // 取消上传
uploadState.resetUploadState()    // 重置状态
uploadState.parseFileMetadata(filename) // 解析文件元数据
```

## 🛠️ 工具函数

### 文件验证
```typescript
import { validateFileType, validateFileSize, validateHashFormat } from '@/utils/fileUpload';

validateFileType('test.exe', ['.exe', '.msi'])  // 验证文件类型
validateFileSize(1024 * 1024)                   // 验证文件大小
validateHashFormat('a'.repeat(64))              // 验证哈希格式
```

### 格式化函数
```typescript
import { formatBytes, formatSpeed, formatTime } from '@/utils/fileUpload';

formatBytes(1024 * 1024)    // "1 MB"
formatSpeed(1024 * 1024)    // "1 MB/s"
formatTime(90)              // "1:30"
```

### 文件元数据检测
```typescript
import { 
  detectPlatformFromFileName,
  detectArchitectureFromFileName,
  extractVersionFromFileName 
} from '@/utils/fileUpload';

detectPlatformFromFileName('app.exe')      // "windows"
detectArchitectureFromFileName('app-x64.exe') // "x86_64"
extractVersionFromFileName('app-1.2.3.exe')   // "1.2.3"
```

## 📊 性能优化

### 节流和防抖
```typescript
import { throttle, debounce } from '@/utils/fileUpload';

// 限制进度更新频率
const throttledUpdate = throttle(updateProgress, 100);

// 防抖用户输入
const debouncedSearch = debounce(search, 300);
```

### 文件处理优化
- 使用 FileReader API 异步读取文件
- 节流进度更新，避免过于频繁的UI更新
- 懒加载组件，减少初始加载时间

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm run test

# 运行特定测试文件
npm run test FileSelector.test.ts

# 运行测试并生成覆盖率报告
npm run test:coverage
```

### 测试覆盖
- 组件渲染测试
- 用户交互测试
- 文件验证测试
- 错误处理测试
- Composables 功能测试
- 工具函数测试

## 🔄 使用示例

### 基本使用
```vue
<template>
  <ChunkFileUpload
    @success="handleSuccess"
    @cancel="handleCancel"
  />
</template>

<script setup>
import ChunkFileUpload from '@/components/Common/ChunkFileUpload.vue';

const handleSuccess = () => {
  console.log('上传成功');
};

const handleCancel = () => {
  console.log('上传取消');
};
</script>
```

### 独立使用子组件
```vue
<template>
  <FileSelector
    v-model:installer-file="installerFile"
    v-model:signature-file="signatureFile"
    v-model:hash-file="hashFile"
    v-model:signature-value="signatureValue"
    v-model:hash-value="hashValue"
  />
</template>

<script setup>
import FileSelector from '@/components/Common/FileUpload/FileSelector.vue';
import { ref } from 'vue';

const installerFile = ref(null);
const signatureFile = ref(null);
const hashFile = ref(null);
const signatureValue = ref(null);
const hashValue = ref(null);
</script>
```

## 🚀 迁移指南

### 从旧版本迁移
1. 替换组件引用：
   ```typescript
   // 旧版本
   import ChunkFileUpload from '@/components/Common/ChunkFileUpload.vue';
   
   // 新版本（API保持兼容）
   import ChunkFileUpload from '@/components/Common/ChunkFileUpload.vue';
   ```

2. API保持兼容，无需修改现有代码

3. 如需使用新功能，可以引入子组件或 composables

## 📈 性能对比

| 指标 | 旧版本 | 新版本 | 改进 |
|------|--------|--------|------|
| 代码行数 | 1,520行 | 230行 | -85% |
| 组件复杂度 | 单一巨型组件 | 5个专门组件 | 模块化 |
| 可测试性 | 困难 | 容易 | 单元测试覆盖 |
| 可维护性 | 低 | 高 | 单一职责 |
| 可复用性 | 无 | 高 | 独立组件 |
| 加载性能 | 一次性加载 | 按需加载 | 懒加载 |

## 🔮 未来规划

- [ ] 添加拖拽上传支持
- [ ] 支持多文件并行上传
- [ ] 添加上传队列管理
- [ ] 支持断点续传
- [ ] 添加更多文件格式支持
- [ ] 国际化支持
