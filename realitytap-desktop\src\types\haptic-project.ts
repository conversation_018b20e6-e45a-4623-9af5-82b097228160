// Define the structure for a group based on project.json
export interface HapticsGroup {
  groupUuid: string;
  name: string;
  path: string;
  description: string;
  parentGroupUuid: string | null; // 父分组的UUID，根分组为null
}

// Define the structure for a file entry based on project.json
export interface HapticFile {
  fileUuid: string;
  name: string;
  path: string;
  group: string; // Corresponds to groupUuid
  formatVersion: "V1" | "V2";
  createTime: string; // ISO 8601 format
  lastModifiedTime: string; // ISO 8601 format
  description: string;
  version: string; // Version of the .he file content
  /**
   * 关联音频文件的相对路径（相对于 audio 目录，如 "group1/xxx.wav"），可选
   */
  associatedAudio?: string;
  /**
   * 关联视频文件的相对路径（相对于 video 目录，如 "group1/xxx.mp4"），可选
   */
  associatedVideo?: string;
  /**
   * 音频文件的详细信息，如时长和采样率，可选
   * 注意：当关联视频文件时，此字段存储视频文件的音频信息
   */
  audioInfo?: { durationMs: number; sampleRate: number } | null;
  /**
   * 文件类型：'he' | 'audio' | 其他。用于前端过滤文件树，仅显示 he 文件。
   */
  fileType?: string;
  /**
   * 波形缩放率，用于持久化存储用户的缩放偏好，默认值为 1.0
   */
  zoomLevel?: number;
}

// Define the main project structure based on project.json
export interface RealityTapProject {
  projectUuid: string;
  projectName: string;
  createTime: string; // ISO 8601 format
  lastModifiedTime: string; // ISO 8601 format
  author: string;
  version: string; // Version of the .arpf format
  description: string;
  tags: string[];
  groups: HapticsGroup[] | undefined;
  files: HapticFile[] | undefined;
}