# Set executable permissions for Docker deployment scripts
# This script is for Windows environments

Write-Host "Setting executable permissions for Docker deployment scripts..." -ForegroundColor Green

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptDir

# Files that need executable permissions
$executableFiles = @(
    "$projectRoot\docker\entrypoint.sh",
    "$projectRoot\scripts\docker-deploy.sh",
    "$projectRoot\scripts\docker-upgrade.sh"
)

foreach ($file in $executableFiles) {
    if (Test-Path $file) {
        Write-Host "Setting permissions for: $file" -ForegroundColor Yellow
        
        # In Windows, we ensure the file is not read-only
        $fileInfo = Get-Item $file
        if ($fileInfo.IsReadOnly) {
            $fileInfo.IsReadOnly = $false
            Write-Host "  - Removed read-only attribute" -ForegroundColor Cyan
        }
        
        Write-Host "  - Permissions set successfully" -ForegroundColor Green
    } else {
        Write-Host "File not found: $file" -ForegroundColor Red
    }
}

Write-Host "`nPermissions setup completed!" -ForegroundColor Green
Write-Host "`nNote: In Linux/macOS environments, run:" -ForegroundColor Yellow
Write-Host "chmod +x docker/entrypoint.sh scripts/docker-deploy.sh scripts/docker-upgrade.sh" -ForegroundColor Cyan
