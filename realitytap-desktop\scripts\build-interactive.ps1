#!/usr/bin/env pwsh

<#
.SYNOPSIS
    交互式编译脚本 - RealityTap Desktop
    Interactive build script for RealityTap Desktop

.DESCRIPTION
    此脚本提供交互式界面来配置和构建签名的安装包。
    This script provides an interactive interface to configure and build signed installers.

.USAGE
    运行方式 / How to run:
    1. 双击 build-interactive.bat 文件
    2. 或在 PowerShell 中执行: .\scripts\build-interactive.ps1

.WORKFLOW
    构建流程 / Build workflow:

    步骤 1/5: 设置版本号
    - 显示当前版本号
    - 输入新版本号（格式: x.y.z）
    - 直接回车使用当前版本

    步骤 2/5: 选择更新服务器
    - 从 build-servers.json 读取服务器配置
    - 选择目标更新服务器（本地测试/局域网/远程）
    - 直接回车使用默认服务器

    步骤 3/5: 选择签名密钥
    - 扫描 keys/ 目录中的 .key 文件
    - 显示密钥文件信息（大小、修改时间）
    - 记住上次选择，直接回车使用默认密钥

    步骤 4/5: 设置签名密码
    - 如果密钥文件名包含 "nopass"，跳过此步骤
    - 如果有保存的密码，直接回车使用保存的密码
    - 输入新密码会覆盖保存的密码
    - 输入空格表示无密码

    步骤 5/5: 确认配置
    - 显示完整的构建配置摘要
    - 确认后开始构建流程
    - 直接回车开始构建（默认 Y）
#>

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 显示标题
function Show-Title {
    Clear-Host
    Write-ColorOutput "╔══════════════════════════════════════════════════════════════╗" "Cyan"
    Write-ColorOutput "║                RealityTap Desktop 交互式构建工具              ║" "Cyan"
    Write-ColorOutput "║              Interactive Build Tool for RealityTap           ║" "Cyan"
    Write-ColorOutput "╚══════════════════════════════════════════════════════════════╝" "Cyan"
    Write-ColorOutput ""
}

# 获取当前版本号
function Get-CurrentVersion {
    $PackageJsonPath = "package.json"
    if (Test-Path $PackageJsonPath) {
        $PackageJson = Get-Content $PackageJsonPath | ConvertFrom-Json
        return $PackageJson.version
    }
    return "1.0.0"
}

# 验证版本号格式
function Test-VersionFormat {
    param([string]$Version)
    return $Version -match '^\d+\.\d+\.\d+$'
}

# 获取用户输入的版本号
function Get-VersionInput {
    $CurrentVersion = Get-CurrentVersion
    Write-ColorOutput "📋 当前版本: $CurrentVersion" "Yellow"
    Write-ColorOutput ""
    
    do {
        $NewVersion = Read-Host "请输入新的版本号 (格式: x.y.z, 回车使用当前版本)"
        
        if ([string]::IsNullOrWhiteSpace($NewVersion)) {
            $NewVersion = $CurrentVersion
            break
        }
        
        if (Test-VersionFormat $NewVersion) {
            break
        } else {
            Write-ColorOutput "❌ 版本号格式错误，请使用 x.y.z 格式 (例如: 1.2.3)" "Red"
        }
    } while ($true)
    
    return $NewVersion
}

# 获取服务器配置
function Get-ServerConfigs {
    $ConfigPath = "build-servers.json"
    if (Test-Path $ConfigPath) {
        $Config = Get-Content $ConfigPath -Encoding UTF8 | ConvertFrom-Json
        return $Config.servers
    } else {
        Write-ColorOutput "❌ 未找到服务器配置文件: $ConfigPath" "Red"
        exit 1
    }
}

# 显示服务器选项并获取用户选择
function Get-ServerChoice {
    $Servers = Get-ServerConfigs
    $DefaultChoice = ($Servers | Where-Object { $_.isDefault }).id
    $LastSelectedServer = Get-LastSelectedServer
    $LastSelectedServerName = $null

    # 确定回车时使用的选择：优先使用上次选择，否则使用默认
    $EnterChoice = $DefaultChoice
    if ($LastSelectedServer) {
        $LastServer = $Servers | Where-Object { $_.id -eq $LastSelectedServer }
        if ($LastServer) {
            $EnterChoice = $LastSelectedServer
            $LastSelectedServerName = $LastServer.name
        }
    }

    Write-ColorOutput "🌐 请选择更新服务器:" "Yellow"
    Write-ColorOutput ""

    foreach ($Server in $Servers) {
        $DefaultMark = ""

        # 优先显示回车选择标记
        if ($Server.id -eq $EnterChoice) {
            $DefaultMark += " (回车选择)"
        } else {
            # 如果不是回车选择，则显示其他标记
            if ($Server.id -eq $DefaultChoice) {
                $DefaultMark += " (默认)"
            }
            if ($Server.id -eq $LastSelectedServer) {
                $DefaultMark += " (上次选择)"
            }
        }

        Write-ColorOutput "  $($Server.id). $($Server.name)$DefaultMark" "White"
        Write-ColorOutput "     $($Server.description)" "Gray"
        Write-ColorOutput "     URL: $($Server.url)" "Gray"
        Write-ColorOutput ""
    }

    if ($LastSelectedServerName) {
        Write-ColorOutput "💡 上次选择: $LastSelectedServerName" "Cyan"
        Write-ColorOutput ""
    }

    # 确定提示文本
    $PromptText = if ($LastSelectedServer) {
        "请输入选项编号 (1-$($Servers.Count), 回车使用上次选择)"
    } else {
        "请输入选项编号 (1-$($Servers.Count), 回车使用默认)"
    }

    do {
        $Choice = Read-Host $PromptText

        if ([string]::IsNullOrWhiteSpace($Choice)) {
            $Choice = $EnterChoice
            break
        }

        if ($Choice -match '^\d+$' -and [int]$Choice -ge 1 -and [int]$Choice -le $Servers.Count) {
            break
        } else {
            Write-ColorOutput "❌ 请输入有效的选项编号 (1-$($Servers.Count))" "Red"
        }
    } while ($true)

    $SelectedServer = $Servers | Where-Object { $_.id -eq [int]$Choice }

    # 保存服务器选择
    Save-BuildConfig -ServerChoice ([int]$Choice)

    return $SelectedServer
}

# 获取构建配置
function Get-BuildConfig {
    $ConfigPath = "build-config.json"
    if (Test-Path $ConfigPath) {
        try {
            $Config = Get-Content $ConfigPath -Encoding UTF8 | ConvertFrom-Json
            return $Config
        } catch {
            return $null
        }
    }
    return $null
}

# 获取上次选择的签名密钥
function Get-LastSelectedKey {
    $Config = Get-BuildConfig
    if ($Config) {
        return $Config.lastSelectedKey
    }
    return $null
}

# 获取上次选择的服务器
function Get-LastSelectedServer {
    $Config = Get-BuildConfig
    if ($Config -and ($Config.PSObject.Properties.Name -contains 'lastSelectedServer') -and ($null -ne $Config.lastSelectedServer) -and ($Config.lastSelectedServer -gt 0)) {
        return $Config.lastSelectedServer
    }
    return $null
}

# 获取保存的密钥密码
function Get-SavedKeyPassword {
    param([string]$KeyFileName)

    $Config = Get-BuildConfig
    if ($Config -and $Config.keyPasswords -and $Config.keyPasswords.$KeyFileName) {
        return $Config.keyPasswords.$KeyFileName
    }
    return $null
}

# 保存构建配置
function Save-BuildConfig {
    param(
        [string]$KeyFileName,
        [string]$KeyPassword = $null,
        [int]$ServerChoice = 0
    )

    $ConfigPath = "build-config.json"
    $Config = Get-BuildConfig

    if (-not $Config) {
        $Config = [PSCustomObject]@{
            lastSelectedKey = ""
            lastSelectedServer = $null
            keyPasswords = [PSCustomObject]@{}
            lastUpdated = ""
        }
    }

    # 确保 keyPasswords 属性存在
    if (-not $Config.PSObject.Properties['keyPasswords']) {
        $Config | Add-Member -MemberType NoteProperty -Name 'keyPasswords' -Value ([PSCustomObject]@{})
    }

    # 确保 lastSelectedServer 属性存在（但不重置现有值）
    if (-not $Config.PSObject.Properties['lastSelectedServer']) {
        $Config | Add-Member -MemberType NoteProperty -Name 'lastSelectedServer' -Value $null
    }

    # 确保 lastSelectedKey 属性存在（但不重置现有值）
    if (-not $Config.PSObject.Properties['lastSelectedKey']) {
        $Config | Add-Member -MemberType NoteProperty -Name 'lastSelectedKey' -Value ""
    }

    # 更新密钥选择（仅在提供时更新）
    if ($KeyFileName) {
        $Config.lastSelectedKey = $KeyFileName
    }

    # 更新服务器选择（仅在提供时更新）
    if ($ServerChoice -gt 0) {
        $Config.lastSelectedServer = $ServerChoice
    }

    $Config.lastUpdated = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")

    # 更新密码（如果提供）
    if ($KeyPassword -and $KeyFileName) {
        # 确保 keyPasswords 是一个对象
        if ($Config.keyPasswords -isnot [PSCustomObject]) {
            $Config.keyPasswords = [PSCustomObject]@{}
        }

        # 添加或更新密码
        if ($Config.keyPasswords.PSObject.Properties[$KeyFileName]) {
            $Config.keyPasswords.$KeyFileName = $KeyPassword
        } else {
            $Config.keyPasswords | Add-Member -MemberType NoteProperty -Name $KeyFileName -Value $KeyPassword
        }
    }

    try {
        $Config | ConvertTo-Json -Depth 3 | Set-Content $ConfigPath -Encoding UTF8
    } catch {
        Write-ColorOutput "⚠️  警告: 无法保存配置文件: $_" "Yellow"
    }
}

# 获取签名文件列表
function Get-SigningKeys {
    $KeysDir = "keys"
    if (-not (Test-Path $KeysDir)) {
        Write-ColorOutput "❌ 未找到签名密钥目录: $KeysDir" "Red"
        exit 1
    }

    $KeyFiles = Get-ChildItem $KeysDir -Filter "*.key" | Where-Object { -not $_.Name.EndsWith(".pub") }

    if ($KeyFiles.Count -eq 0) {
        Write-ColorOutput "❌ 在 $KeysDir 目录中未找到签名密钥文件" "Red"
        exit 1
    }

    return $KeyFiles
}

# 显示签名文件选项并获取用户选择
function Get-SigningKeyChoice {
    $KeyFiles = Get-SigningKeys
    $LastSelectedKey = Get-LastSelectedKey
    $DefaultChoice = 1
    $DefaultKeyName = $null

    # 检查上次选择的密钥是否仍然存在
    if ($LastSelectedKey) {
        for ($i = 0; $i -lt $KeyFiles.Count; $i++) {
            if ($KeyFiles[$i].Name -eq $LastSelectedKey) {
                $DefaultChoice = $i + 1
                $DefaultKeyName = $LastSelectedKey
                break
            }
        }
    }

    Write-ColorOutput "🔐 请选择签名密钥文件:" "Yellow"
    Write-ColorOutput ""

    for ($i = 0; $i -lt $KeyFiles.Count; $i++) {
        $KeyFile = $KeyFiles[$i]
        $KeyPath = $KeyFile.FullName
        $KeySize = [math]::Round($KeyFile.Length / 1KB, 2)
        $DefaultMark = if (($i + 1) -eq $DefaultChoice) { " (默认)" } else { "" }

        Write-ColorOutput "  $($i + 1). $($KeyFile.Name)$DefaultMark" "White"
        Write-ColorOutput "     路径: $KeyPath" "Gray"
        Write-ColorOutput "     大小: $KeySize KB" "Gray"
        Write-ColorOutput "     修改时间: $($KeyFile.LastWriteTime)" "Gray"
        Write-ColorOutput ""
    }

    if ($DefaultKeyName) {
        Write-ColorOutput "💡 上次选择: $DefaultKeyName" "Cyan"
        Write-ColorOutput ""
    }

    do {
        $Choice = Read-Host "请输入选项编号 (1-$($KeyFiles.Count), 回车使用默认)"

        if ([string]::IsNullOrWhiteSpace($Choice)) {
            $Choice = $DefaultChoice
            break
        }

        if ($Choice -match '^\d+$' -and [int]$Choice -ge 1 -and [int]$Choice -le $KeyFiles.Count) {
            break
        } else {
            Write-ColorOutput "❌ 请输入有效的选项编号 (1-$($KeyFiles.Count))" "Red"
        }
    } while ($true)

    $SelectedKey = $KeyFiles[[int]$Choice - 1]

    # 保存当前选择（密码稍后在Set-SigningEnvironment中保存）
    Save-BuildConfig -KeyFileName $SelectedKey.Name

    return $SelectedKey
}

# 更新package.json版本号
function Update-PackageVersion {
    param([string]$Version)

    $PackageJsonPath = "package.json"
    $PackageJson = Get-Content $PackageJsonPath | ConvertFrom-Json
    $PackageJson.version = $Version
    $PackageJson | ConvertTo-Json -Depth 10 | Set-Content $PackageJsonPath -Encoding UTF8

    Write-ColorOutput "✅ 已更新 package.json 版本号: $Version" "Green"
}

# 更新Cargo.toml版本号
function Update-CargoVersion {
    param([string]$Version)

    $CargoTomlPath = "src-tauri/Cargo.toml"

    if (Test-Path $CargoTomlPath) {
        try {
            # 读取 Cargo.toml 文件内容
            $CargoContent = Get-Content $CargoTomlPath -Raw

            # 使用正则表达式替换版本号
            # 匹配 [package] 部分下的 version = "x.y.z" 行
            $VersionPattern = '(?m)^(\s*version\s*=\s*")[^"]+(".*)$'
            $NewVersionLine = "`${1}$Version`${2}"

            $UpdatedContent = $CargoContent -replace $VersionPattern, $NewVersionLine

            # 写回文件
            Set-Content $CargoTomlPath -Value $UpdatedContent -Encoding UTF8 -NoNewline

            Write-ColorOutput "✅ 已更新 Cargo.toml 版本号: $Version" "Green"
        } catch {
            Write-ColorOutput "⚠️  警告: 更新 Cargo.toml 版本号失败: $_" "Yellow"
            Write-ColorOutput "   请手动检查 src-tauri/Cargo.toml 文件" "Yellow"
        }
    } else {
        Write-ColorOutput "⚠️  警告: 未找到 Cargo.toml 文件: $CargoTomlPath" "Yellow"
    }
}

# 更新Tauri配置版本号和更新服务器
function Update-TauriConfig {
    param(
        [string]$Version,
        [object]$Server,
        [System.IO.FileInfo]$KeyFile
    )

    $TauriConfigPath = "src-tauri/tauri.conf.json"
    $TauriConfig = Get-Content $TauriConfigPath | ConvertFrom-Json

    # 更新版本号
    $TauriConfig.version = $Version

    # 更新更新服务器配置和公钥
    if ($TauriConfig.plugins.updater) {
        $TauriConfig.plugins.updater.endpoints = @($Server.url)
        $TauriConfig.plugins.updater.active = $true

        # 根据服务器URL协议自动设置传输协议选项
        if ($Server.url -match "^http://") {
            # HTTP协议：启用不安全传输协议选项
            $TauriConfig.plugins.updater.dangerousInsecureTransportProtocol = $true
            Write-ColorOutput "⚠️  已启用不安全传输协议 (HTTP)" "Yellow"
        } elseif ($Server.url -match "^https://") {
            # HTTPS协议：禁用不安全传输协议选项
            $TauriConfig.plugins.updater.dangerousInsecureTransportProtocol = $false
            Write-ColorOutput "✅ 已禁用不安全传输协议 (HTTPS)" "Green"
        } else {
            # 其他情况：默认禁用不安全传输协议选项
            $TauriConfig.plugins.updater.dangerousInsecureTransportProtocol = $false
            Write-ColorOutput "⚠️  未识别的协议，默认禁用不安全传输协议" "Yellow"
        }

        # 更新公钥
        $PubKeyPath = "$($KeyFile.FullName).pub"
        if (Test-Path $PubKeyPath) {
            $PubKeyContent = Get-Content $PubKeyPath -Raw
            $TauriConfig.plugins.updater.pubkey = $PubKeyContent.Trim()
            Write-ColorOutput "✅ 已更新公钥配置" "Green"
        } else {
            Write-ColorOutput "⚠️  警告: 未找到对应的公钥文件: $PubKeyPath" "Yellow"
        }
    }

    $TauriConfig | ConvertTo-Json -Depth 10 | Set-Content $TauriConfigPath -Encoding UTF8

    Write-ColorOutput "✅ 已更新 Tauri 配置文件" "Green"
    Write-ColorOutput "   版本号: $Version" "Gray"
    Write-ColorOutput "   更新服务器: $($Server.url)" "Gray"
    Write-ColorOutput "   签名密钥: $($KeyFile.Name)" "Gray"
}

# 获取签名密码
function Get-SigningPassword {
    param([System.IO.FileInfo]$KeyFile)

    # 检查是否需要密码
    if ($KeyFile.Name -like "*nopass*") {
        return ""
    }

    # 尝试获取保存的密码
    $SavedPassword = Get-SavedKeyPassword -KeyFileName $KeyFile.Name
    $PasswordToUse = $null

    if ($SavedPassword) {
        Write-ColorOutput "💡 找到保存的密码" "Cyan"
        $PasswordInput = Read-Host "请输入密钥密码 (回车使用保存的密码, 如果没有密码请输入空格后回车)" -AsSecureString

        if ($PasswordInput.Length -eq 0) {
            # 使用保存的密码
            $PasswordToUse = $SavedPassword
            Write-ColorOutput "✅ 使用保存的密码" "Green"
        } elseif ($PasswordInput.Length -eq 1) {
            # 检查是否输入了空格（表示无密码）
            $PlainInput = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($PasswordInput))
            if ($PlainInput -eq " ") {
                $PasswordToUse = ""
            } else {
                $PasswordToUse = $PlainInput
            }
        } else {
            # 使用新输入的密码
            $PasswordToUse = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($PasswordInput))
        }
    } else {
        # 没有保存的密码，正常输入
        $PasswordInput = Read-Host "请输入密钥密码 (如果没有密码请直接回车)" -AsSecureString
        if ($PasswordInput.Length -gt 0) {
            $PasswordToUse = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($PasswordInput))
        } else {
            $PasswordToUse = ""
        }
    }

    return $PasswordToUse
}

# 设置签名环境变量
function Set-SigningEnvironment {
    param(
        [System.IO.FileInfo]$KeyFile,
        [string]$Password
    )

    $KeyPath = $KeyFile.FullName
    $env:TAURI_SIGNING_PRIVATE_KEY = $KeyPath

    Write-ColorOutput "✅ 已设置签名密钥环境变量" "Green"
    Write-ColorOutput "   密钥文件: $KeyPath" "Gray"

    # 设置密码环境变量
    if ($Password -and $Password -ne "") {
        $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD = $Password
    }
}

# 显示配置摘要
function Show-ConfigSummary {
    param(
        [string]$Version,
        [object]$Server,
        [System.IO.FileInfo]$KeyFile
    )

    Write-ColorOutput "📋 构建配置摘要:" "Yellow"
    Write-ColorOutput ""
    Write-ColorOutput "  版本号: $Version" "White"
    Write-ColorOutput "  更新服务器: $($Server.name)" "White"
    Write-ColorOutput "  服务器URL: $($Server.url)" "Gray"
    Write-ColorOutput "  签名密钥: $($KeyFile.Name)" "White"
    Write-ColorOutput "  密钥路径: $($KeyFile.FullName)" "Gray"
    Write-ColorOutput ""
}

# 检查构建环境
function Test-BuildEnvironment {
    Write-ColorOutput "🔍 检查构建环境..." "Yellow"

    $Issues = @()

    # 检查 Node.js
    try {
        $NodeVersion = node --version 2>$null
        if ($NodeVersion) {
            Write-ColorOutput "✅ Node.js: $NodeVersion" "Green"
        } else {
            $Issues += "Node.js 未安装或不在 PATH 中"
        }
    } catch {
        $Issues += "Node.js 未安装或不在 PATH 中"
    }

    # 检查 npm
    try {
        $NpmVersion = npm --version 2>$null
        if ($NpmVersion) {
            Write-ColorOutput "✅ npm: v$NpmVersion" "Green"
        } else {
            $Issues += "npm 未安装或不在 PATH 中"
        }
    } catch {
        $Issues += "npm 未安装或不在 PATH 中"
    }

    # 检查 Tauri CLI
    try {
        $TauriVersion = npm list @tauri-apps/cli --depth=0 2>$null | Select-String "@tauri-apps/cli"
        if ($TauriVersion) {
            Write-ColorOutput "✅ Tauri CLI: 已安装" "Green"
        } else {
            $Issues += "Tauri CLI 未安装"
        }
    } catch {
        $Issues += "Tauri CLI 未安装"
    }

    # 检查 node_modules
    if (Test-Path "node_modules") {
        Write-ColorOutput "✅ 依赖项: 已安装" "Green"
    } else {
        $Issues += "依赖项未安装，请运行 npm install"
    }

    if ($Issues.Count -gt 0) {
        Write-ColorOutput ""
        Write-ColorOutput "❌ 发现以下问题:" "Red"
        foreach ($Issue in $Issues) {
            Write-ColorOutput "  • $Issue" "Red"
        }
        return $false
    }

    Write-ColorOutput "✅ 构建环境检查通过" "Green"
    Write-ColorOutput ""
    return $true
}

# 清理旧的构建产物
function Clear-BuildArtifacts {
    Write-ColorOutput "🧹 清理旧的构建产物..." "Yellow"

    $BundleDir = "src-tauri/target/release/bundle"
    if (Test-Path $BundleDir) {
        try {
            Remove-Item $BundleDir -Recurse -Force
            Write-ColorOutput "✅ 已清理旧的构建产物" "Green"
        } catch {
            Write-ColorOutput "⚠️  警告: 无法完全清理构建产物: $_" "Yellow"
        }
    } else {
        Write-ColorOutput "✅ 无需清理，构建产物目录不存在" "Green"
    }
    Write-ColorOutput ""
}

# 执行构建
function Start-Build {
    Write-ColorOutput "🔨 开始构建过程..." "Yellow"
    Write-ColorOutput ""

    # 检查构建环境
    if (-not (Test-BuildEnvironment)) {
        return $false
    }

    # 清理旧的构建产物
    Clear-BuildArtifacts

    try {
        # 1. 构建前端
        Write-ColorOutput "第一步: 构建前端应用..." "Cyan"
        Write-ColorOutput "执行命令: npm run build" "Gray"

        $BuildOutput = npm run build 2>&1
        $BuildExitCode = $LASTEXITCODE

        if ($BuildExitCode -ne 0) {
            Write-ColorOutput "❌ 前端构建失败 (退出代码: $BuildExitCode)" "Red"
            Write-ColorOutput "构建输出:" "Yellow"
            $BuildOutput | ForEach-Object { Write-ColorOutput "  $_" "Gray" }
            throw "前端构建失败"
        }

        Write-ColorOutput "✅ 前端构建完成" "Green"
        Write-ColorOutput ""

        # 2. 构建 Tauri 应用
        Write-ColorOutput "第二步: 构建 Tauri 应用..." "Cyan"
        Write-ColorOutput "执行命令: npm run tauri:build" "Gray"

        $TauriBuildOutput = npm run tauri:build 2>&1
        $TauriBuildExitCode = $LASTEXITCODE

        if ($TauriBuildExitCode -ne 0) {
            Write-ColorOutput "❌ Tauri 应用构建失败 (退出代码: $TauriBuildExitCode)" "Red"
            Write-ColorOutput "构建输出:" "Yellow"
            $TauriBuildOutput | ForEach-Object { Write-ColorOutput "  $_" "Gray" }
            throw "Tauri 应用构建失败"
        }

        Write-ColorOutput "✅ Tauri 应用构建完成" "Green"
        Write-ColorOutput ""

        # 3. 验证构建结果
        Write-ColorOutput "第三步: 验证构建结果..." "Cyan"
        $BundleDir = "src-tauri/target/release/bundle"

        if (Test-Path $BundleDir) {
            $MsiFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.msi"

            if ($MsiFiles.Count -eq 0) {
                throw "未找到MSI安装包文件"
            }

            Write-ColorOutput "📦 构建产物:" "Green"
            $HasValidPackage = $false

            foreach ($MsiFile in $MsiFiles) {
                $FileSize = [math]::Round($MsiFile.Length / 1MB, 2)
                Write-ColorOutput "  安装包: $($MsiFile.Name) ($FileSize MB)" "White"

                $SigFile = "$($MsiFile.FullName).sig"
                $HashFile = "$($MsiFile.FullName).hash"

                # 检查签名文件
                if (Test-Path $SigFile) {
                    # 验证签名文件不为空
                    $SigContent = Get-Content $SigFile -Raw -ErrorAction SilentlyContinue
                    if ($SigContent -and $SigContent.Trim().Length -gt 0) {
                        Write-ColorOutput "  ✅ 签名文件: $($MsiFile.Name).sig (有效)" "Green"
                        $HasValidPackage = $true
                    } else {
                        Write-ColorOutput "  ❌ 签名文件为空: $($MsiFile.Name).sig" "Red"
                    }
                } else {
                    Write-ColorOutput "  ❌ 缺少签名文件: $($MsiFile.Name).sig" "Red"
                }

                # 生成哈希文件
                try {
                    Write-ColorOutput "  🔍 正在计算文件哈希..." "Cyan"
                    $FileHash = Get-FileHash -Path $MsiFile.FullName -Algorithm SHA256
                    $HashValue = $FileHash.Hash.ToLower()

                    # 将哈希值写入文件
                    Set-Content -Path $HashFile -Value $HashValue -Encoding UTF8 -NoNewline

                    Write-ColorOutput "  ✅ 哈希文件: $($MsiFile.Name).hash (SHA256)" "Green"
                    Write-ColorOutput "     哈希值: $HashValue" "Gray"
                } catch {
                    Write-ColorOutput "  ❌ 生成哈希文件失败: $_" "Red"
                }
            }

            if (-not $HasValidPackage) {
                Write-ColorOutput ""
                Write-ColorOutput "⚠️  警告: 没有找到有效的签名安装包" "Yellow"
                Write-ColorOutput "这可能是由于签名配置问题导致的" "Yellow"
            }

            Write-ColorOutput ""
            Write-ColorOutput "📁 构建产物位置: $((Get-Item $BundleDir).FullName)" "Cyan"
        } else {
            throw "未找到构建产物目录"
        }

        Write-ColorOutput ""
        Write-ColorOutput "🎉 构建完成！" "Green"

    } catch {
        Write-ColorOutput ""
        Write-ColorOutput "❌ 构建失败: $_" "Red"
        return $false
    }

    return $true
}

# 保存构建成功后的密码
function Save-PasswordAfterSuccess {
    param(
        [string]$KeyFileName,
        [string]$Password
    )

    if ($Password -and $Password -ne "" -and $KeyFileName) {
        Save-BuildConfig -KeyFileName $KeyFileName -KeyPassword $Password
        Write-ColorOutput "✅ 构建成功，密码已保存到配置文件" "Green"
    }
}

# 主函数
function Main {
    # 获取项目根目录
    $ProjectRoot = Split-Path -Parent $PSScriptRoot
    Set-Location $ProjectRoot

    try {
        # 显示标题
        Show-Title

        # 1. 获取版本号
        Write-ColorOutput "🔢 步骤 1/4: 设置版本号" "Yellow"
        Write-ColorOutput "═══════════════════════════════════════" "Yellow"
        $Version = Get-VersionInput
        Write-ColorOutput ""

        # 2. 选择服务器
        Write-ColorOutput "🌐 步骤 2/4: 选择更新服务器" "Yellow"
        Write-ColorOutput "═══════════════════════════════════════" "Yellow"
        $Server = Get-ServerChoice
        Write-ColorOutput ""

        # 3. 选择签名密钥
        Write-ColorOutput "🔐 步骤 3/5: 选择签名密钥" "Yellow"
        Write-ColorOutput "═══════════════════════════════════════" "Yellow"
        $KeyFile = Get-SigningKeyChoice
        Write-ColorOutput ""

        # 4. 获取签名密码
        Write-ColorOutput "🔑 步骤 4/5: 设置签名密码" "Yellow"
        Write-ColorOutput "═══════════════════════════════════════" "Yellow"
        $SigningPassword = Get-SigningPassword -KeyFile $KeyFile
        Write-ColorOutput ""

        # 5. 显示配置摘要并确认
        Write-ColorOutput "📋 步骤 5/5: 确认配置" "Yellow"
        Write-ColorOutput "═══════════════════════════════════════" "Yellow"
        Show-ConfigSummary -Version $Version -Server $Server -KeyFile $KeyFile

        $Confirm = Read-Host "确认以上配置并开始构建? (Y/n)"
        if ($Confirm -eq "n" -or $Confirm -eq "N") {
            Write-ColorOutput "构建已取消" "Yellow"
            exit 0
        }

        Write-ColorOutput ""
        Write-ColorOutput "🚀 开始构建流程..." "Green"
        Write-ColorOutput "═══════════════════════════════════════" "Green"

        # 更新配置文件
        Update-PackageVersion -Version $Version
        Update-CargoVersion -Version $Version
        Update-TauriConfig -Version $Version -Server $Server -KeyFile $KeyFile
        Set-SigningEnvironment -KeyFile $KeyFile -Password $SigningPassword

        Write-ColorOutput ""

        # 执行构建
        $BuildSuccess = Start-Build

        Write-ColorOutput ""
        if ($BuildSuccess) {
            # 构建成功后保存密码
            Save-PasswordAfterSuccess -KeyFileName $KeyFile.Name -Password $SigningPassword

            Write-ColorOutput "✨ 构建流程完成！" "Green"
            Write-ColorOutput ""
            Write-ColorOutput "📝 后续步骤:" "Yellow"
            Write-ColorOutput "1. 测试生成的安装包" "White"
            Write-ColorOutput "2. 部署到更新服务器" "White"
            Write-ColorOutput "3. 通知用户新版本可用" "White"
        } else {
            Write-ColorOutput "❌ 构建失败，请检查上面的错误信息" "Red"
            Write-ColorOutput ""
            Write-ColorOutput "💡 常见问题排查:" "Yellow"
            Write-ColorOutput "1. 检查 Node.js 和 npm 是否正确安装" "White"
            Write-ColorOutput "2. 确保所有依赖项已安装 (npm install)" "White"
            Write-ColorOutput "3. 检查 Tauri CLI 是否正确安装" "White"
            Write-ColorOutput "4. 验证签名密钥文件和密码是否正确" "White"
            exit 1
        }

    } catch {
        Write-ColorOutput ""
        Write-ColorOutput "❌ 脚本执行失败: $_" "Red"
        exit 1
    }
}

# 运行主函数
Main
