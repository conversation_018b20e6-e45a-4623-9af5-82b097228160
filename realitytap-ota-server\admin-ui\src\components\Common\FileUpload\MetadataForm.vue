<template>
  <div class="metadata-form">
    <n-card size="small">
      <template #header>
        <n-space align="center">
          <n-icon size="20">
            <DocumentOutline />
          </n-icon>
          <span>编辑文件元数据</span>
        </n-space>
      </template>

      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="文件名" path="filename">
          <n-input v-model:value="formData.filename" placeholder="请输入文件名" :disabled="true" />
        </n-form-item>

        <n-form-item label="版本号" path="version">
          <n-input
            v-model:value="formData.version"
            placeholder="请输入版本号 (如: 1.0.0)"
            :input-props="{ spellcheck: false }"
          />
          <template #feedback>
            <n-text depth="3" style="font-size: 12px">
              使用语义化版本格式：主版本号.次版本号.修订号 (如: 1.2.3)
            </n-text>
          </template>
        </n-form-item>

        <n-form-item label="平台" path="platform">
          <n-select v-model:value="formData.platform" :options="platformOptions" placeholder="请选择平台" />
        </n-form-item>

        <n-form-item label="架构" path="architecture">
          <n-select v-model:value="formData.architecture" :options="architectureOptions" placeholder="请选择架构" />
        </n-form-item>

        <n-form-item label="发布渠道" path="channel">
          <n-select v-model:value="formData.channel" :options="channelOptions" placeholder="请选择发布渠道" />
        </n-form-item>

        <n-form-item label="强制更新" path="isForced">
          <n-space align="center">
            <n-switch v-model:value="formData.isForced" />
            <n-text depth="3"> 开启后，客户端将强制更新到此版本 </n-text>
          </n-space>
          <template #feedback v-if="formData.isForced">
            <n-alert type="warning" size="small" :show-icon="false" style="margin-top: 8px">
              <n-text style="font-size: 12px"> ⚠️ 强制更新将要求所有客户端必须更新到此版本才能继续使用应用程序 </n-text>
            </n-alert>
          </template>
        </n-form-item>

        <n-form-item label="发布说明" path="releaseNotes">
          <n-input v-model:value="formData.releaseNotes" type="textarea" placeholder="请输入发布说明" :rows="3" />
        </n-form-item>
      </n-form>

      <template #action>
        <n-space justify="end">
          <n-button @click="$emit('cancel')">取消</n-button>
          <n-button type="primary" @click="handleSave">保存</n-button>
        </n-space>
      </template>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import type { FileUploadRequest } from '@/types/upload';
import { DocumentOutline } from '@vicons/ionicons5';
import {
  NAlert,
  NButton,
  NCard,
  NForm,
  NFormItem,
  NIcon,
  NInput,
  NSelect,
  NSpace,
  NSwitch,
  NText,
  type FormInst,
  type FormRules,
  type SelectOption,
} from 'naive-ui';
import { ref, watch, nextTick } from 'vue';

// Props
interface Props {
  modelValue: FileUploadRequest & { filename: string };
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: FileUploadRequest & { filename: string }];
  save: [data: FileUploadRequest & { filename: string }];
  cancel: [];
}>();

// Refs
const formRef = ref<FormInst | null>(null);
const formData = ref<FileUploadRequest & { filename: string }>({ ...props.modelValue });

// 防止循环更新的标志
let isUpdatingFromProps = false;
let isUpdatingFromForm = false;

// 安全的深度比较函数
const safeDeepEqual = (obj1: any, obj2: any): boolean => {
  try {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  } catch (error) {
    console.warn('深度比较失败，使用浅比较:', error);
    return obj1 === obj2;
  }
};

// 监听 props 变化
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    // 防止循环更新
    if (isUpdatingFromForm) {
      return;
    }

    // 深度比较，避免不必要的更新
    if (safeDeepEqual(newValue, oldValue)) {
      return;
    }

    isUpdatingFromProps = true;
    nextTick(() => {
      try {
        formData.value = { ...newValue };
        console.debug('[MetadataForm] Props 更新完成:', newValue);
      } catch (error) {
        console.error('[MetadataForm] Props 更新失败:', error);
      } finally {
        isUpdatingFromProps = false;
      }
    });
  },
  { deep: true },
);

// 监听表单数据变化
watch(
  formData,
  (newValue, oldValue) => {
    // 防止循环更新
    if (isUpdatingFromProps) {
      return;
    }

    // 深度比较，避免不必要的更新
    if (safeDeepEqual(newValue, oldValue)) {
      return;
    }

    isUpdatingFromForm = true;
    nextTick(() => {
      try {
        emit('update:modelValue', { ...newValue });
        console.debug('[MetadataForm] Form 数据更新完成:', newValue);
      } catch (error) {
        console.error('[MetadataForm] Form 数据更新失败:', error);
      } finally {
        isUpdatingFromForm = false;
      }
    });
  },
  { deep: true },
);

// 表单选项
const platformOptions: SelectOption[] = [
  { label: 'Windows', value: 'windows' },
  { label: 'macOS', value: 'macos' },
  { label: 'Linux', value: 'linux' },
];

const architectureOptions: SelectOption[] = [
  { label: 'x64 (x86_64)', value: 'x86_64' },
  { label: 'ARM64 (aarch64)', value: 'aarch64' },
  { label: 'x86 (32位)', value: 'x86' },
];

const channelOptions: SelectOption[] = [
  { label: 'Stable (稳定版)', value: 'stable' },
  { label: 'Beta (测试版)', value: 'beta' },
  { label: 'Alpha (内测版)', value: 'alpha' },
];

// 表单验证规则
const formRules: FormRules = {
  filename: [{ required: true, message: '请输入文件名', trigger: 'blur' }],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    {
      pattern: /^\d+\.\d+\.\d+(-[a-zA-Z0-9]+)?$/,
      message: '版本号格式不正确，请使用语义化版本格式 (如: 1.0.0)',
      trigger: 'blur',
    },
  ],
  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
  architecture: [{ required: true, message: '请选择架构', trigger: 'change' }],
  channel: [{ required: true, message: '请选择发布渠道', trigger: 'change' }],
};

// 处理保存
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    emit('save', formData.value);
  } catch (error) {
    // 验证失败，不做任何操作
  }
};
</script>

<style scoped>
.metadata-form {
  margin: 16px 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.metadata-form :deep(.n-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.metadata-form :deep(.n-card__content) {
  flex: 1;
  overflow-y: auto;
}

.metadata-form :deep(.n-form) {
  height: 100%;
}

.metadata-form :deep(.n-form-item) {
  margin-bottom: 16px;
}

.metadata-form :deep(.n-form-item:last-child) {
  margin-bottom: 0;
}
</style>
