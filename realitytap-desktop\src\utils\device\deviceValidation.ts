// 设备验证相关工具函数

import type {
  Device,
  DeviceMetadata,
  DeviceConnectionConfig,
  DeviceScanOptions
} from "@/types/device-types";
import { DeviceType, DeviceStatus } from "@/types/device-types";
import { DEFAULT_DEVICE_CONFIG, DEVICE_ERROR_CODES } from "./deviceConstants";

// === 设备验证接口 ===
export interface DeviceValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface DeviceValidationOptions {
  strict?: boolean;           // 严格模式
  checkDuplicates?: boolean;  // 检查重复
  validateMetadata?: boolean; // 验证元数据
}

// === 基础验证函数 ===

/**
 * 验证设备ID格式
 */
export function validateDeviceId(deviceId: string): DeviceValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!deviceId) {
    errors.push("设备ID不能为空");
  } else if (typeof deviceId !== "string") {
    errors.push("设备ID必须是字符串");
  } else if (deviceId.length < 3) {
    errors.push("设备ID长度不能少于3个字符");
  } else if (deviceId.length > 50) {
    errors.push("设备ID长度不能超过50个字符");
  } else if (!/^[a-zA-Z0-9_-]+$/.test(deviceId)) {
    errors.push("设备ID只能包含字母、数字、下划线和连字符");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证设备名称
 */
export function validateDeviceName(name: string): DeviceValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!name) {
    errors.push("设备名称不能为空");
  } else if (typeof name !== "string") {
    errors.push("设备名称必须是字符串");
  } else if (name.trim().length < DEFAULT_DEVICE_CONFIG.DEVICE_NAME_MIN_LENGTH) {
    errors.push(`设备名称长度不能少于${DEFAULT_DEVICE_CONFIG.DEVICE_NAME_MIN_LENGTH}个字符`);
  } else if (name.length > DEFAULT_DEVICE_CONFIG.DEVICE_NAME_MAX_LENGTH) {
    errors.push(`设备名称长度不能超过${DEFAULT_DEVICE_CONFIG.DEVICE_NAME_MAX_LENGTH}个字符`);
  } else if (name.trim() !== name) {
    warnings.push("设备名称包含前导或尾随空格");
  }

  // 检查特殊字符
  const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
  if (invalidChars.test(name)) {
    errors.push("设备名称包含无效字符");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证设备类型
 */
export function validateDeviceType(type: DeviceType): DeviceValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  const validTypes = Object.values(DeviceType);
  if (!validTypes.includes(type)) {
    errors.push(`无效的设备类型: ${type}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证设备状态
 */
export function validateDeviceStatus(status: DeviceStatus): DeviceValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  const validStatuses = Object.values(DeviceStatus);
  if (!validStatuses.includes(status)) {
    errors.push(`无效的设备状态: ${status}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证设备元数据
 */
export function validateDeviceMetadata(metadata: DeviceMetadata): DeviceValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!metadata || typeof metadata !== "object") {
    errors.push("设备元数据必须是对象");
    return { isValid: false, errors, warnings };
  }

  // 验证制造商
  if (metadata.manufacturer && typeof metadata.manufacturer !== "string") {
    errors.push("制造商信息必须是字符串");
  }

  // 验证型号
  if (metadata.model && typeof metadata.model !== "string") {
    errors.push("型号信息必须是字符串");
  }

  // 验证版本
  if (metadata.version && typeof metadata.version !== "string") {
    errors.push("版本信息必须是字符串");
  }

  // 验证序列号
  if (metadata.serialNumber && typeof metadata.serialNumber !== "string") {
    errors.push("序列号必须是字符串");
  }

  // 验证功能列表
  if (metadata.capabilities) {
    if (!Array.isArray(metadata.capabilities)) {
      errors.push("功能列表必须是数组");
    } else {
      metadata.capabilities.forEach((capability, index) => {
        if (typeof capability !== "string") {
          errors.push(`功能列表第${index + 1}项必须是字符串`);
        }
      });
    }
  }

  // 验证连接信息
  if (metadata.connectionInfo) {
    const connInfo = metadata.connectionInfo;
    
    if (connInfo.ipAddress && !/^(\d{1,3}\.){3}\d{1,3}$/.test(connInfo.ipAddress)) {
      errors.push("IP地址格式无效");
    }
    
    if (connInfo.port && (connInfo.port < 1 || connInfo.port > 65535)) {
      errors.push("端口号必须在1-65535范围内");
    }
    
    if (connInfo.macAddress && !/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(connInfo.macAddress)) {
      errors.push("MAC地址格式无效");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证完整设备对象
 */
export function validateDevice(
  device: Device, 
  options: DeviceValidationOptions = {}
): DeviceValidationResult {
  const { strict = false, validateMetadata: checkMetadata = true } = options;
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  // 验证必需字段
  if (!device) {
    return {
      isValid: false,
      errors: ["设备对象不能为空"],
      warnings: []
    };
  }

  // 验证设备ID
  const deviceIdResult = validateDeviceId(device.deviceId);
  allErrors.push(...deviceIdResult.errors);
  allWarnings.push(...deviceIdResult.warnings);

  // 验证设备名称
  const nameResult = validateDeviceName(device.name);
  allErrors.push(...nameResult.errors);
  allWarnings.push(...nameResult.warnings);

  // 验证设备类型
  const typeResult = validateDeviceType(device.type);
  allErrors.push(...typeResult.errors);
  allWarnings.push(...typeResult.warnings);

  // 验证设备状态
  const statusResult = validateDeviceStatus(device.status);
  allErrors.push(...statusResult.errors);
  allWarnings.push(...statusResult.warnings);

  // 验证连接类型与设备类型一致
  if (device.connectionType.toString() !== device.type.toString()) {
    allErrors.push("连接类型必须与设备类型一致");
  }

  // 验证时间戳格式
  const timeFields = ['lastConnected', 'lastDisconnected', 'createdAt', 'updatedAt'];
  timeFields.forEach(field => {
    const value = device[field as keyof Device];
    if (value && typeof value === 'string') {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        allErrors.push(`${field}时间格式无效`);
      }
    }
  });

  // 验证布尔字段
  if (typeof device.isDefault !== "boolean") {
    allErrors.push("isDefault必须是布尔值");
  }

  // 验证元数据
  if (checkMetadata) {
    const metadataResult = validateDeviceMetadata(device.metadata);
    allErrors.push(...metadataResult.errors);
    allWarnings.push(...metadataResult.warnings);
  }

  // 严格模式下的额外检查
  if (strict) {
    // 检查时间逻辑
    if (device.createdAt && device.updatedAt) {
      const created = new Date(device.createdAt);
      const updated = new Date(device.updatedAt);
      if (updated < created) {
        allErrors.push("更新时间不能早于创建时间");
      }
    }

    // 检查连接时间逻辑
    if (device.lastConnected && device.lastDisconnected) {
      const connected = new Date(device.lastConnected);
      const disconnected = new Date(device.lastDisconnected);
      if (device.status === DeviceStatus.CONNECTED && disconnected > connected) {
        allWarnings.push("设备状态为已连接，但最后断开时间晚于最后连接时间");
      }
    }
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
}

/**
 * 验证设备连接配置
 */
export function validateDeviceConnectionConfig(config: DeviceConnectionConfig): DeviceValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!config.deviceId) {
    errors.push("设备ID不能为空");
  }

  if (config.timeout !== undefined) {
    if (typeof config.timeout !== "number" || config.timeout <= 0) {
      errors.push("超时时间必须是正数");
    } else if (config.timeout > 60000) {
      warnings.push("超时时间过长，建议不超过60秒");
    }
  }

  if (config.retryCount !== undefined) {
    if (typeof config.retryCount !== "number" || config.retryCount < 0) {
      errors.push("重试次数必须是非负整数");
    } else if (config.retryCount > 10) {
      warnings.push("重试次数过多，建议不超过10次");
    }
  }

  if (config.autoReconnect !== undefined && typeof config.autoReconnect !== "boolean") {
    errors.push("自动重连必须是布尔值");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证设备扫描选项
 */
export function validateDeviceScanOptions(options: DeviceScanOptions): DeviceValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (options.types) {
    if (!Array.isArray(options.types)) {
      errors.push("设备类型列表必须是数组");
    } else {
      const validTypes = Object.values(DeviceType);
      options.types.forEach((type, index) => {
        if (!validTypes.includes(type)) {
          errors.push(`设备类型列表第${index + 1}项无效: ${type}`);
        }
      });
    }
  }

  if (options.timeout !== undefined) {
    if (typeof options.timeout !== "number" || options.timeout <= 0) {
      errors.push("扫描超时时间必须是正数");
    } else if (options.timeout > 60000) {
      warnings.push("扫描超时时间过长，建议不超过60秒");
    }
  }

  if (options.includeDisconnected !== undefined && typeof options.includeDisconnected !== "boolean") {
    errors.push("includeDisconnected必须是布尔值");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证设备列表中的重复项
 */
export function validateDeviceListDuplicates(devices: Device[]): DeviceValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const seenIds = new Set<string>();
  const seenNames = new Set<string>();

  devices.forEach((device, index) => {
    // 检查ID重复
    if (seenIds.has(device.deviceId)) {
      errors.push(`设备ID重复: ${device.deviceId} (位置: ${index + 1})`);
    } else {
      seenIds.add(device.deviceId);
    }

    // 检查名称重复
    if (seenNames.has(device.name)) {
      warnings.push(`设备名称重复: ${device.name} (位置: ${index + 1})`);
    } else {
      seenNames.add(device.name);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 批量验证设备列表
 */
export function validateDeviceList(
  devices: Device[], 
  options: DeviceValidationOptions = {}
): DeviceValidationResult {
  const { checkDuplicates = true } = options;
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  // 验证数组本身
  if (!Array.isArray(devices)) {
    return {
      isValid: false,
      errors: ["设备列表必须是数组"],
      warnings: []
    };
  }

  // 检查设备数量限制
  if (devices.length > DEFAULT_DEVICE_CONFIG.MAX_DEVICES) {
    allErrors.push(`设备数量超过限制 (${DEFAULT_DEVICE_CONFIG.MAX_DEVICES})`);
  }

  // 验证每个设备
  devices.forEach((device, index) => {
    const result = validateDevice(device, options);
    result.errors.forEach(error => {
      allErrors.push(`设备${index + 1}: ${error}`);
    });
    result.warnings.forEach(warning => {
      allWarnings.push(`设备${index + 1}: ${warning}`);
    });
  });

  // 检查重复项
  if (checkDuplicates) {
    const duplicateResult = validateDeviceListDuplicates(devices);
    allErrors.push(...duplicateResult.errors);
    allWarnings.push(...duplicateResult.warnings);
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
}

/**
 * 创建设备验证错误消息
 */
export function createDeviceValidationError(result: DeviceValidationResult): Error {
  const message = result.errors.join("; ");
  const error = new Error(message);
  error.name = DEVICE_ERROR_CODES.INVALID_PARAMETER;
  return error;
}
