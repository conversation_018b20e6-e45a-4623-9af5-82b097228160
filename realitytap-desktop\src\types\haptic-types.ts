// RealityTap 触觉反馈系统 TypeScript 类型定义
// 基于现有 Rust 后端接口设计的完整类型系统

/**
 * 线性马达驱动芯片采样频率类型枚举
 * 对应 Rust 后端的 SamplingRateType
 */
export enum SamplingRateType {
  Sampling6Khz = 4,   // 6KHz驱动芯片采样率，基础效果
  Sampling8Khz = 5,   // 8KHz驱动芯片采样率，通用选择
  Sampling12Khz = 6,  // 12KHz驱动芯片采样率，中等精度
  Sampling24Khz = 8,  // 24KHz驱动芯片采样率，高精度效果
}

/**
 * 触觉执行器状态枚举
 */
export enum HapticActuatorState {
  Idle = "idle",           // 空闲状态
  Active = "active",       // 活动状态
  Processing = "processing", // 处理中
  Stopped = "stopped",     // 已停止
  Error = "error",         // 错误状态
}

/**
 * 触觉执行器配置接口
 * 对应 Rust 后端的 HapticActuatorConfig
 */
export interface HapticActuatorConfig {
  /** 设备唯一标识符 */
  id: number;
  /** 线性马达实际驱动频率 (Hz) */
  motor_drive_freq: number;
  /** 配置文件路径 */
  config_file: string;
  /** 驱动芯片采样率类型 */
  sampling_rate: SamplingRateType;
  /** 缓冲区大小（可选） */
  buffer_size?: number;
  /** 超时时间（毫秒，可选） */
  timeout_ms?: number;
  /** 是否启用日志（可选） */
  enable_logging?: boolean;
}

/**
 * 触觉播放参数接口
 * 对应 Rust 后端的 HapticPlayParams
 */
export interface HapticPlayParams {
  /** 触觉数据数组 */
  data: number[];
  /** 播放间隔（毫秒，可选） */
  interval_ms?: number;
  /** 循环次数（可选） */
  loop_count?: number;
  /** 振幅值 (0-255，可选) */
  amplitude?: number;
  /** 频率偏移（可选） */
  frequency_offset?: number;
}

/**
 * 触觉执行器状态接口
 * 对应 Rust 后端的 HapticActuatorStatus
 */
export interface HapticActuatorStatus {
  /** 设备ID */
  id: number;
  /** 设备状态 */
  state: string;
  /** 当前振幅 */
  amplitude: number;
  /** 缓冲区大小 */
  buffer_size: number;
  /** 是否已初始化 */
  is_initialized: boolean;
}

/**
 * 配置文件信息接口
 * 对应 Rust 后端的 ConfigFileInfo
 */
export interface ConfigFileInfo {
  /** 配置文件路径 */
  path: string;
  /** 文件名 */
  name: string;
  /** 文件大小（字节） */
  size: number;
  /** 文件是否存在 */
  exists: boolean;
  /** 最后修改时间 */
  last_modified?: string;
}

/**
 * 触觉执行器信息接口（前端扩展）
 */
export interface HapticActuator {
  /** 执行器配置 */
  config: HapticActuatorConfig;
  /** 执行器状态 */
  status: HapticActuatorStatus;
  /** 创建时间 */
  created_at: string;
  /** 最后更新时间 */
  updated_at: string;
  /** 是否为默认设备 */
  is_default: boolean;
}

/**
 * 触觉管理器状态接口
 */
export interface HapticManagerState {
  /** 是否已初始化 */
  isInitialized: boolean;
  /** 是否已启动 */
  isStarted: boolean;
  /** 执行器列表 */
  actuators: HapticActuator[];
  /** 当前全局振幅 */
  currentAmplitude: number;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 库状态信息 */
  libraryStatus: Record<string, boolean>;
}

/**
 * 触觉效果预设枚举
 */
export enum HapticEffectPreset {
  LightTap = 1,      // 轻点击
  HeavyTap = 2,      // 重点击
  Slide = 3,         // 滑动
  LongPress = 4,     // 长按
}

/**
 * 触觉管理器配置接口
 */
export interface HapticManagerConfig {
  /** 默认振幅 */
  defaultAmplitude?: number;
  /** 是否启用日志 */
  enableLogging?: boolean;
  /** 自动重连间隔（毫秒） */
  autoReconnectInterval?: number;
  /** 最大重试次数 */
  maxRetryCount?: number;
}

/**
 * 触觉操作结果接口
 */
export interface HapticOperationResult {
  /** 操作是否成功 */
  success: boolean;
  /** 结果消息 */
  message: string;
  /** 错误信息（如果有） */
  error?: string;
  /** 附加数据 */
  data?: any;
}

/**
 * 触觉事件类型枚举
 */
export enum HapticEventType {
  DeviceInitialized = "device_initialized",
  DeviceStarted = "device_started",
  DeviceStopped = "device_stopped",
  PlaybackStarted = "playback_started",
  PlaybackCompleted = "playback_completed",
  PlaybackError = "playback_error",
  AmplitudeChanged = "amplitude_changed",
  ConfigChanged = "config_changed",
}

/**
 * 触觉事件接口
 */
export interface HapticEvent {
  /** 事件类型 */
  type: HapticEventType;
  /** 设备ID（可选） */
  deviceId?: number;
  /** 事件数据 */
  data?: any;
  /** 时间戳 */
  timestamp: string;
  /** 错误信息（如果有） */
  error?: string;
}

/**
 * 触觉管理器方法接口
 */
export interface HapticManagerMethods {
  /** 初始化触觉反馈库 */
  initialize: (configs: HapticActuatorConfig[]) => Promise<void>;
  /** 启动触觉播放系统 */
  start: () => Promise<void>;
  /** 播放触觉数据 */
  play: (params: HapticPlayParams) => Promise<void>;
  /** 播放预定义效果 */
  playEffect: (effectId: number, strength: number) => Promise<void>;
  /** 停止触觉播放 */
  stop: () => Promise<void>;
  /** 设置全局振幅 */
  setAmplitude: (amplitude: number) => Promise<void>;
  /** 获取执行器状态 */
  getActuatorStatus: (actuatorId: number) => Promise<HapticActuatorStatus>;
  /** 获取所有执行器状态 */
  getAllActuatorStatus: () => Promise<HapticActuatorStatus[]>;
  /** 获取库状态 */
  getLibraryStatus: () => Promise<Record<string, boolean>>;
  /** 获取可用配置文件 */
  getAvailableConfigs: () => Promise<string[]>;
  /** 验证配置文件 */
  validateConfigFile: (configFile: string) => Promise<boolean>;
  /** 获取配置文件信息 */
  getConfigFileInfo: (configFile: string) => Promise<ConfigFileInfo>;
  /** 重置管理器状态 */
  reset: () => void;
  /** 刷新执行器状态 */
  refreshActuatorStatus: () => Promise<void>;

  // 新增：生命周期管理方法
  /** 安全清理 librtcore 资源 */
  cleanup: () => Promise<void>;
  /** 安全重新初始化 librtcore */
  reinitialize: (configs: HapticActuatorConfig[]) => Promise<void>;
  /** 强制重置（用于错误恢复） */
  forceReset: () => Promise<void>;
  /** 获取生命周期状态 */
  getLifecycleStatus: () => Promise<Record<string, any>>;
}

/**
 * 事件系统方法接口
 */
export interface EventSystemMethods {
  /** 初始化 Channel 系统 */
  initChannelSystem: () => Promise<void>;
  /** 启动消息处理器 */
  startMessageProcessor: () => Promise<void>;
  /** 停止消息处理器 */
  stopMessageProcessor: () => Promise<void>;
  /** 获取 Channel 状态 */
  getChannelStatus: () => Promise<boolean>;
  /** 添加事件监听器 */
  addEventListener: (
    eventType: keyof HapticEventListeners,
    listener: Function
  ) => () => void;
  /** 移除事件监听器 */
  removeEventListener: (
    eventType: keyof HapticEventListeners,
    listener: Function
  ) => void;
  /** 移除所有事件监听器 */
  removeAllEventListeners: () => void;
  /** 配置事件系统 */
  configureEventSystem: (config: Partial<EventSystemConfig>) => void;
}

/**
 * 触觉管理器 Composable 返回类型
 */
export interface UseHapticManagerReturn extends HapticManagerMethods, EventSystemMethods {
  // 只读状态
  readonly isInitialized: Readonly<Ref<boolean>>;
  readonly isStarted: Readonly<Ref<boolean>>;
  readonly actuators: Readonly<Ref<HapticActuator[]>>;
  readonly currentAmplitude: Readonly<Ref<number>>;
  readonly isLoading: Readonly<Ref<boolean>>;
  readonly error: Readonly<Ref<string | null>>;
  readonly libraryStatus: Readonly<Ref<Record<string, boolean>>>;

  // 新增：事件系统状态
  readonly isChannelInitialized: Readonly<Ref<boolean>>;
  readonly isMessageProcessorRunning: Readonly<Ref<boolean>>;
  readonly eventSystemConfig: Readonly<Ref<EventSystemConfig>>;
  readonly lastEvent: Readonly<Ref<HapticEventData | null>>;
  readonly eventCount: Readonly<Ref<number>>;
  readonly connectionStatus: Readonly<Ref<ConnectionStatus>>;
  readonly eventStatistics: Readonly<Ref<EventStatistics>>;
}

/**
 * 触觉 API 错误类型
 */
export interface HapticApiError {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 详细信息 */
  details?: string;
  /** 原始错误 */
  originalError?: any;
}

// 导入 Vue 的 Ref 类型
import type { Ref } from 'vue';

// ==================== 新增：事件系统类型定义 ====================

/**
 * 触觉事件类型枚举
 */
export enum HapticEventType {
  /** 生命周期事件 */
  Lifecycle = "haptic:lifecycle",
  /** 数据事件 */
  Data = "haptic:data",
  /** 错误事件 */
  Error = "haptic:error",
  /** 状态事件 */
  Status = "haptic:status",
}

/**
 * 生命周期事件子类型
 */
export enum LifecycleEventType {
  Start = "start",
  Complete = "complete",
  Stop = "stop",
}

/**
 * 数据事件子类型
 */
export enum DataEventType {
  WaveformSamples = "waveform_samples",
  ChunkInfo = "chunk_info",
  AmplitudeChanges = "amplitude_changes",
  Mixed = "mixed",
}

/**
 * 错误事件子类型
 */
export enum ErrorEventType {
  Processor = "processor",
  System = "system",
  Communication = "communication",
  Hardware = "hardware",
}

/**
 * 状态事件子类型
 */
export enum StatusEventType {
  Processing = "processing",
  Device = "device",
  System = "system",
}

/**
 * 振幅变化信息
 */
export interface AmplitudeChange {
  old_amplitude: number;
  new_amplitude: number;
  timestamp: number;
}

/**
 * 数据块信息
 */
export interface ChunkInfo {
  chunk_id: number;
  start_time: number;
  sample_count: number;
}

/**
 * 生命周期事件数据
 */
export interface LifecycleEventData {
  device_id: number;
  event_type: LifecycleEventType;
  timestamp: number;
  sample_count?: number;
  reason?: string;
}

/**
 * 数据事件数据
 */
export interface DataEventData {
  device_id: number;
  data_type: DataEventType;
  timestamp: number;
  sample_count: number;
  samples?: readonly number[];
  amplitude_changes?: readonly AmplitudeChange[];
  chunk_info?: ChunkInfo;
}

/**
 * 错误事件数据
 */
export interface ErrorEventData {
  device_id: number;
  error_type: ErrorEventType;
  error_message: string;
  timestamp: number;
  error_code?: number;
}

/**
 * 状态事件数据
 */
export interface StatusEventData {
  device_id: number;
  status_type: StatusEventType;
  status: string;
  timestamp: number;
  details?: Record<string, any>;
}

/**
 * 触觉事件联合类型
 */
export type HapticEventData =
  | { type: HapticEventType.Lifecycle; data: LifecycleEventData }
  | { type: HapticEventType.Data; data: DataEventData }
  | { type: HapticEventType.Error; data: ErrorEventData }
  | { type: HapticEventType.Status; data: StatusEventData };

/**
 * 事件监听器类型
 */
export type HapticEventListener<T = any> = (event: T) => void;

/**
 * 事件监听器映射
 */
export interface HapticEventListeners {
  "haptic:lifecycle": HapticEventListener<LifecycleEventData>;
  "haptic:data": HapticEventListener<DataEventData>;
  "haptic:error": HapticEventListener<ErrorEventData>;
  "haptic:status": HapticEventListener<StatusEventData>;
}

/**
 * 事件系统配置
 */
export interface EventSystemConfig {
  /** 是否启用事件监听 */
  enabled: boolean;
  /** 是否启用自动重连 */
  autoReconnect: boolean;
  /** 重连间隔（毫秒） */
  reconnectInterval: number;
  /** 最大重连次数 */
  maxReconnectAttempts: number;
  /** 事件缓冲区大小 */
  eventBufferSize: number;
  /** 是否启用事件日志 */
  enableEventLogging: boolean;
  /** 数据事件防抖延迟（毫秒） */
  dataEventDebounceDelay: number;
  /** 批量更新延迟（毫秒） */
  batchUpdateDelay: number;
  /** 是否启用事件缓冲 */
  enableEventBuffering: boolean;
}

/**
 * 连接状态类型
 */
export type ConnectionStatus = 'connected' | 'disconnected' | 'reconnecting';

/**
 * 事件统计信息
 */
export interface EventStatistics {
  /** 总事件数 */
  totalEvents: number;
  /** 生命周期事件数 */
  lifecycleEvents: number;
  /** 数据事件数 */
  dataEvents: number;
  /** 错误事件数 */
  errorEvents: number;
  /** 状态事件数 */
  statusEvents: number;
  /** 最后事件时间 */
  lastEventTime: number;
  /** 事件处理错误数 */
  processingErrors: number;
}
