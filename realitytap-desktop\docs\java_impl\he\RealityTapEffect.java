package android.os.vibrator.realitytap.he;

import android.annotation.NonNull;
import android.os.Parcel;
import android.os.Parcelable;

/** @hide */
public abstract class RealityTapEffect implements Parcelable {
    public static final int VERSION_1 = 1;
    public static final int VERSION_2 = 2;
    public static final int VERSION_3 = 3;

    public static final String KEY_METADATA = "Metadata";
    public static final String KEY_VERSION = "Version";
    public static final String KEY_DESCRIPTION = "Description";
    public static final String KEY_CREATED = "Created";
    public static final String KEY_PATTERN_LIST = "PatternList";
    public static final String KEY_PATTERN = "Pattern";
    public static final String KEY_ABSOLUTE_TIME = "AbsoluteTime";
    public static final String KEY_EVENT = "Event";
    public static final String KEY_CURVE = "Curve";
    public static final String KEY_TYPE = "Type";
    public static final String KEY_DURATION = "Duration";
    public static final String KEY_RELATIVE_TIME = "RelativeTime";
    public static final String KEY_INDEX = "Index";
    public static final String KEY_PARAMETERS = "Parameters";
    public static final String KEY_FREQUENCY = "Frequency";
    public static final String KEY_INTENSITY = "Intensity";
    public static final String KEY_TIME = "Time";

    protected Metadata metadata;

    /** HE文件版本 */
    private final int version;

    /** 振动时间间隔 */
    private int interval = 0;

    /** 振动次数 */
    private int looper = 1;

    /** 振动幅度 */
    private int amplitude = 255;

    /** 全局频率 */
    private int frequency = 0;

    public RealityTapEffect(int version) {
        this.version = version;
    }

    public int getInterval() {
        return interval;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }

    public int getLooper() {
        return looper;
    }

    public void setLooper(int looper) {
        this.looper = looper;
    }

    public int getAmplitude() {
        return amplitude;
    }

    public void setAmplitude(int amplitude) {
        this.amplitude = amplitude;
    }

    public int getFrequency() {
        return frequency;
    }

    public void setFrequency(int frequency) {
        this.frequency = frequency;
    }

    public int getVersion() {
        return version;
    }

    public Metadata getMetadata() {
        return metadata;
    }

    public void setMetadata(Metadata metadata) {
        this.metadata = metadata;
    }

    public abstract int getDuration();

    public abstract int[] convertToArray();

    @NonNull
    public abstract RealityTapEffect copy();

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(version);
        dest.writeInt(looper);
        dest.writeInt(interval);
        dest.writeInt(amplitude);
        dest.writeInt(frequency);
    }

    public static final @NonNull Creator<RealityTapEffect> CREATOR = new Creator<>() {
        @Override
        public RealityTapEffect createFromParcel(@NonNull Parcel in) {
            int version = in.readInt();
            int looper = in.readInt();
            int interval = in.readInt();
            int amplitude = in.readInt();
            int frequency = in.readInt();

            RealityTapEffect effect;
            if (version == VERSION_1) {
                effect = new RealityTapEffectV1(in);
            } else if (version == VERSION_2) {
                effect = new RealityTapEffectV2(in);
            } else {
                throw new IllegalStateException(
                        "Unexpected reality tap version token in parcel.");
            }

            effect.setLooper(looper);
            effect.setInterval(interval);
            effect.setAmplitude(amplitude);
            effect.setFrequency(frequency);
            return effect;
        }

        @Override
        public RealityTapEffect[] newArray(int size) {
            return new RealityTapEffect[size];
        }
    };
}
