import { BaseDAO } from './base.dao';

export interface CreateSystemLogData {
  timestamp: string;
  level: string;
  message: string;
  module: string;
  operation?: string;
  client_ip?: string;
  is_private_ip?: boolean;
  user_agent?: string;
  path?: string;
  method?: string;
  status_code?: number;
  meta?: any;
}

export interface SystemLogRecord extends CreateSystemLogData {
  id: number;
  created_at: string;
}

export interface SystemLogQueryParams {
  module?: string;
  level?: string;
  operation?: string;
  client_ip?: string;
  start_date?: string;
  end_date?: string;
  limit?: number;
  offset?: number;
  search?: string;
}

export class SystemLogDAO extends BaseDAO {
  constructor() {
    super('system_logs');
  }

  /**
   * 创建系统日志记录
   */
  async createSystemLog(data: CreateSystemLogData): Promise<number> {
    this.logOperation('createSystemLog', { level: data.level, module: data.module });
    
    const logData = {
      timestamp: data.timestamp,
      level: data.level,
      message: data.message,
      module: data.module,
      operation: data.operation || null,
      client_ip: data.client_ip || null,
      is_private_ip: data.is_private_ip ? 1 : 0,
      user_agent: data.user_agent || null,
      path: data.path || null,
      method: data.method || null,
      status_code: data.status_code || null,
      meta: data.meta ? JSON.stringify(data.meta) : null
    };
    
    return await this.insert(logData);
  }

  /**
   * 批量创建系统日志记录
   */
  async batchCreateSystemLogs(logs: CreateSystemLogData[]): Promise<void> {
    this.logOperation('batchCreateSystemLogs', { count: logs.length });
    
    const logData = logs.map(log => ({
      timestamp: log.timestamp,
      level: log.level,
      message: log.message,
      module: log.module,
      operation: log.operation || null,
      client_ip: log.client_ip || null,
      is_private_ip: log.is_private_ip ? 1 : 0,
      user_agent: log.user_agent || null,
      path: log.path || null,
      method: log.method || null,
      status_code: log.status_code || null,
      meta: log.meta ? JSON.stringify(log.meta) : null
    }));
    
    await this.batchInsert(logData);
  }

  /**
   * 查询系统日志
   */
  async getSystemLogs(params: SystemLogQueryParams): Promise<{ logs: SystemLogRecord[], total: number }> {
    this.logOperation('getSystemLogs', params);
    
    let whereClause = 'WHERE 1=1';
    const whereParams: any[] = [];
    
    // 模块过滤
    if (params.module) {
      whereClause += ' AND module = ?';
      whereParams.push(params.module);
    }
    
    // 级别过滤
    if (params.level) {
      whereClause += ' AND level = ?';
      whereParams.push(params.level);
    }
    
    // 操作类型过滤
    if (params.operation) {
      whereClause += ' AND operation = ?';
      whereParams.push(params.operation);
    }
    
    // IP过滤
    if (params.client_ip) {
      whereClause += ' AND client_ip = ?';
      whereParams.push(params.client_ip);
    }
    
    // 时间范围过滤
    if (params.start_date) {
      whereClause += ' AND timestamp >= ?';
      whereParams.push(params.start_date);
    }
    
    if (params.end_date) {
      whereClause += ' AND timestamp <= ?';
      whereParams.push(params.end_date);
    }
    
    // 搜索过滤
    if (params.search) {
      whereClause += ' AND (message LIKE ? OR client_ip LIKE ? OR user_agent LIKE ? OR path LIKE ?)';
      const searchPattern = `%${params.search}%`;
      whereParams.push(searchPattern, searchPattern, searchPattern, searchPattern);
    }
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
    const countResult = await this.db.get<{ total: number }>(countSql, whereParams);
    const total = countResult?.total || 0;

    // 获取日志记录
    const orderClause = 'ORDER BY timestamp DESC';
    const limitClause = params.limit ? `LIMIT ${params.limit}` : '';
    const offsetClause = params.offset ? `OFFSET ${params.offset}` : '';

    const sql = `
      SELECT
        id, timestamp, level, message, module, operation,
        client_ip, is_private_ip, user_agent, path, method,
        status_code, meta, created_at
      FROM ${this.tableName}
      ${whereClause}
      ${orderClause}
      ${limitClause}
      ${offsetClause}
    `;

    const logs = await this.db.all<any>(sql, whereParams);

    // 解析meta字段
    const parsedLogs = logs.map((log: any) => ({
      ...log,
      is_private_ip: Boolean(log.is_private_ip),
      meta: log.meta ? JSON.parse(log.meta) : null
    }));
    
    return { logs: parsedLogs, total };
  }

  /**
   * 根据ID获取系统日志
   */
  async getSystemLogById(id: number): Promise<SystemLogRecord | null> {
    this.logOperation('getSystemLogById', { id });
    
    const sql = `
      SELECT 
        id, timestamp, level, message, module, operation,
        client_ip, is_private_ip, user_agent, path, method,
        status_code, meta, created_at
      FROM ${this.tableName} 
      WHERE id = ?
    `;
    
    const log = await this.db.get<any>(sql, [id]);

    if (!log) return null;

    return {
      ...log,
      is_private_ip: Boolean(log.is_private_ip),
      meta: log.meta ? JSON.parse(log.meta) : null
    };
  }

  /**
   * 删除指定时间之前的日志
   */
  async deleteLogsBefore(beforeDate: string): Promise<number> {
    this.logOperation('deleteLogsBefore', { beforeDate });
    
    const sql = `DELETE FROM ${this.tableName} WHERE timestamp < ?`;
    const result = await this.db.run(sql, [beforeDate]);

    return result.changes || 0;
  }

  /**
   * 清空所有日志
   */
  async clearAllLogs(): Promise<number> {
    this.logOperation('clearAllLogs');
    
    const sql = `DELETE FROM ${this.tableName}`;
    const result = await this.db.run(sql);

    return result.changes || 0;
  }

  /**
   * 获取日志统计信息
   */
  async getLogStats(): Promise<{
    total: number;
    byLevel: Record<string, number>;
    byModule: Record<string, number>;
    recentCount: number;
  }> {
    this.logOperation('getLogStats');
    
    // 总数
    const totalResult = await this.db.get<{ total: number }>(`SELECT COUNT(*) as total FROM ${this.tableName}`);
    const total = totalResult?.total || 0;

    // 按级别统计
    const levelStats = await this.db.all<{ level: string; count: number }>(`
      SELECT level, COUNT(*) as count
      FROM ${this.tableName}
      GROUP BY level
    `);
    const byLevel: Record<string, number> = {};
    levelStats.forEach((stat: { level: string; count: number }) => {
      byLevel[stat.level] = stat.count;
    });

    // 按模块统计
    const moduleStats = await this.db.all<{ module: string; count: number }>(`
      SELECT module, COUNT(*) as count
      FROM ${this.tableName}
      GROUP BY module
    `);
    const byModule: Record<string, number> = {};
    moduleStats.forEach((stat: { module: string; count: number }) => {
      byModule[stat.module] = stat.count;
    });

    // 最近24小时的日志数量
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const recentResult = await this.db.get<{ count: number }>(`
      SELECT COUNT(*) as count
      FROM ${this.tableName}
      WHERE timestamp >= ?
    `, [oneDayAgo]);
    const recentCount = recentResult?.count || 0;
    
    return {
      total,
      byLevel,
      byModule,
      recentCount
    };
  }
}
