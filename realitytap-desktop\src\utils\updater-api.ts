/**
 * 更新器 API 工具函数
 * 不依赖 Vue 组合式 API 上下文，可以在任何地方调用
 */

import { logger, LogModule } from '@/utils/logger/logger';

export interface UpdateInfo {
  version: string
  date?: string
  body?: string
  signature?: string
  file_size?: number // 添加文件大小字段
}

export interface DownloadProgress {
  downloaded: number
  total: number
  percentage: number
}

/**
 * 检查更新（独立函数）
 */
export async function checkForUpdates(silent = false): Promise<UpdateInfo | null> {
  try {
    // 动态导入 tauri-plugin-updater
    const { check } = await import('@tauri-apps/plugin-updater')
    
    const update = await check()
    
    if (update) {
      // 获取文件大小（从服务器响应的 rawJson 中获取）
      const fileSize = (update as any).rawJson?.file_size || 0

      const updateInfo: UpdateInfo = {
        version: update.version,
        date: update.date,
        body: update.body,
        file_size: fileSize,
      }
      
      if (!silent) {
        logger.info(LogModule.GENERAL, `发现新版本: ${update.version}`)
      }

      return updateInfo
    } else {
      if (!silent) {
        logger.info(LogModule.GENERAL, '当前已是最新版本')
      }
      return null
    }
  } catch (err) {
    logger.error(LogModule.GENERAL, '更新检查失败', err)
    throw new Error('Update check failed: ' + (err instanceof Error ? err.message : String(err)))
  }
}

/**
 * 下载并安装更新（独立函数）
 */
export async function downloadAndInstall(
  onProgress?: (progress: DownloadProgress) => void
): Promise<boolean> {
  try {
    // 动态导入 tauri-plugin-updater
    const { check } = await import('@tauri-apps/plugin-updater')
    const { relaunch } = await import('@tauri-apps/plugin-process')
    
    // 重新检查更新以获取 Update 对象
    const update = await check()
    if (!update) {
      throw new Error('无法获取更新信息')
    }
    
    // 下载更新
    await update.downloadAndInstall((progress) => {
      // 处理不同类型的进度事件
      if (progress.event === 'Progress' && progress.data) {
        const data = progress.data as any;
        const progressInfo: DownloadProgress = {
          downloaded: data.chunkLength || 0,
          total: data.contentLength || data.chunkLength || 0,
          percentage: data.contentLength ?
            Math.round((data.chunkLength / data.contentLength) * 100) : 0
        }

        if (onProgress) {
          onProgress(progressInfo)
        }
      }
    })
    
    // 显示重启通知
    logger.info(LogModule.GENERAL, '更新安装完成，即将重启应用')

    // 重启应用
    await relaunch()

    return true
  } catch (err) {
    logger.error(LogModule.GENERAL, '更新安装失败', err)
    throw new Error('Update installation failed: ' + (err instanceof Error ? err.message : String(err)))
  }
}

/**
 * 获取当前应用版本
 */
export async function getCurrentVersion(): Promise<string> {
  try {
    const { getVersion } = await import('@tauri-apps/api/app')
    return await getVersion()
  } catch (err) {
    logger.error(LogModule.GENERAL, '获取应用版本失败', err)
    return '1.0.0'
  }
}

/**
 * 检查是否有可用更新
 */
export async function hasAvailableUpdate(): Promise<boolean> {
  try {
    const updateInfo = await checkForUpdates(true)
    return updateInfo !== null
  } catch (err) {
    logger.error(LogModule.GENERAL, '检查更新失败', err)
    return false
  }
}

/**
 * 获取更新信息（如果有）
 */
export async function getUpdateInfo(): Promise<UpdateInfo | null> {
  try {
    return await checkForUpdates(true)
  } catch (err) {
    logger.error(LogModule.GENERAL, '获取更新信息失败', err)
    return null
  }
}
