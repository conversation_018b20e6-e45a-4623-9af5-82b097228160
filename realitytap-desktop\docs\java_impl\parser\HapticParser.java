package android.os.vibrator.realitytap.parser;

import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_METADATA;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_VERSION;

import android.text.TextUtils;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import android.os.vibrator.realitytap.he.RealityTapEffect;

/** @hide */
public class HapticParser {
    public static final String TAG = "HapticParser";

    public static RealityTapEffect parse(String json) throws JSONException {
        if (TextUtils.isEmpty(json)) {
            throw new JSONException("JSON is empty.");
        }

        JSONObject topNode = new JSONObject(json);
        JSONObject metaNode = topNode.getJSONObject(KEY_METADATA);
        if (!metaNode.has(KEY_VERSION)) {
            throw new JSONException("Haptic json lost version information.");
        }
        int version = metaNode.getInt(KEY_VERSION);
        Log.d(TAG, "Will start parsing haptic file with version: " + version);
        return JsonParserFactory.getParser(version).parse(topNode);
    }
}
