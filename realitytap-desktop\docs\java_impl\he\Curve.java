package android.os.vibrator.realitytap.he;

import android.annotation.NonNull;
import android.os.Parcel;
import android.os.Parcelable;

/** @hide */
public class Curve implements Parcelable {
    /** 包络点时间(ms) */
    private int time;

    /** 包络点强度 */
    private double intensity;

    /** 包络点频率 */
    private int frequency;

    public Curve() {}

    public Curve(int time, double intensity, int frequency) {
        this.time = time;
        this.intensity = intensity;
        this.frequency = frequency;
    }

    protected Curve(@NonNull Parcel in) {
        time = in.readInt();
        intensity = in.readDouble();
        frequency = in.readInt();
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeInt(time);
        dest.writeDouble(intensity);
        dest.writeInt(frequency);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final @NonNull Creator<Curve> CREATOR = new Creator<>() {
        @Override
        public Curve createFromParcel(@NonNull Parcel in) {
            return new Curve(in);
        }

        @Override
        public Curve[] newArray(int size) {
            return new Curve[size];
        }
    };

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }

    public double getIntensity() {
        return intensity;
    }

    public void setIntensity(double intensity) {
        this.intensity = intensity;
    }

    public int getFrequency() {
        return frequency;
    }

    public void setFrequency(int frequency) {
        this.frequency = frequency;
    }

    @NonNull
    public Curve copy() {
        Curve newCurve = new Curve();
        newCurve.setTime(this.time);
        newCurve.setIntensity(this.intensity);
        newCurve.setFrequency(this.frequency);
        return newCurve;
    }

    @NonNull
    @Override
    public String toString() {
        return "Curve{" +
                "time=" + time +
                ", intensity=" + intensity +
                ", frequency=" + frequency +
                '}';
    }
}
