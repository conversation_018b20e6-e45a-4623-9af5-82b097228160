import type { RenderableEvent } from "../types";
import { createSelectionGetters, type SelectionGetters } from "../actions/selection-actions";
import { createDurationGetters, type DurationGetters } from "../actions/duration-actions";

/**
 * 所有Getters的组合接口
 */
export interface AllGetters extends SelectionGetters, DurationGetters {
  // 可以在这里添加其他通用的getters
  eventsCount: () => number;
  hasEvents: () => boolean;
  getEventById: (eventId: string) => RenderableEvent | null;
}

/**
 * 创建所有Getters
 */
export function createAllGetters(state: {
  selectedEventId: string | null;
  selectedCurvePointIndex: number;
  events: RenderableEvent[];
  totalDuration: number;
  isDurationLockedByAudio: boolean;
}): AllGetters {
  // 创建各个模块的getters
  const selectionGetters = createSelectionGetters(state);
  const durationGetters = createDurationGetters(state);

  // 通用getters
  const commonGetters = {
    /**
     * 获取事件数量
     */
    eventsCount() {
      return state.events.length;
    },

    /**
     * 检查是否有事件
     */
    hasEvents() {
      return state.events.length > 0;
    },

    /**
     * 根据ID获取事件
     */
    getEventById(eventId: string) {
      return state.events.find((event) => event.id === eventId) || null;
    },
  };

  // 合并所有getters
  return {
    ...selectionGetters,
    ...durationGetters,
    ...commonGetters,
  };
}
