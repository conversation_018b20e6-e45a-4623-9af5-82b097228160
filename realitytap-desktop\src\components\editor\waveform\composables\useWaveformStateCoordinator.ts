import { ref, type Ref } from "vue";
import type { ScrollbarInst } from "naive-ui";
import type { RenderableEvent } from "@/types/haptic-editor";
import { waveformLogger } from "@/utils/logger/logger";

// 使用 ReturnType 来获取正确的 Store 类型
type FileWaveformEditorStore = ReturnType<typeof import("@/stores/haptics-editor-store").useFileWaveformEditorStore>;
type ProjectStore = ReturnType<typeof import("@/stores/haptics-project-store").useProjectStore>;

/**
 * 波形图状态协调器 Composable
 * 协调各个 composable 之间的状态，减少主组件的复杂度
 */

export interface StateCoordinatorConfig {
  // 基础 Props
  fileUuid: string;
  events: RenderableEvent[];
  totalEffectDuration: number;
  baselineDuration: number;
  availableParentWidth: number;
  audioAmplitudeData?: any | null;
  audioDuration?: number | null;
  showAudioWaveform?: boolean;
  isUsingCachedData?: boolean;
  amplitudeDisplayMode?: string;
  
  // Store 实例
  waveformStore: FileWaveformEditorStore;
  projectStore: ProjectStore;
  
  // DOM 引用
  graphContainer: Ref<HTMLDivElement | null>;
  canvasContainer: Ref<HTMLDivElement | null>;
  waveformCanvas: Ref<HTMLCanvasElement | null>;
  canvasCtx: Ref<CanvasRenderingContext2D | null>;
  horizontalScrollbarRef: Ref<ScrollbarInst | null>;
}

export interface CoordinatedState {
  // 基础状态
  isDragging: Ref<boolean>;
  draggedEvent: Ref<RenderableEvent | null>;
  draggingTarget: Ref<any>;
  draggedCurveIndex: Ref<number>;
  longPressTimer: Ref<number | null>;
  
  // 菜单状态
  isCanvasMenuVisible: Ref<boolean>;
  canvasMenuPosition: Ref<{ x: number; y: number }>;
  canvasMenuTimePosition: Ref<number>;
  contextMenuEventId: Ref<string | null>;
  
  // 键盘状态
  isFrequencyAdjustmentKeyPressed: Ref<boolean>;
  
  // 音频状态
  hasAudioData: Ref<boolean>;
  
  // 画布状态
  canvasWidth: Ref<number>;
  canvasHeight: Ref<number>;
  logicalCanvasWidth: Ref<number>;
  scrollLeftValue: Ref<number>;
  virtualScrollOffset: Ref<number>;
  
  // 工具提示状态
  currentDraggedFrequency: Ref<number>;
  currentDraggedRelativeFrequency: Ref<number>;
  currentDraggedTimeOffset: Ref<number>;
  currentDraggedIntensity: Ref<number>;
}

export interface StateCoordinator {
  state: CoordinatedState;
  updateStateFromComposables: (composableStates: Record<string, any>) => void;
  getStateSnapshot: () => Record<string, any>;
  resetAllState: () => void;
}

export function useWaveformStateCoordinator(_config: StateCoordinatorConfig): StateCoordinator {
  
  // 创建协调的状态
  const state: CoordinatedState = {
    // 拖拽状态
    isDragging: ref(false),
    draggedEvent: ref(null),
    draggingTarget: ref(null),
    draggedCurveIndex: ref(-1),
    longPressTimer: ref(null),
    
    // 菜单状态
    isCanvasMenuVisible: ref(false),
    canvasMenuPosition: ref({ x: 0, y: 0 }),
    canvasMenuTimePosition: ref(0),
    contextMenuEventId: ref(null),
    
    // 键盘状态
    isFrequencyAdjustmentKeyPressed: ref(false),
    
    // 音频状态
    hasAudioData: ref(false),
    
    // 画布状态
    canvasWidth: ref(0),
    canvasHeight: ref(0),
    logicalCanvasWidth: ref(0),
    scrollLeftValue: ref(0),
    virtualScrollOffset: ref(0),
    
    // 工具提示状态
    currentDraggedFrequency: ref(0),
    currentDraggedRelativeFrequency: ref(0),
    currentDraggedTimeOffset: ref(0),
    currentDraggedIntensity: ref(0),
  };

  // 从各个 composable 更新状态
  const updateStateFromComposables = (composableStates: Record<string, any>) => {
    // 更新拖拽状态
    if (composableStates.dragHandler) {
      const dragState = composableStates.dragHandler;
      state.isDragging.value = dragState.isDragging?.value ?? state.isDragging.value;
      state.draggedEvent.value = dragState.draggedEvent?.value ?? state.draggedEvent.value;
      state.draggingTarget.value = dragState.draggingTarget?.value ?? state.draggingTarget.value;
      state.draggedCurveIndex.value = dragState.draggedCurveIndex?.value ?? state.draggedCurveIndex.value;
      state.longPressTimer.value = dragState.longPressTimer?.value ?? state.longPressTimer.value;
      
      // 更新工具提示状态
      state.currentDraggedFrequency.value = dragState.currentDraggedFrequency?.value ?? state.currentDraggedFrequency.value;
      state.currentDraggedRelativeFrequency.value = dragState.currentDraggedRelativeFrequency?.value ?? state.currentDraggedRelativeFrequency.value;
      state.currentDraggedTimeOffset.value = dragState.currentDraggedTimeOffset?.value ?? state.currentDraggedTimeOffset.value;
      state.currentDraggedIntensity.value = dragState.currentDraggedIntensity?.value ?? state.currentDraggedIntensity.value;
    }

    // 更新菜单状态
    if (composableStates.contextMenu) {
      const menuState = composableStates.contextMenu;
      state.isCanvasMenuVisible.value = menuState.isCanvasMenuVisible?.value ?? state.isCanvasMenuVisible.value;
      state.canvasMenuPosition.value = menuState.canvasMenuPosition?.value ?? state.canvasMenuPosition.value;
      state.canvasMenuTimePosition.value = menuState.canvasMenuTimePosition?.value ?? state.canvasMenuTimePosition.value;
      state.contextMenuEventId.value = menuState.contextMenuEventId?.value ?? state.contextMenuEventId.value;
    }

    // 更新键盘状态
    if (composableStates.keyboard) {
      const keyboardState = composableStates.keyboard;
      state.isFrequencyAdjustmentKeyPressed.value = keyboardState.isFrequencyAdjustmentKeyPressed?.value ?? state.isFrequencyAdjustmentKeyPressed.value;
    }

    // 更新音频状态
    if (composableStates.audioWaveform) {
      const audioState = composableStates.audioWaveform;
      state.hasAudioData.value = audioState.hasAudioData?.value ?? state.hasAudioData.value;
    }

    // 更新画布状态
    if (composableStates.canvas) {
      const canvasState = composableStates.canvas;
      state.canvasWidth.value = canvasState.canvasWidth?.value ?? state.canvasWidth.value;
      state.canvasHeight.value = canvasState.canvasHeight?.value ?? state.canvasHeight.value;
      state.logicalCanvasWidth.value = canvasState.logicalCanvasWidth?.value ?? state.logicalCanvasWidth.value;
      state.scrollLeftValue.value = canvasState.scrollLeftValue?.value ?? state.scrollLeftValue.value;
      state.virtualScrollOffset.value = canvasState.virtualScrollOffset?.value ?? state.virtualScrollOffset.value;
    }
  };

  // 获取状态快照
  const getStateSnapshot = () => {
    return {
      isDragging: state.isDragging.value,
      draggedEvent: state.draggedEvent.value,
      draggingTarget: state.draggingTarget.value,
      draggedCurveIndex: state.draggedCurveIndex.value,
      isCanvasMenuVisible: state.isCanvasMenuVisible.value,
      canvasMenuPosition: state.canvasMenuPosition.value,
      canvasMenuTimePosition: state.canvasMenuTimePosition.value,
      contextMenuEventId: state.contextMenuEventId.value,
      isFrequencyAdjustmentKeyPressed: state.isFrequencyAdjustmentKeyPressed.value,
      hasAudioData: state.hasAudioData.value,
      canvasWidth: state.canvasWidth.value,
      canvasHeight: state.canvasHeight.value,
      logicalCanvasWidth: state.logicalCanvasWidth.value,
      scrollLeftValue: state.scrollLeftValue.value,
      virtualScrollOffset: state.virtualScrollOffset.value,
      currentDraggedFrequency: state.currentDraggedFrequency.value,
      currentDraggedRelativeFrequency: state.currentDraggedRelativeFrequency.value,
      currentDraggedTimeOffset: state.currentDraggedTimeOffset.value,
      currentDraggedIntensity: state.currentDraggedIntensity.value,
    };
  };

  // 重置所有状态
  const resetAllState = () => {
    waveformLogger.debug("重置所有状态");
    
    // 重置拖拽状态
    state.isDragging.value = false;
    state.draggedEvent.value = null;
    state.draggingTarget.value = null;
    state.draggedCurveIndex.value = -1;
    state.longPressTimer.value = null;
    
    // 重置菜单状态
    state.isCanvasMenuVisible.value = false;
    state.canvasMenuPosition.value = { x: 0, y: 0 };
    state.canvasMenuTimePosition.value = 0;
    state.contextMenuEventId.value = null;
    
    // 重置键盘状态
    state.isFrequencyAdjustmentKeyPressed.value = false;
    
    // 重置音频状态
    state.hasAudioData.value = false;
    
    // 重置画布状态
    state.canvasWidth.value = 0;
    state.canvasHeight.value = 0;
    state.logicalCanvasWidth.value = 0;
    state.scrollLeftValue.value = 0;
    state.virtualScrollOffset.value = 0;
    
    // 重置工具提示状态
    state.currentDraggedFrequency.value = 0;
    state.currentDraggedRelativeFrequency.value = 0;
    state.currentDraggedTimeOffset.value = 0;
    state.currentDraggedIntensity.value = 0;
  };

  return {
    state,
    updateStateFromComposables,
    getStateSnapshot,
    resetAllState,
  };
}
