import { logger } from '@/utils/logger.util';
import { spawn } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * 签名服务 - 处理更新包的签名生成和验证
 * 使用 Tauri CLI 的 signer 工具进行签名操作
 */
class SignatureService {
  private privateKeyPath: string;
  private privateKeyPassword: string;

  constructor() {
    // 从环境变量获取私钥配置
    this.privateKeyPath = process.env.TAURI_PRIVATE_KEY_PATH || '';
    this.privateKeyPassword = process.env.TAURI_KEY_PASSWORD || '';
  }

  /**
   * 检查签名环境是否配置正确
   */
  async isSigningConfigured(): Promise<boolean> {
    try {
      // 检查是否有私钥路径或私钥内容
      const hasPrivateKey = this.privateKeyPath || process.env.TAURI_PRIVATE_KEY;
      
      if (!hasPrivateKey) {
        logger.warn('Signing not configured: No private key found');
        return false;
      }

      // 如果是文件路径，检查文件是否存在
      if (this.privateKeyPath) {
        try {
          await fs.access(this.privateKeyPath);
        } catch (error) {
          logger.warn('Signing not configured: Private key file not found', {
            path: this.privateKeyPath,
          });
          return false;
        }
      }

      // 检查 Tauri CLI 是否可用
      const tauriAvailable = await this.checkTauriCLI();
      if (!tauriAvailable) {
        logger.warn('Signing not configured: Tauri CLI not available');
        return false;
      }

      logger.info('Signing environment configured successfully');
      return true;
    } catch (error) {
      logger.error('Failed to check signing configuration', { error });
      return false;
    }
  }

  /**
   * 为文件生成签名
   * @param filePath 要签名的文件路径
   * @returns 签名字符串，如果签名失败返回 null
   */
  async signFile(filePath: string): Promise<string | null> {
    try {
      const isConfigured = await this.isSigningConfigured();
      if (!isConfigured) {
        logger.warn('Skipping file signing: Signing not configured', { filePath });
        return null;
      }

      // 检查文件是否存在
      try {
        await fs.access(filePath);
      } catch (error) {
        logger.error('File not found for signing', { filePath, error });
        return null;
      }

      logger.info('Generating signature for file', { filePath });

      // 使用 Tauri CLI 生成签名
      const signature = await this.generateSignatureWithTauriCLI(filePath);
      
      if (signature) {
        logger.info('File signature generated successfully', { 
          filePath, 
          signatureLength: signature.length 
        });
        return signature;
      } else {
        logger.error('Failed to generate signature', { filePath });
        return null;
      }
    } catch (error) {
      logger.error('Error during file signing', { filePath, error });
      return null;
    }
  }

  /**
   * 批量为文件生成签名
   * @param filePaths 文件路径数组
   * @returns 文件路径到签名的映射
   */
  async signFiles(filePaths: string[]): Promise<Record<string, string | null>> {
    const results: Record<string, string | null> = {};

    for (const filePath of filePaths) {
      results[filePath] = await this.signFile(filePath);
    }

    return results;
  }

  /**
   * 检查 Tauri CLI 是否可用
   */
  private async checkTauriCLI(): Promise<boolean> {
    return new Promise((resolve) => {
      const child = spawn('tauri', ['--version'], { stdio: 'pipe' });
      
      child.on('close', (code) => {
        resolve(code === 0);
      });

      child.on('error', () => {
        resolve(false);
      });

      // 设置超时
      setTimeout(() => {
        child.kill();
        resolve(false);
      }, 5000);
    });
  }

  /**
   * 使用 Tauri CLI 生成签名
   */
  private async generateSignatureWithTauriCLI(filePath: string): Promise<string | null> {
    return new Promise((resolve) => {
      const args = ['signer', 'sign'];
      
      // 添加私钥参数
      if (this.privateKeyPath) {
        args.push('-k', this.privateKeyPath);
      } else if (process.env.TAURI_PRIVATE_KEY) {
        // 如果是私钥内容而不是文件路径，需要创建临时文件
        // 这里简化处理，建议使用文件路径
        logger.warn('Using private key content directly is not recommended for security');
      }

      // 添加密码参数
      if (this.privateKeyPassword) {
        args.push('-p', this.privateKeyPassword);
      }

      // 添加文件路径
      args.push(filePath);

      logger.debug('Executing Tauri signer command', { args: args.filter(arg => arg !== this.privateKeyPassword) });

      const child = spawn('tauri', args, { 
        stdio: 'pipe',
        env: {
          ...process.env,
          TAURI_PRIVATE_KEY: process.env.TAURI_PRIVATE_KEY,
          TAURI_KEY_PASSWORD: this.privateKeyPassword,
        }
      });

      let stdout = '';
      let stderr = '';

      child.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        if (code === 0) {
          // 从输出中提取签名
          const signature = this.extractSignatureFromOutput(stdout);
          resolve(signature);
        } else {
          logger.error('Tauri signer failed', { 
            code, 
            stdout, 
            stderr: stderr.replace(this.privateKeyPassword, '***') 
          });
          resolve(null);
        }
      });

      child.on('error', (error) => {
        logger.error('Failed to execute Tauri signer', { error });
        resolve(null);
      });

      // 设置超时
      setTimeout(() => {
        child.kill();
        logger.error('Tauri signer timeout');
        resolve(null);
      }, 30000); // 30秒超时
    });
  }

  /**
   * 从 Tauri CLI 输出中提取签名
   */
  private extractSignatureFromOutput(output: string): string | null {
    try {
      // Tauri CLI 通常会输出签名到标准输出
      // 签名通常是 base64 编码的字符串
      const lines = output.trim().split('\n');
      
      // 查找包含签名的行
      for (const line of lines) {
        const trimmedLine = line.trim();
        // 签名通常是长的 base64 字符串
        if (trimmedLine.length > 50 && /^[A-Za-z0-9+/=]+$/.test(trimmedLine)) {
          return trimmedLine;
        }
      }

      // 如果没有找到明显的签名，返回最后一行非空内容
      const lastLine = lines[lines.length - 1]?.trim();
      if (lastLine && lastLine.length > 10) {
        return lastLine;
      }

      return null;
    } catch (error) {
      logger.error('Failed to extract signature from output', { output, error });
      return null;
    }
  }

  /**
   * 验证签名（可选功能）
   * @param filePath 文件路径
   * @param signature 签名
   * @param publicKey 公钥
   */
  async verifySignature(filePath: string, signature: string, publicKey: string): Promise<boolean> {
    try {
      // 这里可以实现签名验证逻辑
      // 由于 Tauri CLI 主要用于签名而不是验证，这里暂时返回 true
      logger.info('Signature verification requested', { filePath, signatureLength: signature.length });
      return true;
    } catch (error) {
      logger.error('Signature verification failed', { filePath, error });
      return false;
    }
  }
}

// 导出单例实例
export const signatureService = new SignatureService();
