// Application data management commands
use crate::{
    error::{Error, Result},
    models,
    utils::constants::*,
    utils::validation::validate_window_settings,
};
use chrono::Utc;
use std::env;
use std::fs::{self, File};
use std::io::Write;
use std::path::PathBuf;
use std::sync::Mutex;
use tauri;

// Global mutex for thread-safe app data file writing
static RECENT_PROJECTS_WRITE_MUTEX: Mutex<()> = Mutex::new(());
static WINDOW_SETTINGS_WRITE_MUTEX: Mutex<()> = Mutex::new(());

/// Get the application data directory
pub fn get_app_data_dir() -> Result<PathBuf> {
    let home_dir = env::var("LOCALAPPDATA")
        .or_else(|_| env::var("HOME"))
        .map_err(|e| Error::Io(format!("无法获取用户目录: {}", e)))?;
    let app_dir = PathBuf::from(home_dir).join("RealityTap");

    fs::create_dir_all(&app_dir).map_err(|e| Error::Io(format!("无法创建应用程序目录: {}", e)))?;

    Ok(app_dir)
}

/// Get the path to recent projects file
fn get_recent_projects_path() -> Result<PathBuf> {
    Ok(get_app_data_dir()?.join(RECENT_PROJECTS_FILE))
}

/// Get the path to window settings file
fn get_window_settings_path() -> Result<PathBuf> {
    Ok(get_app_data_dir()?.join(WINDOW_SETTINGS_FILE))
}

/// Read recent projects list from file
fn read_recent_projects() -> Result<models::RecentProjectsList> {
    let path = get_recent_projects_path()?;

    if !path.exists() {
        log::info!("最近项目文件不存在，返回默认列表");
        return Ok(models::RecentProjectsList::default());
    }

    let file = match fs::File::open(&path) {
        Ok(f) => f,
        Err(e) => {
            log::error!("无法打开最近项目文件 {:?}: {}", path, e);
            return Err(Error::Io(format!("无法打开最近项目文件: {}", e)));
        }
    };

    match serde_json::from_reader(file) {
        Ok(list) => Ok(list),
        Err(e) => {
            log::error!("解析最近项目文件失败: {}", e);
            Err(Error::Io(format!("解析最近项目文件失败: {}", e)))
        }
    }
}

/// Save recent projects list to file with robust protection
fn save_recent_projects(list: &models::RecentProjectsList) -> Result<()> {
    let path = get_recent_projects_path()?;

    if let Some(parent) = path.parent() {
        fs::create_dir_all(parent).map_err(|e| Error::Io(format!("无法创建目录: {}", e)))?;
    }

    // Thread-safe writing with JSON validation and retry mechanism
    let _lock = RECENT_PROJECTS_WRITE_MUTEX.lock().map_err(|e| {
        Error::Io(format!("Failed to acquire recent projects write lock: {}", e))
    })?;

    const MAX_RETRIES: u32 = 3;
    let mut last_error = None;

    for attempt in 1..=MAX_RETRIES {
        match write_recent_projects_with_validation(list, &path) {
            Ok(()) => {
                log::info!("Successfully wrote recent projects file on attempt {}", attempt);
                return Ok(());
            }
            Err(e) => {
                log::warn!("Attempt {} failed to write recent projects file: {}", attempt, e);
                last_error = Some(e);

                if attempt < MAX_RETRIES {
                    // Wait a bit before retrying
                    std::thread::sleep(std::time::Duration::from_millis(100));
                }
            }
        }
    }

    // All retries failed
    Err(last_error.unwrap_or_else(|| {
        Error::Io("Failed to write recent projects file after all retries".to_string())
    }))
}

/// Write recent projects with JSON validation
fn write_recent_projects_with_validation(list: &models::RecentProjectsList, file_path: &PathBuf) -> Result<()> {
    // Step 1: Serialize to JSON string
    let json_content = serde_json::to_string_pretty(list)
        .map_err(|e| Error::Io(format!("Failed to serialize recent projects: {}", e)))?;

    // Step 2: Validate JSON format by parsing it back
    let _: serde_json::Value = serde_json::from_str(&json_content)
        .map_err(|e| Error::Io(format!("Generated JSON is invalid: {}", e)))?;

    // Step 3: Additional validation - try to deserialize back to RecentProjectsList
    let _: models::RecentProjectsList = serde_json::from_str(&json_content)
        .map_err(|e| Error::Io(format!("Generated JSON cannot be deserialized back to RecentProjectsList: {}", e)))?;

    // Step 4: Write to file with explicit UTF-8 encoding
    let mut file = File::create(file_path)
        .map_err(|e| Error::Io(format!("Failed to create recent projects file: {}", e)))?;

    // Write UTF-8 content (Rust strings are already UTF-8)
    // Note: We don't add BOM for JSON files as it's not recommended for JSON format
    file.write_all(json_content.as_bytes())
        .map_err(|e| Error::Io(format!("Failed to write recent projects file: {}", e)))?;

    // Step 5: Ensure all data is written to disk
    file.flush()
        .map_err(|e| Error::Io(format!("Failed to flush recent projects file: {}", e)))?;

    file.sync_all()
        .map_err(|e| Error::Io(format!("Failed to sync recent projects file: {}", e)))?;

    // Step 6: Final validation - read back and verify
    drop(file); // Close the file handle

    let verification_content = std::fs::read_to_string(file_path)
        .map_err(|e| Error::Io(format!("Failed to read back recent projects file for verification: {}", e)))?;

    let _: models::RecentProjectsList = serde_json::from_str(&verification_content)
        .map_err(|e| Error::Io(format!("Written file failed verification: {}", e)))?;

    Ok(())
}

/// Add or update a project in recent projects list
pub fn add_to_recent_projects(project: &models::Project, project_dir_path: &str) -> Result<()> {
    let mut recent_list = read_recent_projects()?;

    let new_entry = models::RecentProject {
        project_uuid: Some(project.project_uuid),
        project_name: project.project_name.clone(),
        project_path: project_dir_path.to_string(),
        last_accessed: Utc::now(),
        file_count: project.files.len(),
        icon: "📂".to_string(),
    };

    // Remove old record based on project_uuid if exists
    recent_list
        .projects
        .retain(|p_entry| match p_entry.project_uuid {
            Some(existing_uuid) => existing_uuid != project.project_uuid,
            None => true,
        });

    // Add new record at the beginning
    recent_list.projects.insert(0, new_entry);

    // Keep only the most recent projects
    if recent_list.projects.len() > MAX_RECENT_PROJECTS {
        recent_list.projects.truncate(MAX_RECENT_PROJECTS);
    }

    save_recent_projects(&recent_list)
}

/// Read window settings from file
fn read_window_settings() -> Result<models::WindowSettings> {
    let path = get_window_settings_path()?;

    if !path.exists() {
        log::info!("窗口设置文件不存在，返回默认设置");
        return Ok(models::WindowSettings::default());
    }

    let file = match fs::File::open(&path) {
        Ok(f) => f,
        Err(e) => {
            log::error!("无法打开窗口设置文件 {:?}: {}", path, e);
            return Err(Error::Io(format!("无法打开窗口设置文件: {}", e)));
        }
    };

    match serde_json::from_reader(file) {
        Ok(settings) => Ok(settings),
        Err(e) => {
            log::error!("解析窗口设置文件失败: {}", e);
            Err(Error::Io(format!("解析窗口设置文件失败: {}", e)))
        }
    }
}

/// Save window settings to file with robust protection
fn save_window_settings(settings: &models::WindowSettings) -> Result<()> {
    let path = get_window_settings_path()?;

    if let Some(parent) = path.parent() {
        fs::create_dir_all(parent).map_err(|e| Error::Io(format!("无法创建目录: {}", e)))?;
    }

    // Thread-safe writing with JSON validation and retry mechanism
    let _lock = WINDOW_SETTINGS_WRITE_MUTEX.lock().map_err(|e| {
        Error::Io(format!("Failed to acquire window settings write lock: {}", e))
    })?;

    const MAX_RETRIES: u32 = 3;
    let mut last_error = None;

    for attempt in 1..=MAX_RETRIES {
        match write_window_settings_with_validation(settings, &path) {
            Ok(()) => {
                log::info!("Successfully wrote window settings file on attempt {}", attempt);
                return Ok(());
            }
            Err(e) => {
                log::warn!("Attempt {} failed to write window settings file: {}", attempt, e);
                last_error = Some(e);

                if attempt < MAX_RETRIES {
                    // Wait a bit before retrying
                    std::thread::sleep(std::time::Duration::from_millis(100));
                }
            }
        }
    }

    // All retries failed
    Err(last_error.unwrap_or_else(|| {
        Error::Io("Failed to write window settings file after all retries".to_string())
    }))
}

/// Write window settings with JSON validation
fn write_window_settings_with_validation(settings: &models::WindowSettings, file_path: &PathBuf) -> Result<()> {
    // Step 1: Serialize to JSON string
    let json_content = serde_json::to_string_pretty(settings)
        .map_err(|e| Error::Io(format!("Failed to serialize window settings: {}", e)))?;

    // Step 2: Validate JSON format by parsing it back
    let _: serde_json::Value = serde_json::from_str(&json_content)
        .map_err(|e| Error::Io(format!("Generated JSON is invalid: {}", e)))?;

    // Step 3: Additional validation - try to deserialize back to WindowSettings
    let _: models::WindowSettings = serde_json::from_str(&json_content)
        .map_err(|e| Error::Io(format!("Generated JSON cannot be deserialized back to WindowSettings: {}", e)))?;

    // Step 4: Write to file with explicit UTF-8 encoding
    let mut file = File::create(file_path)
        .map_err(|e| Error::Io(format!("Failed to create window settings file: {}", e)))?;

    // Write UTF-8 content (Rust strings are already UTF-8)
    // Note: We don't add BOM for JSON files as it's not recommended for JSON format
    file.write_all(json_content.as_bytes())
        .map_err(|e| Error::Io(format!("Failed to write window settings file: {}", e)))?;

    // Step 5: Ensure all data is written to disk
    file.flush()
        .map_err(|e| Error::Io(format!("Failed to flush window settings file: {}", e)))?;

    file.sync_all()
        .map_err(|e| Error::Io(format!("Failed to sync window settings file: {}", e)))?;

    // Step 6: Final validation - read back and verify
    drop(file); // Close the file handle

    let verification_content = std::fs::read_to_string(file_path)
        .map_err(|e| Error::Io(format!("Failed to read back window settings file for verification: {}", e)))?;

    let _: models::WindowSettings = serde_json::from_str(&verification_content)
        .map_err(|e| Error::Io(format!("Written file failed verification: {}", e)))?;

    Ok(())
}

// === Tauri Commands ===

#[tauri::command]
pub async fn get_recent_projects() -> Result<models::RecentProjectsList> {
    log::info!("获取最近项目列表");
    read_recent_projects()
}

#[tauri::command]
pub async fn clear_recent_projects() -> Result<()> {
    log::info!("清空最近项目列表");
    save_recent_projects(&models::RecentProjectsList::default())
}

#[tauri::command]
pub async fn remove_recent_project(project_dir_path: String) -> Result<models::RecentProjectsList> {
    log::info!("从最近项目中移除: {}", project_dir_path);
    let mut list = read_recent_projects()?;
    list.projects.retain(|p| p.project_path != project_dir_path);
    save_recent_projects(&list)?;
    Ok(list)
}

#[tauri::command]
pub async fn get_window_settings() -> Result<models::WindowSettings> {
    log::info!("获取窗口设置");
    read_window_settings()
}

#[tauri::command]
pub async fn save_window_settings_command(settings: models::WindowSettings) -> Result<()> {
    validate_window_settings(&settings)?;

    let mut validated_settings = settings;
    validated_settings.last_saved = chrono::Utc::now();

    save_window_settings(&validated_settings)
}
