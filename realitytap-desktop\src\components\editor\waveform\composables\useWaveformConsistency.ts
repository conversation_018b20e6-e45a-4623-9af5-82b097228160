/**
 * 波形数据一致性检查
 * 确保数据状态的一致性，提供错误检测和自动修复功能
 */

import { ref, computed } from "vue";
import type { RenderableEvent, RenderableContinuousEvent } from "@/types/haptic-editor";
import { waveformLogger } from "@/utils/logger/logger";

/**
 * 一致性检查配置
 */
export interface ConsistencyConfig {
  enableAutoFix?: boolean;     // 是否启用自动修复
  enableLogging?: boolean;     // 是否启用日志
  checkInterval?: number;      // 检查间隔（ms）
  enableRealTimeCheck?: boolean; // 是否启用实时检查
}

/**
 * 一致性问题类型
 */
export type ConsistencyIssueType = 
  | 'selectedEventNotFound'      // 选中的事件不存在
  | 'selectedCurvePointInvalid'  // 选中的曲线点无效
  | 'eventDataCorrupted'         // 事件数据损坏
  | 'duplicateEventIds'          // 重复的事件ID
  | 'invalidTimeSequence'        // 无效的时间序列
  | 'missingRequiredFields'      // 缺少必需字段
  | 'invalidEventType'           // 无效的事件类型
  | 'storeStateMismatch';        // Store状态不匹配

/**
 * 一致性问题
 */
export interface ConsistencyIssue {
  type: ConsistencyIssueType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  eventId?: string;
  curvePointIndex?: number;
  details?: any;
  autoFixable?: boolean;
  timestamp: number;
}

/**
 * 一致性检查结果
 */
export interface ConsistencyCheckResult {
  isConsistent: boolean;
  issues: ConsistencyIssue[];
  fixedIssues: ConsistencyIssue[];
  checkTime: number;
  eventsChecked: number;
}

/**
 * 波形数据一致性检查Hook
 */
export function useWaveformConsistency(
  waveformStore: any,
  config: ConsistencyConfig = {}
) {
  const {
    enableAutoFix = true,
    enableLogging = false,
    checkInterval = 5000, // 5秒检查一次
    enableRealTimeCheck = true,
  } = config;
  
  // 检查历史
  const checkHistory = ref<ConsistencyCheckResult[]>([]);
  const lastCheckResult = ref<ConsistencyCheckResult | null>(null);
  
  // 统计信息
  const stats = ref({
    totalChecks: 0,
    totalIssuesFound: 0,
    totalIssuesFixed: 0,
    lastCheckTime: 0,
    averageCheckTime: 0,
  });
  
  /**
   * 检查选中事件的一致性
   */
  const checkSelectedEventConsistency = (events: RenderableEvent[]): ConsistencyIssue[] => {
    const issues: ConsistencyIssue[] = [];
    const selectedEventId = waveformStore.selectedEventId;
    const selectedCurvePointIndex = waveformStore.selectedCurvePointIndex;
    
    // 检查选中事件是否存在
    if (selectedEventId) {
      const selectedEvent = events.find(e => e.id === selectedEventId);

      if (!selectedEvent) {
        issues.push({
          type: 'selectedEventNotFound',
          severity: 'high',
          message: `选中的事件 ${selectedEventId} 不存在于事件列表中`,
          eventId: selectedEventId,
          autoFixable: true,
          timestamp: Date.now(),
        });
      } else {
        // 检查选中的曲线点
        if (selectedCurvePointIndex >= 0) {
          if (selectedEvent.type !== 'continuous') {
            issues.push({
              type: 'selectedCurvePointInvalid',
              severity: 'medium',
              message: `非连续事件不应该有选中的曲线点`,
              eventId: selectedEventId,
              curvePointIndex: selectedCurvePointIndex,
              autoFixable: true,
              timestamp: Date.now(),
            });
          } else {
            // 类型断言确保TypeScript知道这是连续事件
            const continuousEvent = selectedEvent as RenderableContinuousEvent;
            if (selectedCurvePointIndex >= continuousEvent.curves.length) {
              issues.push({
                type: 'selectedCurvePointInvalid',
                severity: 'medium',
                message: `选中的曲线点索引 ${selectedCurvePointIndex} 超出范围`,
                eventId: selectedEventId,
                curvePointIndex: selectedCurvePointIndex,
                autoFixable: true,
                timestamp: Date.now(),
              });
            }
          }
        }
      }
    }
    
    return issues;
  };
  
  /**
   * 检查事件数据的完整性
   */
  const checkEventDataIntegrity = (events: RenderableEvent[]): ConsistencyIssue[] => {
    const issues: ConsistencyIssue[] = [];
    const eventIds = new Set<string>();
    
    events.forEach((event, index) => {
      // 检查重复ID
      if (eventIds.has(event.id)) {
        issues.push({
          type: 'duplicateEventIds',
          severity: 'critical',
          message: `发现重复的事件ID: ${event.id}`,
          eventId: event.id,
          autoFixable: false,
          timestamp: Date.now(),
        });
      }
      eventIds.add(event.id);
      
      // 检查必需字段
      if (!event.id || !event.type || typeof event.startTime !== 'number') {
        issues.push({
          type: 'missingRequiredFields',
          severity: 'critical',
          message: `事件 ${event.id || index} 缺少必需字段`,
          eventId: event.id,
          details: { event },
          autoFixable: false,
          timestamp: Date.now(),
        });
      }
      
      // 检查事件类型特定的字段
      if (event.type === 'transient') {
        if (typeof event.intensity !== 'number' ||
            typeof event.frequency !== 'number' ||
            typeof event.peakTime !== 'number') {
          issues.push({
            type: 'missingRequiredFields',
            severity: 'high',
            message: `瞬态事件 ${event.id} 缺少必需字段`,
            eventId: event.id,
            autoFixable: false,
            timestamp: Date.now(),
          });
        }
      } else if (event.type === 'continuous') {
        if (typeof event.duration !== 'number' ||
            typeof event.eventIntensity !== 'number' ||
            typeof event.eventFrequency !== 'number' ||
            !Array.isArray(event.curves)) {
          issues.push({
            type: 'missingRequiredFields',
            severity: 'high',
            message: `连续事件 ${event.id} 缺少必需字段`,
            eventId: event.id,
            autoFixable: false,
            timestamp: Date.now(),
          });
        }
      } else {
        // 使用类型断言避免TypeScript的never类型推断
        const unknownEvent = event as any;
        issues.push({
          type: 'invalidEventType',
          severity: 'critical',
          message: `无效的事件类型: ${unknownEvent.type}`,
          eventId: unknownEvent.id,
          autoFixable: false,
          timestamp: Date.now(),
        });
      }
      
      // 检查时间序列
      if (event.startTime < 0) {
        issues.push({
          type: 'invalidTimeSequence',
          severity: 'medium',
          message: `事件 ${event.id} 的开始时间为负数`,
          eventId: event.id,
          autoFixable: false,
          timestamp: Date.now(),
        });
      }
    });
    
    return issues;
  };
  
  /**
   * 检查Store状态一致性
   */
  const checkStoreStateConsistency = (events: RenderableEvent[]): ConsistencyIssue[] => {
    const issues: ConsistencyIssue[] = [];
    
    // 检查Store中的events与传入的events是否一致
    const storeEvents = waveformStore.events || [];
    
    if (storeEvents.length !== events.length) {
      issues.push({
        type: 'storeStateMismatch',
        severity: 'medium',
        message: `Store中的事件数量 (${storeEvents.length}) 与传入的事件数量 (${events.length}) 不一致`,
        autoFixable: true,
        timestamp: Date.now(),
      });
    }
    
    // 检查事件ID是否一致
    const storeEventIds = new Set(storeEvents.map((e: any) => e.id));
    const inputEventIds = new Set(events.map(e => e.id));
    
    const missingInStore = events.filter(e => !storeEventIds.has(e.id));
    const extraInStore = storeEvents.filter((e: any) => !inputEventIds.has(e.id));
    
    if (missingInStore.length > 0 || extraInStore.length > 0) {
      issues.push({
        type: 'storeStateMismatch',
        severity: 'medium',
        message: `Store中的事件与传入的事件不一致`,
        details: {
          missingInStore: missingInStore.map(e => e.id),
          extraInStore: extraInStore.map((e: any) => e.id),
        },
        autoFixable: true,
        timestamp: Date.now(),
      });
    }
    
    return issues;
  };
  
  /**
   * 自动修复问题
   */
  const autoFixIssues = (issues: ConsistencyIssue[]): ConsistencyIssue[] => {
    if (!enableAutoFix) return [];
    
    const fixedIssues: ConsistencyIssue[] = [];
    
    issues.forEach(issue => {
      if (!issue.autoFixable) return;
      
      try {
        switch (issue.type) {
          case 'selectedEventNotFound':
            // 清除无效的选中事件
            waveformStore.selectEvent(null);
            fixedIssues.push(issue);
            break;
            
          case 'selectedCurvePointInvalid':
            // 重置曲线点选择
            waveformStore.selectCurvePoint(-1);
            fixedIssues.push(issue);
            break;
            
          case 'storeStateMismatch':
            // 同步Store状态（这里需要根据实际情况决定同步方向）
            // 暂时不自动修复，需要手动处理
            break;
        }
        
        if (enableLogging) {
          waveformLogger.debug(`Auto-fixed issue:`, issue);
        }
      } catch (error) {
        if (enableLogging) {
          waveformLogger.warn(`Failed to auto-fix issue: ${issue.type}`, { issue, error });
        }
      }
    });
    
    return fixedIssues;
  };
  
  /**
   * 执行完整的一致性检查
   */
  const performConsistencyCheck = (events: RenderableEvent[]): ConsistencyCheckResult => {
    const startTime = performance.now();
    stats.value.totalChecks++;
    
    const allIssues: ConsistencyIssue[] = [
      ...checkSelectedEventConsistency(events),
      ...checkEventDataIntegrity(events),
      ...checkStoreStateConsistency(events),
    ];
    
    const fixedIssues = autoFixIssues(allIssues);
    const remainingIssues = allIssues.filter(issue => 
      !fixedIssues.some(fixed => fixed === issue)
    );
    
    const checkTime = performance.now() - startTime;
    stats.value.totalIssuesFound += allIssues.length;
    stats.value.totalIssuesFixed += fixedIssues.length;
    stats.value.lastCheckTime = checkTime;
    stats.value.averageCheckTime = 
      (stats.value.averageCheckTime * (stats.value.totalChecks - 1) + checkTime) / stats.value.totalChecks;
    
    const result: ConsistencyCheckResult = {
      isConsistent: remainingIssues.length === 0,
      issues: remainingIssues,
      fixedIssues,
      checkTime,
      eventsChecked: events.length,
    };
    
    lastCheckResult.value = result;
    
    // 保存检查历史（最多保留10次）
    checkHistory.value.push(result);
    if (checkHistory.value.length > 10) {
      checkHistory.value.shift();
    }
    
    if (enableLogging && (allIssues.length > 0 || fixedIssues.length > 0)) {
      waveformLogger.debug(`Check completed:`, {
        totalIssues: allIssues.length,
        fixedIssues: fixedIssues.length,
        remainingIssues: remainingIssues.length,
        checkTime: checkTime.toFixed(2) + 'ms',
      });
    }
    
    return result;
  };
  
  /**
   * 获取统计信息
   */
  const getConsistencyStats = () => {
    return {
      ...stats.value,
      successRate: stats.value.totalChecks > 0 
        ? ((stats.value.totalChecks - stats.value.totalIssuesFound) / stats.value.totalChecks * 100).toFixed(2) + '%'
        : '100%',
      fixRate: stats.value.totalIssuesFound > 0
        ? (stats.value.totalIssuesFixed / stats.value.totalIssuesFound * 100).toFixed(2) + '%'
        : '0%',
    };
  };
  
  /**
   * 重置统计信息
   */
  const resetConsistencyStats = () => {
    stats.value = {
      totalChecks: 0,
      totalIssuesFound: 0,
      totalIssuesFixed: 0,
      lastCheckTime: 0,
      averageCheckTime: 0,
    };
    checkHistory.value = [];
    lastCheckResult.value = null;
  };
  
  return {
    // 主要功能
    performConsistencyCheck,
    
    // 状态访问
    lastCheckResult: computed(() => lastCheckResult.value),
    checkHistory: computed(() => checkHistory.value),
    
    // 统计信息
    getConsistencyStats,
    consistencyStats: computed(() => getConsistencyStats()),
    
    // 管理功能
    resetConsistencyStats,
    
    // 配置信息
    config: computed(() => ({
      enableAutoFix,
      enableLogging,
      checkInterval,
      enableRealTimeCheck,
    })),
  };
}
