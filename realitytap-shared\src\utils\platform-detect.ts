import type {
  Platform,
  Architecture,
  PlatformInfo,
  PlatformDetectionResult,
  PlatformCompatibility
} from '../types/platform';
import { 
  PLATFORM_USER_AGENT_PATTERNS, 
  ARCHITECTURE_USER_AGENT_PATTERNS,
  PLATFORM_COMPATIBILITY_MATRIX,
  DEFAULT_PLATFORM_CONFIG 
} from '../constants/platforms';

/**
 * 从用户代理字符串检测平台
 */
export function detectPlatformFromUserAgent(userAgent: string): Platform | null {
  for (const [platform, patterns] of Object.entries(PLATFORM_USER_AGENT_PATTERNS)) {
    for (const pattern of patterns) {
      if (pattern.test(userAgent)) {
        return platform as Platform;
      }
    }
  }
  return null;
}

/**
 * 从用户代理字符串检测架构
 */
export function detectArchitectureFromUserAgent(userAgent: string): Architecture | null {
  for (const [architecture, patterns] of Object.entries(ARCHITECTURE_USER_AGENT_PATTERNS)) {
    for (const pattern of patterns) {
      if (pattern.test(userAgent)) {
        return architecture as Architecture;
      }
    }
  }
  return null;
}

/**
 * 在浏览器环境中检测平台信息
 */
export function detectPlatformBrowser(): PlatformInfo {
  // 安全检查 navigator 对象是否存在
  if (typeof navigator === 'undefined') {
    throw new Error('Navigator object not available in current environment');
  }

  const userAgent = navigator.userAgent;
  const platform = detectPlatformFromUserAgent(userAgent) || DEFAULT_PLATFORM_CONFIG.platform;
  const architecture = detectArchitectureFromUserAgent(userAgent) || DEFAULT_PLATFORM_CONFIG.architecture;

  return {
    platform,
    architecture,
    osVersion: getOSVersion(userAgent),
    locale: navigator.language,
  };
}

/**
 * 在 Node.js 环境中检测平台信息
 */
export function detectPlatformNode(): PlatformInfo {
  if (typeof (globalThis as any).process === 'undefined') {
    throw new Error('Node.js process object not available');
  }

  const platformMap: Record<string, Platform> = {
    win32: 'windows',
    darwin: 'macos',
    linux: 'linux',
  };

  const archMap: Record<string, Architecture> = {
    x64: 'x86_64',
    arm64: 'aarch64',
    ia32: 'x86',
    x32: 'x86',
  };

  const proc = (globalThis as any).process;
  const platform = platformMap[proc.platform] || DEFAULT_PLATFORM_CONFIG.platform;
  const architecture = archMap[proc.arch] || DEFAULT_PLATFORM_CONFIG.architecture;

  return {
    platform,
    architecture,
    osVersion: proc.platform,
    kernelVersion: proc.version,
  };
}

/**
 * 通用平台检测函数
 */
export function detectPlatform(): PlatformInfo {
  // 检测环境
  if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
    // 浏览器环境
    return detectPlatformBrowser();
  } else if (typeof (globalThis as any).process !== 'undefined' && (globalThis as any).process.versions?.node) {
    // Node.js 环境
    return detectPlatformNode();
  } else {
    // 未知环境，返回默认配置
    return {
      platform: DEFAULT_PLATFORM_CONFIG.platform,
      architecture: DEFAULT_PLATFORM_CONFIG.architecture,
    };
  }
}

/**
 * 从用户代理字符串提取操作系统版本
 */
export function getOSVersion(userAgent: string): string | undefined {
  // Windows 版本检测
  const windowsMatch = userAgent.match(/Windows NT (\d+\.\d+)/);
  if (windowsMatch && windowsMatch[1]) {
    const versionMap: Record<string, string> = {
      '10.0': 'Windows 10/11',
      '6.3': 'Windows 8.1',
      '6.2': 'Windows 8',
      '6.1': 'Windows 7',
      '6.0': 'Windows Vista',
    };
    return versionMap[windowsMatch[1]] || `Windows NT ${windowsMatch[1]}`;
  }

  // macOS 版本检测
  const macMatch = userAgent.match(/Mac OS X (\d+[._]\d+[._]?\d*)/);
  if (macMatch && macMatch[1]) {
    return `macOS ${macMatch[1].replace(/_/g, '.')}`;
  }

  // Linux 发行版检测
  const linuxMatch = userAgent.match(/(Ubuntu|Debian|CentOS|RedHat|Fedora)/i);
  if (linuxMatch && linuxMatch[1]) {
    return linuxMatch[1];
  }

  return undefined;
}

/**
 * 检查平台兼容性
 */
export function checkPlatformCompatibility(
  platform: Platform, 
  architecture: Architecture
): boolean {
  return PLATFORM_COMPATIBILITY_MATRIX[platform]?.[architecture] || false;
}

/**
 * 获取平台兼容性信息
 */
export function getPlatformCompatibility(
  platform: Platform, 
  architecture: Architecture
): PlatformCompatibility {
  const isSupported = checkPlatformCompatibility(platform, architecture);
  
  return {
    platform,
    architecture,
    supportedFeatures: isSupported ? ['ota-updates', 'auto-install'] : [],
    deprecatedFeatures: [],
  };
}

/**
 * 完整的平台检测和兼容性检查
 */
export function detectPlatformWithCompatibility(): PlatformDetectionResult {
  const platformInfo = detectPlatform();
  const compatibility = getPlatformCompatibility(platformInfo.platform, platformInfo.architecture);
  const isSupported = checkPlatformCompatibility(platformInfo.platform, platformInfo.architecture);

  return {
    platform: platformInfo.platform,
    architecture: platformInfo.architecture,
    osVersion: platformInfo.osVersion || 'Unknown',
    isSupported,
    compatibility,
  };
}

/**
 * 验证平台信息
 */
export function validatePlatformInfo(platformInfo: Partial<PlatformInfo>): boolean {
  if (!platformInfo.platform || !platformInfo.architecture) {
    return false;
  }

  const validPlatforms: Platform[] = ['windows', 'macos', 'linux'];
  const validArchitectures: Architecture[] = ['x86_64', 'aarch64', 'x86'];

  return validPlatforms.includes(platformInfo.platform) && 
         validArchitectures.includes(platformInfo.architecture);
}
