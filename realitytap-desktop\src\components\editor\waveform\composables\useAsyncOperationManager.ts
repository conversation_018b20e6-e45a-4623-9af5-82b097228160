// 异步操作管理器组合式函数
// 负责管理和清理所有异步操作，包括定时器、节流函数、Promise等

import { ref, onBeforeUnmount, type Ref } from "vue";
import { logger, LogModule } from "@/utils/logger/logger";

// 异步操作类型枚举
export enum AsyncOperationType {
  TIMEOUT = "timeout",
  INTERVAL = "interval",
  ANIMATION_FRAME = "animationFrame",
  THROTTLE = "throttle",
  DEBOUNCE = "debounce",
  PROMISE = "promise",
  CUSTOM = "custom",
}

// 异步操作项接口
export interface AsyncOperationItem {
  id: string;
  type: AsyncOperationType;
  cleanup: () => void;
  description?: string;
  createdAt: number;
}

// 节流函数包装器接口
export interface ThrottleWrapper<T extends (...args: any[]) => any> {
  fn: T;
  cancel: () => void;
  flush: () => void;
}

// 防抖函数包装器接口
export interface DebounceWrapper<T extends (...args: any[]) => any> {
  fn: T;
  cancel: () => void;
  flush: () => void;
}

/**
 * 异步操作管理器 Composable
 * 提供统一的异步操作管理和清理机制
 */
export function useAsyncOperationManager() {
  // 存储所有异步操作
  const operations = ref<Map<string, AsyncOperationItem>>(new Map());

  // 操作计数器，用于生成唯一ID
  let operationCounter = 0;

  /**
   * 生成唯一的操作ID
   */
  const generateOperationId = (type: AsyncOperationType): string => {
    return `${type}_${++operationCounter}_${Date.now()}`;
  };

  /**
   * 注册异步操作
   */
  const registerOperation = (type: AsyncOperationType, cleanup: () => void, description?: string): string => {
    const id = generateOperationId(type);
    const operation: AsyncOperationItem = {
      id,
      type,
      cleanup,
      description,
      createdAt: Date.now(),
    };

    operations.value.set(id, operation);
    logger.debug(LogModule.GENERAL, "AsyncManager: 注册异步操作", {
      type,
      id,
      description
    });

    return id;
  };

  /**
   * 取消注册异步操作
   */
  const unregisterOperation = (id: string): boolean => {
    const operation = operations.value.get(id);
    if (operation) {
      operations.value.delete(id);
      logger.debug(LogModule.GENERAL, "AsyncManager: 取消注册异步操作", {
        type: operation.type,
        id
      });
      return true;
    }
    return false;
  };

  /**
   * 清理指定的异步操作
   */
  const cleanupOperation = (id: string): boolean => {
    const operation = operations.value.get(id);
    if (operation) {
      try {
        operation.cleanup();
        operations.value.delete(id);
        logger.debug(LogModule.GENERAL, "AsyncManager: 已清理异步操作", {
          type: operation.type,
          id
        });
        return true;
      } catch (error) {
        logger.warn(LogModule.GENERAL, "AsyncManager: 清理异步操作时出错", {
          type: operation.type,
          id,
          error
        });
        operations.value.delete(id); // 即使清理失败也要移除记录
        return false;
      }
    }
    return false;
  };

  /**
   * 清理所有异步操作
   */
  const cleanupAllOperations = (): void => {
    logger.debug(LogModule.GENERAL, "AsyncManager: 开始清理异步操作", {
      operationCount: operations.value.size
    });

    const operationList = Array.from(operations.value.values());
    let successCount = 0;
    let errorCount = 0;

    for (const operation of operationList) {
      try {
        operation.cleanup();
        successCount++;
        logger.debug(LogModule.GENERAL, "AsyncManager: 已清理操作", {
          type: operation.type,
          id: operation.id
        });
      } catch (error) {
        errorCount++;
        logger.warn(LogModule.GENERAL, "AsyncManager: 清理操作时出错", {
          type: operation.type,
          id: operation.id,
          error
        });
      }
    }

    operations.value.clear();
    logger.debug(LogModule.GENERAL, "AsyncManager: 清理完成", {
      successCount,
      errorCount
    });
  };

  /**
   * 创建可管理的 setTimeout
   */
  const createTimeout = (callback: () => void, delay: number, description?: string): string => {
    const timeoutId = window.setTimeout(() => {
      // 执行回调后自动清理
      unregisterOperation(operationId);
      callback();
    }, delay);

    const operationId = registerOperation(
      AsyncOperationType.TIMEOUT,
      () => {
        clearTimeout(timeoutId);
      },
      description || `delay: ${delay}ms`
    );

    return operationId;
  };

  /**
   * 创建可管理的 setInterval
   */
  const createInterval = (callback: () => void, interval: number, description?: string): string => {
    const intervalId = window.setInterval(callback, interval);

    const operationId = registerOperation(
      AsyncOperationType.INTERVAL,
      () => {
        clearInterval(intervalId);
      },
      description || `interval: ${interval}ms`
    );

    return operationId;
  };

  /**
   * 创建可管理的 requestAnimationFrame
   */
  const createAnimationFrame = (callback: () => void, description?: string): string => {
    const frameId = requestAnimationFrame(() => {
      // 执行回调后自动清理
      unregisterOperation(operationId);
      callback();
    });

    const operationId = registerOperation(
      AsyncOperationType.ANIMATION_FRAME,
      () => {
        cancelAnimationFrame(frameId);
      },
      description
    );

    return operationId;
  };

  /**
   * 创建可管理的节流函数
   */
  const createThrottle = <T extends (...args: any[]) => any>(fn: T, delay: number, description?: string): ThrottleWrapper<T> => {
    let lastCall = 0;
    let timeoutId: number | undefined;
    let lastArgs: Parameters<T> | undefined;
    let lastThis: any;

    const throttledFn = function (this: any, ...args: Parameters<T>) {
      lastArgs = args;
      lastThis = this;

      const now = Date.now();
      const timeSinceLastCall = now - lastCall;

      if (timeSinceLastCall >= delay) {
        lastCall = now;
        return fn.apply(this, args);
      } else {
        // 清除之前的延迟调用
        if (timeoutId !== undefined) {
          clearTimeout(timeoutId);
        }

        // 设置延迟调用
        timeoutId = window.setTimeout(() => {
          lastCall = Date.now();
          if (lastArgs) {
            fn.apply(lastThis, lastArgs);
          }
          timeoutId = undefined;
        }, delay - timeSinceLastCall);
      }
    } as T;

    const cancel = () => {
      if (timeoutId !== undefined) {
        clearTimeout(timeoutId);
        timeoutId = undefined;
      }
      lastArgs = undefined;
    };

    const flush = () => {
      if (timeoutId !== undefined && lastArgs) {
        clearTimeout(timeoutId);
        timeoutId = undefined;
        lastCall = Date.now();
        fn.apply(lastThis, lastArgs);
        lastArgs = undefined;
      }
    };

    const operationId = registerOperation(AsyncOperationType.THROTTLE, cancel, description || `throttle: ${delay}ms`);

    return {
      fn: throttledFn,
      cancel: () => {
        cancel();
        unregisterOperation(operationId);
      },
      flush: () => {
        flush();
        unregisterOperation(operationId);
      },
    };
  };

  /**
   * 创建可管理的防抖函数
   */
  const createDebounce = <T extends (...args: any[]) => any>(fn: T, delay: number, description?: string): DebounceWrapper<T> => {
    let timeoutId: number | undefined;
    let lastArgs: Parameters<T> | undefined;
    let lastThis: any;

    const debouncedFn = function (this: any, ...args: Parameters<T>) {
      lastArgs = args;
      lastThis = this;

      if (timeoutId !== undefined) {
        clearTimeout(timeoutId);
      }

      timeoutId = window.setTimeout(() => {
        if (lastArgs) {
          fn.apply(lastThis, lastArgs);
        }
        timeoutId = undefined;
        lastArgs = undefined;
      }, delay);
    } as T;

    const cancel = () => {
      if (timeoutId !== undefined) {
        clearTimeout(timeoutId);
        timeoutId = undefined;
      }
      lastArgs = undefined;
    };

    const flush = () => {
      if (timeoutId !== undefined && lastArgs) {
        clearTimeout(timeoutId);
        timeoutId = undefined;
        fn.apply(lastThis, lastArgs);
        lastArgs = undefined;
      }
    };

    const operationId = registerOperation(AsyncOperationType.DEBOUNCE, cancel, description || `debounce: ${delay}ms`);

    return {
      fn: debouncedFn,
      cancel: () => {
        cancel();
        unregisterOperation(operationId);
      },
      flush: () => {
        flush();
        unregisterOperation(operationId);
      },
    };
  };

  /**
   * 注册 Promise 清理
   */
  const registerPromise = (promise: Promise<any>, abortController?: AbortController, description?: string): string => {
    const operationId = registerOperation(
      AsyncOperationType.PROMISE,
      () => {
        if (abortController && !abortController.signal.aborted) {
          abortController.abort();
        }
      },
      description
    );

    // Promise 完成后自动清理
    promise.finally(() => {
      unregisterOperation(operationId);
    });

    return operationId;
  };

  /**
   * 注册自定义清理函数
   */
  const registerCustomCleanup = (cleanup: () => void, description?: string): string => {
    return registerOperation(AsyncOperationType.CUSTOM, cleanup, description);
  };

  /**
   * 获取操作统计信息
   */
  const getOperationStats = () => {
    const stats = {
      total: operations.value.size,
      byType: {} as Record<AsyncOperationType, number>,
    };

    for (const operation of operations.value.values()) {
      stats.byType[operation.type] = (stats.byType[operation.type] || 0) + 1;
    }

    return stats;
  };

  /**
   * 获取所有操作的详细信息
   */
  const getOperationDetails = () => {
    return Array.from(operations.value.values()).map((op) => ({
      id: op.id,
      type: op.type,
      description: op.description,
      age: Date.now() - op.createdAt,
    }));
  };

  // 组件卸载时自动清理所有操作
  onBeforeUnmount(() => {
    logger.debug(LogModule.GENERAL, "AsyncManager: 组件卸载，开始清理所有异步操作");
    cleanupAllOperations();
  });

  return {
    // 基础管理
    registerOperation,
    unregisterOperation,
    cleanupOperation,
    cleanupAllOperations,

    // 创建可管理的异步操作
    createTimeout,
    createInterval,
    createAnimationFrame,
    createThrottle,
    createDebounce,
    registerPromise,
    registerCustomCleanup,

    // 统计和调试
    getOperationStats,
    getOperationDetails,

    // 响应式状态
    operations: operations as Ref<Map<string, AsyncOperationItem>>,
  };
}
