/**
 * 支持的操作系统平台
 */
export type Platform = 'windows' | 'macos' | 'linux';

/**
 * 支持的CPU架构
 */
export type Architecture = 'x86_64' | 'aarch64' | 'x86';

/**
 * 发布渠道
 */
export type ReleaseChannel = 'stable' | 'beta' | 'alpha';

/**
 * 平台信息接口
 */
export interface PlatformInfo {
  platform: Platform;
  architecture: Architecture;
  osVersion?: string;
  kernelVersion?: string;
  locale?: string;
}

/**
 * 平台特定的发布信息
 */
export interface PlatformRelease {
  filename: string;
  size: number;
  checksum: string;
  releaseDate: string;
  releaseNotes: string;
  downloadUrl?: string;
  mirrors?: string[];
}

/**
 * 平台兼容性信息
 */
export interface PlatformCompatibility {
  platform: Platform;
  architecture: Architecture;
  minimumOSVersion?: string;
  maximumOSVersion?: string;
  supportedFeatures: string[];
  deprecatedFeatures: string[];
}

/**
 * 平台检测结果
 */
export interface PlatformDetectionResult {
  platform: Platform;
  architecture: Architecture;
  osVersion: string;
  isSupported: boolean;
  compatibility: PlatformCompatibility;
}


