#!/bin/bash

# RealityTap OTA Server 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Node.js 版本
check_node_version() {
    log_info "Checking Node.js version..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    REQUIRED_VERSION="18.0.0"
    
    if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
        log_error "Node.js version $NODE_VERSION is not supported. Required: >= $REQUIRED_VERSION"
        exit 1
    fi
    
    log_success "Node.js version $NODE_VERSION is supported"
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    if [ ! -d "node_modules" ]; then
        log_warning "Dependencies not installed. Installing..."
        npm install
    fi
    
    log_success "Dependencies are ready"
}

# 检查环境变量
check_environment() {
    log_info "Checking environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            log_warning ".env file not found. Copying from .env.example"
            cp .env.example .env
        else
            log_error ".env file not found and no .env.example available"
            exit 1
        fi
    fi
    
    log_success "Environment configuration is ready"
}

# 检查存储目录
check_storage() {
    log_info "Checking storage directories..."
    
    STORAGE_PATH=${STORAGE_PATH:-"./storage"}
    
    # 创建必要的目录
    mkdir -p "$STORAGE_PATH/releases/stable"
    mkdir -p "$STORAGE_PATH/releases/beta"
    mkdir -p "$STORAGE_PATH/releases/alpha"
    mkdir -p "$STORAGE_PATH/metadata"
    mkdir -p "$STORAGE_PATH/logs"
    
    # 检查配置文件
    if [ ! -f "$STORAGE_PATH/metadata/versions.json" ]; then
        log_warning "versions.json not found. Creating default configuration..."
        # 这里可以创建默认配置文件
    fi
    
    if [ ! -f "$STORAGE_PATH/metadata/channels.json" ]; then
        log_warning "channels.json not found. Creating default configuration..."
        # 这里可以创建默认配置文件
    fi
    
    log_success "Storage directories are ready"
}

# 验证配置
validate_config() {
    log_info "Validating configuration..."
    
    if npm run validate-config; then
        log_success "Configuration validation passed"
    else
        log_error "Configuration validation failed"
        exit 1
    fi
}

# 启动服务器
start_server() {
    log_info "Starting RealityTap OTA Server..."
    
    MODE=${1:-"production"}
    
    case $MODE in
        "dev"|"development")
            log_info "Starting in development mode..."
            npm run dev
            ;;
        "prod"|"production")
            log_info "Starting in production mode..."
            npm run build
            npm run start:prod
            ;;
        *)
            log_error "Invalid mode: $MODE. Use 'dev' or 'prod'"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    log_info "🚀 RealityTap OTA Server Startup Script"
    log_info "========================================"
    
    # 检查参数
    MODE=${1:-"production"}
    
    # 执行检查
    check_node_version
    check_dependencies
    check_environment
    check_storage
    
    # 只在生产模式下验证配置
    if [ "$MODE" = "prod" ] || [ "$MODE" = "production" ]; then
        validate_config
    fi
    
    # 启动服务器
    start_server "$MODE"
}

# 错误处理
trap 'log_error "Script interrupted"; exit 1' INT TERM

# 运行主函数
main "$@"
