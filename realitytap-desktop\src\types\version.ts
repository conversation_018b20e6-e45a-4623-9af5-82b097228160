/**
 * 应用版本信息类型定义
 * Application version information types
 */

export interface AppVersionInfo {
  /** 应用名称 */
  appName: string;
  /** 前端版本号 (来自 package.json) */
  appVersion: string;
  /** 后端版本号 (来自 Cargo.toml) */
  backendVersion: string;
  /** 构建日期 */
  buildDate: string;
  /** 构建模式 (debug/release) */
  buildMode: string;
  /** 目标架构 */
  targetArch: string;
  /** 目标操作系统 */
  targetOs: string;
}

/**
 * 版本检查状态
 */
export interface VersionCheckStatus {
  /** 是否正在检查 */
  isChecking: boolean;
  /** 是否有新版本 */
  hasNewVersion: boolean;
  /** 最新版本号 */
  latestVersion?: string;
  /** 下载URL */
  downloadUrl?: string;
  /** 发布说明 */
  releaseNotes?: string;
  /** 文件大小 */
  fileSize?: number;
  /** 校验和 */
  checksum?: string;
  /** 是否强制更新 */
  isForced?: boolean;
  /** 检查错误信息 */
  error?: string;
  /** 最后检查时间 */
  lastCheckTime?: string;
}

/**
 * 版本下载进度
 */
export interface VersionDownloadProgress {
  /** 是否正在下载 */
  isDownloading: boolean;
  /** 下载进度 (0-100) */
  progress: number;
  /** 下载速度 (bytes/s) */
  speed?: number;
  /** 已下载大小 (bytes) */
  downloaded?: number;
  /** 总大小 (bytes) */
  total?: number;
  /** 下载错误信息 */
  error?: string;
}
