// 波形编辑器命中测试服务
// 提供事件、曲线点、辅助线等命中检测相关接口
// 支持可配置命中半径、优先级等

import type {
  RenderableEvent,
  RenderableTransientEvent,
  RenderableContinuousEvent,
} from "@/types/haptic-editor";
import { isPointInRadius } from "./coordinate";

export interface HitTestOptions {
  pointRadius?: number; // 命中点半径
  eventBodyPriority?: boolean; // 是否优先命中事件体（否则优先点）
  curvePointRadius?: number; // 曲线点命中半径
}

export interface HitTestResult {
  event: RenderableEvent | null;
  eventType: "transient" | "continuous" | null;
  eventIndex: number;
  curvePointIndex: number; // -1 表示未命中曲线点
}

const DEFAULT_POINT_RADIUS = 8; // 默认命中半径（像素）

/**
 * 命中测试：单个事件（支持 transient/continuous）
 * @param x 画布坐标x
 * @param y 画布坐标y
 * @param event 事件对象
 * @param mapTimeToX 时间->X映射函数
 * @param mapIntensityToY 强度->Y映射函数
 * @param options 命中参数
 */
export function hitTestEvent(
  x: number,
  y: number,
  event: RenderableEvent,
  mapTimeToX: (time: number) => number,
  mapIntensityToY: (intensity: number) => number,
  options: HitTestOptions = {}
): HitTestResult {
  const pointRadius = options.pointRadius ?? DEFAULT_POINT_RADIUS;
  const curvePointRadius = options.curvePointRadius ?? pointRadius;
  let eventType: "transient" | "continuous" | null = null;
  let curvePointIndex = -1;
  let hit = false;

  if (event.type === "transient") {
    eventType = "transient";
    const e = event as RenderableTransientEvent;
    // 1. 先检测 peak 点
    const peakX = mapTimeToX(e.peakTime);
    const peakY = mapIntensityToY(e.intensity);
    if (isPointInRadius(x, y, peakX, peakY, pointRadius)) {
      hit = true;
      curvePointIndex = -1;
    } else {
      // 2. 检查事件体三角形
      const startX = mapTimeToX(e.startTime);
      const endX = mapTimeToX(e.stopTime);
      const baseY = mapIntensityToY(0);
      // 三角形命中
      if (pointInTriangle({ x, y }, { x: startX, y: baseY }, { x: peakX, y: peakY }, { x: endX, y: baseY })) {
        hit = true;
      }
    }
  } else if (event.type === "continuous") {
    eventType = "continuous";
    const e = event as RenderableContinuousEvent;
    // 1. 检查所有曲线点
    if (e.curves && e.curves.length > 0) {
      for (let j = 0; j < e.curves.length; j++) {
        const p = e.curves[j];
        const px = mapTimeToX(e.startTime + p.timeOffset);
        const py = mapIntensityToY(p.drawIntensity);
        if (isPointInRadius(x, y, px, py, curvePointRadius)) {
          hit = true;
          curvePointIndex = j;
          break;
        }
      }
    }
    // 2. 检查事件体区域
    if (!hit && e.curves.length >= 2) {
      const points = e.curves.map((p) => ({
        x: mapTimeToX(e.startTime + p.timeOffset),
        y: mapIntensityToY(p.drawIntensity),
      }));
      const baselineY = mapIntensityToY(0);
      if (
        pointInPolygon({ x, y }, [
          ...points,
          { x: points[points.length - 1].x, y: baselineY },
          { x: points[0].x, y: baselineY },
        ])
      ) {
        hit = true;
      }
    }
  }
  return {
    event: hit ? event : null,
    eventType,
    eventIndex: -1, // 由批量命中测试补充
    curvePointIndex: curvePointIndex,
  };
}

/**
 * 命中测试：所有事件，返回优先命中的事件及曲线点
 * @param x 画布坐标x
 * @param y 画布坐标y
 * @param events 事件数组
 * @param mapTimeToX 时间->X映射函数
 * @param mapIntensityToY 强度->Y映射函数
 * @param options 命中参数
 */
export function hitTestEvents(
  x: number,
  y: number,
  events: RenderableEvent[],
  mapTimeToX: (time: number) => number,
  mapIntensityToY: (intensity: number) => number,
  options: HitTestOptions = {}
): HitTestResult {
  // 优先级：后绘制的事件优先（通常为数组后面的元素）
  for (let i = events.length - 1; i >= 0; i--) {
    const res = hitTestEvent(x, y, events[i], mapTimeToX, mapIntensityToY, options);
    if (res.event) {
      return { ...res, eventIndex: i };
    }
  }
  return {
    event: null,
    eventType: null,
    eventIndex: -1,
    curvePointIndex: -1,
  };
}

/**
 * 命中测试：连续事件的曲线点
 * @param x 画布坐标x
 * @param y 画布坐标y
 * @param event 连续事件
 * @param mapTimeToX 时间->X映射函数
 * @param mapIntensityToY 强度->Y映射函数
 * @param options 命中参数
 */
export function hitTestCurvePoint(
  x: number,
  y: number,
  event: RenderableContinuousEvent,
  mapTimeToX: (time: number) => number,
  mapIntensityToY: (intensity: number) => number,
  options: HitTestOptions = {}
): number {
  const curvePointRadius = options.curvePointRadius ?? DEFAULT_POINT_RADIUS;
  if (!event.curves || event.curves.length === 0) return -1;
  for (let j = 0; j < event.curves.length; j++) {
    const p = event.curves[j];
    const px = mapTimeToX(event.startTime + p.timeOffset);
    const py = mapIntensityToY(p.drawIntensity);
    if (isPointInRadius(x, y, px, py, curvePointRadius)) {
      return j;
    }
  }
  return -1;
}

// --- 辅助函数 ---

// 判断点是否在三角形内
function pointInTriangle(
  p: { x: number; y: number },
  a: { x: number; y: number },
  b: { x: number; y: number },
  c: { x: number; y: number }
) {
  const area = (a: any, b: any, c: any) => Math.abs((a.x * (b.y - c.y) + b.x * (c.y - a.y) + c.x * (a.y - b.y)) / 2.0);
  const A = area(a, b, c);
  const A1 = area(p, b, c);
  const A2 = area(a, p, c);
  const A3 = area(a, b, p);
  return Math.abs(A - (A1 + A2 + A3)) < 0.5; // 容差
}

// 判断点是否在多边形内
function pointInPolygon(p: { x: number; y: number }, polygon: { x: number; y: number }[]) {
  let inside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].x,
      yi = polygon[i].y;
    const xj = polygon[j].x,
      yj = polygon[j].y;
    const intersect = yi > p.y !== yj > p.y && p.x < ((xj - xi) * (p.y - yi)) / (yj - yi + 0.00001) + xi;
    if (intersect) inside = !inside;
  }
  return inside;
}
