/**
 * 通用格式化工具函数
 * 提供统一的数据格式化功能
 */

/**
 * 格式化字节数为人类可读的格式
 * @param bytes 字节数
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的字符串
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`;
}

/**
 * 格式化文件大小（formatBytes 的别名，保持向后兼容）
 * @param bytes 字节数
 * @returns 格式化后的字符串
 */
export function formatFileSize(bytes: number): string {
  return formatBytes(bytes, 2);
}

/**
 * 格式化内存使用量（formatBytes 的别名，保持向后兼容）
 * @param bytes 字节数
 * @returns 格式化后的字符串
 */
export function formatMemoryUsage(bytes: number): string {
  return formatBytes(bytes, 1);
}

/**
 * 格式化百分比
 * @param value 数值
 * @param decimals 小数位数，默认为1
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * 格式化CPU使用率
 * @param percentage CPU使用率百分比
 * @returns 格式化后的字符串
 */
export function formatCpuUsage(percentage: number): string {
  return formatPercentage(percentage, 1);
}

/**
 * 格式化时间间隔（毫秒）
 * @param milliseconds 毫秒数
 * @returns 格式化后的时间字符串
 */
export function formatDuration(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  
  const seconds = Math.floor(milliseconds / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
}

/**
 * 格式化数字，添加千位分隔符
 * @param num 数字
 * @returns 格式化后的字符串
 */
export function formatNumber(num: number): string {
  return num.toLocaleString();
}

/**
 * 格式化版本号，确保格式一致
 * @param version 版本号字符串
 * @returns 格式化后的版本号
 */
export function formatVersion(version: string): string {
  // 移除前缀 'v' 如果存在
  const cleanVersion = version.replace(/^v/, '');
  
  // 确保版本号格式为 x.y.z
  const parts = cleanVersion.split('.');
  while (parts.length < 3) {
    parts.push('0');
  }
  
  return parts.slice(0, 3).join('.');
}

/**
 * 格式化日期时间
 * @param date 日期对象或ISO字符串
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export function formatDateTime(
  date: Date | string,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString('zh-CN', options);
}

/**
 * 格式化相对时间（如：2分钟前）
 * @param date 日期对象或ISO字符串
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffSeconds < 60) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return formatDateTime(dateObj, { month: '2-digit', day: '2-digit' });
  }
}
