// Temporary file management utilities for cross-platform compatibility
use crate::error::{Error, Result};
use std::fs;
use std::path::{Path, PathBuf};
use std::time::{SystemTime, UNIX_EPOCH};
use uuid::Uuid;

/// Temporary file manager for handling temporary files with automatic cleanup
pub struct TempFileManager {
    temp_dir: PathBuf,
    cleanup_on_drop: bool,
}

impl TempFileManager {
    /// Create a new temporary file manager
    pub fn new() -> Result<Self> {
        let temp_dir = Self::get_system_temp_dir()?;
        let app_temp_dir = temp_dir.join("realitytap-studio");

        // Ensure the temporary directory exists
        fs::create_dir_all(&app_temp_dir)
            .map_err(|e| Error::Io(format!("Failed to create temp directory: {}", e)))?;

        Ok(TempFileManager {
            temp_dir: app_temp_dir,
            cleanup_on_drop: true,
        })
    }

    /// Create a new temporary file manager with custom directory
    pub fn with_dir<P: AsRef<Path>>(dir: P) -> Result<Self> {
        let temp_dir = dir.as_ref().to_path_buf();

        // Ensure the temporary directory exists
        fs::create_dir_all(&temp_dir)
            .map_err(|e| Error::Io(format!("Failed to create temp directory: {}", e)))?;

        Ok(TempFileManager {
            temp_dir,
            cleanup_on_drop: true,
        })
    }

    /// Get the system temporary directory
    fn get_system_temp_dir() -> Result<PathBuf> {
        std::env::temp_dir()
            .canonicalize()
            .map_err(|e| Error::Io(format!("Failed to get system temp directory: {}", e)))
    }

    /// Create a temporary file with a unique name
    pub fn create_temp_file(&self, prefix: &str, extension: &str) -> Result<TempFile> {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| Error::Io(format!("Failed to get timestamp: {}", e)))?
            .as_secs();

        let uuid = Uuid::new_v4();
        let filename = format!(
            "{}_{}_{}_{}.{}",
            prefix,
            timestamp,
            uuid.simple(),
            std::process::id(),
            extension
        );

        let file_path = self.temp_dir.join(filename);

        // Create the file to ensure it exists
        fs::File::create(&file_path)
            .map_err(|e| Error::Io(format!("Failed to create temp file: {}", e)))?;

        Ok(TempFile {
            path: file_path,
            auto_cleanup: true,
        })
    }

    /// Clean up old temporary files (older than specified hours)
    pub fn cleanup_old_files(&self, max_age_hours: u64) -> Result<usize> {
        let max_age = std::time::Duration::from_secs(max_age_hours * 3600);
        let now = SystemTime::now();
        let mut cleaned_count = 0;

        if !self.temp_dir.exists() {
            return Ok(0);
        }

        let entries = fs::read_dir(&self.temp_dir)
            .map_err(|e| Error::Io(format!("Failed to read temp directory: {}", e)))?;

        for entry in entries {
            let entry =
                entry.map_err(|e| Error::Io(format!("Failed to read directory entry: {}", e)))?;
            let path = entry.path();

            if let Ok(metadata) = entry.metadata() {
                if let Ok(modified) = metadata.modified() {
                    if let Ok(age) = now.duration_since(modified) {
                        if age > max_age {
                            if path.is_file() {
                                if fs::remove_file(&path).is_ok() {
                                    cleaned_count += 1;
                                    log::debug!("Cleaned up old temp file: {:?}", path);
                                }
                            } else if path.is_dir() {
                                if fs::remove_dir_all(&path).is_ok() {
                                    cleaned_count += 1;
                                    log::debug!("Cleaned up old temp directory: {:?}", path);
                                }
                            }
                        }
                    }
                }
            }
        }

        log::info!(
            "Cleaned up {} old temporary files/directories",
            cleaned_count
        );
        Ok(cleaned_count)
    }
}

impl Drop for TempFileManager {
    fn drop(&mut self) {
        if self.cleanup_on_drop {
            // Clean up files older than 1 hour on drop
            let _ = self.cleanup_old_files(1);
        }
    }
}

/// Represents a temporary file with automatic cleanup
pub struct TempFile {
    path: PathBuf,
    auto_cleanup: bool,
}

impl TempFile {
    /// Get the path to the temporary file
    pub fn path(&self) -> &Path {
        &self.path
    }

    /// Set whether to automatically cleanup the file when dropped
    pub fn set_auto_cleanup(&mut self, auto_cleanup: bool) {
        self.auto_cleanup = auto_cleanup;
    }
}

impl Drop for TempFile {
    fn drop(&mut self) {
        if self.auto_cleanup && self.path.exists() {
            let _ = fs::remove_file(&self.path);
        }
    }
}

/// Represents a temporary directory with automatic cleanup
pub struct TempDir {
    path: PathBuf,
    auto_cleanup: bool,
}

impl TempDir {}

impl Drop for TempDir {
    fn drop(&mut self) {
        if self.auto_cleanup && self.path.exists() {
            let _ = fs::remove_dir_all(&self.path);
        }
    }
}

/// Global temporary file manager instance
use std::sync::OnceLock;
static GLOBAL_TEMP_MANAGER: OnceLock<TempFileManager> = OnceLock::new();

/// Get the global temporary file manager
pub fn get_global_temp_manager() -> Result<&'static TempFileManager> {
    GLOBAL_TEMP_MANAGER.get_or_init(|| {
        TempFileManager::new().unwrap_or_else(|e| {
            log::error!("Failed to initialize global temp manager: {}", e);
            // Return a default manager with system temp dir
            TempFileManager::with_dir(std::env::temp_dir()).unwrap()
        })
    });

    Ok(GLOBAL_TEMP_MANAGER.get().unwrap())
}

/// Initialize the global temporary file manager and perform initial cleanup
pub fn init_temp_manager() -> Result<()> {
    let manager = get_global_temp_manager()?;
    // Clean up files older than 24 hours on startup
    manager.cleanup_old_files(24)?;
    Ok(())
}
