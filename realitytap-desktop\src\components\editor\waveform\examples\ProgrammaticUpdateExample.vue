<template>
  <div class="programmatic-update-example">
    <h3>波形编程式数据更新示例</h3>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <n-space>
        <n-button @click="generateRandomEvents" type="primary">
          生成随机事件
        </n-button>
        <n-button @click="updateSelectedEvents" type="info">
          更新选中事件
        </n-button>
        <n-button @click="replaceTimeRange" type="warning">
          替换时间范围
        </n-button>
        <n-button @click="clearAllEvents" type="error">
          清空所有事件
        </n-button>
        <n-button @click="forceRedraw" type="default">
          强制重绘
        </n-button>
      </n-space>
    </div>

    <!-- 状态显示 -->
    <div class="status-panel">
      <n-card title="当前状态" size="small">
        <p>事件数量: {{ currentEventsCount }}</p>
        <p>最后操作: {{ lastOperation }}</p>
        <p>操作状态: {{ operationStatus }}</p>
      </n-card>
    </div>

    <!-- 波形画布 -->
    <div class="waveform-container">
      <InteractiveWaveformCanvas
        ref="waveformCanvasRef"
        :fileUuid="fileUuid"
        :totalEffectDuration="totalDuration"
        :baselineDuration="baselineDuration"
        :availableParentWidth="containerWidth"
        @event-selected="handleEventSelected"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NButton, NSpace, NCard } from 'naive-ui';
import InteractiveWaveformCanvas from '../InteractiveWaveformCanvas.vue';
import type { RenderableEvent, RenderableTransientEvent, RenderableContinuousEvent } from '@/types/haptic-editor';
import { v4 as uuidv4 } from 'uuid';

// 组件引用
const waveformCanvasRef = ref<InstanceType<typeof InteractiveWaveformCanvas>>();

// 基础配置
const fileUuid = ref('example-file-uuid');
const totalDuration = ref(5000);
const baselineDuration = ref(5000);
const containerWidth = ref(800);

// 状态管理
const currentEventsCount = ref(0);
const lastOperation = ref('无');
const operationStatus = ref('就绪');

// 计算属性
const selectedEventId = ref<string | null>(null);

// 事件处理
const handleEventSelected = (eventId: string | null) => {
  selectedEventId.value = eventId;
};

// 生成随机事件数据
const generateRandomEvents = async () => {
  if (!waveformCanvasRef.value) return;
  
  try {
    operationStatus.value = '生成中...';
    lastOperation.value = '生成随机事件';
    
    const events: RenderableEvent[] = [];
    const eventCount = Math.floor(Math.random() * 8) + 3; // 3-10个事件
    
    for (let i = 0; i < eventCount; i++) {
      const startTime = Math.floor(Math.random() * 4000); // 0-4000ms
      const eventType = Math.random() > 0.5 ? 'transient' : 'continuous';
      
      if (eventType === 'transient') {
        const intensity = Math.floor(Math.random() * 80) + 20; // 20-100
        const frequency = Math.floor(Math.random() * 80) + 20; // 20-100
        const width = Math.floor(Math.random() * 15) + 10; // 10-25ms
        
        const transientEvent: RenderableTransientEvent = {
          type: 'transient',
          id: uuidv4(),
          startTime,
          peakTime: startTime + Math.floor(width / 2),
          stopTime: startTime + width,
          intensity,
          frequency,
          width
        };
        
        events.push(transientEvent);
      } else {
        const intensity = Math.floor(Math.random() * 80) + 20;
        const frequency = Math.floor(Math.random() * 80) + 20;
        const duration = Math.floor(Math.random() * 300) + 100; // 100-400ms
        
        const continuousEvent: RenderableContinuousEvent = {
          type: 'continuous',
          id: uuidv4(),
          startTime,
          stopTime: startTime + duration,
          duration,
          eventIntensity: intensity,
          eventFrequency: frequency,
          curves: [
            { timeOffset: 0, drawIntensity: 0, rawIntensity: 0, relativeCurveFrequency: 0, curveFrequency: frequency },
            { timeOffset: duration * 0.3, drawIntensity: intensity * 0.7, rawIntensity: 0.7, relativeCurveFrequency: 0, curveFrequency: frequency },
            { timeOffset: duration * 0.7, drawIntensity: intensity * 0.9, rawIntensity: 0.9, relativeCurveFrequency: 0, curveFrequency: frequency },
            { timeOffset: duration, drawIntensity: 0, rawIntensity: 0, relativeCurveFrequency: 0, curveFrequency: frequency }
          ]
        };
        
        events.push(continuousEvent);
      }
    }
    
    // 按时间排序
    events.sort((a, b) => a.startTime - b.startTime);
    
    // 使用编程式接口更新数据
    await waveformCanvasRef.value.setEventsData(events, {
      preserveSelection: false,
      forceRedraw: true
    });
    
    currentEventsCount.value = events.length;
    operationStatus.value = '完成';
    
  } catch (error) {
    console.error('生成随机事件失败:', error);
    operationStatus.value = '失败';
  }
};

// 更新选中的事件
const updateSelectedEvents = async () => {
  if (!waveformCanvasRef.value) return;
  
  try {
    operationStatus.value = '更新中...';
    lastOperation.value = '更新选中事件';
    
    const currentEvents = waveformCanvasRef.value.getCurrentEventsData();
    const updates = currentEvents
      .filter((_, index) => index % 2 === 0) // 更新偶数索引的事件
      .map(event => ({
        id: event.id,
        data: event.type === 'transient' 
          ? { intensity: Math.floor(Math.random() * 50) + 50 } // 50-100
          : { eventIntensity: Math.floor(Math.random() * 50) + 50 }
      }));
    
    if (updates.length > 0) {
      await waveformCanvasRef.value.updateEventsData(updates, {
        preserveSelection: true,
        forceRedraw: true
      });
    }
    
    operationStatus.value = '完成';
    
  } catch (error) {
    console.error('更新事件失败:', error);
    operationStatus.value = '失败';
  }
};

// 替换指定时间范围的事件
const replaceTimeRange = async () => {
  if (!waveformCanvasRef.value) return;
  
  try {
    operationStatus.value = '替换中...';
    lastOperation.value = '替换时间范围';
    
    const startTime = 1000; // 1秒
    const endTime = 3000;   // 3秒
    
    // 创建新的替换事件
    const newEvents: RenderableEvent[] = [
      {
        type: 'transient',
        id: uuidv4(),
        startTime: 1500,
        peakTime: 1510,
        stopTime: 1520,
        intensity: 80,
        frequency: 70,
        width: 20
      },
      {
        type: 'continuous',
        id: uuidv4(),
        startTime: 2000,
        stopTime: 2200,
        duration: 200,
        eventIntensity: 90,
        eventFrequency: 60,
        curves: [
          { timeOffset: 0, drawIntensity: 0, rawIntensity: 0, relativeCurveFrequency: 0, curveFrequency: 60 },
          { timeOffset: 100, drawIntensity: 81, rawIntensity: 0.9, relativeCurveFrequency: 0, curveFrequency: 60 },
          { timeOffset: 200, drawIntensity: 0, rawIntensity: 0, relativeCurveFrequency: 0, curveFrequency: 60 }
        ]
      }
    ];
    
    await waveformCanvasRef.value.replaceEventsInRange(startTime, endTime, newEvents, {
      preserveSelection: true,
      forceRedraw: true
    });
    
    const updatedEvents = waveformCanvasRef.value.getCurrentEventsData();
    currentEventsCount.value = updatedEvents.length;
    operationStatus.value = '完成';
    
  } catch (error) {
    console.error('替换时间范围失败:', error);
    operationStatus.value = '失败';
  }
};

// 清空所有事件
const clearAllEvents = async () => {
  if (!waveformCanvasRef.value) return;
  
  try {
    operationStatus.value = '清空中...';
    lastOperation.value = '清空所有事件';
    
    await waveformCanvasRef.value.setEventsData([], {
      preserveSelection: false,
      forceRedraw: true
    });
    
    currentEventsCount.value = 0;
    operationStatus.value = '完成';
    
  } catch (error) {
    console.error('清空事件失败:', error);
    operationStatus.value = '失败';
  }
};

// 强制重绘
const forceRedraw = async () => {
  if (!waveformCanvasRef.value) return;
  
  try {
    operationStatus.value = '重绘中...';
    lastOperation.value = '强制重绘';
    
    await waveformCanvasRef.value.forceRedraw();
    
    operationStatus.value = '完成';
    
  } catch (error) {
    console.error('强制重绘失败:', error);
    operationStatus.value = '失败';
  }
};

// 组件挂载时初始化
onMounted(() => {
  // 初始化一些示例数据
  setTimeout(() => {
    generateRandomEvents();
  }, 1000);
});
</script>

<style scoped>
.programmatic-update-example {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.control-panel {
  flex-shrink: 0;
}

.status-panel {
  flex-shrink: 0;
}

.waveform-container {
  flex: 1;
  min-height: 400px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}
</style>
