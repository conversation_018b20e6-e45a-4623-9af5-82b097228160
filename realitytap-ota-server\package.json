{"name": "@realitytap/ota-server", "version": "1.0.0", "description": "RealityTap OTA update server with SQLite database architecture", "main": "dist/app.js", "scripts": {"dev": "tsx watch src/app.ts", "build": "tsc && tsc-alias", "build:full": "npm run clean && npm run build && npm run build-admin-ui", "start": "node dist/app.js", "start:prod": "cross-env NODE_ENV=production node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "validate-config": "tsx scripts/validate-config.ts", "init-storage": "tsx scripts/init-storage.ts", "upload-release": "tsx scripts/upload-release.ts", "manage-release": "tsx scripts/manage-release.ts", "test-ota": "tsx scripts/test-ota.ts", "test-ip-logging": "node scripts/test-ip-logging.js", "generate-test-logs": "node scripts/generate-test-logs.js", "build-admin-ui": "tsx scripts/build-admin-ui.ts", "start-dev": "bash scripts/start-dev.sh", "start-dev-full": "bash scripts/start-dev-full.sh", "start-dev-all": "node start-dev-full.js", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "npm run build-admin-ui", "docker:build": "docker build -t realitytap-ota-server:latest .", "docker:run": "docker run -d --name realitytap-ota -p 3000:3000 -v $(pwd)/storage:/app/storage realitytap-ota-server:latest", "cleanup-logs": "tsx scripts/cleanup-logs.ts", "logs:status": "tsx scripts/cleanup-logs.ts status", "logs:cleanup": "tsx scripts/cleanup-logs.ts cleanup", "logs:cleanup-dry": "tsx scripts/cleanup-logs.ts cleanup --dry-run", "migrate:demo": "tsx scripts/demo-migration.ts run", "migrate:test": "tsx scripts/test-migration.ts test", "migrate:report": "tsx scripts/test-migration.ts report", "migrate:to-sqlite": "tsx scripts/migrate-to-sqlite.ts migrate", "migrate:rollback": "tsx scripts/migrate-to-sqlite.ts rollback", "migrate:validate": "tsx scripts/migrate-to-sqlite.ts validate", "migrate:cleanup": "tsx scripts/migrate-and-cleanup.ts execute", "migrate:cleanup-dry": "tsx scripts/migrate-and-cleanup.ts execute --dry-run", "db:enable": "tsx scripts/enable-database.ts enable", "db:disable": "tsx scripts/enable-database.ts disable", "db:status": "tsx scripts/enable-database.ts status", "storage:analyze": "tsx scripts/analyze-storage-cleanup.ts analyze", "storage:cleanup": "tsx scripts/cleanup-storage.ts safe", "storage:cleanup-deep": "tsx scripts/cleanup-storage.ts deep", "storage:cleanup-logs": "tsx scripts/cleanup-storage.ts logs", "storage:cleanup-temp": "tsx scripts/cleanup-storage.ts temp", "storage:cleanup-dry": "tsx scripts/cleanup-storage.ts safe --dry-run", "db:backup": "tsx scripts/backup-database.ts", "db:verify": "tsx scripts/final-verification.ts", "force-update:set": "tsx scripts/manage-force-update.ts set", "force-update:status": "tsx scripts/manage-force-update.ts status", "force-update:channels": "tsx scripts/manage-force-update.ts channels", "force-update:batch": "tsx scripts/manage-force-update.ts batch-set", "force-update:test": "tsx scripts/test-force-update.ts", "force-update:integration": "tsx scripts/test-integration.ts", "db:fix-migration": "tsx scripts/fix-migration-state.ts", "test-data:add": "tsx scripts/add-test-data.ts"}, "keywords": ["ota", "update", "server", "realitytap", "typescript", "express"], "dependencies": {"@tauri-apps/plugin-os": "^2.2.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.13", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "commander": "^11.1.0", "compression": "^1.7.4", "cors": "^2.8.5", "cross-env": "^7.0.3", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "semver": "^7.5.4", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^3.22.4"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.8", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/semver": "^7.5.6", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}