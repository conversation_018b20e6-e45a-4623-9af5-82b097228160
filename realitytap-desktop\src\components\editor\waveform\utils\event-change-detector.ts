import type { RenderableEvent, RenderableContinuousEvent, RenderableTransientEvent } from "@/types/haptic-editor";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 事件变化检测工具
 * 用于精确检测事件数据的真实变化，排除仅选中状态的变化
 */

/**
 * 检查是否存在真正的事件数据变化（排除仅选中状态的变化）
 * @param newEvents 新的事件数组
 * @param oldEvents 旧的事件数组
 * @returns 是否存在真实的数据变化
 */
export function checkForRealEventDataChanges(newEvents: RenderableEvent[], oldEvents: RenderableEvent[]): boolean {
  // 1. 检查数组长度是否变化（新增/删除事件）
  if (newEvents.length !== oldEvents.length) {
    logger.debug(LogModule.WAVEFORM, `EventChangeDetector: 检测到事件数量变化: ${oldEvents.length} -> ${newEvents.length}`);
    return true;
  }

  // 2. 检查事件顺序是否变化
  for (let i = 0; i < newEvents.length; i++) {
    if (newEvents[i].id !== oldEvents[i].id) {
      logger.debug(LogModule.WAVEFORM, `EventChangeDetector: 检测到事件顺序变化`);
      return true;
    }
  }

  // 3. 检查每个事件的关键属性是否变化（排除选中状态相关属性）
  for (let i = 0; i < newEvents.length; i++) {
    const newEvent = newEvents[i];
    const oldEvent = oldEvents[i];

    // 检查共同的基本属性
    if (
      newEvent.startTime !== oldEvent.startTime ||
      newEvent.stopTime !== oldEvent.stopTime ||
      newEvent.type !== oldEvent.type
    ) {
      logger.debug(LogModule.WAVEFORM, `EventChangeDetector: 检测到事件 ${newEvent.id} 的基本属性变化`);
      return true;
    }

    // 对于连续事件，检查连续事件特有属性和曲线数据
    if (newEvent.type === "continuous" && oldEvent.type === "continuous") {
      if (hasRenderableContinuousEventChanged(newEvent, oldEvent)) {
        return true;
      }
    }

    // 对于瞬态事件，检查瞬态事件特有属性
    if (newEvent.type === "transient" && oldEvent.type === "transient") {
      if (hasRenderableTransientEventChanged(newEvent, oldEvent)) {
        return true;
      }
    }
  }

  // 没有检测到真实的数据变化
  return false;
}

/**
 * 检查连续事件是否有实质变化
 */
function hasRenderableContinuousEventChanged(
  newEvent: RenderableContinuousEvent, 
  oldEvent: RenderableContinuousEvent
): boolean {
  // 检查连续事件的特有属性
  if (
    newEvent.duration !== oldEvent.duration ||
    newEvent.originalEventDuration !== oldEvent.originalEventDuration ||
    newEvent.eventIntensity !== oldEvent.eventIntensity ||
    newEvent.eventFrequency !== oldEvent.eventFrequency
  ) {
    logger.debug(LogModule.WAVEFORM, `EventChangeDetector: 检测到连续事件 ${newEvent.id} 的属性变化`);
    return true;
  }

  if (newEvent.curves.length !== oldEvent.curves.length) {
    logger.debug(LogModule.WAVEFORM, `EventChangeDetector: 检测到事件 ${newEvent.id} 的曲线点数量变化`);
    return true;
  }

  // 检查曲线点数据
  for (let j = 0; j < newEvent.curves.length; j++) {
    const newCurve = newEvent.curves[j];
    const oldCurve = oldEvent.curves[j];

    if (
      newCurve.timeOffset !== oldCurve.timeOffset ||
      newCurve.drawIntensity !== oldCurve.drawIntensity ||
      newCurve.rawIntensity !== oldCurve.rawIntensity ||
      newCurve.curveFrequency !== oldCurve.curveFrequency ||
      newCurve.relativeCurveFrequency !== oldCurve.relativeCurveFrequency
    ) {
      logger.debug(LogModule.WAVEFORM, `EventChangeDetector: 检测到事件 ${newEvent.id} 的曲线点 ${j} 数据变化`);
      return true;
    }
  }

  return false;
}

/**
 * 检查瞬态事件是否有实质变化
 */
function hasRenderableTransientEventChanged(
  newEvent: RenderableTransientEvent, 
  oldEvent: RenderableTransientEvent
): boolean {
  if (
    newEvent.peakTime !== oldEvent.peakTime ||
    newEvent.width !== oldEvent.width ||
    newEvent.intensity !== oldEvent.intensity ||
    newEvent.frequency !== oldEvent.frequency
  ) {
    logger.debug(LogModule.WAVEFORM, `EventChangeDetector: 检测到瞬态事件 ${newEvent.id} 的属性变化`);
    return true;
  }

  return false;
}
