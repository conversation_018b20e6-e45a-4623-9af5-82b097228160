<template>
  <div class="close-tab-button-container">
    <n-tooltip :show-arrow="false" placement="bottom">
      <template #trigger>
        <n-button
          type="primary"
          @click="handleCloseTab"
          class="close-tab-btn"
          :class="{ 'close-tab-btn--disabled': !canClose }"
          :disabled="!canClose"
          secondary
        >
          <template #icon>
            <n-icon class="close-icon">
              <CloseIcon />
            </n-icon>
          </template>
        </n-button>
      </template>
      <span>{{ tooltipText }}</span>
    </n-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed, defineEmits } from "vue";
import { NButton, NIcon, NTooltip } from "naive-ui";
import { Close as CloseIcon } from "@vicons/ionicons5";
import { useProjectStore } from "@/stores/haptics-project-store";
import { logger, LogModule } from "@/utils/logger/logger";

// 定义事件
const emit = defineEmits<{
  (e: "close-tab"): void;
}>();

// 获取stores
const projectStore = useProjectStore();

// 计算属性：是否可以关闭
const canClose = computed(() => {
  // 如果有选中的文件，则可以关闭
  return !!projectStore.selectedFileUuid;
});

// 计算属性：tooltip文本
const tooltipText = computed(() => {
  if (!projectStore.selectedFileUuid) {
    return "没有打开的文件";
  }
  return "关闭当前文件";
});

// 关闭标签页处理函数
const handleCloseTab = () => {
  if (!canClose.value) {
    logger.debug(LogModule.GENERAL, "无法关闭：没有打开的文件");
    return;
  }

  logger.debug(LogModule.GENERAL, "CloseTabButton: 触发关闭标签页事件");
  emit("close-tab");
};
</script>

<style scoped>
/* ===== 关闭按钮容器 ===== */
.close-tab-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* ===== 关闭按钮样式 ===== */
.close-tab-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: transparent !important;
  border: 1px solid #ff6b6b !important;
  color: #ff6b6b !important;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.close-tab-btn:hover {
  background-color: #ff6b6b !important;
  color: #ffffff !important;
  border-color: #ff6b6b !important;
  transform: scale(1.05);
}

.close-tab-btn:active {
  transform: scale(0.95);
}

/* ===== 禁用状态样式 ===== */
.close-tab-btn--disabled {
  background-color: transparent !important;
  border: 1px solid #4a4a4a !important;
  color: #6a6a6a !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.close-tab-btn--disabled:hover {
  background-color: transparent !important;
  border: 1px solid #4a4a4a !important;
  color: #6a6a6a !important;
  transform: none !important;
}

.close-tab-btn--disabled:active {
  transform: none !important;
}

.close-icon {
  font-size: 18px;
  color: inherit !important;
}
</style>
