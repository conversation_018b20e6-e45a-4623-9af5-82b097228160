<template>
  <div class="editor-view">
    <ProjectEditor />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useProjectStore } from "@/stores/haptics-project-store";
import ProjectEditor from "@/components/editor/HapticProjectEditor.vue";
import { useMessage } from "naive-ui";
import { useI18n } from "@/composables/useI18n";
import { logger, LogModule } from "@/utils/logger/logger";

const route = useRoute();
const router = useRouter();
const projectStore = useProjectStore();
const message = useMessage();
const { t } = useI18n();

// 在组件挂载时创建新项目或加载现有项目
onMounted(async () => {
  const exampleId = route.query.example_id as string | undefined;

  if (projectStore.activeProject && projectStore.projectPath) {
    // Store 中已有项目 (由 createNewProject 或 openRecentProject 设置)
    logger.info(LogModule.GENERAL, "EditorView: Using project from store", { projectName: projectStore.projectName });
    document.title = `RealityTap Studio - ${projectStore.projectName}`;
    // 无需其他操作，编辑器将自动使用 store 中的数据
  } else if (exampleId) {
    try {
      logger.info(LogModule.GENERAL, "EditorView: Loading example project", { exampleId });
      await loadExampleProject(exampleId);
    } catch (error) {
      logger.error(LogModule.GENERAL, `EditorView: Failed to load example project ${exampleId}`, error);
      projectStore.clearProjectData(); // 清理可能不完整的状态
      message.error(`Failed to load example project: ${error}`);
      router.push("/");
    }
  } else {
    // 没有项目被加载，也没有示例ID
    logger.warn(LogModule.GENERAL, "EditorView: No project in store and no exampleId provided. Redirecting to home.");
    projectStore.clearProjectData(); // 确保 store 是干净的
    message.info(t("editor.project.noProjectLoaded"));
    router.push("/");
  }
});

// 新增 loadExampleProject 函数
async function loadExampleProject(exampleId: string): Promise<void> {
  // 假设 projectStore.handleLoadProject 或其内部的 Tauri 命令能直接处理 exampleId
  const success = await projectStore.handleLoadProject(exampleId);
  if (!success) {
    throw new Error(`Failed to load example project '${exampleId}' using projectStore.handleLoadProject.`);
  }
  // 成功加载后，projectStore 会更新，document.title 应从中获取
  if (projectStore.projectName) {
    document.title = `RealityTap Studio - ${projectStore.projectName}`;
  } else {
    // Fallback if projectName is not set for some reason after load
    document.title = `RealityTap Studio - ${exampleId}`;
  }
}
</script>

<style scoped>
.editor-view {
  height: 100%;
  width: 100vw;
  overflow: hidden;
}
</style>
