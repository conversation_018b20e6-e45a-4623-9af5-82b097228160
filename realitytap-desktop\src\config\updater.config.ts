/**
 * 更新器配置
 * Updater Configuration
 *
 * 注意：端点配置现在通过 tauri.conf.json 文件管理，
 * 这里只保留客户端行为相关的配置。
 */

export interface UpdaterConfig {
  /** 检查间隔（毫秒） */
  checkInterval: number
  /** 是否启用自动检查 */
  autoCheck: boolean
  /** 是否启用后台检查 */
  backgroundCheck: boolean
  /** 最大重试次数 */
  maxRetries: number
  /** 请求超时时间（毫秒） */
  timeout: number
  /** 红点通知相关配置 */
  notification: {
    /** 是否启用红点通知 */
    enableBadge: boolean
    /** 通知持续时间（毫秒），0 表示永久显示直到用户查看 */
    badgeDuration: number
  }
}

/**
 * 默认更新器配置
 */
export const DEFAULT_UPDATER_CONFIG: UpdaterConfig = {
  checkInterval: 30 * 60 * 1000, // 30分钟
  autoCheck: true,
  backgroundCheck: true,
  maxRetries: 3,
  timeout: 30000, // 30秒
  notification: {
    enableBadge: true,
    badgeDuration: 0, // 永久显示直到用户查看
  }
}

/**
 * 开发环境配置
 */
export const DEV_UPDATER_CONFIG: UpdaterConfig = {
  ...DEFAULT_UPDATER_CONFIG,
  checkInterval: 5 * 60 * 1000, // 5分钟（开发环境更频繁）
  notification: {
    ...DEFAULT_UPDATER_CONFIG.notification,
    enableBadge: true, // 开发环境也启用红点通知便于测试
  }
}

/**
 * 获取当前环境的更新器配置
 */
export function getUpdaterConfig(): UpdaterConfig {
  const baseConfig = import.meta.env.DEV ? DEV_UPDATER_CONFIG : DEFAULT_UPDATER_CONFIG

  // 从环境变量读取检查间隔（如果设置了的话）
  const envInterval = import.meta.env.VITE_UPDATE_CHECK_INTERVAL
  if (envInterval && !isNaN(Number(envInterval))) {
    return {
      ...baseConfig,
      checkInterval: Number(envInterval) * 60 * 1000 // 转换分钟为毫秒
    }
  }

  return baseConfig
}

/**
 * 更新通道类型
 */
export type UpdateChannel = 'stable' | 'beta' | 'alpha'

/**
 * 更新通道配置
 */
export const UPDATE_CHANNELS: Record<UpdateChannel, { name: string; description: string }> = {
  stable: {
    name: '稳定版',
    description: '经过充分测试的稳定版本，推荐普通用户使用'
  },
  beta: {
    name: '测试版',
    description: '包含新功能的测试版本，可能存在一些问题'
  },
  alpha: {
    name: '开发版',
    description: '最新的开发版本，仅供开发者和高级用户使用'
  }
}

/**
 * 获取当前更新通道
 */
export function getCurrentChannel(): UpdateChannel {
  // 从环境变量或配置文件读取
  const channel = import.meta.env.VITE_UPDATE_CHANNEL as UpdateChannel
  return channel || 'stable'
}
