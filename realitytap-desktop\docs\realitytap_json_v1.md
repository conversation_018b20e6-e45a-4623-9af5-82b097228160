## RealityTap Haptics JSON V1:

```JSON
{
    "Metadata": {
        "Created": "2020-08-10",
        "Description": "Haptic editor design",
        "Version": 1
    },
    "Pattern": [
        {
            "Event": {
                "Parameters":{"Frequency":80, "Intensity":80},
                "Type":"transient",
                "RelativeTime":5
            }
        },
	    {
            "Event": {
                "Duration": 35,
                "Parameters": {
                    "Curve": [
                        {
                            "Frequency": 12,
                            "Intensity": 0,
                            "Time": 0
                        },
                        {
                            "Frequency": 12,
                            "Intensity": 0.7,
                            "Time": 20
                        },
                        {
                            "Frequency": 12,
                            "Intensity": 0,
                            "Time":30
                        },
                        {
                            "Frequency": 12,
                            "Intensity": 0,
                            "Time": 35
                        }
                    ],
                    "Frequency": 50,
                    "Intensity": 100
                },
                "Type": "continuous",
                "RelativeTime": 15
            }
        }
    ]
}
```
### 数据结构解析
- Version: 版本号，整型
- Created：创建时间，String类型
- Description：震动效果描述，String类型
- Pattern：事件列表
  - 瞬时事件
    - RelativeTime：相对开始时间, 整形, 单位ms
    - Type：事件类型
    - Parameters：事件参数
    - Frequency：震动频率, 整形, [0,100]
    - Intensity：震动强度, 整形, [0,100]
  - 包络事件
    - RelativeTime：相对开始时间, 整形, 单位ms
    - Type：事件类型
    - Duration：持续时间，整形, 单位ms
    - Parameters：事件参数
      - Intensity：包络事件全局振动强度, 整形, [0,100]
      - Frequency：包络事件全局振动频率, 整形, [0,100]
      - Curve：包络事件曲线关键点列表
        - Time：绝对时间，整形，单位ms
        - Intensity：关键点振动强度，浮点型，[0,1.0]
        - Frequency：关键点振动频率，整形，[-100,100]
### 注意点
- 一个Pattern可能包含不定个数的瞬时事件或者包络事件
- 瞬时事件的时长由振动频率决定，通常在10ms-25ms之间
- 相邻事件不得有时间重合
- Curve曲线包含4个关键点：
  - 第一个点为起始点，Time必须等于0，Intensity必须等于0
  - 第二个点为加速点
  - 第三个点为稳态点
  - 第四个点为结束点，Time必须等于Duration，Intensity必须等于0
  - 四个点的Time必须不得重合
  - 每个点的实际振动强度 = 自身振动强度 * 全局振动强度
  - 每个点的实际振动频率 = 自身振动频率 + 全局振动频率