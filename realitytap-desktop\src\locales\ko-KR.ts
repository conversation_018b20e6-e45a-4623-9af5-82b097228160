/**
 * 한국어 언어팩
 */

export default {
  // === common ===
  common: {
    confirm: "확인",
    cancel: "취소",
    cancelled: "작업이 취소되었습니다",
    save: "저장",
    delete: "삭제",
    close: "닫기",
    loading: "로딩 중...",
    error: "오류",
    clear: "지우기",
    about: "정보",
    retry: "다시 시도",
    dismiss: "무시",
    enabled: "활성화",
    disabled: "비활성화"
  },
  // === app ===
  app: {
    title: "RealityTap Haptics Studio"
  },
  // === dashboard ===
  dashboard: {
    // === tabs ===
    tabs: {
      projects: "프로젝트",
      learning: "학습 자료"
    },
    // === projects ===
    projects: {
      newProject: "새 프로젝트",
      newProjectSubtitle: "오디오 또는 햅틱 파일에서",
      openProject: "프로젝트 열기",
      openProjectSubtitle: "로컬 파일에서",
      recentProjects: "최근 프로젝트",
      exampleProjects: "예제 프로젝트",
      noRecentProjects: "최근 프로젝트 기록이 없습니다",
      goToLearning: "학습 페이지로 이동"
    }
  },
  // === project ===
  project: {
    // === create ===
    create: {
      success: "프로젝트가 성공적으로 생성되었습니다",
      failed: "프로젝트 생성에 실패했습니다"
    },
    // === open ===
    open: {
      failed: "프로젝트 열기에 실패했습니다",
      notFound: "프로젝트를 찾을 수 없습니다"
    },
    // === save ===
    save: {
      saving: "저장 중...",
      success: "파일이 성공적으로 저장되었습니다",
      noData: "저장할 이벤트 데이터가 없습니다",
      noFileSelected: "선택된 파일이 없습니다",
      saveFile: "파일 저장 (Ctrl+S)",
      noChanges: "파일이 변경되지 않았거나 이미 저장되었습니다"
    },
    // === undo ===
    undo: {
      undoAction: "실행 취소 (Ctrl+Z)",
      noUndoAvailable: "실행 취소할 작업이 없습니다",
      noFileSelected: "선택된 파일이 없습니다"
    },
    // === redo ===
    redo: {
      redoAction: "다시 실행 (Ctrl+Y)",
      noRedoAvailable: "다시 실행할 작업이 없습니다",
      noFileSelected: "선택된 파일이 없습니다"
    }
  },
  // === editor ===
  editor: {
    // === navigation ===
    navigation: {
      projects: "프로젝트",
      doubleClickToEdit: "더블클릭하여 편집",
      unsavedChanges: "저장되지 않은 변경사항"
    },
    // === eventProperties ===
    eventProperties: {
      title: "이벤트 속성",
      transientEvent: "순시 이벤트",
      continuousEvent: "연속 이벤트",
      startTime: "시작 시간",
      intensity: "강도",
      frequency: "주파수",
      duration: "지속 시간",
      globalIntensity: "강도 (전역)",
      globalFrequency: "주파수 (전역)",
      selectedCurvePoint: "선택된 곡선 포인트",
      pointNumber: "포인트 #",
      time: "시간",
      pointRelativeIntensity: "포인트 상대 강도",
      pointRelativeFrequency: "포인트 상대 주파수",
      computedAbsoluteIntensity: "계산된 절대 강도",
      computedAbsoluteFrequency: "계산된 절대 주파수",
      firstLastPointZeroIntensity: "첫 번째와 마지막 포인트는 항상 강도가 0",
      frequencyAdjustmentHint: "팁: {key} 키를 누르고 수직으로 드래그하여 주파수 조정",
      selectEventToAdjust: "속성을 조정하려면 타임라인에서 이벤트를 선택하세요."
    },
    // === duration ===
    duration: {
      increased: "지속 시간이 {duration} 밀리초 증가했습니다"
    },
    // === durationPanel ===
    durationPanel: {
      increaseDuration: "지속 시간 증가",
      milliseconds: "밀리초",
      confirm: "확인",
      hideAudio: "오디오 숨기기",
      showAudio: "오디오 표시",
      loadingAudio: "오디오 로딩 중..."
    },
    // === empty ===
    empty: {
      title: "새 햅틱 파일 추가",
      description: "단일 오디오 파일 또는 에셋 폴더를 드래그 앤 드롭하여 햅틱 디자인을 시작하세요",
      addHapticFile: "햅틱 파일 추가",
      addAudioFile: "오디오 파일 추가",
      addVideoFile: "비디오 파일 추가"
    },
    // === waveform ===
    waveform: {
      noFileSelected: "편집하려면 먼저 RealityTap 햅틱 파형 파일을 열어주세요",
      loading: "RealityTap 햅틱 효과 로딩 중..."
    },
    // === project ===
    project: {
      untitled: "제목 없는 프로젝트",
      renameSuccess: "프로젝트 이름이 변경되고 디렉토리가 이름 변경되었습니다",
      renameFailed: "프로젝트 이름 변경에 실패했습니다",
      noProjectLoaded: "현재 로드된 프로젝트가 없습니다. 프로젝트를 생성하거나 열어주세요."
    },
    // === file ===
    file: {
      confirmDelete: "삭제 확인",
      confirmDeleteMessage: "파일 \"{name}\"을(를) 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.",
      createSuccess: "햅틱 파일이 성공적으로 생성되었습니다",
      deleteSuccess: "파일 \"{name}\"이(가) 성공적으로 삭제되었습니다",
      deleteFailed: "파일 \"{name}\" 삭제 실패: {error}",
      selectFirst: "먼저 파일을 선택해주세요",
      noFileSelected: "선택된 파일이 없습니다",
      noEventData: "저장할 이벤트 데이터가 없습니다",
      saveSuccess: "파일이 성공적으로 저장되었습니다"
    },
    // === audio ===
    audio: {
      importingToRoot: "루트 디렉토리에 오디오 파일 가져오는 중...",
      importingToGroup: "그룹 \"{name}\"에 오디오 파일 가져오는 중...",
      importSuccess: "오디오 가져오기 및 .he 파일 생성이 성공했습니다",
      importFailed: "오디오 가져오기 실패: {error}",
      metadataFailed: "오디오 메타데이터 파싱에 실패했지만 가져오기는 완료되었습니다 (지속 시간을 가져올 수 없음)",
      amplitudeLoadFailed: "오디오 진폭 데이터 로딩 실패: {error}",
      processingFailed: "오디오 처리 실패: {error}",
      loadSuccess: "오디오 데이터 로딩이 성공했습니다",
      loadFailed: "오디오 데이터 로딩에 실패했습니다",
      noAudioFile: "현재 파일에 연결된 오디오 파일이 없습니다",
      checkFailed: "오디오 데이터 확인에 실패했습니다"
    },
    // === video ===
    video: {
      importingToRoot: "루트 디렉토리에 비디오 파일 가져오는 중...",
      importingToGroup: "그룹 \"{name}\"에 비디오 파일 가져오는 중...",
      importSuccess: "비디오 가져오기 및 .he 파일 생성이 성공했습니다",
      metadataFailed: "비디오 메타데이터 파싱에 실패했지만 가져오기는 완료되었습니다 (지속 시간을 가져올 수 없음)"
    },
    // === contextMenu ===
    contextMenu: {
      playEffect: "효과 재생",
      renameFile: "파일 이름 변경",
      deleteFile: "햅틱 파일 삭제",
      renameGroup: "현재 그룹 이름 변경",
      deleteGroup: "현재 그룹 삭제",
      newHapticFile: "새 햅틱 파일",
      importAudioFile: "오디오 파일 가져오기",
      importVideoFile: "비디오 파일 가져오기",
      newRootGroup: "새 루트 그룹",
      addFileToGroup: "그룹 \"{name}\"에 파일 추가",
      importAudioToGroup: "그룹 \"{name}\"에 오디오 가져오기",
      importVideoToGroup: "그룹 \"{name}\"에 비디오 가져오기",
      newChildGroup: "\"{name}\" 하위에 새 자식 그룹 생성",
      addEvent: "이벤트 추가",
      transientEvent: "순시 이벤트",
      continuousEvent: "연속 이벤트",
      deleteEvent: "이벤트 삭제",
      needSpace: "최소 {space}ms 공간이 필요",
      availableSpace: "사용 가능한 공간: {space}ms"
    },
    // === event ===
    event: {
      exceedsAudioDuration: "이벤트가 오디오 지속 시간을 초과합니다. 추가/편집할 수 없습니다!"
    },
    // === hapticFiles ===
    hapticFiles: {
      title: "햅틱 파일"
    },
    // === inlineEdit ===
    inlineEdit: {
      groupCreateSuccess: "그룹 \"{name}\"이(가) 성공적으로 생성되었습니다",
      groupCreateFailed: "그룹 생성 실패: {error}",
      groupNameEmpty: "그룹 이름은 비워둘 수 없습니다",
      groupNamePlaceholder: "그룹 이름 입력",
      newGroupPlaceholder: "새 그룹 이름 입력",
      newFileNamePlaceholder: "새 파일 이름 입력",
      groupRenameSuccess: "그룹 이름이 \"{oldName}\"에서 \"{newName}\"으로 변경되었습니다",
      groupRenameFailed: "그룹 이름 변경 실패: {error}",
      fileNameEmpty: "파일 이름은 비워둘 수 없습니다",
      fileNameMustEndWithHe: "파일 이름은 .he로 끝나야 합니다",
      fileRenameSuccess: "파일 이름이 \"{oldName}\"에서 \"{newName}\"으로 변경되었습니다",
      fileRenameFailed: "파일 이름 변경 실패: {error}"
    },
    // === groupDelete ===
    groupDelete: {
      confirmTitle: "그룹 삭제 확인",
      confirmMessage: "그룹 \"{name}\"을(를) 삭제하시겠습니까?",
      confirmWithContentTitle: "그룹 및 내용 삭제 확인",
      confirmWithContentMessage: "그룹 \"{name}\"에는 {count}개의 파일이 포함되어 있습니다. 그룹과 모든 내용을 삭제하시겠습니까?",
      confirmDelete: "삭제",
      deleteSuccess: "그룹 \"{name}\"이(가) 성공적으로 삭제되었습니다",
      deleteWithContentSuccess: "그룹 \"{name}\" 및 내용이 성공적으로 삭제되었습니다",
      deleteFailed: "그룹 삭제 실패: {error}",
      cancelled: "삭제 작업이 취소되었습니다"
    },
    // === dragAndDrop ===
    dragAndDrop: {
      targetFileNotFound: "대상 파일을 찾을 수 없습니다",
      cannotDropOnSelfOrDescendant: "항목을 자기 자신이나 하위 항목에 드롭할 수 없습니다",
      fileMovedToGroup: "파일이 그룹 \"{groupName}\"으로 이동되었습니다",
      fileMovedToRoot: "파일이 루트 디렉토리로 이동되었습니다",
      fileMoveSuccess: "파일 이동이 성공했습니다",
      groupMovedToGroup: "그룹이 그룹 \"{groupName}\"으로 이동되었습니다",
      groupMovedToRoot: "그룹이 루트 디렉토리로 이동되었습니다",
      groupMoveSuccess: "그룹 이동이 성공했습니다",
      moveFailed: "이동 실패",
      moveToRootFailed: "루트 디렉토리로 이동 실패",
      unknownGroup: "알 수 없는 그룹"
    }
  },
  // === device ===
  device: {
    // === status ===
    status: {
      connected: "연결됨",
      disconnected: "연결 안됨",
      connecting: "연결 중",
      disconnecting: "연결 해제 중",
      error: "오류",
      unknown: "알 수 없음",
      noDevices: "디바이스 없음",
      title: "디바이스 상태",
      total: "총 디바이스",
      errorDevices: "오류 디바이스",
      defaultDevice: "기본 디바이스",
      clickToOpen: "클릭하여 디바이스 관리자 열기",
      default: "기본값"
    },
    // === types ===
    types: {
      usb: "USB",
      wifi: "WiFi",
      bluetooth: "블루투스"
    },
    // === actions ===
    actions: {
      scan: "디바이스 스캔",
      refresh: "새로고침",
      connect: "연결",
      disconnect: "연결 해제",
      setDefault: "기본값으로 설정",
      rename: "이름 변경",
      remove: "제거",
      sendFile: "파일 전송",
      addTestDevice: "테스트 디바이스 추가",
      unsetDefault: "기본값 해제"
    },
    // === messages ===
    messages: {
      scanComplete: "디바이스 스캔이 완료되었습니다",
      scanFailed: "디바이스 스캔에 실패했습니다",
      refreshSuccess: "디바이스 목록이 새로고침되었습니다",
      refreshFailed: "새로고침에 실패했습니다",
      connectSuccess: "디바이스 연결이 성공했습니다",
      connectFailed: "디바이스 연결에 실패했습니다",
      disconnectSuccess: "디바이스 연결 해제가 성공했습니다",
      disconnectFailed: "디바이스 연결 해제에 실패했습니다",
      setDefaultSuccess: "기본 디바이스 설정이 성공했습니다",
      setDefaultFailed: "기본 디바이스 설정에 실패했습니다",
      removeSuccess: "디바이스 제거가 성공했습니다",
      removeFailed: "디바이스 제거에 실패했습니다",
      renameSuccess: "디바이스 이름 변경이 성공했습니다",
      renameFailed: "디바이스 이름 변경에 실패했습니다",
      sendFileInfo: "파일 전송 기능은 개발 중입니다...",
      sendFileFailed: "파일 전송에 실패했습니다",
      addTestDeviceSuccess: "테스트 디바이스 추가가 성공했습니다",
      addTestDeviceFailed: "테스트 디바이스 추가에 실패했습니다",
      initializeFailed: "디바이스 관리자 초기화에 실패했습니다"
    },
    // === filter ===
    filter: {
      deviceType: "디바이스 타입",
      connectionStatus: "연결 상태",
      searchDevices: "디바이스 검색..."
    },
    // === details ===
    details: {
      deviceId: "디바이스 ID",
      deviceType: "디바이스 타입",
      connectionStatus: "연결 상태",
      lastConnected: "마지막 연결",
      manufacturer: "제조사",
      model: "모델"
    },
    // === rename ===
    rename: {
      title: "디바이스 이름 변경",
      deviceName: "디바이스 이름",
      placeholder: "새 디바이스 이름 입력",
      nameRequired: "디바이스 이름은 필수입니다"
    },
    // === remove ===
    remove: {
      confirmTitle: "삭제 확인",
      confirmMessage: "디바이스 \"{name}\"을(를) 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다."
    },
    // === transmission ===
    transmission: {
      heFile: "HE 파일",
      audioFile: "오디오 파일",
      projectData: "프로젝트 데이터",
      deviceConfig: "디바이스 설정",
      priority: {
        low: "낮음",
        normal: "보통",
        high: "높음",
        urgent: "긴급"
      }
    },
    // === testDevice ===
    testDevice: {
      name: "테스트 디바이스 {number}",
      manufacturer: "테스트 제조사",
      model: "테스트 모델"
    },
    // === errors ===
    errors: {
      unknownError: "알 수 없는 오류",
      timeoutError: "연결 시간 초과",
      permissionDenied: "권한이 거부됨",
      deviceNotFound: "디바이스를 찾을 수 없음",
      deviceBusy: "디바이스가 사용 중",
      invalidParameter: "잘못된 매개변수"
    },
    // === management ===
    management: {
      title: "디바이스 관리"
    }
  },
  // === errors ===
  errors: {
    unknown: "알 수 없는 오류가 발생했습니다",
    networkError: "네트워크 오류",
    fileNotFound: "파일을 찾을 수 없습니다",
    operationFailed: "작업에 실패했습니다"
  },
  // === learning ===
  learning: {
    title: "햅틱 디자인 학습 자료",
    // === gettingStarted ===
    gettingStarted: {
      title: "햅틱 디자인 시작하기",
      description: "햅틱 디자인의 기본 사항과 효과적인 햅틱 피드백을 만드는 방법을 배웁니다.",
      // === tags ===
      tags: {
        beginner: "초급",
        tutorial: "튜토리얼"
      }
    },
    // === audioToHaptics ===
    audioToHaptics: {
      title: "오디오에서 햅틱으로 변환",
      description: "오디오 파일을 의미 있는 햅틱 피드백으로 변환하는 팁과 기법.",
      // === tags ===
      tags: {
        intermediate: "중급",
        tutorial: "튜토리얼"
      }
    },
    // === gaming ===
    gaming: {
      title: "게임용 햅틱",
      description: "게임 애플리케이션에서 햅틱 피드백을 구현하는 모범 사례.",
      // === tags ===
      tags: {
        gaming: "게임",
        caseStudy: "사례 연구"
      }
    },
    // === uxDesign ===
    uxDesign: {
      title: "햅틱 UX 디자인",
      description: "모바일 애플리케이션에서 햅틱 피드백을 사용하여 사용자 경험을 향상시키는 방법.",
      // === tags ===
      tags: {
        ux: "UX",
        mobile: "모바일"
      }
    },
    // === advanced ===
    advanced: {
      title: "고급 햅틱 기술",
      description: "복잡하고 섬세한 햅틱 경험을 만들기 위한 고급 기술.",
      // === tags ===
      tags: {
        advanced: "고급",
        tutorial: "튜토리얼"
      }
    },
    // === research ===
    research: {
      title: "햅틱 피드백 연구",
      description: "햅틱 피드백의 효과에 대한 최신 연구 및 학술 연구.",
      // === tags ===
      tags: {
        research: "연구",
        academic: "학술"
      }
    }
  },
  // === examples ===
  examples: {
    // === populationOne ===
    populationOne: {
      haptics: "8개 햅틱"
    },
    notImplemented: "기능 미구현",
    notImplementedMessage: "\"{title}\" 예제 프로젝트는 아직 구현되지 않았습니다. 곧 출시 예정입니다!"
  },
  // === update ===
  update: {
    downloading: "다운로드 중",
    installing: "설치 중",
    updateAvailable: "업데이트 사용 가능",
    newVersionAvailable: "새 버전 사용 가능",
    downloadNow: "지금 다운로드",
    installNow: "지금 설치",
    remindLater: "나중에 알림",
    installConfirmTitle: "업데이트 설치 확인",
    installConfirmMessage: "업데이트를 설치하려면 애플리케이션을 닫아야 합니다. 설치 후 새 버전으로 자동 재시작됩니다.",
    installConfirmDetails: "전체 과정은 약 30초가 소요됩니다. 모든 작업이 저장되었는지 확인하세요.",
    installConfirmWarning: "과정 중에 설치 프로그램을 수동으로 닫지 마세요.",
    currentVersion: "현재 버전",
    latestVersion: "최신 버전",
    releaseNotes: "릴리스 노트",
    fileSize: "파일 크기",
    confirmInstallation: "설치 확인",
    confirmInstall: "설치 확인",
    installationNotice: "설치 알림",
    applicationWillClose: "설치를 완료하기 위해 애플리케이션이 닫히고 완료 후 자동으로 재시작됩니다.",
    error: "업데이트 오류",
    installingMessage: "업데이트를 설치 중입니다. 잠시 기다려 주세요...",
    readyToInstall: "업데이트 설치 준비 완료",
    downloadComplete: "다운로드 완료",
    downloadCompleteMessage: "업데이트 파일 다운로드가 완료되었습니다. 「지금 설치」를 클릭하여 설치를 시작하세요.",
    cancelling: "취소 중",
    // === processManagement ===
    processManagement: {
      title: "프로세스 관리 확인",
      warningTitle: "경고",
      warningMessage: "업데이트 설치를 위해 다음 관련 프로세스를 종료해야 합니다. 모든 중요한 작업을 저장했는지 확인하세요.",
      processListTitle: "종료할 프로세스",
      closeAndInstall: "프로세스 종료 후 설치",
      closeStrategyTitle: "종료 방법",
      gracefulClose: "정상 종료",
      gracefulCloseDesc: "프로세스를 정상적으로 종료하여 프로그램이 데이터를 저장할 시간을 제공합니다",
      forceClose: "강제 종료",
      forceCloseDesc: "프로세스를 즉시 종료합니다. 데이터가 손실될 수 있습니다",
      critical: "중요",
      noticeTitle: "중요 사항",
      notice1: "프로세스를 종료하기 전에 모든 중요한 작업을 저장하세요",
      notice2: "중요한 프로세스 종료는 시스템 안정성에 영향을 줄 수 있습니다",
      notice3: "설치 완료 후 애플리케이션이 자동으로 재시작됩니다",
      // === processTypes ===
      processTypes: {
        MainApplication: "메인 앱",
        EditorWindow: "에디터",
        RenderProcess: "렌더러",
        AudioService: "오디오 서비스",
        FileMonitor: "파일 모니터",
        BackgroundService: "백그라운드 서비스",
        ChildProcess: "자식 프로세스",
        Unknown: "알 수 없는 프로세스"
      }
    }
  },
  // === i18nTest ===
  i18nTest: {
    title: "국제화 테스트",
    languageSwitchTest: "언어 전환 테스트",
    currentLanguage: "현재 언어",
    commonTextTest: "공통 텍스트 테스트",
    errorMessageTest: "오류 메시지 테스트",
    languageDetectionInfo: "언어 감지 정보",
    exampleError: "디스크 공간 부족",
    // === labels ===
    labels: {
      confirm: "확인",
      cancel: "취소",
      save: "저장",
      delete: "삭제",
      loading: "로딩 중",
      saveFailedExample: "저장 실패 (예시)",
      unknownError: "알 수 없는 오류",
      networkError: "네트워크 오류",
      fileNotFound: "파일을 찾을 수 없음"
    }
  },
  // === forceUpdate ===
  forceUpdate: {
    title: "강제 업데이트",
    notice: "중요한 업데이트",
    noticeMessage: "이것은 필수 업데이트입니다. 애플리케이션을 계속 사용하려면 설치해야 합니다.",
    newVersionRequired: "새 버전이 필요합니다",
    readyToInstall: "업데이트 다운로드가 완료되었습니다, 설치 준비 완료",
    startDownload: "다운로드 시작",
    installNow: "지금 설치",
    retryDownload: "다운로드 재시도",
    exitApplication: "애플리케이션 종료"
  },
  // === about ===
  about: {
    title: "정보",
    loading: "버전 정보 로딩 중...",
    version: "버전",
    buildInfo: "빌드 정보",
    platform: "플랫폼",
    // === updateCheck ===
    updateCheck: {
      title: "버전 확인",
      checking: "업데이트 확인 중...",
      checkNow: "지금 확인",
      newVersionAvailable: "새 버전 사용 가능",
      latestVersion: "최신 버전",
      upToDate: "최신 버전을 사용 중입니다",
      checkFailed: "업데이트 확인 실패",
      viewUpdate: "업데이트 보기"
    }
  },
  // === demo ===
  demo: {
    installProcessTitle: "설치 프로세스",
    step1Title: "사용자 설치 확인",
    step1Description: "설치 확인 대화상자를 표시하고 앱이 닫히고 자동으로 재시작됨을 설명",
    step2Title: "설치 스크립트 생성",
    step2Description: "설치 및 재시작 로직을 포함하는 독립적인 배치 스크립트 생성",
    step3Title: "독립 설치 시작",
    step3Description: "독립적인 설치 프로그램을 시작하고 현재 앱 종료",
    step4Title: "애플리케이션 자동 재시작",
    step4Description: "설치 완료 후 새 버전을 자동으로 시작"
  },
  // === installer ===
  installer: {
    ready: "준비 완료",
    preparing: "준비 중",
    installing: "설치 중",
    success: "설치 성공",
    failed: "설치 실패",
    cancelled: "취소됨",
    completed: "완료됨",
    // === operations ===
    operations: {
      initializing: "초기화 중",
      validating: "패키지 검증 중",
      waitingForExit: "앱 종료 대기 중",
      backingUp: "파일 백업 중",
      installing: "설치 중",
      restarting: "애플리케이션 재시작 중",
      completed: "설치 완료",
      failed: "설치 실패",
      cancelled: "취소됨"
    },
    initializing: "설치 프로그램 초기화 중",
    installSuccess: "설치가 성공적으로 완료되었습니다",
    unknownError: "알 수 없는 오류",
    preparingExit: "애플리케이션 종료 준비 중",
    cancelling: "취소 중",
    installCancelled: "설치가 취소되었습니다"
  },
  // === ota ===
  ota: {
    checkingUpdates: "업데이트 확인 중",
    closingProcesses: "프로세스 종료 중",
    downloadFailed: "다운로드 실패",
    downloadingUpdate: "업데이트 다운로드 중",
    installationCompleted: "설치 완료",
    installationFailed: "설치 실패",
    installingUpdate: "업데이트 설치 중",
    noDownloadedFile: "다운로드된 파일이 없습니다",
    noUpdateInfo: "업데이트 정보가 없습니다",
    noUpdatesAvailable: "사용 가능한 업데이트가 없습니다",
    preparingInstallation: "설치 준비 중",
    verificationFailed: "검증 실패",
    verifyingUpdate: "업데이트 검증 중"
  },
  // === debug ===
  debug: {
    info: "디버그 정보:",
    isLoading: "로딩 상태",
    hasRecentProjects: "최근 프로젝트 있음",
    recentProjectsLength: "최근 프로젝트 수",
    recentProjectsContent: "최근 프로젝트 내용",
    title: "디버그 도구",
    activated: "디버그 모드가 활성화되었습니다!",
    // === button ===
    button: {
      tooltip: "디버그 도구"
    },
    // === menu ===
    menu: {
      settings: "디버그 설정",
      viewLogs: "로그 보기",
      openLogFolder: "로그 폴더 열기",
      exportDebugInfo: "디버그 정보 내보내기"
    },
    // === settings ===
    settings: {
      title: "디버그 설정",
      enableDebug: "디버그 모드 활성화",
      debugEnabled: "디버그 모드 활성화됨",
      debugDisabled: "디버그 모드 비활성화됨",
      debugEnabledDesc: "현재 개발 모드에서 실행 중이며, 디버그 기능이 자동으로 활성화되었습니다.",
      debugDisabledDesc: "현재 프로덕션 모드에서 실행 중이며, 디버그 기능이 자동으로 비활성화되었습니다.",
      buildMode: "빌드 모드",
      currentConfig: "현재 구성",
      logLevel: "로그 레벨",
      logOtaOperations: "OTA 작업 로그 기록",
      logDeviceOperations: "장치 작업 로그 기록",
      viewLogs: "로그 보기",
      openLogFolder: "로그 폴더 열기",
      resetDefault: "기본값으로 재설정",
      saveSuccess: "디버그 설정이 성공적으로 저장되었습니다",
      resetSuccess: "기본 설정으로 재설정되었습니다",
      // === notice ===
      notice: {
        title: "중요 사항",
        sessionOnly: "디버그 설정은 현재 세션에서만 유효하며, 앱 재시작 후 기본값으로 복원됩니다",
        performance: "상세 로깅을 활성화하면 애플리케이션 성능에 영향을 줄 수 있습니다",
        logLocation: "로그 파일은 애플리케이션 데이터 디렉토리에 저장됩니다"
      },
      // === validation ===
      validation: {
        levelRequired: "로그 레벨을 선택해주세요"
      },
      // === errors ===
      errors: {
        loadFailed: "디버그 구성 로드에 실패했습니다",
        saveFailed: "디버그 구성 저장에 실패했습니다",
        openFolderFailed: "로그 폴더 열기에 실패했습니다"
      }
    },
    // === logViewer ===
    logViewer: {
      title: "로그 뷰어",
      refresh: "새로고침",
      clear: "지우기",
      export: "내보내기",
      maxLines: "최대 줄 수",
      autoRefreshOn: "자동 새로고침",
      autoRefreshOff: "수동 새로고침",
      loading: "로그 로딩 중...",
      noLogs: "로그 내용이 없습니다",
      logPath: "로그 경로",
      unknown: "알 수 없음",
      lines: "줄 수",
      size: "크기",
      clearSuccess: "로그가 지워졌습니다",
      exportSuccess: "디버그 정보가 내보내졌습니다: {path}",
      // === errors ===
      errors: {
        getPathFailed: "로그 파일 경로 가져오기에 실패했습니다",
        readFailed: "로그 파일 읽기에 실패했습니다",
        clearFailed: "로그 파일 지우기에 실패했습니다",
        exportFailed: "디버그 정보 내보내기에 실패했습니다"
      }
    },
    // === errors ===
    errors: {
      openFolderFailed: "로그 폴더 열기에 실패했습니다",
      exportFailed: "디버그 정보 내보내기에 실패했습니다"
    },
    exportSuccess: "디버그 정보가 내보내졌습니다: {path}"
  },
  // === librtcore ===
  librtcore: {
    error: {
      general: "librtcore 오류: {message}",
      retrying: "librtcore 초기화에 실패했습니다. 재시도 중: {message}",
      manualRetryRequired: "librtcore 초기화에 실패했습니다. 수동 재시도가 필요합니다: {message}",
      fallbackMode: "librtcore가 폴백 모드로 전환되었습니다. 일부 기능을 사용할 수 없을 수 있습니다",
      fallbackModeActive: "현재 폴백 모드입니다. 햅틱 재생 기능이 제한됩니다",
      fallbackModeExited: "폴백 모드를 종료했습니다. 기능이 정상적으로 복구되었습니다",
      resetting: "librtcore 시스템을 재설정 중...",
      retrySuccess: "재시도가 성공했습니다. librtcore가 정상적으로 복구되었습니다",
      retryFailed: "재시도에 실패했습니다. 장치 연결 또는 설정을 확인해주세요",
      retryError: "재시도 과정에서 오류가 발생했습니다"
    }
  },
  // === playEffect ===
  playEffect: {
    loading: "파일 데이터 로딩 중...",
    dialogTitle: "효과 재생 - {fileName}",
    dialogTitleDefault: "효과 재생",
    fileInfo: "파일 정보",
    fileName: "파일 이름",
    fileUuid: "파일 UUID",
    filePath: "파일 경로",
    lastModified: "마지막 수정",
    jsonData: "JSON 데이터",
    copyJson: "JSON 복사",
    downloadJson: "JSON 다운로드",
    copySuccess: "JSON 데이터가 클립보드에 복사되었습니다",
    copyFailed: "복사 실패",
    downloadSuccess: "JSON 파일 다운로드 성공",
    downloadFailed: "다운로드 실패",
    errorPlaying: "효과 재생 실패: {error}",
    notImplemented: "효과 재생 기능이 구현되지 않았습니다",

    // 새로운 재생 제어 관련
    play: "재생",
    stop: "정지",
    pause: "일시정지",
    resume: "재개",
    save: "저장",
    saveAsRtp: "RTP로 저장",
    saveToFile: "파일로 저장",
    saving: "저장 중...",
    saveSuccess: "저장 성공",
    saveFailed: "저장 실패: {error}",
    selectSaveLocation: "저장 위치 선택",
    saveDialogTitle: "햅틱 데이터 파일 저장",
    noData: "저장할 데이터가 없습니다",

    // 모터 선택 관련
    motorSelection: "모터 선택",
    motorModel: "모터 모델",
    selectMotor: "모터 모델을 선택하세요",
    motorLoadFailed: "모터 설정 로드 실패, 기본 설정 사용",

    // 샘플링 레이트 선택 관련
    samplingRateSelection: "샘플링 레이트 선택",
    selectSamplingRate: "샘플링 레이트를 선택하세요",
    samplingRate6Khz: "6KHz - 기본 효과",
    samplingRate8Khz: "8KHz - 일반적인 선택",
    samplingRate12Khz: "12KHz - 중간 정밀도",
    samplingRate24Khz: "24KHz - 고정밀도 효과",

    // 실제 주파수 선택 관련
    actualFrequencySelection: "실제 주파수 선택",
    selectActualFrequency: "실제 주파수를 선택하세요",

    // 캔버스 관련
    canvas: "캔버스",
    noEvents: "이벤트 데이터 없음",
    timeAxis: "시간축",
    amplitudeAxis: "진폭축",



    // 제어 패널
    controls: "재생 제어",
    settings: "설정",
    volume: "볼륨",
    loop: "반복 재생",

    // librtcore 통합 관련
    initializingLibrtcore: "햅틱 알고리즘 라이브러리 초기화 중...",
    initSuccess: "햅틱 알고리즘 라이브러리 초기화 성공",
    reinitSuccess: "햅틱 알고리즘 라이브러리 재초기화 성공",
    librtcoreError: "햅틱 알고리즘 라이브러리 오류",
    retryInit: "초기화 재시도",
    clearError: "오류 지우기"
  }
} as const;
