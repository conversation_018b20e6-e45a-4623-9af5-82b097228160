import type { RenderableEvent, RenderableTransientEvent, RenderableContinuousEvent, ValidationResult } from "../types";

/**
 * 数据验证工具函数
 */

/**
 * 验证事件数据的完整性和有效性
 */
export function validateEventsData(events: RenderableEvent[]): ValidationResult {
  const errors: string[] = [];

  if (!Array.isArray(events)) {
    errors.push("Events must be an array");
    return { valid: false, errors };
  }

  events.forEach((event, index) => {
    // 基础字段验证
    if (!event.id || typeof event.id !== "string") {
      errors.push(`Event ${index}: missing or invalid id`);
    }

    if (!event.type || !["transient", "continuous"].includes(event.type)) {
      errors.push(`Event ${index}: invalid type, must be 'transient' or 'continuous'`);
    }

    if (typeof event.startTime !== "number" || event.startTime < 0) {
      errors.push(`Event ${index}: invalid startTime, must be a non-negative number`);
    }

    // 类型特定验证
    if (event.type === "transient") {
      const transientEvent = event as RenderableTransientEvent;
      if (typeof transientEvent.intensity !== "number" || transientEvent.intensity < 0 || transientEvent.intensity > 100) {
        errors.push(`Event ${index}: invalid intensity, must be between 0-100`);
      }
      if (typeof transientEvent.frequency !== "number" || transientEvent.frequency < 0 || transientEvent.frequency > 100) {
        errors.push(`Event ${index}: invalid frequency, must be between 0-100`);
      }
    } else if (event.type === "continuous") {
      const continuousEvent = event as RenderableContinuousEvent;
      if (typeof continuousEvent.eventIntensity !== "number" || continuousEvent.eventIntensity < 0 || continuousEvent.eventIntensity > 100) {
        errors.push(`Event ${index}: invalid eventIntensity, must be between 0-100`);
      }
      if (typeof continuousEvent.eventFrequency !== "number" || continuousEvent.eventFrequency < 0 || continuousEvent.eventFrequency > 100) {
        errors.push(`Event ${index}: invalid eventFrequency, must be between 0-100`);
      }
      if (!Array.isArray(continuousEvent.curves) || continuousEvent.curves.length < 2) {
        errors.push(`Event ${index}: continuous event must have at least 2 curve points`);
      }
    }
  });

  return { valid: errors.length === 0, errors };
}

/**
 * 验证fileUuid的有效性
 */
export function validateFileUuid(fileUuid: string | null | undefined): string {
  if (!fileUuid || typeof fileUuid !== "string" || fileUuid.trim() === "") {
    throw new Error("无效的fileUuid: 文件级别状态管理需要有效的文件UUID");
  }
  return fileUuid.trim();
}

/**
 * 验证时间范围的有效性
 */
export function validateTimeRange(startTime: number, endTime: number): boolean {
  return typeof startTime === "number" && 
         typeof endTime === "number" && 
         startTime >= 0 && 
         endTime > startTime;
}

/**
 * 验证强度值的有效性
 */
export function validateIntensity(intensity: number): boolean {
  return typeof intensity === "number" && intensity >= 0 && intensity <= 100;
}

/**
 * 验证频率值的有效性
 */
export function validateFrequency(frequency: number): boolean {
  return typeof frequency === "number" && frequency >= 0 && frequency <= 100;
}

/**
 * 验证持续时间的有效性
 */
export function validateDuration(duration: number): boolean {
  return typeof duration === "number" && duration > 0;
}
