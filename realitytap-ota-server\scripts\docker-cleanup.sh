#!/bin/bash

# RealityTap OTA Server Docker Cleanup Script
# This script completely removes the Docker deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

print_info "RealityTap OTA Server Docker Cleanup"
print_info "===================================="

# Change to project directory
cd "$PROJECT_DIR"

# Ask for confirmation
echo
print_warning "This will completely remove the RealityTap OTA Server deployment:"
print_warning "- Stop all running containers"
print_warning "- Remove containers and networks"
print_warning "- Remove Docker images"
print_warning "- Clean up Docker build cache"
print_warning "- Optionally remove persistent data volumes"
echo

read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Operation cancelled"
    exit 0
fi

print_info "Starting cleanup process..."

# Check if systemd service is running and stop it
if command -v systemctl &> /dev/null && systemctl is-active --quiet realitytap-ota.service 2>/dev/null; then
    print_info "Stopping systemd service..."
    sudo systemctl stop realitytap-ota.service 2>/dev/null || true
fi

# Stop and remove containers
print_info "Stopping and removing containers..."
docker-compose -f docker/docker-compose.http.yml down --remove-orphans 2>/dev/null || true
docker-compose -f docker/docker-compose.https.yml down --remove-orphans 2>/dev/null || true

# Remove networks
print_info "Removing Docker networks..."
docker network rm realitytap-ota-network 2>/dev/null || true

# Remove images
print_info "Removing Docker images..."
docker rmi realitytap-ota-server:latest 2>/dev/null || true
docker rmi realitytap-ota-server_realitytap-ota-server:latest 2>/dev/null || true
docker rmi $(docker images -q --filter "reference=realitytap-ota-server*") 2>/dev/null || true

# Clean up build cache
print_info "Cleaning Docker build cache..."
docker builder prune -f

# Ask about host data directories
echo
print_warning "Do you want to remove host data directories?"
print_warning "This will delete all application data including:"
print_warning "- Database files (docker/data/storage/database/)"
print_warning "- Release files (docker/data/storage/releases/)"
print_warning "- Log files (docker/data/storage/logs/)"
print_warning "- Backup files (docker/data/storage/backup/)"
print_warning "- SSL certificates (ssl/)"
print_warning "- Signing keys (keys/)"
echo

read -p "Remove host data directories? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "Removing host data directories..."

    # Remove storage data directory
    if [ -d "docker/data" ]; then
        rm -rf docker/data
        print_success "Storage data directory removed: docker/data"
    else
        print_info "Storage data directory not found: docker/data"
    fi

    # Remove SSL certificates directory
    if [ -d "ssl" ]; then
        rm -rf ssl
        print_success "SSL certificates directory removed: ssl"
    else
        print_info "SSL certificates directory not found: ssl"
    fi

    # Remove signing keys directory
    if [ -d "keys" ]; then
        rm -rf keys
        print_success "Signing keys directory removed: keys"
    else
        print_info "Signing keys directory not found: keys"
    fi

    print_success "Host data directories removed"
else
    print_info "Host data directories preserved"
fi

# Clean up unused Docker volumes (in case there are any legacy volumes)
print_info "Cleaning up unused Docker volumes..."
docker volume prune -f

# Clean up unused images
print_info "Cleaning up unused Docker images..."
docker image prune -f

# Show final status
print_success "Cleanup completed successfully!"

print_info "Remaining Docker resources:"
echo
print_info "Images:"
docker images | grep -E "(realitytap|REPOSITORY)" || echo "No RealityTap images found"
echo
print_info "Volumes:"
docker volume ls | grep -E "(realitytap|DRIVER)" || echo "No RealityTap volumes found"
echo
print_info "Networks:"
docker network ls | grep -E "(realitytap|NETWORK)" || echo "No RealityTap networks found"
echo
print_info "Host directories:"
echo "- Storage data: $([ -d "docker/data" ] && echo "EXISTS" || echo "REMOVED")"
echo "- SSL certificates: $([ -d "ssl" ] && echo "EXISTS" || echo "REMOVED")"
echo "- Signing keys: $([ -d "keys" ] && echo "EXISTS" || echo "REMOVED")"

print_success "RealityTap OTA Server deployment has been completely removed!"
