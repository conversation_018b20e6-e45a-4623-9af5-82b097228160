<template>
  <div class="multi-upload-test">
    <n-page-header title="多文件上传测试" subtitle="测试 .msi + .sig 文件批量上传功能">
      <template #extra>
        <n-button @click="showUploadModal = true" type="primary">
          测试多文件上传
        </n-button>
      </template>
    </n-page-header>

    <n-card>
      <n-alert type="info" title="测试说明">
        <p>此页面用于测试多文件上传功能，支持以下场景：</p>
        <ul>
          <li>单文件上传：选择一个 .msi/.exe/.dmg 等安装包文件</li>
          <li>多文件上传：同时选择安装包文件和对应的 .sig 签名文件</li>
          <li>文件验证：系统会验证文件名匹配和文件类型</li>
        </ul>
      </n-alert>

      <div style="margin-top: 16px;">
        <h3>测试结果</h3>
        <div v-if="testResults.length === 0">
          <n-empty description="暂无测试结果" />
        </div>
        <div v-else>
          <n-timeline>
            <n-timeline-item
              v-for="(result, index) in testResults"
              :key="index"
              :type="result.success ? 'success' : 'error'"
              :title="result.title"
              :content="result.message"
              :time="result.time"
            />
          </n-timeline>
        </div>
      </div>
    </n-card>

    <!-- 上传测试模态框 -->
    <n-modal
      v-model:show="showUploadModal"
      preset="card"
      title="多文件上传测试"
      style="width: 700px"
      :mask-closable="false"
    >
      <chunk-file-upload @success="handleUploadSuccess" @cancel="showUploadModal = false" />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import ChunkFileUpload from '@/components/Common/ChunkFileUpload.vue';
import {
  NAlert,
  NButton,
  NCard,
  NEmpty,
  NModal,
  NPageHeader,
  NTimeline,
  NTimelineItem,
} from 'naive-ui';
import { ref } from 'vue';

interface TestResult {
  title: string;
  message: string;
  success: boolean;
  time: string;
}

const showUploadModal = ref(false);
const testResults = ref<TestResult[]>([]);

const handleUploadSuccess = () => {
  showUploadModal.value = false;
  
  const result: TestResult = {
    title: '上传成功',
    message: '文件上传完成，元数据已保存',
    success: true,
    time: new Date().toLocaleString(),
  };
  
  testResults.value.unshift(result);
};

const handleUploadError = (error: string) => {
  const result: TestResult = {
    title: '上传失败',
    message: error,
    success: false,
    time: new Date().toLocaleString(),
  };
  
  testResults.value.unshift(result);
};
</script>

<style scoped>
.multi-upload-test {
  max-width: 1200px;
  margin: 0 auto;
}

ul {
  margin: 8px 0;
  padding-left: 20px;
}

li {
  margin: 4px 0;
}
</style>
