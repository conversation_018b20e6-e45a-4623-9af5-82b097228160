import type { RenderableEvent, RenderableTransientEvent } from "@/types/haptic-editor";
import type { DragTarget } from "./useDragState";
import type { DragHandlerConfig } from "./useDragConfig";
import { isPointInRadius } from "../utils/coordinate";
import { SELECTED_POINT_RADIUS } from "../utils/drawing-helpers";
import { useDragBoundaryValidator } from "./useDragBoundaryValidator";
import type { Ref } from "vue";

/**
 * 瞬态事件拖拽处理器
 * 专门处理瞬态事件的拖拽逻辑，包括峰值点强度调整和事件整体移动
 */
export function useTransientDragProcessor(
  config: DragHandlerConfig,
  // 拖拽状态
  draggedEvent: Ref<RenderableEvent | null>,
  draggingTarget: Ref<DragTarget | null>,
  currentDraggedIntensity: Ref<number>,
  // 状态更新函数
  setupTransientPeakDrag: (event: RenderableEvent, transient: RenderableTransientEvent) => void,
  setupEventDrag: (event: RenderableEvent) => void,
  updateTransientPeakDragValues: (intensity: number) => void
) {
  const boundaryValidator = useDragBoundaryValidator();
  const { validateTransientPeakBoundary } = boundaryValidator;

  /**
   * 检测瞬态事件的命中测试
   * @param clickX 点击的X坐标
   * @param clickY 点击的Y坐标
   * @param event 瞬态事件
   * @returns 命中测试结果
   */
  const hitTestTransientEvent = (
    clickX: number,
    clickY: number,
    event: RenderableTransientEvent
  ): { hitType: "peak" | "body" | "none"; event: RenderableTransientEvent } => {
    const peakX = config.mapTimeToXLocal(event.peakTime);
    const peakY = config.mapIntensityToYLocal(event.intensity);

    // 检查是否命中峰值点
    if (isPointInRadius(clickX, clickY, peakX, peakY, SELECTED_POINT_RADIUS * 2)) {
      return { hitType: "peak", event };
    }

    // 检查是否命中事件体（这里可以添加更复杂的事件体检测逻辑）
    // 简化处理：如果在事件时间范围内且在合理的Y范围内，认为命中事件体
    const eventStartX = config.mapTimeToXLocal(event.startTime);
    const eventEndX = config.mapTimeToXLocal(event.stopTime);
    const eventBaseY = config.mapIntensityToYLocal(0);

    if (clickX >= eventStartX && clickX <= eventEndX && clickY >= peakY && clickY <= eventBaseY) {
      return { hitType: "body", event };
    }

    return { hitType: "none", event };
  };

  /**
   * 处理瞬态事件的鼠标按下事件
   * @param clickX 点击的X坐标
   * @param clickY 点击的Y坐标
   * @param event 瞬态事件
   * @returns 是否处理了该事件
   */
  const handleTransientMouseDown = (clickX: number, clickY: number, event: RenderableTransientEvent): { handled: boolean; targetType?: DragTarget } => {
    const hitResult = hitTestTransientEvent(clickX, clickY, event);

    if (hitResult.hitType === "peak") {
      // 命中瞬态峰值点 - 拖动改变振幅
      setupTransientPeakDrag(event, event);
      return { handled: true, targetType: "transientPeak" };
    } else if (hitResult.hitType === "body") {
      // 命中瞬态事件体但不是峰值点 - 拖动改变时间
      setupEventDrag(event);
      return { handled: true, targetType: "event" };
    }

    return { handled: false };
  };

  /**
   * 处理瞬态事件峰值点的拖拽
   * @param currentY 当前鼠标Y坐标
   */
  const handleTransientPeakDrag = (currentY: number) => {
    if (draggingTarget.value !== "transientPeak" || !draggedEvent.value || draggedEvent.value.type !== "transient") {
      return;
    }

    const newIntensity = config.mapYToIntensityLocal(currentY);

    // 使用边界验证器验证强度值
    const validation = validateTransientPeakBoundary(newIntensity);
    const validatedIntensity = validation.adjustedValue || newIntensity;

    updateTransientPeakDragValues(validatedIntensity);
  };

  /**
   * 设置瞬态事件拖拽的光标样式
   * @param targetType 拖拽目标类型
   */
  const setTransientDragCursor = (targetType: DragTarget) => {
    if (!config.canvas.value) return;

    if (targetType === "transientPeak") {
      config.canvas.value.style.cursor = "ns-resize";
    } else if (targetType === "event") {
      config.canvas.value.style.cursor = "grabbing";
    }
  };

  /**
   * 验证瞬态事件拖拽是否有效
   * @param event 瞬态事件
   * @param targetType 拖拽目标类型
   * @returns 验证结果
   */
  const validateTransientDrag = (_event: RenderableTransientEvent, targetType: DragTarget): { isValid: boolean; errorMessage?: string } => {
    if (targetType === "transientPeak") {
      // 验证峰值强度拖拽
      const validation = validateTransientPeakBoundary(currentDraggedIntensity.value);
      return {
        isValid: validation.isValid,
        errorMessage: validation.errorMessage,
      };
    }

    // 事件整体拖拽的验证在其他地方处理
    return { isValid: true };
  };

  /**
   * 获取瞬态事件拖拽的描述信息
   * @param targetType 拖拽目标类型
   * @returns 描述信息
   */
  const getTransientDragDescription = (targetType: DragTarget): string => {
    switch (targetType) {
      case "transientPeak":
        return "调整瞬态事件强度";
      case "event":
        return "拖拽瞬态事件位置";
      default:
        return "瞬态事件拖拽操作";
    }
  };

  /**
   * 检查是否应该显示瞬态事件的拖拽提示
   * @param event 瞬态事件
   * @param mouseX 鼠标X坐标
   * @param mouseY 鼠标Y坐标
   * @returns 是否显示提示及提示内容
   */
  const shouldShowTransientDragHint = (event: RenderableTransientEvent, mouseX: number, mouseY: number): { shouldShow: boolean; hintText?: string } => {
    const hitResult = hitTestTransientEvent(mouseX, mouseY, event);

    if (hitResult.hitType === "peak") {
      return {
        shouldShow: true,
        hintText: "拖拽调整强度",
      };
    } else if (hitResult.hitType === "body") {
      return {
        shouldShow: true,
        hintText: "拖拽移动事件",
      };
    }

    return { shouldShow: false };
  };

  /**
   * 计算瞬态事件拖拽的约束信息
   * @param event 瞬态事件
   * @param targetType 拖拽目标类型
   * @returns 约束信息
   */
  const getTransientDragConstraints = (_event: RenderableTransientEvent, targetType: DragTarget): { minValue?: number; maxValue?: number; constraintType: string } => {
    if (targetType === "transientPeak") {
      return {
        minValue: 0,
        maxValue: 100,
        constraintType: "intensity",
      };
    }

    return {
      constraintType: "time",
    };
  };

  /**
   * 重置瞬态事件拖拽状态
   */
  const resetTransientDragState = () => {
    // 重置光标样式
    if (config.canvas.value) {
      config.canvas.value.style.cursor = "default";
    }
  };

  return {
    // 命中测试
    hitTestTransientEvent,

    // 事件处理
    handleTransientMouseDown,
    handleTransientPeakDrag,

    // UI 相关
    setTransientDragCursor,
    shouldShowTransientDragHint,

    // 验证和约束
    validateTransientDrag,
    getTransientDragConstraints,

    // 工具函数
    getTransientDragDescription,
    resetTransientDragState,
  };
}
