version: '3.8'

services:
  realitytap-ota-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: realitytap-ota-server
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      - STORAGE_PATH=/app/storage
      - LOG_LEVEL=info
      # Admin credentials (change these in production!)
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      - SESSION_TIMEOUT=${SESSION_TIMEOUT:-3600000}
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/storage/logs
    networks:
      - realitytap-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ota-server.rule=Host(`ota.realitytap.local`)"
      - "traefik.http.routers.ota-server.entrypoints=web"
      - "traefik.http.services.ota-server.loadbalancer.server.port=3000"

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: realitytap-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - realitytap-ota-server
    networks:
      - realitytap-network
    profiles:
      - production

networks:
  realitytap-network:
    driver: bridge

volumes:
  storage-data:
    driver: local
  logs-data:
    driver: local
