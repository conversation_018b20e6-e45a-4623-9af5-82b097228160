/**
 * 前端加密工具类
 * 提供兼容的哈希计算功能，支持安全和非安全环境
 */

/**
 * 检查是否支持 Web Crypto API
 */
export const isWebCryptoSupported = (): boolean => {
  return typeof crypto !== 'undefined' && 
         typeof crypto.subtle !== 'undefined' && 
         typeof crypto.subtle.digest === 'function';
};

/**
 * 备用哈希计算函数（使用简单的哈希算法）
 * 在非安全环境下使用，提供基本的文件唯一性标识
 */
export const calculateFileHashFallback = async (file: File): Promise<string> => {
  const buffer = await file.arrayBuffer();
  const uint8Array = new Uint8Array(buffer);
  
  // 使用简单的哈希算法作为备用方案
  let hash = 0;
  for (let i = 0; i < uint8Array.length; i++) {
    const char = uint8Array[i];
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  
  // 将哈希值转换为十六进制字符串，并确保长度一致
  const hashHex = Math.abs(hash).toString(16).padStart(8, '0');
  
  // 为了提供更好的唯一性，添加文件大小和时间戳
  const sizeHex = file.size.toString(16).padStart(8, '0');
  const timeHex = file.lastModified.toString(16).padStart(8, '0');
  
  return `${hashHex}${sizeHex}${timeHex}`;
};

/**
 * 计算文件哈希值
 * 优先使用 Web Crypto API，在不支持的环境下使用备用方案
 */
export const calculateFileHash = async (file: File): Promise<string> => {
  try {
    if (isWebCryptoSupported()) {
      // 使用 Web Crypto API（推荐方式）
      const buffer = await file.arrayBuffer();
      const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } else {
      // 在非安全环境下使用备用方案
      console.warn('Web Crypto API 不可用，使用备用哈希计算方法');
      return await calculateFileHashFallback(file);
    }
  } catch (error) {
    console.error('计算文件哈希失败，使用备用方案:', error);
    return await calculateFileHashFallback(file);
  }
};

/**
 * 计算字符串哈希值
 */
export const calculateStringHash = async (data: string): Promise<string> => {
  try {
    if (isWebCryptoSupported()) {
      const encoder = new TextEncoder();
      const buffer = encoder.encode(data);
      const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } else {
      // 备用方案：简单字符串哈希
      let hash = 0;
      for (let i = 0; i < data.length; i++) {
        const char = data.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      return Math.abs(hash).toString(16).padStart(8, '0');
    }
  } catch (error) {
    console.error('计算字符串哈希失败，使用备用方案:', error);
    // 备用方案：简单字符串哈希
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16).padStart(8, '0');
  }
};

/**
 * 获取当前环境信息
 */
export const getEnvironmentInfo = () => {
  return {
    isSecureContext: typeof window !== 'undefined' ? window.isSecureContext : false,
    protocol: typeof window !== 'undefined' ? window.location.protocol : 'unknown',
    hostname: typeof window !== 'undefined' ? window.location.hostname : 'unknown',
    webCryptoSupported: isWebCryptoSupported(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
  };
};

/**
 * 验证环境并提供建议
 */
export const validateEnvironment = () => {
  const env = getEnvironmentInfo();
  
  if (!env.webCryptoSupported) {
    console.warn('Web Crypto API 环境检测:', {
      isSecureContext: env.isSecureContext,
      protocol: env.protocol,
      hostname: env.hostname,
      recommendation: env.protocol === 'http:' && env.hostname !== 'localhost' 
        ? '建议使用 HTTPS 或 localhost 以启用 Web Crypto API'
        : '当前浏览器可能不支持 Web Crypto API'
    });
  }
  
  return env;
};
