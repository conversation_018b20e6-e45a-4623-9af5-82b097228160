/**
 * 波形画布管理服务
 * 负责画布的创建、尺寸管理、虚拟滚动等核心功能
 */

export interface CanvasDimensions {
  width: number;
  height: number;
  logicalWidth: number;
  devicePixelRatio: number;
}

export interface VirtualScrollState {
  offset: number;
  scrollLeft: number;
  maxScrollLeft: number;
  isAtLeftBoundary: boolean;
  isAtRightBoundary: boolean;
}

export interface CanvasConfig {
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  safeOffset: number;
  maxCanvasWidth: number;
  scrollBoundaryTolerance: number;
}

export class WaveformCanvasManager {
  private canvas: HTMLCanvasElement | null = null;
  private context: CanvasRenderingContext2D | null = null;
  private config: CanvasConfig;
  private dimensions: CanvasDimensions = {
    width: 0,
    height: 0,
    logicalWidth: 0,
    devicePixelRatio: 1,
  };
  private virtualScrollState: VirtualScrollState = {
    offset: 0,
    scrollLeft: 0,
    maxScrollLeft: 0,
    isAtLeftBoundary: true,
    isAtRightBoundary: false,
  };

  constructor(config: CanvasConfig) {
    this.config = config;
  }

  /**
   * 初始化画布
   */
  initialize(canvas: HTMLCanvasElement): boolean {
    if (!canvas) return false;

    this.canvas = canvas;
    this.context = canvas.getContext('2d');
    this.dimensions.devicePixelRatio = window.devicePixelRatio || 1;

    return this.context !== null;
  }

  /**
   * 计算目标图形宽度（逻辑宽度）
   */
  calculateTargetGraphWidth(
    availableParentWidth: number,
    totalEffectDuration: number,
    baselineDuration: number,
    audioDuration?: number | null // Store 已经正确处理了音频时长锁定
  ): number {
    // Store 已经正确处理了音频时长锁定，直接使用 totalEffectDuration
    const effectiveDuration = totalEffectDuration;

    let targetGraphWidth: number;

    if (baselineDuration <= 0 || availableParentWidth <= 0) {
      targetGraphWidth = availableParentWidth > 0 ? availableParentWidth : 300;
    } else {
      // 【修复】当没有音频时，始终铺满父元素宽度，避免非固定时长事件显示不完整
      if (audioDuration === null || audioDuration === undefined) {
        // 没有音频时，始终铺满父元素宽度
        targetGraphWidth = availableParentWidth;
      } else {
        // 有音频时，使用原有逻辑
        const defaultTimeStep = availableParentWidth / baselineDuration;
        if (effectiveDuration <= baselineDuration) {
          targetGraphWidth = availableParentWidth;
        } else {
          targetGraphWidth = defaultTimeStep * effectiveDuration;
        }
      }
    }

    const minWidth = this.config.padding.left + this.config.padding.right + 50;
    return Math.max(targetGraphWidth, minWidth);
  }

  /**
   * 计算实际画布宽度（物理宽度，限制内存分配）
   */
  calculateActualCanvasWidth(
    logicalWidth: number,
    availableParentWidth: number
  ): number {
    const availableDrawingWidth = availableParentWidth - this.config.padding.left - this.config.padding.right;

    if (logicalWidth <= availableParentWidth) {
      return logicalWidth;
    }

    const bufferMultiplier = 2.5;
    const maxPhysicalWidth = Math.min(
      availableDrawingWidth * bufferMultiplier + this.config.padding.left + this.config.padding.right,
      this.config.maxCanvasWidth
    );

    return Math.max(maxPhysicalWidth, availableParentWidth);
  }

  /**
   * 调整画布尺寸
   */
  resizeCanvas(
    targetLogicalWidth: number,
    availableParentWidth: number,
    containerHeight: number
  ): boolean {
    if (!this.canvas || !this.context) return false;

    // 更新逻辑宽度
    this.dimensions.logicalWidth = targetLogicalWidth;

    // 计算实际画布宽度
    const actualCanvasWidth = this.calculateActualCanvasWidth(targetLogicalWidth, availableParentWidth);
    this.dimensions.width = actualCanvasWidth;
    this.dimensions.height = containerHeight;

    // 设置画布尺寸
    const dpr = this.dimensions.devicePixelRatio;
    this.canvas.width = (actualCanvasWidth - this.config.padding.left) * dpr;
    this.canvas.height = containerHeight * dpr;
    this.canvas.style.width = `${actualCanvasWidth - this.config.padding.left}px`;
    this.canvas.style.height = `${containerHeight}px`;

    // 应用设备像素比缩放
    this.context.scale(dpr, dpr);

    return true;
  }

  /**
   * 计算虚拟滚动偏移量
   */
  calculateVirtualScrollOffset(
    scrollLeft: number,
    availableWidth: number
  ): number {
    const logicalScrollableWidth = this.dimensions.logicalWidth - this.config.padding.left;
    const availableScrollableWidth = availableWidth - this.config.padding.left - 20;
    const maxScrollLeft = logicalScrollableWidth - availableScrollableWidth;

    if (maxScrollLeft <= 0) return 0;

    const tolerance = this.config.scrollBoundaryTolerance;

    if (scrollLeft <= tolerance) return 0;
    if (scrollLeft >= maxScrollLeft - tolerance) return maxScrollLeft;

    const scrollRatio = Math.max(0, Math.min(scrollLeft / maxScrollLeft, 1));
    const virtualOffset = scrollRatio * maxScrollLeft;

    if (scrollLeft >= maxScrollLeft - tolerance * 2 || scrollLeft <= tolerance * 2) {
      return virtualOffset;
    }

    return Math.round(virtualOffset * 10) / 10;
  }

  /**
   * 更新虚拟滚动状态
   */
  updateVirtualScrollState(scrollLeft: number, availableWidth: number): VirtualScrollState {
    const newOffset = this.calculateVirtualScrollOffset(scrollLeft, availableWidth);

    const logicalScrollableWidth = this.dimensions.logicalWidth - this.config.padding.left;
    const physicalScrollableWidth = availableWidth - this.config.padding.left - this.config.padding.right;
    const maxScrollLeft = Math.max(0, logicalScrollableWidth - physicalScrollableWidth);

    this.virtualScrollState = {
      offset: newOffset,
      scrollLeft,
      maxScrollLeft,
      isAtLeftBoundary: scrollLeft <= this.config.scrollBoundaryTolerance,
      isAtRightBoundary: scrollLeft >= maxScrollLeft - this.config.scrollBoundaryTolerance,
    };

    return this.virtualScrollState;
  }

  /**
   * 更新画布位置以跟随虚拟滚动
   */
  updateCanvasPosition(availableWidth: number): void {
    if (!this.canvas) return;

    const logicalScrollableWidth = this.dimensions.logicalWidth - this.config.padding.left;
    const physicalScrollableWidth = availableWidth - this.config.padding.left - this.config.padding.right;
    const maxScrollLeft = Math.max(0, logicalScrollableWidth - physicalScrollableWidth);

    if (maxScrollLeft <= 0) {
      this.canvas.style.left = '0px';
      return;
    }

    const tolerance = this.config.scrollBoundaryTolerance;
    const scrollLeft = this.virtualScrollState.scrollLeft;

    if (scrollLeft <= tolerance) {
      this.canvas.style.left = '0px';
      return;
    }

    if (scrollLeft >= maxScrollLeft - tolerance) {
      const maxCanvasOffset = logicalScrollableWidth - physicalScrollableWidth;
      this.canvas.style.left = `${maxCanvasOffset}px`;
      return;
    }

    const scrollRatio = Math.max(0, Math.min(scrollLeft / maxScrollLeft, 1));
    const maxCanvasOffset = logicalScrollableWidth - physicalScrollableWidth;
    const canvasLeftOffset = scrollRatio * maxCanvasOffset;

    this.canvas.style.left = `${canvasLeftOffset}px`;
  }

  /**
   * 获取画布尺寸信息
   */
  getDimensions(): CanvasDimensions {
    return { ...this.dimensions };
  }

  /**
   * 获取虚拟滚动状态
   */
  getVirtualScrollState(): VirtualScrollState {
    return { ...this.virtualScrollState };
  }

  /**
   * 获取画布上下文
   */
  getContext(): CanvasRenderingContext2D | null {
    return this.context;
  }

  /**
   * 获取画布元素
   */
  getCanvas(): HTMLCanvasElement | null {
    return this.canvas;
  }

  /**
   * 清除画布
   */
  clearCanvas(): void {
    if (!this.context) return;
    this.context.clearRect(0, 0, this.dimensions.width, this.dimensions.height);
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.canvas = null;
    this.context = null;
    this.dimensions = {
      width: 0,
      height: 0,
      logicalWidth: 0,
      devicePixelRatio: 1,
    };
    this.virtualScrollState = {
      offset: 0,
      scrollLeft: 0,
      maxScrollLeft: 0,
      isAtLeftBoundary: true,
      isAtRightBoundary: false,
    };
  }
}