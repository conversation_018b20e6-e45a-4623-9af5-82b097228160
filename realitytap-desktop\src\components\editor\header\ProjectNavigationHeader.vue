<template>
  <div class="project-header">
    <div class="left-section">
      <n-button class="back-button-modern" @click="goBack" text size="small">
        <template #icon>
          <n-icon>
            <ArrowLeftRegular />
          </n-icon>
        </template>
        <span class="back-button-text">{{ t('editor.navigation.projects') }}</span>
      </n-button>
    </div>
    <div class="center-section">
      <n-breadcrumb class="custom-breadcrumb">
        <n-breadcrumb-item
          @dblclick="toggleEdit"
          :title="isEditing ? '' : t('editor.navigation.doubleClickToEdit')"
        >
          <div class="breadcrumb-item-content project-item">
            <span v-if="!isEditing" class="project-name-display">
              {{ projectName }}
            </span>
            <input
              v-else
              ref="titleInputRef"
              v-model="editingTitle"
              class="title-input"
              type="text"
              @blur="saveTitle"
              @keyup.enter="saveTitle"
              @keyup.esc="cancelEdit"
            />
          </div>
        </n-breadcrumb-item>
        <template v-for="group in groupChain" :key="group.groupUuid">
          <n-breadcrumb-item>
            <div class="breadcrumb-item-content group-item">
              <span class="group-name-display">{{ group.name }}</span>
            </div>
          </n-breadcrumb-item>
        </template>
        <n-breadcrumb-item v-if="fileName">
          <div class="breadcrumb-item-content file-item">
            <span class="file-name-display">
              {{ fileName }}
              <span v-if="isCurrentFileUnsaved" class="unsaved-indicator" :title="t('editor.navigation.unsavedChanges')"> •</span>
            </span>
          </div>
        </n-breadcrumb-item>
      </n-breadcrumb>
    </div>
    <div class="right-section"></div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed } from "vue";
import { useRouter } from "vue-router";
import { NBreadcrumb, NBreadcrumbItem, NButton, NIcon } from "naive-ui";
import { ArrowLeft20Regular as ArrowLeftRegular } from "@vicons/fluent";
import type { HapticsGroup } from "@/types/haptic-project";
import { useProjectStore } from "@/stores/haptics-project-store";
import { useI18n } from "@/composables/useI18n";

const props = defineProps<{
  projectName: string;
  groupChain: HapticsGroup[];
  fileName: string | null;
}>();

const emit = defineEmits<{
  (e: "update:projectName", name: string): void;
}>();

const router = useRouter();
const projectStore = useProjectStore();
const { t } = useI18n();
const isEditing = ref(false);
const editingTitle = ref(props.projectName);
const titleInputRef = ref<HTMLInputElement | null>(null);
const isSavingTitle = ref(false);

// 计算属性：当前文件是否未保存
const isCurrentFileUnsaved = computed(() => {
  if (!projectStore.selectedFileUuid) return false;
  return projectStore.isFileUnsaved(projectStore.selectedFileUuid);
});

const goBack = () => {
  router.push("/");
};

const toggleEdit = () => {
  isEditing.value = true;
  editingTitle.value = props.projectName;
  setTimeout(() => {
    titleInputRef.value?.focus();
  });
};

const saveTitle = () => {
  if (isSavingTitle.value) return;
  isSavingTitle.value = true;

  if (editingTitle.value.trim()) {
    emit("update:projectName", editingTitle.value.trim());
    titleInputRef.value?.blur();
  } else {
    editingTitle.value = props.projectName;
  }
  isEditing.value = false;
  isSavingTitle.value = false;
};

const cancelEdit = () => {
  editingTitle.value = props.projectName;
  isEditing.value = false;
};
</script>

<style scoped>
.project-header {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  width: 100%;
  min-height: 56px;
  max-height: 56px;
  height: 56px;
}

.left-section {
  justify-self: start;
}

.center-section {
  justify-self: center;
  text-align: center;
}

.right-section {
  justify-self: end;
}

.back-button-modern {
  display: flex;
  align-items: center;
  color: #e6e6e6;
  transition: color 0.1s ease, background-color 0.3s ease;
  font-weight: 500;
  border-radius: 4px;
  padding: 0.25rem 0.4rem;
  margin-left: 0.2rem;
  padding: 0.5rem 1rem;
  border-radius: 1em;
}

.back-button-modern:hover {
  color: #fff;
  background-color: rgba(100, 155, 255, 0.15);
}

.back-button-text {
  margin-left: 0.3rem;
  font-size: 0.9rem;
  letter-spacing: 0.3px;
}

.custom-breadcrumb {
  padding: 0.3rem 0.5rem;
  border-radius: 6px;
}

:deep(.n-breadcrumb-item:not(:last-child) .n-breadcrumb-item__separator) {
  margin: 0 0.5rem;
  color: rgba(255, 255, 255, 0.3);
}

.breadcrumb-item-content {
  display: flex;
  align-items: center;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.project-item {
  cursor: pointer;
}

.project-item:hover {
  background-color: rgba(64, 158, 255, 0.25);
}

.project-name-display {
  font-size: 1.1rem;
  font-weight: 400;
  color: white;
  display: inline-block;
}

.group-item:hover {
  background-color: rgba(255, 184, 0, 0.25);
}

.group-name-display {
  font-size: 1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.file-item:hover {
  background-color: rgba(103, 194, 58, 0.25);
}



.file-name-display {
  font-size: 0.95rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  display: inline-block;
}

.title-input {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  line-height: 1.2;
  display: inline-block;
  width: auto;
  min-width: 2em;
  border: none;
  background: none;
  outline: none;
  border-bottom: 1px solid rgba(255,255,255,0.4);
  transition: border-bottom-color 0.2s, border-bottom-width 0.2s;
  text-align: center;
  margin: 0;
  padding: 0;
  border-radius: 0;
}

.title-input:focus {
  border-bottom-color: #4785eb;
  border-bottom-width: 2px;
}

/* 未保存状态指示器样式 */
.unsaved-indicator {
  color: #ff4757;
  font-weight: bold;
  font-size: 20px;
  text-shadow: 0 0 6px rgba(255, 71, 87, 0.8);
  animation: pulse-glow-header 1.5s infinite;
  position: absolute;
  display: inline-block;
  right: -24px;
  top: 50%;
  transform: translateY(-50%);
}

.unsaved-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background: radial-gradient(circle, rgba(255, 71, 87, 0.3) 0%, rgba(255, 107, 107, 0.2) 70%, transparent 100%);
  border-radius: 50%;
  animation: pulse-ring-header 1.5s infinite;
  z-index: -1;
}

@keyframes pulse-glow-header {
  0%, 100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
    text-shadow: 0 0 6px rgba(255, 71, 87, 0.8);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1.15);
    text-shadow: 0 0 12px rgba(255, 71, 87, 1);
  }
}

@keyframes pulse-ring-header {
  0% {
    transform: translate(-50%, -50%) scale(0.6);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.4);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}
</style>
