// 颜色相关的纯函数与常量

import { MAX_FREQUENCY, MIN_FREQUENCY } from "./coordinate";

export const AXIS_COLOR = "#888";                                              // 轴线颜色
export const GRID_COLOR = "rgba(133, 133, 133, 0.1)";                        // 网格颜色
export const AXIS_LABEL_COLOR = "#aaa";                                        // 轴标签颜色
export const TRANSIENT_STROKE_COLOR = "rgba(255, 140, 0, 1)";                // 瞬态线颜色
export const TRANSIENT_FILL_COLOR = "rgba(255, 140, 0, 0.5)";                // 瞬态填充颜色
export const CONTINUOUS_STROKE_COLOR = "rgba(48, 158, 155, 1)";              // 连续线颜色
export const CONTINUOUS_SELECTED_STROKE_COLOR = "rgba(255, 255, 255, 1)";    // 连续选中线颜色
export const TRANSIENT_SELECTED_STROKE_COLOR = "rgba(255, 255, 255, 1)";     // 瞬态选中线颜色
export const TRANSIENT_SELECTED_FILL_COLOR = "rgba(255, 140, 0, 0.5)";        // 瞬态选中填充颜色
export const FONT_STYLE = "10px sans-serif";                                    // 字体样式

// 频率映射到颜色
// 频率值组成：全局频率(0-100) + Curve点相对频率(-100到100)
// 总范围：-100 到 200，需要保持线性颜色变化
export function mapFrequencyToColor(frequency: number, _isSelected: boolean): string {
  // 安全检查：如果频率是NaN或无效值，使用默认颜色
  if (!isFinite(frequency) || isNaN(frequency)) {
    console.warn('mapFrequencyToColor: 接收到无效的频率值', frequency);
    return "rgba(255, 255, 255, 0.5)"; // 返回默认的半透明色
  }

  let r, g, b, a;
  const baseAlpha = 0.6;
  const lowRgb = [0, 100, 255];
  const midRgb = [100, 255, 100];
  const highRgb = [255, 100, 0];
  if (frequency <= MIN_FREQUENCY) {
    [r, g, b] = lowRgb;
  } else if (frequency >= MAX_FREQUENCY) {
    [r, g, b] = highRgb;
  } else if (frequency < 0) {
    const t = (frequency - MIN_FREQUENCY) / (0 - MIN_FREQUENCY);
    r = lowRgb[0] + t * (midRgb[0] - lowRgb[0]);
    g = lowRgb[1] + t * (midRgb[1] - lowRgb[1]);
    b = lowRgb[2] + t * (midRgb[2] - lowRgb[2]);
  } else {
    const t = (frequency - MIN_FREQUENCY) / (MAX_FREQUENCY - MIN_FREQUENCY);
    r = midRgb[0] + t * (highRgb[0] - midRgb[0]);
    g = midRgb[1] + t * (highRgb[1] - midRgb[1]);
    b = midRgb[2] + t * (highRgb[2] - midRgb[2]);
  }
  a = baseAlpha;
  return `rgba(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)}, ${a})`;
}