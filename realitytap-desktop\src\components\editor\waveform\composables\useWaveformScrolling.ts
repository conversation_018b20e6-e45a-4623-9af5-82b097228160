import type { ScrollbarInst } from "naive-ui";
import { type Ref } from "vue";
import { SCROLL_BOUNDARY_TOLERANCE } from "../config/waveform-constants";
import { throttle } from "../utils/coordinate";

// 滚动配置接口
export interface ScrollConfig {
  horizontalScrollbarRef: Ref<ScrollbarInst | null>;
  availableParentWidth: number;
  padding: { top: number; right: number; bottom: number; left: number };
  logicalCanvasWidth: Ref<number>;
  scrollLeftValue: Ref<number>;
  updateVirtualScrollOffset: () => boolean;
  updateCanvasPosition: (canvas: HTMLCanvasElement | null) => void;
  checkAndFixScrollBoundary: (scrollbar: ScrollbarInst | null) => boolean;
}

export function useWaveformScrolling(
  config: ScrollConfig,
  resetDrawState: () => void,
  drawWaveform: (forceRedraw?: boolean) => void
) {
  // 滚动更新处理函数
  const handleScrollUpdate = throttle(() => {
    // 更新虚拟滚动偏移量
    const offsetChanged = config.updateVirtualScrollOffset();

    if (offsetChanged) {
      drawWaveform(true);
    }

    // 更新Canvas位置以跟随虚拟滚动
    // 这里需要从外部传入canvas引用
  }, 16);

  // 处理水平滚动事件
  const onHorizontalScroll = (event: Event, isDragging: boolean, canvas: HTMLCanvasElement | null) => {
    const target = event.target as HTMLElement;
    if (target) {
      config.scrollLeftValue.value = target.scrollLeft;
    }

    // 检查并修正滚动边界
    const wasCorrected = config.checkAndFixScrollBoundary(config.horizontalScrollbarRef.value);

    if (wasCorrected) {
      return;
    }

    // 更新虚拟滚动偏移量
    const offsetChanged = config.updateVirtualScrollOffset();

    if (offsetChanged) {
      config.updateCanvasPosition(canvas);
    }

    // 立即处理滚动更新，不使用节流，确保边界位置的精确同步
    if (!isDragging) {
      // 计算是否在边界位置
      const logicalScrollableWidth = config.logicalCanvasWidth.value - config.padding.left;
      const physicalScrollableWidth = config.availableParentWidth - config.padding.left - config.padding.right;
      const maxScrollLeft = Math.max(0, logicalScrollableWidth - physicalScrollableWidth);
      const isAtBoundary =
        config.scrollLeftValue.value <= SCROLL_BOUNDARY_TOLERANCE ||
        config.scrollLeftValue.value >= maxScrollLeft - SCROLL_BOUNDARY_TOLERANCE;

      if (isAtBoundary) {
        // 边界位置立即重绘
        resetDrawState();
        drawWaveform(true);
      } else {
        // 非边界位置使用节流
        handleScrollUpdate();
      }
    }
  };

  // 处理滚轮事件
  const handleGraphWheelScroll = (
    event: WheelEvent,
    isAddMenuVisible: boolean,
    closeAddMenu: () => void,
    hideRightClickGuide: (shouldRedraw?: boolean) => void
  ) => {
    // 清除右键辅助线
    hideRightClickGuide(false);

    // 如果菜单可见，先隐藏菜单
    if (isAddMenuVisible) {
      closeAddMenu();
    }

    if (config.horizontalScrollbarRef.value && event.deltaY !== 0) {
      event.preventDefault();

      // 计算滚动量
      let baseAmount = Math.sign(event.deltaY) * Math.min(Math.abs(event.deltaY), 100) * 0.5;

      let scrollFactor = 1;
      if (event.deltaMode === WheelEvent.DOM_DELTA_LINE) {
        scrollFactor = 20;
      } else if (event.deltaMode === WheelEvent.DOM_DELTA_PAGE) {
        scrollFactor = 100;
      }

      const actualScrollAmount = baseAmount * scrollFactor;
      const currentScrollLeft = config.scrollLeftValue.value;

      // 计算滚动边界
      const logicalScrollableWidth = config.logicalCanvasWidth.value - config.padding.left;
      const physicalScrollableWidth = config.availableParentWidth - config.padding.left - config.padding.right;
      const maxScrollLeft = Math.max(0, logicalScrollableWidth - physicalScrollableWidth);

      // 检查滚动边界
      if (actualScrollAmount < 0) {
        // 向左滚动：检查左边界
        if (currentScrollLeft <= Math.abs(actualScrollAmount) + SCROLL_BOUNDARY_TOLERANCE) {
          config.horizontalScrollbarRef.value.scrollTo({ left: 0, behavior: "auto" });
          return;
        }
      } else {
        // 向右滚动：检查右边界
        if (currentScrollLeft + actualScrollAmount >= maxScrollLeft - SCROLL_BOUNDARY_TOLERANCE) {
          config.horizontalScrollbarRef.value.scrollTo({
            left: maxScrollLeft,
            behavior: "auto",
          });
          return;
        }
      }

      // 在边界范围内，正常滚动
      config.horizontalScrollbarRef.value.scrollBy({
        left: actualScrollAmount,
        top: 0,
        behavior: "auto",
      });
    }
  };

  return {
    handleScrollUpdate,
    onHorizontalScroll,
    handleGraphWheelScroll,
  };
}
