import { ref } from "vue";
import { useMessage } from "naive-ui";
import { useProjectStore } from "@/stores/haptics-project-store";
import { useI18n } from "@/composables/useI18n";
import { parseErrorMessage } from "@/utils/commonUtils";

/**
 * 文件操作 Composable
 * 提供创建触觉文件和导入音频文件的共享功能
 */
export function useFileOperations() {
  const projectStore = useProjectStore();
  const message = useMessage();
  const { t } = useI18n();

  // 导入进度状态
  const isImportingAudio = ref(false);
  const importSpinMessage = ref("");

  /**
   * 在根目录创建新的触觉文件
   */
  const createNewHapticFileInRoot = async () => {
    try {
      await projectStore.createNewHeFile(null);
      message.success(t('editor.file.createSuccess'));
    } catch (error: any) {
      console.error("创建触觉文件失败:", error);
      message.error(t('editor.file.createFailed', { error: parseErrorMessage(error) }));
    }
  };

  /**
   * 导入音频文件到根目录
   */
  const importAudioFileToRoot = async () => {
    try {
      const { open } = await import("@tauri-apps/plugin-dialog");
      const selected = await open({
        multiple: false,
        filters: [{ name: "Audio Files", extensions: ["wav", "mp3", "ogg", "flac"] }],
      });

      if (!selected || typeof selected !== "string") {
        return;
      }

      isImportingAudio.value = true;
      importSpinMessage.value = t('editor.audio.importingToRoot');

      const fileUuid = await projectStore.addAudioFileToProject(
        selected,
        null, // groupUuid 传 null 表示根目录
        "", // path 传空字符串表示根目录
        message
      );

      if (fileUuid) {
        // addAudioFileToProject 内部已经处理了音频信息获取和文件选中
        message.success(t('editor.audio.importSuccess'));
      }
    } catch (error: any) {
      console.error("音频导入失败", error);
      message.error(t('editor.audio.importFailed', { error: parseErrorMessage(error) }));
    } finally {
      isImportingAudio.value = false;
      importSpinMessage.value = "";
    }
  };

  /**
   * 导入视频文件到根目录
   */
  const importVideoFileToRoot = async () => {
    try {
      const { open } = await import("@tauri-apps/plugin-dialog");
      const selected = await open({
        multiple: false,
        filters: [{ name: "Video Files", extensions: ["mp4"] }],
      });

      if (!selected || typeof selected !== "string") {
        return;
      }

      isImportingAudio.value = true;
      importSpinMessage.value = t('editor.video.importingToRoot');

      const fileUuid = await projectStore.addVideoFileToProject(
        selected,
        null, // groupUuid 传 null 表示根目录
        "", // path 传空字符串表示根目录
        message
      );

      if (fileUuid) {
        // addVideoFileToProject 内部已经处理了音频信息获取和文件选中
        message.success(t('editor.audio.importSuccess'));
      }
    } catch (error: any) {
      console.error("视频导入失败", error);
      message.error(t('editor.audio.importFailed', { error: parseErrorMessage(error) }));
    } finally {
      isImportingAudio.value = false;
      importSpinMessage.value = "";
    }
  };

  return {
    // 状态
    isImportingAudio,
    importSpinMessage,

    // 方法
    createNewHapticFileInRoot,
    importAudioFileToRoot,
    importVideoFileToRoot,
  };
}
