# FFmpeg Download Script
# This script downloads FFmpeg binaries for different platforms

param(
    [string]$Platform = "all",
    [switch]$Force = $false
)

$ErrorActionPreference = "Stop"

# FFmpeg download URL
$BASE_URL = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest"

# Target directory
$FFMPEG_DIR = Join-Path $PSScriptRoot "..\src-tauri\ffmpeg"

# Platform configuration
$PLATFORMS = @{
    "linux-x64" = @{
        "url" = "$BASE_URL/ffmpeg-master-latest-linux64-gpl.tar.xz"
        "dir" = "linux/x64"
        "binaries" = @("ffmpeg", "ffprobe")
    }
    "linux-arm64" = @{
        "url" = "$BASE_URL/ffmpeg-master-latest-linuxarm64-gpl.tar.xz"
        "dir" = "linux/arm64"
        "binaries" = @("ffmpeg", "ffprobe")
    }
    "macos" = @{
        "url" = "$BASE_URL/ffmpeg-master-latest-macos64-gpl.tar.xz"
        "dir" = "macos"
        "binaries" = @("ffmpeg", "ffprobe")
    }
}

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Invoke-DownloadAndExtract {
    param(
        [string]$Url,
        [string]$TargetDir,
        [array]$Binaries
    )
    
    $tempDir = Join-Path $env:TEMP "ffmpeg-download-$(Get-Random)"
    $archiveFile = Join-Path $tempDir "ffmpeg.tar.xz"
    
    try {
        # Create temporary directory
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
        
        Write-Info "Downloading FFmpeg from $Url"
        Invoke-WebRequest -Uri $Url -OutFile $archiveFile -UseBasicParsing
        
        # Check for 7zip or tar command
        if (Test-Command "7z") {
            Write-Info "Extracting using 7zip"
            & 7z x $archiveFile -o"$tempDir" -y | Out-Null
            $extractedDir = Get-ChildItem -Path $tempDir -Directory | Where-Object { $_.Name -like "*ffmpeg*" } | Select-Object -First 1
        }
        elseif (Test-Command "tar") {
            Write-Info "Extracting using tar"
            & tar -xf $archiveFile -C $tempDir
            $extractedDir = Get-ChildItem -Path $tempDir -Directory | Where-Object { $_.Name -like "*ffmpeg*" } | Select-Object -First 1
        }
        else {
            throw "Need 7zip or tar command to extract files. Please install one of them."
        }
        
        if (-not $extractedDir) {
            throw "Cannot find extracted FFmpeg directory"
        }
        
        $binDir = Join-Path $extractedDir.FullName "bin"
        if (-not (Test-Path $binDir)) {
            throw "Cannot find FFmpeg bin directory"
        }
        
        # Copy binary files
        foreach ($binary in $Binaries) {
            $sourcePath = Join-Path $binDir $binary
            $targetPath = Join-Path $TargetDir $binary
            
            if (Test-Path $sourcePath) {
                Write-Info "Copying $binary to $targetPath"
                Copy-Item $sourcePath $targetPath -Force
            }
            else {
                Write-Warning "Binary $binary not found at $sourcePath"
            }
        }
        
        Write-Info "Successfully downloaded and extracted FFmpeg binaries to $TargetDir"
    }
    finally {
        # Clean up temporary files
        if (Test-Path $tempDir) {
            Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
}

function Main {
    Write-Info "Starting FFmpeg binary download"
    
    # Check if target directory exists
    if (-not (Test-Path $FFMPEG_DIR)) {
        New-Item -ItemType Directory -Path $FFMPEG_DIR -Force | Out-Null
    }
    
    # Determine platforms to download
    $platformsToDownload = @()
    if ($Platform -eq "all") {
        $platformsToDownload = $PLATFORMS.Keys
    }
    elseif ($PLATFORMS.ContainsKey($Platform)) {
        $platformsToDownload = @($Platform)
    }
    else {
        Write-Error "Unsupported platform: $Platform. Supported platforms: $($PLATFORMS.Keys -join ', '), all"
        exit 1
    }
    
    foreach ($platformKey in $platformsToDownload) {
        $config = $PLATFORMS[$platformKey]
        $targetDir = Join-Path $FFMPEG_DIR $config.dir
        
        # Check if already exists
        $allBinariesExist = $true
        foreach ($binary in $config.binaries) {
            $binaryPath = Join-Path $targetDir $binary
            if (-not (Test-Path $binaryPath)) {
                $allBinariesExist = $false
                break
            }
        }
        
        if ($allBinariesExist -and -not $Force) {
            Write-Info "FFmpeg binaries for platform $platformKey already exist, skipping download"
            continue
        }
        
        try {
            Write-Info "Downloading FFmpeg for platform $platformKey"
            Invoke-DownloadAndExtract -Url $config.url -TargetDir $targetDir -Binaries $config.binaries
        }
        catch {
            Write-Error "Failed to download platform $platformKey : $($_.Exception.Message)"
            exit 1
        }
    }
    
    Write-Info "All FFmpeg binaries downloaded successfully"
}

# Run main function
Main
