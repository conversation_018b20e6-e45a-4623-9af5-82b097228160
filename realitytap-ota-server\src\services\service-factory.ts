import { config } from '@/config/server.config';
import { logger } from '@/utils/logger.util';

// JSON 文件服务
import { versionService as jsonVersionService } from './version.service';
import { configService as jsonConfigService } from './config.service';
import { statsService as jsonStatsService } from './stats.service';

// 数据库服务
import { versionDatabaseService } from './version-db.service';
import { configDatabaseService } from './config-db.service';
import { statsDatabaseService } from './stats-db.service';

// 服务接口定义
export interface IVersionService {
  checkForUpdates(request: any): Promise<any>;
  getAvailableVersions(): Promise<any>;
  getChannelInfo(): Promise<any>;
  updateVersionsConfig(config: any): Promise<void>;
  updateChannelsConfig(config: any): Promise<void>;
  clearCache(): void;
}

export interface IConfigService {
  // 基础配置操作
  getConfig(): Promise<any>;
  updateConfig(config: any, userId?: string, reason?: string): Promise<any>;
  resetConfig(userId?: string, reason?: string): Promise<any>;

  // 配置验证和模式
  validateConfig(config: any): Promise<{ isValid: boolean; errors: string[] }>;
  getConfigSchema(): any;
  getDefaultConfig(): any;

  // 配置历史
  getConfigHistory(limit?: number): Promise<any>;

  // 单个配置值操作
  getConfigValue<T>(key: string, defaultValue?: T): Promise<T | undefined>;
  setConfigValue(key: string, value: any, userId?: string, reason?: string): Promise<void>;

  // 缓存管理
  clearCache(): void;
}

export interface IStatsService {
  recordDownload(downloadInfo: any): Promise<void>;
  getDownloadStats(query?: any): Promise<any>;
  getDownloadRecords(query?: any): Promise<any>;
  getSummaryStats(): Promise<any>;
  cleanupOldStats(daysToKeep: number): Promise<void>;
  clearCache(): void;
}

/**
 * 服务工厂类
 * 根据配置选择使用 JSON 文件服务还是数据库服务
 */
export class ServiceFactory {
  private static instance: ServiceFactory;
  private _versionService: IVersionService | null = null;
  private _configService: IConfigService | null = null;
  private _statsService: IStatsService | null = null;

  private constructor() {}

  public static getInstance(): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory();
    }
    return ServiceFactory.instance;
  }

  /**
   * 获取版本服务
   */
  public getVersionService(): IVersionService {
    if (!this._versionService) {
      // 始终使用数据库版本服务
      logger.info('Using database version service (SQLite only)');
      this._versionService = versionDatabaseService;
    }
    return this._versionService;
  }

  /**
   * 获取配置服务
   */
  public getConfigService(): IConfigService {
    if (!this._configService) {
      // 始终使用数据库配置服务
      logger.info('Using database config service (SQLite only)');
      this._configService = configDatabaseService;
    }
    return this._configService;
  }

  /**
   * 获取统计服务
   */
  public getStatsService(): IStatsService {
    if (!this._statsService) {
      // 始终使用数据库统计服务
      logger.info('Using database stats service (SQLite only)');
      this._statsService = statsDatabaseService;
    }
    return this._statsService;
  }

  /**
   * 切换到数据库服务
   */
  public switchToDatabase(): void {
    logger.info('Switching to database services');
    this._versionService = versionDatabaseService;
    this._configService = configDatabaseService;
    this._statsService = statsDatabaseService;
  }

  /**
   * 切换到 JSON 文件服务
   */
  public switchToJsonFiles(): void {
    logger.info('Switching to JSON file services');
    this._versionService = jsonVersionService;
    this._configService = jsonConfigService;
    this._statsService = jsonStatsService;
  }

  /**
   * 重置服务实例（强制重新选择）
   */
  public reset(): void {
    this._versionService = null;
    this._configService = null;
    this._statsService = null;
    logger.info('Service factory reset');
  }

  /**
   * 获取当前使用的服务类型
   */
  public getCurrentServiceType(): 'database' | 'json' {
    // 始终返回数据库类型
    return 'database';
  }

  /**
   * 检查数据库是否可用
   */
  public async isDatabaseAvailable(): Promise<boolean> {
    try {
      // 始终检查数据库可用性
      const { dbConnection } = await import('@/database/connection');
      return await dbConnection.healthCheck();
    } catch (error) {
      logger.error('Database availability check failed', { error });
      return false;
    }
  }

  /**
   * 初始化服务
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing SQLite database services');

      // 初始化数据库连接（包含所有表和数据的创建）
      const { dbConnection } = await import('@/database/connection');
      await dbConnection.initialize();

      logger.info('Database services initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize services', { error });
      throw new Error(`Service initialization failed: ${error}`);
    }
  }
}

// 导出单例实例
export const serviceFactory = ServiceFactory.getInstance();

// 导出便捷访问方法
export const getVersionService = (): IVersionService => serviceFactory.getVersionService();
export const getConfigService = (): IConfigService => serviceFactory.getConfigService();
export const getStatsService = (): IStatsService => serviceFactory.getStatsService();

// 兼容性导出（保持现有代码可以正常工作）
export const versionService = serviceFactory.getVersionService();
export const configService = serviceFactory.getConfigService();
export const statsService = serviceFactory.getStatsService();
