# RealityTap Desktop 签名配置示例
# 复制此文件为 .env.signing 并填入实际的签名密钥信息

# Tauri 签名私钥（必需）
# 可以是文件路径或密钥内容
# 文件路径示例：
# TAURI_SIGNING_PRIVATE_KEY=C:\keys\realitytap-updater.key
# 密钥内容示例：
# TAURI_SIGNING_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----
# MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
# -----END PRIVATE KEY-----
TAURI_SIGNING_PRIVATE_KEY=

# Tauri 签名私钥密码（可选，如果私钥有密码保护）
TAURI_SIGNING_PRIVATE_KEY_PASSWORD=

# 使用方法：
# 1. 复制此文件为 .env.signing
# 2. 填入实际的签名密钥信息
# 3. 在 PowerShell 中执行：
#    Get-Content .env.signing | ForEach-Object { if ($_ -match '^([^=]+)=(.*)$') { [Environment]::SetEnvironmentVariable($matches[1], $matches[2], 'Process') } }
# 4. 或者直接设置环境变量：
#    $env:TAURI_SIGNING_PRIVATE_KEY="your-private-key"
#    $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD="your-password"
