/**
 * 波形图网格和轴绘制管理 Composable
 * 负责处理网格线、X轴、Y轴的绘制逻辑
 */


import { WAVEFORM_SAFE_OFFSET, drawGridLinesAndXAxis } from "../utils/drawing-helpers";
import type { ComputedRef } from "vue";

export interface WaveformGridConfig {
  // 画布状态
  getGraphAreaWidth: () => number;
  getGraphAreaHeight: () => number;
  getEffectiveDuration: () => number;
  getLogicalGraphAreaWidth?: () => number; // 新增：获取逻辑画布宽度

  // 坐标转换函数
  mapIntensityToYLocal: (intensity: number) => number;
  mapTimeToXLocal: (time: number) => number;

  // 缩放状态
  currentZoomLevel: { value: number }; // 响应式缩放级别

  // 布局配置
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };

  // 新增：父元素可视宽度
  availableParentWidth?: number;
}

export function useWaveformGrid(config: WaveformGridConfig | ComputedRef<WaveformGridConfig>) {
  // 【修复】支持响应式配置，获取当前配置值
  const getConfig = () => 'value' in config ? config.value : config;

  /**
   * 绘制完整的网格线和X轴
   * 用于正常的重绘操作
   */
  const drawFullGrid = (ctx: CanvasRenderingContext2D) => {
    const currentConfig = getConfig();

    // 计算逻辑Canvas宽度（包含padding）
    const logicalCanvasWidth = currentConfig.getLogicalGraphAreaWidth ?
      currentConfig.getLogicalGraphAreaWidth() + currentConfig.padding.left + currentConfig.padding.right :
      undefined;



    drawGridLinesAndXAxis(
      ctx,
      currentConfig.getGraphAreaWidth(),
      currentConfig.getGraphAreaHeight(),
      currentConfig.mapIntensityToYLocal,
      currentConfig.padding,
      WAVEFORM_SAFE_OFFSET,
      currentConfig.getEffectiveDuration(),
      currentConfig.mapTimeToXLocal,
      currentConfig.currentZoomLevel.value, // 传递当前缩放级别
      currentConfig.availableParentWidth, // 传递父元素可视宽度
      logicalCanvasWidth // 传递逻辑Canvas宽度
    );
  };

  /**
   * 绘制简化的网格线
   * 用于性能优化场景，如属性调整时的快速重绘
   * 【修复】确保X轴刻度始终被绘制
   */
  const drawSimplifiedGrid = (ctx: CanvasRenderingContext2D) => {
    const currentConfig = getConfig();

    // 计算逻辑Canvas宽度（包含padding）
    const logicalCanvasWidth = currentConfig.getLogicalGraphAreaWidth ?
      currentConfig.getLogicalGraphAreaWidth() + currentConfig.padding.left + currentConfig.padding.right :
      undefined;

    // 【修复】即使在简化模式下也要绘制完整的网格和X轴刻度
    // 因为X轴刻度对于时间显示至关重要
    drawGridLinesAndXAxis(
      ctx,
      currentConfig.getGraphAreaWidth(),
      currentConfig.getGraphAreaHeight(),
      currentConfig.mapIntensityToYLocal,
      currentConfig.padding,
      WAVEFORM_SAFE_OFFSET,
      currentConfig.getEffectiveDuration(),
      currentConfig.mapTimeToXLocal,
      currentConfig.currentZoomLevel.value, // 传递当前缩放级别
      currentConfig.availableParentWidth, // 传递父元素可视宽度
      logicalCanvasWidth // 传递逻辑Canvas宽度
    );
  };

  /**
   * 智能网格绘制
   * 根据是否强制重绘来选择绘制方式
   */
  const drawGrid = (ctx: CanvasRenderingContext2D, forceRedraw: boolean = false) => {
    if (forceRedraw) {
      drawFullGrid(ctx);
    } else {
      drawSimplifiedGrid(ctx);
    }
  };

  return {
    drawFullGrid,
    drawSimplifiedGrid,
    drawGrid,
  };
}
