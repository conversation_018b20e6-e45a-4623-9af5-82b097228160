<template>
  <!-- 加载状态 -->
  <div v-if="initializing" class="loading-container">
    <n-spin size="large">
      <template #description> 正在初始化管理界面... </template>
    </n-spin>
  </div>

  <!-- 主界面 -->
  <n-layout v-else has-sider class="admin-layout">
    <!-- 侧边栏 -->
    <n-layout-sider
      bordered
      collapse-mode="width"
      :collapsed-width="64"
      :width="240"
      :collapsed="collapsed"
      show-trigger
      @collapse="collapsed = true"
      @expand="collapsed = false"
    >
      <div class="sidebar-header">
        <div v-if="!collapsed" class="logo">
          <h3>RealityTap OTA</h3>
        </div>
        <div v-else class="logo-collapsed">
          <span>RT</span>
        </div>
      </div>

      <n-menu
        :collapsed="collapsed"
        :collapsed-width="64"
        :collapsed-icon-size="20"
        :options="menuOptions"
        :value="activeKey"
        @update:value="handleMenuSelect"
      />
    </n-layout-sider>

    <!-- 主内容区 -->
    <n-layout>
      <!-- 顶部导航 -->
      <n-layout-header bordered class="header">
        <div class="header-content">
          <div class="header-left">
            <n-breadcrumb>
              <n-breadcrumb-item>
                <router-link to="/">首页</router-link>
              </n-breadcrumb-item>
              <n-breadcrumb-item v-if="currentRoute.meta?.title">
                {{ currentRoute.meta.title }}
              </n-breadcrumb-item>
            </n-breadcrumb>
          </div>

          <div class="header-right">
            <n-space>
              <!-- 主题切换 -->
              <n-button quaternary circle @click="themeStore.toggleTheme()">
                <template #icon>
                  <n-icon :component="themeStore.isDark ? SunnyOutline : MoonOutline" />
                </template>
              </n-button>

              <!-- 刷新 -->
              <n-button quaternary circle :loading="adminStore.loading" @click="handleRefresh">
                <template #icon>
                  <n-icon :component="RefreshOutline" />
                </template>
              </n-button>

              <!-- 用户菜单 -->
              <n-dropdown trigger="click" :options="userMenuOptions" @select="handleUserMenuSelect">
                <n-button quaternary>
                  <template #icon>
                    <n-icon :component="PersonCircleOutline" />
                  </template>
                  {{ authStore.user?.username }}
                </n-button>
              </n-dropdown>
            </n-space>
          </div>
        </div>
      </n-layout-header>

      <!-- 内容区域 -->
      <n-layout-content class="content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<script setup lang="ts">
import { useAdminStore } from '@/stores/admin';
import { useAuthStore } from '@/stores/auth';
import { useThemeStore } from '@/stores/theme';
import {
  DocumentTextOutline,
  FolderOutline,
  HomeOutline,
  LogOutOutline,
  MoonOutline,
  PersonCircleOutline,
  RefreshOutline,
  SettingsOutline,
  SunnyOutline,
} from '@vicons/ionicons5';
import {
  NBreadcrumb,
  NBreadcrumbItem,
  NButton,
  NDropdown,
  NIcon,
  NLayout,
  NLayoutContent,
  NLayoutHeader,
  NLayoutSider,
  NMenu,
  NSpace,
  NSpin,
  useMessage,
  type MenuOption,
} from 'naive-ui';
import { computed, h, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();
const message = useMessage();
const authStore = useAuthStore();
const themeStore = useThemeStore();
const adminStore = useAdminStore();

const collapsed = ref(false);
const initializing = ref(true);

const currentRoute = computed(() => route);
const activeKey = computed(() => route.name as string);

// 菜单选项
const menuOptions: MenuOption[] = [
  {
    label: '仪表板',
    key: 'Dashboard',
    icon: () => h(NIcon, { component: HomeOutline }),
  },
  {
    label: '版本管理',
    key: 'VersionManagement',
    icon: () => h(NIcon, { component: FolderOutline }),
  },
  {
    label: '系统状态',
    key: 'SystemStatus',
    icon: () => h(NIcon, { component: SettingsOutline }),
  },
  {
    label: '系统日志',
    key: 'LogViewer',
    icon: () => h(NIcon, { component: DocumentTextOutline }),
  },
];

// 用户菜单选项
const userMenuOptions = [
  {
    label: '退出登录',
    key: 'logout',
    icon: () => h(NIcon, { component: LogOutOutline }),
  },
];

const handleMenuSelect = (key: string) => {
  router.push({ name: key });
};

const handleUserMenuSelect = async (key: string) => {
  if (key === 'logout') {
    await authStore.logout();
    message.success('已退出登录');
    router.push('/login');
  }
};

const handleRefresh = () => {
  adminStore.refreshData();
};

onMounted(async () => {
  try {
    // 检查当前认证状态
    if (!authStore.isAuthenticated) {
      // 如果未认证，尝试从本地存储恢复
      const savedToken = localStorage.getItem('admin_token');
      const savedUser = localStorage.getItem('admin_user');

      if (savedToken && savedUser) {
        // 只有在没有认证状态时才初始化
        await authStore.initAuth();
      }

      // 再次检查认证状态
      if (!authStore.isAuthenticated) {
        initializing.value = false;
        router.push('/login');
        return;
      }
    }

    // 加载初始数据
    try {
      await adminStore.refreshData();
    } catch (error) {
      console.error('Failed to load initial data:', error);
      // 不因为数据加载失败而退出登录
    }
  } finally {
    // 无论成功还是失败，都结束初始化状态
    initializing.value = false;
  }
});
</script>

<style scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--n-color);
}

.admin-layout {
  height: 100vh;
}

.sidebar-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--n-border-color);
}

.logo h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--n-text-color);
}

.logo-collapsed span {
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
}

.header {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 24px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.content {
  padding: 24px;
  overflow: auto;
}

.content-wrapper {
  margin: 0 auto;
}
</style>
