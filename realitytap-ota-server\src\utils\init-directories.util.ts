import { config } from '@/config/server.config';
import { logger } from '@/utils/logger.util';
import fs from 'fs-extra';
import path from 'path';

/**
 * 初始化所有必需的目录
 */
export async function initializeDirectories(): Promise<void> {
  const requiredDirectories = [
    config.storage.basePath,
    config.storage.releasesPath,
    config.storage.metadataPath,
    config.storage.logsPath,
    config.storage.tempPath,
    path.dirname(config.database.path), // 数据库目录
    config.database.backupPath, // 备份目录
  ];

  logger.info('开始初始化目录结构...', {
    operation: 'init_directories_start',
    module: 'init',
    directories: requiredDirectories,
    timestamp: new Date().toISOString(),
  });

  const results = [];

  for (const dirPath of requiredDirectories) {
    try {
      const exists = await fs.pathExists(dirPath);
      if (!exists) {
        await fs.ensureDir(dirPath);
        logger.info(`创建目录: ${dirPath}`, {
          operation: 'directory_created',
          module: 'init',
          path: dirPath,
          timestamp: new Date().toISOString(),
        });
        results.push({ path: dirPath, status: 'created' });
      } else {
        logger.debug(`目录已存在: ${dirPath}`, {
          operation: 'directory_exists',
          module: 'init',
          path: dirPath,
          timestamp: new Date().toISOString(),
        });
        results.push({ path: dirPath, status: 'exists' });
      }

      // 验证目录权限
      await fs.access(dirPath, fs.constants.R_OK | fs.constants.W_OK);
      
    } catch (error) {
      logger.error(`目录初始化失败: ${dirPath}`, {
        operation: 'directory_init_failed',
        module: 'init',
        path: dirPath,
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          stack: error instanceof Error ? error.stack : undefined,
        },
        timestamp: new Date().toISOString(),
      });
      throw new Error(`Failed to initialize directory: ${dirPath} - ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // 初始化元数据文件
  await initializeMetadataFiles();

  logger.info('目录结构初始化完成', {
    operation: 'init_directories_complete',
    module: 'init',
    results,
    timestamp: new Date().toISOString(),
  });
}

/**
 * 初始化元数据文件
 */
async function initializeMetadataFiles(): Promise<void> {
  const metadataFiles = [
    {
      path: path.join(config.storage.metadataPath, 'versions.json'),
      defaultContent: { versions: [] },
    },
    {
      path: path.join(config.storage.metadataPath, 'channels.json'),
      defaultContent: { 
        channels: {
          stable: { name: 'Stable', description: '稳定版本' },
          beta: { name: 'Beta', description: '测试版本' },
          alpha: { name: 'Alpha', description: '开发版本' },
        }
      },
    },
  ];

  for (const file of metadataFiles) {
    try {
      const exists = await fs.pathExists(file.path);
      if (!exists) {
        await fs.writeJson(file.path, file.defaultContent, { spaces: 2 });
        logger.info(`创建元数据文件: ${file.path}`, {
          operation: 'metadata_file_created',
          module: 'init',
          path: file.path,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      logger.error(`元数据文件初始化失败: ${file.path}`, {
        operation: 'metadata_file_init_failed',
        module: 'init',
        path: file.path,
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        },
        timestamp: new Date().toISOString(),
      });
      throw new Error(`Failed to initialize metadata file: ${file.path} - ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

/**
 * 检查目录健康状态
 */
export async function checkDirectoryHealth(): Promise<{
  healthy: boolean;
  issues: string[];
  details: Record<string, { exists: boolean; writable: boolean; error?: string }>;
}> {
  const requiredDirectories = [
    config.storage.basePath,
    config.storage.releasesPath,
    config.storage.metadataPath,
    config.storage.logsPath,
    config.storage.tempPath,
  ];

  const issues: string[] = [];
  const details: Record<string, { exists: boolean; writable: boolean; error?: string }> = {};

  for (const dirPath of requiredDirectories) {
    try {
      const exists = await fs.pathExists(dirPath);
      let writable = false;
      let error: string | undefined;

      if (exists) {
        try {
          await fs.access(dirPath, fs.constants.R_OK | fs.constants.W_OK);
          writable = true;
        } catch (accessError) {
          error = `无权限访问: ${accessError instanceof Error ? accessError.message : String(accessError)}`;
          issues.push(`目录 ${dirPath} 无读写权限`);
        }
      } else {
        error = '目录不存在';
        issues.push(`目录 ${dirPath} 不存在`);
      }

      details[dirPath] = { exists, writable, error };
    } catch (checkError) {
      const error = `检查失败: ${checkError instanceof Error ? checkError.message : String(checkError)}`;
      details[dirPath] = { exists: false, writable: false, error };
      issues.push(`目录 ${dirPath} 检查失败: ${error}`);
    }
  }

  return {
    healthy: issues.length === 0,
    issues,
    details,
  };
}
