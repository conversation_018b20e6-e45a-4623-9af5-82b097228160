#!/usr/bin/env pwsh

<#
.SYNOPSIS
    设置 Tauri 签名环境变量
    Setup Tauri signing environment variables

.DESCRIPTION
    此脚本帮助设置构建更新文件所需的环境变量。
    支持从文件路径或直接输入密钥内容。

.PARAMETER KeyPath
    私钥文件路径

.PARAMETER KeyContent
    私钥内容（直接输入）

.PARAMETER Password
    私钥密码（可选）

.PARAMETER LoadFromFile
    从 .env.production.signing 文件加载环境变量

.EXAMPLE
    .\scripts\setup-signing-env.ps1 -KeyPath "C:\keys\private.key"
    .\scripts\setup-signing-env.ps1 -LoadFromFile
    .\scripts\setup-signing-env.ps1 -KeyContent "-----BEGIN PRIVATE KEY-----..."
#>

param(
    [string]$KeyPath,
    [string]$KeyContent,
    [string]$Password,
    [switch]$LoadFromFile
)

# 获取项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot

Write-Host "🔐 设置 Tauri 签名环境变量" -ForegroundColor Cyan

if ($LoadFromFile) {
    # 从 .env.production.signing 文件加载
    $EnvFile = ".env.production.signing"
    
    if (-not (Test-Path $EnvFile)) {
        Write-Host "❌ 错误: 未找到 $EnvFile 文件" -ForegroundColor Red
        Write-Host "请先复制 .env.signing.example 为 .env.production.signing 并填入密钥信息" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Host "📁 从 $EnvFile 加载环境变量..." -ForegroundColor Yellow
    
    Get-Content $EnvFile | ForEach-Object {
        if ($_ -match '^([^=]+)=(.*)$' -and -not $_.StartsWith('#')) {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            
            if ($value) {
                [Environment]::SetEnvironmentVariable($name, $value, 'Process')
                Write-Host "✅ 设置 $name" -ForegroundColor Green
            }
        }
    }
    
} elseif ($KeyPath) {
    # 从文件路径设置
    if (-not (Test-Path $KeyPath)) {
        Write-Host "❌ 错误: 未找到私钥文件: $KeyPath" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "📁 从文件设置私钥: $KeyPath" -ForegroundColor Yellow
    $env:TAURI_SIGNING_PRIVATE_KEY = $KeyPath
    
    if ($Password) {
        $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD = $Password
        Write-Host "✅ 私钥密码已设置" -ForegroundColor Green
    }
    
    Write-Host "✅ 私钥路径已设置" -ForegroundColor Green
    
} elseif ($KeyContent) {
    # 直接设置密钥内容
    Write-Host "📝 设置私钥内容..." -ForegroundColor Yellow
    $env:TAURI_SIGNING_PRIVATE_KEY = $KeyContent
    
    if ($Password) {
        $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD = $Password
        Write-Host "✅ 私钥密码已设置" -ForegroundColor Green
    }
    
    Write-Host "✅ 私钥内容已设置" -ForegroundColor Green
    
} else {
    # 交互式设置
    Write-Host "🔧 交互式设置环境变量" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "请选择设置方式:" -ForegroundColor Cyan
    Write-Host "1. 从文件路径设置私钥" -ForegroundColor White
    Write-Host "2. 直接输入私钥内容" -ForegroundColor White
    Write-Host "3. 从 .env.production.signing 文件加载" -ForegroundColor White
    
    $choice = Read-Host "请输入选择 (1-3)"
    
    switch ($choice) {
        "1" {
            $keyPath = Read-Host "请输入私钥文件路径"
            if (Test-Path $keyPath) {
                $env:TAURI_SIGNING_PRIVATE_KEY = $keyPath
                Write-Host "✅ 私钥路径已设置" -ForegroundColor Green
            } else {
                Write-Host "❌ 文件不存在: $keyPath" -ForegroundColor Red
                exit 1
            }
        }
        "2" {
            Write-Host "请输入私钥内容 (多行输入，输入空行结束):" -ForegroundColor Cyan
            $keyLines = @()
            do {
                $line = Read-Host
                if ($line) {
                    $keyLines += $line
                }
            } while ($line)
            
            if ($keyLines.Count -gt 0) {
                $env:TAURI_SIGNING_PRIVATE_KEY = $keyLines -join "`n"
                Write-Host "✅ 私钥内容已设置" -ForegroundColor Green
            } else {
                Write-Host "❌ 未输入私钥内容" -ForegroundColor Red
                exit 1
            }
        }
        "3" {
            & $PSCommandPath -LoadFromFile
            return
        }
        default {
            Write-Host "❌ 无效选择" -ForegroundColor Red
            exit 1
        }
    }
    
    # 询问是否设置密码
    $setPassword = Read-Host "是否需要设置私钥密码? (y/N)"
    if ($setPassword -eq "y" -or $setPassword -eq "Y") {
        $password = Read-Host "请输入私钥密码" -AsSecureString
        $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))
        Write-Host "✅ 私钥密码已设置" -ForegroundColor Green
    }
}

# 验证环境变量
Write-Host "`n🔍 验证环境变量设置:" -ForegroundColor Cyan

if ($env:TAURI_SIGNING_PRIVATE_KEY) {
    Write-Host "✅ TAURI_SIGNING_PRIVATE_KEY: 已设置" -ForegroundColor Green
    
    # 显示密钥类型
    if ($env:TAURI_SIGNING_PRIVATE_KEY.StartsWith("-----BEGIN")) {
        Write-Host "   类型: 密钥内容" -ForegroundColor Gray
    } else {
        Write-Host "   类型: 文件路径 ($($env:TAURI_SIGNING_PRIVATE_KEY))" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ TAURI_SIGNING_PRIVATE_KEY: 未设置" -ForegroundColor Red
}

if ($env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD) {
    Write-Host "✅ TAURI_SIGNING_PRIVATE_KEY_PASSWORD: 已设置" -ForegroundColor Green
} else {
    Write-Host "⚠️  TAURI_SIGNING_PRIVATE_KEY_PASSWORD: 未设置（如果私钥无密码保护则正常）" -ForegroundColor Yellow
}

Write-Host "`n💡 下一步:" -ForegroundColor Yellow
Write-Host "现在可以运行构建命令:" -ForegroundColor White
Write-Host "  npm run build:with-updater" -ForegroundColor Cyan
Write-Host "  npm run generate:update-files" -ForegroundColor Cyan

Write-Host "`n✨ 环境变量设置完成！" -ForegroundColor Green
