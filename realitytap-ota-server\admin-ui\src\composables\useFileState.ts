import { ref, computed } from 'vue';

export function useFileState() {
  // 文件状态
  const installerFile = ref<File | null>(null);
  const signatureFile = ref<File | null>(null);
  const hashFile = ref<File | null>(null);
  const hashValue = ref<string | null>(null);
  const signatureValue = ref<string | null>(null);

  // 计算属性
  const selectedFiles = computed(() => {
    const files: File[] = [];
    if (installerFile.value) {
      files.push(installerFile.value);
    }
    // 注意：不将签名文件和hash文件包含在上传文件列表中
    // 签名内容和hash值将通过metadata传递
    return files;
  });

  const canStartUpload = computed(() => {
    return (
      installerFile.value !== null &&
      signatureFile.value !== null &&
      hashFile.value !== null &&
      signatureValue.value !== null &&
      hashValue.value !== null
    );
  });

  // 清除所有文件
  const clearAllFiles = () => {
    installerFile.value = null;
    signatureFile.value = null;
    hashFile.value = null;
    hashValue.value = null;
    signatureValue.value = null;
  };

  // 更新文件状态
  const updateInstallerFile = (file: File | null) => {
    installerFile.value = file;
    // 如果清除了安装包文件，也清除其他文件
    if (!file) {
      signatureFile.value = null;
      hashFile.value = null;
      signatureValue.value = null;
      hashValue.value = null;
    }
  };

  const updateSignatureFile = (file: File | null) => {
    signatureFile.value = file;
  };

  const updateHashFile = (file: File | null) => {
    hashFile.value = file;
  };

  const updateSignatureValue = (value: string | null) => {
    signatureValue.value = value;
  };

  const updateHashValue = (value: string | null) => {
    hashValue.value = value;
  };

  return {
    // 状态
    installerFile,
    signatureFile,
    hashFile,
    hashValue,
    signatureValue,

    // 计算属性
    selectedFiles,
    canStartUpload,

    // 方法
    clearAllFiles,
    updateInstallerFile,
    updateSignatureFile,
    updateHashFile,
    updateSignatureValue,
    updateHashValue,
  };
}
