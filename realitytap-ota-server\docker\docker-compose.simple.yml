# Simplified Docker Compose configuration for quick deployment
# This is a minimal configuration for development and testing

version: '3.8'

services:
  realitytap-ota-server:
    build:
      context: ..
      dockerfile: docker/Dockerfile.production
    container_name: realitytap-ota-server
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      # Basic Configuration
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      - BASE_URL=http://localhost:3000
      
      # Admin Configuration (CHANGE THESE!)
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=admin123
      - JWT_SECRET=simple_jwt_secret_for_development_only_32_chars
      
      # Database Configuration
      - DB_ENABLED=true
      - DB_PATH=/app/storage/database/ota.db
      
      # Storage Configuration
      - STORAGE_PATH=/app/storage
      - RELEASES_PATH=/app/storage/releases
      - LOGS_PATH=/app/storage/logs
      
      # Security Configuration
      - CORS_ORIGIN=*
      - LOG_LEVEL=info
      
    volumes:
      # Simple host directory mounting for easy access
      - ./data/storage:/app/storage
      - ./data/logs:/app/storage/logs
      
    networks:
      - realitytap-network
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  realitytap-network:
    driver: bridge

# Note: This configuration uses host directory mounting instead of named volumes
# Make sure to create the ./data directory before running:
# mkdir -p data/storage data/logs
