import { BaseDAO } from './base.dao';
import { ChannelConfig } from '@/types/server.types';

export interface ChannelEntity {
  id: number;
  name: string;
  enabled: boolean;
  description: string;
  auto_update: boolean;
  rollout_percentage: number;
  priority: number;
  created_at: string;
  updated_at: string;
}

export interface CreateChannelData {
  name: string;
  enabled?: boolean;
  description?: string;
  auto_update?: boolean;
  rollout_percentage?: number;
  priority?: number;
}

export interface UpdateChannelData {
  enabled?: boolean;
  description?: string;
  auto_update?: boolean;
  rollout_percentage?: number;
  priority?: number;
}

export class ChannelDAO extends BaseDAO {
  constructor() {
    super('channels');
  }

  /**
   * 创建渠道
   */
  async createChannel(data: CreateChannelData): Promise<number> {
    this.logOperation('createChannel', data);
    
    const channelData = {
      name: data.name,
      enabled: data.enabled ?? true,
      description: data.description || '',
      auto_update: data.auto_update ?? true,
      rollout_percentage: data.rollout_percentage ?? 100,
      priority: data.priority ?? 1
    };

    return await this.insert(channelData);
  }

  /**
   * 根据名称获取渠道
   */
  async getChannelByName(name: string): Promise<ChannelEntity | undefined> {
    this.logOperation('getChannelByName', { name });
    return await this.findOneWhere<ChannelEntity>({ name });
  }

  /**
   * 获取所有渠道
   */
  async getAllChannels(): Promise<ChannelEntity[]> {
    this.logOperation('getAllChannels');
    return await this.findAll<ChannelEntity>('priority ASC, name ASC');
  }

  /**
   * 获取启用的渠道
   */
  async getEnabledChannels(): Promise<ChannelEntity[]> {
    this.logOperation('getEnabledChannels');
    return await this.findWhere<ChannelEntity>({ enabled: true }, 'priority ASC, name ASC');
  }

  /**
   * 更新渠道
   */
  async updateChannel(name: string, data: UpdateChannelData): Promise<boolean> {
    this.logOperation('updateChannel', { name, data });
    
    const channel = await this.getChannelByName(name);
    if (!channel) {
      return false;
    }

    return await this.update(channel.id, data);
  }

  /**
   * 删除渠道
   */
  async deleteChannel(name: string): Promise<boolean> {
    this.logOperation('deleteChannel', { name });
    
    const channel = await this.getChannelByName(name);
    if (!channel) {
      return false;
    }

    return await this.delete(channel.id);
  }

  /**
   * 检查渠道是否存在
   */
  async channelExists(name: string): Promise<boolean> {
    return await this.exists({ name });
  }

  /**
   * 获取渠道配置（转换为旧格式）
   */
  async getChannelsConfig(): Promise<Record<string, ChannelConfig>> {
    this.logOperation('getChannelsConfig');
    
    const channels = await this.getAllChannels();
    const config: Record<string, ChannelConfig> = {};

    for (const channel of channels) {
      config[channel.name] = {
        enabled: channel.enabled,
        description: channel.description,
        autoUpdate: channel.auto_update,
        rolloutPercentage: channel.rollout_percentage,
        priority: channel.priority
      };
    }

    return config;
  }

  /**
   * 批量更新渠道配置
   */
  async updateChannelsConfig(config: Record<string, ChannelConfig>): Promise<void> {
    this.logOperation('updateChannelsConfig', config);

    await this.transaction(async () => {
      for (const [channelName, channelConfig] of Object.entries(config)) {
        const existingChannel = await this.getChannelByName(channelName);
        
        if (existingChannel) {
          // 更新现有渠道
          await this.updateChannel(channelName, {
            enabled: channelConfig.enabled,
            description: channelConfig.description,
            auto_update: channelConfig.autoUpdate,
            rollout_percentage: channelConfig.rolloutPercentage,
            priority: channelConfig.priority
          });
        } else {
          // 创建新渠道
          await this.createChannel({
            name: channelName,
            enabled: channelConfig.enabled,
            description: channelConfig.description,
            auto_update: channelConfig.autoUpdate,
            rollout_percentage: channelConfig.rolloutPercentage,
            priority: channelConfig.priority
          });
        }
      }
    });
  }

  /**
   * 获取渠道统计信息
   */
  async getChannelStats(): Promise<{
    total: number;
    enabled: number;
    disabled: number;
  }> {
    this.logOperation('getChannelStats');

    const [total, enabled] = await Promise.all([
      this.count(),
      this.count({ enabled: true })
    ]);

    return {
      total,
      enabled,
      disabled: total - enabled
    };
  }

  /**
   * 根据优先级排序获取渠道
   */
  async getChannelsByPriority(): Promise<ChannelEntity[]> {
    this.logOperation('getChannelsByPriority');
    return await this.findAll<ChannelEntity>('priority ASC, name ASC');
  }

  /**
   * 更新渠道优先级
   */
  async updateChannelPriority(name: string, priority: number): Promise<boolean> {
    this.logOperation('updateChannelPriority', { name, priority });
    return await this.updateChannel(name, { priority });
  }

  /**
   * 启用/禁用渠道
   */
  async toggleChannel(name: string, enabled: boolean): Promise<boolean> {
    this.logOperation('toggleChannel', { name, enabled });
    return await this.updateChannel(name, { enabled });
  }
}
