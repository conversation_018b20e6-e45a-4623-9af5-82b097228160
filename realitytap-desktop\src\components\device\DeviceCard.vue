<template>
  <n-card
    class="device-card"
    :class="{
      'device-card--connected': device.status === 'connected',
      'device-card--default': device.isDefault,
    }"
    hoverable
  >
    <!-- 设备头部信息 -->
    <template #header>
      <div class="device-header">
        <div class="device-info">
          <n-icon :component="deviceTypeIcon" :color="deviceTypeColor" size="20" />
          <span class="device-name">{{ device.name }}</span>
          <n-tag v-if="device.isDefault" type="success" size="small" round> {{ t('device.status.default') }} </n-tag>
        </div>
        <DeviceStatusIndicator :status="device.status" />
      </div>
    </template>

    <!-- 设备详细信息 -->
    <div class="device-details">
      <n-descriptions :column="1" size="small">
        <n-descriptions-item :label="t('device.details.deviceId')">
          <n-text code>{{ device.deviceId }}</n-text>
        </n-descriptions-item>
        <n-descriptions-item :label="t('device.details.deviceType')">
          {{ deviceTypeLabel }}
        </n-descriptions-item>
        <n-descriptions-item :label="t('device.details.connectionStatus')">
          <n-tag :type="statusTagType" size="small">
            {{ statusLabel }}
          </n-tag>
        </n-descriptions-item>
        <n-descriptions-item v-if="device.lastConnected" :label="t('device.details.lastConnected')">
          {{ formatTime(device.lastConnected) }}
        </n-descriptions-item>
        <n-descriptions-item v-if="device.metadata.manufacturer" :label="t('device.details.manufacturer')">
          {{ device.metadata.manufacturer }}
        </n-descriptions-item>
        <n-descriptions-item v-if="device.metadata.model" :label="t('device.details.model')">
          {{ device.metadata.model }}
        </n-descriptions-item>
      </n-descriptions>
    </div>

    <!-- 操作按钮 -->
    <template #action>
      <n-space justify="space-between">
        <n-space>
          <!-- 连接/断开按钮 -->
          <n-button v-if="device.status === 'disconnected' || device.status === 'error'" type="primary" size="small" :loading="isConnecting" @click="handleConnect">
            {{ t('device.actions.connect') }}
          </n-button>
          <n-button v-else-if="device.status === 'connected'" type="warning" size="small" :loading="isDisconnecting" @click="handleDisconnect"> {{ t('device.actions.disconnect') }} </n-button>
          <n-button v-else size="small" disabled>
            {{ statusLabel }}
          </n-button>

          <!-- 发送文件按钮 -->
          <n-button size="small" :disabled="device.status !== 'connected'" @click="handleSendFile">
            <template #icon>
              <n-icon><SendIcon /></n-icon>
            </template>
            {{ t('device.actions.sendFile') }}
          </n-button>
        </n-space>

        <!-- 更多操作 -->
        <n-dropdown :options="dropdownOptions" @select="handleDropdownSelect">
          <n-button size="small" quaternary circle>
            <template #icon>
              <n-icon><MoreIcon /></n-icon>
            </template>
          </n-button>
        </n-dropdown>
      </n-space>
    </template>

    <!-- 错误信息 -->
    <n-alert v-if="device.status === 'error' && device.metadata.lastError" type="error" size="small" :title="device.metadata.lastError" style="margin-top: 12px" />
  </n-card>

  <!-- 重命名对话框 -->
  <n-modal v-model:show="showRenameModal">
    <n-card style="width: 400px" :title="t('device.rename.title')" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <n-form @submit.prevent="handleRenameSubmit">
        <n-form-item :label="t('device.rename.deviceName')">
          <n-input v-model:value="newDeviceName" :placeholder="t('device.rename.placeholder')" maxlength="50" show-count />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space justify="end">
          <n-button @click="showRenameModal = false">{{ t('common.cancel') }}</n-button>
          <n-button type="primary" @click="handleRenameSubmit">{{ t('common.confirm') }}</n-button>
        </n-space>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  NCard,
  NIcon,
  NTag,
  NText,
  NDescriptions,
  NDescriptionsItem,
  NSpace,
  NButton,
  NDropdown,
  NAlert,
  NModal,
  NForm,
  NFormItem,
  NInput,
  useDialog,
  useMessage,
} from "naive-ui";
import { Send as SendIcon, EllipsisHorizontal as MoreIcon, Desktop as UsbIcon, Wifi as WifiIcon, Bluetooth as BluetoothIcon } from "@vicons/ionicons5";
import DeviceStatusIndicator from "./DeviceStatusIndicator.vue";
import type { Device, DeviceType, DeviceStatus } from "@/types/device-types";
import { DEVICE_TYPE_COLORS, getDeviceTypeKey, getDeviceStatusKey } from "@/utils/device/deviceConstants";
import { useI18n } from "@/composables/useI18n";

// === Props ===
interface Props {
  device: Device;
}

const props = defineProps<Props>();

// === Events ===
interface Emits {
  connect: [];
  disconnect: [];
  "set-default": [];
  remove: [];
  rename: [deviceId: string, newName: string];
  "send-file": [filePath: string];
}

const emit = defineEmits<Emits>();

// === 组合函数 ===
const dialog = useDialog();
const message = useMessage();
const { t } = useI18n();

// === 本地状态 ===
const isConnecting = ref(false);
const isDisconnecting = ref(false);
const showRenameModal = ref(false);
const newDeviceName = ref("");

// === 计算属性 ===
const deviceTypeIcon = computed(() => {
  switch (props.device.type) {
    case "usb":
      return UsbIcon;
    case "wifi":
      return WifiIcon;
    case "bluetooth":
      return BluetoothIcon;
    default:
      return UsbIcon;
  }
});

const deviceTypeColor = computed(() => DEVICE_TYPE_COLORS[props.device.type as DeviceType]);

const deviceTypeLabel = computed(() => t(getDeviceTypeKey(props.device.type as DeviceType)));

const statusLabel = computed(() => t(getDeviceStatusKey(props.device.status as DeviceStatus)));

const statusTagType = computed(() => {
  switch (props.device.status) {
    case "connected":
      return "success";
    case "connecting":
    case "disconnecting":
      return "info";
    case "error":
      return "error";
    default:
      return "default";
  }
});

const dropdownOptions = computed(() => [
  {
    label: props.device.isDefault ? t('device.actions.unsetDefault') : t('device.actions.setDefault'),
    key: "toggle-default",
    disabled: props.device.status !== "connected",
  },
  {
    label: t('device.actions.rename'),
    key: "rename",
  },
  {
    type: "divider",
    key: "divider",
  },
  {
    label: t('device.actions.remove'),
    key: "remove",
    props: {
      style: "color: #d03050",
    },
  },
]);

// === 工具函数 ===
const formatTime = (timeString: string): string => {
  try {
    const date = new Date(timeString);
    // 使用当前语言环境格式化时间
    return date.toLocaleString();
  } catch {
    return timeString;
  }
};

// === 事件处理 ===
const handleConnect = async () => {
  isConnecting.value = true;
  try {
    emit("connect");
  } finally {
    isConnecting.value = false;
  }
};

const handleDisconnect = async () => {
  isDisconnecting.value = true;
  try {
    emit("disconnect");
  } finally {
    isDisconnecting.value = false;
  }
};

const handleSendFile = () => {
  // TODO: 打开文件选择对话框
  message.info(t('device.messages.sendFileInfo'));
};

const handleDropdownSelect = (key: string) => {
  switch (key) {
    case "toggle-default":
      emit("set-default");
      break;
    case "rename":
      newDeviceName.value = props.device.name;
      showRenameModal.value = true;
      break;
    case "remove":
      dialog.warning({
        title: t('device.remove.confirmTitle'),
        content: t('device.remove.confirmMessage', { name: props.device.name }),
        positiveText: t('common.delete'),
        negativeText: t('common.cancel'),
        onPositiveClick: () => {
          emit("remove");
        },
      });
      break;
  }
};

const handleRenameSubmit = () => {
  if (!newDeviceName.value.trim()) {
    message.error(t('device.rename.nameRequired'));
    return;
  }

  if (newDeviceName.value.trim() === props.device.name) {
    showRenameModal.value = false;
    return;
  }

  emit("rename", props.device.deviceId, newDeviceName.value.trim());
  showRenameModal.value = false;
};
</script>

<style scoped>
.device-card {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.device-card--connected {
  border-color: var(--n-color-success);
  box-shadow: 0 0 0 1px var(--n-color-success-hover);
}

.device-card--default {
  background: linear-gradient(135deg, var(--n-color-success-hover) 0%, transparent 100%);
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-name {
  font-weight: 500;
  font-size: 16px;
}

.device-details {
  margin: 12px 0;
}
</style>
