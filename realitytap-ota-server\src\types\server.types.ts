// Server-specific types for OTA updates

// Tauri Plugin Updater 兼容的响应格式
export interface TauriUpdaterResponse {
  url: string;
  version: string;
  notes?: string;
  pub_date?: string;
  signature?: string;
  force_update?: boolean;
  file_size?: number; // 添加文件大小字段
}

// Update check request and response types
export interface UpdateCheckRequest {
  currentVersion: string;
  platform: 'windows' | 'macos' | 'linux';
  architecture: 'x86_64' | 'aarch64' | 'x86';
  channel: 'stable' | 'beta' | 'alpha';
  locale?: string;
}

export interface UpdateCheckResponse {
  hasUpdate: boolean;
  latestVersion?: string;
  downloadUrl?: string;
  releaseNotes?: string;
  fileSize?: number;
  checksum?: string;
  isForced?: boolean;
  signature?: string;
  releaseDate?: string;
}

// Server-specific types
export interface ServerVersionInfo {
  version: string;
  platforms: Record<string, Record<string, PlatformRelease>>;
}

export interface PlatformRelease {
  filename: string;
  size: number;
  checksum: string;
  releaseDate: string;
  releaseNotes: string;
  signature?: string;
}

export interface ChannelConfig {
  enabled: boolean;
  description: string;
  autoUpdate: boolean;
  rolloutPercentage: number;
  priority: number;
}

export interface VersionsConfig {
  channels: Record<string, ServerVersionInfo>;
  minimumVersions: Record<string, string>;
  deprecatedVersions: string[];
}

export interface ChannelsConfig {
  [channel: string]: ChannelConfig;
}

export interface DownloadStats {
  filename: string;
  downloadCount: number;
  totalBytes: number;
  lastDownload: string;
}

export interface ServerHealth {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  storage?: {
    available: boolean;
    freeSpace?: number;
    totalSpace?: number;
  };
  memory?: {
    used: number;
    total: number;
    percentage: number;
  };
}

export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  version: string;
}

export interface SuccessResponse<T = any> {
  success: true;
  data: T;
  timestamp: string;
  version: string;
}

// Download statistics types
export interface DownloadRecord {
  id: string;
  filename: string;
  version: string;
  platform: string;
  architecture: string;
  channel: string;
  downloadTime: string;
  clientIP: string;
  userAgent: string;
  fileSize: number;
  downloadDuration?: number;
}

export interface DownloadStatsData {
  totalDownloads: number;
  totalBytes: number;
  byVersion: Record<string, number>;
  byPlatform: Record<string, number>;
  byChannel: Record<string, number>;
  byDate: Record<string, number>;
  recentDownloads: DownloadRecord[];
}

export interface DownloadStatsQuery {
  startDate?: string;
  endDate?: string;
  version?: string;
  platform?: string;
  channel?: string;
  limit?: number;
  offset?: number;
}

// System configuration types
export interface SystemConfig {
  upload: {
    maxFileSize: number;
    allowedExtensions: string[];
    tempDir: string;
    cleanupInterval: number;
  };
  download: {
    rateLimit: {
      windowMs: number;
      maxRequests: number;
    };
    enableStats: boolean;
    enableCompression: boolean;
  };
  storage: {
    basePath: string;
    cleanupInterval: number;
    maxStorageSize: number;
  };
  security: {
    enableCORS: boolean;
    allowedOrigins: string[];
    enableHelmet: boolean;
  };
  logging: {
    level: string;
    enableFileLogging: boolean;
    maxLogFiles: number;
    maxLogSize: string;
  };
}

export interface ConfigChangeLog {
  id: string;
  timestamp: string;
  userId?: string;
  action: 'update' | 'reset';
  changes: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  reason?: string;
}
