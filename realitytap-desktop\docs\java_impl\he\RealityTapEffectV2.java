package android.os.vibrator.realitytap.he;

import android.annotation.NonNull;
import android.os.Parcel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/** @hide */
public class RealityTapEffectV2 extends RealityTapEffect {
    public static final int MAX_CURVE_COUNT = 16;
    public static final int MAX_EVENT_COUNT = 16;

    /** 当前振动的进程ID */
    private int processId = 0;

    /** 当前振动序列号 */
    private int sequence = 0;

    private final List<PatternListItem> patternList = new ArrayList<>();

    public RealityTapEffectV2() {
        super(VERSION_2);
    }

    protected RealityTapEffectV2(@NonNull Parcel in) {
        super(VERSION_2);
        setMetadata(in.readParcelable(Metadata.class.getClassLoader()));
        processId = in.readInt();
        sequence = in.readInt();
        patternList.addAll(in.createTypedArrayList(PatternListItem.CREATOR));
    }

    public static final @NonNull Creator<RealityTapEffectV2> CREATOR = new Creator<>() {
        @Override
        public RealityTapEffectV2 createFromParcel(@NonNull Parcel in) {
            return new RealityTapEffectV2(in);
        }

        @Override
        public RealityTapEffectV2[] newArray(int size) {
            return new RealityTapEffectV2[size];
        }
    };

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeParcelable(getMetadata(), flags);
        dest.writeInt(processId);
        dest.writeInt(sequence);
        dest.writeTypedList(patternList, flags);
    }

    public int getSequence() {
        return sequence;
    }

    public void setSequence(int sequence) {
        this.sequence = sequence;
    }

    public int getProcessId() {
        return processId;
    }

    public void setProcessId(int processId) {
        this.processId = processId;
    }

    public List<PatternListItem> getPatternList() {
        return patternList;
    }

    @Override
    public int getDuration() {
        if (patternList.isEmpty()) {
            return 0;
        }

        final int size = patternList.size();
        PatternListItem lastItem = patternList.get(size - 1);
        Event lastEvent = lastItem.getPatterns().get(lastItem.getPatterns().size() - 1);
        return lastEvent.getRelativeTime() + lastEvent.getDuration();
    }

    @Override
    public int[] convertToArray() {
        // 预先计算数组大小
        final int patternCount = patternList.size();
        int totalSize = 5; // 头部固定5个元素

        for (PatternListItem listItem : patternList) {
            totalSize += 3; // pattern list 每项固定3个元素
            for (Event event : listItem.getPatterns()) {
                totalSize += getEventLength(event) + 2; // +2 是因为要存储 event type 和 length
            }
        }

        int[] result = new int[totalSize];
        Arrays.fill(result, 0);

        // 写入头部信息
        int index = 0;
        result[index++] = getVersion();      // format version
        result[index++] = getVersion();      // metadata version
        result[index++] = getProcessId();    // process id
        result[index++] = getSequence();     // vibration sequence
        result[index++] = patternCount;      // pattern count

        // 写入pattern数据
        for (int i = 0; i < patternCount; i++) {
            PatternListItem listItem = patternList.get(i);
            result[index++] = listItem.getPatternIndex();                       // pattern index
            result[index++] = listItem.getAbsoluteTime();                       // absolute time
            result[index++] = listItem.getPatterns().size();                    // event count

            for (Event event : listItem.getPatterns()) {
                if (Event.TYPE_TRANSIENT.equals(event.getType())) {
                    result[index++] = Event.TRANSIENT;                         // event type
                    result[index++] = getEventLength(event);                    // event length
                    result[index++] = event.getIndex();                         // motor index
                    result[index++] = event.getRelativeTime();                  // relative time
                    result[index++] = event.getParameters().getIntensity();     // intensity
                    result[index++] = event.getParameters().getFrequency();     // frequency
                    result[index++] = 48;                                       // duration
                } else if (Event.TYPE_CONTINUOUS.equals(event.getType())) {
                    result[index++] = Event.CONTINUOUS;                         // event type
                    result[index++] = getEventLength(event);                    // event length
                    result[index++] = event.getIndex();                         // motor index
                    result[index++] = event.getRelativeTime();                  // relative time
                    result[index++] = event.getParameters().getIntensity();     // global intensity
                    result[index++] = event.getParameters().getFrequency();     // global frequency
                    result[index++] = event.getDuration();                      // duration
                    result[index++] = event.getParameters().getCurves().size(); // curve count

                    for (Curve curve : event.getParameters().getCurves()) {
                        result[index++] = curve.getTime();                      // curve time
                        result[index++] = (int) (curve.getIntensity() * 100);   // curve intensity
                        result[index++] = curve.getFrequency();                 // curve frequency
                    }
                }
            }
        }

        return result;
    }

    private static int getEventLength(Event event) {
        if (Event.TYPE_TRANSIENT.equals(event.getType())) {
            return 5; // transition event fixed length
        } else if (Event.TYPE_CONTINUOUS.equals(event.getType())) {
            return 3 * event.getParameters().getCurves().size() + 6;
        }
        return 0;
    }

    @NonNull
    @Override
    public RealityTapEffectV2 copy() {
        RealityTapEffectV2 copy = new RealityTapEffectV2();
        copy.setMetadata(getMetadata() != null ? getMetadata().copy() : null);
        copy.processId = this.processId;
        copy.sequence = this.sequence;
        for (PatternListItem item : patternList) {
            copy.patternList.add(item.copy());
        }
        return copy;
    }

    @NonNull
    @Override
    public String toString() {
        return "RealityTapEffectV2{" +
                "processId=" + processId +
                ", sequence=" + sequence +
                ", patternList=" + patternList +
                ", metadata=" + metadata +
                '}';
    }
}
