/**
 * 关于对话框相关逻辑
 * About dialog related logic
 */

import { ref, computed } from "vue";
import { getAppVersionInfo, formatVersionText, formatBuildInfo, formatPlatformInfo } from "@/utils/api/versionApi";
import type { AppVersionInfo, VersionCheckStatus, VersionDownloadProgress } from "@/types/version";
import { useUpdateNotificationStore } from "@/stores/update-notification-store";
import { logger, LogModule } from "@/utils/logger/logger";

import type { UpdateInfo } from "@/composables/useUpdater";

// === 全局状态 ===
const isVisible = ref(false);
const isLoading = ref(false);
const versionInfo = ref<AppVersionInfo | null>(null);
const error = ref<string | null>(null);

// === 更新对话框状态 ===
const updateDialogVisible = ref(false);
const updateInfo = ref<UpdateInfo | null>(null);
const downloadedFilePath = ref<string | null>(null);

// === 版本检查状态 ===
const versionCheckStatus = ref<VersionCheckStatus>({
  isChecking: false,
  hasNewVersion: false,
});

// === 下载进度状态 ===
const downloadProgress = ref<VersionDownloadProgress>({
  isDownloading: false,
  progress: 0,
});

export function useAboutDialog() {
  // === 计算属性 ===
  const formattedVersion = computed(() => {
    return versionInfo.value ? formatVersionText(versionInfo.value) : "未知版本";
  });

  const formattedBuildInfo = computed(() => {
    return versionInfo.value ? formatBuildInfo(versionInfo.value) : "未知构建信息";
  });

  const formattedPlatformInfo = computed(() => {
    return versionInfo.value ? formatPlatformInfo(versionInfo.value) : "未知平台";
  });

  // === 方法 ===

  /**
   * 显示关于对话框
   */
  const showAboutDialog = async () => {
    isVisible.value = true;
    await loadVersionInfo();
  };

  /**
   * 隐藏关于对话框
   */
  const hideAboutDialog = () => {
    isVisible.value = false;
    error.value = null;
  };

  /**
   * 加载版本信息
   */
  const loadVersionInfo = async () => {
    if (versionInfo.value) {
      // 如果已经加载过，直接返回
      return;
    }

    isLoading.value = true;
    error.value = null;

    try {
      versionInfo.value = await getAppVersionInfo();
    } catch (err) {
      error.value = err instanceof Error ? err.message : "加载版本信息失败";
      logger.error(LogModule.GENERAL, "加载版本信息失败", err);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 检查新版本 (集成真实OTA功能)
   */
  const checkForUpdates = async () => {
    versionCheckStatus.value.isChecking = true;
    versionCheckStatus.value.error = undefined;

    // 获取更新通知 store
    const updateStore = useUpdateNotificationStore();

    // 重置本地检查状态，但不清除全局通知状态
    updateInfo.value = null;
    downloadedFilePath.value = null;
    downloadProgress.value.isDownloading = false;
    downloadProgress.value.progress = 0;
    downloadProgress.value.error = undefined;
    versionCheckStatus.value.hasNewVersion = false;
    versionCheckStatus.value.latestVersion = undefined;
    versionCheckStatus.value.downloadUrl = undefined;
    versionCheckStatus.value.releaseNotes = undefined;
    versionCheckStatus.value.fileSize = undefined;
    versionCheckStatus.value.error = undefined;

    try {
      logger.info(LogModule.GENERAL, "开始检查更新");

      // 使用 useUpdater 的更新检查逻辑，它会正确使用用户配置的OTA服务器
      const { useUpdater } = await import("@/composables/useUpdater");
      const updater = useUpdater();

      const currentVersion = versionInfo.value?.appVersion || "1.0.0";
      logger.info(LogModule.GENERAL, "当前版本", { version: currentVersion });

      // OTA 配置现在通过 tauri.conf.json 管理
      logger.info(LogModule.GENERAL, "OTA 配置通过 tauri.conf.json 文件管理");

      // 记录手动检查更新操作到后端日志
      const { invoke } = await import("@tauri-apps/api/core");
      await invoke("log_ota_operation", {
        operation: "MANUAL_CHECK_START",
        message: "用户手动检查更新",
        details: {
          timestamp: new Date().toISOString()
        }
      });

      logger.info(LogModule.GENERAL, "开始调用 useUpdater.checkForUpdates");
      // 手动检查更新时，强制输出详细日志（silent = false）
      const updateResult = await updater.checkForUpdates(false);
      logger.info(LogModule.GENERAL, "更新检查响应", updateResult);

      if (updateResult) {
        versionCheckStatus.value.hasNewVersion = true;
        versionCheckStatus.value.latestVersion = updateResult.version;
        versionCheckStatus.value.downloadUrl = ""; // tauri-plugin-updater 不需要手动指定
        versionCheckStatus.value.releaseNotes = updateResult.body || "";
        versionCheckStatus.value.fileSize = updateResult.file_size || 0;

        // 转换为 UpdateInfo 格式以匹配 UpdateDialog 的期望
        updateInfo.value = {
          version: updateResult.version,
          date: updateResult.date,
          body: updateResult.body || "",
          file_size: updateResult.file_size || 0,
        };

        // 同步更新到全局通知状态
        updateStore.setUpdateAvailable({
          version: updateResult.version,
          date: updateResult.date,
          body: updateResult.body || "",
          file_size: updateResult.file_size || 0,
        });

        logger.info(LogModule.GENERAL, "发现新版本，等待用户查看更新详情");
      } else {
        // 没有更新时，明确重置状态
        versionCheckStatus.value.hasNewVersion = false;
        versionCheckStatus.value.latestVersion = undefined;
        versionCheckStatus.value.downloadUrl = undefined;
        versionCheckStatus.value.releaseNotes = undefined;
        versionCheckStatus.value.fileSize = undefined;

        // 手动检查没有发现新版本时，清除可能存在的旧通知
        updateStore.clearUpdateNotification();

        logger.info(LogModule.GENERAL, "当前已是最新版本");
      }

      versionCheckStatus.value.lastCheckTime = new Date().toISOString();
    } catch (err) {
      logger.error(LogModule.GENERAL, "更新检查失败", err);
      versionCheckStatus.value.error = err instanceof Error ? err.message : "检查更新失败";
      // 出错时也要重置更新状态
      versionCheckStatus.value.hasNewVersion = false;
      versionCheckStatus.value.latestVersion = undefined;
      versionCheckStatus.value.downloadUrl = undefined;
      versionCheckStatus.value.releaseNotes = undefined;
      versionCheckStatus.value.fileSize = undefined;
    } finally {
      versionCheckStatus.value.isChecking = false;
    }
  };

  /**
   * 下载新版本 (使用新的 updater)
   */
  const downloadUpdate = async () => {
    downloadProgress.value.isDownloading = true;
    downloadProgress.value.progress = 0;
    downloadProgress.value.error = undefined;

    try {
      // 使用 useUpdater 的下载安装逻辑
      const { useUpdater } = await import("@/composables/useUpdater");
      const updater = useUpdater();

      // 直接下载并安装
      const success = await updater.downloadAndInstall();

      if (success) {
        logger.info(LogModule.GENERAL, "更新安装成功，应用即将重启");
        clearUpdateInfo();
      } else {
        downloadProgress.value.error = "更新安装失败";
      }
    } catch (err) {
      logger.error(LogModule.GENERAL, "下载失败", err);
      downloadProgress.value.error = err instanceof Error ? err.message : "下载失败";
    } finally {
      downloadProgress.value.isDownloading = false;
    }
  };

  /**
   * 显示更新对话框
   */
  const showUpdateDialog = () => {
    updateDialogVisible.value = true;
    // 不在这里隐藏红点，红点应该一直显示直到用户真正安装了最新版本
  };

  /**
   * 隐藏更新对话框
   * 注意：不清空 updateInfo，保持更新信息可用于重复打开
   */
  const hideUpdateDialog = () => {
    updateDialogVisible.value = false;
    // 不清空 updateInfo，让用户可以重复查看更新信息
  };

  /**
   * 清空更新信息
   * 只在完成安装时调用，会清除所有状态包括红点通知
   */
  const clearUpdateInfo = () => {
    updateInfo.value = null;
    downloadedFilePath.value = null;
    // 重置下载进度
    downloadProgress.value.isDownloading = false;
    downloadProgress.value.progress = 0;
    downloadProgress.value.error = undefined;
    // 重置版本检查状态（除了 isChecking 和 lastCheckTime）
    versionCheckStatus.value.hasNewVersion = false;
    versionCheckStatus.value.latestVersion = undefined;
    versionCheckStatus.value.downloadUrl = undefined;
    versionCheckStatus.value.releaseNotes = undefined;
    versionCheckStatus.value.fileSize = undefined;
    versionCheckStatus.value.error = undefined;

    // 清除全局通知状态
    const updateStore = useUpdateNotificationStore();
    updateStore.clearUpdateNotification();
  };

  /**
   * 开始下载更新
   */
  const startUpdateDownload = async () => {
    try {
      logger.info(LogModule.GENERAL, "开始下载更新");

      downloadProgress.value.isDownloading = true;
      downloadProgress.value.error = undefined;
      downloadProgress.value.progress = 0;

      // 使用 useUpdater 的下载安装逻辑
      const { useUpdater } = await import("@/composables/useUpdater");
      const updater = useUpdater();

      // 直接下载并安装
      const success = await updater.downloadAndInstall();

      if (success) {
        logger.info(LogModule.GENERAL, "更新安装成功，应用即将重启");
        clearUpdateInfo();
      } else {
        downloadProgress.value.error = "更新安装失败";
      }
    } catch (err) {
      logger.error(LogModule.GENERAL, "下载失败", err);
      const errorMessage = err instanceof Error ? err.message : "下载失败";
      downloadProgress.value.error = errorMessage;
    } finally {
      downloadProgress.value.isDownloading = false;
    }
  };

  /**
   * 安装更新
   */
  const installUpdate = async () => {
    try {
      logger.info(LogModule.GENERAL, "开始安装更新");

      // 使用 useUpdater 的下载安装逻辑
      const { useUpdater } = await import("@/composables/useUpdater");
      const updater = useUpdater();

      // 直接下载并安装
      const success = await updater.downloadAndInstall();

      if (success) {
        logger.info(LogModule.GENERAL, "更新安装成功，应用即将重启");
        clearUpdateInfo();
      } else {
        throw new Error("更新安装失败");
      }
    } catch (err) {
      logger.error(LogModule.GENERAL, "useAboutDialog.installUpdate 失败", err);

      // 提取详细的错误信息
      let errorMessage = "安装失败：未知错误";
      if (err instanceof Error) {
        errorMessage = `安装失败：${err.message}`;
      } else if (typeof err === "string") {
        errorMessage = `安装失败：${err}`;
      }

      logger.error(LogModule.GENERAL, "格式化错误信息", { errorMessage });
      throw new Error(errorMessage);
    }
  };

  /**
   * 稍后提醒
   */
  const remindLater = () => {
    hideUpdateDialog();
    // TODO: 设置提醒时间
  };

  /**
   * 稍后安装
   */
  const installLater = () => {
    hideUpdateDialog();
    // TODO: 保存下载的文件，稍后安装
  };

  return {
    // 状态
    isVisible,
    isLoading,
    versionInfo,
    error,
    versionCheckStatus,
    downloadProgress,
    updateDialogVisible,
    updateInfo,
    downloadedFilePath,

    // 计算属性
    formattedVersion,
    formattedBuildInfo,
    formattedPlatformInfo,

    // 方法
    showAboutDialog,
    hideAboutDialog,
    loadVersionInfo,
    checkForUpdates,
    downloadUpdate,
    showUpdateDialog,
    hideUpdateDialog,
    startUpdateDownload,
    installUpdate,
    remindLater,
    installLater,
  };
}
