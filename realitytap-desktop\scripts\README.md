# FFmpeg 下载脚本

由于 FFmpeg 二进制文件过大（超过 GitHub 的 100MB 文件大小限制），我们将它们从版本控制中移除，并提供脚本在构建时自动下载。

## 使用方法

### Windows (PowerShell)

```powershell
# Download FFmpeg binaries for all platforms
.\scripts\download-ffmpeg-en.ps1

# Download for specific platform
.\scripts\download-ffmpeg-en.ps1 -Platform linux-x64

# Force re-download
.\scripts\download-ffmpeg-en.ps1 -Force
```

**Note**: Use `download-ffmpeg-en.ps1` for the working English version.

### Linux/macOS (Bash)

```bash
# 下载所有平台的 FFmpeg 二进制文件
./scripts/download-ffmpeg.sh

# 下载特定平台
./scripts/download-ffmpeg.sh --platform linux-x64

# 强制重新下载
./scripts/download-ffmpeg.sh --force
```

## 支持的平台

- `linux-x64`: Linux x86_64
- `linux-arm64`: Linux ARM64
- `macos`: macOS x86_64/ARM64 通用版本
- `all`: 所有平台（默认）

## 构建集成

这些脚本应该在构建过程中自动运行。您可以将它们集成到：

1. **Tauri 构建脚本** (`build.rs`)
2. **npm/yarn 脚本** (`package.json`)
3. **CI/CD 流水线**

### 示例：在 package.json 中添加

```json
{
  "scripts": {
    "prebuild": "powershell -ExecutionPolicy Bypass -File scripts/download-ffmpeg.ps1",
    "build": "tauri build"
  }
}
```

### 示例：在 build.rs 中添加

```rust
use std::process::Command;

fn main() {
    // 下载 FFmpeg 二进制文件
    if cfg!(target_os = "windows") {
        Command::new("powershell")
            .args(&["-ExecutionPolicy", "Bypass", "-File", "scripts/download-ffmpeg.ps1"])
            .status()
            .expect("Failed to download FFmpeg");
    } else {
        Command::new("bash")
            .args(&["scripts/download-ffmpeg.sh"])
            .status()
            .expect("Failed to download FFmpeg");
    }
}
```

## 依赖要求

### Windows
- PowerShell 5.0+
- 7zip 或 tar 命令（用于解压）

### Linux/macOS
- Bash
- curl 或 wget（用于下载）
- tar（用于解压）

## 故障排除

1. **网络问题**: 确保可以访问 GitHub 和下载 URL
2. **权限问题**: 确保脚本有执行权限
3. **解压工具**: 确保系统安装了必要的解压工具

## 文件结构

下载完成后，FFmpeg 二进制文件将位于：

```
src-tauri/ffmpeg/
├── linux/
│   ├── x64/
│   │   ├── ffmpeg
│   │   └── ffprobe
│   └── arm64/
│       ├── ffmpeg
│       └── ffprobe
└── macos/
    ├── ffmpeg
    └── ffprobe
```

这些文件已添加到 `.gitignore` 中，不会被提交到版本控制。
