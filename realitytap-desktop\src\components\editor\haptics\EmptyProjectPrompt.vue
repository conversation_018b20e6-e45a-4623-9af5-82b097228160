<template>
  <div class="empty-clip-area">
    <div class="content">
      <div class="icon">▶</div>
      <h3 class="title">{{ t('editor.empty.title') }}</h3>
      <p class="description">
        {{ t('editor.empty.description') }}
      </p>
      <n-button-group>
        <n-button type="primary" @click="createNewHapticFileInRoot">
          {{ t('editor.empty.addHapticFile') }}
        </n-button>
        <n-dropdown
          trigger="click"
          :options="dropdownOptions"
          @select="handleDropdownSelect"
          placement="bottom-end"
        >
          <n-button
            type="primary"
            :focusable="false"
            style="padding-left: 8px; padding-right: 8px"
          >
            <template #icon>
              <n-icon><ChevronDownIcon /></n-icon>
            </template>
          </n-button>
        </n-dropdown>
      </n-button-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NDropdown, NButton, NButtonGroup, NIcon } from "naive-ui";
import type { DropdownOption } from "naive-ui";
import { ChevronDownOutline as ChevronDownIcon } from "@vicons/ionicons5";
import { useI18n } from "@/composables/useI18n";
import { useFileOperations } from "@/composables/useFileOperations";

const { t } = useI18n();
const { createNewHapticFileInRoot, importAudioFileToRoot, importVideoFileToRoot } = useFileOperations();

const dropdownOptions: DropdownOption[] = [
  {
    label: t('editor.empty.addAudioFile'),
    key: "audio",
  },
  {
    label: t('editor.empty.addVideoFile'),
    key: "video",
  },
];

const handleDropdownSelect = (key: string | number) => {
  if (key === "audio") {
    importAudioFileToRoot();
  } else if (key === "video") {
    importVideoFileToRoot();
  }
};
</script>

<style scoped>
.empty-clip-area {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 220px;
}

.icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #555;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.title {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0 0 0.5rem;
}

.description {
  font-size: 0.9rem;
  margin: 0 0 1.5rem;
  color: #888;
  line-height: 1.4;
}
</style>
