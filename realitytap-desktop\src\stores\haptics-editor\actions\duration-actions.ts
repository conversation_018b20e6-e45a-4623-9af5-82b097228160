import type { RenderableEvent } from "../types";
import { DEFAULT_DURATION_MS } from "../types";
import { calculateEventsTotalDuration } from "../utils/converters";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 时长管理相关操作
 */
export interface DurationActions {
  updateBaseDurations: (effectDurationMs: number, audioDurationMs: number | null) => void;
  increaseTotalDurationByUser: (durationToAddMs: number) => void;
  recalculateTotalDuration: () => void;
  setTotalDuration: (duration: number) => void;
  lockDurationByAudio: (audioDuration: number) => void;
  unlockDuration: () => void;
}

/**
 * 创建时长管理操作
 */
export function createDurationActions(
  state: {
    totalDuration: number;
    isDurationLockedByAudio: boolean;
    events: RenderableEvent[];
  },
  setState: (updates: Partial<typeof state>) => void
): DurationActions {
  return {
    /**
     * 更新基础时长
     */
    updateBaseDurations(effectDurationMs: number, audioDurationMs: number | null) {
      logger.debug(LogModule.WAVEFORM, `更新基础时长: effect=${effectDurationMs}ms, audio=${audioDurationMs}ms`);

      if (audioDurationMs && audioDurationMs > 0) {
        setState({
          totalDuration: audioDurationMs,
          isDurationLockedByAudio: true,
        });
        logger.debug(LogModule.WAVEFORM, `音频时长锁定: ${audioDurationMs}ms`);
      } else {
        setState({
          totalDuration: Math.max(effectDurationMs, DEFAULT_DURATION_MS),
          isDurationLockedByAudio: false,
        });
        logger.debug(LogModule.WAVEFORM, `效果时长设置: ${Math.max(effectDurationMs, DEFAULT_DURATION_MS)}ms`);
      }
    },

    /**
     * 用户手动增加总时长
     */
    increaseTotalDurationByUser(durationToAddMs: number) {
      if (!state.isDurationLockedByAudio && durationToAddMs > 0) {
        // 确保时长始终为整数
        const integerDurationToAdd = Math.floor(durationToAddMs);
        const newDuration = Math.floor(state.totalDuration + integerDurationToAdd);

        setState({
          totalDuration: newDuration,
        });

        logger.debug(LogModule.WAVEFORM, `用户增加时长: +${integerDurationToAdd}ms, 新时长: ${newDuration}ms`);
      } else {
        logger.debug(LogModule.WAVEFORM, `时长增加被阻止: 音频锁定=${state.isDurationLockedByAudio}, 增加量=${durationToAddMs}ms`);
      }
    },

    /**
     * 根据事件重新计算总时长
     */
    recalculateTotalDuration() {
      if (!state.isDurationLockedByAudio) {
        const calculatedDuration = calculateEventsTotalDuration(state.events);
        const newDuration = Math.max(calculatedDuration, DEFAULT_DURATION_MS);

        setState({
          totalDuration: newDuration,
        });

        logger.debug(LogModule.WAVEFORM, `重新计算时长: ${newDuration}ms`);
      } else {
        logger.debug(LogModule.WAVEFORM, "音频时长锁定，跳过重新计算");
      }
    },

    /**
     * 直接设置总时长
     */
    setTotalDuration(duration: number) {
      if (!state.isDurationLockedByAudio && duration > 0) {
        const integerDuration = Math.floor(duration);

        setState({
          totalDuration: integerDuration,
        });

        logger.debug(LogModule.WAVEFORM, `设置总时长: ${integerDuration}ms`);
      } else {
        logger.debug(LogModule.WAVEFORM, `时长设置被阻止: 音频锁定=${state.isDurationLockedByAudio}, 时长=${duration}ms`);
      }
    },

    /**
     * 通过音频锁定时长
     */
    lockDurationByAudio(audioDuration: number) {
      setState({
        totalDuration: Math.floor(audioDuration),
        isDurationLockedByAudio: true,
      });

      logger.debug(LogModule.WAVEFORM, `音频锁定时长: ${Math.floor(audioDuration)}ms`);
    },

    /**
     * 解锁时长
     */
    unlockDuration() {
      setState({
        isDurationLockedByAudio: false,
      });

      logger.debug(LogModule.WAVEFORM, "解锁时长限制");
    },
  };
}

/**
 * 时长相关的 Getters
 */
export interface DurationGetters {
  currentTotalDuration: () => number;
  isTimelineDurationAdjustable: () => boolean;
  isDurationLocked: () => boolean;
  getEffectiveDuration: () => number;
}

/**
 * 创建时长相关的 Getters
 */
export function createDurationGetters(state: { totalDuration: number; isDurationLockedByAudio: boolean; events: RenderableEvent[] }): DurationGetters {
  return {
    /**
     * 获取当前总时长
     */
    currentTotalDuration() {
      return state.totalDuration;
    },

    /**
     * 检查时间轴时长是否可调整
     */
    isTimelineDurationAdjustable() {
      return !state.isDurationLockedByAudio;
    },

    /**
     * 检查时长是否被锁定
     */
    isDurationLocked() {
      return state.isDurationLockedByAudio;
    },

    /**
     * 获取有效时长（考虑事件和设定时长的最大值）
     */
    getEffectiveDuration() {
      const eventsDuration = calculateEventsTotalDuration(state.events);
      return Math.max(state.totalDuration, eventsDuration);
    },
  };
}
