import { BaseDAO } from './base.dao';
import { ServerVersionInfo, VersionsConfig } from '@/types/server.types';
import { logger } from '../utils/logger.util';

export interface VersionEntity {
  id: number;
  channel_id: number;
  version: string;
  force_update: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateVersionData {
  channel_id: number;
  version: string;
  force_update?: boolean;
}

export interface UpdateVersionData {
  version?: string;
  force_update?: boolean;
}

export class VersionDAO extends BaseDAO {
  constructor() {
    super('versions');
  }

  /**
   * 创建版本
   */
  async createVersion(data: CreateVersionData): Promise<number> {
    this.logOperation('createVersion', data);
    return await this.insert(data);
  }

  /**
   * 根据ID获取版本
   */
  async getVersionById(id: number): Promise<VersionEntity | undefined> {
    this.logOperation('getVersionById', { id });
    return await this.findById<VersionEntity>(id);
  }

  /**
   * 根据渠道ID获取版本
   */
  async getVersionsByChannelId(channelId: number): Promise<VersionEntity[]> {
    this.logOperation('getVersionsByChannelId', { channelId });
    return await this.findWhere<VersionEntity>({ channel_id: channelId }, 'version DESC');
  }

  /**
   * 根据渠道名称获取最新版本
   */
  async getLatestVersionByChannel(channelName: string): Promise<VersionEntity | undefined> {
    this.logOperation('getLatestVersionByChannel', { channelName });

    const sql = `
      SELECT v.* FROM versions v
      JOIN channels c ON v.channel_id = c.id
      WHERE c.name = ? AND c.enabled = 1
      ORDER BY v.version DESC
      LIMIT 1
    `;

    logger.info('Executing getLatestVersionByChannel SQL', { sql, channelName });
    const results = await this.query<VersionEntity>(sql, [channelName]);
    logger.info('getLatestVersionByChannel results', { results, count: results.length });

    // 额外的调试信息
    if (results.length > 0) {
      const firstResult = results[0];
      if (firstResult) {
        logger.info('First result details', {
          id: firstResult.id,
          hasChannelId: 'channel_id' in firstResult,
          hasVersion: 'version' in firstResult,
          hasName: 'name' in firstResult,
          keys: Object.keys(firstResult)
        });
      }
    }

    return results.length > 0 ? results[0] : undefined;
  }

  /**
   * 根据渠道ID和版本号获取版本
   */
  async getVersionByChannelAndVersion(channelId: number, version: string): Promise<VersionEntity | undefined> {
    this.logOperation('getVersionByChannelAndVersion', { channelId, version });
    return await this.findOneWhere<VersionEntity>({ channel_id: channelId, version });
  }

  /**
   * 更新版本
   */
  async updateVersion(id: number, data: UpdateVersionData): Promise<boolean> {
    this.logOperation('updateVersion', { id, data });
    return await this.update(id, data);
  }

  /**
   * 删除版本
   */
  async deleteVersion(id: number): Promise<boolean> {
    this.logOperation('deleteVersion', { id });
    return await this.delete(id);
  }

  /**
   * 根据渠道ID删除所有版本
   */
  async deleteVersionsByChannelId(channelId: number): Promise<number> {
    this.logOperation('deleteVersionsByChannelId', { channelId });
    return await this.deleteWhere({ channel_id: channelId });
  }

  /**
   * 检查版本是否存在
   */
  async versionExists(channelId: number, version: string): Promise<boolean> {
    return await this.exists({ channel_id: channelId, version });
  }

  /**
   * 获取所有版本（带渠道信息）
   */
  async getAllVersionsWithChannels(): Promise<Array<VersionEntity & { channel_name: string }>> {
    this.logOperation('getAllVersionsWithChannels');
    
    const sql = `
      SELECT v.*, c.name as channel_name
      FROM versions v
      JOIN channels c ON v.channel_id = c.id
      ORDER BY c.priority ASC, v.version DESC
    `;
    
    return await this.query<VersionEntity & { channel_name: string }>(sql);
  }

  /**
   * 获取版本统计信息
   */
  async getVersionStats(): Promise<{
    totalVersions: number;
    versionsByChannel: Record<string, number>;
  }> {
    this.logOperation('getVersionStats');

    const totalVersions = await this.count();
    
    const sql = `
      SELECT c.name as channel_name, COUNT(v.id) as version_count
      FROM channels c
      LEFT JOIN versions v ON c.id = v.channel_id
      GROUP BY c.id, c.name
    `;
    
    const channelStats = await this.query<{ channel_name: string; version_count: number }>(sql);
    
    const versionsByChannel: Record<string, number> = {};
    for (const stat of channelStats) {
      versionsByChannel[stat.channel_name] = stat.version_count;
    }

    return {
      totalVersions,
      versionsByChannel
    };
  }

  /**
   * 批量创建版本
   */
  async batchCreateVersions(versions: CreateVersionData[]): Promise<void> {
    this.logOperation('batchCreateVersions', { count: versions.length });
    
    await this.transaction(async () => {
      for (const version of versions) {
        await this.createVersion(version);
      }
    });
  }

  /**
   * 获取渠道的版本历史
   */
  async getVersionHistory(channelName: string, limit: number = 10): Promise<VersionEntity[]> {
    this.logOperation('getVersionHistory', { channelName, limit });
    
    const sql = `
      SELECT v.* FROM versions v
      JOIN channels c ON v.channel_id = c.id
      WHERE c.name = ?
      ORDER BY v.created_at DESC
      LIMIT ?
    `;
    
    return await this.query<VersionEntity>(sql, [channelName, limit]);
  }

  /**
   * 检查版本是否比指定版本新
   */
  async isVersionNewer(channelName: string, currentVersion: string, targetVersion: string): Promise<boolean> {
    this.logOperation('isVersionNewer', { channelName, currentVersion, targetVersion });
    
    // 这里需要实现版本比较逻辑
    // 可以使用 semver 库或自定义版本比较逻辑
    const semver = require('semver');
    
    try {
      return semver.gt(targetVersion, currentVersion);
    } catch (error) {
      // 如果不是标准的 semver 格式，使用字符串比较
      return targetVersion > currentVersion;
    }
  }

  /**
   * 清理旧版本（保留指定数量的最新版本）
   */
  async cleanupOldVersions(channelId: number, keepCount: number = 5): Promise<number> {
    this.logOperation('cleanupOldVersions', { channelId, keepCount });

    const sql = `
      DELETE FROM versions
      WHERE channel_id = ? AND id NOT IN (
        SELECT id FROM (
          SELECT id FROM versions
          WHERE channel_id = ?
          ORDER BY created_at DESC
          LIMIT ?
        ) as keep_versions
      )
    `;

    const result = await this.execute(sql, [channelId, channelId, keepCount]);
    return result;
  }

  /**
   * 获取渠道中指定版本范围内的所有版本
   * 用于强制更新检查
   */
  async getVersionsInRange(channelName: string, fromVersion: string, toVersion: string): Promise<VersionEntity[]> {
    this.logOperation('getVersionsInRange', { channelName, fromVersion, toVersion });

    const sql = `
      SELECT v.* FROM versions v
      JOIN channels c ON v.channel_id = c.id
      WHERE c.name = ? AND c.enabled = 1
      ORDER BY v.version ASC
    `;

    const allVersions = await this.query<VersionEntity>(sql, [channelName]);

    // 使用 semver 进行版本过滤
    const semver = require('semver');
    const filteredVersions = allVersions.filter(v => {
      try {
        return semver.gt(v.version, fromVersion) && semver.lte(v.version, toVersion);
      } catch (error) {
        // 如果不是标准的 semver 格式，使用字符串比较
        return v.version > fromVersion && v.version <= toVersion;
      }
    });

    return filteredVersions;
  }

  /**
   * 检查版本范围内是否有强制更新版本
   */
  async hasForceUpdateInRange(channelName: string, fromVersion: string, toVersion: string): Promise<boolean> {
    this.logOperation('hasForceUpdateInRange', { channelName, fromVersion, toVersion });

    const versionsInRange = await this.getVersionsInRange(channelName, fromVersion, toVersion);
    return versionsInRange.some(v => v.force_update);
  }

  /**
   * 根据渠道名称和版本号获取版本信息
   */
  async getVersionByChannelNameAndVersion(channelName: string, version: string): Promise<VersionEntity | undefined> {
    this.logOperation('getVersionByChannelNameAndVersion', { channelName, version });

    const sql = `
      SELECT v.* FROM versions v
      JOIN channels c ON v.channel_id = c.id
      WHERE c.name = ? AND v.version = ?
    `;

    const results = await this.query<VersionEntity>(sql, [channelName, version]);
    return results.length > 0 ? results[0] : undefined;
  }
}
