<template>
  <div class="progress-display">
    <n-card size="small">
      <template #header>
        <n-space align="center" justify="space-between">
          <span>上传进度</span>
          <n-tag :type="getStatusType(progress.status)">
            {{ getStatusText(progress.status) }}
          </n-tag>
        </n-space>
      </template>

      <!-- 总体进度 -->
      <div class="progress-section">
        <n-progress
          type="line"
          :percentage="progress.percentage"
          :status="getProgressStatus(progress.status)"
          :show-indicator="true"
          :processing="progress.status === UploadStatus.UPLOADING"
        />
        <n-space justify="space-between" style="margin-top: 8px">
          <n-text depth="3">
            {{ formatBytes(progress.uploadedBytes) }} / {{ formatBytes(progress.totalBytes) }}
          </n-text>
          <n-text depth="3" v-if="progress.percentage > 0"> {{ progress.percentage.toFixed(1) }}% </n-text>
        </n-space>

        <!-- 详细信息 -->
        <div v-if="progress.status === UploadStatus.UPLOADING && progress.speed > 0" class="progress-details">
          <n-space justify="space-between" style="margin-top: 4px; font-size: 12px">
            <n-text depth="3"> 上传速度: {{ formatSpeed(progress.speed) }} </n-text>
            <n-text depth="3" v-if="progress.estimatedTimeRemaining > 0">
              剩余时间: {{ formatTime(progress.estimatedTimeRemaining) }}
            </n-text>
          </n-space>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="progress-actions">
        <n-space>
          <n-button v-if="canCancel" size="small" type="error" @click="$emit('cancel')"> 取消 </n-button>
        </n-space>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { UploadStatus, type UploadProgress } from '@/types/upload';
import { NButton, NCard, NProgress, NSpace, NTag, NText } from 'naive-ui';
import { computed } from 'vue';

// Props
interface Props {
  progress: UploadProgress;
  isUploading: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  cancel: [];
}>();

// 计算属性
const canCancel = computed(() => {
  return [UploadStatus.UPLOADING, UploadStatus.INITIALIZING].includes(props.progress.status);
});

// 工具函数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getStatusType = (status: UploadStatus) => {
  switch (status) {
    case UploadStatus.UPLOADING:
      return 'info';
    case UploadStatus.COMPLETED:
      return 'success';
    case UploadStatus.FAILED:
    case UploadStatus.CANCELLED:
      return 'error';
    default:
      return 'default';
  }
};

const getStatusText = (status: UploadStatus) => {
  switch (status) {
    case UploadStatus.INITIALIZING:
      return '初始化中';
    case UploadStatus.UPLOADING:
      return '上传中';
    case UploadStatus.COMPLETED:
      return '已完成';
    case UploadStatus.FAILED:
      return '上传失败';
    case UploadStatus.CANCELLED:
      return '已取消';
    case UploadStatus.MERGING:
      return '合并中';
    default:
      return '未知';
  }
};

const getProgressStatus = (status: UploadStatus) => {
  switch (status) {
    case UploadStatus.COMPLETED:
      return 'success';
    case UploadStatus.FAILED:
    case UploadStatus.CANCELLED:
      return 'error';
    default:
      return 'default';
  }
};

const formatSpeed = (bytesPerSecond: number): string => {
  return formatBytes(bytesPerSecond) + '/s';
};

const formatTime = (seconds: number): string => {
  if (seconds === 0 || !isFinite(seconds)) return '--';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};
</script>

<style scoped>
.progress-display {
  margin: 16px 0;
}

.progress-section {
  margin-bottom: 16px;
}

.progress-actions {
  margin-top: 16px;
}
</style>
