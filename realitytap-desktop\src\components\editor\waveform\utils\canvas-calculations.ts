/**
 * 画布尺寸计算工具函数
 * 专门处理画布宽度、高度和区域计算
 */

// 最大画布宽度限制（像素），防止内存过度分配
export const MAX_CANVAS_WIDTH = 8000; // 8000px 约等于 64MB 内存（假设 300px 高度，DPR=2）

/**
 * 计算目标图形宽度（逻辑宽度，用于坐标计算）
 */
export function calculateTargetGraphWidth(
  availableParentWidth: number,
  totalEffectDuration: number,
  baselineDuration: number,
  paddingLeft: number,
  paddingRight: number,
  _audioDuration?: number | null
): number {
  let targetGraphWidth: number;

  // Store 已经正确处理了音频时长锁定，直接使用 totalEffectDuration
  const effectiveDuration = totalEffectDuration;

  if (baselineDuration <= 0 || availableParentWidth <= 0) {
    targetGraphWidth = availableParentWidth > 0 ? availableParentWidth : 300;
  } else {
    const defaultTimeStep = availableParentWidth / baselineDuration;
    if (effectiveDuration <= baselineDuration) {
      targetGraphWidth = availableParentWidth;
    } else {
      targetGraphWidth = defaultTimeStep * effectiveDuration;
    }
  }

  const minWidth = paddingLeft + paddingRight + 50;
  targetGraphWidth = Math.max(targetGraphWidth, minWidth);
  return targetGraphWidth;
}

/**
 * 计算实际画布宽度（物理宽度，限制内存分配）
 */
export function calculateActualCanvasWidth(
  logicalWidth: number,
  availableParentWidth: number,
  paddingLeft: number,
  paddingRight: number
): number {
  // 计算可用的绘图区域宽度
  const availableDrawingWidth = availableParentWidth - paddingLeft - paddingRight;

  // 如果逻辑宽度小于等于可用宽度，直接使用逻辑宽度
  if (logicalWidth <= availableParentWidth) {
    return logicalWidth;
  }

  // 否则，使用虚拟滚动：画布宽度限制为可用宽度的2-3倍（提供缓冲区）
  const bufferMultiplier = 2.5;
  const maxPhysicalWidth = Math.min(
    availableDrawingWidth * bufferMultiplier + paddingLeft + paddingRight,
    MAX_CANVAS_WIDTH
  );

  return Math.max(maxPhysicalWidth, availableParentWidth);
}

/**
 * 计算图形区域宽度
 */
export function graphAreaWidth(
  canvasWidth: number,
  paddingLeft: number,
  paddingRight: number,
  safeOffset: number
): number {
  return canvasWidth - paddingLeft - paddingRight - safeOffset;
}

/**
 * 计算图形区域高度
 */
export function graphAreaHeight(canvasHeight: number, paddingTop: number, paddingBottom: number): number {
  return canvasHeight - paddingTop - paddingBottom;
}

/**
 * 计算逻辑图形区域宽度
 */
export function logicalGraphAreaWidth(
  logicalCanvasWidth: number,
  paddingLeft: number,
  paddingRight: number,
  safeOffset: number
): number {
  return logicalCanvasWidth - paddingLeft - paddingRight - safeOffset;
}
