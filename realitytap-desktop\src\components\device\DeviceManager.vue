<template>
  <div class="device-manager">
    <n-card :title="t('device.management.title')" :bordered="false">
      <!-- 工具栏 -->
      <template #header-extra>
        <n-space>
          <n-button type="primary" :loading="isScanning" @click="handleScanDevices">
            <template #icon>
              <n-icon><SearchIcon /></n-icon>
            </template>
            {{ t('device.actions.scan') }}
          </n-button>
          <n-button quaternary @click="handleRefresh">
            <template #icon>
              <n-icon><RefreshIcon /></n-icon>
            </template>
            {{ t('device.actions.refresh') }}
          </n-button>
          <n-button secondary @click="handleAddTestDevice">
            <template #icon>
              <n-icon><AddIcon /></n-icon>
            </template>
            {{ t('device.actions.addTestDevice') }}
          </n-button>
        </n-space>
      </template>

      <!-- 统计信息 -->
      <div class="statistics-section">
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <n-statistic :label="t('device.status.total')" :value="statistics.totalDevices" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic :label="t('device.status.connected')" :value="statistics.connectedDevices" :value-style="{ color: '#18a058' }" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic :label="t('device.status.disconnected')" :value="statistics.disconnectedDevices" :value-style="{ color: '#909399' }" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic :label="t('device.status.errorDevices')" :value="statistics.errorDevices" :value-style="{ color: '#d03050' }" />
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 过滤器 -->
      <div class="filter-section">
        <n-space>
          <n-select v-model:value="filterType" :placeholder="t('device.filter.deviceType')" clearable style="min-width: 170px" :options="deviceTypeOptions" @update:value="handleFilterChange" />
          <n-select v-model:value="filterStatus" :placeholder="t('device.filter.connectionStatus')" clearable style="min-width: 160px" :options="deviceStatusOptions" @update:value="handleFilterChange" />
          <n-input v-model:value="searchText" :placeholder="t('device.filter.searchDevices')" clearable style="width: 200px" @update:value="handleSearchChange">
            <template #prefix>
              <n-icon><SearchIcon /></n-icon>
            </template>
          </n-input>
        </n-space>
      </div>

      <!-- 设备列表 -->
      <div class="device-list-section">
        <DeviceList
          :devices="devices.value"
          :loading="isLoading"
          @connect="handleConnectDevice"
          @disconnect="handleDisconnectDevice"
          @set-default="handleSetDefaultDevice"
          @remove="handleRemoveDevice"
          @rename="handleRenameDevice"
          @send-file="handleSendFile"
        />
      </div>

      <!-- 错误提示 -->
      <n-alert v-if="lastError" type="error" :title="lastError" closable @close="clearError" style="margin-top: 16px" />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { NCard, NButton, NSpace, NIcon, NGrid, NGridItem, NStatistic, NSelect, NInput, NAlert, useMessage } from "naive-ui";
import { Search as SearchIcon, Refresh as RefreshIcon, Add as AddIcon } from "@vicons/ionicons5";
import DeviceList from "./DeviceList.vue";
import { useDeviceManager } from "@/composables/useDeviceManager";
import { DeviceType, DeviceStatus } from "@/types/device-types";
import { getDeviceTypeKey, getDeviceStatusKey } from "@/utils/device/deviceConstants";
import { useI18n } from "@/composables/useI18n";

// === 组合函数 ===
const deviceManager = useDeviceManager({
  autoInitialize: true,
  enableAutoScan: true,
  enableLogging: true,
});

const message = useMessage();
const { t } = useI18n();

// === 本地状态 ===
const isLoading = ref(false);
const filterType = ref<DeviceType | null>(null);
const filterStatus = ref<DeviceStatus | null>(null);
const searchText = ref("");

// === 计算属性 ===
const devices = computed(() => deviceManager.devices);
const statistics = computed(() => deviceManager.statistics);
const isScanning = computed(() => deviceManager.isScanning);
const lastError = computed(() => deviceManager.lastError.value);

const deviceTypeOptions = computed(() =>
  Object.values(DeviceType).map((deviceType) => ({
    value: deviceType,
    label: t(getDeviceTypeKey(deviceType)),
  }))
);

const deviceStatusOptions = computed(() =>
  Object.values(DeviceStatus).map((deviceStatus) => ({
    value: deviceStatus,
    label: t(getDeviceStatusKey(deviceStatus)),
  }))
);

// === 事件处理 ===
const handleScanDevices = async () => {
  try {
    isLoading.value = true;
    const result = await deviceManager.scanDevices();
    if (result.success) {
      message.success(result.message || t('device.messages.scanComplete'));
    } else {
      message.error(result.error || t('device.messages.scanFailed'));
    }
  } catch (error: any) {
    message.error(error.message || t('device.messages.scanFailed'));
  } finally {
    isLoading.value = false;
  }
};

const handleRefresh = async () => {
  try {
    isLoading.value = true;
    await deviceManager.initialize();
    message.success(t('device.messages.refreshSuccess'));
  } catch (error: any) {
    message.error(error.message || t('device.messages.refreshFailed'));
  } finally {
    isLoading.value = false;
  }
};

const handleFilterChange = () => {
  deviceManager.setFilter({
    types: filterType.value ? [filterType.value] : undefined,
    statuses: filterStatus.value ? [filterStatus.value] : undefined,
    searchText: searchText.value || undefined,
  });
};

const handleSearchChange = () => {
  deviceManager.setFilter({
    searchText: searchText.value || undefined,
  });
};

const handleConnectDevice = async (deviceId: string) => {
  try {
    const result = await deviceManager.connectDevice(deviceId);
    if (result.success) {
      message.success(result.message || t('device.messages.connectSuccess'));
    } else {
      message.error(result.error || t('device.messages.connectFailed'));
    }
  } catch (error: any) {
    message.error(error.message || t('device.messages.connectFailed'));
  }
};

const handleDisconnectDevice = async (deviceId: string) => {
  try {
    const result = await deviceManager.disconnectDevice(deviceId);
    if (result.success) {
      message.success(result.message || t('device.messages.disconnectSuccess'));
    } else {
      message.error(result.error || t('device.messages.disconnectFailed'));
    }
  } catch (error: any) {
    message.error(error.message || t('device.messages.disconnectFailed'));
  }
};

const handleSetDefaultDevice = async (deviceId: string) => {
  try {
    const result = await deviceManager.setDefaultDevice(deviceId);
    if (result.success) {
      message.success(result.message || t('device.messages.setDefaultSuccess'));
    } else {
      message.error(result.error || t('device.messages.setDefaultFailed'));
    }
  } catch (error: any) {
    message.error(error.message || t('device.messages.setDefaultFailed'));
  }
};

const handleRemoveDevice = async (deviceId: string) => {
  try {
    const success = await deviceManager.removeDevice(deviceId);
    if (success) {
      message.success(t('device.messages.removeSuccess'));
    } else {
      message.error(t('device.messages.removeFailed'));
    }
  } catch (error: any) {
    message.error(error.message || t('device.messages.removeFailed'));
  }
};

const handleRenameDevice = async (deviceId: string, newName: string) => {
  try {
    const success = await deviceManager.renameDevice(deviceId, newName);
    if (success) {
      message.success(t('device.messages.renameSuccess'));
    } else {
      message.error(t('device.messages.renameFailed'));
    }
  } catch (error: any) {
    message.error(error.message || t('device.messages.renameFailed'));
  }
};

const handleSendFile = async (_deviceId: string, _filePath: string) => {
  try {
    // TODO: 实现文件发送逻辑
    message.info(t('device.messages.sendFileInfo'));
  } catch (error: any) {
    message.error(error.message || t('device.messages.sendFileFailed'));
  }
};

const handleAddTestDevice = async () => {
  try {
    // 创建一个测试设备
    const testDevice = {
      deviceId: `test-device-${Date.now()}`,
      name: t('device.testDevice.name', { number: Math.floor(Math.random() * 100) }),
      type: ["usb", "wifi", "bluetooth"][Math.floor(Math.random() * 3)] as any,
      connectionType: "usb" as any,
      status: "disconnected" as any,
      lastConnected: null,
      lastDisconnected: null,
      isDefault: false,
      metadata: {
        manufacturer: t('device.testDevice.manufacturer'),
        model: t('device.testDevice.model'),
        version: "1.0.0",
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // 添加到设备管理器
    deviceManager.addDevice(testDevice);
    message.success(t('device.messages.addTestDeviceSuccess'));
  } catch (error: any) {
    message.error(error.message || t('device.messages.addTestDeviceFailed'));
  }
};

const clearError = () => {
  deviceManager.clearError();
};

// === 生命周期 ===
onMounted(async () => {
  if (!deviceManager.isInitialized) {
    try {
      await deviceManager.initialize();
    } catch (error) {
      console.error(t('device.messages.initializeFailed'), error);
    }
  }
});
</script>

<style scoped>
.device-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.statistics-section {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--n-card-color);
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
}

.filter-section {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--n-card-color);
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
}

.device-list-section {
  flex: 1;
  min-height: 0;
}
</style>
