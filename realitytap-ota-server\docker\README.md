# RealityTap OTA Server Docker 部署方案

## 🎯 概述

本目录包含了 RealityTap OTA Server 的完整 Docker 部署解决方案，支持：

- ✅ **环境变量配置管理**：管理员认证、HTTPS 控制、数据库参数
- ✅ **数据持久化**：自动初始化数据库和目录结构
- ✅ **灵活部署模式**：HTTP/HTTPS 两种模式，可选 Nginx 反向代理
- ✅ **零停机升级**：完整的升级和回滚机制
- ✅ **生产就绪**：安全配置、健康检查、日志管理

## 🚀 快速开始

### 1. 一键启动（推荐新手）

**Linux/macOS:**
```bash
./scripts/quick-start.sh
```

**Windows (PowerShell):**
```powershell
.\scripts\quick-start.ps1
```

### 2. 标准部署

```bash
# 1. 配置环境变量
cp docker/.env.docker.example docker/.env
nano docker/.env  # 编辑配置

# 2. 部署服务
./scripts/docker-deploy.sh -m http    # HTTP 模式
./scripts/docker-deploy.sh -m https   # HTTPS 模式
```

## 📋 部署模式

### HTTP 模式（开发/内网）
```bash
# 使用部署脚本
./scripts/docker-deploy.sh -m http

# 或直接使用 docker-compose
cd docker
docker-compose -f docker-compose.http.yml up -d
```

### HTTPS 模式（生产环境）
```bash
# 准备 SSL 证书
mkdir -p ssl
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem

# 配置环境变量
echo "ENABLE_HTTPS=true" >> docker/.env
echo "SSL_CERT_HOST_PATH=./ssl/cert.pem" >> docker/.env
echo "SSL_KEY_HOST_PATH=./ssl/key.pem" >> docker/.env

# 部署
./scripts/docker-deploy.sh -m https
```

### 简化模式（快速测试）
```bash
cd docker
docker-compose -f docker-compose.simple.yml up -d
```

## ⚙️ 配置文件说明

| 文件 | 用途 | 适用场景 |
|------|------|----------|
| `docker-compose.simple.yml` | 简化配置，快速启动 | 开发测试 |
| `docker-compose.http.yml` | HTTP 模式，完整配置 | 开发/内网部署 |
| `docker-compose.https.yml` | HTTPS 模式，生产配置 | 生产环境 |
| `.env.docker.example` | 环境变量模板 | 配置参考 |

## 🔧 核心环境变量

### 必需配置
```bash
# 管理员认证
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password

# JWT 密钥（≥32字符）
JWT_SECRET=your_super_secret_jwt_key_min_32_chars_long

# 基础 URL
BASE_URL=http://localhost:3000
```

### HTTPS 配置
```bash
# 启用 HTTPS
ENABLE_HTTPS=true

# SSL 证书路径
SSL_CERT_HOST_PATH=./ssl/cert.pem
SSL_KEY_HOST_PATH=./ssl/key.pem
```

### 数据库配置
```bash
# 启用 SQLite 数据库
DB_ENABLED=true
DB_MAX_CONNECTIONS=10
DB_ENABLE_WAL=true
```

## 🗄️ 数据持久化

### 数据卷
- `realitytap-ota-storage`: 应用数据存储
- `realitytap-ota-database`: SQLite 数据库
- `realitytap-ota-logs`: 日志文件
- `realitytap-ota-backup`: 备份文件

### 目录结构
```
/app/storage/
├── database/           # SQLite 数据库
├── releases/           # 应用安装包
│   ├── stable/
│   ├── beta/
│   └── alpha/
├── logs/              # 日志文件
├── temp/              # 临时文件
├── backup/            # 备份文件
└── metadata/          # 元数据文件
```

## 🔄 升级和维护

### 自动升级
```bash
# 升级到最新版本
./scripts/docker-upgrade.sh

# 升级到指定版本
./scripts/docker-upgrade.sh -v 1.2.3

# 回滚到上一版本
./scripts/docker-upgrade.sh --rollback
```

### 手动操作
```bash
# 查看状态
docker-compose -f docker-compose.http.yml ps

# 查看日志
docker-compose -f docker-compose.http.yml logs -f

# 重启服务
docker-compose -f docker-compose.http.yml restart

# 停止服务
docker-compose -f docker-compose.http.yml down
```

## 🌐 Nginx 反向代理

### 启用 Nginx
```bash
# 使用 nginx profile
./scripts/docker-deploy.sh -p nginx

# 或直接启动
docker-compose -f docker-compose.http.yml --profile nginx up -d
```

### 配置文件
- `nginx/nginx.http.conf`: HTTP 反向代理配置
- `nginx/nginx.https.conf`: HTTPS 反向代理配置

## 🔒 安全配置

### SSL 证书生成

**自签名证书（开发）:**
```bash
mkdir -p ssl
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes
```

**Let's Encrypt（生产）:**
```bash
sudo certbot certonly --standalone -d your-domain.com
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem
```

### 安全最佳实践
- 使用强密码（≥12字符）
- JWT 密钥随机生成（≥32字符）
- 生产环境启用 HTTPS
- 配置具体的 CORS 域名
- 定期更新密钥和证书

## 🐛 故障排除

### 常见问题

**容器启动失败:**
```bash
# 查看详细日志
docker-compose -f docker-compose.http.yml logs realitytap-ota-server

# 检查配置
docker-compose -f docker-compose.http.yml config
```

**数据库问题:**
```bash
# 检查数据库文件
docker exec realitytap-ota-server ls -la /app/storage/database/

# 重新初始化
docker exec realitytap-ota-server rm -f /app/storage/database/ota.db
docker-compose -f docker-compose.http.yml restart
```

**SSL 证书问题:**
```bash
# 验证证书
openssl x509 -in ssl/cert.pem -text -noout
openssl rsa -in ssl/key.pem -check

# 检查挂载
docker exec realitytap-ota-server ls -la /app/ssl/
```

## 📚 相关文档

- [完整部署文档](../docs/DOCKER_DEPLOYMENT.md)
- [项目主文档](../README.md)
- [API 文档](../docs/API.md)

## 🆘 获取帮助

如果遇到问题：
1. 查看故障排除部分
2. 检查项目 Issues
3. 提交新 Issue 并包含详细信息

---

**⚠️ 生产环境提醒：**
- 更改所有默认密码
- 配置备份策略
- 设置监控告警
- 定期安全更新
