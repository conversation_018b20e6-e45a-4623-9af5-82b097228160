// 重新导出项目类型
export * from "./haptic-project";

// 重新导出设备管理类型
export * from "./device-types";
export * from "./device-transmission";

// 定义其他全局类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
}

export interface UserSettings {
  darkMode: boolean;
  defaultProjectSettings: {
    audioPath: string;
    exportFormat: "json" | "binary";
  };
  recentProjects: string[]; // 项目ID列表
}

export type AppMode = "create" | "edit" | "preview";
