/**
 * OTA更新相关常量
 */

/**
 * 更新检查间隔（毫秒）
 */
export const UPDATE_CHECK_INTERVALS = {
  IMMEDIATE: 0,
  HOURLY: 60 * 60 * 1000,
  DAILY: 24 * 60 * 60 * 1000,
  WEEKLY: 7 * 24 * 60 * 60 * 1000,
  MANUAL: -1, // 仅手动检查
} as const;

/**
 * 下载状态
 */
export const DOWNLOAD_STATUS = {
  IDLE: 'idle',
  DOWNLOADING: 'downloading',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

/**
 * 更新状态
 */
export const UPDATE_STATUS = {
  CHECKING: 'checking',
  AVAILABLE: 'available',
  NOT_AVAILABLE: 'not_available',
  DOWNLOADING: 'downloading',
  DOWNLOADED: 'downloaded',
  INSTALLING: 'installing',
  INSTALLED: 'installed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

/**
 * 更新类型优先级
 */
export const UPDATE_TYPE_PRIORITY = {
  major: 3,
  minor: 2,
  patch: 1,
  none: 0,
} as const;

/**
 * 发布渠道优先级
 */
export const CHANNEL_PRIORITY = {
  stable: 3,
  beta: 2,
  alpha: 1,
} as const;

/**
 * 文件大小限制（字节）
 */
export const FILE_SIZE_LIMITS = {
  MAX_UPDATE_SIZE: 500 * 1024 * 1024, // 500MB
  MIN_FREE_SPACE: 1024 * 1024 * 1024, // 1GB
} as const;

/**
 * 网络相关配置
 */
export const NETWORK_CONFIG = {
  DEFAULT_TIMEOUT: 30000, // 30秒
  DOWNLOAD_TIMEOUT: 300000, // 5分钟
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1秒
  CHUNK_SIZE: 1024 * 1024, // 1MB
} as const;

/**
 * 校验算法
 */
export const CHECKSUM_ALGORITHMS = {
  SHA256: 'sha256',
  MD5: 'md5',
  SHA1: 'sha1',
  SHA512: 'sha512',
} as const;

/**
 * 默认配置
 */
export const DEFAULT_OTA_CONFIG = {
  checkInterval: UPDATE_CHECK_INTERVALS.DAILY,
  autoDownload: false,
  autoInstall: false,
  channel: 'stable' as const,
  enableNotifications: true,
  enableBackgroundCheck: true,
  maxRetries: NETWORK_CONFIG.MAX_RETRIES,
  timeout: NETWORK_CONFIG.DEFAULT_TIMEOUT,
} as const;

/**
 * 错误代码
 */
export const OTA_ERROR_CODES = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CONNECTION_ERROR: 'CONNECTION_ERROR',
  
  // 服务器错误
  SERVER_ERROR: 'SERVER_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  UNAUTHORIZED: 'UNAUTHORIZED',
  
  // 文件错误
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  FILE_CORRUPTED: 'FILE_CORRUPTED',
  CHECKSUM_MISMATCH: 'CHECKSUM_MISMATCH',
  INSUFFICIENT_SPACE: 'INSUFFICIENT_SPACE',
  
  // 版本错误
  INVALID_VERSION: 'INVALID_VERSION',
  VERSION_NOT_SUPPORTED: 'VERSION_NOT_SUPPORTED',
  DOWNGRADE_NOT_ALLOWED: 'DOWNGRADE_NOT_ALLOWED',
  
  // 安装错误
  INSTALL_FAILED: 'INSTALL_FAILED',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  PROCESS_RUNNING: 'PROCESS_RUNNING',
  
  // 用户操作
  USER_CANCELLED: 'USER_CANCELLED',
  USER_REJECTED: 'USER_REJECTED',
  
  // 系统错误
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',

  // 第四阶段新增错误代码
  // 调度器错误
  SCHEDULER_ERROR: 'SCHEDULER_ERROR',
  SCHEDULE_CONFLICT: 'SCHEDULE_CONFLICT',

  // 后台检查错误
  BACKGROUND_CHECK_FAILED: 'BACKGROUND_CHECK_FAILED',
  SYSTEM_CONDITIONS_NOT_MET: 'SYSTEM_CONDITIONS_NOT_MET',

  // 回滚错误
  ROLLBACK_FAILED: 'ROLLBACK_FAILED',
  BACKUP_NOT_FOUND: 'BACKUP_NOT_FOUND',
  RESTORE_POINT_NOT_FOUND: 'RESTORE_POINT_NOT_FOUND',
  BACKUP_CORRUPTED: 'BACKUP_CORRUPTED',

  // 临时错误
  TEMPORARY_ERROR: 'TEMPORARY_ERROR',
  RATE_LIMITED: 'RATE_LIMITED',
} as const;

/**
 * 错误消息映射
 */
export const OTA_ERROR_MESSAGES = {
  [OTA_ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [OTA_ERROR_CODES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [OTA_ERROR_CODES.CONNECTION_ERROR]: '无法连接到更新服务器',
  [OTA_ERROR_CODES.SERVER_ERROR]: '服务器内部错误',
  [OTA_ERROR_CODES.NOT_FOUND]: '更新文件未找到',
  [OTA_ERROR_CODES.UNAUTHORIZED]: '访问被拒绝',
  [OTA_ERROR_CODES.FILE_NOT_FOUND]: '更新文件不存在',
  [OTA_ERROR_CODES.FILE_CORRUPTED]: '更新文件已损坏',
  [OTA_ERROR_CODES.CHECKSUM_MISMATCH]: '文件完整性校验失败',
  [OTA_ERROR_CODES.INSUFFICIENT_SPACE]: '磁盘空间不足',
  [OTA_ERROR_CODES.INVALID_VERSION]: '版本号格式无效',
  [OTA_ERROR_CODES.VERSION_NOT_SUPPORTED]: '不支持的版本',
  [OTA_ERROR_CODES.DOWNGRADE_NOT_ALLOWED]: '不允许降级',
  [OTA_ERROR_CODES.INSTALL_FAILED]: '安装失败',
  [OTA_ERROR_CODES.PERMISSION_DENIED]: '权限不足',
  [OTA_ERROR_CODES.PROCESS_RUNNING]: '应用程序正在运行',
  [OTA_ERROR_CODES.USER_CANCELLED]: '用户取消操作',
  [OTA_ERROR_CODES.USER_REJECTED]: '用户拒绝更新',
  [OTA_ERROR_CODES.SYSTEM_ERROR]: '系统错误',
  [OTA_ERROR_CODES.UNKNOWN_ERROR]: '未知错误',

  // 第四阶段新增错误消息
  [OTA_ERROR_CODES.SCHEDULER_ERROR]: '调度器错误',
  [OTA_ERROR_CODES.SCHEDULE_CONFLICT]: '调度冲突',
  [OTA_ERROR_CODES.BACKGROUND_CHECK_FAILED]: '后台检查失败',
  [OTA_ERROR_CODES.SYSTEM_CONDITIONS_NOT_MET]: '系统条件不满足',
  [OTA_ERROR_CODES.ROLLBACK_FAILED]: '回滚失败',
  [OTA_ERROR_CODES.BACKUP_NOT_FOUND]: '备份文件未找到',
  [OTA_ERROR_CODES.RESTORE_POINT_NOT_FOUND]: '还原点未找到',
  [OTA_ERROR_CODES.BACKUP_CORRUPTED]: '备份文件已损坏',
  [OTA_ERROR_CODES.TEMPORARY_ERROR]: '临时错误',
  [OTA_ERROR_CODES.RATE_LIMITED]: '请求频率受限',
} as const;

/**
 * 平台特定配置
 */
export const PLATFORM_CONFIGS = {
  windows: {
    fileExtensions: ['.exe', '.msi'],
    installCommand: 'start',
    restartRequired: true,
  },
  macos: {
    fileExtensions: ['.dmg', '.pkg'],
    installCommand: 'open',
    restartRequired: true,
  },
  linux: {
    fileExtensions: ['.deb', '.rpm', '.appimage'],
    installCommand: 'xdg-open',
    restartRequired: false,
  },
} as const;

/**
 * 通知类型
 */
export const NOTIFICATION_TYPES = {
  UPDATE_AVAILABLE: 'update_available',
  DOWNLOAD_STARTED: 'download_started',
  DOWNLOAD_PROGRESS: 'download_progress',
  DOWNLOAD_COMPLETED: 'download_completed',
  DOWNLOAD_FAILED: 'download_failed',
  INSTALL_STARTED: 'install_started',
  INSTALL_COMPLETED: 'install_completed',
  INSTALL_FAILED: 'install_failed',
} as const;

/**
 * 存储键名
 */
export const STORAGE_KEYS = {
  LAST_CHECK_TIME: 'ota_last_check_time',
  UPDATE_CONFIG: 'ota_update_config',
  DOWNLOAD_CACHE: 'ota_download_cache',
  UPDATE_HISTORY: 'ota_update_history',
  USER_PREFERENCES: 'ota_user_preferences',
} as const;

/**
 * 类型定义
 */
export type DownloadStatus = typeof DOWNLOAD_STATUS[keyof typeof DOWNLOAD_STATUS];
export type UpdateStatus = typeof UPDATE_STATUS[keyof typeof UPDATE_STATUS];
export type OTAErrorCode = typeof OTA_ERROR_CODES[keyof typeof OTA_ERROR_CODES];
export type NotificationType = typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];
export type ChecksumAlgorithm = typeof CHECKSUM_ALGORITHMS[keyof typeof CHECKSUM_ALGORITHMS];
