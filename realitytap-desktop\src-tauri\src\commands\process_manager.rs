/**
 * 进程管理命令
 * 处理RealityTap应用进程的检测、关闭和管理
 */
use crate::error::{<PERSON>rro<PERSON>, Result};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};
use sysinfo::{Pid, System};
use tokio::time::sleep;

/// 进程信息
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct ProcessInfo {
    pub pid: u32,
    pub name: String,
    pub description: String,
    pub process_type: String,
    pub priority: u32,
    pub is_critical: bool,
    pub executable_path: Option<String>,
    pub memory_usage: u64,
    pub cpu_usage: f32,
}

/// 进程关闭结果
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct ProcessCloseResult {
    pub pid: u32,
    pub name: String,
    pub success: bool,
    pub error_message: Option<String>,
    pub close_method: String,
    pub close_duration_ms: u64,
}

/// 进程关闭选项
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct ProcessCloseOptions {
    pub graceful_timeout: u64, // 优雅关闭超时时间（秒）
    pub force_close: bool,     // 是否强制关闭
    pub wait_for_exit: bool,   // 是否等待进程退出
}



impl Default for ProcessCloseOptions {
    fn default() -> Self {
        Self {
            graceful_timeout: 10,
            force_close: false,
            wait_for_exit: true,
        }
    }
}

/// 获取所有RealityTap相关进程
#[tauri::command]
pub async fn list_realitytap_processes() -> Result<Vec<ProcessInfo>> {
    log::info!("🔍 开始检测RealityTap相关进程");

    let mut system = System::new_all();
    system.refresh_all();

    let mut processes = Vec::new();
    let realitytap_names = get_realitytap_process_names();

    for (pid, process) in system.processes() {
        let process_name = process.name().to_lowercase();

        // 检查是否是RealityTap相关进程
        if realitytap_names
            .iter()
            .any(|name| process_name.contains(name))
        {
            let process_info = ProcessInfo {
                pid: pid.as_u32(),
                name: process.name().to_string(),
                description: get_process_description(&process_name),
                process_type: get_process_type(&process_name),
                priority: get_process_priority(&process_name),
                is_critical: is_critical_process(&process_name),
                executable_path: process.exe().map(|p| p.to_string_lossy().to_string()),
                memory_usage: process.memory(),
                cpu_usage: process.cpu_usage(),
            };

            processes.push(process_info);
            log::info!("📋 发现进程: {} (PID: {})", process.name(), pid);
        }
    }

    // 按优先级排序（优先级高的先关闭）
    processes.sort_by(|a, b| b.priority.cmp(&a.priority));

    log::info!("✅ 检测完成，共发现 {} 个RealityTap进程", processes.len());
    Ok(processes)
}

/// 优雅关闭进程
#[tauri::command]
pub async fn close_process_gracefully(
    pid: u32,
    timeout_seconds: Option<u64>,
) -> Result<ProcessCloseResult> {
    let timeout = timeout_seconds.unwrap_or(10);
    let start_time = Instant::now();

    log::info!("🔄 开始优雅关闭进程 PID: {}", pid);

    let mut system = System::new_all();
    system.refresh_all();

    let process_name = system
        .process(Pid::from(pid as usize))
        .map(|p| p.name().to_string())
        .unwrap_or_else(|| "Unknown".to_string());

    // 尝试优雅关闭
    let close_result = close_process_graceful_impl(pid).await;

    match close_result {
        Ok(_) => {
            log::info!("🔧 taskkill 命令执行成功，开始等待进程退出...");
            // 等待进程退出
            let mut attempts = 0;
            let max_attempts = timeout * 10; // 每100ms检查一次

            while attempts < max_attempts {
                system.refresh_all();
                if system.process(Pid::from(pid as usize)).is_none() {
                    let duration = start_time.elapsed().as_millis() as u64;
                    log::info!(
                        "✅ 进程 {} 优雅关闭成功，耗时: {}ms",
                        process_name,
                        duration
                    );

                    return Ok(ProcessCloseResult {
                        pid,
                        name: process_name,
                        success: true,
                        error_message: None,
                        close_method: "Graceful".to_string(),
                        close_duration_ms: duration,
                    });
                }

                // 每2秒输出一次等待日志
                if attempts % 20 == 0 && attempts > 0 {
                    log::info!("⏳ 仍在等待进程 {} 退出... ({}/{}秒)", process_name, attempts / 10, timeout);
                }

                sleep(Duration::from_millis(100)).await;
                attempts += 1;
            }

            // 超时
            let duration = start_time.elapsed().as_millis() as u64;
            log::warn!("⚠️ 进程 {} 优雅关闭超时", process_name);

            Ok(ProcessCloseResult {
                pid,
                name: process_name,
                success: false,
                error_message: Some("Graceful close timeout".to_string()),
                close_method: "Graceful".to_string(),
                close_duration_ms: duration,
            })
        }
        Err(e) => {
            let duration = start_time.elapsed().as_millis() as u64;
            log::error!("❌ 进程 {} 优雅关闭失败: {}", process_name, e);

            Ok(ProcessCloseResult {
                pid,
                name: process_name,
                success: false,
                error_message: Some(e.to_string()),
                close_method: "Graceful".to_string(),
                close_duration_ms: duration,
            })
        }
    }
}

/// 强制关闭进程
#[tauri::command]
pub async fn force_close_process(pid: u32) -> Result<ProcessCloseResult> {
    let start_time = Instant::now();

    log::info!("💥 开始强制关闭进程 PID: {}", pid);

    let mut system = System::new_all();
    system.refresh_all();

    let process_name = system
        .process(Pid::from(pid as usize))
        .map(|p| p.name().to_string())
        .unwrap_or_else(|| "Unknown".to_string());

    let close_result = close_process_force_impl(pid).await;

    let duration = start_time.elapsed().as_millis() as u64;

    match close_result {
        Ok(_) => {
            log::info!(
                "✅ 进程 {} 强制关闭成功，耗时: {}ms",
                process_name,
                duration
            );

            Ok(ProcessCloseResult {
                pid,
                name: process_name,
                success: true,
                error_message: None,
                close_method: "Forced".to_string(),
                close_duration_ms: duration,
            })
        }
        Err(e) => {
            log::error!("❌ 进程 {} 强制关闭失败: {}", process_name, e);

            Ok(ProcessCloseResult {
                pid,
                name: process_name,
                success: false,
                error_message: Some(e.to_string()),
                close_method: "Forced".to_string(),
                close_duration_ms: duration,
            })
        }
    }
}

/// 等待所有进程退出
#[tauri::command]
pub async fn wait_for_processes_exit(pids: Vec<u32>, timeout_seconds: Option<u64>) -> Result<bool> {
    let timeout = timeout_seconds.unwrap_or(30);
    let start_time = Instant::now();

    log::info!("⏳ 等待 {} 个进程退出，超时: {}秒", pids.len(), timeout);

    let mut system = System::new_all();
    let mut remaining_pids = pids;

    while !remaining_pids.is_empty() && start_time.elapsed().as_secs() < timeout {
        system.refresh_all();

        remaining_pids.retain(|&pid| {
            let exists = system.process(Pid::from(pid as usize)).is_some();
            if !exists {
                log::info!("✅ 进程 PID {} 已退出", pid);
            }
            exists
        });

        if !remaining_pids.is_empty() {
            sleep(Duration::from_millis(500)).await;
        }
    }

    let all_exited = remaining_pids.is_empty();
    let elapsed = start_time.elapsed().as_secs();

    if all_exited {
        log::info!("✅ 所有进程已退出，耗时: {}秒", elapsed);
    } else {
        log::warn!("⚠️ 仍有 {} 个进程未退出，已超时", remaining_pids.len());
    }

    Ok(all_exited)
}

// === 辅助函数 ===

/// 获取RealityTap相关进程名称列表
fn get_realitytap_process_names() -> Vec<&'static str> {
    vec![
        "realitytap",
        "realitytap_studio",
        "realitytap-studio",
        "realitytap_haptics_studio",
        "realitytap-haptics-studio",
    ]
}

/// 获取进程描述
fn get_process_description(process_name: &str) -> String {
    match process_name {
        name if name.contains("studio") => "RealityTap Haptics Studio 主程序".to_string(),
        name if name.contains("editor") => "RealityTap 编辑器窗口".to_string(),
        name if name.contains("render") => "RealityTap 渲染进程".to_string(),
        name if name.contains("audio") => "RealityTap 音频服务".to_string(),
        name if name.contains("monitor") => "RealityTap 文件监控服务".to_string(),
        _ => "RealityTap 相关进程".to_string(),
    }
}

/// 获取进程类型
fn get_process_type(process_name: &str) -> String {
    match process_name {
        name if name.contains("studio") => "MainApplication".to_string(),
        name if name.contains("editor") => "EditorWindow".to_string(),
        name if name.contains("render") => "RenderProcess".to_string(),
        name if name.contains("audio") => "AudioService".to_string(),
        name if name.contains("monitor") => "FileMonitor".to_string(),
        _ => "BackgroundService".to_string(),
    }
}

/// 获取进程优先级（数字越大优先级越高，越先关闭）
fn get_process_priority(process_name: &str) -> u32 {
    match process_name {
        name if name.contains("studio") => 100, // 主程序最后关闭
        name if name.contains("editor") => 90,
        name if name.contains("render") => 80,
        name if name.contains("audio") => 70,
        name if name.contains("monitor") => 60,
        _ => 50,
    }
}

/// 判断是否为关键进程
fn is_critical_process(process_name: &str) -> bool {
    process_name.contains("studio") || process_name.contains("main")
}

// === 平台特定的进程关闭实现 ===

#[cfg(target_os = "windows")]
async fn close_process_windows_graceful(pid: u32) -> Result<()> {
    use std::process::Command;
    use tokio::time::{timeout, Duration};

    log::info!("🔧 开始执行 taskkill 命令关闭进程 PID: {}", pid);

    // 添加超时机制，防止 taskkill 命令卡住
    let taskkill_future = async {
        log::info!("🔧 正在执行: taskkill /PID {}", pid);
        let result = Command::new("taskkill")
            .args(&["/PID", &pid.to_string()])
            .output()
            .map_err(|e| Error::Io(format!("Failed to execute taskkill: {}", e)));
        log::info!("🔧 taskkill 命令执行完毕");
        result
    };

    log::info!("⏳ 等待 taskkill 命令完成（最多30秒）...");
    let output = timeout(Duration::from_secs(30), taskkill_future)
        .await
        .map_err(|_| {
            log::error!("⏰ taskkill 命令超时（30秒）");
            Error::Io("Taskkill command timeout after 30 seconds".to_string())
        })??;

    log::info!("📋 taskkill 命令执行完成，退出码: {:?}", output.status.code());

    if output.status.success() {
        log::info!("✅ taskkill 命令执行成功");
        Ok(())
    } else {
        let error_msg = String::from_utf8_lossy(&output.stderr);
        let stdout_msg = String::from_utf8_lossy(&output.stdout);
        log::error!("❌ taskkill 命令失败 - stderr: {}, stdout: {}", error_msg, stdout_msg);
        Err(Error::Io(format!("Taskkill failed: {}", error_msg)))
    }
}

#[cfg(target_os = "windows")]
async fn close_process_windows_force(pid: u32) -> Result<()> {
    use std::process::Command;

    let output = Command::new("taskkill")
        .args(&["/F", "/PID", &pid.to_string()])
        .output()
        .map_err(|e| Error::Io(format!("Failed to execute taskkill: {}", e)))?;

    if output.status.success() {
        Ok(())
    } else {
        let error_msg = String::from_utf8_lossy(&output.stderr);
        Err(Error::Io(format!("Taskkill failed: {}", error_msg)))
    }
}

#[cfg(not(target_os = "windows"))]
async fn close_process_unix_graceful(pid: u32) -> Result<()> {
    use std::process::Command;

    let output = Command::new("kill")
        .args(&["-TERM", &pid.to_string()])
        .output()
        .map_err(|e| Error::Io(format!("Failed to execute kill: {}", e)))?;

    if output.status.success() {
        Ok(())
    } else {
        let error_msg = String::from_utf8_lossy(&output.stderr);
        Err(Error::Io(format!("Kill failed: {}", error_msg)))
    }
}

#[cfg(not(target_os = "windows"))]
async fn close_process_unix_force(pid: u32) -> Result<()> {
    use std::process::Command;

    let output = Command::new("kill")
        .args(&["-KILL", &pid.to_string()])
        .output()
        .map_err(|e| Error::Io(format!("Failed to execute kill: {}", e)))?;

    if output.status.success() {
        Ok(())
    } else {
        let error_msg = String::from_utf8_lossy(&output.stderr);
        Err(Error::Io(format!("Kill failed: {}", error_msg)))
    }
}

// === 统一的跨平台实现 ===

/// 跨平台的优雅关闭进程实现
async fn close_process_graceful_impl(pid: u32) -> Result<()> {
    #[cfg(target_os = "windows")]
    {
        close_process_windows_graceful(pid).await
    }

    #[cfg(not(target_os = "windows"))]
    {
        close_process_unix_graceful(pid).await
    }
}

/// 跨平台的强制关闭进程实现
async fn close_process_force_impl(pid: u32) -> Result<()> {
    #[cfg(target_os = "windows")]
    {
        close_process_windows_force(pid).await
    }

    #[cfg(not(target_os = "windows"))]
    {
        close_process_unix_force(pid).await
    }
}





/// 获取当前应用的PID
#[tauri::command]
pub async fn get_current_app_pid() -> Result<u32> {
    let pid = std::process::id();
    log::info!("📋 当前应用PID: {}", pid);
    Ok(pid)
}



/// 优雅退出应用程序
#[tauri::command]
pub async fn exit_application_gracefully() -> Result<()> {
    log::info!("🔄 开始优雅退出应用程序");

    // 保存当前状态
    log::info!("💾 保存应用状态");

    // 清理资源
    log::info!("🧹 清理应用资源");

    // 延迟一点时间确保日志写入
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

    log::info!("✅ 应用程序优雅退出完成");
    std::process::exit(0);
}

/// 检查进程是否存在
#[tauri::command]
pub async fn is_process_running(pid: u32) -> Result<bool> {
    let mut system = System::new_all();
    system.refresh_all();

    let exists = system.process(Pid::from(pid as usize)).is_some();
    Ok(exists)
}

/// 获取进程详细信息
#[tauri::command]
pub async fn get_process_info(pid: u32) -> Result<Option<ProcessInfo>> {
    let mut system = System::new_all();
    system.refresh_all();

    if let Some(process) = system.process(Pid::from(pid as usize)) {
        let process_name = process.name().to_lowercase();

        let process_info = ProcessInfo {
            pid,
            name: process.name().to_string(),
            description: get_process_description(&process_name),
            process_type: get_process_type(&process_name),
            priority: get_process_priority(&process_name),
            is_critical: is_critical_process(&process_name),
            executable_path: process.exe().map(|p| p.to_string_lossy().to_string()),
            memory_usage: process.memory(),
            cpu_usage: process.cpu_usage(),
        };

        Ok(Some(process_info))
    } else {
        Ok(None)
    }
}

/// 批量关闭进程
#[tauri::command]
pub async fn close_processes_batch(
    pids: Vec<u32>,
    options: ProcessCloseOptions,
) -> Result<Vec<ProcessCloseResult>> {
    log::info!("🔄 开始批量关闭 {} 个进程", pids.len());

    let mut results = Vec::new();

    for pid in pids {
        let result = if options.force_close {
            force_close_process(pid).await?
        } else {
            close_process_gracefully(pid, Some(options.graceful_timeout)).await?
        };

        results.push(result);

        // 如果需要等待进程退出，稍微延迟一下
        if options.wait_for_exit {
            sleep(Duration::from_millis(100)).await;
        }
    }

    log::info!(
        "✅ 批量关闭完成，成功: {}, 失败: {}",
        results.iter().filter(|r| r.success).count(),
        results.iter().filter(|r| !r.success).count()
    );

    Ok(results)
}
