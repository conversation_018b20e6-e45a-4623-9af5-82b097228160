import { v4 as uuidv4 } from "uuid";
import type {
  RealityTapEffect,
  RealityTapEffectV1,
  RealityTapEffectV2,
  EventV1,
  EventV2,
  CurvePoint,
} from "@/types/haptic-file";
import { getTransientDuration, getTotalDuration } from "@/types/haptic-file";
import type {
  RenderableEvent,
  RenderableTransientEvent,
  RenderableContinuousEvent,
  RenderableContinuousCurvePoint,
} from "@/types/haptic-editor";

// 从WaveformEditor.vue提取的主函数，将原始RealityTapEffect转换为渲染格式
export function flattenRealityTapEffect(effectData: RealityTapEffect | null): {
  events: RenderableEvent[];
  duration: number;
} {
  if (!effectData) {
    return { events: [], duration: 0 };
  }

  const renderableEvents: RenderableEvent[] = [];

  if (effectData.Metadata.Version === 1) {
    const v1Effect = effectData as RealityTapEffectV1;
    v1Effect.Pattern.forEach((wrappedEvent) =>
      processEvent(wrappedEvent.Event, 0, renderableEvents)
    );
  } else if (effectData.Metadata.Version === 2) {
    const v2Effect = effectData as RealityTapEffectV2;
    v2Effect.PatternList.forEach(
      (item: { AbsoluteTime: number; Pattern: EventV2[] }) => {
        item.Pattern.forEach((event: EventV2) =>
          processEvent(event, item.AbsoluteTime, renderableEvents)
        );
      }
    );
  }

  // 对事件按开始时间排序
  renderableEvents.sort((a, b) => a.startTime - b.startTime);

  // 优先使用保存的总时长，如果没有则使用计算的时长
  let duration = getTotalDuration(effectData);
  if (effectData.Metadata.TotalDuration && effectData.Metadata.TotalDuration > 0) {
    // 使用保存的总时长，但确保不小于事件的实际时长
    const savedDuration = effectData.Metadata.TotalDuration;
    duration = Math.max(savedDuration, duration);
    console.log(`[haptic-event-processor] 使用保存的总时长: ${savedDuration}ms, 事件计算时长: ${getTotalDuration(effectData)}ms, 最终时长: ${duration}ms`);
  } else {
    console.log(`[haptic-event-processor] 使用事件计算的总时长: ${duration}ms`);
  }

  return { events: renderableEvents, duration };
}

// 处理单个事件，从flattenRealityTapEffect中提取并重构
export function processEvent(
  event: EventV1 | EventV2,
  absoluteStartTimeOffset: number = 0,
  renderableEvents: RenderableEvent[] = []
): RenderableEvent {
  const id = uuidv4();
  const eventStartTime = Math.max(
    0,
    absoluteStartTimeOffset + event.RelativeTime
  );
  let renderedEvent: RenderableEvent;

  if (event.Type === "transient") {
    const width = getTransientDuration(event.Parameters.Frequency);
    const peakTime = Math.floor(eventStartTime + width / 2);
    const stopTime = Math.floor(eventStartTime + width);
    const transientRender: RenderableTransientEvent = {
      type: "transient",
      id,
      peakTime: peakTime,
      stopTime: stopTime,
      startTime: eventStartTime,
      intensity: event.Parameters.Intensity,
      frequency: event.Parameters.Frequency,
      width,
    };
    renderedEvent = transientRender;
  } else if (event.Type === "continuous") {
    const startTime = Math.floor(eventStartTime);
    // Calculate actualDuration based on the last curve point's Time
    let actualDuration = 0;
    if (event.Parameters.Curve && event.Parameters.Curve.length > 0) {
      actualDuration =
        event.Parameters.Curve[event.Parameters.Curve.length - 1].Time;
    } else {
      // Fallback or warning if curve is empty or invalid
      // For now, using 0, but consider event.Duration or a warning
      console.warn(
        "Continuous event has no curve points or invalid curve, defaulting duration to 0 or from Event.Duration if preferable.",
        event
      );
      // actualDuration = event.Duration; // Alternative: use original duration if curve is empty
    }
    const endTime = Math.floor(eventStartTime + actualDuration);
    const originalEventDuration = event.Duration;

    const curvePoints: RenderableContinuousCurvePoint[] =
      event.Parameters.Curve.map((p: CurvePoint, index: number) => {
        const isFirstOrLastPoint =
          index === 0 || index === event.Parameters.Curve.length - 1;
        return {
          timeOffset: p.Time,
          // CurvePoint中的Intensity为0-1，乘以事件Intensity (0-100)
          // 对首尾点，强制 drawIntensity 为 0
          drawIntensity: isFirstOrLastPoint
            ? 0
            : p.Intensity * event.Parameters.Intensity,
          // 确保 rawIntensity 保持两位小数精度
          rawIntensity: Number(p.Intensity.toFixed(2)),
          // CurvePoint中的Frequency为-100-100，直接赋值给relativeCurveFrequency
          relativeCurveFrequency: p.Frequency,
          // curveFrequency (绝对频率) = relativeCurveFrequency + 事件全局频率
          curveFrequency: p.Frequency + event.Parameters.Frequency,
        };
      });

    // 确保curvePoints至少有4个元素
    if (curvePoints.length >= 4 || event.Parameters.Curve?.length > 0) {
      // Allow processing if original curve has points even if mapped is less than 4 for some reason
      const continuousRender: RenderableContinuousEvent = {
        type: "continuous",
        id,
        startTime,
        stopTime: endTime,
        duration: actualDuration,
        originalEventDuration: originalEventDuration,
        eventIntensity: event.Parameters.Intensity,
        eventFrequency: event.Parameters.Frequency,
        curves: curvePoints,
      };
      renderedEvent = continuousRender;
    } else {
      console.warn(
        "Continuous event curve has less than 4 points and original curve is also empty/short:",
        event
      );
      // 返回一个默认的连续事件以避免空返回
      const defaultContinuousRender: RenderableContinuousEvent = {
        type: "continuous",
        id,
        startTime,
        stopTime: endTime, // Uses calculated endTime from actualDuration
        duration: actualDuration, // Uses calculated actualDuration
        originalEventDuration: originalEventDuration,
        eventIntensity: event.Parameters.Intensity,
        eventFrequency: event.Parameters.Frequency,
        curves: [], // 空曲线点
      };
      renderedEvent = defaultContinuousRender;
    }
  } else {
    throw new Error(`未知的事件类型: ${(event as any).Type}`);
  }

  // 如果提供了数组，则添加到数组中
  if (renderableEvents) {
    renderableEvents.push(renderedEvent);
  }

  return renderedEvent;
}

// 新增：接收已经存在的RenderableEvent和更新的基本属性，重新计算派生属性
export function reprocessRenderableEvent(
  existingEvent: RenderableEvent,
  updatedProperties: any
): RenderableEvent {
  // 克隆现有事件，以免直接修改原对象
  const clonedEvent = { ...existingEvent };

  if (clonedEvent.type === "transient") {
    return reprocessTransientEvent(
      clonedEvent as RenderableTransientEvent,
      updatedProperties
    );
  } else if (clonedEvent.type === "continuous") {
    return reprocessContinuousEvent(
      clonedEvent as RenderableContinuousEvent,
      updatedProperties
    );
  }

  // 默认返回克隆的事件（不应该到达这里）
  return clonedEvent;
}

// 处理瞬态事件的更新
function reprocessTransientEvent(
  existingEvent: RenderableTransientEvent,
  updatedProperties: any
): RenderableTransientEvent {
  const event = { ...existingEvent };
  let needsRecalculation = false;

  // 更新RelativeTime（影响peakTime, startTime, endTime）
  if (updatedProperties.RelativeTime !== undefined) {
    event.startTime = updatedProperties.RelativeTime;
    needsRecalculation = true;
  }

  // 更新Intensity
  if (updatedProperties.Parameters?.Intensity !== undefined) {
    event.intensity = updatedProperties.Parameters.Intensity;
  }

  // 更新Frequency（影响width, startTime, endTime）
  if (updatedProperties.Parameters?.Frequency !== undefined) {
    event.frequency = updatedProperties.Parameters.Frequency;
    event.width = getTransientDuration(event.frequency);
    needsRecalculation = true;
  }

  // 如果需要重新计算时间相关属性
  if (needsRecalculation) {
    event.peakTime = Math.floor(event.startTime + event.width / 2);
    event.stopTime = Math.floor(event.startTime + event.width);
  }

  return event;
}

// 处理连续事件的更新
function reprocessContinuousEvent(
  existingEvent: RenderableContinuousEvent,
  updatedProperties: any
): RenderableContinuousEvent {
  const event = { ...existingEvent };
  let needsRecalculation = false;

  // 更新RelativeTime（影响startTime, endTime）
  if (updatedProperties.RelativeTime !== undefined) {
    event.startTime = Math.floor(updatedProperties.RelativeTime);
    needsRecalculation = true;
  }

  // 更新Duration（影响endTime 和 curve points）
  if (updatedProperties.Duration !== undefined) {
    const oldEffectiveDuration = existingEvent.duration; // This is the previous curve-based duration
    event.duration = updatedProperties.Duration;
    needsRecalculation = true; // stopTime will be recalculated based on new duration

    // Adjust curve points based on the new duration
    if (event.curves && event.curves.length > 0) {
      const lastCurveIndex = event.curves.length - 1;

      // Ensure first point is always at timeOffset 0
      event.curves[0].timeOffset = 0;

      // Set last point's timeOffset to the new duration
      event.curves[lastCurveIndex].timeOffset = event.duration;

      // Scale intermediate points
      // Only scale if there are actual intermediate points (more than 2 points total)
      if (lastCurveIndex > 1) {
        // Equivalent to event.curves.length > 2
        if (oldEffectiveDuration > 0) {
          for (let i = 1; i < lastCurveIndex; i++) {
            event.curves[i].timeOffset =
              (event.curves[i].timeOffset / oldEffectiveDuration) *
              event.duration;
          }
        } else if (event.duration > 0) {
          // If old duration was 0, distribute intermediate points proportionally within the new duration.
          // This assumes intermediate points had some original relative spacing or default values.
          // A simple equal distribution for points between first and last:
          const numberOfIntermediatePoints = lastCurveIndex - 1;
          const segmentDuration =
            event.duration / (numberOfIntermediatePoints + 1);
          for (let i = 1; i < lastCurveIndex; i++) {
            event.curves[i].timeOffset = segmentDuration * i;
          }
        }
        // If both oldEffectiveDuration and event.duration are 0, intermediate points' timeOffset remain 0.
      }
    }
  }

  // 更新Intensity（影响所有曲线点的curveIntensity）
  if (updatedProperties.Parameters?.Intensity !== undefined) {
    event.eventIntensity = updatedProperties.Parameters.Intensity;

    // 更新所有曲线点的强度
    if (event.curves && event.curves.length > 0) {
      event.curves = event.curves.map((point, index) => {
        const isFirstOrLastPoint =
          index === 0 || index === event.curves.length - 1;
        if (isFirstOrLastPoint) {
          // 首尾点的 curveIntensity 始终为 0
          return { ...point, drawIntensity: 0 };
        }

        return {
          ...point,
          // 根据原始强度比例和新的全局强度重新计算曲线点强度
          drawIntensity: point.rawIntensity * event.eventIntensity,
        } as RenderableContinuousCurvePoint & { rawIntensity: number };
      });
    }
  }

  // 更新Frequency（影响所有曲线点的curveFrequency）
  if (updatedProperties.Parameters?.Frequency !== undefined) {
    event.eventFrequency = updatedProperties.Parameters.Frequency;

    // 更新所有曲线点的绝对频率 curveFrequency
    if (event.curves && event.curves.length > 0) {
      event.curves = event.curves.map((point) => ({
        ...point,
        // 重新计算绝对频率: 该点的相对频率 + 新的事件全局频率
        curveFrequency: point.relativeCurveFrequency + event.eventFrequency,
      }));
    }
  }

  // 如果需要重新计算时间相关属性
  if (needsRecalculation) {
    event.stopTime = Math.floor(event.startTime + event.duration);
    // Note: The logic for scaling curve points based on duration change
    // is now handled directly when updatedProperties.Duration is processed.
    // The previous block that scaled all points if event.duration changed (even if only RelativeTime changed it indirectly)
    // is removed as event.duration is now the primary source of curve length.
  }

  return event;
}

// 计算一组事件的总持续时间
export function calculateTotalDurationFromEvents(
  events: RenderableEvent[]
): number {
  if (!events || events.length === 0) return 0;

  // 找出所有事件中结束时间最晚的
  const lastEndTime = Math.max(...events.map((event) => event.stopTime));
  return lastEndTime;
}
