# RealityTap Desktop Haptic 模块架构图表

本文档包含 RealityTap Desktop haptic 模块的架构图和流程图，使用 Mermaid 图表语法描述。

## 整体架构图

```mermaid
graph TB
    subgraph "TypeScript 前端层"
        A[Vue 组件] --> B[useHapticManager Composable]
        B --> C[HapticApi 封装层]
        C --> D[Tauri invoke 调用]
        C --> E[Tauri 事件监听]
    end
    
    subgraph "Tauri 通信层"
        D --> F[Tauri Commands]
        G[Tauri Events] --> E
    end
    
    subgraph "Rust 后端层"
        F --> H[commands.rs]
        H --> I[HapticChannelManager]
        I --> J[HapticMessageProcessor]
        J --> G
        
        subgraph "消息系统"
            K[message.rs] --> L[channel.rs]
            L --> M[processor.rs]
            M --> N[filter.rs]
            M --> O[aggregation.rs]
        end
        
        subgraph "FFI 层"
            P[ffi.rs] --> Q[HapticOutputHandler]
            Q --> R[handler.rs]
        end
        
        H --> P
        R --> I
    end
    
    subgraph "C++ 核心库层"
        S[librtcore.dll] --> T[IHapticOutputHandler 接口]
        T --> U[回调函数]
        U --> Q
        P --> V[awa_realitytap_* API]
        V --> S
    end
    
    style A fill:#e1f5fe
    style S fill:#fff3e0
    style I fill:#f3e5f5
    style Q fill:#e8f5e8
```

## 消息传递流程图

```mermaid
sequenceDiagram
    participant CPP as librtcore.dll
    participant Handler as HapticOutputHandler
    participant Channel as HapticChannel
    participant Processor as MessageProcessor
    participant Tauri as Tauri Events
    participant TS as TypeScript

    Note over CPP,TS: 触觉播放开始
    CPP->>Handler: on_haptic_output_start()
    Handler->>Channel: send(OutputStart)
    
    Note over CPP,TS: 波形数据处理
    loop 高频数据处理
        CPP->>Handler: process_waveform_sample(sample)
        Handler->>Channel: try_send(WaveformSample)
        Note right of Channel: 队列满时丢弃消息
    end
    
    Note over CPP,TS: 后台消息处理
    Channel->>Processor: receive_batch()
    Processor->>Processor: filter_messages()
    Processor->>Processor: aggregate_data()
    
    Note over CPP,TS: 分类发送事件
    alt 生命周期事件
        Processor->>Tauri: emit("haptic:lifecycle")
        Tauri->>TS: 实时事件
    else 数据事件
        Processor->>Tauri: emit("haptic:data")
        Tauri->>TS: 聚合事件
    end
    
    Note over CPP,TS: 触觉播放完成
    CPP->>Handler: on_haptic_output_complete()
    Handler->>Channel: send(OutputComplete)
    Channel->>Processor: receive()
    Processor->>Tauri: emit("haptic:lifecycle")
    Tauri->>TS: 完成事件
```

## FFI 交互详细流程

```mermaid
sequenceDiagram
    participant App as Tauri App
    participant Rust as Rust Backend
    participant FFI as FFI Layer
    participant CPP as librtcore.dll

    Note over App,CPP: 初始化阶段
    App->>Rust: haptic_init(configs)
    Rust->>FFI: load_library()
    FFI->>CPP: LoadLibrary("librtcore.dll")
    CPP-->>FFI: Library Handle
    FFI->>CPP: GetProcAddress("awa_realitytap_*")
    CPP-->>FFI: Function Pointers
    
    Rust->>FFI: create_handler_vtable()
    FFI->>Rust: HapticOutputHandlerVTable
    Rust->>CPP: awa_realitytap_init(params)
    CPP-->>Rust: Success/Error Code
    
    Note over App,CPP: 播放阶段
    App->>Rust: haptic_play(data)
    Rust->>CPP: awa_realitytap_append_haptics()
    
    Note over App,CPP: 回调阶段
    CPP->>FFI: on_haptic_output_start_c()
    FFI->>Rust: handler.on_haptic_output_start()
    Rust->>Rust: send_message(OutputStart)
    
    loop 高频回调
        CPP->>FFI: process_waveform_sample_c(sample)
        FFI->>Rust: handler.process_waveform_sample()
        Rust->>Rust: try_send_message(WaveformSample)
    end
    
    CPP->>FFI: on_haptic_output_complete_c()
    FFI->>Rust: handler.on_haptic_output_complete()
    Rust->>Rust: send_message(OutputComplete)
```

## 消息处理器工作流程

```mermaid
flowchart TD
    A[消息接收] --> B{消息类型判断}
    
    B -->|生命周期事件| C[高优先级处理]
    B -->|数据事件| D[低优先级处理]
    B -->|错误事件| E[错误处理]
    
    C --> F[实时发送到前端]
    
    D --> G[添加到聚合器]
    G --> H{达到聚合条件?}
    H -->|是| I[批量发送到前端]
    H -->|否| J[继续聚合]
    
    E --> K[错误日志记录]
    K --> L[发送错误事件]
    
    F --> M[更新统计信息]
    I --> M
    L --> M
    
    M --> N[检查停止信号]
    N -->|继续| A
    N -->|停止| O[清理资源]
```

## 内存安全机制

```mermaid
graph LR
    subgraph "Handler 生命周期管理"
        A[创建 Handler] --> B[注册指针]
        B --> C[VALID_HANDLERS.insert]
        C --> D[Handler 使用中]
        D --> E[销毁 Handler]
        E --> F[注销指针]
        F --> G[VALID_HANDLERS.remove]
    end
    
    subgraph "FFI 回调安全检查"
        H[C++ 回调] --> I[检查指针有效性]
        I --> J{指针是否有效?}
        J -->|是| K[执行回调]
        J -->|否| L[忽略回调]
        K --> M[catch_unwind]
        M --> N{发生 Panic?}
        N -->|是| O[记录错误]
        N -->|否| P[正常返回]
    end
    
    C -.-> I
    G -.-> I
```

## 事件分类和处理策略

```mermaid
graph TD
    A[HapticMessage] --> B{消息分类}
    
    B -->|生命周期| C[OutputStart<br/>OutputComplete<br/>OutputStop]
    B -->|数据| D[WaveformSample<br/>ChunkStart]
    B -->|状态| E[AmplitudeChange<br/>ProcessingStatus]
    B -->|错误| F[ProcessorError]
    
    C --> G[高优先级<br/>实时发送]
    D --> H[低优先级<br/>聚合发送]
    E --> I[中优先级<br/>批量发送]
    F --> J[高优先级<br/>实时发送]
    
    G --> K[haptic:lifecycle]
    H --> L[haptic:data]
    I --> M[haptic:status]
    J --> N[haptic:error]
    
    K --> O[TypeScript 前端]
    L --> O
    M --> O
    N --> O
```

## 性能优化架构

```mermaid
graph TB
    subgraph "消息生产端"
        A[C++ 回调] --> B[Handler]
        B --> C{消息优先级}
        C -->|高| D[直接发送]
        C -->|低| E[try_send]
        E -->|成功| F[进入队列]
        E -->|失败| G[丢弃消息]
    end
    
    subgraph "消息队列"
        F --> H[有界队列<br/>容量: 10000]
        H --> I[批量读取]
    end
    
    subgraph "消息处理端"
        I --> J[消息过滤]
        J --> K[消息聚合]
        K --> L{聚合条件}
        L -->|时间窗口| M[定时发送]
        L -->|数量阈值| N[批量发送]
        L -->|优先级| O[实时发送]
    end
    
    subgraph "前端接收"
        M --> P[Tauri Events]
        N --> P
        O --> P
        P --> Q[TypeScript 监听器]
    end
```

## 图表说明

### 架构图说明
- **蓝色**：TypeScript 前端层
- **橙色**：C++ 核心库层  
- **紫色**：消息系统核心
- **绿色**：FFI 交互层

### 流程图说明
- 展示了从 C++ 回调到 TypeScript 前端的完整消息传递流程
- 突出了高频数据处理和消息聚合机制
- 说明了不同类型事件的处理策略

### 性能优化说明
- 有界队列防止内存溢出
- 消息丢弃策略处理高频数据
- 分级处理确保重要事件的实时性
- 聚合机制减少前端事件频率

这些图表清晰地展示了 RealityTap Desktop haptic 模块的架构设计和工作原理，有助于理解系统的复杂性和优化策略。
