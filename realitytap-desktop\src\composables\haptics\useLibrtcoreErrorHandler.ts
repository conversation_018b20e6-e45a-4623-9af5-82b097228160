/**
 * librtcore 错误处理和恢复机制
 * 提供分级错误处理、自动重试和降级模式
 */

import { computed, reactive, type ComputedRef } from "vue";
import { useMessage } from "naive-ui";
import { useLibrtcoreGlobalManager, LibrtcoreEvent, ErrorLevel } from "./useLibrtcoreGlobalManager";
import { LIBRTCORE_CONFIG } from "./useLibrtcoreGlobalConfig";
import { logger, LogModule } from "@/utils/logger/logger";
import { useI18n } from "@/composables/useI18n";

/**
 * 错误恢复策略枚举
 */
export enum RecoveryStrategy {
  NONE = "none",           // 不进行恢复
  RETRY = "retry",         // 自动重试
  MANUAL_RETRY = "manual_retry", // 手动重试
  FALLBACK = "fallback",   // 降级模式
  RESET = "reset",         // 重置系统
}

/**
 * 错误处理配置接口
 */
export interface ErrorHandlerConfig {
  /** 是否启用自动重试 */
  enableAutoRetry: boolean;
  /** 最大自动重试次数 */
  maxAutoRetries: number;
  /** 重试延迟时间（毫秒） */
  retryDelay: number;
  /** 是否显示用户通知 */
  showUserNotifications: boolean;
  /** 是否启用降级模式 */
  enableFallbackMode: boolean;
}

/**
 * 错误恢复状态接口
 */
export interface ErrorRecoveryState {
  /** 当前重试次数 */
  currentRetryCount: number;
  /** 是否正在恢复中 */
  isRecovering: boolean;
  /** 最后一次错误时间 */
  lastErrorTime: number | null;
  /** 恢复策略 */
  recoveryStrategy: RecoveryStrategy;
  /** 是否处于降级模式 */
  isFallbackMode: boolean;
}

/**
 * 错误处理器返回接口
 */
export interface UseLibrtcoreErrorHandlerReturn {
  // 状态
  config: ErrorHandlerConfig;
  recoveryState: ErrorRecoveryState;
  
  // 计算属性
  canAutoRetry: ComputedRef<boolean>;
  shouldShowRetryButton: ComputedRef<boolean>;
  isInFallbackMode: ComputedRef<boolean>;
  
  // 方法
  handleError: (error: any, context?: string) => Promise<void>;
  manualRetry: () => Promise<boolean>;
  resetErrorState: () => void;
  enterFallbackMode: () => void;
  exitFallbackMode: () => void;
  updateConfig: (newConfig: Partial<ErrorHandlerConfig>) => void;
}

/**
 * 默认错误处理配置
 */
const DEFAULT_ERROR_CONFIG: ErrorHandlerConfig = {
  enableAutoRetry: true,
  maxAutoRetries: LIBRTCORE_CONFIG.MAX_RETRY_COUNT,
  retryDelay: LIBRTCORE_CONFIG.RETRY_DELAY,
  showUserNotifications: true,
  enableFallbackMode: true,
};

/**
 * librtcore 错误处理器
 */
export function useLibrtcoreErrorHandler(
  initialConfig: Partial<ErrorHandlerConfig> = {}
): UseLibrtcoreErrorHandlerReturn {
  const message = useMessage();
  const { t } = useI18n();
  const librtcoreGlobalManager = useLibrtcoreGlobalManager();

  // 配置状态
  const config = reactive<ErrorHandlerConfig>({
    ...DEFAULT_ERROR_CONFIG,
    ...initialConfig,
  });

  // 恢复状态
  const recoveryState = reactive<ErrorRecoveryState>({
    currentRetryCount: 0,
    isRecovering: false,
    lastErrorTime: null,
    recoveryStrategy: RecoveryStrategy.NONE,
    isFallbackMode: false,
  });

  // 计算属性
  const canAutoRetry = computed(() => 
    config.enableAutoRetry && 
    recoveryState.currentRetryCount < config.maxAutoRetries &&
    !recoveryState.isRecovering
  );

  const shouldShowRetryButton = computed(() => 
    !canAutoRetry.value && 
    recoveryState.currentRetryCount < config.maxAutoRetries * 2 // 允许更多手动重试
  );

  const isInFallbackMode = computed(() => recoveryState.isFallbackMode);

  /**
   * 确定错误恢复策略
   */
  const determineRecoveryStrategy = (error: any, retryCount: number): RecoveryStrategy => {
    // 如果已经达到最大重试次数
    if (retryCount >= config.maxAutoRetries) {
      if (config.enableFallbackMode) {
        return RecoveryStrategy.FALLBACK;
      }
      return RecoveryStrategy.MANUAL_RETRY;
    }

    // 根据错误类型决定策略
    const errorMessage = error?.message || String(error);
    
    // 网络相关错误 - 适合重试
    if (errorMessage.includes('network') || 
        errorMessage.includes('timeout') || 
        errorMessage.includes('connection')) {
      return RecoveryStrategy.RETRY;
    }

    // 配置相关错误 - 需要手动处理
    if (errorMessage.includes('config') || 
        errorMessage.includes('invalid') ||
        errorMessage.includes('not found')) {
      return RecoveryStrategy.MANUAL_RETRY;
    }

    // 系统资源错误 - 可能需要重置
    if (errorMessage.includes('memory') || 
        errorMessage.includes('resource') ||
        errorMessage.includes('busy')) {
      return retryCount > 1 ? RecoveryStrategy.RESET : RecoveryStrategy.RETRY;
    }

    // 默认策略
    return RecoveryStrategy.RETRY;
  };

  /**
   * 显示用户通知
   */
  const showUserNotification = (error: any, strategy: RecoveryStrategy) => {
    if (!config.showUserNotifications) return;

    const errorMessage = error?.message || String(error);

    switch (strategy) {
      case RecoveryStrategy.RETRY:
        message.warning(t("librtcore.error.retrying", { message: errorMessage }));
        break;
      case RecoveryStrategy.MANUAL_RETRY:
        message.error(t("librtcore.error.manualRetryRequired", { message: errorMessage }));
        break;
      case RecoveryStrategy.FALLBACK:
        message.warning(t("librtcore.error.fallbackMode"));
        break;
      case RecoveryStrategy.RESET:
        message.info(t("librtcore.error.resetting"));
        break;
      default:
        message.error(t("librtcore.error.general", { message: errorMessage }));
    }
  };

  /**
   * 执行自动重试
   */
  const performAutoRetry = async (): Promise<boolean> => {
    if (!canAutoRetry.value) return false;

    recoveryState.isRecovering = true;
    recoveryState.recoveryStrategy = RecoveryStrategy.RETRY;

    try {
      logger.info(LogModule.GENERAL, `librtcore 错误处理器：开始第 ${recoveryState.currentRetryCount + 1} 次自动重试`);

      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, config.retryDelay));

      // 执行重试
      const success = await librtcoreGlobalManager.retry();
      
      if (success) {
        logger.info(LogModule.GENERAL, "librtcore 错误处理器：自动重试成功");
        resetErrorState();
        return true;
      } else {
        recoveryState.currentRetryCount++;
        logger.warn(LogModule.GENERAL, `librtcore 错误处理器：第 ${recoveryState.currentRetryCount} 次自动重试失败`);
        return false;
      }
    } catch (error) {
      recoveryState.currentRetryCount++;
      logger.error(LogModule.GENERAL, "librtcore 错误处理器：自动重试过程中出现错误", error);
      return false;
    } finally {
      recoveryState.isRecovering = false;
    }
  };

  /**
   * 进入降级模式
   */
  const enterFallbackMode = () => {
    recoveryState.isFallbackMode = true;
    recoveryState.recoveryStrategy = RecoveryStrategy.FALLBACK;
    
    logger.info(LogModule.GENERAL, "librtcore 错误处理器：进入降级模式");
    
    if (config.showUserNotifications) {
      message.warning(t("librtcore.error.fallbackModeActive"));
    }
  };

  /**
   * 退出降级模式
   */
  const exitFallbackMode = () => {
    recoveryState.isFallbackMode = false;
    resetErrorState();
    
    logger.info(LogModule.GENERAL, "librtcore 错误处理器：退出降级模式");
    
    if (config.showUserNotifications) {
      message.success(t("librtcore.error.fallbackModeExited"));
    }
  };

  /**
   * 重置错误状态
   */
  const resetErrorState = () => {
    recoveryState.currentRetryCount = 0;
    recoveryState.isRecovering = false;
    recoveryState.lastErrorTime = null;
    recoveryState.recoveryStrategy = RecoveryStrategy.NONE;
    recoveryState.isFallbackMode = false;
    
    // 清除全局管理器的错误状态
    librtcoreGlobalManager.clearError();
  };

  /**
   * 处理错误
   */
  const handleError = async (error: any, context: string = "未知"): Promise<void> => {
    recoveryState.lastErrorTime = Date.now();
    
    logger.error(LogModule.GENERAL, `librtcore 错误处理器：处理错误 [${context}]`, error);

    // 确定恢复策略
    const strategy = determineRecoveryStrategy(error, recoveryState.currentRetryCount);
    recoveryState.recoveryStrategy = strategy;

    // 显示用户通知
    showUserNotification(error, strategy);

    // 执行恢复策略
    switch (strategy) {
      case RecoveryStrategy.RETRY:
        await performAutoRetry();
        break;
        
      case RecoveryStrategy.FALLBACK:
        enterFallbackMode();
        break;
        
      case RecoveryStrategy.RESET:
        try {
          await librtcoreGlobalManager.cleanup();
          resetErrorState();
        } catch (resetError) {
          logger.error(LogModule.GENERAL, "librtcore 错误处理器：重置失败", resetError);
        }
        break;
        
      case RecoveryStrategy.MANUAL_RETRY:
      case RecoveryStrategy.NONE:
      default:
        // 等待手动处理
        break;
    }
  };

  /**
   * 手动重试
   */
  const manualRetry = async (): Promise<boolean> => {
    if (recoveryState.isRecovering) {
      logger.warn(LogModule.GENERAL, "librtcore 错误处理器：正在恢复中，忽略手动重试请求");
      return false;
    }

    recoveryState.isRecovering = true;
    
    try {
      logger.info(LogModule.GENERAL, "librtcore 错误处理器：开始手动重试");
      
      const success = await librtcoreGlobalManager.retry();
      
      if (success) {
        logger.info(LogModule.GENERAL, "librtcore 错误处理器：手动重试成功");
        resetErrorState();
        
        if (config.showUserNotifications) {
          message.success(t("librtcore.error.retrySuccess"));
        }
        
        return true;
      } else {
        logger.warn(LogModule.GENERAL, "librtcore 错误处理器：手动重试失败");
        
        if (config.showUserNotifications) {
          message.error(t("librtcore.error.retryFailed"));
        }
        
        return false;
      }
    } catch (error) {
      logger.error(LogModule.GENERAL, "librtcore 错误处理器：手动重试过程中出现错误", error);
      
      if (config.showUserNotifications) {
        message.error(t("librtcore.error.retryError"));
      }
      
      return false;
    } finally {
      recoveryState.isRecovering = false;
    }
  };

  /**
   * 更新配置
   */
  const updateConfig = (newConfig: Partial<ErrorHandlerConfig>) => {
    Object.assign(config, newConfig);
    logger.debug(LogModule.GENERAL, "librtcore 错误处理器：配置已更新", newConfig);
  };

  // 监听全局管理器的错误事件
  librtcoreGlobalManager.on(LibrtcoreEvent.ERROR, (errorData) => {
    if (errorData && errorData.level !== ErrorLevel.SILENT) {
      handleError(errorData, "全局管理器");
    }
  });

  return {
    // 状态
    config,
    recoveryState,
    
    // 计算属性
    canAutoRetry,
    shouldShowRetryButton,
    isInFallbackMode,
    
    // 方法
    handleError,
    manualRetry,
    resetErrorState,
    enterFallbackMode,
    exitFallbackMode,
    updateConfig,
  };
}
