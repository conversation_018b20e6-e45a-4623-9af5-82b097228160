<template>
  <div class="debug-button-container">
    <!-- 调试按钮 -->
    <NButton
      text
      size="small"
      class="debug-button"
      @click="showDebugMenu"
      :title="t('debug.button.tooltip')"
    >
      <template #icon>
        <NIcon>
          <BugIcon />
        </NIcon>
      </template>
    </NButton>
    
    <!-- 调试菜单 -->
    <NDropdown
      :show="showMenu"
      :options="debugMenuOptions"
      @select="handleMenuSelect"
      @clickoutside="hideDebugMenu"
      placement="bottom-end"
    >
      <div></div>
    </NDropdown>
    
    <!-- 调试设置对话框 -->
    <DebugSettings
      ref="debugSettingsRef"
      @open-log-viewer="openLogViewer"
    />
    
    <!-- 日志查看器 -->
    <LogViewer ref="logViewerRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue';
import { useI18n } from '@/composables/useI18n';
import { NIcon } from 'naive-ui';
import {
  BugOutline as BugIcon,
  SettingsOutline as SettingsIcon,
  DocumentTextOutline as LogIcon,
  FolderOpenOutline as FolderIcon,
  DownloadOutline as ExportIcon
} from '@vicons/ionicons5';
import DebugSettings from '@/components/debug/DebugSettings.vue';
import LogViewer from '@/components/debug/LogViewer.vue';
import { invoke } from '@tauri-apps/api/core';
import { useMessage } from 'naive-ui';
import { logger, LogModule } from '@/utils/logger/logger';

// === 组合函数 ===
const { t } = useI18n();
const message = useMessage();

// === 响应式状态 ===
const showMenu = ref(false);
const debugSettingsRef = ref();
const logViewerRef = ref();

// === 调试菜单选项 ===
const debugMenuOptions = [
  {
    label: t('debug.menu.settings'),
    key: 'settings',
    icon: () => h(NIcon, { component: SettingsIcon }),
  },
  {
    label: t('debug.menu.viewLogs'),
    key: 'logs',
    icon: () => h(NIcon, { component: LogIcon }),
  },
  {
    label: t('debug.menu.openLogFolder'),
    key: 'folder',
    icon: () => h(NIcon, { component: FolderIcon }),
  },
  {
    type: 'divider',
    key: 'divider1'
  },
  {
    label: t('debug.menu.exportDebugInfo'),
    key: 'export',
    icon: () => h(NIcon, { component: ExportIcon }),
  },
];

// === 方法 ===

/**
 * 显示调试菜单
 */
const showDebugMenu = () => {
  showMenu.value = true;
};

/**
 * 隐藏调试菜单
 */
const hideDebugMenu = () => {
  showMenu.value = false;
};

/**
 * 处理菜单选择
 */
const handleMenuSelect = async (key: string) => {
  hideDebugMenu();
  
  switch (key) {
    case 'settings':
      await debugSettingsRef.value?.showDebugSettings();
      break;
      
    case 'logs':
      await logViewerRef.value?.showLogViewer();
      break;
      
    case 'folder':
      await openLogFolder();
      break;
      
    case 'export':
      await exportDebugInfo();
      break;
  }
};

/**
 * 打开日志文件夹
 */
const openLogFolder = async () => {
  try {
    const logPath = await invoke<string>('get_log_file_path');
    // 获取文件夹路径（去掉文件名）
    const folderPath = logPath.substring(0, logPath.lastIndexOf('\\') || logPath.lastIndexOf('/'));
    // 使用系统默认方式打开文件夹
    await invoke('open_external_url', { url: `file://${folderPath}` });
  } catch (error) {
    logger.error(LogModule.GENERAL, 'Failed to open log folder', error);
    message.error(t('debug.errors.openFolderFailed'));
  }
};

/**
 * 导出调试信息
 */
const exportDebugInfo = async () => {
  try {
    const exportPath = await invoke<string>('export_debug_info');
    message.success(t('debug.exportSuccess', { path: exportPath }));
  } catch (error) {
    logger.error(LogModule.GENERAL, 'Failed to export debug info', error);
    message.error(t('debug.errors.exportFailed'));
  }
};

/**
 * 打开日志查看器（从设置对话框触发）
 */
const openLogViewer = async () => {
  await logViewerRef.value?.showLogViewer();
};
</script>

<style scoped>
.debug-button-container {
  position: relative;
  display: inline-block;
}

.debug-button {
  color: var(--n-text-color-2);
  transition: color 0.2s ease;
}

.debug-button:hover {
  color: var(--n-text-color);
}

/* 在生产环境中隐藏调试按钮（可选） */
/* 如果需要在生产环境中完全隐藏，可以取消注释下面的代码 */
/*
.debug-button-container {
  display: none;
}

@media (prefers-color-scheme: dark) {
  .debug-button-container {
    display: inline-block;
  }
}
*/

/* 开发环境标识 */
.debug-button::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 6px;
  height: 6px;
  background: #ff6b6b;
  border-radius: 50%;
  opacity: 0.8;
}

/* 生产环境下隐藏开发标识 */
:global(.production) .debug-button::after {
  display: none;
}
</style>
