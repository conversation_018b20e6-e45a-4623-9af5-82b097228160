#!/usr/bin/env node

// 简单的开发启动脚本
const { spawn } = require('child_process');
const path = require('path');

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.PORT = '3000';
process.env.HOST = '0.0.0.0';
process.env.LOG_LEVEL = 'debug';
process.env.STORAGE_PATH = './storage';
process.env.RELEASES_PATH = './storage/releases';
process.env.METADATA_PATH = './storage/metadata';
process.env.LOGS_PATH = './storage/logs';
process.env.CORS_ORIGIN = '*';

console.log('🚀 启动 RealityTap OTA 服务器...');
console.log('📍 工作目录:', process.cwd());
console.log('🌐 服务器地址: http://localhost:3000');
console.log('');

// 启动tsx
const tsx = spawn('npx', ['tsx', 'src/app.ts'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
});

tsx.on('close', (code) => {
  console.log(`\n服务器已停止，退出码: ${code}`);
});

tsx.on('error', (err) => {
  console.error('启动失败:', err);
});

// 处理中断信号
process.on('SIGINT', () => {
  console.log('\n正在停止服务器...');
  tsx.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n正在停止服务器...');
  tsx.kill('SIGTERM');
});
