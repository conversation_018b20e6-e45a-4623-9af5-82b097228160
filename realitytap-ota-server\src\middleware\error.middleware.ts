import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger.util';
import { config } from '@/config/server.config';

interface CustomError extends Error {
  statusCode?: number;
  code?: string;
}

export const errorMiddleware = (
  error: CustomError,
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const statusCode = error.statusCode || 500;
  const errorCode = error.code || 'INTERNAL_SERVER_ERROR';
  const message = error.message || 'Internal server error';

  // Log error details
  logger.error('请求处理错误', {
    error: {
      name: error.name || 'Error',
      message: error.message,
      stack: error.stack,
      code: errorCode,
      statusCode,
      type: typeof error,
      constructor: error.constructor?.name,
    },
    request: {
      method: req.method,
      url: req.url,
      originalUrl: req.originalUrl,
      path: req.path,
      query: Object.keys(req.query).length > 0 ? req.query : undefined,
      params: Object.keys(req.params).length > 0 ? req.params : undefined,
      headers: {
        'content-type': req.get('Content-Type'),
        'content-length': req.get('Content-Length'),
        'authorization': req.get('Authorization') ? '[REDACTED]' : undefined,
        'referer': req.get('Referer'),
        'origin': req.get('Origin'),
        'host': req.get('Host'),
        'x-forwarded-for': req.get('X-Forwarded-For'),
        'x-real-ip': req.get('X-Real-IP'),
      },
      clientIP: req.ip,
      userAgent: req.get('User-Agent'),
      protocol: req.protocol,
      secure: req.secure,
      xhr: req.xhr,
    },
    requestBody: req.body && Object.keys(req.body).length > 0 ?
      (typeof req.body === 'object' ?
        JSON.stringify(req.body).substring(0, 500) + (JSON.stringify(req.body).length > 500 ? '...' : '') :
        String(req.body).substring(0, 500) + (String(req.body).length > 500 ? '...' : '')
      ) : undefined,
    timing: {
      timestamp: new Date().toISOString(),
      requestStartTime: (req as any).startTime ? new Date((req as any).startTime).toISOString() : undefined,
      processingTime: (req as any).startTime ? Date.now() - (req as any).startTime : undefined,
    },
    module: 'system',
    operation: 'request_error',
  });

  // Prepare error response
  const errorResponse: any = {
    success: false,
    error: {
      code: errorCode,
      message: config.server.nodeEnv === 'production' 
        ? (statusCode >= 500 ? 'Internal server error' : message)
        : message,
    },
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  };

  // Include stack trace in development
  if (config.server.nodeEnv === 'development') {
    errorResponse.error.stack = error.stack;
  }

  res.status(statusCode).json(errorResponse);
};
