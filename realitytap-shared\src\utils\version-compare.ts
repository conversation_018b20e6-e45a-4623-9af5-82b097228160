import { VERSION_REGEX, SEMVER_REGEX, VERSION_COMPARISON } from '../constants/versions';

/**
 * 解析版本字符串
 */
export interface ParsedVersion {
  major: number;
  minor: number;
  patch: number;
  prerelease?: string;
  build?: string;
}

/**
 * 解析版本字符串为结构化对象
 */
export function parseVersion(version: string): ParsedVersion {
  const match = version.match(SEMVER_REGEX);
  if (!match) {
    throw new Error(`Invalid version format: ${version}`);
  }

  const [, major, minor, patch, prerelease, build] = match;

  // 确保必需的版本号部分存在
  if (!major || !minor || !patch) {
    throw new Error(`Invalid version format: missing required version components in ${version}`);
  }

  return {
    major: parseInt(major, 10),
    minor: parseInt(minor, 10),
    patch: parseInt(patch, 10),
    prerelease: prerelease || undefined,
    build: build || undefined,
  };
}

/**
 * 比较两个版本号
 * @param version1 第一个版本
 * @param version2 第二个版本
 * @returns -1 if version1 < version2, 0 if equal, 1 if version1 > version2
 */
export function compareVersions(version1: string, version2: string): number {
  try {
    const v1 = parseVersion(version1);
    const v2 = parseVersion(version2);

    // 比较主版本号
    if (v1.major !== v2.major) {
      return v1.major > v2.major ? VERSION_COMPARISON.GREATER_THAN : VERSION_COMPARISON.LESS_THAN;
    }

    // 比较次版本号
    if (v1.minor !== v2.minor) {
      return v1.minor > v2.minor ? VERSION_COMPARISON.GREATER_THAN : VERSION_COMPARISON.LESS_THAN;
    }

    // 比较补丁版本号
    if (v1.patch !== v2.patch) {
      return v1.patch > v2.patch ? VERSION_COMPARISON.GREATER_THAN : VERSION_COMPARISON.LESS_THAN;
    }

    // 比较预发布版本
    if (v1.prerelease && v2.prerelease) {
      return v1.prerelease > v2.prerelease ? VERSION_COMPARISON.GREATER_THAN : VERSION_COMPARISON.LESS_THAN;
    } else if (v1.prerelease && !v2.prerelease) {
      return VERSION_COMPARISON.LESS_THAN; // 预发布版本小于正式版本
    } else if (!v1.prerelease && v2.prerelease) {
      return VERSION_COMPARISON.GREATER_THAN; // 正式版本大于预发布版本
    }

    return VERSION_COMPARISON.EQUAL;
  } catch (error) {
    throw new Error(`Failed to compare versions: ${error}`);
  }
}

/**
 * 检查版本兼容性
 */
export function isVersionCompatible(
  currentVersion: string,
  minimumVersion: string
): boolean {
  try {
    return compareVersions(currentVersion, minimumVersion) >= 0;
  } catch (error) {
    return false;
  }
}

/**
 * 检查是否有更新
 */
export function hasUpdate(currentVersion: string, latestVersion: string): boolean {
  try {
    return compareVersions(latestVersion, currentVersion) > 0;
  } catch (error) {
    return false;
  }
}

/**
 * 获取更新类型
 */
export function getUpdateType(
  currentVersion: string,
  newVersion: string
): 'major' | 'minor' | 'patch' | 'none' {
  try {
    if (!hasUpdate(currentVersion, newVersion)) {
      return 'none';
    }

    const current = parseVersion(currentVersion);
    const latest = parseVersion(newVersion);

    if (latest.major > current.major) {
      return 'major';
    } else if (latest.minor > current.minor) {
      return 'minor';
    } else {
      return 'patch';
    }
  } catch (error) {
    return 'none';
  }
}

/**
 * 验证版本格式
 */
export function isValidVersion(version: string): boolean {
  return VERSION_REGEX.test(version);
}

/**
 * 清理版本字符串（移除前缀 v）
 */
export function cleanVersion(version: string): string {
  return version.replace(/^v/, '');
}

/**
 * 获取版本的预发布标识
 */
export function getPrerelease(version: string): string | null {
  try {
    const parsed = parseVersion(version);
    return parsed.prerelease || null;
  } catch (error) {
    return null;
  }
}

/**
 * 检查是否为预发布版本
 */
export function isPrerelease(version: string): boolean {
  return getPrerelease(version) !== null;
}