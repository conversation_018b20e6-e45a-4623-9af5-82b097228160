import { ref, nextTick, type Ref } from "vue";
import { NInput, useMessage, type TreeOption as NaiveTreeOption } from "naive-ui";
import { parseErrorMessage } from "@/utils/commonUtils";
import { findNodeByKey, type TreeNode } from "@/utils/tree/treeUtils";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * Composable for handling inline editing functionality
 */
export function useInlineEdit(
  projectStore: any,
  treeData: Ref<TreeNode[]>,
  selectedTreeKeys: Ref<string[]>,
  // 接受外部的新建分组状态变量
  isCreatingNewGroup: Ref<boolean>,
  newGroupParentUuid: Ref<string | null>,
  newGroupName: Ref<string>,
  newGroupTemporaryKey: Ref<string | null>,
  t: (key: string, values?: Record<string, any>) => string
) {
  const message = useMessage();

  // 编辑状态
  const editingNodeKey = ref<string | null>(null);
  const editingNodeOriginalLabel = ref<string | null>(null);
  const inlineEditValue = ref("");
  const isRenameProcessing = ref(false);
  const inputRef = ref<InstanceType<typeof NInput> | null>(null);

  // 前置声明函数变量
  let cancelGroupRename: () => void;
  let cancelFileRename: () => void;

  /**
   * 取消任何正在进行的编辑（前置声明）
   */
  const cancelAnyOngoingEdit = (nodeToExcludeKey?: string) => {
    // Cancel new group creation if active and not excluded
    if (isCreatingNewGroup.value && (!nodeToExcludeKey || newGroupTemporaryKey.value !== nodeToExcludeKey)) {
      cancelNewGroupCreation();
    }

    // Cancel existing item rename if active and not excluded
    if (editingNodeKey.value && (!nodeToExcludeKey || editingNodeKey.value !== nodeToExcludeKey)) {
      if (editingNodeKey.value.startsWith("group-")) {
        cancelGroupRename?.();
      } else if (editingNodeKey.value.startsWith("file-")) {
        cancelFileRename?.();
      }
    }
  };

  /**
   * 新建分组相关功能（使用外部状态变量）
   */
  const triggerNewGroupCreation = (parentUuid: string | null) => {
    cancelAnyOngoingEdit(); // Cancel other edits before starting a new one

    isCreatingNewGroup.value = true;
    newGroupParentUuid.value = parentUuid;
    newGroupName.value = ""; // Default name, user will edit
    newGroupTemporaryKey.value = `temp-group-${Date.now()}`;
  };

  const cancelNewGroupCreation = () => {
    isCreatingNewGroup.value = false;
    newGroupParentUuid.value = null;
    newGroupName.value = "";
    newGroupTemporaryKey.value = null;
  };

  const commitNewGroup = async () => {
    if (!newGroupName.value.trim()) {
      cancelNewGroupCreation(); // Cancel if name is empty after trim
      return;
    }
    if (!isCreatingNewGroup.value) return; // Avoid double commits

    try {
      await projectStore.addNewGroup(newGroupName.value.trim(), newGroupParentUuid.value);
      message.success(t('editor.inlineEdit.groupCreateSuccess', { name: newGroupName.value.trim() }));
    } catch (error: any) {
      logger.error(LogModule.PROJECT, "Error creating new group", error);
      message.error(t('editor.inlineEdit.groupCreateFailed', { error: parseErrorMessage(error) }));
    } finally {
      cancelNewGroupCreation(); // Reset state whether success or fail, store should update tree
    }
  };

  /**
   * 分组编辑功能
   */
  const useGroupEdit = () => {
    const triggerGroupRename = (node: NaiveTreeOption) => {
      const key = String(node.key);
      const isNodeGroup = key.startsWith("group-");

      if (!isNodeGroup) return; // Only groups can be renamed this way

      cancelAnyOngoingEdit(key);

      editingNodeKey.value = key;
      editingNodeOriginalLabel.value = node.label as string;
      inlineEditValue.value = node.label as string; // Set input to current name
      selectedTreeKeys.value = [key]; // Ensure node is selected

      nextTick(() => {
        if (inputRef.value) {
          inputRef.value.focus();
        }
      });
    };

    const commitGroupRename = async () => {
      if (!editingNodeKey.value || isRenameProcessing.value) return;
      isRenameProcessing.value = true;

      const newName = inlineEditValue.value.trim();
      const oldName = editingNodeOriginalLabel.value;

      if (!newName || newName === oldName) {
        cancelGroupRename();
        if (!newName) message.warning(t('editor.inlineEdit.groupNameEmpty'));
        isRenameProcessing.value = false;
        return;
      }

      // 从node.key中提取UUID
      const parts = String(editingNodeKey.value).split("-");
      if (parts.length < 2 || parts[0] !== "group") {
        logger.error(LogModule.PROJECT, "Invalid node key format for group rename", { nodeKey: editingNodeKey.value });
        cancelGroupRename();
        isRenameProcessing.value = false;
        return;
      }

      // 重新组合UUID部分(可能包含多个-)
      const groupUuid = parts.slice(1).join("-");

      try {
        await projectStore.renameGroup(groupUuid, newName);
        // The treeData will refresh from the store, so local label update might be redundant
        // but setting isEditing to false is important.
        const treeNode = findNodeByKey(treeData.value, editingNodeKey.value);
        if (treeNode) {
          treeNode.label = newName; // Optimistically update label, store will confirm
        }
        message.success(t('editor.inlineEdit.groupRenameSuccess', { oldName, newName }));
      } catch (error: any) {
        logger.error(LogModule.PROJECT, "Error renaming group", error);
        message.error(t('editor.inlineEdit.groupRenameFailed', { error: parseErrorMessage(error) }));
        // On error, restore original label and cancel editing
        const treeNode = findNodeByKey(treeData.value, editingNodeKey.value);
        if (treeNode) {
          treeNode.label = oldName || treeNode.label;
        }
      } finally {
        editingNodeKey.value = null;
        editingNodeOriginalLabel.value = null;
        inlineEditValue.value = "";
        isRenameProcessing.value = false;
      }
    };

    const cancelGroupRename = () => {
      if (!editingNodeKey.value) return;
      editingNodeKey.value = null;
      editingNodeOriginalLabel.value = null;
      inlineEditValue.value = "";
      isRenameProcessing.value = false;
    };

    return {
      triggerGroupRename,
      commitGroupRename,
      cancelGroupRename,
    };
  };

  /**
   * 文件编辑功能
   */
  const useFileEdit = () => {
    const triggerFileRename = (node: NaiveTreeOption) => {
      const key = String(node.key);

      cancelAnyOngoingEdit(key);

      editingNodeKey.value = key;
      editingNodeOriginalLabel.value = node.label as string; // Full name with extension
      inlineEditValue.value = node.label as string; // User edits full name with extension
      selectedTreeKeys.value = [key];

      nextTick(() => {
        if (inputRef.value) {
          inputRef.value.focus();
        }
      });
    };

    const commitFileRename = async () => {
      if (!editingNodeKey.value || isRenameProcessing.value) return;
      isRenameProcessing.value = true;

      const newName = inlineEditValue.value.trim();
      const oldName = editingNodeOriginalLabel.value;

      if (!newName) {
        message.warning(t('editor.inlineEdit.fileNameEmpty'));
        cancelFileRename();
        return;
      }

      if (newName === oldName) {
        cancelFileRename();
        return;
      }

      // Basic frontend check for extension - backend will do the definitive check
      if (!newName.toLowerCase().endsWith(".he")) {
        message.warning(t('editor.inlineEdit.fileNameMustEndWithHe'));
        isRenameProcessing.value = false;
        return;
      }

      const parts = String(editingNodeKey.value).split("-");
      if (parts.length < 2 || parts[0] !== "file") {
        logger.error(LogModule.PROJECT, "Invalid node key format for file rename", { nodeKey: editingNodeKey.value });
        cancelFileRename();
        return;
      }
      const fileUuid = parts.slice(1).join("-");

      try {
        await projectStore.renameHapticFile(fileUuid, newName);
        message.success(t('editor.inlineEdit.fileRenameSuccess', { oldName, newName }));
      } catch (error: any) {
        logger.error(LogModule.PROJECT, "Error renaming file", error);
        message.error(t('editor.inlineEdit.fileRenameFailed', { error: parseErrorMessage(error) }));
      } finally {
        editingNodeKey.value = null;
        editingNodeOriginalLabel.value = null;
        inlineEditValue.value = "";
        isRenameProcessing.value = false;
      }
    };

    const cancelFileRename = () => {
      if (!editingNodeKey.value) return;
      editingNodeKey.value = null;
      editingNodeOriginalLabel.value = null;
      inlineEditValue.value = "";
      isRenameProcessing.value = false;
    };

    return {
      triggerFileRename,
      commitFileRename,
      cancelFileRename,
    };
  };

  // 创建分组和文件编辑功能的实例
  const groupEditState = useGroupEdit();
  const fileEditState = useFileEdit();

  // 给前置声明的函数赋值
  cancelGroupRename = groupEditState.cancelGroupRename;
  cancelFileRename = fileEditState.cancelFileRename;

  return {
    // 编辑状态
    editingNodeKey,
    editingNodeOriginalLabel,
    inlineEditValue,
    isRenameProcessing,
    inputRef,

    // 新建分组功能
    triggerNewGroupCreation,
    cancelNewGroupCreation,
    commitNewGroup,

    // 分组编辑
    ...groupEditState,

    // 文件编辑
    ...fileEditState,

    // 通用功能
    cancelAnyOngoingEdit,
  };
}
