<template>
  <!-- 使用 NBadge 包装按钮以显示红点 -->
  <NBadge :show="shouldShowUpdateBadge" dot type="error" :offset="[-14, 14]">
    <button class="titlebar-button about-button" @click="handleClick" :title="getButtonTitle()">
      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="7" cy="7" r="5" stroke="currentColor" stroke-width="1" />
        <circle cx="7" cy="5" r="0.5" fill="currentColor" />
        <path d="M7 6.5V9.5" stroke="currentColor" stroke-width="1" stroke-linecap="round" />
      </svg>
    </button>
  </NBadge>

  <!-- 关于对话框 -->
  <AboutDialog />
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { NBadge } from "naive-ui";
import { useAboutDialog } from "@/composables/useAboutDialog";
import { useUpdateNotificationStore } from "@/stores/update-notification-store";
import AboutDialog from "./AboutDialog.vue";

// === 组合函数 ===
const { t } = useI18n();
const { showAboutDialog } = useAboutDialog();
const updateStore = useUpdateNotificationStore();

// === 计算属性 ===
const shouldShowUpdateBadge = computed(() => {
  return updateStore.shouldShowBadge;
});

// === 事件处理 ===
const handleClick = () => {
  // 只是打开对话框，不立即隐藏红点
  // 红点应该在用户真正查看更新信息或进行更新操作后才消失
  showAboutDialog();
};

// === 工具方法 ===
const getButtonTitle = () => {
  if (shouldShowUpdateBadge.value && updateStore.updateInfo) {
    return `${t('common.about')} - ${t('about.updateCheck.newVersionAvailable')}: ${updateStore.updateInfo.version}`;
  }
  return t('common.about');
};
</script>

<style scoped>
.titlebar-button {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  border: none;
  color: #e6e6e6;
  cursor: pointer;
  outline: none;
  transition: background-color 0.2s;
}

.titlebar-button:hover {
  background: #3a3a3a;
  color: #e6e6e6 !important;
}

.titlebar-button:hover svg {
  color: #7fe7c4 !important;
}

.titlebar-button svg {
  color: inherit;
}

.about-button {
  margin-right: 8px;
  border-radius: 4px;
}
</style>
