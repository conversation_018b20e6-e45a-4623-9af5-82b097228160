#!/bin/bash

# RealityTap OTA Server Permission Fix Script
# This script fixes directory permissions for Docker container compatibility

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_DIR="$PROJECT_ROOT/docker"

show_help() {
    echo "RealityTap OTA Server 权限修复脚本"
    echo ""
    echo "用法:"
    echo "  $0 [OPTIONS]"
    echo ""
    echo "选项:"
    echo "  -s, --storage PATH    指定存储目录路径 (默认: ./data/storage)"
    echo "  -k, --keys PATH       指定密钥目录路径 (默认: ./keys)"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                          # 使用默认路径"
    echo "  $0 -s /media/extdisk/realitytap_ota/storage # 指定存储目录"
    echo "  $0 -s /custom/storage -k /custom/keys       # 指定自定义路径"
}

# Parse command line arguments
STORAGE_PATH=""
KEYS_PATH=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--storage)
            STORAGE_PATH="$2"
            shift 2
            ;;
        -k|--keys)
            KEYS_PATH="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# Load environment variables if .env exists
if [[ -f "$DOCKER_DIR/.env" ]]; then
    log_info "加载环境变量..."
    set -a
    source "$DOCKER_DIR/.env"
    set +a
fi

# Set default paths
STORAGE_PATH="${STORAGE_PATH:-${STORAGE_HOST_PATH:-./data/storage}}"
KEYS_PATH="${KEYS_PATH:-${KEYS_HOST_PATH:-./keys}}"

# Convert relative paths to absolute paths
if [[ ! "$STORAGE_PATH" = /* ]]; then
    STORAGE_PATH="$DOCKER_DIR/$STORAGE_PATH"
fi

if [[ ! "$KEYS_PATH" = /* ]]; then
    KEYS_PATH="$DOCKER_DIR/$KEYS_PATH"
fi

log_info "存储目录: $STORAGE_PATH"
log_info "密钥目录: $KEYS_PATH"

# Check if directories exist
if [[ ! -d "$STORAGE_PATH" ]]; then
    log_error "存储目录不存在: $STORAGE_PATH"
    exit 1
fi

if [[ ! -d "$KEYS_PATH" ]]; then
    log_warn "密钥目录不存在，将创建: $KEYS_PATH"
    mkdir -p "$KEYS_PATH"
fi

# Function to fix permissions
fix_permissions() {
    local dir_path="$1"
    local dir_name="$2"
    
    log_step "修复 $dir_name 权限..."
    
    # Check current ownership
    current_owner=$(stat -c '%u:%g' "$dir_path" 2>/dev/null || echo "unknown")
    log_info "当前所有者: $current_owner"
    
    # Try to set ownership to container user (UID 1001, GID 1001)
    if command -v sudo >/dev/null 2>&1; then
        log_info "尝试使用 sudo 设置所有者为 1001:1001..."
        if sudo chown -R 1001:1001 "$dir_path" 2>/dev/null; then
            log_info "✓ 所有者设置成功"
        else
            log_warn "sudo 设置所有者失败，尝试不使用 sudo..."
            if chown -R 1001:1001 "$dir_path" 2>/dev/null; then
                log_info "✓ 所有者设置成功"
            else
                log_warn "设置所有者失败，将使用权限 777 作为备选方案"
                chmod -R 777 "$dir_path" 2>/dev/null || {
                    log_error "设置权限失败"
                    return 1
                }
                log_info "✓ 权限设置为 777"
                return 0
            fi
        fi
    else
        log_warn "sudo 命令不可用，尝试直接设置所有者..."
        if chown -R 1001:1001 "$dir_path" 2>/dev/null; then
            log_info "✓ 所有者设置成功"
        else
            log_warn "设置所有者失败，将使用权限 777 作为备选方案"
            chmod -R 777 "$dir_path" 2>/dev/null || {
                log_error "设置权限失败"
                return 1
            }
            log_info "✓ 权限设置为 777"
            return 0
        fi
    fi
    
    # Set proper permissions
    log_info "设置目录权限为 755..."
    chmod -R 755 "$dir_path" 2>/dev/null || {
        log_error "设置权限失败"
        return 1
    }
    
    log_info "✓ $dir_name 权限修复完成"
    return 0
}

# Main execution
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  RealityTap OTA Server 权限修复工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "此脚本将修复 Docker 容器所需的目录权限"
    echo ""
    
    # Fix storage directory permissions
    fix_permissions "$STORAGE_PATH" "存储目录"
    
    # Fix keys directory permissions
    fix_permissions "$KEYS_PATH" "密钥目录"
    
    echo ""
    log_info "权限修复完成！"
    echo ""
    echo -e "${BLUE}下一步操作：${NC}"
    echo "1. 重新启动 Docker 容器"
    echo "2. 检查容器日志确认问题已解决"
    echo ""
    echo "重启命令示例："
    echo "  docker-compose -f docker/docker-compose.http.yml restart"
    echo "  # 或"
    echo "  ./scripts/docker-deploy.sh"
}

# Run main function
main "$@"
