<svg width="800" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 文件层 -->
  <rect x="50" y="50" width="120" height="60" rx="10" fill="#e1f5fe" stroke="#01579b" stroke-width="2"/>
  <text x="110" y="75" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">.he文件</text>
  <text x="110" y="90" text-anchor="middle" font-family="Arial" font-size="10">JSON格式</text>
  
  <!-- Tauri后端 -->
  <rect x="50" y="150" width="120" height="60" rx="10" fill="#f3e5f5" stroke="#4a148c" stroke-width="2"/>
  <text x="110" y="170" text-anchor="middle" font-family="Arial" font-size="10">Tauri后端</text>
  <text x="110" y="185" text-anchor="middle" font-family="Arial" font-size="9">read_file_content</text>
  <text x="110" y="200" text-anchor="middle" font-family="Arial" font-size="9">_command</text>
  
  <!-- 前端接收 -->
  <rect x="50" y="250" width="120" height="60" rx="10" fill="#e8f5e8" stroke="#1b5e20" stroke-width="2"/>
  <text x="110" y="275" text-anchor="middle" font-family="Arial" font-size="12">前端接收</text>
  <text x="110" y="290" text-anchor="middle" font-family="Arial" font-size="10">JSON字符串</text>
  
  <!-- JSON解析 -->
  <rect x="250" y="250" width="120" height="60" rx="10" fill="#fff3e0" stroke="#e65100" stroke-width="2"/>
  <text x="310" y="270" text-anchor="middle" font-family="Arial" font-size="10">parseRealityTap</text>
  <text x="310" y="285" text-anchor="middle" font-family="Arial" font-size="9">JSON解析+验证</text>
  
  <!-- RealityTapEffect -->
  <rect x="450" y="250" width="120" height="60" rx="10" fill="#f3e5f5" stroke="#4a148c" stroke-width="2"/>
  <text x="510" y="270" text-anchor="middle" font-family="Arial" font-size="9">RealityTapEffect</text>
  <text x="510" y="285" text-anchor="middle" font-family="Arial" font-size="9">对象</text>
  <text x="510" y="300" text-anchor="middle" font-family="Arial" font-size="9">V1/V2格式</text>
  
  <!-- 数据扁平化 -->
  <rect x="450" y="350" width="120" height="60" rx="10" fill="#e8f5e8" stroke="#1b5e20" stroke-width="2"/>
  <text x="510" y="370" text-anchor="middle" font-family="Arial" font-size="9">flattenRealityTap</text>
  <text x="510" y="385" text-anchor="middle" font-family="Arial" font-size="9">Effect</text>
  <text x="510" y="400" text-anchor="middle" font-family="Arial" font-size="9">数据扁平化</text>
  
  <!-- RenderableEvent数组 -->
  <rect x="250" y="350" width="120" height="60" rx="10" fill="#e8f5e8" stroke="#1b5e20" stroke-width="2"/>
  <text x="310" y="370" text-anchor="middle" font-family="Arial" font-size="9">RenderableEvent</text>
  <text x="310" y="385" text-anchor="middle" font-family="Arial" font-size="9">数组</text>
  <text x="310" y="400" text-anchor="middle" font-family="Arial" font-size="9">渲染就绪的事件数据</text>
  
  <!-- Canvas组件 -->
  <rect x="250" y="450" width="120" height="60" rx="10" fill="#fff9c4" stroke="#f57f17" stroke-width="2"/>
  <text x="310" y="470" text-anchor="middle" font-family="Arial" font-size="9">Interactive</text>
  <text x="310" y="485" text-anchor="middle" font-family="Arial" font-size="9">WaveformCanvas</text>
  <text x="310" y="500" text-anchor="middle" font-family="Arial" font-size="9">Canvas组件</text>
  
  <!-- 坐标转换系统 -->
  <rect x="250" y="550" width="120" height="60" rx="10" fill="#ffccbc" stroke="#bf360c" stroke-width="2"/>
  <text x="310" y="570" text-anchor="middle" font-family="Arial" font-size="9">坐标转换系统</text>
  <text x="310" y="585" text-anchor="middle" font-family="Arial" font-size="9">时间/强度→</text>
  <text x="310" y="600" text-anchor="middle" font-family="Arial" font-size="9">像素坐标</text>
  
  <!-- 绘制编排器 -->
  <rect x="250" y="650" width="120" height="60" rx="10" fill="#d1c4e9" stroke="#512da8" stroke-width="2"/>
  <text x="310" y="670" text-anchor="middle" font-family="Arial" font-size="9">绘制编排器</text>
  <text x="310" y="685" text-anchor="middle" font-family="Arial" font-size="8">useWaveform</text>
  <text x="310" y="700" text-anchor="middle" font-family="Arial" font-size="8">DrawingOrchestrator</text>
  
  <!-- 绘制管道 -->
  <rect x="50" y="750" width="100" height="40" rx="5" fill="#c8e6c9" stroke="#388e3c" stroke-width="1"/>
  <text x="100" y="775" text-anchor="middle" font-family="Arial" font-size="10">绘制管道</text>
  
  <!-- 绘制步骤 -->
  <rect x="200" y="750" width="80" height="30" rx="5" fill="#ffcdd2" stroke="#d32f2f" stroke-width="1"/>
  <text x="240" y="770" text-anchor="middle" font-family="Arial" font-size="8">1.清除画布</text>
  
  <rect x="300" y="750" width="80" height="30" rx="5" fill="#ffcdd2" stroke="#d32f2f" stroke-width="1"/>
  <text x="340" y="770" text-anchor="middle" font-family="Arial" font-size="8">2.绘制网格</text>
  
  <rect x="400" y="750" width="80" height="30" rx="5" fill="#ffcdd2" stroke="#d32f2f" stroke-width="1"/>
  <text x="440" y="770" text-anchor="middle" font-family="Arial" font-size="8">3.音频波形</text>
  
  <rect x="500" y="750" width="80" height="30" rx="5" fill="#ffcdd2" stroke="#d32f2f" stroke-width="1"/>
  <text x="540" y="770" text-anchor="middle" font-family="Arial" font-size="8">4.触觉事件</text>
  
  <rect x="600" y="750" width="80" height="30" rx="5" fill="#ffcdd2" stroke="#d32f2f" stroke-width="1"/>
  <text x="640" y="770" text-anchor="middle" font-family="Arial" font-size="8">5.辅助线</text>
  
  <!-- 最终显示 -->
  <rect x="350" y="850" width="120" height="60" rx="10" fill="#fff3e0" stroke="#e65100" stroke-width="2"/>
  <text x="410" y="875" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">最终Canvas</text>
  <text x="410" y="890" text-anchor="middle" font-family="Arial" font-size="10">显示</text>
  
  <!-- 箭头连接 -->
  <line x1="110" y1="110" x2="110" y2="140" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="110" y1="210" x2="110" y2="240" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="170" y1="280" x2="240" y2="280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="370" y1="280" x2="440" y2="280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="510" y1="310" x2="510" y2="340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="450" y1="380" x2="380" y2="380" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="310" y1="410" x2="310" y2="440" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="310" y1="510" x2="310" y2="540" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="310" y1="610" x2="310" y2="640" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="250" y1="680" x2="150" y2="750" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="150" y1="770" x2="190" y2="765" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="280" y1="765" x2="290" y2="765" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="380" y1="765" x2="390" y2="765" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="480" y1="765" x2="490" y2="765" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="580" y1="765" x2="590" y2="765" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="640" y1="780" x2="450" y2="840" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
</svg>
