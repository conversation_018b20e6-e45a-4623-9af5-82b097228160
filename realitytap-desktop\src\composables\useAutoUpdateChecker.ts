import { ref, onUnmounted } from "vue";
import { getUpdaterConfig } from "@/config/updater.config";
import { useUpdateNotificationStore } from "@/stores/update-notification-store";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 自动更新检查服务
 * 提供后台自动检查更新的功能
 */
export function useAutoUpdateChecker() {
  // === 状态 ===
  const isRunning = ref(false);
  const isChecking = ref(false);
  const lastError = ref<string | null>(null);
  const retryCount = ref(0);

  // === 私有变量 ===
  let checkTimer: ReturnType<typeof setTimeout> | null = null;
  let retryTimer: ReturnType<typeof setTimeout> | null = null;

  // === 配置和依赖 ===
  const config = getUpdaterConfig();
  const updateStore = useUpdateNotificationStore();

  // === 方法 ===

  /**
   * 执行更新检查
   */
  const performCheck = async (silent: boolean = true): Promise<boolean> => {
    if (isChecking.value) {
      logger.debug(LogModule.GENERAL, "更新检查已在进行中，跳过本次检查");
      return false;
    }

    isChecking.value = true;
    lastError.value = null;

    try {
      logger.info(LogModule.GENERAL, "[自动检查] 开始检查更新");

      // 动态导入 useUpdater 以避免循环依赖
      const { useUpdater } = await import("@/composables/useUpdater");
      const updater = useUpdater();

      // 使用指定的静默模式检查更新
      const updateResult = await updater.checkForUpdates(silent);

      if (updateResult) {
        logger.info(LogModule.GENERAL, "[自动检查] 发现新版本", { version: updateResult.version });

        // 更新通知状态
        updateStore.setUpdateAvailable({
          version: updateResult.version,
          date: updateResult.date,
          body: updateResult.body || "",
          file_size: updateResult.file_size || 0,
        });

        // 如果是强制更新，立即显示强制更新对话框
        if (updateResult.force_update) {
          logger.warn(LogModule.GENERAL, "[自动检查] 检测到强制更新，立即显示强制更新对话框");
          // 动态导入全局更新管理器
          const { useGlobalUpdate } = await import("@/composables/useGlobalUpdate");
          const globalUpdate = useGlobalUpdate();
          globalUpdate.showForceUpdate();
        }

        // 发现新版本后停止自动检查
        stop();

        retryCount.value = 0;
        return true;
      } else {
        logger.info(LogModule.GENERAL, "[自动检查] 当前已是最新版本");
        updateStore.updateLastCheckTime();
        retryCount.value = 0;
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "检查更新失败";
      lastError.value = errorMessage;
      retryCount.value++;

      logger.error(LogModule.GENERAL, `[自动检查] 检查更新失败 (重试 ${retryCount.value}/${config.maxRetries})`, error);

      // 如果达到最大重试次数，停止检查一段时间
      if (retryCount.value >= config.maxRetries) {
        logger.warn(LogModule.GENERAL, "[自动检查] 达到最大重试次数，暂停检查");
        scheduleRetryAfterDelay();
      }

      return false;
    } finally {
      isChecking.value = false;
    }
  };

  /**
   * 安排下次检查
   */
  const scheduleNextCheck = () => {
    if (!isRunning.value) return;

    if (checkTimer) {
      clearTimeout(checkTimer);
    }
    checkTimer = setTimeout(() => {
      if (isRunning.value) {
        performCheck().then(() => {
          // 如果检查成功且没有发现新版本，继续安排下次检查
          if (isRunning.value && !updateStore.hasNewVersion) {
            scheduleNextCheck();
          }
        });
      }
    }, config.checkInterval);

    logger.debug(LogModule.GENERAL, "[自动检查] 已安排下次检查", {
      intervalMinutes: config.checkInterval / 1000 / 60
    });
  };

  /**
   * 在延迟后安排重试
   */
  const scheduleRetryAfterDelay = () => {
    const retryDelay = config.checkInterval * 2; // 失败后延迟更长时间重试

    if (retryTimer) {
      clearTimeout(retryTimer);
    }
    retryTimer = setTimeout(() => {
      if (isRunning.value) {
        retryCount.value = 0; // 重置重试计数
        scheduleNextCheck();
      }
    }, retryDelay);

    logger.debug(LogModule.GENERAL, "[自动检查] 将在指定时间后重试", {
      retryDelayMinutes: retryDelay / 1000 / 60
    });
  };

  /**
   * 启动自动检查服务
   */
  const start = async () => {
    if (isRunning.value) {
      logger.warn(LogModule.GENERAL, "[自动检查] 服务已在运行中");
      return;
    }

    if (!config.autoCheck || !config.backgroundCheck) {
      logger.warn(LogModule.GENERAL, "[自动检查] 自动检查已禁用");
      return;
    }

    logger.info(LogModule.GENERAL, "[自动检查] 启动自动更新检查服务", {
      checkIntervalMinutes: config.checkInterval / 1000 / 60,
      maxRetries: config.maxRetries,
      timeoutSeconds: config.timeout / 1000,
    });

    isRunning.value = true;
    retryCount.value = 0;
    lastError.value = null;

    // 总是在启动时执行一次检查，确保获取最新的版本信息
    logger.info(LogModule.GENERAL, "[自动检查] 启动时立即执行一次检查");
    const hasUpdate = await performCheck(false); // 启动时使用非静默模式，便于调试

    // 如果没有发现新版本且服务仍在运行，安排后续检查
    if (!hasUpdate && isRunning.value) {
      scheduleNextCheck();
    }
  };

  /**
   * 停止自动检查服务
   */
  const stop = () => {
    if (!isRunning.value) {
      return;
    }

    logger.info(LogModule.GENERAL, "[自动检查] 停止自动更新检查服务");

    isRunning.value = false;
    isChecking.value = false;

    if (checkTimer) {
      clearTimeout(checkTimer);
      checkTimer = null;
    }

    if (retryTimer) {
      clearTimeout(retryTimer);
      retryTimer = null;
    }
  };

  /**
   * 重启服务（用于配置更改后）
   */
  const restart = async () => {
    stop();
    await start();
  };

  /**
   * 获取服务状态
   */
  const getStatus = () => {
    return {
      isRunning: isRunning.value,
      isChecking: isChecking.value,
      lastError: lastError.value,
      retryCount: retryCount.value,
      nextCheckIn: checkTimer ? config.checkInterval : null,
    };
  };

  // === 清理 ===
  onUnmounted(() => {
    stop();
  });

  return {
    // 状态
    isRunning,
    isChecking,
    lastError,
    retryCount,

    // 方法
    start,
    stop,
    restart,
    getStatus,
    performCheck,
  };
}
