#!/usr/bin/env node

/**
 * 安全清理未使用的国际化翻译键
 * 基于实际使用情况，只删除确实未使用的键，保留所有实际使用的键
 */

const fs = require('fs');
const path = require('path');

// 加载实际使用的键
const usedKeysFile = path.join(__dirname, 'used-i18n-keys.json');
if (!fs.existsSync(usedKeysFile)) {
  console.error('❌ 请先运行 find-used-i18n-keys.cjs 生成使用键列表');
  process.exit(1);
}

const { allUsedKeys } = JSON.parse(fs.readFileSync(usedKeysFile, 'utf8'));
const usedKeysSet = new Set(allUsedKeys);

// 语言文件路径
const localeFiles = {
  'zh-CN': path.join(__dirname, '../src/locales/zh-CN.ts'),
  'en-US': path.join(__dirname, '../src/locales/en-US.ts'),
  'ja-JP': path.join(__dirname, '../src/locales/ja-JP.ts'),
  'ko-KR': path.join(__dirname, '../src/locales/ko-KR.ts'),
};

// 需要保留的键（即使未在代码中直接使用）
const KEEP_KEYS = new Set([
  // 更新相关的核心键
  'update.confirmInstallation',
  'update.installationNotice',
  'update.applicationWillClose',
  'update.error',
  'update.installingMessage',
  'update.cancelling',
  
  // 进程管理相关（可能在更新流程中使用）
  'update.processManagement.title',
  'update.processManagement.warningTitle',
  'update.processManagement.warningMessage',
  'update.processManagement.processListTitle',
  'update.processManagement.closeAndInstall',
  'update.processManagement.closeStrategyTitle',
  'update.processManagement.gracefulClose',
  'update.processManagement.gracefulCloseDesc',
  'update.processManagement.forceClose',
  'update.processManagement.forceCloseDesc',
  'update.processManagement.critical',
  'update.processManagement.noticeTitle',
  'update.processManagement.notice1',
  'update.processManagement.notice2',
  'update.processManagement.notice3',
  
  // 进程类型
  'update.processManagement.processTypes.MainApplication',
  'update.processManagement.processTypes.EditorWindow',
  'update.processManagement.processTypes.RenderProcess',
  'update.processManagement.processTypes.AudioService',
  'update.processManagement.processTypes.FileMonitor',
  'update.processManagement.processTypes.BackgroundService',
  'update.processManagement.processTypes.ChildProcess',
  'update.processManagement.processTypes.Unknown',
  
  // 安装器相关（可能在独立安装器中使用）
  'installer.ready',
  'installer.preparing',
  'installer.installing',
  'installer.success',
  'installer.failed',
  'installer.cancelled',
  'installer.initializing',
  'installer.installSuccess',
  'installer.unknownError',
  'installer.preparingExit',
  'installer.cancelling',
  'installer.installCancelled',
  
  // 安装器操作
  'installer.operations.initializing',
  'installer.operations.validating',
  'installer.operations.waitingForExit',
  'installer.operations.backingUp',
  'installer.operations.installing',
  'installer.operations.restarting',
  'installer.operations.completed',
  'installer.operations.failed',
  'installer.operations.cancelled',
  
  // 常用的通用键
  'common.retry',
  'common.dismiss',
]);

// 提取翻译键的函数
function extractKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

// 加载语言文件
function loadLocaleFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const match = content.match(/export default\s+({[\s\S]*})\s*(?:as const)?;?\s*$/);
    if (!match) {
      throw new Error('无法解析语言文件格式');
    }
    
    const objStr = match[1];
    const obj = new Function('return ' + objStr)();
    
    return obj;
  } catch (error) {
    console.error(`❌ 加载语言文件失败: ${filePath}`);
    console.error(`   错误: ${error.message}`);
    return null;
  }
}

// 检查键是否应该保留
function shouldKeepKey(key) {
  return usedKeysSet.has(key) || KEEP_KEYS.has(key);
}

// 移除未使用的键
function removeUnusedKeys(obj, prefix = '', removedKeys = []) {
  const result = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      // 递归处理嵌套对象
      const nestedResult = removeUnusedKeys(value, fullKey, removedKeys);
      if (Object.keys(nestedResult).length > 0) {
        result[key] = nestedResult;
      } else {
        // 检查是否有任何子键被使用
        const hasUsedChildren = extractKeys(value, fullKey).some(childKey => shouldKeepKey(childKey));
        if (hasUsedChildren) {
          result[key] = nestedResult; // 保留空对象结构
        } else {
          removedKeys.push(fullKey + '.*');
        }
      }
    } else {
      // 检查是否使用
      if (shouldKeepKey(fullKey)) {
        result[key] = value;
      } else {
        removedKeys.push(fullKey);
      }
    }
  }
  
  return result;
}

// 格式化对象为 TypeScript 代码
function formatToTypeScript(obj, indent = 0) {
  const spaces = '  '.repeat(indent);
  let result = '{\n';
  
  const entries = Object.entries(obj);
  for (let i = 0; i < entries.length; i++) {
    const [key, value] = entries[i];
    const isLast = i === entries.length - 1;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      result += `${spaces}  // === ${key} ===\n`;
      result += `${spaces}  ${key}: ${formatToTypeScript(value, indent + 1)}`;
    } else {
      const escapedValue = typeof value === 'string' 
        ? `"${value.replace(/"/g, '\\"')}"` 
        : JSON.stringify(value);
      result += `${spaces}  ${key}: ${escapedValue}`;
    }
    
    if (!isLast) {
      result += ',\n';
    } else {
      result += '\n';
    }
  }
  
  result += `${spaces}}`;
  return result;
}

// 主清理函数
function safeCleanupUnusedKeys() {
  console.log('🧹 开始安全清理未使用的国际化翻译键...\n');
  console.log(`📋 保护策略:`);
  console.log(`   - 保留所有实际使用的键 (${allUsedKeys.length} 个)`);
  console.log(`   - 保留关键系统键 (${KEEP_KEYS.size} 个)`);
  console.log(`   - 删除确实未使用的键\n`);
  
  for (const [locale, filePath] of Object.entries(localeFiles)) {
    console.log(`📝 处理 ${locale} 语言文件...`);
    
    const originalData = loadLocaleFile(filePath);
    if (!originalData) {
      continue;
    }
    
    const originalKeys = extractKeys(originalData);
    const removedKeys = [];
    
    // 移除未使用的键
    const cleanedData = removeUnusedKeys(originalData, '', removedKeys);
    const cleanedKeys = extractKeys(cleanedData);
    
    console.log(`   原始键数量: ${originalKeys.length}`);
    console.log(`   清理后键数量: ${cleanedKeys.length}`);
    console.log(`   移除键数量: ${removedKeys.length}`);
    
    if (removedKeys.length > 0) {
      console.log(`   移除的键 (前10个):`);
      removedKeys.slice(0, 10).forEach(key => {
        console.log(`     - ${key}`);
      });
      if (removedKeys.length > 10) {
        console.log(`     ... 还有 ${removedKeys.length - 10} 个键`);
      }
    }
    
    // 生成新的文件内容
    const header = `/**
 * ${locale === 'zh-CN' ? '简体中文语言包' : 
      locale === 'en-US' ? 'English Language Pack' :
      locale === 'ja-JP' ? '日本語言語パック' :
      locale === 'ko-KR' ? '한국어 언어팩' : '语言包'}
 */

export default `;
    
    const newContent = header + formatToTypeScript(cleanedData) + ' as const;\n';
    
    // 备份原文件
    const backupPath = filePath + '.backup-' + Date.now();
    fs.copyFileSync(filePath, backupPath);
    console.log(`   ✅ 原文件已备份到: ${path.basename(backupPath)}`);
    
    // 写入新文件
    fs.writeFileSync(filePath, newContent);
    console.log(`   ✅ 已更新语言文件\n`);
  }
  
  console.log('🎉 安全清理完成！');
  console.log('\n📋 清理摘要:');
  console.log('   - 所有原始文件已备份（带时间戳）');
  console.log('   - 保留了所有实际使用的翻译键');
  console.log('   - 保留了关键系统键');
  console.log('   - 删除了确实未使用的键');
  console.log('\n⚠️  注意: 请测试应用以确保没有遗漏重要的翻译键');
}

// 运行清理
if (require.main === module) {
  safeCleanupUnusedKeys();
}

module.exports = { safeCleanupUnusedKeys };
