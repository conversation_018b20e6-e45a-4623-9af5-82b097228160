import { config } from '@/config/server.config';
import { PlatformRelease, ServerVersionInfo, VersionsConfig } from '@/types/server.types';
import { FileUtil } from '@/utils/file.util';
import { logger } from '@/utils/logger.util';
import { VersionUtil } from '@/utils/version.util';
import fs from 'fs-extra';
import path from 'path';

/**
 * 版本同步服务
 * 负责将单个版本元数据文件同步到全局的versions.json配置文件
 */
export class VersionSyncService {
  private readonly versionsConfigPath: string;
  private readonly versionsMetadataPath: string;
  private syncInProgress = false;
  private readonly syncQueue: Array<() => Promise<void>> = [];

  constructor() {
    this.versionsConfigPath = path.join(config.storage.metadataPath, 'versions.json');
    this.versionsMetadataPath = path.join(config.storage.metadataPath, 'versions');
  }

  /**
   * 同步版本配置
   * 扫描所有单个版本元数据文件，重新构建versions.json
   */
  async syncVersionsConfig(): Promise<void> {
    return this.queueSync(() => this.doSyncVersionsConfig());
  }

  /**
   * 延迟同步版本配置
   * 在文件操作完成后稍等一下再同步，避免文件还未完全写入的问题
   */
  async syncVersionsConfigDelayed(delayMs: number = 500): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(async () => {
        try {
          await this.syncVersionsConfig();
          resolve();
        } catch (error) {
          reject(error);
        }
      }, delayMs);
    });
  }

  /**
   * 队列化同步操作，防止并发冲突
   */
  private async queueSync(syncOperation: () => Promise<void>): Promise<void> {
    return new Promise((resolve, reject) => {
      this.syncQueue.push(async () => {
        try {
          await syncOperation();
          resolve();
        } catch (error) {
          reject(error);
        }
      });

      this.processSyncQueue();
    });
  }

  /**
   * 处理同步队列
   */
  private async processSyncQueue(): Promise<void> {
    if (this.syncInProgress || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;

    try {
      while (this.syncQueue.length > 0) {
        const operation = this.syncQueue.shift();
        if (operation) {
          await operation();
        }
      }
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 实际执行同步配置的方法
   */
  private async doSyncVersionsConfig(): Promise<void> {
    try {
      logger.info('Starting version config synchronization', {
        versionsConfigPath: this.versionsConfigPath,
        versionsMetadataPath: this.versionsMetadataPath,
      });

      // 确保目录存在
      await FileUtil.ensureDir(path.dirname(this.versionsConfigPath));
      await FileUtil.ensureDir(this.versionsMetadataPath);

      // 读取现有配置以保留minimumVersions和deprecatedVersions
      const existingConfig = await this.getExistingConfig();
      logger.debug('Existing config loaded', {
        hasChannels: !!existingConfig.channels,
        channelCount: Object.keys(existingConfig.channels || {}).length,
        hasMinimumVersions: !!existingConfig.minimumVersions,
        hasDeprecatedVersions: !!existingConfig.deprecatedVersions,
      });

      // 扫描所有版本元数据文件
      const versionMetadataFiles = await this.scanVersionMetadataFiles();
      logger.debug('Metadata files scanned', {
        fileCount: versionMetadataFiles.length,
        files: versionMetadataFiles,
      });

      // 构建新的渠道配置
      const newChannels = await this.buildChannelsFromMetadata(versionMetadataFiles);
      logger.debug('Channels built from metadata', {
        channelCount: Object.keys(newChannels).length,
        channels: Object.keys(newChannels),
      });

      // 构建新的配置
      const newConfig: VersionsConfig = {
        channels: newChannels,
        minimumVersions: existingConfig.minimumVersions || {},
        deprecatedVersions: existingConfig.deprecatedVersions || [],
      };

      // 写入新配置
      logger.debug('Writing new config to file', {
        configPath: this.versionsConfigPath,
        configSize: JSON.stringify(newConfig).length,
      });

      await FileUtil.writeJSONAtomic(this.versionsConfigPath, newConfig);

      // 清除版本服务缓存，确保下次读取时获取最新配置
      try {
        const { getVersionService } = await import('@/services/service-factory');
        const versionService = getVersionService();
        versionService.clearCache();
        logger.debug('Version service cache cleared after sync');
      } catch (cacheError) {
        logger.warn('Failed to clear version service cache', { error: cacheError });
      }

      logger.info('Version config synchronized successfully', {
        channelCount: Object.keys(newChannels).length,
        totalVersions: this.countTotalVersions(newChannels),
        configPath: this.versionsConfigPath,
      });
    } catch (error) {
      logger.error('Failed to sync version config', {
        error:
          error instanceof Error
            ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
              }
            : error,
        versionsConfigPath: this.versionsConfigPath,
        versionsMetadataPath: this.versionsMetadataPath,
      });
      throw new Error(`Version sync failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取现有配置
   */
  private async getExistingConfig(): Promise<Partial<VersionsConfig>> {
    try {
      if (await FileUtil.exists(this.versionsConfigPath)) {
        return await fs.readJSON(this.versionsConfigPath);
      }
    } catch (error) {
      logger.warn('Failed to read existing config, using defaults', { error });
    }

    return {
      channels: {},
      minimumVersions: {},
      deprecatedVersions: [],
    };
  }

  /**
   * 扫描版本元数据文件
   */
  private async scanVersionMetadataFiles(): Promise<string[]> {
    try {
      if (!(await FileUtil.exists(this.versionsMetadataPath))) {
        logger.info('Versions metadata directory does not exist, creating empty config');
        return [];
      }

      const files = await fs.readdir(this.versionsMetadataPath);
      return files.filter(file => file.endsWith('.json'));
    } catch (error) {
      logger.error('Failed to scan version metadata files', { error });
      return [];
    }
  }

  /**
   * 从元数据文件构建渠道配置
   */
  private async buildChannelsFromMetadata(metadataFiles: string[]): Promise<Record<string, ServerVersionInfo>> {
    const channels: Record<string, ServerVersionInfo> = {};

    for (const file of metadataFiles) {
      try {
        const metadataPath = path.join(this.versionsMetadataPath, file);
        const metadata = await fs.readJSON(metadataPath);

        // 验证元数据格式
        if (!this.isValidMetadata(metadata)) {
          logger.warn('Invalid metadata format, skipping', { file, metadata });
          continue;
        }

        // 检查对应的发布文件是否存在
        const releaseFilePath = await this.findReleaseFile(metadata.filename, metadata.channel);
        if (!releaseFilePath) {
          logger.warn('Release file not found, skipping metadata', {
            file,
            filename: metadata.filename,
            channel: metadata.channel,
          });
          continue;
        }

        // 获取文件统计信息
        const stats = await fs.stat(releaseFilePath);

        // 构建平台发布信息
        const platformRelease: PlatformRelease = {
          filename: metadata.filename,
          size: stats.size,
          checksum: metadata.checksum,
          releaseDate: metadata.uploadTime || stats.birthtime.toISOString(),
          releaseNotes: metadata.releaseNotes || `Release ${metadata.version}`,
        };

        // 添加签名信息（如果存在）
        if (metadata.signature) {
          (platformRelease as any).signature = metadata.signature;
        }

        // 添加到对应的渠道
        this.addToChannel(channels, metadata, platformRelease);
      } catch (error) {
        logger.error('Failed to process metadata file', { file, error });
        continue;
      }
    }

    // 对每个渠道的版本进行排序，选择最新版本
    this.sortAndSelectLatestVersions(channels);

    return channels;
  }

  /**
   * 验证元数据格式
   */
  private isValidMetadata(metadata: any): boolean {
    return (
      metadata &&
      typeof metadata.filename === 'string' &&
      typeof metadata.version === 'string' &&
      typeof metadata.platform === 'string' &&
      typeof metadata.architecture === 'string' &&
      typeof metadata.channel === 'string' &&
      typeof metadata.checksum === 'string'
    );
  }

  /**
   * 查找发布文件
   */
  private async findReleaseFile(filename: string, channel: string): Promise<string | null> {
    // 首先在指定渠道目录中查找
    const channelPath = path.join(config.storage.releasesPath, channel, filename);
    if (await FileUtil.exists(channelPath)) {
      return channelPath;
    }

    // 然后在根目录查找（兼容旧版本）
    const rootPath = path.join(config.storage.releasesPath, filename);
    if (await FileUtil.exists(rootPath)) {
      return rootPath;
    }

    // 在所有渠道目录中查找
    const channels = ['stable', 'beta', 'alpha'];
    for (const ch of channels) {
      const chPath = path.join(config.storage.releasesPath, ch, filename);
      if (await FileUtil.exists(chPath)) {
        return chPath;
      }
    }

    return null;
  }

  /**
   * 添加到渠道配置
   */
  private addToChannel(
    channels: Record<string, ServerVersionInfo>,
    metadata: any,
    platformRelease: PlatformRelease,
  ): void {
    const { channel, version, platform, architecture } = metadata;

    try {
      // 初始化渠道
      if (!channels[channel]) {
        channels[channel] = {
          version,
          platforms: {},
        };
      }

      // 初始化平台
      if (!channels[channel].platforms[platform]) {
        channels[channel].platforms[platform] = {};
      }

      // 添加架构信息
      channels[channel].platforms[platform][architecture] = platformRelease;

      // 更新渠道版本（选择最新版本）
      try {
        // 验证版本格式
        if (VersionUtil.isValid(version) && VersionUtil.isValid(channels[channel].version)) {
          if (VersionUtil.hasUpdate(channels[channel].version, version)) {
            channels[channel].version = version;
          }
        } else {
          // 如果版本格式无效，使用字符串比较作为后备
          logger.warn('Invalid version format detected, using string comparison', {
            currentVersion: channels[channel].version,
            newVersion: version,
            channel,
            platform,
            architecture,
          });

          if (version > channels[channel].version) {
            channels[channel].version = version;
          }
        }
      } catch (versionError) {
        logger.error('Version comparison failed, keeping current version', {
          error: versionError,
          currentVersion: channels[channel].version,
          newVersion: version,
          channel,
          platform,
          architecture,
        });
      }
    } catch (error) {
      logger.error('Failed to add to channel', {
        error,
        metadata,
        channel,
        platform,
        architecture,
      });
      throw error;
    }
  }

  /**
   * 排序并选择最新版本
   */
  private sortAndSelectLatestVersions(channels: Record<string, ServerVersionInfo>): void {
    for (const channel of Object.values(channels)) {
      // 这里可以添加更复杂的版本选择逻辑
      // 目前已经在addToChannel中处理了版本选择
    }
  }

  /**
   * 计算总版本数
   */
  private countTotalVersions(channels: Record<string, ServerVersionInfo>): number {
    let count = 0;
    for (const channel of Object.values(channels)) {
      for (const platform of Object.values(channel.platforms)) {
        count += Object.keys(platform).length;
      }
    }
    return count;
  }

  /**
   * 验证并修复配置文件
   * 确保配置文件存在且格式正确
   */
  async validateAndRepairConfig(): Promise<void> {
    try {
      if (!(await FileUtil.exists(this.versionsConfigPath))) {
        logger.info('Versions config file does not exist, creating default');
        await this.syncVersionsConfig();
        return;
      }

      // 验证配置文件格式
      const config = await fs.readJSON(this.versionsConfigPath);
      if (!config.channels || typeof config.channels !== 'object') {
        logger.warn('Invalid config format detected, rebuilding');
        await this.syncVersionsConfig();
        return;
      }

      logger.info('Versions config validation passed');
    } catch (error) {
      logger.error('Config validation failed, rebuilding', { error });
      await this.syncVersionsConfig();
    }
  }

  /**
   * 清理孤立的元数据文件
   * 删除没有对应发布文件的元数据文件
   */
  async cleanupOrphanedMetadata(): Promise<void> {
    try {
      const metadataFiles = await this.scanVersionMetadataFiles();
      let cleanedCount = 0;

      for (const file of metadataFiles) {
        try {
          const metadataPath = path.join(this.versionsMetadataPath, file);
          const metadata = await fs.readJSON(metadataPath);

          if (!this.isValidMetadata(metadata)) {
            continue;
          }

          const releaseFilePath = await this.findReleaseFile(metadata.filename, metadata.channel);
          if (!releaseFilePath) {
            logger.info('Removing orphaned metadata file', { file, filename: metadata.filename });
            await fs.remove(metadataPath);
            cleanedCount++;
          }
        } catch (error) {
          logger.error('Failed to process metadata file during cleanup', { file, error });
        }
      }

      if (cleanedCount > 0) {
        logger.info('Cleaned up orphaned metadata files', { count: cleanedCount });
        // 重新同步配置
        await this.syncVersionsConfig();
      }
    } catch (error) {
      logger.error('Failed to cleanup orphaned metadata', { error });
    }
  }
}

// 导出单例实例
export const versionSyncService = new VersionSyncService();
