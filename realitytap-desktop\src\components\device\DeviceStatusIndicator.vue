<template>
  <div class="device-status-indicator">
    <div class="status-dot" :class="statusClass" :style="{ backgroundColor: statusColor }"></div>
    <span class="status-text">{{ statusLabel }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { DeviceStatus } from "@/types/device-types";
import { DEVICE_STATUS_COLORS, getDeviceStatusKey } from "@/utils/device/deviceConstants";
import { useI18n } from "@/composables/useI18n";

// === Props ===
interface Props {
  status: DeviceStatus;
  showText?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showText: true,
});

// === 组合函数 ===
const { t } = useI18n();

// === 计算属性 ===
const statusLabel = computed(() => t(getDeviceStatusKey(props.status)));

const statusColor = computed(() => DEVICE_STATUS_COLORS[props.status]);

const statusClass = computed(() => ({
  "status-dot--connected": props.status === "connected",
  "status-dot--connecting": props.status === "connecting" || props.status === "disconnecting",
  "status-dot--error": props.status === "error",
  "status-dot--disconnected": props.status === "disconnected",
  "status-dot--unknown": props.status === "unknown",
}));
</script>

<style scoped>
.device-status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-dot--connected {
  animation: pulse-success 2s infinite;
}

.status-dot--connecting {
  animation: pulse-info 1s infinite;
}

.status-dot--error {
  animation: pulse-error 1.5s infinite;
}

.status-text {
  color: var(--n-text-color-2);
  font-weight: 500;
}

@keyframes pulse-success {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 160, 88, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(24, 160, 88, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 160, 88, 0);
  }
}

@keyframes pulse-info {
  0% {
    box-shadow: 0 0 0 0 rgba(32, 128, 240, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(32, 128, 240, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(32, 128, 240, 0);
  }
}

@keyframes pulse-error {
  0% {
    box-shadow: 0 0 0 0 rgba(208, 48, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(208, 48, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(208, 48, 80, 0);
  }
}
</style>
