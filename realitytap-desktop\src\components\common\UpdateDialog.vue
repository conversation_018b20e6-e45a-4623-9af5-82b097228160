<template>
  <NModal
    :show="visible"
    @update:show="(value: boolean) => emit('update:visible', value)"
    preset="dialog"
    :title="dialogTitle"
    :closable="!updater.isInstalling.value"
    :mask-closable="!updater.isInstalling.value"
    :close-on-esc="!updater.isInstalling.value"
    :style="{ width: '520px', maxWidth: '90vw' }"
    class="update-dialog"
  >
    <div class="update-content">
      <!-- 更新信息展示 -->
      <div v-if="updateInfo && !updater.isDownloading.value && !updater.isInstalling.value" class="update-info">
        <div class="version-info">
          <h3 class="new-version">{{ getUpdateStatusTitle() }}</h3>
          <div class="version-details">
            <p class="version-item">
              <span class="version-label">📱 {{ t('update.currentVersion') }}:</span>
              <span class="version-value">{{ props.currentVersion }}</span>
            </p>
            <p class="version-item">
              <span class="version-label">🚀 {{ t('update.latestVersion') }}:</span>
              <span class="version-value">{{ updateInfo.version }}</span>
            </p>
            <p v-if="updateInfo.file_size && updateInfo.file_size > 0" class="version-item">
              <span class="version-label">📦 {{ t('update.fileSize') }}:</span>
              <span class="version-value">{{ formatFileSize(updateInfo.file_size) }}</span>
            </p>
          </div>
        </div>

        <!-- 下载完成提示 -->
        <div v-if="updater.isDownloaded.value" class="download-complete-notice">
          <NAlert type="success" :title="t('update.downloadComplete')">
            {{ t("update.downloadCompleteMessage") }}
          </NAlert>
        </div>

        <div v-if="updateInfo.body" class="release-notes">
          <h4 class="notes-title">📋 {{ t('update.releaseNotes') }}</h4>
          <NScrollbar class="notes-scrollbar">
            <div class="notes-content">{{ updateInfo.body }}</div>
          </NScrollbar>
        </div>
      </div>

      <!-- 下载/安装进度 -->
      <div v-if="updater.isDownloading.value || updater.isInstalling.value" class="progress-section">
        <div class="progress-header">
          <h3>{{ updater.isInstalling.value ? t("update.installing") : t("update.downloading") }}</h3>
          <span v-if="updater.isDownloading.value" class="progress-percentage">{{ downloadProgressPercentage }}%</span>
          <NSpin v-if="updater.isInstalling.value" size="small" />
        </div>

        <NProgress
          v-if="updater.isDownloading.value"
          type="line"
          :percentage="downloadProgressPercentage"
          :show-indicator="false"
          status="info"
          class="download-progress"
        />

        <div v-if="updater.isDownloading.value" class="progress-details">
          <div class="progress-stats">
            <span class="progress-stat">
              <span class="stat-value">{{
                formatFileSize(updater.downloadProgress.value.downloaded || 0)
              }}</span>
              <span class="stat-separator"> / </span>
              <span class="stat-total">{{
                formatFileSize(getExpectedFileSize())
              }}</span>
            </span>
          </div>
        </div>

        <p v-if="updater.isInstalling.value" class="install-message">
          {{ t("update.installingMessage") }}
        </p>
      </div>

      <!-- 错误信息 -->
      <div v-if="updater.error.value" class="error-section">
        <NAlert type="error" :title="t('update.error')">
          {{ updater.error.value }}
        </NAlert>
      </div>
    </div>

    <!-- 对话框按钮 -->
    <template #action>
      <div class="dialog-actions">
        <!-- 稍后提醒按钮 -->
        <NButton
          v-if="!updater.isProcessing.value"
          @click="handleRemindLater"
        >
          {{ t("update.remindLater") }}
        </NButton>

        <!-- 立即下载按钮 -->
        <NButton
          v-if="!updater.isProcessing.value && updater.canDownload.value"
          type="primary"
          @click="handleStartDownload"
        >
          {{ t("update.downloadNow") }}
        </NButton>

        <!-- 立即安装按钮 -->
        <NButton
          v-if="!updater.error.value && !updater.isProcessing.value && updater.canInstall.value"
          type="primary"
          @click="handleStartInstall"
        >
          {{ t("update.installNow") }}
        </NButton>
      </div>
    </template>
  </NModal>

  <!-- 安装确认对话框 -->
  <NModal
    v-model:show="showInstallConfirmDialog"
    preset="dialog"
    :title="t('update.confirmInstallation')"
    :positive-text="t('update.confirmInstall')"
    :negative-text="t('common.cancel')"
    @positive-click="handleInstallConfirm"
    @negative-click="handleInstallCancel"
  >
    <NAlert type="info" :title="t('update.installationNotice')">
      {{ t("update.applicationWillClose") }}
    </NAlert>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { NScrollbar } from "naive-ui";
import { useUpdateDialog, type UpdateDialogProps } from "@/composables/useUpdateDialog";
import { formatFileSize } from "@/utils/format";
import { logger, LogModule } from "@/utils/logger/logger";

// === Props ===
interface Props extends UpdateDialogProps {}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  updateInfo: null,
  currentVersion: "1.0.0",
});

// === Emits ===
const emit = defineEmits<{
  "update:visible": [value: boolean];
  "remind-later": [];
  "install-later": [];
}>();

// === 使用共享的更新对话框逻辑 ===
const {
  t,
  updater,
  downloadProgressPercentage,
  getExpectedFileSize,
  handleStartDownload,
  handleStartInstall,
} = useUpdateDialog(props);

// === 响应式状态 ===
const showInstallConfirmDialog = ref(false);

// === 普通更新特定的计算属性 ===
const dialogTitle = computed(() => {
  if (updater.isInstalling.value) return t("update.installing");
  if (updater.isDownloading.value) return t("update.downloading");
  if (updater.status.value === "available") return t("update.updateAvailable");
  return t("update.updateAvailable");
});

// === 普通更新特定的事件处理 ===
const getUpdateStatusTitle = (): string => {
  if (updater.isDownloaded.value) {
    return t("update.readyToInstall");
  }
  return t("update.newVersionAvailable");
};

const handleDownloadAndInstall = async () => {
  logger.info(LogModule.GENERAL, '开始下载并安装更新');
  await updater.downloadAndInstall();
};

const handleInstallConfirm = async () => {
  logger.info(LogModule.GENERAL, "用户确认安装更新");
  showInstallConfirmDialog.value = false;
  await handleDownloadAndInstall();
};

const handleInstallCancel = () => {
  logger.info(LogModule.GENERAL, "用户取消安装");
  showInstallConfirmDialog.value = false;
};

const handleRemindLater = () => {
  // 不完全重置状态，保持下载状态和更新信息
  // 只清除错误信息，让用户下次打开时能看到正确的状态
  updater.clearError();
  emit("remind-later");
  emit("update:visible", false);
};

</script>

<style scoped>
.update-content {
  padding: 16px 0;
}

.update-info {
  margin-bottom: 20px;
}

.version-info {
  margin-bottom: 16px;
}

.new-version {
  margin: 0 0 12px 0;
  color: #18a058;
  font-size: 18px;
  font-weight: 600;
}

/* 版本信息简约高亮样式 */
.version-item {
  margin: 10px 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.version-label {
  color: var(--n-text-color-2);
  font-weight: 500;
  min-width: 80px;
}

.version-value {
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background: var(--n-color-embedded);
  color: var(--n-text-color);
  border: 1px solid var(--n-divider-color);
}

.release-notes {
  margin-top: 16px;
  max-height: 300px;
  height: 300px;
}

/* 发布说明简约高亮样式 */
.notes-title {
  margin: 0 0 10px 0;
  font-size: 15px;
  font-weight: 600;
  color: var(--n-text-color);
}

.notes-scrollbar {
  max-height: 120px;
  border: 1px solid var(--n-divider-color);
  border-radius: 6px;
  background: var(--n-color-embedded);
}

/* 确保 Naive UI 滚动条样式正确应用 */
.notes-scrollbar :deep(.n-scrollbar-container) {
  /* 隐藏原生滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.notes-scrollbar :deep(.n-scrollbar-container::-webkit-scrollbar) {
  display: none;
}

.notes-scrollbar :deep(.n-scrollbar-rail) {
  right: 2px;
  width: 6px;
}

.notes-scrollbar :deep(.n-scrollbar-rail .n-scrollbar-rail__scrollbar) {
  border-radius: 3px;
  background: var(--n-scrollbar-color);
  transition: background-color 0.2s ease;
  width: 6px;
}

.notes-scrollbar :deep(.n-scrollbar-rail .n-scrollbar-rail__scrollbar:hover) {
  background: var(--n-scrollbar-color-hover);
}

.notes-content {
  padding: 14px;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-line; /* 保持换行符和空格 */
  color: var(--n-text-color);
}

.download-complete-notice {
  margin-top: 16px;
}

.forced-update-notice {
  margin-top: 16px;
}

.download-section,
.install-section {
  margin: 20px 0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.progress-percentage {
  font-size: 15px;
  font-weight: 600;
  color: #2e7d32;
  padding: 4px 10px;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  box-shadow: 0 1px 3px rgba(46, 125, 50, 0.1);
}

.download-progress {
  margin-bottom: 12px;
}

/* 下载进度简约高亮样式 */
.progress-details {
  font-size: 13px;
  color: var(--n-text-color-2);
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  flex-wrap: wrap;
}

.progress-stat {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  font-size: 12px;
}

.stat-label {
  color: var(--n-text-color-2);
}

.stat-value {
  font-weight: 600;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  color: var(--n-text-color);
}

.stat-value.speed,
.stat-value.eta {
  color: var(--n-primary-color);
}

.stat-separator {
  color: var(--n-text-color-3);
  font-weight: normal;
}

.stat-total {
  color: var(--n-text-color-2);
  font-weight: 500;
}

.install-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.install-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.install-message {
  margin: 8px 0 16px 0;
  font-size: 14px;
  color: #666;
}

/* 安装确认样式 */
.install-confirmation {
  margin: 20px 0;
}

.confirmation-header h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
}

.confirmation-content {
  margin-bottom: 16px;
}

.install-steps {
  margin-top: 16px;
}

.install-steps h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--n-text-color);
}

.install-steps ol {
  margin: 0;
  padding-left: 20px;
  font-size: 13px;
  color: var(--n-text-color-2);
  line-height: 1.6;
}

.install-steps li {
  margin-bottom: 4px;
}

/* 退出倒计时样式 */
.exit-countdown {
  margin: 12px 0;
  padding: 12px;
  background: #fff3e0;
  border-radius: 6px;
  border: 1px solid #ffb74d;
}

.exit-countdown p {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ef6c00;
  text-align: center;
}

.error-section,
.completed-section {
  margin: 16px 0;
}

.manual-install-hint {
  margin-top: 12px;
  padding: 12px;
  background: var(--n-color-embedded);
  border-radius: 6px;
  border-left: 3px solid var(--n-warning-color);
}

.hint-text {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: var(--n-text-color-2);
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 深色主题适配 */
:deep(.n-dialog.n-modal) {
  background: var(--n-color);
}
</style>
