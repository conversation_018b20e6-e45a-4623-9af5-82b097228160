import { h, type Ref, type VNode } from "vue";
import { NIcon, NInput, type TreeOption as NaiveTreeOption } from "naive-ui";
import { ChevronRight20Regular } from "@vicons/fluent";
import type { HapticFile } from "@/types/haptic-project";
import { getItemFromNodeKey } from "@/utils/tree/treeUtils";
import type { RealityTapProject } from "@/types/haptic-project";

/**
 * 树形渲染器 Composable
 * 负责处理树节点的自定义渲染逻辑
 */
export function useTreeRenderer(
  projectStore: { activeProject: RealityTapProject | null; isFileUnsaved: (fileUuid: string) => boolean },
  // 内联编辑状态
  editingNodeKey: Ref<string | null>,
  inlineEditValue: Ref<string>,
  inputRef: Ref<any>,
  // 新建分组状态
  isCreatingNewGroup: Ref<boolean>,
  newGroupTemporaryKey: Ref<string | null>,
  newGroupName: Ref<string>,
  // 编辑操作函数
  commitNewGroup: () => Promise<void>,
  cancelNewGroupCreation: () => void,
  commitGroupRename: () => Promise<void>,
  cancelGroupRename: () => void,
  commitFileRename: () => Promise<void>,
  cancelFileRename: () => void,
  message: { warning: (msg: string) => void },
  rightClickedNodeKey: Ref<string | null>,
  t: (key: string, values?: Record<string, any>) => string
) {
  /**
   * 渲染文件标签，包含未保存状态指示器
   */
  const renderFileLabel = (file: HapticFile): VNode => {
    const isUnsaved = projectStore.isFileUnsaved(file.fileUuid);
    return h(
      "span",
      {
        class: ["file-label", isUnsaved ? "file-label--unsaved" : ""],
        key: `${file.fileUuid}-${isUnsaved ? "unsaved" : "saved"}`, // 添加key确保VNode更新
      },
      [file.name]
    );
  };

  /**
   * 渲染树节点标签
   * 处理内联编辑、新建分组和默认显示三种状态
   */
  const renderLabel = ({ option }: { option: NaiveTreeOption }): VNode => {
    const key = String(option.key);
    const isNodeGroup = key.startsWith("group-");
    const isNodeFile = key.startsWith("file-");

    const isRightClicked = rightClickedNodeKey.value === key;

    // Priority 1: 新建分组输入框
    if (isCreatingNewGroup.value && option.key === newGroupTemporaryKey.value) {
      return h(NInput, {
        class: "inline-edit-input",
        ref: inputRef,
        value: newGroupName.value,
        onUpdateValue: (val) => (newGroupName.value = val),
        size: "small",
        placeholder: t('editor.inlineEdit.groupNamePlaceholder'),
        autofocus: true,
        onBlur: () => {
          if (newGroupName.value.trim()) {
            commitNewGroup();
          } else {
            cancelNewGroupCreation();
          }
        },
        onKeydown: (event: KeyboardEvent) => {
          if (event.key === "Enter") {
            event.preventDefault();
            if (newGroupName.value.trim()) {
              commitNewGroup();
            } else {
              message.warning(t('editor.inlineEdit.groupNameEmpty'));
              cancelNewGroupCreation();
            }
          }
          if (event.key === "Escape") {
            event.preventDefault();
            cancelNewGroupCreation();
          }
        },
      });
    }

    // Priority 2: 现有项目重命名输入框
    if (editingNodeKey.value === key && (isNodeGroup || isNodeFile)) {
      return h(NInput, {
        class: "inline-edit-input",
        ref: inputRef,
        value: inlineEditValue.value,
        onUpdateValue: (val) => (inlineEditValue.value = val),
        size: "small",
        placeholder: isNodeGroup ? t('editor.inlineEdit.newGroupPlaceholder') : t('editor.inlineEdit.newFileNamePlaceholder'),
        autofocus: true,
        onBlur: () => {
          if (isNodeGroup) {
            commitGroupRename();
          } else if (isNodeFile) {
            commitFileRename();
          }
        },
        onKeydown: (event: KeyboardEvent) => {
          if (event.key === "Enter") {
            event.preventDefault();
            if (isNodeGroup) {
              commitGroupRename();
            } else if (isNodeFile) {
              commitFileRename();
            }
          }
          if (event.key === "Escape") {
            event.preventDefault();
            if (isNodeGroup) {
              cancelGroupRename();
            } else if (isNodeFile) {
              cancelFileRename();
            }
          }
        },
      });
    }

    // Priority 3: 默认标签渲染
    if (isNodeFile) {
      // 对于文件节点，使用 renderFileLabel 来显示未保存状态
      const fileItem = getItemFromNodeKey(key, projectStore.activeProject) as HapticFile;
      if (fileItem) {
        const fileLabelVNode = renderFileLabel(fileItem);
        return h(
          "div",
          {
            class: { "right-click-indicator": isRightClicked },
          },
          [fileLabelVNode]
        );
      }
    }

    // 对于分组节点或其他情况，使用默认渲染
    return h(
      "span",
      {
        class: ["tree-node-label", { "right-click-indicator": isRightClicked }],
      },
      option.label || ""
    );
  };

  /**
   * 渲染树节点展开/折叠图标
   */
  const renderSwitcherIcon = ({ expanded, option }: { expanded: boolean; option: NaiveTreeOption }): VNode => {
    // 判断当前节点是否有子项
    const hasChildren = Array.isArray(option.children) && option.children.length > 0;
    return h(
      "span",
      {
        class: ["switcher-icon-container", expanded ? "expanded" : "", hasChildren ? "switcher-has-children" : "switcher-no-children"],
      },
      h(NIcon, null, { default: () => h(ChevronRight20Regular) })
    );
  };

  return {
    renderLabel,
    renderSwitcherIcon,
    renderFileLabel,
  };
}
