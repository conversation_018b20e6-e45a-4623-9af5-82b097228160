// Group-related data models
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Group {
    pub group_uuid: Uuid,
    pub name: String,
    pub path: String, // Added back as per plan
    #[serde(default)]
    pub description: String,
    pub parent_group_uuid: Option<Uuid>, // NEW: Link to parent group
}
