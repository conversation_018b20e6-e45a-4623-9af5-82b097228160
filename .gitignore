# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Debug files
*.pdb

# SQLite temporary files
*.db-shm
*.db-wal

# FFmpeg binaries (large files, will be copied during build)
src-tauri/ffmpeg.exe
src-tauri/ffprobe.exe
src-tauri/target/

# FFmpeg binaries for all platforms (downloaded during build)
realitytap-desktop/src-tauri/ffmpeg/linux/
realitytap-desktop/src-tauri/ffmpeg/macos/

__tests__
build-config.json