<script setup lang="ts">
import { Window } from "@tauri-apps/api/window";
import { ref, onMounted } from "vue";
import { useWindowSettingsStore } from "@/stores/window-settings-store";
import { useI18n } from "@/composables/useI18n";
import iconUrl from "@/assets/icon.png";
import GlobalDeviceStatusIndicator from "./GlobalDeviceStatusIndicator.vue";
import LanguageSwitcher from "./LanguageSwitcher.vue";
import AboutButton from "./AboutButton.vue";

// 国际化
const { app } = useI18n();

// 窗口实例
const appWindow = ref<Window | null>(null);
// 是否最大化
const isMaximized = ref(false);
// 窗口设置store
const windowSettingsStore = useWindowSettingsStore();

// 窗口控制函数
const minimize = async () => {
  if (appWindow.value) {
    await appWindow.value.minimize();
  }
};

const toggleMaximize = async () => {
  if (appWindow.value) {
    await appWindow.value.toggleMaximize();
    isMaximized.value = await appWindow.value.isMaximized();
  }
};

const close = async () => {
  if (appWindow.value) {
    // Save current window state before closing
    try {
      await windowSettingsStore.saveCurrentWindowState();
    } catch (error) {
      console.error("Failed to save window state before closing:", error);
    }
    await appWindow.value.close();
  }
};

// 双击标题栏最大化/还原
const handleDoubleClick = () => {
  toggleMaximize();
};

// 组件挂载时初始化
onMounted(async () => {
  const { getCurrentWindow } = await import("@tauri-apps/api/window");
  appWindow.value = await getCurrentWindow();

  // 监听窗口最大化状态
  if (appWindow.value) {
    appWindow.value.onResized(async () => {
      if (appWindow.value) {
        isMaximized.value = await appWindow.value.isMaximized();
      }
    });
  }
});
</script>

<template>
  <div class="titlebar" data-tauri-drag-region @dblclick="handleDoubleClick">
    <div class="titlebar-left" data-tauri-drag-region>
      <img class="app-icon" :src="iconUrl" :alt="app.title()" />
      <span class="app-title" data-tauri-drag-region>{{ app.title() }}</span>
    </div>
    <div class="titlebar-center" data-tauri-drag-region>
      <GlobalDeviceStatusIndicator :show-text="true" />
      <LanguageSwitcher :compact="true" />
      <AboutButton />
    </div>
    <div class="titlebar-right">
      <button class="titlebar-button" id="titlebar-minimize" @click="minimize">
        <svg
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2 6H10"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
          />
        </svg>
      </button>
      <button
        class="titlebar-button"
        id="titlebar-maximize"
        @click="toggleMaximize"
      >
        <svg
          v-if="!isMaximized"
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="2"
            y="2"
            width="8"
            height="8"
            stroke="currentColor"
            stroke-width="1"
          />
        </svg>
        <svg
          v-else
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M3 5V3H9V9H7" stroke="currentColor" stroke-width="1" />
          <rect
            x="2.5"
            y="5.5"
            width="5"
            height="5"
            stroke="currentColor"
            stroke-width="1"
          />
        </svg>
      </button>
      <button class="titlebar-button" id="titlebar-close" @click="close">
        <svg
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2 2L10 10"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
          />
          <path
            d="M10 2L2 10"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<style scoped>
.titlebar {
  height: 40px;
  max-height: 40px;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  /* border-bottom: 1px solid #333333; */
}

.titlebar-left {
  display: flex;
  align-items: center;
  padding-left: 12px;
  flex: 1;
}

.app-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  object-fit: contain;
}

.app-title {
  font-size: 14px;
  font-weight: 500;
  color: #e6e6e6;
}

.titlebar-center {
  flex: 2;
  display: flex;
  justify-content: right;
  align-items: center;
  padding: 0 12px;
}

.titlebar-right {
  display: flex;
  align-items: center;
}

.titlebar-button {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  border: none;
  color: #e6e6e6;
  cursor: pointer;
  outline: none;
  transition: background-color 0.2s;
}

.titlebar-button:hover {
  background: #3a3a3a;
}

#titlebar-close:hover {
  background: #e05c44;
}
</style>
