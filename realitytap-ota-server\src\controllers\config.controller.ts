import { authMiddleware } from '@/middleware/auth.middleware';
import { getConfigService } from '@/services/service-factory';
import { ErrorResponse, SuccessResponse } from '@/types/server.types';
import { ConfigUtil } from '@/utils/config.util';
import { logger } from '@/utils/logger.util';
import { NextFunction, Response, Router } from 'express';
import { z } from 'zod';

const router: Router = Router();

// 获取配置服务实例
const configService = getConfigService();

// 验证模式
const UpdateConfigRequestSchema = z.object({
  config: z.any(), // 使用 ConfigUtil 进行详细验证
  reason: z.string().optional(),
});

const ResetConfigRequestSchema = z.object({
  reason: z.string().optional(),
});

const HistoryQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(1000).optional().default(100),
});

/**
 * 获取当前系统配置
 * GET /api/v1/config
 */
router.get('/', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    logger.info('Getting system config', {
      userId: req.user?.username,
      ip: req.ip,
    });

    const systemConfig = await configService.getConfig();
    const sanitizedConfig = ConfigUtil.sanitizeConfig(systemConfig);

    const response: SuccessResponse = {
      success: true,
      data: {
        config: sanitizedConfig,
        metadata: {
          lastModified: new Date().toISOString(),
          version: '1.0.0',
        },
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get system config', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    next(error);
  }
});

/**
 * 更新系统配置
 * PUT /api/v1/config
 */
router.put('/', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    // 验证请求体
    const requestValidation = UpdateConfigRequestSchema.safeParse(req.body);
    if (!requestValidation.success) {
      const errorResponse: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_REQUEST_BODY',
          message: 'Invalid request body',
          details: requestValidation.error.errors,
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      return res.status(400).json(errorResponse);
    }

    const { config: newConfig, reason } = requestValidation.data;

    logger.info('Updating system config', {
      userId: req.user?.username,
      ip: req.ip,
      reason,
    });

    // 验证配置
    const validation = await configService.validateConfig(newConfig);
    if (!validation.isValid) {
      const errorResponse: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_CONFIGURATION',
          message: 'Invalid configuration',
          details: validation.errors,
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      return res.status(400).json(errorResponse);
    }

    // 更新配置
    const updatedConfig = await configService.updateConfig(newConfig, req.user?.username, reason);

    const response: SuccessResponse = {
      success: true,
      data: {
        config: ConfigUtil.sanitizeConfig(updatedConfig),
        message: 'Configuration updated successfully',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to update system config', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    return next(error);
  }
});

/**
 * 重置配置到默认值
 * POST /api/v1/config/reset
 */
router.post('/reset', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    // 验证请求体
    const requestValidation = ResetConfigRequestSchema.safeParse(req.body);
    if (!requestValidation.success) {
      const errorResponse: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_REQUEST_BODY',
          message: 'Invalid request body',
          details: requestValidation.error.errors,
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      return res.status(400).json(errorResponse);
    }

    const { reason } = requestValidation.data;

    logger.info('Resetting system config', {
      userId: req.user?.username,
      ip: req.ip,
      reason,
    });

    // 重置配置
    const defaultConfig = await configService.resetConfig(req.user?.username, reason);

    const response: SuccessResponse = {
      success: true,
      data: {
        config: ConfigUtil.sanitizeConfig(defaultConfig),
        message: 'Configuration reset to default values',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to reset system config', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    return next(error);
  }
});

/**
 * 获取默认配置
 * GET /api/v1/config/default
 */
router.get('/default', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    logger.info('Getting default config', {
      userId: req.user?.username,
      ip: req.ip,
    });

    const defaultConfig = configService.getDefaultConfig();

    const response: SuccessResponse = {
      success: true,
      data: {
        config: ConfigUtil.sanitizeConfig(defaultConfig),
        metadata: {
          description: 'Default system configuration',
          version: '1.0.0',
        },
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get default config', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    next(error);
  }
});

/**
 * 获取配置模式（用于前端验证）
 * GET /api/v1/config/schema
 */
router.get('/schema', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    logger.info('Getting config schema', {
      userId: req.user?.username,
      ip: req.ip,
    });

    const schema = configService.getConfigSchema();

    const response: SuccessResponse = {
      success: true,
      data: {
        schema,
        metadata: {
          description: 'Configuration validation schema',
          version: '1.0.0',
        },
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get config schema', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    next(error);
  }
});

/**
 * 获取配置变更历史
 * GET /api/v1/config/history
 */
router.get('/history', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    // 验证查询参数
    const queryValidation = HistoryQuerySchema.safeParse(req.query);
    if (!queryValidation.success) {
      const errorResponse: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_QUERY_PARAMETERS',
          message: 'Invalid query parameters',
          details: queryValidation.error.errors,
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      return res.status(400).json(errorResponse);
    }

    const { limit } = queryValidation.data;

    logger.info('Getting config history', {
      userId: req.user?.username,
      ip: req.ip,
      limit,
    });

    const history = await configService.getConfigHistory(limit);

    const response: SuccessResponse = {
      success: true,
      data: {
        history,
        total: history.length,
        limit,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get config history', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    return next(error);
  }
});

/**
 * 验证配置
 * POST /api/v1/config/validate
 */
router.post('/validate', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    const { config } = req.body;

    if (!config) {
      const errorResponse: ErrorResponse = {
        success: false,
        error: {
          code: 'MISSING_CONFIG',
          message: 'Configuration object is required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      return res.status(400).json(errorResponse);
    }

    logger.info('Validating config', {
      userId: req.user?.username,
      ip: req.ip,
    });

    const validation = await configService.validateConfig(config);

    const response: SuccessResponse = {
      success: true,
      data: {
        isValid: validation.isValid,
        errors: validation.errors,
        message: validation.isValid ? 'Configuration is valid' : 'Configuration has errors',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to validate config', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    return next(error);
  }
});

export { router as configController };
