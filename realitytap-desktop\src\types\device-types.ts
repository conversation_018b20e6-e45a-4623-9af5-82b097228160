// 设备管理相关类型定义

// 设备类型枚举
export enum DeviceType {
  USB = "usb",
  WIFI = "wifi",
  BLUETOOTH = "bluetooth",
}

// 连接类型枚举（与设备类型对应）
export enum ConnectionType {
  USB = "usb",
  WIFI = "wifi",
  BLUETOOTH = "bluetooth",
}

// 设备状态枚举
export enum DeviceStatus {
  CONNECTED = "connected",
  DISCONNECTED = "disconnected",
  CONNECTING = "connecting",
  DISCONNECTING = "disconnecting",
  ERROR = "error",
  UNKNOWN = "unknown",
}

// 设备元数据接口
export interface DeviceMetadata {
  manufacturer?: string;
  model?: string;
  version?: string;
  serialNumber?: string;
  capabilities?: string[];
  lastError?: string;
  connectionInfo?: {
    ipAddress?: string; // WIFI设备的IP地址
    port?: number; // WIFI设备的端口
    macAddress?: string; // 蓝牙设备的MAC地址
    usbPath?: string; // USB设备的路径
  };
}

// 设备接口
export interface Device {
  deviceId: string;
  name: string;
  type: DeviceType;
  connectionType: ConnectionType;
  status: DeviceStatus;
  lastConnected: string | null;
  lastDisconnected: string | null;
  isDefault: boolean;
  metadata: DeviceMetadata;
  createdAt: string;
  updatedAt: string;
}

// 设备连接配置
export interface DeviceConnectionConfig {
  deviceId: string;
  timeout?: number; // 连接超时时间（毫秒）
  retryCount?: number; // 重试次数
  autoReconnect?: boolean; // 是否自动重连
}

// 设备扫描选项
export interface DeviceScanOptions {
  types?: DeviceType[]; // 要扫描的设备类型
  timeout?: number; // 扫描超时时间（毫秒）
  includeDisconnected?: boolean; // 是否包含已断开的设备
}

// 设备事件类型
export enum DeviceEventType {
  DEVICE_DISCOVERED = "device_discovered",
  DEVICE_CONNECTED = "device_connected",
  DEVICE_DISCONNECTED = "device_disconnected",
  DEVICE_ERROR = "device_error",
  DEVICE_STATUS_CHANGED = "device_status_changed",
  SCAN_STARTED = "scan_started",
  SCAN_COMPLETED = "scan_completed",
  SCAN_ERROR = "scan_error",
}

// 设备事件接口
export interface DeviceEvent {
  type: DeviceEventType;
  deviceId?: string;
  device?: Device;
  error?: string;
  timestamp: string;
  data?: any;
}

// 设备操作结果
export interface DeviceOperationResult {
  success: boolean;
  deviceId: string;
  message?: string;
  error?: string;
  data?: any;
}

// 设备列表过滤选项
export interface DeviceListFilter {
  types?: DeviceType[];
  statuses?: DeviceStatus[];
  searchText?: string;
  showOnlyConnected?: boolean;
  showOnlyDefault?: boolean;
}

// 设备排序选项
export interface DeviceListSort {
  field: "name" | "type" | "status" | "lastConnected" | "createdAt";
  order: "asc" | "desc";
}

// 设备管理器配置
export interface DeviceManagerConfig {
  autoScanInterval?: number; // 自动扫描间隔（毫秒）
  maxDevices?: number; // 最大设备数量
  enableAutoReconnect?: boolean; // 是否启用自动重连
  connectionTimeout?: number; // 默认连接超时时间
  scanTimeout?: number; // 默认扫描超时时间
}

// 设备统计信息
export interface DeviceStatistics {
  totalDevices: number;
  connectedDevices: number;
  disconnectedDevices: number;
  errorDevices: number;
  devicesByType: Record<DeviceType, number>;
  lastScanTime: string | null;
  lastConnectionTime: string | null;
}

// 设备导入/导出格式
export interface DeviceExportData {
  version: string;
  exportTime: string;
  devices: Device[];
  defaultDeviceId: string | null;
}

// 设备验证规则
export interface DeviceValidationRules {
  nameMinLength: number;
  nameMaxLength: number;
  requiredFields: (keyof Device)[];
  allowedTypes: DeviceType[];
}

// 设备操作权限
export interface DevicePermissions {
  canConnect: boolean;
  canDisconnect: boolean;
  canDelete: boolean;
  canEdit: boolean;
  canSetDefault: boolean;
  canSendData: boolean;
}

// 设备组（用于设备分组管理）
export interface DeviceGroup {
  groupId: string;
  name: string;
  description?: string;
  deviceIds: string[];
  createdAt: string;
  updatedAt: string;
}
