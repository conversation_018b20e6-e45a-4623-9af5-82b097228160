/**
 * PlayEffectDialog 相关类型定义
 * Types for PlayEffectDialog component
 */

import type { Ref } from "vue";
import type { RenderableEvent } from "./haptic-editor";

// 播放状态枚举
export enum PlaybackState {
  STOPPED = "stopped",
  PLAYING = "playing",
  LOADING = "loading"
}

// 马达型号类型
export interface MotorModel {
  id: string;
  name: string;
  displayName: string;
  configPath: string;
  resonantFreq?: number;
  ratedF0: number; // 额定F0频率 (Hz)
  description?: string;
}

// 后端配置文件信息类型
export interface ConfigFileInfo {
  path: string;
  name: string;
  size: number;
  exists: boolean;
}

// 播放控制接口
export interface PlaybackControl {
  state: PlaybackState;
  currentTime: number;
  totalDuration: number;
  isLooping: boolean;
  volume: number;
}

// Canvas 绘制配置
export interface CanvasConfig {
  width: number;
  height: number;
  backgroundColor: string;
  gridColor: string;
  eventColor: string;
  timelineColor: string;
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

// 事件时间线数据
export interface EventTimelineData {
  events: RenderableEvent[];
  totalDuration: number;
  timeScale: number;
  amplitudeScale: number;
}

// Canvas 绘制上下文
export interface CanvasDrawingContext {
  canvas: HTMLCanvasElement;
  context: CanvasRenderingContext2D;
  config: CanvasConfig;
  data: EventTimelineData;
}

// 对话框属性接口
export interface PlayEffectDialogProps {
  visible: boolean;
  events: RenderableEvent[];
  totalDuration: number;
  currentFile?: {
    name: string;
    uuid: string;
    path: string;
  } | null;
  isLoading?: boolean;
  error?: string | null;
}

// 对话框事件接口
export interface PlayEffectDialogEmits {
  (e: "update:visible", value: boolean): void;
  (e: "play", motorId: string): void;
  (e: "stop"): void;
  (e: "motor-change", motorId: string): void;
}

// 播放控制方法接口
export interface PlaybackControlMethods {
  play: () => Promise<void>;
  stop: () => Promise<void>;
  setVolume: (volume: number) => void;
  setLooping: (loop: boolean) => void;
  seekTo: (time: number) => void;
}

// Canvas 绘制方法接口
export interface CanvasDrawingMethods {
  initCanvas: (canvas: HTMLCanvasElement) => void;
  resizeCanvas: (width: number, height: number) => void;
  clearCanvas: () => void;
  drawTimeline: () => void;
  drawEvents: (events: RenderableEvent[]) => void;
  drawPlayhead: (currentTime: number) => void;
  updateData: (data: EventTimelineData) => void;
}

// 马达选择方法接口
export interface MotorSelectionMethods {
  getAvailableMotors: () => MotorModel[];
  loadAvailableMotors: () => Promise<void>;
  selectMotor: (motorId: string) => void;
  getCurrentMotor: () => MotorModel | null;
}

// 组合函数返回类型
export interface UsePlayEffectDialogReturn {
  // 状态
  playbackControl: Ref<PlaybackControl>;
  selectedMotor: Ref<MotorModel | null>;
  canvasConfig: Ref<CanvasConfig>;
  availableMotors: Ref<MotorModel[]>;
  isLoadingMotors: Ref<boolean>;

  // 方法
  playbackMethods: PlaybackControlMethods;
  canvasMethods: CanvasDrawingMethods;
  motorMethods: MotorSelectionMethods;

  // 事件处理
  handleClose: () => void;
  handlePlay: () => Promise<void>;
  handleStop: () => Promise<void>;
  handleMotorChange: (motorId: string) => void;
}

// 默认马达型号列表（作为回退）
export const DEFAULT_MOTOR_MODELS: MotorModel[] = [
  {
    id: "lra_0809_normal_170hz",
    name: "LRA_0809_normal_170Hz",
    displayName: "LRA 0809 Normal 170Hz",
    configPath: "motors/LRA_0809_normal_170Hz.conf",
    resonantFreq: 170,
    ratedF0: 170,
    description: "标准线性马达，适用于大多数触觉效果"
  },
  {
    id: "lra_0809_pro_170hz",
    name: "LRA_0809_pro_170Hz",
    displayName: "LRA 0809 Pro 170Hz",
    configPath: "motors/LRA_0809_pro_170Hz.conf",
    resonantFreq: 170,
    ratedF0: 170,
    description: "专业版线性马达，适用于高质量触觉效果"
  },
  {
    id: "lra_0916_normal_150hz",
    name: "LRA_0916_normal_150Hz",
    displayName: "LRA 0916 Normal 150Hz",
    configPath: "motors/LRA_0916_normal_150Hz.conf",
    resonantFreq: 150,
    ratedF0: 150,
    description: "大尺寸线性马达，适用于强烈触觉反馈"
  }
];

// 默认 Canvas 配置
export const DEFAULT_CANVAS_CONFIG: CanvasConfig = {
  width: 800,
  height: 300,
  backgroundColor: "#ffffff",
  gridColor: "#e0e0e0",
  eventColor: "#1976d2",
  timelineColor: "#424242",
  padding: {
    top: 20,
    right: 20,
    bottom: 40,
    left: 60
  }
};

// 默认播放控制状态
export const DEFAULT_PLAYBACK_CONTROL: PlaybackControl = {
  state: PlaybackState.STOPPED,
  currentTime: 0,
  totalDuration: 0,
  isLooping: false,
  volume: 1.0
};
