#!/bin/bash

# RealityTap OTA Server Docker Deployment Script
# This script helps deploy the OTA server using Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_DIR="$PROJECT_ROOT/docker"

# Default values
MODE="http"
PROFILE=""
ENV_FILE=""
DETACHED=true
BUILD=false
PULL=false

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
RealityTap OTA Server Docker Deployment Script

Usage: $0 [OPTIONS]

Options:
    -m, --mode MODE         Deployment mode: http or https (default: http)
    -p, --profile PROFILE   Docker Compose profile to use (e.g., nginx)
    -e, --env-file FILE     Environment file to use (default: .env)
    -f, --foreground        Run in foreground (don't detach)
    -b, --build             Force rebuild of Docker images
    --pull                  Pull latest base images before building
    -h, --help              Show this help message

Examples:
    $0                                  # Deploy with HTTP mode
    $0 -m https                         # Deploy with HTTPS mode
    $0 -m http -p nginx                 # Deploy with HTTP mode and Nginx proxy
    $0 -m https -p nginx -b             # Deploy with HTTPS mode, Nginx proxy, and rebuild images
    $0 -e .env.production               # Use custom environment file

Environment Setup:
    1. Copy docker/.env.example to docker/.env
    2. Edit .env file with your configuration
    3. For HTTPS mode, ensure SSL certificates are available
    4. Run this script to deploy

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                MODE="$2"
                shift 2
                ;;
            -p|--profile)
                PROFILE="$2"
                shift 2
                ;;
            -e|--env-file)
                ENV_FILE="$2"
                shift 2
                ;;
            -f|--foreground)
                DETACHED=false
                shift
                ;;
            -b|--build)
                BUILD=true
                shift
                ;;
            --pull)
                PULL=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Validate arguments
validate_args() {
    if [[ "$MODE" != "http" && "$MODE" != "https" ]]; then
        log_error "Invalid mode: $MODE. Must be 'http' or 'https'"
        exit 1
    fi
    
    if [[ -n "$ENV_FILE" && ! -f "$ENV_FILE" ]]; then
        log_error "Environment file not found: $ENV_FILE"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_info "Prerequisites check passed"
}

# Setup environment
setup_environment() {
    log_info "Setting up environment..."
    
    cd "$DOCKER_DIR"
    
    # Use default .env file if not specified
    if [[ -z "$ENV_FILE" ]]; then
        ENV_FILE=".env"
    fi
    
    # Check if environment file exists
    if [[ ! -f "$ENV_FILE" ]]; then
        if [[ -f ".env.example" ]]; then
            log_warn "Environment file $ENV_FILE not found"
            log_info "Copying .env.example to $ENV_FILE"
            cp .env.example "$ENV_FILE"
            log_warn "Please edit $ENV_FILE with your configuration before deploying"
            log_warn "Especially set ADMIN_PASSWORD and JWT_SECRET!"
            exit 1
        else
            log_error "Environment file $ENV_FILE not found and no example file available"
            exit 1
        fi
    fi
    
    # Source environment file to validate required variables
    set -a
    source "$ENV_FILE"
    set +a

    # Create necessary host directories
    log_info "Creating host directories..."

    # Create storage directory
    STORAGE_PATH="${STORAGE_HOST_PATH:-./data/storage}"
    log_info "Creating storage directory: $STORAGE_PATH"
    mkdir -p "$STORAGE_PATH"/{database,releases,logs,temp,backup,metadata}

    # Create keys directory
    KEYS_PATH="${KEYS_HOST_PATH:-./keys}"
    log_info "Creating keys directory: $KEYS_PATH"
    mkdir -p "$KEYS_PATH"

    # Set proper permissions for container user (UID 1001)
    log_info "Setting directory permissions..."

    # Set ownership to match container user (UID 1001, GID 1001)
    if command -v chown >/dev/null 2>&1; then
        log_info "Setting ownership to UID 1001:GID 1001 for container compatibility..."
        sudo chown -R 1001:1001 "$STORAGE_PATH" 2>/dev/null || {
            log_warn "Failed to set ownership with sudo, trying without sudo..."
            chown -R 1001:1001 "$STORAGE_PATH" 2>/dev/null || {
                log_warn "Could not set ownership. Setting permissions to 777 as fallback..."
                chmod -R 777 "$STORAGE_PATH" 2>/dev/null || true
            }
        }
        sudo chown -R 1001:1001 "$KEYS_PATH" 2>/dev/null || {
            chown -R 1001:1001 "$KEYS_PATH" 2>/dev/null || {
                chmod -R 777 "$KEYS_PATH" 2>/dev/null || true
            }
        }
    else
        log_warn "chown command not available, setting permissions to 777..."
        chmod -R 777 "$STORAGE_PATH" 2>/dev/null || true
        chmod -R 777 "$KEYS_PATH" 2>/dev/null || true
    fi

    # Ensure directories are readable and writable
    chmod -R 755 "$STORAGE_PATH" 2>/dev/null || true
    chmod -R 755 "$KEYS_PATH" 2>/dev/null || true
    
    # Validate required environment variables
    if [[ -z "$ADMIN_PASSWORD" ]]; then
        log_error "ADMIN_PASSWORD is not set in $ENV_FILE"
        exit 1
    fi
    
    if [[ -z "$JWT_SECRET" ]]; then
        log_error "JWT_SECRET is not set in $ENV_FILE"
        exit 1
    fi
    
    if [[ ${#JWT_SECRET} -lt 32 ]]; then
        log_error "JWT_SECRET must be at least 32 characters long"
        exit 1
    fi
    
    # Additional validation for HTTPS mode
    if [[ "$MODE" == "https" ]]; then
        if [[ -z "$SSL_CERT_HOST_PATH" || -z "$SSL_KEY_HOST_PATH" ]]; then
            log_error "SSL certificate paths are required for HTTPS mode"
            log_error "Please set SSL_CERT_HOST_PATH and SSL_KEY_HOST_PATH in $ENV_FILE"
            exit 1
        fi
        
        if [[ ! -f "$SSL_CERT_HOST_PATH" ]]; then
            log_error "SSL certificate file not found: $SSL_CERT_HOST_PATH"
            exit 1
        fi
        
        if [[ ! -f "$SSL_KEY_HOST_PATH" ]]; then
            log_error "SSL private key file not found: $SSL_KEY_HOST_PATH"
            exit 1
        fi
    fi
    
    log_info "Environment setup completed"
}

# Deploy application
deploy_application() {
    log_info "Deploying RealityTap OTA Server..."
    
    # Determine compose file
    COMPOSE_FILE="docker-compose.$MODE.yml"
    
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Compose file not found: $COMPOSE_FILE"
        exit 1
    fi
    
    # Build Docker Compose command
    COMPOSE_CMD="docker-compose -f $COMPOSE_FILE"
    
    if [[ -n "$ENV_FILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --env-file $ENV_FILE"
    fi
    
    if [[ -n "$PROFILE" ]]; then
        COMPOSE_CMD="$COMPOSE_CMD --profile $PROFILE"
    fi
    
    # Pull images if requested
    if [[ "$PULL" == true ]]; then
        log_info "Pulling latest base images..."
        $COMPOSE_CMD pull
    fi
    
    # Build images if requested
    if [[ "$BUILD" == true ]]; then
        log_info "Building Docker images..."
        $COMPOSE_CMD build --no-cache
    fi
    
    # Deploy the application
    if [[ "$DETACHED" == true ]]; then
        log_info "Starting services in detached mode..."
        $COMPOSE_CMD up -d
    else
        log_info "Starting services in foreground mode..."
        $COMPOSE_CMD up
    fi
    
    if [[ "$DETACHED" == true ]]; then
        log_info "Deployment completed successfully!"
        log_info ""
        log_info "Service Status:"
        $COMPOSE_CMD ps
        log_info ""
        log_info "To view logs: docker-compose -f $COMPOSE_FILE logs -f"
        log_info "To stop services: docker-compose -f $COMPOSE_FILE down"
        
        # Show access information
        if [[ "$MODE" == "https" ]]; then
            log_info "Access the application at: https://localhost:${HOST_PORT:-3000}"
        else
            log_info "Access the application at: http://localhost:${HOST_PORT:-3000}"
        fi
        
        log_info "Admin interface: ${BASE_URL:-http://localhost:3000}/admin"
    fi
}

# Main function
main() {
    log_info "RealityTap OTA Server Docker Deployment"
    log_info "======================================="
    
    parse_args "$@"
    validate_args
    check_prerequisites
    setup_environment
    deploy_application
}

# Run main function
main "$@"
