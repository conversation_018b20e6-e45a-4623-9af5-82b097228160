import { ref } from "vue";
import { useMessage, useDialog } from "naive-ui";
import type { HapticFile, HapticsGroup } from "@/types/haptic-project";
import { parseErrorMessage } from "@/utils/commonUtils";
import type { useProjectStore } from "@/stores/haptics-project-store";
import type { ContextMenuType } from "./useContextMenu";
import type { useI18n } from "@/composables/useI18n";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 上下文菜单操作处理 Composable
 * 处理各种菜单操作的具体执行逻辑
 */
export function useContextMenuOperations(
  projectStore: ReturnType<typeof useProjectStore>,
  expandedTreeKeys: ReturnType<typeof ref<string[]>>,
  selectedTreeKeys: ReturnType<typeof ref<string[]>>,
  ensureParentGroupsExpanded: (groupUuid: string) => void,
  triggerNewGroupCreation: (parentUuid: string | null) => void,
  triggerGroupRename: (node: any) => void,
  triggerFileRename: (node: any) => void,
  handleDeleteGroup: (group: HapticsGroup) => void,
  findNodeByKey: (nodes: any[], key: string) => any,
  treeData: ReturnType<typeof ref<any[]>>,
  getItemFromNodeKey: (key: string, project: any) => HapticFile | HapticsGroup | undefined,
  selectProjectItem: (item: HapticFile | HapticsGroup | null) => void,
  emit: any,
  t: ReturnType<typeof useI18n>['t'],
  playEffect?: (file: HapticFile) => Promise<void>
) {
  const message = useMessage();
  const dialog = useDialog();

  // 导入进度状态
  const isImportingAudio = ref(false);
  const importSpinMessage = ref("");

  /**
   * 处理下拉菜单选择
   */
  const createDropdownSelectHandler =
    (
      currentType: ReturnType<typeof ref<ContextMenuType | null>>,
      currentItem: ReturnType<typeof ref<HapticFile | HapticsGroup | null>>,
      showDropdown: ReturnType<typeof ref<boolean>>
    ) =>
    async (key: string | number) => {
      const selectedKey = String(key);
      showDropdown.value = false;

      logger.debug(LogModule.PROJECT, "下拉菜单选择", {
        selectedKey,
        type: currentType.value,
        item: currentItem.value
      });

      try {
        if (selectedKey === "new-top-group") {
          triggerNewGroupCreation(null);
        } else if (selectedKey === "new-child-group" && currentType.value === "group" && currentItem.value) {
          const parentGroup = currentItem.value as HapticsGroup;
          // 确保父分组也被展开
          if (parentGroup.parentGroupUuid) {
            ensureParentGroupsExpanded(parentGroup.parentGroupUuid);
          }
          triggerNewGroupCreation(parentGroup.groupUuid);
        } else if (selectedKey === "new-file-panel") {
          // Handling for new file from panel context menu
          await projectStore.createNewHeFile(null);
        } else if (selectedKey === "new-file-in-group" && currentType.value === "group" && currentItem.value) {
          const parentGroup = currentItem.value as HapticsGroup;
          const groupNodeKey = `group-${parentGroup.groupUuid}`;
          if (!(expandedTreeKeys.value ?? []).includes(groupNodeKey)) {
            (expandedTreeKeys.value ?? []).push(groupNodeKey);
          }
          // 确保父分组也被展开
          if (parentGroup.parentGroupUuid) {
            ensureParentGroupsExpanded(parentGroup.parentGroupUuid);
          }
          await projectStore.createNewHeFile(parentGroup.groupUuid);
        } else if (selectedKey === "rename-group" && currentType.value === "group" && currentItem.value) {
          // Find the corresponding TreeNode to trigger rename
          const groupToRename = currentItem.value as HapticsGroup;
          const nodeToRenameKey = `group-${groupToRename.groupUuid}`;
          const nodeToRename = findNodeByKey(treeData.value ?? [], nodeToRenameKey);
          if (nodeToRename) {
            triggerGroupRename(nodeToRename);
          }
        } else if (selectedKey === "play-effect" && currentType.value === "file" && currentItem.value) {
          // 处理播放效果
          const fileToPlay = currentItem.value as HapticFile;
          if (playEffect) {
            try {
              await playEffect(fileToPlay);
            } catch (error) {
              message.error(t('playEffect.errorPlaying', { error: parseErrorMessage(error) }));
              logger.error(LogModule.PROJECT, `播放效果失败: ${error}`);
            }
          } else {
            logger.warn(LogModule.PROJECT, "播放效果函数未提供");
            message.warning(t('playEffect.notImplemented'));
          }
        } else if (selectedKey === "rename-file" && currentType.value === "file" && currentItem.value) {
          const fileToRename = currentItem.value as HapticFile;
          const nodeToRenameKey = `file-${fileToRename.fileUuid}`;
          const nodeToRename = findNodeByKey(treeData.value ?? [], nodeToRenameKey);
          if (nodeToRename) {
            triggerFileRename(nodeToRename);
          }
        } else if (selectedKey === "delete-file" && currentType.value === "file" && currentItem.value) {
          await handleDeleteFile(currentItem.value as HapticFile);
        } else if (selectedKey === "delete-group" && currentType.value === "group" && currentItem.value) {
          const groupToDelete = currentItem.value as HapticsGroup;
          handleDeleteGroup(groupToDelete);
        } else if (selectedKey === "import-audio-to-group" && currentType.value === "group" && currentItem.value) {
          await handleImportAudioToGroup(currentItem.value as HapticsGroup);
        } else if (selectedKey === "import-video-to-group" && currentType.value === "group" && currentItem.value) {
          await handleImportVideoToGroup(currentItem.value as HapticsGroup);
        } else if (selectedKey === "import-audio-to-root" && currentType.value === "panel") {
          await handleImportAudioToRoot();
        } else if (selectedKey === "import-video-to-root" && currentType.value === "panel") {
          await handleImportVideoToRoot();
        }
      } catch (error: any) {
        logger.error(LogModule.PROJECT, "处理下拉菜单选择失败", error);
        message.error(t('errors.operationFailed') + `: ${parseErrorMessage(error)}`);
      }
    };

  /**
   * 处理删除文件
   */
  const handleDeleteFile = async (fileToDelete: HapticFile) => {
    return new Promise<void>((resolve) => {
      dialog.warning({
        title: t('editor.file.confirmDelete'),
        content: t('editor.file.confirmDeleteMessage', { name: fileToDelete.name }),
        positiveText: t('common.confirm'),
        negativeText: t('common.cancel'),
        onPositiveClick: async () => {
          try {
            await projectStore.removeHapticFile(fileToDelete.fileUuid);
            message.success(t('editor.file.deleteSuccess', { name: fileToDelete.name }));

            // 删除成功后，清空选中状态并通知父组件
            selectedTreeKeys.value = [];
            emit("select-project-item", null);
            resolve();
          } catch (error: any) {
            message.error(t('editor.file.deleteFailed', { name: fileToDelete.name, error: parseErrorMessage(error) }));
            logger.error(LogModule.PROJECT, "删除文件失败", { fileName: fileToDelete.name, error });
            resolve();
          }
        },
        onNegativeClick: () => {
          message.info(t('common.cancelled'));
          resolve();
        },
      });
    });
  };

  /**
   * 处理导入音频到分组
   */
  const handleImportAudioToGroup = async (group: HapticsGroup) => {
    try {
      const { open } = await import("@tauri-apps/plugin-dialog");
      const selected = await open({
        multiple: false,
        filters: [{ name: "Audio Files", extensions: ["wav", "mp3", "ogg", "flac"] }],
      });
      if (!selected || typeof selected !== "string") return;

      isImportingAudio.value = true;
      importSpinMessage.value = t('editor.audio.importingToGroup', { name: group.name });

      const fileUuid = await projectStore.addAudioFileToProject(selected, group.groupUuid, group.path, message);
      isImportingAudio.value = false;

      if (fileUuid) {
        // addAudioFileToProject 内部已经处理了音频信息获取和文件选中
        // 这里只需要更新树形控件的选中状态
        selectedTreeKeys.value = ["file-" + fileUuid];
        const fileItem = getItemFromNodeKey("file-" + fileUuid, projectStore.activeProject);
        selectProjectItem(fileItem || null);
        message.success(t('editor.audio.importSuccess'));
      }
    } catch (error: any) {
      isImportingAudio.value = false;
      logger.error(LogModule.AUDIO, "音频导入失败", error);
      message.error(t('editor.audio.importFailed', { error: parseErrorMessage(error) }));
    }
  };

  /**
   * 处理导入视频到分组
   */
  const handleImportVideoToGroup = async (group: HapticsGroup) => {
    try {
      const { open } = await import("@tauri-apps/plugin-dialog");
      const selected = await open({
        multiple: false,
        filters: [{ name: "Video Files", extensions: ["mp4"] }],
      });
      if (!selected || typeof selected !== "string") return;

      isImportingAudio.value = true;
      importSpinMessage.value = t('editor.video.importingToGroup', { name: group.name });

      const fileUuid = await projectStore.addVideoFileToProject(selected, group.groupUuid, group.path, message);
      isImportingAudio.value = false;

      if (fileUuid) {
        // addVideoFileToProject 内部已经处理了音频信息获取和文件选中
        // 这里只需要更新树形控件的选中状态
        selectedTreeKeys.value = ["file-" + fileUuid];
        const fileItem = getItemFromNodeKey("file-" + fileUuid, projectStore.activeProject);
        selectProjectItem(fileItem || null);
        message.success(t('editor.video.importSuccess'));
      }
    } catch (error: any) {
      isImportingAudio.value = false;
      logger.error(LogModule.AUDIO, "视频导入失败", error);
      message.error(t('editor.video.importFailed', { error: parseErrorMessage(error) }));
    }
  };

  /**
   * 处理导入音频到根目录
   */
  const handleImportAudioToRoot = async () => {
    try {
      const { open } = await import("@tauri-apps/plugin-dialog");
      const selected = await open({
        multiple: false,
        filters: [{ name: "Audio Files", extensions: ["wav", "mp3", "ogg", "flac"] }],
      });
      if (!selected || typeof selected !== "string") return;

      isImportingAudio.value = true;
      importSpinMessage.value = t('editor.audio.importingToRoot');

      const fileUuid = await projectStore.addAudioFileToProject(
        selected,
        null, // groupUuid 传 null 表示根目录
        "", // path 传空字符串表示根目录
        message
      );
      isImportingAudio.value = false;

      if (fileUuid) {
        // addAudioFileToProject 内部已经处理了音频信息获取和文件选中
        // 这里只需要更新树形控件的选中状态
        selectedTreeKeys.value = ["file-" + fileUuid];
        const fileItem = getItemFromNodeKey("file-" + fileUuid, projectStore.activeProject);
        selectProjectItem(fileItem || null);
        message.success(t('editor.audio.importSuccess'));
      }
    } catch (error: any) {
      isImportingAudio.value = false;
      logger.error(LogModule.AUDIO, "音频导入失败", error);
      message.error(t('editor.audio.importFailed', { error: parseErrorMessage(error) }));
    }
  };

  /**
   * 处理导入视频到根目录
   */
  const handleImportVideoToRoot = async () => {
    try {
      const { open } = await import("@tauri-apps/plugin-dialog");
      const selected = await open({
        multiple: false,
        filters: [{ name: "Video Files", extensions: ["mp4"] }],
      });
      if (!selected || typeof selected !== "string") return;

      isImportingAudio.value = true;
      importSpinMessage.value = t('editor.video.importingToRoot');

      const fileUuid = await projectStore.addVideoFileToProject(
        selected,
        null, // groupUuid 传 null 表示根目录
        "", // path 传空字符串表示根目录
        message
      );
      isImportingAudio.value = false;

      if (fileUuid) {
        // addVideoFileToProject 内部已经处理了音频信息获取和文件选中
        // 这里只需要更新树形控件的选中状态
        selectedTreeKeys.value = ["file-" + fileUuid];
        const fileItem = getItemFromNodeKey("file-" + fileUuid, projectStore.activeProject);
        selectProjectItem(fileItem || null);
        message.success(t('editor.video.importSuccess'));
      }
    } catch (error: any) {
      isImportingAudio.value = false;
      logger.error(LogModule.AUDIO, "视频导入失败", error);
      message.error(t('editor.video.importFailed', { error: parseErrorMessage(error) }));
    }
  };

  return {
    // 状态
    isImportingAudio,
    importSpinMessage,

    // 方法
    createDropdownSelectHandler,
    handleDeleteFile,
    handleImportAudioToGroup,
    handleImportAudioToRoot,
  };
}
