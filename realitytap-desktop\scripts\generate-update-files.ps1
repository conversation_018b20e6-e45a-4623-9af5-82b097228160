#!/usr/bin/env pwsh

<#
.SYNOPSIS
    快速生成 RealityTap Desktop 更新文件
    Quick generate RealityTap Desktop update files

.DESCRIPTION
    此脚本专门用于生成更新文件，假设前端已经构建完成。
    适用于只需要重新生成更新包和签名的场景。

.PARAMETER Configuration
    构建配置：release 或 debug（默认：release）

.PARAMETER ShowSignature
    显示完整的签名内容（默认：false）

.EXAMPLE
    .\scripts\generate-update-files.ps1
    .\scripts\generate-update-files.ps1 -Configuration debug -ShowSignature
#>

param(
    [ValidateSet("release", "debug")]
    [string]$Configuration = "release",
    
    [switch]$ShowSignature
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot

Write-Host "🔄 快速生成更新文件（配置：$Configuration）" -ForegroundColor Cyan

# 检查签名密钥
if (-not $env:TAURI_SIGNING_PRIVATE_KEY) {
    Write-Host "❌ 错误: 未设置 TAURI_SIGNING_PRIVATE_KEY 环境变量" -ForegroundColor Red
    Write-Host "请先设置签名密钥环境变量，然后重新运行此脚本。" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ 签名密钥配置已找到" -ForegroundColor Green

try {
    # 只构建 Tauri 应用
    Write-Host "`n🔨 生成更新文件..." -ForegroundColor Yellow
    
    if ($Configuration -eq "release") {
        npm run tauri:build
    } else {
        npm run tauri build -- --debug
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "构建失败"
    }
    
    # 在生成更新文件前验证 DLL 包含情况
    Write-Host "`n📦 验证 DLL 文件包含情况..." -ForegroundColor Yellow

    $BundleDir = if ($Configuration -eq "release") {
        "src-tauri/target/release/bundle"
    } else {
        "src-tauri/target/debug/bundle"
    }

    # 检查构建产物中是否包含 DLL
    $MsiFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.msi" -ErrorAction SilentlyContinue
    if ($MsiFiles) {
        Write-Host "✅ MSI 文件已生成，DLL 文件已自动打包" -ForegroundColor Green
    } else {
        Write-Host "⚠️  未找到 MSI 文件" -ForegroundColor Yellow
    }

    # 显示生成的更新文件
    Write-Host "`n📦 更新文件生成完成:" -ForegroundColor Green
    
    $BundleDir = if ($Configuration -eq "release") {
        "src-tauri/target/release/bundle"
    } else {
        "src-tauri/target/debug/bundle"
    }
    
    $MsiFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.msi"
    foreach ($MsiFile in $MsiFiles) {
        Write-Host "📦 $($MsiFile.FullName)" -ForegroundColor White
        
        $SigFile = "$($MsiFile.FullName).sig"
        if (Test-Path $SigFile) {
            Write-Host "🔐 $SigFile" -ForegroundColor Green
            
            if ($ShowSignature) {
                $SigContent = Get-Content $SigFile -Raw
                Write-Host "   签名内容:" -ForegroundColor Gray
                Write-Host "   $SigContent" -ForegroundColor Gray
            }
        }
    }
    
    Write-Host "`n✅ 更新文件生成完成！" -ForegroundColor Green
    
} catch {
    Write-Host "`n❌ 生成失败: $_" -ForegroundColor Red
    exit 1
}
