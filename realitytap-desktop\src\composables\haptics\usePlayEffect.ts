import { ref } from "vue";
import type { HapticFile } from "@/types/haptic-project";
import type { RenderableEvent } from "@/types/haptic-editor";
import { useProjectStore } from "@/stores/haptics-project-store";
// import { useFileWaveformEditorStore } from "@/stores/haptics-editor-store";

import { LogModule, logger } from "@/utils/logger/logger";

/**
 * 播放效果功能的 Composable
 * 处理获取文件数据、转换为JSON和管理对话框状态
 */
export function usePlayEffect() {
  // 状态管理
  const showDialog = ref(false);
  const events = ref<RenderableEvent[]>([]);
  const totalDuration = ref(0);
  const currentFile = ref<HapticFile | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // 获取 stores
  const projectStore = useProjectStore();

  /**
   * 计算事件的总时长
   */
  const calculateTotalDuration = (events: RenderableEvent[]): number => {
    if (!events || events.length === 0) {
      return 0;
    }

    let maxEndTime = 0;

    events.forEach(event => {
      let endTime = event.startTime;

      if (event.type === 'transient') {
        // 瞬态事件使用 stopTime 或 startTime + width
        endTime = event.stopTime || (event.startTime + (event.width || 25));
      } else if (event.type === 'continuous') {
        // 连续事件使用 stopTime 或 startTime + duration
        endTime = event.stopTime || (event.startTime + (event.duration || 0));
      }

      maxEndTime = Math.max(maxEndTime, endTime);
    });

    return maxEndTime;
  };

  /**
   * 获取文件的事件数据
   * 优先级：编辑器缓存 > 项目缓存 > 文件系统
   */
  const getFileEventData = async (fileUuid: string): Promise<RenderableEvent[]> => {
    try {
      // 1. 检查项目缓存（优先使用缓存数据）
      const cache = projectStore.getFileCache(fileUuid);
      if (cache?.currentEvents && cache.currentEvents.length > 0) {
        logger.debug(LogModule.PROJECT, `从项目缓存获取文件数据: ${fileUuid}`);
        return cache.currentEvents;
      }

      // 2. 从文件系统读取
      logger.debug(LogModule.PROJECT, `需要从文件系统读取文件数据: ${fileUuid}`);

      // 找到对应的文件
      const file = projectStore.activeProject?.files?.find(f => f.fileUuid === fileUuid);
      if (!file) {
        throw new Error(`找不到文件: ${fileUuid}`);
      }

      // 获取文件完整路径
      const fullPath = await projectStore.getResolvedFilePath(file);
      if (!fullPath) {
        throw new Error(`无法解析文件路径: ${file.name}`);
      }

      // 读取文件内容
      const fileContent = await projectStore.readFileContentFromBackend(fullPath);

      // 解析文件内容
      const { parseRealityTap } = await import("@/types/haptic-file");
      const effectData = parseRealityTap(fileContent);

      // 转换为 RenderableEvent
      const { flattenRealityTapEffect } = await import("@/utils/haptic-event-processor");
      const { events } = flattenRealityTapEffect(effectData);

      // 缓存数据以供后续使用
      projectStore.setFileCache(fileUuid, effectData, events);

      return events;
    } catch (err) {
      logger.error(LogModule.PROJECT, `获取文件事件数据失败: ${err}`);
      throw err;
    }
  };



  /**
   * 播放效果
   * 获取文件数据并显示对话框
   */
  const playEffect = async (file: HapticFile) => {
    isLoading.value = true;
    error.value = null;
    currentFile.value = file;
    
    try {
      // 获取事件数据
      const fileEvents = await getFileEventData(file.fileUuid);

      // 设置事件数据
      events.value = fileEvents;

      // 计算总时长
      totalDuration.value = calculateTotalDuration(fileEvents);

      // 显示对话框
      showDialog.value = true;
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err);
      logger.error(LogModule.PROJECT, `播放效果失败: ${error.value}`);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 关闭对话框
   */
  const closeDialog = () => {
    showDialog.value = false;
    // 清理状态
    setTimeout(() => {
      events.value = [];
      totalDuration.value = 0;
      error.value = null;
    }, 300); // 等待对话框关闭动画完成
  };

  return {
    showDialog,
    events,
    totalDuration,
    currentFile,
    isLoading,
    error,
    playEffect,
    closeDialog
  };
}
