# RealityTap OTA Server Docker Environment Configuration
# Copy this file to .env and modify the values as needed

# =============================================================================
# HOST DIRECTORY MAPPING
# =============================================================================
# Absolute path to storage directory on host (will be created if not exists)
STORAGE_HOST_PATH=/opt/realitytap-ota/storage

# Absolute path to signing keys directory on host
KEYS_HOST_PATH=/opt/realitytap-ota/keys

# SSL certificate paths (for HTTPS mode)
SSL_CERT_HOST_PATH=/opt/realitytap-ota/ssl/cert.pem
SSL_KEY_HOST_PATH=/opt/realitytap-ota/ssl/key.pem

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Application version
APP_VERSION=latest

# Node environment
NODE_ENV=production

# Server ports
HOST_PORT=3000
CONTAINER_PORT=3000

# Base URL for the application
BASE_URL=http://localhost:3000
# For HTTPS: BASE_URL=https://your-domain.com

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================
# Admin credentials (REQUIRED - change these!)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password-here

# JWT configuration
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRES_IN=1h
SESSION_TIMEOUT=3600000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_ENABLED=true
DB_MAX_CONNECTIONS=10
DB_BUSY_TIMEOUT=30000
DB_ENABLE_WAL=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS origin (use specific domain in production)
CORS_ORIGIN=*

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
ADMIN_RATE_LIMIT_WINDOW_MS=300000
ADMIN_RATE_LIMIT_MAX_REQUESTS=500
PUBLIC_RATE_LIMIT_WINDOW_MS=300000
PUBLIC_RATE_LIMIT_MAX_REQUESTS=200

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# Max file size in bytes (100MB default)
ADMIN_MAX_FILE_SIZE=104857600

# Allowed file types
ADMIN_ALLOWED_FILE_TYPES=.exe,.msi,.dmg,.deb,.rpm,.tar.gz,.zip,.pkg,.hash

# =============================================================================
# TAURI UPDATER CONFIGURATION
# =============================================================================
# Path to private key for signing updates
TAURI_PRIVATE_KEY_PATH=/app/keys/private.key

# Password for the private key (if encrypted)
TAURI_KEY_PASSWORD=

# =============================================================================
# NGINX CONFIGURATION (when using nginx profile)
# =============================================================================
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# =============================================================================
# DEBUG CONFIGURATION
# =============================================================================
DEBUG=false
