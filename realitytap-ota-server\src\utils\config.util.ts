import { ConfigChangeLog, SystemConfig } from '@/types/server.types';
import crypto from 'crypto';
import { z } from 'zod';

/**
 * 配置工具类
 * 提供系统配置相关的工具函数
 */
export class ConfigUtil {
  /**
   * 生成配置变更日志ID
   * @returns 唯一ID
   */
  static generateChangeLogId(): string {
    return crypto.randomUUID();
  }

  /**
   * 获取默认系统配置
   * @returns 默认配置对象
   */
  static getDefaultConfig(): SystemConfig {
    return {
      upload: {
        maxFileSize: 500 * 1024 * 1024, // 500MB
        allowedExtensions: ['.exe', '.dmg', '.deb', '.rpm', '.zip', '.tar.gz'],
        tempDir: './storage/temp',
        cleanupInterval: 24 * 60 * 60 * 1000, // 24小时
      },
      download: {
        rateLimit: {
          windowMs: 15 * 60 * 1000, // 15分钟
          maxRequests: 100,
        },
        enableStats: true,
        enableCompression: true,
      },
      storage: {
        basePath: './storage',
        cleanupInterval: 7 * 24 * 60 * 60 * 1000, // 7天
        maxStorageSize: 10 * 1024 * 1024 * 1024, // 10GB
      },
      security: {
        enableCORS: true,
        allowedOrigins: ['http://localhost:3000', 'http://localhost:5173'],
        enableHelmet: true,
      },
      logging: {
        level: 'info',
        enableFileLogging: true,
        maxLogFiles: 10,
        maxLogSize: '10m',
      },
    };
  }

  /**
   * 系统配置验证模式
   */
  static getConfigSchema() {
    return z.object({
      upload: z.object({
        maxFileSize: z
          .number()
          .min(1024 * 1024)
          .max(2 * 1024 * 1024 * 1024), // 1MB - 2GB
        allowedExtensions: z.array(z.string().regex(/^\.[a-zA-Z0-9.]+$/)),
        tempDir: z.string().min(1),
        cleanupInterval: z
          .number()
          .min(60 * 1000)
          .max(7 * 24 * 60 * 60 * 1000), // 1分钟 - 7天
      }),
      download: z.object({
        rateLimit: z.object({
          windowMs: z
            .number()
            .min(60 * 1000)
            .max(60 * 60 * 1000), // 1分钟 - 1小时
          maxRequests: z.number().min(1).max(10000),
        }),
        enableStats: z.boolean(),
        enableCompression: z.boolean(),
      }),
      storage: z.object({
        basePath: z.string().min(1),
        cleanupInterval: z
          .number()
          .min(60 * 60 * 1000)
          .max(30 * 24 * 60 * 60 * 1000), // 1小时 - 30天
        maxStorageSize: z
          .number()
          .min(100 * 1024 * 1024)
          .max(1000 * 1024 * 1024 * 1024), // 100MB - 1TB
      }),
      security: z.object({
        enableCORS: z.boolean(),
        allowedOrigins: z.array(z.string().url()),
        enableHelmet: z.boolean(),
      }),
      logging: z.object({
        level: z.enum(['error', 'warn', 'info', 'debug']),
        enableFileLogging: z.boolean(),
        maxLogFiles: z.number().min(1).max(100),
        maxLogSize: z.string().regex(/^\d+[kmg]$/i),
      }),
    });
  }

  /**
   * 验证配置对象
   * @param config 配置对象
   * @returns 验证结果
   */
  static validateConfig(config: any): {
    isValid: boolean;
    errors: string[];
    validatedConfig?: SystemConfig;
  } {
    try {
      const schema = this.getConfigSchema();
      const validatedConfig = schema.parse(config);

      return {
        isValid: true,
        errors: [],
        validatedConfig,
      };
    } catch (error: any) {
      const errors: string[] = [];

      if (error instanceof z.ZodError) {
        for (const issue of error.issues) {
          errors.push(`${issue.path.join('.')}: ${issue.message}`);
        }
      } else {
        errors.push(error.message || 'Unknown validation error');
      }

      return {
        isValid: false,
        errors,
      };
    }
  }

  /**
   * 比较配置对象，找出差异
   * @param oldConfig 旧配置
   * @param newConfig 新配置
   * @returns 变更列表
   */
  static compareConfigs(
    oldConfig: SystemConfig,
    newConfig: SystemConfig,
  ): {
    field: string;
    oldValue: any;
    newValue: any;
  }[] {
    const changes: { field: string; oldValue: any; newValue: any }[] = [];

    this.compareObjects(oldConfig, newConfig, '', changes);

    return changes;
  }

  /**
   * 递归比较对象
   * @param oldObj 旧对象
   * @param newObj 新对象
   * @param prefix 字段前缀
   * @param changes 变更列表
   */
  private static compareObjects(
    oldObj: any,
    newObj: any,
    prefix: string,
    changes: { field: string; oldValue: any; newValue: any }[],
  ): void {
    const allKeys = new Set([...Object.keys(oldObj || {}), ...Object.keys(newObj || {})]);

    for (const key of allKeys) {
      const fieldPath = prefix ? `${prefix}.${key}` : key;
      const oldValue = oldObj?.[key];
      const newValue = newObj?.[key];

      if (oldValue === undefined && newValue !== undefined) {
        changes.push({ field: fieldPath, oldValue: undefined, newValue });
      } else if (oldValue !== undefined && newValue === undefined) {
        changes.push({ field: fieldPath, oldValue, newValue: undefined });
      } else if (
        typeof oldValue === 'object' &&
        typeof newValue === 'object' &&
        oldValue !== null &&
        newValue !== null &&
        !Array.isArray(oldValue) &&
        !Array.isArray(newValue)
      ) {
        // 递归比较对象
        this.compareObjects(oldValue, newValue, fieldPath, changes);
      } else if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        changes.push({ field: fieldPath, oldValue, newValue });
      }
    }
  }

  /**
   * 创建配置变更日志
   * @param changes 变更列表
   * @param userId 用户ID
   * @param action 操作类型
   * @param reason 变更原因
   * @returns 变更日志对象
   */
  static createChangeLog(
    changes: { field: string; oldValue: any; newValue: any }[],
    userId?: string,
    action: 'update' | 'reset' = 'update',
    reason?: string,
  ): ConfigChangeLog {
    return {
      id: this.generateChangeLogId(),
      timestamp: new Date().toISOString(),
      userId,
      action,
      changes,
      reason,
    };
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化后的大小字符串
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 解析日志大小字符串
   * @param sizeStr 大小字符串 (如 "10m", "1g")
   * @returns 字节数
   */
  static parseLogSize(sizeStr: string): number {
    const match = sizeStr.match(/^(\d+)([kmg]?)$/i);
    if (!match || !match[1]) {
      throw new Error('Invalid log size format');
    }

    const value = parseInt(match[1], 10);
    const unit = (match[2] || '').toLowerCase();

    switch (unit) {
      case 'k':
        return value * 1024;
      case 'm':
        return value * 1024 * 1024;
      case 'g':
        return value * 1024 * 1024 * 1024;
      default:
        return value;
    }
  }

  /**
   * 验证文件扩展名
   * @param filename 文件名
   * @param allowedExtensions 允许的扩展名列表
   * @returns 是否允许
   */
  static isAllowedExtension(filename: string, allowedExtensions: string[]): boolean {
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return allowedExtensions.some(ext => ext.toLowerCase() === extension);
  }

  /**
   * 清理配置对象，移除敏感信息
   * @param config 配置对象
   * @returns 清理后的配置对象
   */
  static sanitizeConfig(config: SystemConfig): SystemConfig {
    // 创建配置的深拷贝
    const sanitized = JSON.parse(JSON.stringify(config));

    // 这里可以添加敏感信息的清理逻辑
    // 目前配置中没有敏感信息，所以直接返回

    return sanitized;
  }

  /**
   * 验证配置路径是否安全
   * @param configPath 配置路径
   * @returns 是否安全
   */
  static isSecurePath(configPath: string): boolean {
    // 检查路径是否包含危险字符
    const dangerousPatterns = [
      /\.\./, // 父目录引用
      /^\//, // 绝对路径
      /[<>:"|?*]/, // Windows 不允许的字符
    ];

    return !dangerousPatterns.some(pattern => pattern.test(configPath));
  }

  /**
   * 格式化配置变更日志为可读文本
   * @param changeLog 变更日志
   * @returns 格式化后的文本
   */
  static formatChangeLog(changeLog: ConfigChangeLog): string {
    const lines: string[] = [];

    lines.push(`[${changeLog.timestamp}] Config ${changeLog.action} by ${changeLog.userId || 'system'}`);

    if (changeLog.reason) {
      lines.push(`Reason: ${changeLog.reason}`);
    }

    lines.push('Changes:');
    for (const change of changeLog.changes) {
      lines.push(`  ${change.field}: ${JSON.stringify(change.oldValue)} -> ${JSON.stringify(change.newValue)}`);
    }

    return lines.join('\n');
  }
}
