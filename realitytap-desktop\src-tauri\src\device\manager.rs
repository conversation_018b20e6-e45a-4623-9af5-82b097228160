// Device manager implementation

use crate::error::Result;
use crate::models::device::*;
use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use tokio::time::Instant;

// === Device Interface Trait ===

#[async_trait]
pub trait DeviceInterface: Send + Sync {
    async fn connect(&mut self) -> Result<()>;
    async fn disconnect(&mut self) -> Result<()>;
    async fn is_connected(&self) -> bool;
    async fn send_data(&mut self, data: &[u8]) -> Result<()>;
    async fn get_device_info(&self) -> Result<DeviceDiscoveryInfo>;
    async fn get_status(&self) -> DeviceStatus;
}

// === Device Manager ===

pub struct DeviceManager {
    devices: Arc<RwLock<HashMap<String, Device>>>,
    device_interfaces: Arc<Mutex<HashMap<String, Box<dyn DeviceInterface>>>>,
    default_device_id: Arc<RwLock<Option<String>>>,
    config: Arc<RwLock<DeviceManagerConfig>>,
    is_scanning: Arc<Mutex<bool>>,
    last_scan_time: Arc<RwLock<Option<Instant>>>,
}

impl DeviceManager {
    pub fn new() -> Self {
        Self {
            devices: Arc::new(RwLock::new(HashMap::new())),
            device_interfaces: Arc::new(Mutex::new(HashMap::new())),
            default_device_id: Arc::new(RwLock::new(None)),
            config: Arc::new(RwLock::new(DeviceManagerConfig::default())),
            is_scanning: Arc::new(Mutex::new(false)),
            last_scan_time: Arc::new(RwLock::new(None)),
        }
    }

    // === Device Management ===

    pub async fn add_device(&self, device: Device) -> Result<()> {
        let mut devices = self.devices.write().await;
        devices.insert(device.device_id.clone(), device);
        Ok(())
    }

    pub async fn remove_device(&self, device_id: &str) -> Result<bool> {
        // First disconnect if connected
        if let Some(device) = self.get_device(device_id).await {
            if device.is_connected() {
                let _ = self.disconnect_device(device_id).await;
            }
        }

        // Remove from interfaces
        let mut interfaces = self.device_interfaces.lock().await;
        interfaces.remove(device_id);

        // Remove from devices
        let mut devices = self.devices.write().await;
        let removed = devices.remove(device_id).is_some();

        // Clear default if this was the default device
        let mut default_id = self.default_device_id.write().await;
        if default_id.as_ref() == Some(&device_id.to_string()) {
            *default_id = None;
        }

        Ok(removed)
    }

    pub async fn get_device(&self, device_id: &str) -> Option<Device> {
        let devices = self.devices.read().await;
        devices.get(device_id).cloned()
    }

    pub async fn get_all_devices(&self) -> Vec<Device> {
        let devices = self.devices.read().await;
        devices.values().cloned().collect()
    }

    pub async fn update_device(&self, device_id: &str, updates: Device) -> Result<bool> {
        let mut devices = self.devices.write().await;
        if devices.contains_key(device_id) {
            devices.insert(device_id.to_string(), updates);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    // === Device Connection ===

    pub async fn connect_device(&self, device_id: &str, _config: Option<DeviceConnectionConfig>) -> Result<()> {
        // Get device
        let mut device = self.get_device(device_id).await
            .ok_or_else(|| crate::error::Error::NotFound(format!("Device not found: {}", device_id)))?;

        if device.is_connected() {
            return Ok(());
        }

        if !device.can_connect() {
            return Err(crate::error::Error::InvalidOperation(
                format!("Device {} cannot be connected in current state: {}", device_id, device.status.as_str())
            ));
        }

        // Update status to connecting
        device.update_status(DeviceStatus::Connecting);
        self.update_device(device_id, device.clone()).await?;

        // Get or create device interface
        let mut interfaces = self.device_interfaces.lock().await;
        let interface = match interfaces.get_mut(device_id) {
            Some(interface) => interface,
            None => {
                // Create new interface based on device type
                let new_interface = self.create_device_interface(&device).await?;
                interfaces.insert(device_id.to_string(), new_interface);
                interfaces.get_mut(device_id).unwrap()
            }
        };

        // Attempt connection
        match interface.connect().await {
            Ok(()) => {
                device.update_status(DeviceStatus::Connected);
                device.clear_error();
                self.update_device(device_id, device).await?;
                log::info!("Device {} connected successfully", device_id);
                Ok(())
            }
            Err(e) => {
                device.set_error(e.to_string());
                self.update_device(device_id, device).await?;
                log::error!("Failed to connect device {}: {}", device_id, e);
                Err(e)
            }
        }
    }

    pub async fn disconnect_device(&self, device_id: &str) -> Result<()> {
        // Get device
        let mut device = self.get_device(device_id).await
            .ok_or_else(|| crate::error::Error::NotFound(format!("Device not found: {}", device_id)))?;

        if !device.is_connected() {
            return Ok(());
        }

        // Update status to disconnecting
        device.update_status(DeviceStatus::Disconnecting);
        self.update_device(device_id, device.clone()).await?;

        // Get device interface
        let mut interfaces = self.device_interfaces.lock().await;
        if let Some(interface) = interfaces.get_mut(device_id) {
            match interface.disconnect().await {
                Ok(()) => {
                    device.update_status(DeviceStatus::Disconnected);
                    device.clear_error();
                    self.update_device(device_id, device).await?;
                    log::info!("Device {} disconnected successfully", device_id);
                    Ok(())
                }
                Err(e) => {
                    device.set_error(e.to_string());
                    self.update_device(device_id, device).await?;
                    log::error!("Failed to disconnect device {}: {}", device_id, e);
                    Err(e)
                }
            }
        } else {
            // No interface found, just update status
            device.update_status(DeviceStatus::Disconnected);
            self.update_device(device_id, device).await?;
            Ok(())
        }
    }

    // === Device Scanning ===

    pub async fn scan_devices(&self, options: Option<DeviceScanOptions>) -> Result<Vec<Device>> {
        let mut is_scanning = self.is_scanning.lock().await;
        if *is_scanning {
            return Err(crate::error::Error::InvalidOperation("Scan already in progress".to_string()));
        }
        *is_scanning = true;
        drop(is_scanning);

        let scan_result = self.perform_scan(options).await;

        // Update scan time and reset scanning flag
        let mut last_scan = self.last_scan_time.write().await;
        *last_scan = Some(Instant::now());
        drop(last_scan);

        let mut is_scanning = self.is_scanning.lock().await;
        *is_scanning = false;

        scan_result
    }

    async fn perform_scan(&self, options: Option<DeviceScanOptions>) -> Result<Vec<Device>> {
        let options = options.unwrap_or_default();
        let types_to_scan = options.types.unwrap_or_else(|| vec![
            DeviceType::Usb,
            DeviceType::Wifi,
            DeviceType::Bluetooth,
        ]);

        let mut discovered_devices = Vec::new();

        // Scan each device type
        for device_type in types_to_scan {
            match self.scan_device_type(&device_type).await {
                Ok(mut devices) => {
                    discovered_devices.append(&mut devices);
                }
                Err(e) => {
                    log::warn!("Failed to scan {} devices: {}", device_type.as_str(), e);
                }
            }
        }

        // Update existing devices or add new ones
        for device in &discovered_devices {
            if let Some(existing) = self.get_device(&device.device_id).await {
                // Update existing device with new information
                let mut updated = existing;
                updated.metadata = device.metadata.clone();
                updated.updated_at = device.updated_at.clone();
                self.update_device(&device.device_id, updated).await?;
            } else {
                // Add new device
                self.add_device(device.clone()).await?;
            }
        }

        Ok(discovered_devices)
    }

    async fn scan_device_type(&self, device_type: &DeviceType) -> Result<Vec<Device>> {
        match device_type {
            DeviceType::Usb => self.scan_usb_devices().await,
            DeviceType::Wifi => self.scan_wifi_devices().await,
            DeviceType::Bluetooth => self.scan_bluetooth_devices().await,
        }
    }

    // === Device Type Specific Scanning ===

    async fn scan_usb_devices(&self) -> Result<Vec<Device>> {
        // TODO: Implement USB device scanning
        log::info!("Scanning USB devices...");
        Ok(vec![])
    }

    async fn scan_wifi_devices(&self) -> Result<Vec<Device>> {
        // TODO: Implement WiFi device scanning
        log::info!("Scanning WiFi devices...");
        Ok(vec![])
    }

    async fn scan_bluetooth_devices(&self) -> Result<Vec<Device>> {
        // TODO: Implement Bluetooth device scanning
        log::info!("Scanning Bluetooth devices...");
        Ok(vec![])
    }

    // === Device Interface Creation ===

    async fn create_device_interface(&self, device: &Device) -> Result<Box<dyn DeviceInterface>> {
        match device.device_type {
            DeviceType::Usb => {
                // TODO: Create USB device interface
                Err(crate::error::Error::NotImplemented("USB device interface not implemented".to_string()))
            }
            DeviceType::Wifi => {
                // TODO: Create WiFi device interface
                Err(crate::error::Error::NotImplemented("WiFi device interface not implemented".to_string()))
            }
            DeviceType::Bluetooth => {
                // TODO: Create Bluetooth device interface
                Err(crate::error::Error::NotImplemented("Bluetooth device interface not implemented".to_string()))
            }
        }
    }

    // === Default Device Management ===

    pub async fn set_default_device(&self, device_id: Option<String>) -> Result<()> {
        if let Some(id) = &device_id {
            // Verify device exists
            if self.get_device(id).await.is_none() {
                return Err(crate::error::Error::NotFound(format!("Device not found: {}", id)));
            }
        }

        let mut default_id = self.default_device_id.write().await;
        *default_id = device_id;
        Ok(())
    }

    pub async fn get_default_device(&self) -> Option<Device> {
        let default_id = self.default_device_id.read().await;
        if let Some(id) = default_id.as_ref() {
            self.get_device(id).await
        } else {
            None
        }
    }

    // === Statistics ===

    pub async fn get_statistics(&self) -> DeviceStatistics {
        let devices = self.get_all_devices().await;
        let total = devices.len();
        let connected = devices.iter().filter(|d| d.is_connected()).count();
        let disconnected = devices.iter().filter(|d| d.status == DeviceStatus::Disconnected).count();
        let error = devices.iter().filter(|d| d.status == DeviceStatus::Error).count();

        let mut devices_by_type = HashMap::new();
        for device in &devices {
            *devices_by_type.entry(device.device_type.clone()).or_insert(0) += 1;
        }

        let last_scan = self.last_scan_time.read().await;
        let last_scan_time = last_scan.map(|_instant| {
            chrono::Utc::now().to_rfc3339()
        });

        let last_connection_time = devices
            .iter()
            .filter_map(|d| d.last_connected.as_ref())
            .max()
            .cloned();

        DeviceStatistics {
            total_devices: total,
            connected_devices: connected,
            disconnected_devices: disconnected,
            error_devices: error,
            devices_by_type,
            last_scan_time,
            last_connection_time,
        }
    }

    // === Data Transmission ===

    pub async fn send_data(&self, device_id: &str, data: &[u8]) -> Result<()> {
        // Verify device is connected
        let device = self.get_device(device_id).await
            .ok_or_else(|| crate::error::Error::NotFound(format!("Device not found: {}", device_id)))?;

        if !device.is_connected() {
            return Err(crate::error::Error::InvalidOperation(
                format!("Device {} is not connected", device_id)
            ));
        }

        // Get device interface and send data
        let mut interfaces = self.device_interfaces.lock().await;
        if let Some(interface) = interfaces.get_mut(device_id) {
            interface.send_data(data).await
        } else {
            Err(crate::error::Error::InvalidOperation(
                format!("No interface found for device {}", device_id)
            ))
        }
    }

    // === Configuration ===

    pub async fn get_config(&self) -> DeviceManagerConfig {
        let config = self.config.read().await;
        config.clone()
    }

    pub async fn update_config(&self, new_config: DeviceManagerConfig) -> Result<()> {
        let mut config = self.config.write().await;
        *config = new_config;
        Ok(())
    }

    // === Status Checking ===

    pub async fn is_scanning(&self) -> bool {
        let is_scanning = self.is_scanning.lock().await;
        *is_scanning
    }

    pub async fn get_last_scan_time(&self) -> Option<String> {
        let last_scan = self.last_scan_time.read().await;
        last_scan.map(|_| chrono::Utc::now().to_rfc3339())
    }
}

impl Default for DeviceScanOptions {
    fn default() -> Self {
        Self {
            types: None,
            timeout: Some(15000), // 15 seconds
            include_disconnected: Some(true),
        }
    }
}
