// 上传相关类型定义

export interface FileUploadRequest {
  version: string
  platform: 'windows' | 'macos' | 'linux'
  architecture: 'x86_64' | 'aarch64' | 'x86'
  channel?: 'stable' | 'beta' | 'alpha'
  releaseNotes?: string
  isForced?: boolean
  fileHash?: string // 可选的文件hash值，从.hash文件中读取
  signature?: string // 可选的签名值，从.sig文件中读取
}



export interface UploadProgress {
  sessionId: string
  uploadedBytes: number
  totalBytes: number
  uploadedChunks: number
  totalChunks: number
  percentage: number
  speed: number // bytes per second
  estimatedTimeRemaining: number // seconds
  status: UploadStatus
}

export enum UploadStatus {
  INITIALIZING = 'initializing',
  UPLOADING = 'uploading',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  MERGING = 'merging',
}

export interface UploadError {
  code: string
  message: string
  chunkIndex?: number
  retryable: boolean
}



// 批量上传相关类型
export interface BatchUploadFileInfo {
  filename: string
  fileSize: number
  fileHash: string
  fileType: 'installer' | 'signature'
}

export interface BatchUploadInitRequest {
  files: BatchUploadFileInfo[]
  metadata: FileUploadRequest
}

export interface BatchUploadInitResponse {
  sessionId: string
  files: BatchUploadFileStatus[]
  totalFiles: number
  totalSize: number
}

export interface BatchUploadFileStatus {
  filename: string
  fileType: 'installer' | 'signature'
  status: UploadStatus
  uploadedBytes: number
  totalBytes: number
  error?: string
}

export interface BatchUploadProgress {
  sessionId: string
  files: BatchUploadFileStatus[]
  overallProgress: {
    uploadedBytes: number
    totalBytes: number
    percentage: number
    completedFiles: number
    totalFiles: number
  }
  status: UploadStatus
}

// API 响应类型
export interface BaseResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
  }
  timestamp: string
  version: string
}

export type UploadResponse<T = any> = BaseResponse<T>
