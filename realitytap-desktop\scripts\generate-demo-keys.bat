@echo off

echo.
echo Generate RealityTap Desktop Demo Signing Keys
echo WARNING: This script is for development and testing only
echo.

set "KEY_NAME=realitytap-demo"
set "KEY_DIR=keys"

echo Checking Tauri CLI...
npm run tauri -- --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Tauri CLI not found
    echo Please install Tauri CLI first
    pause
    exit /b 1
)
echo Tauri CLI is installed

echo.
echo Creating key directory...
if not exist "%KEY_DIR%" (
    mkdir "%KEY_DIR%"
    echo Created directory: %KEY_DIR%
) else (
    echo Directory exists: %KEY_DIR%
)

set "PRIVATE_KEY_PATH=%KEY_DIR%\%KEY_NAME%.key"
set "PUBLIC_KEY_PATH=%KEY_DIR%\%KEY_NAME%.pub"

if exist "%PRIVATE_KEY_PATH%" (
    echo.
    echo WARNING: Private key file already exists: %PRIVATE_KEY_PATH%
    set /p "overwrite=Overwrite existing key? (y/N): "
    if /i not "%overwrite%"=="y" (
        echo Operation cancelled
        pause
        exit /b 0
    )
)

echo.
echo Generating key pair...
npm run tauri signer generate -- -w "%PRIVATE_KEY_PATH%"
if errorlevel 1 (
    echo ERROR: Key generation failed
    pause
    exit /b 1
)
echo Key pair generated successfully!

echo.
echo Verifying generated files...
if exist "%PRIVATE_KEY_PATH%" (
    echo Private key file: %PRIVATE_KEY_PATH%
) else (
    echo ERROR: Private key file not found: %PRIVATE_KEY_PATH%
    pause
    exit /b 1
)

if exist "%PUBLIC_KEY_PATH%" (
    echo Public key file: %PUBLIC_KEY_PATH%
) else (
    echo ERROR: Public key file not found: %PUBLIC_KEY_PATH%
    pause
    exit /b 1
)

echo.
echo Public key content (for tauri.conf.json):
type "%PUBLIC_KEY_PATH%"

echo.
echo Setting environment variables...
for %%i in ("%PRIVATE_KEY_PATH%") do set "FULL_PRIVATE_KEY_PATH=%%~fi"
set "TAURI_SIGNING_PRIVATE_KEY=%FULL_PRIVATE_KEY_PATH%"
echo Set TAURI_SIGNING_PRIVATE_KEY = %FULL_PRIVATE_KEY_PATH%

echo.
echo Creating environment configuration file...
(
echo # RealityTap Desktop signing configuration (demo)
echo # Generated by generate-demo-keys.bat
echo.
echo # Tauri signing private key
echo TAURI_SIGNING_PRIVATE_KEY=%FULL_PRIVATE_KEY_PATH%
echo.
echo # Tauri signing private key password (demo key has no password)
echo TAURI_SIGNING_PRIVATE_KEY_PASSWORD=
) > .env.signing
echo Created environment configuration file: .env.signing

echo.
echo Next steps:
echo 1. You can now run build commands:
echo    npm run tauri:build
echo.
echo 2. For new terminals, set environment variable:
echo    set TAURI_SIGNING_PRIVATE_KEY=%FULL_PRIVATE_KEY_PATH%
echo.
echo 3. For production deployment:
echo    - Use secure key management system
echo    - Keep private key files secure
echo    - Rotate keys regularly

echo.
echo Demo key generation completed!
echo Key file locations:
echo    Private key: %PRIVATE_KEY_PATH%
echo    Public key: %PUBLIC_KEY_PATH%
echo.
pause
