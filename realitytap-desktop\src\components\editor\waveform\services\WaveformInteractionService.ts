/**
 * 波形交互服务
 * 处理所有用户交互，包括鼠标事件、拖拽逻辑、键盘事件等
 */

import type { RenderableEvent, RenderableContinuousEvent, RenderableContinuousCurvePoint } from "@/types/haptic-editor";
import { WaveformCoordinateService } from "./WaveformCoordinateService";

export interface InteractionConfig {
  clickRadius: number;
  dragThreshold: number;
  autoScrollSpeed: number;
  autoScrollBoundary: number;
}

export interface DragState {
  isDragging: boolean;
  draggedEventId: string | null;
  dragStartX: number;
  dragStartY: number;
  dragCurrentX: number;
  dragCurrentY: number;
  dragStartTime: number;
  originalStartTime: number;
  timeOffset: number;
  intensityOffset: number;
}

export interface MouseState {
  isMouseDown: boolean;
  mouseX: number;
  mouseY: number;
  lastClickTime: number;
  clickCount: number;
}

export interface HitTestResult {
  eventId: string | null;
  hitType: 'event' | 'background' | null;
  time: number;
  intensity: number;
}

export interface AutoScrollState {
  isScrolling: boolean;
  direction: 'left' | 'right' | null;
  speed: number;
}

export class WaveformInteractionService {
  private coordinateService: WaveformCoordinateService;
  private config: InteractionConfig;
  private dragState: DragState = {
    isDragging: false,
    draggedEventId: null,
    dragStartX: 0,
    dragStartY: 0,
    dragCurrentX: 0,
    dragCurrentY: 0,
    dragStartTime: 0,
    originalStartTime: 0,
    timeOffset: 0,
    intensityOffset: 0,
  };
  private mouseState: MouseState = {
    isMouseDown: false,
    mouseX: 0,
    mouseY: 0,
    lastClickTime: 0,
    clickCount: 0,
  };
  private autoScrollState: AutoScrollState = {
    isScrolling: false,
    direction: null,
    speed: 0,
  };

  constructor(coordinateService: WaveformCoordinateService, config: InteractionConfig) {
    this.coordinateService = coordinateService;
    this.config = config;
  }

  /**
   * 处理鼠标按下事件
   */
  handleMouseDown(
    event: MouseEvent,
    events: RenderableEvent[],
    containerRect: DOMRect
  ): HitTestResult {
    const rect = containerRect;
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    this.mouseState.isMouseDown = true;
    this.mouseState.mouseX = x;
    this.mouseState.mouseY = y;

    // 检测双击
    const now = Date.now();
    if (now - this.mouseState.lastClickTime < 300) {
      this.mouseState.clickCount++;
    } else {
      this.mouseState.clickCount = 1;
    }
    this.mouseState.lastClickTime = now;

    // 命中测试
    const hitResult = this.performHitTest(x, y, events);

    // 如果点击到事件，准备拖拽
    if (hitResult.eventId) {
      this.initializeDrag(hitResult.eventId, x, y, hitResult.time);
    }

    return hitResult;
  }

  /**
   * 处理鼠标移动事件
   */
  handleMouseMove(
    event: MouseEvent,
    containerRect: DOMRect,
    availableWidth: number
  ): { shouldUpdate: boolean; autoScroll: AutoScrollState } {
    const rect = containerRect;
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    this.mouseState.mouseX = x;
    this.mouseState.mouseY = y;

    let shouldUpdate = false;
    let autoScroll = { ...this.autoScrollState };

    if (this.dragState.isDragging) {
      // 更新拖拽位置
      this.dragState.dragCurrentX = x;
      this.dragState.dragCurrentY = y;

      // 计算时间和强度偏移
      const deltaX = x - this.dragState.dragStartX;
      // const deltaY = y - this.dragState.dragStartY; // 暂时不使用Y轴偏移

      this.dragState.timeOffset = this.coordinateService.mapXOffsetToTimeOffset(deltaX);
      this.dragState.intensityOffset = this.coordinateService.mapYToIntensity(this.dragState.dragStartY) -
                                      this.coordinateService.mapYToIntensity(y);

      // 检查是否需要自动滚动
      autoScroll = this.checkAutoScroll(x, availableWidth);

      shouldUpdate = true;
    }

    return { shouldUpdate, autoScroll };
  }

  /**
   * 处理鼠标释放事件
   */
  handleMouseUp(): { wasDragging: boolean; draggedEventId: string | null } {
    const wasDragging = this.dragState.isDragging;
    const draggedEventId = this.dragState.draggedEventId;

    this.mouseState.isMouseDown = false;
    this.resetDragState();
    this.resetAutoScroll();

    return { wasDragging, draggedEventId };
  }

  /**
   * 处理键盘事件
   */
  handleKeyDown(event: KeyboardEvent): { action: string; data?: any } {
    switch (event.key) {
      case 'Delete':
      case 'Backspace':
        return { action: 'delete' };
      case 'Escape':
        if (this.dragState.isDragging) {
          this.resetDragState();
          return { action: 'cancelDrag' };
        }
        return { action: 'clearSelection' };
      case 'ArrowLeft':
        return { action: 'moveSelection', data: { direction: 'left', amount: event.shiftKey ? 10 : 1 } };
      case 'ArrowRight':
        return { action: 'moveSelection', data: { direction: 'right', amount: event.shiftKey ? 10 : 1 } };
      case 'ArrowUp':
        return { action: 'adjustIntensity', data: { direction: 'up', amount: event.shiftKey ? 10 : 1 } };
      case 'ArrowDown':
        return { action: 'adjustIntensity', data: { direction: 'down', amount: event.shiftKey ? 10 : 1 } };
      default:
        return { action: 'none' };
    }
  }

  /**
   * 执行命中测试
   */
  private performHitTest(x: number, y: number, events: RenderableEvent[]): HitTestResult {
    const time = this.coordinateService.convertXToTime(x);
    const intensity = this.coordinateService.mapYToIntensity(y);

    // 从后往前检测（后绘制的事件优先）
    for (let i = events.length - 1; i >= 0; i--) {
      const event = events[i];

      if (this.isPointInEvent(x, y, event)) {
        return {
          eventId: event.id,
          hitType: 'event',
          time,
          intensity,
        };
      }
    }

    return {
      eventId: null,
      hitType: 'background',
      time,
      intensity,
    };
  }

  /**
   * 检查点是否在事件内
   */
  private isPointInEvent(x: number, y: number, event: RenderableEvent): boolean {
    const startX = this.coordinateService.mapTimeToX(event.startTime);
    const endX = this.coordinateService.mapTimeToX(event.stopTime);

    // 检查X范围
    if (x < startX - this.config.clickRadius || x > endX + this.config.clickRadius) {
      return false;
    }

    if (event.type === 'transient') {
      // 瞬态事件：检查三角形区域
      const peakX = this.coordinateService.mapTimeToX(event.peakTime);
      const peakY = this.coordinateService.mapIntensityToY(event.intensity);
      const baseY = this.coordinateService.mapIntensityToY(0);

      return this.isPointInTriangle(x, y, startX, baseY, peakX, peakY, endX, baseY);
    } else if (event.type === 'continuous') {
      // 连续事件：检查曲线附近
      return this.isPointNearContinuousEvent(x, y, event);
    }

    return false;
  }

  /**
   * 检查点是否在三角形内
   */
  private isPointInTriangle(
    px: number, py: number,
    x1: number, y1: number,
    x2: number, y2: number,
    x3: number, y3: number
  ): boolean {
    const denominator = (y2 - y3) * (x1 - x3) + (x3 - x2) * (y1 - y3);
    if (Math.abs(denominator) < 1e-10) return false;

    const a = ((y2 - y3) * (px - x3) + (x3 - x2) * (py - y3)) / denominator;
    const b = ((y3 - y1) * (px - x3) + (x1 - x3) * (py - y3)) / denominator;
    const c = 1 - a - b;

    return a >= 0 && b >= 0 && c >= 0;
  }

  /**
   * 检查点是否靠近连续事件
   */
  private isPointNearContinuousEvent(_x: number, y: number, event: RenderableEvent): boolean {
    // 简化实现：检查是否在事件的边界框内
    if (event.type !== 'continuous') return false;

    const continuousEvent = event as RenderableContinuousEvent;
    const minY = Math.min(...continuousEvent.curves.map((p: RenderableContinuousCurvePoint) =>
      this.coordinateService.mapIntensityToY(p.drawIntensity)
    ));
    const maxY = Math.max(...continuousEvent.curves.map((p: RenderableContinuousCurvePoint) =>
      this.coordinateService.mapIntensityToY(p.drawIntensity)
    ));

    return y >= minY - this.config.clickRadius && y <= maxY + this.config.clickRadius;
  }

  /**
   * 初始化拖拽
   */
  private initializeDrag(eventId: string, x: number, y: number, time: number): void {
    this.dragState = {
      isDragging: false, // 等到移动超过阈值才设为true
      draggedEventId: eventId,
      dragStartX: x,
      dragStartY: y,
      dragCurrentX: x,
      dragCurrentY: y,
      dragStartTime: time,
      originalStartTime: time,
      timeOffset: 0,
      intensityOffset: 0,
    };
  }

  /**
   * 检查是否需要自动滚动
   */
  private checkAutoScroll(x: number, availableWidth: number): AutoScrollState {
    const boundary = this.config.autoScrollBoundary;

    if (x < boundary) {
      return {
        isScrolling: true,
        direction: 'left',
        speed: this.config.autoScrollSpeed * (1 - x / boundary),
      };
    } else if (x > availableWidth - boundary) {
      return {
        isScrolling: true,
        direction: 'right',
        speed: this.config.autoScrollSpeed * ((x - (availableWidth - boundary)) / boundary),
      };
    }

    return {
      isScrolling: false,
      direction: null,
      speed: 0,
    };
  }

  /**
   * 重置拖拽状态
   */
  private resetDragState(): void {
    this.dragState = {
      isDragging: false,
      draggedEventId: null,
      dragStartX: 0,
      dragStartY: 0,
      dragCurrentX: 0,
      dragCurrentY: 0,
      dragStartTime: 0,
      originalStartTime: 0,
      timeOffset: 0,
      intensityOffset: 0,
    };
  }

  /**
   * 重置自动滚动状态
   */
  private resetAutoScroll(): void {
    this.autoScrollState = {
      isScrolling: false,
      direction: null,
      speed: 0,
    };
  }

  /**
   * 检查是否应该开始拖拽
   */
  shouldStartDrag(): boolean {
    if (this.dragState.draggedEventId && !this.dragState.isDragging) {
      const deltaX = this.dragState.dragCurrentX - this.dragState.dragStartX;
      const deltaY = this.dragState.dragCurrentY - this.dragState.dragStartY;
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      if (distance > this.config.dragThreshold) {
        this.dragState.isDragging = true;
        return true;
      }
    }
    return false;
  }

  /**
   * 获取拖拽状态
   */
  getDragState(): DragState {
    return { ...this.dragState };
  }

  /**
   * 获取鼠标状态
   */
  getMouseState(): MouseState {
    return { ...this.mouseState };
  }

  /**
   * 获取自动滚动状态
   */
  getAutoScrollState(): AutoScrollState {
    return { ...this.autoScrollState };
  }

  /**
   * 是否为双击
   */
  isDoubleClick(): boolean {
    return this.mouseState.clickCount >= 2;
  }
}
