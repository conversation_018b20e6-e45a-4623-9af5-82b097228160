/**
 * 音频文件验证工具函数
 * 提供音频文件时长验证等功能
 */

import { invoke } from "@tauri-apps/api/core";
import { MAX_HAPTIC_DURATION_MS } from "@/components/editor/waveform/config/waveform-constants";

/**
 * 音频验证结果接口
 */
export interface AudioValidationResult {
  isValid: boolean;
  actualDurationMs?: number;
  maxAllowedMs: number;
  errorMessage?: string;
}

/**
 * 音频信息接口（与后端保持一致）
 * 注意：后端使用 camelCase 序列化
 */
interface AudioInfo {
  durationMs: number;
  sampleRate: number;
}

/**
 * 验证音频文件时长是否符合要求
 *
 * @param audioFilePath 音频文件的绝对路径
 * @returns Promise<AudioValidationResult> 验证结果
 */
export async function validateAudioDuration(audioFilePath: string): Promise<AudioValidationResult> {
  try {
    // 使用后端命令获取音频信息（更可靠的方法）
    const audioInfo = await getAudioInfoFromBackend(audioFilePath);

    if (!audioInfo) {
      return {
        isValid: false,
        maxAllowedMs: MAX_HAPTIC_DURATION_MS,
        errorMessage: "无法读取音频文件信息，请确认文件格式正确"
      };
    }

    const actualDurationMs = audioInfo.durationMs;
    const isValid = actualDurationMs <= MAX_HAPTIC_DURATION_MS;

    return {
      isValid,
      actualDurationMs,
      maxAllowedMs: MAX_HAPTIC_DURATION_MS,
      errorMessage: isValid
        ? undefined
        : `音频文件时长超出限制，最大允许时长为 ${MAX_HAPTIC_DURATION_MS}ms，当前文件时长为 ${actualDurationMs}ms`
    };

  } catch (error: any) {
    console.error("音频时长验证失败:", error);
    return {
      isValid: false,
      maxAllowedMs: MAX_HAPTIC_DURATION_MS,
      errorMessage: `音频文件验证失败: ${error.message || "未知错误"}`
    };
  }
}

/**
 * 使用后端命令获取音频文件信息
 * 这是更可靠的方法，直接调用后端的音频解析功能
 *
 * @param audioFilePath 音频文件的绝对路径
 * @returns Promise<AudioInfo | null> 音频信息或null
 */
async function getAudioInfoFromBackend(audioFilePath: string): Promise<AudioInfo | null> {
  try {
    // 使用后端的 get_audio_info_from_file 命令
    // 这个命令直接接受文件路径，不需要项目上下文
    const audioInfo = await invoke<AudioInfo | null>("get_audio_info_from_file", {
      filePath: audioFilePath
    });

    return audioInfo;
  } catch (error: any) {
    console.error("后端获取音频信息失败:", error);
    return null;
  }
}

/**
 * 格式化时长显示
 * 
 * @param durationMs 时长（毫秒）
 * @returns 格式化的时长字符串
 */
export function formatDuration(durationMs: number): string {
  if (durationMs < 1000) {
    return `${durationMs}ms`;
  } else if (durationMs < 60000) {
    const seconds = (durationMs / 1000).toFixed(1);
    return `${seconds}s`;
  } else {
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }
}
