#!/usr/bin/env tsx

import path from 'path';
import { z } from 'zod';
import { FileUtil } from '../src/utils/file.util';
import { logger } from '../src/utils/logger.util';

// 版本配置验证模式
const PlatformReleaseSchema = z.object({
  filename: z.string().min(1),
  size: z.number().positive(),
  checksum: z.string().regex(/^(sha256|md5):[a-fA-F0-9]+$/),
  releaseDate: z.string().datetime(),
  releaseNotes: z.string(),
});

const ServerVersionInfoSchema = z.object({
  version: z.string().regex(/^\d+\.\d+\.\d+/),
  platforms: z.record(z.record(PlatformReleaseSchema)),
});

const VersionsConfigSchema = z.object({
  channels: z.record(ServerVersionInfoSchema),
  minimumVersions: z.record(z.string()),
  deprecatedVersions: z.array(z.string()),
});

// 渠道配置验证模式
const ChannelConfigSchema = z.object({
  enabled: z.boolean(),
  description: z.string(),
  autoUpdate: z.boolean(),
  rolloutPercentage: z.number().min(0).max(100),
  priority: z.number().positive(),
});

const ChannelsConfigSchema = z.record(ChannelConfigSchema);

async function validateConfig(): Promise<void> {
  try {
    console.log('🔍 Validating configuration files...');

    const storagePath = process.env.STORAGE_PATH || './storage';
    const metadataPath = path.join(storagePath, 'metadata');

    // 验证版本配置
    const versionsPath = path.join(metadataPath, 'versions.json');
    if (await FileUtil.exists(versionsPath)) {
      console.log('📋 Validating versions.json...');
      const versionsConfig = await FileUtil.readJSON(versionsPath);
      const versionsResult = VersionsConfigSchema.safeParse(versionsConfig);
      
      if (!versionsResult.success) {
        console.error('❌ versions.json validation failed:');
        versionsResult.error.errors.forEach(error => {
          console.error(`  - ${error.path.join('.')}: ${error.message}`);
        });
        process.exit(1);
      }
      console.log('✅ versions.json is valid');
    } else {
      console.error('❌ versions.json not found');
      process.exit(1);
    }

    // 验证渠道配置
    const channelsPath = path.join(metadataPath, 'channels.json');
    if (await FileUtil.exists(channelsPath)) {
      console.log('📋 Validating channels.json...');
      const channelsConfig = await FileUtil.readJSON(channelsPath);
      const channelsResult = ChannelsConfigSchema.safeParse(channelsConfig);
      
      if (!channelsResult.success) {
        console.error('❌ channels.json validation failed:');
        channelsResult.error.errors.forEach(error => {
          console.error(`  - ${error.path.join('.')}: ${error.message}`);
        });
        process.exit(1);
      }
      console.log('✅ channels.json is valid');
    } else {
      console.error('❌ channels.json not found');
      process.exit(1);
    }

    // 验证文件存在性
    console.log('📁 Checking release files...');
    const versionsConfig = await FileUtil.readJSON(versionsPath);
    const releasesPath = path.join(storagePath, 'releases');
    
    let missingFiles = 0;
    for (const [channel, versionInfo] of Object.entries(versionsConfig.channels)) {
      for (const [platform, platformData] of Object.entries((versionInfo as any).platforms)) {
        for (const [arch, release] of Object.entries(platformData as any)) {
          const filename = (release as any).filename;
          const filePath = path.join(releasesPath, channel, filename);
          
          if (!await FileUtil.exists(filePath)) {
            console.warn(`⚠️  Missing file: ${filePath}`);
            missingFiles++;
          }
        }
      }
    }

    if (missingFiles > 0) {
      console.warn(`⚠️  Found ${missingFiles} missing release files`);
    } else {
      console.log('✅ All release files exist');
    }

    console.log('🎉 Configuration validation completed successfully!');
  } catch (error) {
    console.error('❌ Configuration validation failed:', error);
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  validateConfig().catch(error => {
    console.error('❌ Validation script failed:', error);
    process.exit(1);
  });
}
