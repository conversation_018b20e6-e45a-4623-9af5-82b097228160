/**
 * RealityTap 数组转换工具相关类型定义
 * 基于 Java 实现的 convertToArray 函数逻辑
 */

import type { RealityTapEffect, RealityTapEffectV1, RealityTapEffectV2 } from './haptic-file';
import type { RenderableEvent } from './haptic-editor';

// ===== 转换选项配置 =====

/**
 * 转换选项配置接口
 */
export interface ConvertToArrayOptions {
  /** V2专用：进程ID，默认为0 */
  processId?: number;
  
  /** V2专用：振动序列号，默认为0 */
  sequence?: number;
  
  /** 是否验证输入数据，默认为true */
  validateInput?: boolean;
  
  /** 严格模式：启用更严格的数据验证，默认为false */
  strictMode?: boolean;
  
  /** 是否启用调试日志，默认为false */
  enableLogging?: boolean;
  
  /** 自定义版本（当无法自动检测时使用） */
  forceVersion?: 1 | 2;
}

// ===== 转换结果接口 =====

/**
 * 转换结果元数据
 */
export interface ConvertToArrayMetadata {
  /** 处理的事件总数 */
  totalEvents: number;
  
  /** 生成的数组长度 */
  arrayLength: number;
  
  /** 处理耗时（毫秒） */
  processingTime?: number;
  
  /** 检测到的版本 */
  detectedVersion: 1 | 2;
  
  /** 是否使用了强制版本 */
  usedForceVersion?: boolean;
  
  /** 处理的PatternList数量（仅V2） */
  patternListCount?: number;
  
  /** 跳过的无效事件数量 */
  skippedEvents?: number;
}

/**
 * 转换结果接口
 */
export interface ConvertToArrayResult {
  /** 转换是否成功 */
  success: boolean;
  
  /** 转换后的数组数据 */
  data?: number[];
  
  /** 错误信息（转换失败时） */
  error?: string;
  
  /** 检测到的版本号 */
  version?: 1 | 2;
  
  /** 转换元数据 */
  metadata?: ConvertToArrayMetadata;
  
  /** 警告信息列表 */
  warnings?: string[];
}

// ===== 支持的输入数据类型 =====

/**
 * 支持的输入数据类型联合
 */
export type ConvertToArrayInput = 
  | RealityTapEffect 
  | RealityTapEffectV1 
  | RealityTapEffectV2 
  | RenderableEvent[]
  | unknown; // 允许未知类型，在函数内部进行类型检查

// ===== 版本检测相关 =====

/**
 * 版本检测结果
 */
export interface VersionDetectionResult {
  /** 检测是否成功 */
  success: boolean;
  
  /** 检测到的版本 */
  version?: 1 | 2;
  
  /** 检测依据 */
  detectionMethod: 'metadata' | 'structure' | 'forced' | 'default';
  
  /** 检测置信度 (0-1) */
  confidence: number;
  
  /** 检测详情 */
  details?: string;
}

// ===== 数据验证相关 =====

/**
 * 数据验证结果
 */
export interface ValidationResult {
  /** 验证是否通过 */
  isValid: boolean;
  
  /** 错误信息列表 */
  errors: string[];
  
  /** 警告信息列表 */
  warnings: string[];
  
  /** 验证的字段统计 */
  fieldStats?: {
    totalFields: number;
    validFields: number;
    invalidFields: number;
  };
}

// ===== 事件类型常量 =====

/**
 * 事件类型常量（对应Java实现）
 */
export const EVENT_TYPES = {
  /** 瞬时事件类型标识 */
  TRANSIENT: 4097,
  
  /** 连续事件类型标识 */
  CONTINUOUS: 4096,
} as const;

/**
 * 事件类型字符串常量
 */
export const EVENT_TYPE_STRINGS = {
  TRANSIENT: 'transient',
  CONTINUOUS: 'continuous',
} as const;

// ===== V1 转换相关常量 =====

/**
 * V1 版本转换常量
 */
export const V1_CONSTANTS = {
  /** 每个事件在数组中占用的位置数 */
  POSITIONS_PER_EVENT: 17,
  
  /** TRANSIENT 事件的数据位置数 */
  TRANSIENT_DATA_POSITIONS: 4,
  
  /** CONTINUOUS 事件的基础数据位置数 */
  CONTINUOUS_BASE_POSITIONS: 5,
  
  /** 每个曲线点占用的位置数 */
  POSITIONS_PER_CURVE: 3,
  
  /** 固定的曲线点数量 */
  FIXED_CURVE_COUNT: 4,
  
  /** 强度缩放因子 */
  INTENSITY_SCALE_FACTOR: 100,
} as const;

// ===== V2 转换相关常量 =====

/**
 * V2 版本转换常量
 */
export const V2_CONSTANTS = {
  /** 头部信息元素数量 */
  HEADER_SIZE: 5,
  
  /** 每个PatternListItem的固定字段数 */
  PATTERN_LIST_ITEM_FIELDS: 3,
  
  /** 每个事件的固定头部字段数 */
  EVENT_HEADER_FIELDS: 2,
  
  /** TRANSIENT 事件的固定长度 */
  TRANSIENT_EVENT_LENGTH: 5,
  
  /** CONTINUOUS 事件的基础长度 */
  CONTINUOUS_BASE_LENGTH: 6,
  
  /** 每个曲线点的字段数 */
  CURVE_POINT_FIELDS: 3,
  
  /** 强度缩放因子 */
  INTENSITY_SCALE_FACTOR: 100,
  
  /** TRANSIENT 事件的固定持续时间 */
  TRANSIENT_DURATION: 48,
} as const;

// ===== 数据范围验证常量 =====

/**
 * 数据范围验证常量
 */
export const VALIDATION_RANGES = {
  /** 强度范围 */
  INTENSITY: { min: 0, max: 100 },
  
  /** 频率范围 */
  FREQUENCY: { min: 0, max: 100 },
  
  /** 相对频率范围 */
  RELATIVE_FREQUENCY: { min: -100, max: 100 },
  
  /** 时间范围（毫秒） */
  TIME: { min: 0, max: Number.MAX_SAFE_INTEGER },
  
  /** 马达索引范围（V2） */
  MOTOR_INDEX: { min: 0, max: 1 },
  
  /** 曲线强度范围 */
  CURVE_INTENSITY: { min: 0, max: 1 },
} as const;

// ===== 错误类型定义 =====

/**
 * 转换错误类型枚举（保留用于未来扩展）
 */
export enum ConvertToArrayErrorType {
  INVALID_INPUT = 'INVALID_INPUT',
  UNSUPPORTED_VERSION = 'UNSUPPORTED_VERSION',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  MISSING_METADATA = 'MISSING_METADATA',
  MISSING_EVENTS = 'MISSING_EVENTS',
  INVALID_EVENT_TYPE = 'INVALID_EVENT_TYPE',
  INVALID_CURVE_DATA = 'INVALID_CURVE_DATA',
  ARRAY_SIZE_OVERFLOW = 'ARRAY_SIZE_OVERFLOW',
  PROCESSING_ERROR = 'PROCESSING_ERROR',
}

/**
 * 转换错误详情接口（保留用于未来扩展）
 * 注意：当前实现主要使用简单的字符串错误消息
 */
export interface ConvertToArrayError {
  type: ConvertToArrayErrorType;
  message: string;
  details?: any;
  eventId?: string;
  eventIndex?: number;
}
