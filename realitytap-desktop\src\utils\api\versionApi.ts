/**
 * 版本信息相关 API 函数
 * Version information related API functions
 */

import { invoke } from "@tauri-apps/api/core";
import type { AppVersionInfo } from "@/types/version";

/**
 * 获取应用版本信息
 * Get application version information
 */
export async function getAppVersionInfo(): Promise<AppVersionInfo> {
  try {
    const versionInfo = await invoke<AppVersionInfo>("get_app_version_info");
    return versionInfo;
  } catch (error) {
    console.error("Failed to get app version info:", error);
    throw new Error(`获取版本信息失败: ${error}`);
  }
}

/**
 * 格式化版本显示文本
 * Format version display text
 */
export function formatVersionText(versionInfo: AppVersionInfo): string {
  return `v${versionInfo.appVersion}`;
}

/**
 * 格式化构建信息文本
 * Format build information text
 */
export function formatBuildInfo(versionInfo: AppVersionInfo): string {
  // 将UTC时间转换为CST时间（UTC+8）
  const cstBuildDate = convertUtcToCst(versionInfo.buildDate);
  return `${cstBuildDate} (${versionInfo.buildMode})`;
}

/**
 * 将UTC时间字符串转换为CST时间字符串
 * Convert UTC time string to CST time string
 */
function convertUtcToCst(utcTimeString: string): string {
  try {
    // 解析UTC时间字符串，格式如 "2025-06-17 22:35:59 UTC"
    const utcMatch = utcTimeString.match(/^(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2}) UTC$/);

    if (!utcMatch) {
      // 如果格式不匹配，直接返回原字符串
      return utcTimeString;
    }

    const [, datePart, timePart] = utcMatch;
    const utcDateTime = new Date(`${datePart}T${timePart}Z`);

    // 检查日期是否有效
    if (isNaN(utcDateTime.getTime())) {
      return utcTimeString;
    }

    // 转换为CST时间（UTC+8）
    const cstDateTime = new Date(utcDateTime.getTime() + 8 * 60 * 60 * 1000);

    // 格式化为 "YYYY-MM-DD HH:MM:SS CST"
    const year = cstDateTime.getUTCFullYear();
    const month = String(cstDateTime.getUTCMonth() + 1).padStart(2, '0');
    const day = String(cstDateTime.getUTCDate()).padStart(2, '0');
    const hours = String(cstDateTime.getUTCHours()).padStart(2, '0');
    const minutes = String(cstDateTime.getUTCMinutes()).padStart(2, '0');
    const seconds = String(cstDateTime.getUTCSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds} CST`;
  } catch (error) {
    console.warn('Failed to convert UTC time to CST:', error);
    return utcTimeString;
  }
}

/**
 * 格式化平台信息文本
 * Format platform information text
 */
export function formatPlatformInfo(versionInfo: AppVersionInfo): string {
  return `${versionInfo.targetOs}-${versionInfo.targetArch}`;
}
