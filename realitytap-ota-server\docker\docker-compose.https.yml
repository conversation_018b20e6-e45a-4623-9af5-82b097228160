version: '3.8'

services:
  realitytap-ota-server:
    build:
      context: ..
      dockerfile: docker/Dockerfile.production
    container_name: realitytap-ota-server
    restart: always
    ports:
      - "${HOST_PORT:-3000}:${CONTAINER_PORT:-3000}"
    environment:
      # Server Configuration
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=${CONTAINER_PORT:-3000}
      - HOST=0.0.0.0
      - BASE_URL=${BASE_URL:-https://localhost:3000}
      - ENABLE_HTTPS=true
      
      # SSL Configuration
      - SSL_CERT_PATH=/app/ssl/cert.pem
      - SSL_KEY_PATH=/app/ssl/key.pem
      
      # Admin Configuration
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-1h}
      - SESSION_TIMEOUT=${SESSION_TIMEOUT:-3600000}
      
      # Database Configuration
      - DB_ENABLED=${DB_ENABLED:-true}
      - DB_PATH=/app/storage/database/ota.db
      - DB_BACKUP_PATH=/app/storage/backup/database
      - DB_MAX_CONNECTIONS=${DB_MAX_CONNECTIONS:-10}
      - DB_BUSY_TIMEOUT=${DB_BUSY_TIMEOUT:-30000}
      - DB_ENABLE_WAL=${DB_ENABLE_WAL:-true}
      
      # Storage Configuration
      - STORAGE_PATH=/app/storage
      - RELEASES_PATH=/app/storage/releases
      - METADATA_PATH=/app/storage/metadata
      - LOGS_PATH=/app/storage/logs
      - TEMP_PATH=/app/storage/temp
      
      # Security Configuration
      - CORS_ORIGIN=${CORS_ORIGIN:-https://localhost:3000}
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-900000}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-100}
      - ADMIN_RATE_LIMIT_WINDOW_MS=${ADMIN_RATE_LIMIT_WINDOW_MS:-300000}
      - ADMIN_RATE_LIMIT_MAX_REQUESTS=${ADMIN_RATE_LIMIT_MAX_REQUESTS:-500}
      - PUBLIC_RATE_LIMIT_WINDOW_MS=${PUBLIC_RATE_LIMIT_WINDOW_MS:-300000}
      - PUBLIC_RATE_LIMIT_MAX_REQUESTS=${PUBLIC_RATE_LIMIT_MAX_REQUESTS:-200}
      
      # Logging Configuration
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FORMAT=${LOG_FORMAT:-json}
      - LOG_MAX_SIZE=${LOG_MAX_SIZE:-20m}
      - LOG_MAX_FILES=${LOG_MAX_FILES:-14d}
      
      # File Upload Configuration
      - ADMIN_MAX_FILE_SIZE=${ADMIN_MAX_FILE_SIZE:-104857600}
      - ADMIN_ALLOWED_FILE_TYPES=${ADMIN_ALLOWED_FILE_TYPES:-.exe,.msi,.dmg,.deb,.rpm,.tar.gz,.zip,.pkg}
      
      # Tauri Updater Configuration
      - TAURI_PRIVATE_KEY_PATH=${TAURI_PRIVATE_KEY_PATH:-/app/keys/private.key}
      - TAURI_KEY_PASSWORD=${TAURI_KEY_PASSWORD:-}
      
      # Debug Configuration
      - DEBUG=${DEBUG:-false}
      
    volumes:
      # Direct host directory mapping for easier access and management
      - ${STORAGE_HOST_PATH:-./data/storage}:/app/storage

      # SSL certificates (required for HTTPS mode)
      - ${SSL_CERT_HOST_PATH:-./ssl/cert.pem}:/app/ssl/cert.pem:ro
      - ${SSL_KEY_HOST_PATH:-./ssl/key.pem}:/app/ssl/key.pem:ro

      # Optional: Mount signing keys from host
      - ${KEYS_HOST_PATH:-./keys}:/app/keys:ro
      
    networks:
      - realitytap-network
      
    healthcheck:
      test: ["CMD", "curl", "-k", "-f", "https://localhost:${CONTAINER_PORT:-3000}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
      
    labels:
      - "com.realitytap.service=ota-server"
      - "com.realitytap.version=${APP_VERSION:-latest}"
      - "com.realitytap.environment=${NODE_ENV:-production}"
      - "com.realitytap.https=enabled"

  # Nginx reverse proxy with SSL termination
  nginx:
    image: nginx:alpine
    container_name: realitytap-nginx
    restart: always
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.https.conf:/etc/nginx/nginx.conf:ro
      - ${SSL_CERT_HOST_PATH:-./ssl/cert.pem}:/etc/nginx/ssl/cert.pem:ro
      - ${SSL_KEY_HOST_PATH:-./ssl/key.pem}:/etc/nginx/ssl/key.pem:ro
    depends_on:
      - realitytap-ota-server
    networks:
      - realitytap-network
    profiles:
      - nginx
    labels:
      - "com.realitytap.service=nginx-proxy"
      - "com.realitytap.https=enabled"

networks:
  realitytap-network:
    driver: bridge
    name: realitytap-network
