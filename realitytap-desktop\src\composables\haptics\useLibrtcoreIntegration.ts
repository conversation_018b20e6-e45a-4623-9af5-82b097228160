/**
 * librtcore 算法库集成组合函数
 * 管理 librtcore 的初始化、重新初始化和生命周期
 */

import { ref, computed, onUnmounted, type Ref, type ComputedRef } from "vue";
import { LogModule, logger } from "@/utils/logger/logger";
import { hapticApi, HAPTIC_CONSTANTS } from "@/utils/api/haptic-api";
import type { HapticActuatorConfig, SamplingRateType } from "@/types/haptic-types";
import type { MotorModel } from "@/types/play-effect-dialog";

/**
 * librtcore 初始化状态枚举
 */
export enum LibrtcoreInitState {
  UNINITIALIZED = "uninitialized",
  INITIALIZING = "initializing",
  INITIALIZED = "initialized",
  REINITIALIZING = "reinitializing",
  ERROR = "error",
  CLEANUP = "cleanup",
}

/**
 * librtcore 集成状态接口
 */
export interface LibrtcoreIntegrationState {
  /** 初始化状态 */
  initState: LibrtcoreInitState;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 当前配置 */
  currentConfig: HapticActuatorConfig | null;
  /** 是否已启动播放系统 */
  isStarted: boolean;
  /** 重试次数 */
  retryCount: number;
}

/**
 * librtcore 集成组合函数返回类型
 */
export interface UseLibrtcoreIntegrationReturn {
  /** 状态 */
  state: Ref<LibrtcoreIntegrationState>;
  /** 计算属性 */
  isInitialized: ComputedRef<boolean>;
  canPlay: ComputedRef<boolean>;
  isLoading: ComputedRef<boolean>;
  hasError: ComputedRef<boolean>;
  /** 方法 */
  initializeLibrtcore: (motorModel: MotorModel, samplingRate: SamplingRateType) => Promise<boolean>;
  reinitializeLibrtcore: (motorModel: MotorModel, samplingRate: SamplingRateType) => Promise<boolean>;
  cleanup: () => Promise<void>;
  retry: () => Promise<boolean>;
  clearError: () => void;
}

/**
 * librtcore 集成配置选项
 */
export interface LibrtcoreIntegrationOptions {
  /** 是否启用重试机制 */
  enableRetry?: boolean;
  /** 最大重试次数 */
  maxRetries?: number;
}

/**
 * librtcore 算法库集成组合函数
 */
export function useLibrtcoreIntegration(options: LibrtcoreIntegrationOptions = {}): UseLibrtcoreIntegrationReturn {
  const { enableRetry = false, maxRetries = 3 } = options;

  // ===== 状态管理 =====

  const state = ref<LibrtcoreIntegrationState>({
    initState: LibrtcoreInitState.UNINITIALIZED,
    isLoading: false,
    error: null,
    currentConfig: null,
    isStarted: false,
    retryCount: 0,
  });

  // 存储当前参数用于重试
  const lastInitParams = ref<{
    motorModel: MotorModel;
    samplingRate: SamplingRateType;
  } | null>(null);

  // ===== 计算属性 =====

  const isInitialized = computed(() => state.value.initState === LibrtcoreInitState.INITIALIZED);

  const canPlay = computed(() => isInitialized.value && state.value.isStarted && !state.value.isLoading);

  const isLoading = computed(
    () => state.value.isLoading || state.value.initState === LibrtcoreInitState.INITIALIZING || state.value.initState === LibrtcoreInitState.REINITIALIZING
  );

  const hasError = computed(() => state.value.initState === LibrtcoreInitState.ERROR || state.value.error !== null);

  // ===== 私有方法 =====

  /**
   * 创建设备配置
   */
  const createDeviceConfig = (motorModel: MotorModel, samplingRate: SamplingRateType): HapticActuatorConfig => {
    return {
      id: 0, // 默认设备ID
      motor_drive_freq: HAPTIC_CONSTANTS.DEFAULT_MOTOR_DRIVE_FREQ,
      config_file: motorModel.configPath || HAPTIC_CONSTANTS.DEFAULT_CONFIG_FILE,
      sampling_rate: samplingRate,
      buffer_size: 1024,
      timeout_ms: HAPTIC_CONSTANTS.DEFAULT_TIMEOUT_MS,
      enable_logging: true,
    };
  };

  /**
   * 安全地序列化错误对象用于日志记录
   */
  const serializeError = (error: any): any => {
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack,
        cause: (error as any).cause, // 安全访问 cause 属性
      };
    }

    if (error && typeof error === "object") {
      try {
        // 尝试序列化对象
        return JSON.parse(JSON.stringify(error));
      } catch {
        // 如果序列化失败，提取关键属性
        return {
          type: Object.prototype.toString.call(error),
          message: error.message || error.toString(),
          code: error.code,
          details: error.details,
          originalError: String(error),
        };
      }
    }

    return { value: String(error) };
  };

  /**
   * 提取用户友好的错误消息
   */
  const extractErrorMessage = (error: any): string => {
    if (error instanceof Error) {
      return error.message;
    }

    if (error && typeof error === "object") {
      // 检查 HapticApiError 格式
      if ("details" in error && error.details) {
        return error.details;
      }
      if ("message" in error && error.message) {
        return error.message;
      }
      // 检查 Tauri 错误格式
      if ("error" in error && error.error) {
        return error.error;
      }
      // 检查其他常见错误格式
      if ("description" in error && error.description) {
        return error.description;
      }
    }

    return String(error);
  };

  /**
   * 处理初始化错误
   */
  const handleInitError = (error: any, operation: string): void => {
    const errorMessage = extractErrorMessage(error);
    const serializedError = serializeError(error);

    logger.error(LogModule.GENERAL, `${operation}失败`, {
      error: serializedError,
      errorMessage,
      retryCount: state.value.retryCount,
      operation,
      enableRetry,
    });

    state.value.initState = LibrtcoreInitState.ERROR;
    state.value.error = `${operation}失败: ${errorMessage}`;
    state.value.isLoading = false;
  };

  /**
   * 执行初始化流程
   */
  const performInitialization = async (motorModel: MotorModel, samplingRate: SamplingRateType, isReinit: boolean = false): Promise<boolean> => {
    try {
      // 设置状态
      state.value.initState = isReinit ? LibrtcoreInitState.REINITIALIZING : LibrtcoreInitState.INITIALIZING;
      state.value.isLoading = true;
      state.value.error = null;

      logger.info(LogModule.GENERAL, `开始${isReinit ? "重新" : ""}初始化 librtcore`, {
        motorModel: motorModel.name,
        samplingRate,
        configPath: motorModel.configPath,
        isReinit,
      });

      // 创建设备配置
      const config = createDeviceConfig(motorModel, samplingRate);

      logger.debug(LogModule.GENERAL, "创建的设备配置", {
        config: {
          id: config.id,
          motor_drive_freq: config.motor_drive_freq,
          config_file: config.config_file,
          sampling_rate: config.sampling_rate,
          buffer_size: config.buffer_size,
          timeout_ms: config.timeout_ms,
          enable_logging: config.enable_logging,
        },
      });

      // 验证配置文件
      logger.debug(LogModule.GENERAL, "开始验证配置文件", { configFile: config.config_file });

      try {
        const isValidConfig = await hapticApi.validateConfigFile(config.config_file);
        logger.debug(LogModule.GENERAL, "配置文件验证结果", {
          configFile: config.config_file,
          isValid: isValidConfig,
        });

        if (!isValidConfig) {
          throw new Error(`配置文件不存在或无效: ${config.config_file}`);
        }
      } catch (validateError) {
        logger.error(LogModule.GENERAL, "配置文件验证失败", {
          configFile: config.config_file,
          error: serializeError(validateError),
        });
        throw validateError;
      }

      // 如果是重新初始化，先清理现有资源
      if (isReinit) {
        try {
          logger.debug(LogModule.GENERAL, "开始清理现有资源");
          await hapticApi.cleanup();
          logger.info(LogModule.GENERAL, "清理现有资源成功");
        } catch (cleanupError) {
          logger.warn(LogModule.GENERAL, "清理现有资源时出现警告", {
            error: serializeError(cleanupError),
          });
          // 清理失败不阻止重新初始化
        }
      }

      // 初始化 librtcore
      logger.debug(LogModule.GENERAL, `开始调用 ${isReinit ? "reinitialize" : "initialize"} API`);

      try {
        const initResult = isReinit ? await hapticApi.reinitialize([config]) : await hapticApi.initialize([config]);

        logger.info(LogModule.GENERAL, `librtcore ${isReinit ? "重新" : ""}初始化成功`, {
          result: initResult,
          config: {
            id: config.id,
            sampling_rate: config.sampling_rate,
            config_file: config.config_file,
          },
        });
      } catch (initError) {
        logger.error(LogModule.GENERAL, `librtcore ${isReinit ? "重新" : ""}初始化失败`, {
          error: serializeError(initError),
          config: {
            id: config.id,
            sampling_rate: config.sampling_rate,
            config_file: config.config_file,
          },
        });
        throw initError;
      }

      // 启动播放系统
      logger.debug(LogModule.GENERAL, "开始启动播放系统");

      try {
        const startResult = await hapticApi.start();
        logger.info(LogModule.GENERAL, "播放系统启动成功", { result: startResult });
      } catch (startError) {
        logger.error(LogModule.GENERAL, "播放系统启动失败", {
          error: serializeError(startError),
        });
        throw startError;
      }

      // 更新状态
      state.value.initState = LibrtcoreInitState.INITIALIZED;
      state.value.currentConfig = config;
      state.value.isStarted = true;
      state.value.isLoading = false;
      state.value.retryCount = 0;

      // 存储参数用于重试
      lastInitParams.value = { motorModel, samplingRate };

      // 注意：不显示成功通知，避免打扰用户
      return true;
    } catch (error) {
      handleInitError(error, isReinit ? "重新初始化" : "初始化");
      return false;
    }
  };

  // ===== 公共方法 =====

  /**
   * 初始化 librtcore 算法库
   */
  const initializeLibrtcore = async (motorModel: MotorModel, samplingRate: SamplingRateType): Promise<boolean> => {
    if (state.value.initState === LibrtcoreInitState.INITIALIZING) {
      logger.warn(LogModule.GENERAL, "正在初始化中，忽略重复请求");
      return false;
    }

    return await performInitialization(motorModel, samplingRate, false);
  };

  /**
   * 重新初始化 librtcore 算法库
   */
  const reinitializeLibrtcore = async (motorModel: MotorModel, samplingRate: SamplingRateType): Promise<boolean> => {
    if (state.value.initState === LibrtcoreInitState.REINITIALIZING) {
      logger.warn(LogModule.GENERAL, "正在重新初始化中，忽略重复请求");
      return false;
    }

    return await performInitialization(motorModel, samplingRate, true);
  };

  /**
   * 清理资源
   */
  const cleanup = async (): Promise<void> => {
    if (state.value.initState === LibrtcoreInitState.CLEANUP) {
      return;
    }

    try {
      const previousState = state.value.initState;
      state.value.initState = LibrtcoreInitState.CLEANUP;
      state.value.isLoading = true;

      logger.info(LogModule.GENERAL, "开始清理 librtcore 资源", {
        previousState,
        isStarted: state.value.isStarted,
        hasError: state.value.error !== null
      });

      // 停止播放（优雅处理错误）
      if (state.value.isStarted) {
        try {
          await hapticApi.stop();
          logger.debug(LogModule.GENERAL, "播放系统已停止");
        } catch (error) {
          // 停止失败通常是因为系统已经停止或设备断开，这是正常情况
          logger.debug(LogModule.GENERAL, "停止播放时出现预期错误（系统可能已停止）", error);
        }
      }

      // 清理资源（优雅处理错误）
      // 即使在错误状态下也尝试清理，因为可能有部分资源需要释放
      try {
        await hapticApi.cleanup();
        logger.info(LogModule.GENERAL, "librtcore 资源清理完成");
      } catch (error) {
        // 清理失败可能是因为资源已经被清理或系统已关闭，记录但不抛出错误
        logger.debug(LogModule.GENERAL, "清理过程中出现预期错误（资源可能已清理）", error);
      }

      // 重置状态（无论清理是否成功都要重置状态）
      state.value.initState = LibrtcoreInitState.UNINITIALIZED;
      state.value.currentConfig = null;
      state.value.isStarted = false;
      state.value.isLoading = false;
      state.value.error = null;

      logger.info(LogModule.GENERAL, "librtcore 状态已重置");
    } catch (error) {
      // 只有在意外错误时才记录错误，但不设置错误状态
      logger.warn(LogModule.GENERAL, "清理过程中出现意外错误，但状态已重置", error);

      // 确保状态被重置
      state.value.initState = LibrtcoreInitState.UNINITIALIZED;
      state.value.currentConfig = null;
      state.value.isStarted = false;
      state.value.isLoading = false;
      state.value.error = null;
    } finally {
      state.value.isLoading = false;
    }
  };

  /**
   * 重试初始化
   */
  const retry = async (): Promise<boolean> => {
    if (!enableRetry) {
      logger.warn(LogModule.GENERAL, "重试功能已禁用");
      return false;
    }

    if (!lastInitParams.value) {
      logger.warn(LogModule.GENERAL, "没有可重试的初始化参数");
      return false;
    }

    if (state.value.retryCount >= maxRetries) {
      logger.warn(LogModule.GENERAL, `已达到最大重试次数 ${maxRetries}`);
      return false;
    }

    state.value.retryCount++;
    logger.info(LogModule.GENERAL, `开始第 ${state.value.retryCount} 次重试初始化`);

    const { motorModel, samplingRate } = lastInitParams.value;
    return await reinitializeLibrtcore(motorModel, samplingRate);
  };

  /**
   * 清除错误状态
   */
  const clearError = (): void => {
    state.value.error = null;
    if (state.value.initState === LibrtcoreInitState.ERROR) {
      state.value.initState = LibrtcoreInitState.UNINITIALIZED;
    }
  };

  // ===== 生命周期管理 =====

  // 组件卸载时清理资源
  onUnmounted(async () => {
    if (isInitialized.value) {
      await cleanup();
    }
  });

  return {
    // 状态
    state,

    // 计算属性
    isInitialized,
    canPlay,
    isLoading,
    hasError,

    // 方法
    initializeLibrtcore,
    reinitializeLibrtcore,
    cleanup,
    retry,
    clearError,
  };
}
