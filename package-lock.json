{"name": "realitytap-ecosystem", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "realitytap-ecosystem", "version": "1.0.0", "devDependencies": {"typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}, "node_modules/typescript": {"version": "5.8.3", "resolved": "http://mirrors.cloud.tencent.com/npm/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "realitytap-desktop": {"name": "realitytap-studio", "version": "1.0.7", "extraneous": true, "dependencies": {"@realitytap/shared": "file:../realitytap-shared", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.1", "@tauri-apps/plugin-fs": "^2.2.1", "@tauri-apps/plugin-os": "^2.0.0", "@tauri-apps/plugin-process": "^2.0.0", "@tauri-apps/plugin-updater": "^2.0.0", "@types/uuid": "^10.0.0", "@vicons/fluent": "^0.13.0", "@vicons/ionicons5": "^0.13.0", "@vicons/material": "^0.13.0", "naive-ui": "^2.41.0", "path": "^0.12.7", "pinia": "^3.0.2", "uuid": "^11.1.0", "vfonts": "^0.0.3", "vue": "^3.5.13", "vue-i18n": "^9.14.4", "vue-router": "^4.5.1", "zod": "^3.24.4"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@types/node": "^22.15.31", "@vitejs/plugin-vue": "^5.2.2", "@vitest/ui": "^3.2.3", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "jsdom": "^26.1.0", "typescript": "~5.7.2", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.1", "vite-node": "^2.1.8", "vitest": "^3.2.3", "vue-tsc": "^2.2.8"}}, "realitytap-ota-server": {"name": "@realitytap/ota-server", "version": "1.0.0", "extraneous": true, "dependencies": {"@tauri-apps/plugin-os": "^2.2.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.13", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "commander": "^11.1.0", "compression": "^1.7.4", "cors": "^2.8.5", "cross-env": "^7.0.3", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "semver": "^7.5.4", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^3.22.4"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.8", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/semver": "^7.5.6", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}, "realitytap-shared": {"name": "@realitytap/shared", "version": "1.0.0", "extraneous": true, "dependencies": {"zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "typescript": "^5.0.0"}}}}