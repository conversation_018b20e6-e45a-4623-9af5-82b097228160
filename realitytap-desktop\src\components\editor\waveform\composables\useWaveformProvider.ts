/**
 * 波形数据提供者
 * 使用Vue的provide/inject机制实现数据共享，避免props drilling
 */

import { provide, inject, type InjectionKey } from "vue";
import { useWaveformDataAccess, useWaveformDataAccessWithCache, type WaveformDataAccess } from "./useWaveformDataAccess";

/**
 * 波形数据上下文接口
 */
export interface WaveformDataContext {
  dataAccess: WaveformDataAccess;
  store: any;
  fileUuid: string;
}

/**
 * 波形数据注入键
 */
export const WaveformDataKey: InjectionKey<WaveformDataContext> = Symbol("WaveformData");

/**
 * 波形数据提供者配置
 */
export interface WaveformProviderConfig {
  waveformStore: any;
  fileUuid: string;
  enableCache?: boolean;
  enableLogging?: boolean;
}

/**
 * 创建波形数据提供者
 * 在父组件中使用，为子组件提供统一的数据访问接口
 */
export function useWaveformProvider(config: WaveformProviderConfig) {
  const { waveformStore, fileUuid, enableCache = true } = config;

  // 根据配置创建数据访问实例
  const dataAccess = enableCache ? useWaveformDataAccessWithCache(waveformStore) : useWaveformDataAccess(waveformStore);

  // 创建数据上下文
  const dataContext: WaveformDataContext = {
    dataAccess,
    store: waveformStore,
    fileUuid,
  };

  // 提供数据上下文
  provide(WaveformDataKey, dataContext);

  // 返回数据访问接口供父组件使用
  return {
    dataAccess,
    store: waveformStore,
    fileUuid,
  };
}

/**
 * 注入波形数据
 * 在子组件中使用，获取统一的数据访问接口
 */
export function useWaveformInjection(): WaveformDataContext {
  const injected = inject(WaveformDataKey);

  if (!injected) {
    throw new Error("WaveformData not provided. Make sure to use useWaveformProvider in a parent component.");
  }

  return injected;
}

/**
 * 安全的波形数据注入
 * 提供默认值，避免在没有提供者的情况下抛出错误
 */
export function useWaveformInjectionSafe(): WaveformDataContext | null {
  return inject(WaveformDataKey, null);
}

/**
 * 检查是否有波形数据提供者
 */
export function hasWaveformProvider(): boolean {
  return inject(WaveformDataKey, null) !== null;
}

/**
 * 波形数据消费者Hook
 * 提供更便捷的数据访问方式
 */
export function useWaveformData() {
  const context = useWaveformInjection();

  return {
    // 数据访问接口
    ...context.dataAccess,

    // Store直接访问（用于特殊情况）
    store: context.store,

    // 文件UUID
    fileUuid: context.fileUuid,

    // 便捷方法
    selectEvent: (eventId: string | null) => {
      context.store.selectEvent(eventId);
    },

    updateEvent: (eventDetail: any) => {
      context.store.updateSelectedEvent(eventDetail);
    },

    deleteSelectedEvent: () => {
      context.store.deleteSelectedEvent();
    },

    createEvent: (eventConfig: any) => {
      context.store.createNewEvent(eventConfig);
    },
  };
}

/**
 * 波形数据状态Hook
 * 提供响应式的数据状态
 */
export function useWaveformDataState() {
  const context = useWaveformInjection();
  const { store } = context;

  return {
    // 响应式状态
    events: store.events,
    selectedEventId: store.selectedEventId,
    selectedEvent: store.selectedEvent,
    selectedCurvePointIndex: store.selectedCurvePointIndex,
    isAdjustingProperties: store.isAdjustingProperties,

    // 计算属性
    hasEvents: () => (store.events?.length || 0) > 0,
    hasSelectedEvent: () => !!store.selectedEventId,
    eventsCount: () => store.events?.length || 0,
  };
}

/**
 * 波形数据操作Hook
 * 提供数据操作方法
 */
export function useWaveformDataOperations() {
  const context = useWaveformInjection();
  const { store } = context;

  return {
    // 事件选择
    selectEvent: (eventId: string | null) => {
      store.selectEvent(eventId);
    },

    selectCurvePoint: (index: number) => {
      store.selectCurvePoint(index);
    },

    // 事件操作
    updateEvent: (eventDetail: any) => {
      store.setAdjustingProperties(true);
      store.updateSelectedEvent(eventDetail);

      // 延迟重置状态
      setTimeout(() => {
        store.setAdjustingProperties(false);
      }, 50);
    },

    createEvent: (eventConfig: any) => {
      store.createNewEvent(eventConfig);
    },

    deleteSelectedEvent: () => {
      store.deleteSelectedEvent();
    },

    // 批量操作
    batchUpdateEvents: (updates: Array<{ id: string; changes: any }>) => {
      store.setAdjustingProperties(true);

      updates.forEach(({ id, changes }) => {
        const event = store.events?.find((e: any) => e.id === id);
        if (event) {
          // 这里需要根据实际的store实现来调整
          store.updateEventById(id, changes);
        }
      });

      setTimeout(() => {
        store.setAdjustingProperties(false);
      }, 50);
    },

    // 状态管理
    setAdjustingProperties: (adjusting: boolean) => {
      store.setAdjustingProperties(adjusting);
    },
  };
}

/**
 * 调试用的数据提供者信息
 */
export function useWaveformProviderDebug() {
  const context = useWaveformInjectionSafe();

  if (!context) {
    return {
      hasProvider: false,
      fileUuid: null,
      eventsCount: 0,
      selectedEventId: null,
    };
  }

  return {
    hasProvider: true,
    fileUuid: context.fileUuid,
    eventsCount: context.dataAccess.getEventsCount(),
    selectedEventId: context.dataAccess.getSelectedEventId(),
    storeType: context.store.constructor.name,
  };
}
