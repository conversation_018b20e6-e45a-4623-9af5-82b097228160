<template>
  <NModal
    :show="visible"
    @update:show="(value: boolean) => emit('update:visible', value)"
    preset="card"
    :title="dialogTitle"
    :closable="true"
    :mask-closable="false"
    :close-on-esc="true"
    :style="{ width: '90vw', maxWidth: '1400px', height: '65vh' }"
    class="play-effect-dialog"
  >
    <div class="dialog-content">
      <!-- 对话框级别的加载状态（仅用于数据加载和马达加载） -->
      <div v-if="isDialogLoading" class="loading-container">
        <NSpin size="large">
          <template #description>
            <div class="loading-details">
              <div>{{ t("playEffect.loading") }}</div>
            </div>
          </template>
        </NSpin>
      </div>

      <!-- 致命错误状态（非 librtcore 错误） -->
      <div v-else-if="hasFatalError" class="error-container">
        <NAlert type="error" :title="t('common.error')">
          {{ fatalErrorMessage }}
        </NAlert>
      </div>

      <!-- 主要内容 - 始终显示（除非有致命错误） -->
      <div v-else class="main-content">
        <!-- librtcore 错误提示（不阻塞UI） -->
        <div v-if="hasLibrtcoreError" class="librtcore-error-banner">
          <NAlert type="error" :title="t('playEffect.librtcoreError')" closable @close="handleLibrtcoreErrorClose">
            <div>
              {{ librtcoreErrorMessage }}
            </div>
            <div style="margin-top: 8px">
              <NButton size="small" @click="handleLibrtcoreErrorClose">
                {{ t("common.close") }}
              </NButton>
            </div>
          </NAlert>
        </div>
        <!-- 控制面板 -->
        <div class="control-panel" :class="{ initializing: isLibrtcoreLoading }">
          <!-- 初始化状态 - 只显示状态提示 -->
          <div v-if="isLibrtcoreLoading" class="initialization-state">
            <div class="librtcore-status-centered">
              <NText depth="3" class="librtcore-text">
                {{ t("playEffect.initializingLibrtcore") }}
              </NText>
            </div>
          </div>

          <!-- 正常控制状态 - 初始化完成后显示 -->
          <template v-else>
            <!-- 播放控制区域 -->
            <div class="playback-controls">
              <NSpace>
                <!-- 分离式播放按钮组 -->
                <div class="play-button-group">
                  <!-- 主播放按钮 -->
                  <NButton
                    type="primary"
                    :loading="playbackControl.state === 'loading'"
                    :disabled="!canPlay || isPlaying || isSaving"
                    size="medium"
                    @click="handlePlay"
                    class="play-main-button"
                  >
                    <template #icon>
                      <NIcon>
                        <PlayIcon />
                      </NIcon>
                    </template>
                    {{ t("playEffect.play") }}
                  </NButton>

                  <!-- 下拉按钮 -->
                  <NDropdown
                    :options="saveDropdownOptions"
                    @select="handleSaveDropdownSelect"
                    :disabled="!canPlay || isPlaying || isSaving"
                    trigger="click"
                    placement="bottom-end"
                  >
                    <NButton type="primary" :loading="isSaving" :disabled="!canPlay || isPlaying || isSaving" size="medium" class="play-dropdown-button">
                      <NIcon style="font-size: 12px">
                        <svg viewBox="0 0 16 16" fill="currentColor" style="width: 12px; height: 12px">
                          <path d="M4.427 9.573L8 13.146l3.573-3.573a.5.5 0 0 1 .708.708L8.354 14.207a.5.5 0 0 1-.708 0L3.72 10.281a.5.5 0 1 1 .708-.708z" />
                        </svg>
                      </NIcon>
                    </NButton>
                  </NDropdown>
                </div>

                <NButton @click="handleStop" :disabled="playbackControl.state === 'stopped'" size="medium">
                  <template #icon>
                    <NIcon><StopIcon /></NIcon>
                  </template>
                  {{ t("playEffect.stop") }}
                </NButton>
              </NSpace>
            </div>

            <!-- 马达选择区域 -->
            <div class="motor-selection">
              <NSpace align="center">
                <NText>{{ t("playEffect.motorSelection") }}:</NText>
                <NSelect
                  v-model:value="selectedMotorId"
                  :options="motorOptions"
                  @update:value="handleMotorChange"
                  :placeholder="t('playEffect.selectMotor')"
                  :loading="isLoadingMotors"
                  style="width: 200px"
                  size="medium"
                />
              </NSpace>
            </div>

            <!-- 采样率选择区域 -->
            <div class="sampling-rate-selection">
              <NSpace align="center">
                <NText>{{ t("playEffect.samplingRateSelection") }}:</NText>
                <NSelect
                  v-model:value="selectedSamplingRate"
                  :options="samplingRateOptions"
                  @update:value="handleSamplingRateChange"
                  :placeholder="t('playEffect.selectSamplingRate')"
                  style="width: 180px"
                  size="medium"
                />
              </NSpace>
            </div>

            <!-- 实际频率选择区域 -->
            <div class="actual-frequency-selection">
              <NSpace align="center">
                <NText>{{ t("playEffect.actualFrequencySelection") }}:</NText>
                <NSelect
                  v-model:value="selectedActualFrequency"
                  :options="actualFrequencyOptions"
                  @update:value="handleActualFrequencyChange"
                  :placeholder="t('playEffect.selectActualFrequency')"
                  :disabled="!selectedMotor"
                  style="width: 120px"
                  size="medium"
                />
              </NSpace>
            </div>
          </template>
        </div>

        <!-- Event Canvas 画布区域 -->
        <div class="canvas-section">
          <NCard class="canvas-card">
            <div class="canvas-container" ref="canvasContainerRef">
              <canvas ref="canvasRef" class="event-canvas"></canvas>
            </div>
          </NCard>
        </div>
      </div>
    </div>
  </NModal>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch, nextTick, h } from "vue";
import type { RenderableEvent } from "@/types/haptic-editor";
import { useI18n } from "@/composables/useI18n";
import { usePlayEffectDialog } from "@/composables/haptics/usePlayEffectDialog";
import { useSimpleWaveform } from "@/composables/haptics/useSimpleWaveform";
import { useLibrtcoreGlobalManager } from "@/composables/haptics/useLibrtcoreGlobalManager";
import { useAppConfig } from "@/composables/useAppConfig";
import { NModal, NCard, NSpin, NAlert, NText, NSpace, NButton, NIcon, NSelect, NDropdown, useMessage } from "naive-ui";
import { Play as PlayIcon, Stop as StopIcon, Save as SaveIcon } from "@vicons/ionicons5";
import { SamplingRateType } from "@/types/haptic-types";
import { HAPTIC_CONSTANTS } from "@/utils/api/haptic-api";
import { logger, LogModule } from "@/utils/logger/logger";
import { generateActualFrequencyOptions } from "@/utils/format/motorConfigParser";

// Props
interface Props {
  visible: boolean;
  events?: RenderableEvent[];
  currentFile?: {
    name: string;
    uuid: string;
    path: string;
  } | null;
  isLoading?: boolean;
  error?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  events: () => [],
  currentFile: null,
  isLoading: false,
  error: null,
});

// Emits
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "play", motorId: string): void;
  (e: "stop"): void;
  (e: "motor-change", motorId: string): void;
  (e: "sampling-rate-change", samplingRate: SamplingRateType): void;
}>();

// Composables
const { t } = useI18n();
const message = useMessage();

// 使用主要的组合函数
const { playbackControl, selectedMotor, availableMotors, isLoadingMotors, handlePlay, handleStop, handleMotorChange } = usePlayEffectDialog(props, emit);

// 使用简化的波形图组合函数
const waveform = useSimpleWaveform();

// 使用 librtcore 全局管理器
const librtcoreGlobalManager = useLibrtcoreGlobalManager();

// App configuration management
const { playEffectDialogConfig, updateLastSelectedMotor, updateLastSelectedSamplingRate } = useAppConfig();

// 模板引用
const canvasRef = ref<HTMLCanvasElement | null>(null);
const canvasContainerRef = ref<HTMLDivElement | null>(null);

// 计算属性
const dialogTitle = computed(() => {
  if (props.currentFile) {
    return t("playEffect.dialogTitle", { fileName: props.currentFile.name });
  }
  return t("playEffect.dialogTitleDefault");
});

const isPlaying = computed(() => playbackControl.value.state === "playing");

const canPlay = computed(
  () => props.events && props.events.length > 0 && selectedMotor.value !== null && librtcoreGlobalManager.isInitialized.value && librtcoreGlobalManager.canPlay.value
);

// 对话框级别的加载状态（不包含 librtcore 加载状态）
const isDialogLoading = computed(() => props.isLoading || isLoadingMotors.value);

// 致命错误状态（非 librtcore 错误）
const hasFatalError = computed(() => props.error !== null);

// 致命错误信息
const fatalErrorMessage = computed(() => props.error);

// librtcore 错误信息
const librtcoreErrorMessage = computed(() => librtcoreGlobalManager.error.value?.message || null);

// librtcore 错误状态
const hasLibrtcoreError = computed(() => librtcoreGlobalManager.hasError.value);

// librtcore 加载状态
const isLibrtcoreLoading = computed(() => librtcoreGlobalManager.isLoading.value);

const selectedMotorId = computed({
  get: () => selectedMotor.value?.id || "",
  set: (value: string) => handleMotorChangeWithConfig(value),
});

const motorOptions = computed(() =>
  availableMotors.value.map((motor) => ({
    label: motor.displayName,
    value: motor.id,
  }))
);

// 采样率相关状态 - 从配置中初始化
const selectedSamplingRate = ref<SamplingRateType>(playEffectDialogConfig.value?.lastSelectedSamplingRate || HAPTIC_CONSTANTS.DEFAULT_SAMPLING_RATE);

// 监听配置变化，更新本地状态
watch(
  playEffectDialogConfig,
  (newConfig) => {
    if (newConfig?.lastSelectedSamplingRate && newConfig.lastSelectedSamplingRate !== selectedSamplingRate.value) {
      selectedSamplingRate.value = newConfig.lastSelectedSamplingRate;
      logger.debug(LogModule.HAPTIC, "从配置更新采样率", { samplingRate: newConfig.lastSelectedSamplingRate });
    }
  },
  { immediate: true, deep: true }
);

// 实际频率相关状态 - 默认为null，将在马达选择后设置为额定F0
const selectedActualFrequency = ref<number | null>(null);

// 采样率选项
const samplingRateOptions = computed(() => [
  {
    label: t("playEffect.samplingRate6Khz"),
    value: SamplingRateType.Sampling6Khz,
  },
  {
    label: t("playEffect.samplingRate8Khz"),
    value: SamplingRateType.Sampling8Khz,
  },
  {
    label: t("playEffect.samplingRate12Khz"),
    value: SamplingRateType.Sampling12Khz,
  },
  {
    label: t("playEffect.samplingRate24Khz"),
    value: SamplingRateType.Sampling24Khz,
  },
]);

// 实际频率选项 - 基于选中马达的额定F0生成（±10Hz范围内的所有整数）
const actualFrequencyOptions = computed(() => {
  if (!selectedMotor.value) {
    return [];
  }

  const ratedF0 = selectedMotor.value.ratedF0;
  const frequencies = generateActualFrequencyOptions(ratedF0);

  return frequencies.map((freq) => ({
    label: `${freq}Hz`,
    value: freq,
  }));
});

// 保存状态管理
const isSaving = ref(false);

// 保存下拉菜单选项（只包含保存相关选项）
const saveDropdownOptions = computed(() => [
  {
    label: t("playEffect.saveAsRtp"),
    key: "save",
    icon: () => h(NIcon, null, { default: () => h(SaveIcon) }),
  },
]);

// 保存下拉菜单选择处理
const handleSaveDropdownSelect = async (key: string) => {
  if (key === "save") {
    await handleSave();
  }
};

// 保存处理函数
const handleSave = async () => {
  if (isSaving.value) {
    logger.debug(LogModule.HAPTIC, "保存操作正在进行中，忽略重复请求");
    return;
  }

  isSaving.value = true;

  try {
    // 1. 显示文件保存对话框
    const { invoke } = await import("@tauri-apps/api/core");
    const filePath = await invoke<string | null>("show_save_file_dialog", {
      defaultFilename: props.currentFile?.name || "haptic_data",
    });

    if (!filePath) {
      logger.info(LogModule.HAPTIC, "用户取消了文件保存");
      return;
    }

    logger.info(LogModule.HAPTIC, "用户选择保存路径", { filePath });

    // 2. 检查是否有事件数据
    if (!props.events || props.events.length === 0) {
      logger.warn(LogModule.HAPTIC, "没有事件数据可保存");
      message.warning(t("playEffect.noData"));
      return;
    }

    // 3. 转换事件数据为 int 数组
    const { convertRealityTapToArray } = await import("@/utils/format/realityTapArrayConverter");
    const conversionResult = convertRealityTapToArray(props.events, {
      enableLogging: true,
      validateInput: true,
    });

    if (!conversionResult.success || !conversionResult.data) {
      throw new Error(`事件转换失败: ${conversionResult.error}`);
    }

    const hapticData = conversionResult.data;
    logger.info(LogModule.HAPTIC, "事件转换成功", {
      originalEventsCount: props.events.length,
      convertedArrayLength: hapticData.length,
      version: conversionResult.version,
    });

    // 4. 调用保存 API
    const { hapticApi } = await import("@/utils/api/haptic-api");
    const saveParams = {
      data: hapticData,
      interval_ms: 0,
      loop_count: 1,
      amplitude: 255,
      frequency_offset: 0,
    };

    logger.debug(LogModule.HAPTIC, "调用后端保存API", { filePath, ...saveParams });
    const result = await hapticApi.saveToFile(saveParams, filePath);

    logger.info(LogModule.HAPTIC, "保存成功", { result, filePath });
    message.success(t("playEffect.saveSuccess"));
  } catch (error) {
    logger.error(LogModule.HAPTIC, "保存失败", error);
    message.error(t("playEffect.saveFailed", { error: String(error) }));
  } finally {
    // 延迟重置状态
    setTimeout(() => {
      isSaving.value = false;
    }, 500);
  }
};

// 马达变化处理函数 - 保存配置（全局监听器会自动处理 librtcore 更新）
const handleMotorChangeWithConfig = async (motorId: string) => {
  // 调用原始的马达变化处理函数
  handleMotorChange(motorId);

  // 保存到配置文件（这会触发全局配置监听器自动更新 librtcore）
  try {
    await updateLastSelectedMotor(motorId);
    logger.info(LogModule.HAPTIC, "马达配置已保存", { motorId });
  } catch (error) {
    logger.error(LogModule.HAPTIC, "保存马达配置失败", error);
  }
};

// 采样率变化处理函数 - 保存配置（全局监听器会自动处理 librtcore 更新）
const handleSamplingRateChange = async (samplingRate: SamplingRateType) => {
  selectedSamplingRate.value = samplingRate;
  emit("sampling-rate-change", samplingRate);

  // 保存到配置文件（这会触发全局配置监听器自动更新 librtcore）
  try {
    await updateLastSelectedSamplingRate(samplingRate);
    logger.info(LogModule.HAPTIC, "采样率配置已保存", { samplingRate });
  } catch (error) {
    logger.error(LogModule.HAPTIC, "保存采样率配置失败", error);
  }
};

// 实际频率变化处理函数 - 触发librtcore重新初始化
const handleActualFrequencyChange = async (actualFrequency: number) => {
  selectedActualFrequency.value = actualFrequency;

  // 如果有选中的马达和采样率，触发librtcore重新初始化
  if (selectedMotor.value && selectedSamplingRate.value) {
    try {
      logger.info(LogModule.HAPTIC, "实际频率变化，触发librtcore重新初始化", {
        actualFrequency,
        motor: selectedMotor.value.name,
        samplingRate: selectedSamplingRate.value
      });

      await librtcoreGlobalManager.updateConfiguration(
        selectedMotor.value,
        selectedSamplingRate.value,
        actualFrequency
      );

      logger.info(LogModule.HAPTIC, "实际频率更新完成", { actualFrequency });
    } catch (error) {
      logger.error(LogModule.HAPTIC, "实际频率更新失败", error);
    }
  } else {
    logger.debug(LogModule.HAPTIC, "实际频率已更改，但缺少马达或采样率信息", { actualFrequency });
  }
};

// 处理 librtcore 错误关闭
const handleLibrtcoreErrorClose = () => {
  logger.info(LogModule.HAPTIC, "librtcore 错误，清除错误状态并关闭对话框");

  // 清除错误状态
  librtcoreGlobalManager.clearError();

  // 关闭对话框
  logger.debug(LogModule.HAPTIC, "关闭对话框");
  emit("update:visible", false);
};

// Canvas初始化函数
const initializeCanvas = async () => {
  // 等待DOM完全渲染
  await nextTick();

  if (canvasRef.value) {
    logger.debug(LogModule.WAVEFORM, "PlayEffectDialog: 开始初始化Canvas", {
      clientWidth: canvasRef.value.clientWidth,
      clientHeight: canvasRef.value.clientHeight,
    });

    const success = waveform.initCanvas(canvasRef.value);

    if (success) {
      // 初始化成功后立即使用传入的events数据进行绘制
      waveform.updateData({
        events: props.events || [],
        currentTime: playbackControl.value.currentTime,
      });
    } else {
      logger.error(LogModule.WAVEFORM, "PlayEffectDialog: Canvas初始化失败");
    }
  }
};

// 强制重新初始化Canvas和数据
const forceReinitialize = async () => {
  logger.debug(LogModule.WAVEFORM, "PlayEffectDialog: 强制重新初始化Canvas");

  // 清理现有状态
  waveform.ctx.value = null;
  waveform.canvas.value = null;

  // 等待DOM更新
  await nextTick();

  if (canvasRef.value) {
    const success = waveform.initCanvas(canvasRef.value);
    if (success) {
      // 初始化成功后更新数据
      waveform.updateData({
        events: props.events || [],
        currentTime: playbackControl.value.currentTime,
      });
    }
  }
};

// 生命周期
onMounted(() => {
  initializeCanvas();

  // 观察Canvas容器尺寸变化
  if (canvasContainerRef.value) {
    resizeObserver.observe(canvasContainerRef.value);
  }
});

// 监听对话框显示状态，确保Canvas和librtcore在对话框打开时正确初始化
watch(
  () => props.visible,
  async (newVisible) => {
    if (newVisible) {
      logger.debug(LogModule.HAPTIC, "PlayEffectDialog: 对话框打开，准备重新初始化");

      // 1. 确保 librtcore 已初始化
      if (selectedMotor.value && selectedSamplingRate.value) {
        try {
          logger.debug(LogModule.HAPTIC, "PlayEffectDialog: 确保 librtcore 已初始化", {
            motor: selectedMotor.value.name,
            samplingRate: selectedSamplingRate.value,
            actualFrequency: selectedActualFrequency.value
          });

          await librtcoreGlobalManager.ensureInitialized(
            selectedMotor.value,
            selectedSamplingRate.value,
            selectedActualFrequency.value || undefined
          );

          logger.info(LogModule.HAPTIC, "PlayEffectDialog: librtcore 初始化完成");
        } catch (error) {
          logger.error(LogModule.HAPTIC, "PlayEffectDialog: librtcore 初始化失败", error);
        }
      }

      // 2. 对话框打开时，延迟一点时间确保DOM完全渲染
      setTimeout(async () => {
        await forceReinitialize();
      }, 150); // 增加延迟时间确保Modal完全渲染
    }
    // 注意：对话框关闭时不再清理 librtcore 资源，由全局管理器统一管理
  }
);

// 监听 Canvas 容器尺寸变化
const resizeObserver = new ResizeObserver((entries) => {
  for (const entry of entries) {
    const { width, height } = entry.contentRect;
    if (canvasRef.value && width > 0 && height > 0) {
      waveform.resizeCanvas(width, height - 20); // 减去一些边距

      // 尺寸变化后重新绘制当前的events数据
      waveform.updateData({
        events: props.events || [],
        currentTime: playbackControl.value.currentTime,
      });
    }
  }
});

// 监听马达选择变化（全局配置监听器会自动处理 librtcore 更新）
watch(
  () => selectedMotor.value,
  (newMotor, oldMotor) => {
    if (newMotor && newMotor !== oldMotor) {
      logger.debug(LogModule.HAPTIC, "马达选择变化", {
        oldMotor: oldMotor?.name,
        newMotor: newMotor.name,
      });

      // 马达变化时，自动设置实际频率为新马达的额定F0
      selectedActualFrequency.value = newMotor.ratedF0;
      logger.debug(LogModule.HAPTIC, "自动设置实际频率为马达额定F0", {
        motorName: newMotor.name,
        ratedF0: newMotor.ratedF0,
      });
    }
  },
  { immediate: true } // 立即执行，确保初始化时也设置默认值
);

onUnmounted(() => {
  resizeObserver.disconnect();
  // 注意：组件卸载时不再清理 librtcore 资源，由全局管理器统一管理
});

// 监听播放时间变化，更新播放头
watch(
  () => playbackControl.value.currentTime,
  (currentTime) => {
    if (canvasRef.value) {
      waveform.setCurrentTime(currentTime);
    }
  }
);
</script>

<style scoped>
.dialog-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
}

.loading-details {
  text-align: center;
}

.loading-subtitle {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 4px;
}

.librtcore-error-banner {
  margin-bottom: 16px;
}

/* 初始化状态样式 */
.control-panel.initializing {
  justify-content: center;
  align-items: center;
}

.initialization-state {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.librtcore-status-centered {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: var(--n-color-embedded);
  border-radius: 4px;
  opacity: 0.75;
  transition: opacity 0.3s ease;
  filter: brightness(0.95);
}

.librtcore-status-centered:hover {
  opacity: 1;
}

.librtcore-text {
  font-size: 12px;
  font-weight: 400;
  opacity: 0.9;
  color: var(--n-text-color);
}

.main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 控制面板样式 */
.control-panel {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: var(--n-color-embedded);
  border-radius: 8px;
  gap: 16px;
  transition: all 0.3s ease;
}

.playback-controls {
  display: flex;
  align-items: center;
}

.play-button-group {
  display: inline-flex;
  align-items: center;
}

/* 主播放按钮样式 */
.play-button-group .play-main-button {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-right: none !important;
}

/* 下拉按钮样式 */
.play-button-group .play-dropdown-button {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  padding-left: 8px !important;
  padding-right: 8px !important;
  min-width: 32px !important;
  border-left: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 移除按钮组内按钮的悬停位移效果 */
.play-button-group .n-button {
  transition: background-color 0.3s ease, border-color 0.3s ease !important;
}

.play-button-group .n-button:hover {
  transform: none !important;
}

.motor-selection,
.sampling-rate-selection,
.actual-frequency-selection {
  display: flex;
  align-items: center;
}

/* Canvas 区域样式 */
.canvas-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.canvas-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.canvas-container {
  position: relative;
  flex: 1;
  min-height: 300px;
  background: var(--n-color-embedded);
  border-radius: 6px;
  overflow: hidden;
}

.event-canvas {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 4px;
}

.no-events-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

/* 深色主题适配 */
:deep(.n-card) {
  height: 100%;
}

:deep(.n-card .n-card__content) {
  height: calc(100% - 60px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Canvas 卡片特殊样式 */
.canvas-card :deep(.n-card__content) {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 对话框样式 */
.play-effect-dialog :deep(.n-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.play-effect-dialog :deep(.n-card .n-card__content) {
  flex: 1;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .playback-controls,
  .motor-selection,
  .sampling-rate-selection,
  .actual-frequency-selection {
    justify-content: center;
  }

  .canvas-container {
    min-height: 250px;
  }
}

/* 按钮状态样式 */
.playback-controls :deep(.n-button) {
  transition: all 0.3s ease;
}

/* 只对非按钮组内的按钮应用悬停位移效果 */
.playback-controls :deep(.n-button:hover):not(.play-main-button):not(.play-dropdown-button) {
  transform: translateY(-1px);
}

/* Canvas 容器边框效果 */
.canvas-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid transparent;
  border-radius: 6px;
  pointer-events: none;
  transition: border-color 0.3s ease;
}

.canvas-container:hover::before {
  border-color: var(--n-color-primary);
}
</style>
