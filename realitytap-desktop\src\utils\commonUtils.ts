import { invoke } from "@tauri-apps/api/core";
import type { RealityTapProject } from "@/types/haptic-project";

export const formatWindowsPath = (message: string): string => {
  // 定义特殊标记
  const SLASH_MARKER = "##SLASH##";

  // 使用正则表达式匹配形如 directory \"C:\\Users\\<USER>\"
  return message.replace(
    /(directory\s+)\\"([A-Z]:\\\\[^"]+)\\"/g,
    (_match, prefix, path) => {
      // 第一步：将\\替换为特殊标记
      let formattedPath = path.replace(/\\/g, SLASH_MARKER);

      // 第二步：将特殊标记替换为单反斜杠
      formattedPath = formattedPath.replace(
        new RegExp(SLASH_MARKER, "g"),
        "\\"
      );

      // 返回前缀 + 格式化后的路径
      return `${prefix}${formattedPath}`;
    }
  );
};

export const parseErrorMessage = (error: unknown): string => {
  let errorMessage = "Unknown error occurred"; // Default fallback message in English

  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === 'string') {
    errorMessage = error;
  } else if (typeof error === "object" && error !== null) {
    // Prioritize known properties for more specific messages
    if ("message" in error && typeof (error as any).message === 'string') {
      errorMessage = (error as any).message;
    } else if ("Io" in error && typeof (error as any).Io === 'string') { // Tauri IO error
      errorMessage = (error as any).Io;
    } else if ("details" in error && typeof (error as any).details === 'string') {
      errorMessage = (error as any).details;
    } else if ("ValidationError" in error && typeof (error as any).ValidationError === 'string') {
      errorMessage = (error as any).ValidationError;
    } else {
      // If no specific known properties are found in the object,
      // provide a message that directs the user to check console logs for more complex objects.
      // Avoid directly stringifying potentially large or sensitive objects to the UI.
      console.error("An unhandled object error occurred:", error);
      errorMessage = "操作失败，发生未知错误。";
    }
  }
  // All extracted messages go through path formatting
  return formatWindowsPath(errorMessage);
};

/**
 * 调用后端，根据目录自动生成 RealityTap 项目结构
 * @param projectDirPath 项目根目录绝对路径
 * @returns Promise<RealityTapProject>
 */
export async function generateProjectFromDirectory(projectDirPath: string): Promise<RealityTapProject> {
  return await invoke<RealityTapProject>("generate_project_from_directory_command", {
    projectDirPath
  });
}

/**
 * 调用后端，刷新 RealityTap 项目（重新遍历目录，更新 groups、files）
 * @param projectDirPath 项目根目录绝对路径
 * @returns Promise<RealityTapProject>
 */
export async function refreshProjectDirectory(projectDirPath: string): Promise<RealityTapProject> {
  return await invoke<RealityTapProject>("refresh_project_directory_command", {
    projectDirPath
  });
}
