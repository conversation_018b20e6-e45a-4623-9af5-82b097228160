/**
 * 增强型多文件标签页管理器
 * 实现Chrome浏览器风格的标签页行为和完整状态缓存
 */

import { ref, computed, watch } from "vue";
import { useProjectStore } from "@/stores/haptics-project-store";
import {
  useFileWaveformEditorStore,
  clearFileStoreInstance,
  fileEditorStateManager,
  type FileEditorState
} from "@/stores/haptics-editor-store";
import type { RealityTapEffect } from "@/types/haptic-file";
import { logger, LogModule } from "@/utils/logger/logger";

// 音频振幅数据接口已移动到 Store 中
import type { AudioAmplitudeData } from "@/stores/haptics-editor-store";

// 增强型标签页数据接口
export interface EnhancedFileTab {
  fileUuid: string;
  fileName: string;
  effect: RealityTapEffect | null;
  // 完整的状态缓存
  editorState: FileEditorState;
  // 标签页元数据
  isActive: boolean;
  isModified: boolean;
  lastActiveTime: number;
  // 创建时间（用于排序）
  createdTime: number;
}

// 标签页管理器配置
export interface TabManagerConfig {
  maxTabs?: number; // 最大标签页数量
  enableLRU?: boolean; // 是否启用LRU缓存策略
  autoSave?: boolean; // 是否自动保存状态
  debugMode?: boolean; // 调试模式
}

/**
 * 增强型标签页管理器
 * 提供Chrome浏览器风格的多文件编辑体验
 */
export function useEnhancedTabManager(config: TabManagerConfig = {}) {
  const {
    maxTabs = 10,
    enableLRU = true,
    autoSave = true,
    debugMode = false
  } = config;

  const projectStore = useProjectStore();

  // 标签页管理状态
  const openTabs = ref<Map<string, EnhancedFileTab>>(new Map());
  const activeTabKey = ref<string>("");
  const tabOrder = ref<string[]>([]); // 标签页顺序

  // 计算属性
  const activeTab = computed(() => {
    return activeTabKey.value ? openTabs.value.get(activeTabKey.value) : null;
  });

  const tabCount = computed(() => openTabs.value.size);

  const tabList = computed(() => {
    return tabOrder.value
      .map(fileUuid => openTabs.value.get(fileUuid))
      .filter(Boolean) as EnhancedFileTab[];
  });

  // 获取文件名
  const getFileName = (fileUuid: string): string => {
    const file = projectStore.activeProject?.files?.find(f => f.fileUuid === fileUuid);
    return file?.name || `File ${fileUuid.slice(0, 8)}`;
  };

  // 创建新标签页
  const createTab = (
    fileUuid: string,
    effect: RealityTapEffect | null,
    audioAmplitudeData?: AudioAmplitudeData | null,
    audioDuration?: number | null
  ): EnhancedFileTab => {
    const fileName = getFileName(fileUuid);
    const now = Date.now();

    // 获取或创建文件级状态
    const editorState = fileEditorStateManager.getFileState(fileUuid);

    // 更新音频相关状态（通过 Store 管理）
    // 只有当 audioAmplitudeData 不是 undefined 时才设置，避免清空现有数据
    if (audioAmplitudeData !== undefined) {
      // 获取文件的 Store 实例并设置音频数据
      try {
        const fileStore = useFileWaveformEditorStore(fileUuid);
        fileStore.setAudioData(audioAmplitudeData);
        logger.debug(LogModule.PROJECT, "创建标签页时通过 Store 设置音频数据", {
          fileUuid,
          hasAudioData: !!audioAmplitudeData,
          samplesCount: audioAmplitudeData?.samples?.length || 0,
          action: audioAmplitudeData ? "设置数据" : "清空数据"
        });
      } catch (error) {
        logger.warn(LogModule.PROJECT, "通过 Store 设置音频数据失败，回退到状态管理器", { fileUuid, error });
        editorState.audioAmplitudeData = audioAmplitudeData;
      }
    } else {
      logger.debug(LogModule.PROJECT, "创建标签页时跳过音频数据设置（undefined），保持现有数据", { fileUuid });
    }
    if (audioDuration !== undefined) {
      editorState.audioDuration = audioDuration;
      logger.debug(LogModule.PROJECT, "创建标签页时设置音频时长", { fileUuid, audioDuration });
    }
    editorState.lastActiveTime = now;

    const newTab: EnhancedFileTab = {
      fileUuid,
      fileName,
      effect,
      editorState,
      isActive: false,
      isModified: projectStore.isFileUnsaved(fileUuid),
      lastActiveTime: now,
      createdTime: now,
    };

    if (debugMode) {
      logger.debug(LogModule.PROJECT, "创建新标签页", { fileUuid, tab: newTab });
    }

    return newTab;
  };

  // 打开标签页
  const openTab = (
    fileUuid: string,
    effect: RealityTapEffect | null,
    audioAmplitudeData?: AudioAmplitudeData | null,
    audioDuration?: number | null
  ): boolean => {
    // 检查是否已存在
    if (openTabs.value.has(fileUuid)) {
      // 【修复】即使标签页已存在，也要更新音频数据
      // 这确保了重新打开文件时音频数据能正确传递
      const existingTab = openTabs.value.get(fileUuid);
      if (existingTab) {
        // 更新音频相关状态（通过 Store 管理）
        // 只有当 audioAmplitudeData 不是 undefined 时才设置，避免清空现有数据
        if (audioAmplitudeData !== undefined) {
          try {
            const fileStore = useFileWaveformEditorStore(fileUuid);
            fileStore.setAudioData(audioAmplitudeData);
            logger.debug(LogModule.PROJECT, "更新已存在标签页时通过 Store 设置音频数据", {
              fileUuid,
              hasAudioData: !!audioAmplitudeData,
              samplesCount: audioAmplitudeData?.samples?.length || 0,
              action: audioAmplitudeData ? "设置数据" : "清空数据"
            });
          } catch (error) {
            logger.warn(LogModule.PROJECT, "通过 Store 更新音频数据失败，回退到状态管理器", { fileUuid, error });
            existingTab.editorState.audioAmplitudeData = audioAmplitudeData;
          }
        } else {
          logger.debug(LogModule.PROJECT, "更新已存在标签页时跳过音频数据设置（undefined），保持现有数据", { fileUuid });
        }
        if (audioDuration !== undefined) {
          existingTab.editorState.audioDuration = audioDuration;
        }
        logger.debug(LogModule.PROJECT, "更新已存在标签页的音频数据", {
          fileUuid,
          hasAudioData: !!audioAmplitudeData,
          samplesCount: audioAmplitudeData?.samples?.length || 0,
          audioDuration,
          currentStateAudioData: !!existingTab.editorState.audioAmplitudeData,
          currentStateAudioDuration: existingTab.editorState.audioDuration,
        });
      }
      return switchToTab(fileUuid);
    }

    // 检查标签页数量限制
    if (enableLRU && tabCount.value >= maxTabs) {
      const lruTab = findLRUTab();
      if (lruTab) {
        closeTab(lruTab.fileUuid, false); // 静默关闭
      }
    }

    // 创建新标签页
    const newTab = createTab(fileUuid, effect, audioAmplitudeData, audioDuration);
    openTabs.value.set(fileUuid, newTab);
    tabOrder.value.push(fileUuid);

    // 【修复】不在文件打开时立即创建空缓存，让WaveformEditor处理原始数据后再创建缓存
    // 这样可以确保首次打开文件时使用原始数据而不是空缓存

    // 切换到新标签页
    switchToTab(fileUuid);

    if (debugMode) {
      logger.debug(LogModule.PROJECT, "打开标签页", { fileUuid, tabCount: tabCount.value });
    }

    return true;
  };

  // 切换标签页
  const switchToTab = (fileUuid: string): boolean => {
    const tab = openTabs.value.get(fileUuid);
    if (!tab) {
      logger.warn(LogModule.PROJECT, "标签页不存在", { fileUuid });
      return false;
    }

    // 保存当前活跃标签页的状态
    if (activeTabKey.value && activeTabKey.value !== fileUuid) {
      saveTabState(activeTabKey.value);
    }

    // 更新活跃状态
    if (activeTab.value) {
      activeTab.value.isActive = false;
    }

    tab.isActive = true;
    tab.lastActiveTime = Date.now();
    activeTabKey.value = fileUuid;

    // 恢复标签页状态
    restoreTabState(fileUuid);

    if (debugMode) {
      logger.debug(LogModule.PROJECT, "切换到标签页", { fileUuid });
    }

    return true;
  };

  // 关闭标签页
  const closeTab = (fileUuid: string, showConfirm: boolean = true): boolean => {
    const tab = openTabs.value.get(fileUuid);
    if (!tab) {
      return false;
    }

    // 检查是否有未保存的修改
    if (showConfirm && tab.isModified) {
      // TODO: 显示确认对话框
      logger.warn(LogModule.PROJECT, "文件有未保存的修改", { fileUuid });
    }

    // 保存状态（如果启用自动保存）
    if (autoSave) {
      saveTabState(fileUuid);
    }

    // 【优化】清理资源（包括项目缓存）
    try {
      clearFileStoreInstance(fileUuid);
      logger.debug(LogModule.PROJECT, `已清理标签页 ${fileUuid} 的 Store 实例`);
    } catch (error) {
      logger.warn(LogModule.PROJECT, `清理 Store 实例失败: ${fileUuid}`, error);
    }

    // 清理项目缓存
    try {
      projectStore.clearFileCache(fileUuid);
      logger.debug(LogModule.PROJECT, `已清理标签页 ${fileUuid} 的项目缓存`);
    } catch (error) {
      logger.warn(LogModule.PROJECT, `清理项目缓存失败: ${fileUuid}`, error);
    }

    // 清理文件状态管理器中的状态
    try {
      fileEditorStateManager.removeFileState(fileUuid);
      logger.debug(LogModule.PROJECT, `已清理标签页 ${fileUuid} 的状态管理器状态`);
    } catch (error) {
      logger.warn(LogModule.PROJECT, `清理状态管理器状态失败: ${fileUuid}`, error);
    }

    openTabs.value.delete(fileUuid);

    // 更新标签页顺序
    const index = tabOrder.value.indexOf(fileUuid);
    if (index >= 0) {
      tabOrder.value.splice(index, 1);
    }

    // 如果关闭的是当前活跃标签页，切换到其他标签页
    if (activeTabKey.value === fileUuid) {
      if (tabOrder.value.length > 0) {
        // 切换到最近使用的标签页
        const nextTab = findMostRecentTab();
        if (nextTab) {
          switchToTab(nextTab.fileUuid);
        }
      } else {
        activeTabKey.value = "";
      }
    }

    if (debugMode) {
      logger.debug(LogModule.PROJECT, "关闭标签页", { fileUuid, remaining: tabCount.value });
    }

    return true;
  };

  // 保存标签页状态
  const saveTabState = (fileUuid: string): void => {
    const tab = openTabs.value.get(fileUuid);
    if (!tab) return;

    try {
      const store = useFileWaveformEditorStore(fileUuid);

      // 同步状态到文件状态管理器
      fileEditorStateManager.setFileState(fileUuid, {
        selectedEventId: store.selectedEventId,
        selectedCurvePointIndex: store.$state.selectedCurvePointIndex,
        events: [...store.events],
        totalDuration: store.totalDuration,
        isDurationLockedByAudio: store.isDurationLockedByAudio,
        lastActiveTime: Date.now(),
      });

      // 【优化】更新项目缓存（移除事件数量限制）
      // 确保项目缓存存在，如果不存在则创建
      if (!projectStore.hasFileCache(fileUuid)) {
        projectStore.setFileCache(fileUuid, tab.effect, store.events);
      } else {
        projectStore.updateFileCacheEvents(fileUuid, store.events);
      }

      if (debugMode) {
        logger.debug(LogModule.PROJECT, "保存标签页状态", { fileUuid });
      }
    } catch (error) {
      logger.error(LogModule.PROJECT, "保存状态失败", { fileUuid, error });
    }
  };

  // 恢复标签页状态
  const restoreTabState = (fileUuid: string): void => {
    const tab = openTabs.value.get(fileUuid);
    if (!tab) return;

    try {
      const store = useFileWaveformEditorStore(fileUuid);
      const state = tab.editorState;

      // 恢复状态到store
      store.selectEvent(state.selectedEventId);
      store.selectCurvePoint(state.selectedCurvePointIndex);

      if (state.events.length > 0) {
        store.setEvents(state.events);
      }

      if (debugMode) {
        logger.debug(LogModule.PROJECT, "恢复标签页状态", { fileUuid });
      }
    } catch (error) {
      logger.error(LogModule.PROJECT, "恢复状态失败", { fileUuid, error });
    }
  };

  // 查找LRU标签页
  const findLRUTab = (): EnhancedFileTab | null => {
    let lruTab: EnhancedFileTab | null = null;
    let oldestTime = Date.now();

    for (const tab of openTabs.value.values()) {
      if (!tab.isActive && tab.lastActiveTime < oldestTime) {
        oldestTime = tab.lastActiveTime;
        lruTab = tab;
      }
    }

    return lruTab;
  };

  // 查找最近使用的标签页
  const findMostRecentTab = (): EnhancedFileTab | null => {
    let recentTab: EnhancedFileTab | null = null;
    let recentTime = 0;

    for (const tab of openTabs.value.values()) {
      if (tab.lastActiveTime > recentTime) {
        recentTime = tab.lastActiveTime;
        recentTab = tab;
      }
    }

    return recentTab;
  };

  // 更新标签页修改状态
  const updateTabModifiedState = (fileUuid: string, isModified: boolean): void => {
    const tab = openTabs.value.get(fileUuid);
    if (tab) {
      tab.isModified = isModified;
    }
  };

  // 清理所有标签页
  const closeAllTabs = (): void => {
    for (const fileUuid of Array.from(openTabs.value.keys())) {
      closeTab(fileUuid, false);
    }
  };

  // 监听项目文件变化，自动清理已删除文件的标签页
  watch(
    () => projectStore.activeProject?.files,
    (newFiles, oldFiles) => {
      if (!oldFiles || !newFiles) return;

      const currentFileUuids = new Set(newFiles.map(f => f.fileUuid));
      const openTabUuids = Array.from(openTabs.value.keys());

      // 检查是否有标签页对应的文件已被删除
      for (const tabUuid of openTabUuids) {
        if (!currentFileUuids.has(tabUuid)) {
          if (debugMode) {
            logger.info(LogModule.PROJECT, `文件已删除，自动关闭标签页: ${tabUuid}`);
          }
          closeTab(tabUuid, false); // 静默关闭
        }
      }
    },
    { deep: true }
  );

  return {
    // 状态
    openTabs: computed(() => Array.from(openTabs.value.values())),
    activeTabKey: computed(() => activeTabKey.value),
    activeTab,
    tabCount,
    tabList,

    // 方法
    openTab,
    switchToTab,
    closeTab,
    closeAllTabs,
    updateTabModifiedState,
    saveTabState,
    restoreTabState,

    // 工具方法
    hasTab: (fileUuid: string) => openTabs.value.has(fileUuid),
    getTab: (fileUuid: string) => openTabs.value.get(fileUuid),
  };
}
