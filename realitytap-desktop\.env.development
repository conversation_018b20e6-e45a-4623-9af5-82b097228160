# RealityTap Studio 开发环境配置

# 日志配置
VITE_LOG_LEVEL=DEBUG
VITE_ENABLE_PERFORMANCE_LOG=true
VITE_ENABLE_DRAG_LOG=false

# 开发调试开关
VITE_DEBUG_WAVEFORM=true
VITE_DEBUG_DEVICE=true
VITE_DEBUG_AUDIO=false

# === 更新配置 ===
# 注意：更新服务器端点现在通过 tauri.conf.json 配置
# 这里只保留客户端行为相关的配置
VITE_UPDATE_CHANNEL=beta

# === 调试配置 ===
VITE_ENABLE_UPDATER_DEBUG=true
VITE_ENABLE_VERBOSE_LOGGING=true

# === 功能开关 ===
VITE_ENABLE_AUTO_UPDATE=true
VITE_ENABLE_BACKGROUND_UPDATE=true
VITE_ENABLE_UPDATE_NOTIFICATIONS=true

# === 性能配置 ===
VITE_UPDATE_CHECK_INTERVAL=5
VITE_MAX_UPDATE_RETRIES=3
VITE_DOWNLOAD_TIMEOUT=300

# === 安全配置 ===
VITE_ALLOWED_UPDATE_DOMAINS=localhost,127.0.0.1,dev-releases.realitytap.com
VITE_FORCE_HTTPS_UPDATES=false
