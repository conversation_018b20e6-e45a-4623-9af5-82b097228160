/**
 * OTA 服务
 * 提供 Over-The-Air 更新相关功能
 */

import { invoke } from '@tauri-apps/api/core';

// 进程信息接口
export interface ProcessInfo {
  pid: number;
  name: string;
  processType: 'MainApplication' | 'EditorWindow' | 'AudioService' | 'BackgroundService';
  isCritical: boolean;
  priority: number;
  memoryUsage?: number;
  cpuUsage?: number;
}

// 进程关闭结果接口
export interface ProcessCloseResult {
  pid: number;
  success: boolean;
  closeMethod: 'Graceful' | 'Force' | 'Timeout';
  errorMessage?: string;
  timeTaken?: number;
}

// 安全安装选项接口
export interface SafeInstallOptions {
  skipBackup?: boolean;
  forceInstall?: boolean;
  cleanupOnFailure?: boolean;
  timeout?: number;
  validateSignature?: boolean;
  requireElevation?: boolean;
}

// 安全安装结果接口
export interface SafeInstallResult {
  success: boolean;
  errorMessage?: string;
  installPath?: string;
  backupPath?: string;
  logs?: string[];
  processResults?: ProcessCloseResult[];
}

// OTA 更新信息接口
export interface OTAUpdateInfo {
  version: string;
  downloadUrl: string;
  signature: string;
  checksum: string;
  size: number;
  releaseNotes?: string;
  isForced?: boolean;
  minimumVersion?: string;
}

// OTA 下载进度接口
export interface OTADownloadProgress {
  downloaded: number;
  total: number;
  percentage: number;
  speed?: number;
  remainingTime?: number;
}

/**
 * OTA 服务类
 */
export class OTAService {
  /**
   * 检查更新
   */
  async checkForUpdates(): Promise<{
    hasUpdate: boolean;
    updateInfo?: OTAUpdateInfo;
    errorMessage?: string;
  }> {
    try {
      const result = await invoke<{
        hasUpdate: boolean;
        updateInfo?: OTAUpdateInfo;
        errorMessage?: string;
      }>('check_for_updates');
      
      return result;
    } catch (error) {
      console.error('❌ 检查更新失败:', error);
      return {
        hasUpdate: false,
        errorMessage: error instanceof Error ? error.message : '检查更新失败',
      };
    }
  }

  /**
   * 下载更新
   */
  async downloadUpdate(
    updateInfo: OTAUpdateInfo,
    onProgress?: (progress: OTADownloadProgress) => void
  ): Promise<{
    success: boolean;
    filePath?: string;
    errorMessage?: string;
  }> {
    try {
      // 注册进度回调
      if (onProgress) {
        // 这里应该设置一个监听器来接收下载进度事件
        // 具体实现取决于 Tauri 后端的事件系统
      }
      
      const result = await invoke<{
        success: boolean;
        filePath?: string;
        errorMessage?: string;
      }>('download_update', { updateInfo });
      
      return result;
    } catch (error) {
      console.error('❌ 下载更新失败:', error);
      return {
        success: false,
        errorMessage: error instanceof Error ? error.message : '下载更新失败',
      };
    }
  }

  /**
   * 获取运行中的进程列表
   */
  async getRunningProcesses(): Promise<ProcessInfo[]> {
    try {
      const processes = await invoke<ProcessInfo[]>('get_running_processes');
      return processes;
    } catch (error) {
      console.error('❌ 获取进程列表失败:', error);
      return [];
    }
  }

  /**
   * 优雅关闭进程
   */
  async gracefulCloseProcesses(
    processes: ProcessInfo[]
  ): Promise<ProcessCloseResult[]> {
    try {
      const results = await invoke<ProcessCloseResult[]>('graceful_close_processes', {
        processes,
      });
      
      return results;
    } catch (error) {
      console.error('❌ 关闭进程失败:', error);
      return processes.map(p => ({
        pid: p.pid,
        success: false,
        closeMethod: 'Force' as const,
        errorMessage: error instanceof Error ? error.message : '关闭进程失败',
      }));
    }
  }

  /**
   * 安装更新
   */
  async installUpdate(
    filePath: string,
    options: SafeInstallOptions = {}
  ): Promise<SafeInstallResult> {
    try {
      const result = await invoke<SafeInstallResult>('install_update', {
        filePath,
        options,
      });
      
      return result;
    } catch (error) {
      console.error('❌ 安装更新失败:', error);
      return {
        success: false,
        errorMessage: error instanceof Error ? error.message : '安装更新失败',
      };
    }
  }

  /**
   * 验证更新文件
   */
  async verifyUpdateFile(
    filePath: string,
    expectedChecksum: string,
    signature?: string
  ): Promise<{
    isValid: boolean;
    errorMessage?: string;
  }> {
    try {
      const result = await invoke<{
        isValid: boolean;
        errorMessage?: string;
      }>('verify_update_file', {
        filePath,
        expectedChecksum,
        signature,
      });
      
      return result;
    } catch (error) {
      console.error('❌ 验证更新文件失败:', error);
      return {
        isValid: false,
        errorMessage: error instanceof Error ? error.message : '验证失败',
      };
    }
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles(): Promise<boolean> {
    try {
      const result = await invoke<boolean>('cleanup_temp_files');
      return result;
    } catch (error) {
      console.error('❌ 清理临时文件失败:', error);
      return false;
    }
  }

  /**
   * 获取更新历史
   */
  async getUpdateHistory(): Promise<{
    version: string;
    installDate: string;
    success: boolean;
    errorMessage?: string;
  }[]> {
    try {
      const history = await invoke<{
        version: string;
        installDate: string;
        success: boolean;
        errorMessage?: string;
      }[]>('get_update_history');
      
      return history;
    } catch (error) {
      console.error('❌ 获取更新历史失败:', error);
      return [];
    }
  }
}

// 导出单例实例
export const otaService = new OTAService();
