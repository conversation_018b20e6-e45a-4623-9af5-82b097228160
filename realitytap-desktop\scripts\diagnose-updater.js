#!/usr/bin/env node

/**
 * RealityTap Desktop 更新器诊断工具
 * 用于诊断和修复 "Invalid encoding in minisign data" 错误
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step} ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'cyan');
}

// 检查文件是否存在
function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    logSuccess(`${description}: ${filePath}`);
    return true;
  } else {
    logError(`${description} 不存在: ${filePath}`);
    return false;
  }
}

// 解码 Base64
function decodeBase64(encoded) {
  try {
    return Buffer.from(encoded, 'base64').toString('utf8');
  } catch (error) {
    return null;
  }
}

// 检查配置文件
function checkConfigurations() {
  logStep('1️⃣', '检查 Tauri 配置文件');
  
  const configs = [
    { file: 'src-tauri/tauri.conf.json', env: '生产环境' },
    { file: 'src-tauri/tauri.dev.conf.json', env: '开发环境' }
  ];
  
  const configData = {};
  
  configs.forEach(({ file, env }) => {
    if (checkFile(file, `${env}配置`)) {
      try {
        const content = JSON.parse(fs.readFileSync(file, 'utf8'));
        configData[env] = content;
        
        if (content.plugins?.updater) {
          logSuccess(`${env} updater 插件已启用`);
          logInfo(`  端点: ${content.plugins.updater.endpoints?.join(', ') || '未配置'}`);
          
          if (content.plugins.updater.pubkey) {
            const decoded = decodeBase64(content.plugins.updater.pubkey);
            if (decoded) {
              logSuccess(`  公钥格式正确`);
              logInfo(`  公钥内容: ${decoded.substring(0, 50)}...`);
            } else {
              logError(`  公钥 Base64 解码失败`);
            }
          } else {
            logError(`  缺少公钥配置`);
          }
        } else {
          logError(`${env} updater 插件未配置`);
        }
      } catch (error) {
        logError(`解析 ${file} 失败: ${error.message}`);
      }
    }
  });
  
  return configData;
}

// 检查密钥文件
function checkKeyFiles() {
  logStep('2️⃣', '检查密钥文件');
  
  const keyPaths = [
    'keys/realitytap-updater.key',
    'keys/realitytap-updater.pub',
    '../realitytap-ota-server/keys/realitytap-updater.key',
    '../realitytap-ota-server/keys/realitytap-updater.pub'
  ];
  
  let foundKeys = false;
  keyPaths.forEach(keyPath => {
    if (fs.existsSync(keyPath)) {
      logSuccess(`找到密钥文件: ${keyPath}`);
      foundKeys = true;
    }
  });
  
  if (!foundKeys) {
    logWarning('未找到密钥文件，可能需要生成新的密钥对');
  }
  
  return foundKeys;
}

// 测试更新端点
async function testUpdateEndpoints(configData) {
  logStep('3️⃣', '测试更新端点');
  
  const testEndpoint = async (url, env) => {
    try {
      logInfo(`测试 ${env} 端点: ${url}`);
      
      // 替换模板变量
      const testUrl = url
        .replace('{{target}}', 'windows-x86_64')
        .replace('{{current_version}}', '1.0.0');
      
      logInfo(`实际请求 URL: ${testUrl}`);
      
      // 使用 curl 测试（如果可用）
      try {
        const result = execSync(`curl -s -w "HTTP_CODE:%{http_code}" "${testUrl}"`, { 
          encoding: 'utf8',
          timeout: 10000 
        });
        
        const lines = result.split('\n');
        const httpCode = lines[lines.length - 1].replace('HTTP_CODE:', '');
        const body = lines.slice(0, -1).join('\n');
        
        logInfo(`HTTP 状态码: ${httpCode}`);
        
        if (httpCode === '200') {
          try {
            const response = JSON.parse(body);
            logSuccess(`端点响应正常`);
            logInfo(`  版本: ${response.version || '未知'}`);
            logInfo(`  签名长度: ${response.signature?.length || 0} 字符`);
            
            if (response.signature) {
              const decoded = decodeBase64(response.signature);
              if (decoded) {
                logSuccess(`  签名格式正确`);
              } else {
                logError(`  签名 Base64 解码失败`);
              }
            } else {
              logWarning(`  响应中缺少签名`);
            }
          } catch (parseError) {
            logError(`解析响应 JSON 失败: ${parseError.message}`);
          }
        } else if (httpCode === '204') {
          logInfo(`无可用更新 (HTTP 204)`);
        } else {
          logWarning(`端点返回状态码: ${httpCode}`);
        }
      } catch (curlError) {
        logWarning(`curl 测试失败: ${curlError.message}`);
      }
    } catch (error) {
      logError(`测试端点失败: ${error.message}`);
    }
  };
  
  // 测试开发环境端点
  if (configData['开发环境']?.plugins?.updater?.endpoints) {
    for (const endpoint of configData['开发环境'].plugins.updater.endpoints) {
      await testEndpoint(endpoint, '开发环境');
    }
  }
  
  // 测试生产环境端点
  if (configData['生产环境']?.plugins?.updater?.endpoints) {
    for (const endpoint of configData['生产环境'].plugins.updater.endpoints) {
      await testEndpoint(endpoint, '生产环境');
    }
  }
}

// 检查环境变量
function checkEnvironmentVariables() {
  logStep('4️⃣', '检查环境变量');
  
  const envVars = [
    'TAURI_PRIVATE_KEY',
    'TAURI_KEY_PASSWORD',
    'TAURI_PRIVATE_KEY_PATH'
  ];
  
  envVars.forEach(varName => {
    if (process.env[varName]) {
      logSuccess(`${varName} 已设置`);
    } else {
      logInfo(`${varName} 未设置`);
    }
  });
}

// 提供修复建议
function provideFixes() {
  logStep('5️⃣', '修复建议');
  
  log('\n🔧 可能的解决方案:', 'yellow');
  
  log('\n1. 重新生成密钥对:', 'cyan');
  log('   cd ../realitytap-ota-server');
  log('   npm run generate-keypair');
  
  log('\n2. 更新客户端公钥配置:', 'cyan');
  log('   将生成的公钥复制到 tauri.conf.json 和 tauri.dev.conf.json');
  
  log('\n3. 检查服务器签名配置:', 'cyan');
  log('   确保 OTA 服务器使用正确的私钥进行签名');
  
  log('\n4. 验证环境一致性:', 'cyan');
  log('   确保开发环境和生产环境使用匹配的密钥对');
  
  log('\n5. 清理缓存:', 'cyan');
  log('   rm -rf src-tauri/target');
  log('   npm run build');
}

// 主函数
async function main() {
  log('🔍 RealityTap Desktop 更新器诊断工具', 'magenta');
  log('=' .repeat(50), 'magenta');
  
  try {
    const configData = checkConfigurations();
    checkKeyFiles();
    checkEnvironmentVariables();
    await testUpdateEndpoints(configData);
    provideFixes();
    
    log('\n✨ 诊断完成！请根据上述建议进行修复。', 'green');
  } catch (error) {
    logError(`诊断过程中发生错误: ${error.message}`);
    process.exit(1);
  }
}

// 运行诊断
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
