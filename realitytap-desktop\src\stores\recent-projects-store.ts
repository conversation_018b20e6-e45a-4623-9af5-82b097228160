import { defineStore } from "pinia";
import { ref } from "vue";
import { invoke } from "@tauri-apps/api/core";
import { logger, LogModule } from "@/utils/logger/logger";

export interface RecentProject {
  projectUuid?: string;
  projectName: string;
  projectPath: string;
  lastAccessed: string;
  fileCount: number;
  icon: string;
}

export const useRecentProjectsStore = defineStore("recentProjects", () => {
  const recentProjects = ref<RecentProject[]>([]);
  const isLoading = ref(false);

  // 加载最近项目列表
  async function loadRecentProjects() {
    isLoading.value = true;
    try {
      const result = await invoke<{ projects: RecentProject[] }>(
        "get_recent_projects"
      );
      recentProjects.value = result.projects;
    } catch (error) {
      logger.error(LogModule.PROJECT, "RecentProjects loading error", error);
      recentProjects.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  // 移除单个项目
  async function removeRecentProject(projectPath: string) {
    try {
      const result = await invoke<{ projects: RecentProject[] }>(
        "remove_recent_project",
        { projectDirPath: projectPath }
      );
      recentProjects.value = result.projects;
    } catch (error) {
      logger.error(LogModule.PROJECT, "移除最近项目失败", error);
    }
  }

  // 清空列表
  async function clearRecentProjects() {
    try {
      await invoke("clear_recent_projects");
      recentProjects.value = [];
    } catch (error) {
      logger.error(LogModule.PROJECT, "清空最近项目失败", error);
    }
  }

  return {
    recentProjects,
    isLoading,
    loadRecentProjects,
    removeRecentProject,
    clearRecentProjects,
  };
});
