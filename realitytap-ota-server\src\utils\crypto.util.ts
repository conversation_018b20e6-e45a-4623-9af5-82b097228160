import crypto from 'crypto';
import fs from 'fs-extra';
import { logger } from '@/utils/logger.util';

/**
 * 加密和哈希工具类
 */
export class CryptoUtil {
  /**
   * 计算文件的 SHA256 哈希值
   * @param filePath 文件路径
   * @returns SHA256 哈希值
   */
  static async calculateFileHash(filePath: string): Promise<string> {
    try {
      const hash = crypto.createHash('sha256');
      const stream = fs.createReadStream(filePath);

      return new Promise((resolve, reject) => {
        stream.on('data', (data) => {
          hash.update(data);
        });

        stream.on('end', () => {
          const hashValue = hash.digest('hex');
          resolve(`sha256:${hashValue}`);
        });

        stream.on('error', (error) => {
          reject(new Error(`Failed to calculate file hash: ${error.message}`));
        });
      });
    } catch (error) {
      throw new Error(`Failed to calculate file hash: ${error}`);
    }
  }

  /**
   * 计算字符串的 SHA256 哈希值
   * @param data 要哈希的数据
   * @returns SHA256 哈希值
   */
  static calculateStringHash(data: string): string {
    try {
      const hash = crypto.createHash('sha256');
      hash.update(data, 'utf8');
      return `sha256:${hash.digest('hex')}`;
    } catch (error) {
      throw new Error(`Failed to calculate string hash: ${error}`);
    }
  }

  /**
   * 验证文件哈希值
   * @param filePath 文件路径
   * @param expectedHash 期望的哈希值
   * @returns 是否匹配
   */
  static async verifyFileHash(filePath: string, expectedHash: string): Promise<boolean> {
    try {
      const actualHash = await this.calculateFileHash(filePath);
      return actualHash === expectedHash;
    } catch (error) {
      logger.error('Failed to verify file hash', { filePath, expectedHash, error });
      return false;
    }
  }

  /**
   * 生成随机字符串
   * @param length 长度
   * @returns 随机字符串
   */
  static generateRandomString(length: number = 32): string {
    try {
      return crypto.randomBytes(length).toString('hex');
    } catch (error) {
      throw new Error(`Failed to generate random string: ${error}`);
    }
  }

  /**
   * 生成 UUID v4
   * @returns UUID
   */
  static generateUUID(): string {
    try {
      return crypto.randomUUID();
    } catch (error) {
      throw new Error(`Failed to generate UUID: ${error}`);
    }
  }

  /**
   * 计算文件的 MD5 哈希值（用于兼容性）
   * @param filePath 文件路径
   * @returns MD5 哈希值
   */
  static async calculateFileMD5(filePath: string): Promise<string> {
    try {
      const hash = crypto.createHash('md5');
      const stream = fs.createReadStream(filePath);

      return new Promise((resolve, reject) => {
        stream.on('data', (data) => {
          hash.update(data);
        });

        stream.on('end', () => {
          const hashValue = hash.digest('hex');
          resolve(`md5:${hashValue}`);
        });

        stream.on('error', (error) => {
          reject(new Error(`Failed to calculate MD5 hash: ${error.message}`));
        });
      });
    } catch (error) {
      throw new Error(`Failed to calculate MD5 hash: ${error}`);
    }
  }

  /**
   * 解析哈希值格式
   * @param hashString 哈希字符串（如 "sha256:abc123"）
   * @returns 解析结果
   */
  static parseHash(hashString: string): { algorithm: string; hash: string } {
    const parts = hashString.split(':');
    if (parts.length !== 2) {
      throw new Error('Invalid hash format. Expected "algorithm:hash"');
    }

    const [algorithm, hash] = parts;
    if (!algorithm || !hash) {
      throw new Error('Invalid hash format. Algorithm or hash is empty');
    }

    return { algorithm, hash };
  }

  /**
   * 验证哈希格式
   * @param hashString 哈希字符串
   * @returns 是否有效
   */
  static isValidHashFormat(hashString: string): boolean {
    try {
      const { algorithm, hash } = this.parseHash(hashString);
      
      // 检查支持的算法
      const supportedAlgorithms = ['sha256', 'md5', 'sha1', 'sha512'];
      if (!supportedAlgorithms.includes(algorithm.toLowerCase())) {
        return false;
      }

      // 检查哈希长度
      const expectedLengths: Record<string, number> = {
        md5: 32,
        sha1: 40,
        sha256: 64,
        sha512: 128,
      };

      const expectedLength = expectedLengths[algorithm.toLowerCase()];
      if (expectedLength && hash.length !== expectedLength) {
        return false;
      }

      // 检查是否为有效的十六进制字符串
      return /^[a-fA-F0-9]+$/.test(hash);
    } catch (error) {
      return false;
    }
  }

  /**
   * 创建 HMAC 签名
   * @param data 要签名的数据
   * @param secret 密钥
   * @param algorithm 算法（默认 sha256）
   * @returns HMAC 签名
   */
  static createHMAC(data: string, secret: string, algorithm: string = 'sha256'): string {
    try {
      const hmac = crypto.createHmac(algorithm, secret);
      hmac.update(data);
      return hmac.digest('hex');
    } catch (error) {
      throw new Error(`Failed to create HMAC: ${error}`);
    }
  }

  /**
   * 验证 HMAC 签名
   * @param data 原始数据
   * @param signature 签名
   * @param secret 密钥
   * @param algorithm 算法（默认 sha256）
   * @returns 是否有效
   */
  static verifyHMAC(data: string, signature: string, secret: string, algorithm: string = 'sha256'): boolean {
    try {
      const expectedSignature = this.createHMAC(data, secret, algorithm);
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      logger.error('Failed to verify HMAC', { error });
      return false;
    }
  }
}
