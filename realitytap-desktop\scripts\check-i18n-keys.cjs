#!/usr/bin/env node

/**
 * 检查国际化翻译键的完整性
 * 确保所有语言文件都包含相同的翻译键
 */

const fs = require('fs');
const path = require('path');

// 语言文件路径
const localeFiles = {
  'zh-CN': path.join(__dirname, '../src/locales/zh-CN.ts'),
  'en-US': path.join(__dirname, '../src/locales/en-US.ts'),
  'ja-JP': path.join(__dirname, '../src/locales/ja-JP.ts'),
  'ko-KR': path.join(__dirname, '../src/locales/ko-KR.ts'),
};

// 提取翻译键的函数
function extractKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

// 加载语言文件
function loadLocaleFile(filePath) {
  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 简单的方式：使用 eval 来解析 TypeScript 导出
    // 注意：这只适用于简单的对象导出
    const match = content.match(/export default\s+({[\s\S]*})\s*(?:as const)?;?\s*$/);
    if (!match) {
      throw new Error('无法解析语言文件格式');
    }
    
    // 使用 Function 构造器来安全地解析对象
    const objStr = match[1];
    const obj = new Function('return ' + objStr)();
    
    return obj;
  } catch (error) {
    console.error(`❌ 加载语言文件失败: ${filePath}`);
    console.error(`   错误: ${error.message}`);
    return null;
  }
}

// 主检查函数
function checkI18nKeys() {
  console.log('🔍 检查国际化翻译键的完整性...\n');
  
  const localeData = {};
  const allKeys = new Set();
  
  // 加载所有语言文件
  for (const [locale, filePath] of Object.entries(localeFiles)) {
    console.log(`📖 加载 ${locale} 语言文件...`);
    
    const data = loadLocaleFile(filePath);
    if (!data) {
      return false;
    }
    
    const keys = extractKeys(data);
    localeData[locale] = { data, keys: new Set(keys) };
    
    // 收集所有键
    keys.forEach(key => allKeys.add(key));
    
    console.log(`   ✅ 找到 ${keys.length} 个翻译键`);
  }
  
  console.log(`\n📊 总共找到 ${allKeys.size} 个唯一翻译键\n`);
  
  // 检查缺失的键
  let hasErrors = false;
  
  for (const [locale, { keys }] of Object.entries(localeData)) {
    const missingKeys = [...allKeys].filter(key => !keys.has(key));
    
    if (missingKeys.length > 0) {
      hasErrors = true;
      console.log(`❌ ${locale} 缺失 ${missingKeys.length} 个翻译键:`);
      missingKeys.forEach(key => {
        console.log(`   - ${key}`);
      });
      console.log();
    } else {
      console.log(`✅ ${locale} 翻译键完整`);
    }
  }
  
  // 检查特定的更新相关键
  console.log('\n🔍 检查更新相关的关键翻译键:');
  const updateKeys = [
    'update.confirmInstallation',
    'update.downloading',
    'update.installing',
    'update.updateAvailable',
    'update.installNow',
    'about.updateCheck.checking'
  ];
  
  for (const key of updateKeys) {
    const missingLocales = [];
    
    for (const [locale, { keys }] of Object.entries(localeData)) {
      if (!keys.has(key)) {
        missingLocales.push(locale);
      }
    }
    
    if (missingLocales.length > 0) {
      hasErrors = true;
      console.log(`❌ ${key}: 缺失于 ${missingLocales.join(', ')}`);
    } else {
      console.log(`✅ ${key}: 所有语言都有`);
    }
  }
  
  if (!hasErrors) {
    console.log('\n🎉 所有语言文件的翻译键都完整！');
    return true;
  } else {
    console.log('\n⚠️ 发现翻译键不一致的问题，请修复后重试。');
    return false;
  }
}

// 运行检查
if (require.main === module) {
  const success = checkI18nKeys();
  process.exit(success ? 0 : 1);
}

module.exports = { checkI18nKeys };
