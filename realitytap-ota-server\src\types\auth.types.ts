/**
 * 身份验证相关类型定义
 */

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  expiresIn: number;
  user: AdminUser;
}

export interface AdminUser {
  id: string;
  username: string;
  role: 'admin';
  loginTime: string;
}

export interface JWTPayload {
  userId: string;
  username: string;
  role: 'admin';
  iat: number;
  exp: number;
}

import { Request } from 'express';

export interface AuthenticatedRequest extends Request {
  user?: AdminUser;
}

export interface AuthenticatedRequestWithFile extends AuthenticatedRequest {
  file?: Express.Multer.File;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  expiresIn: number;
}

// 管理员配置接口
export interface AdminConfig {
  username: string;
  password: string;
  jwtSecret: string;
  jwtExpiresIn: string;
  sessionTimeout: number;
}

// 文件上传相关类型
export interface FileUploadRequest {
  version: string;
  platform: 'windows' | 'macos' | 'linux';
  architecture: 'x86_64' | 'aarch64' | 'x86';
  channel: 'stable' | 'beta' | 'alpha';
  releaseNotes?: string;
  isForced?: boolean;
  fileHash?: string; // 可选的文件hash值，从.hash文件中读取
  signature?: string; // 可选的签名值，从.sig文件中读取
}

export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
}



export enum UploadStatus {
  INITIALIZING = 'initializing',
  UPLOADING = 'uploading',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  MERGING = 'merging',
}

// 批量上传相关类型
export interface BatchUploadRequest {
  files: BatchUploadFileInfo[];
  metadata: FileUploadRequest;
}

export interface BatchUploadFileInfo {
  filename: string;
  fileSize: number;
  fileHash: string;
  fileType: 'installer' | 'signature'; // installer: msi/exe/dmg等, signature: .sig文件
}

export interface BatchUploadResponse {
  sessionId: string;
  files: BatchUploadFileStatus[];
  totalFiles: number;
  totalSize: number;
}

export interface BatchUploadFileStatus {
  filename: string;
  fileType: 'installer' | 'signature';
  status: UploadStatus;
  uploadedBytes: number;
  totalBytes: number;
  error?: string;
}

export interface BatchUploadProgress {
  sessionId: string;
  files: BatchUploadFileStatus[];
  overallProgress: {
    uploadedBytes: number;
    totalBytes: number;
    percentage: number;
    completedFiles: number;
    totalFiles: number;
  };
  status: UploadStatus;
}

export interface UploadError {
  code: string;
  message: string;
  chunkIndex?: number;
  retryable: boolean;
}

// 管理统计信息
export interface AdminStats {
  system: {
    uptime: number;
    nodeVersion: string;
    platform: string;
    architecture: string;
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    disk: {
      used: number;
      total: number;
      percentage: number;
    };
  };
  ota: {
    totalVersions: number;
    totalDownloads: number;
    totalSize: number;
    channels: {
      stable: number;
      beta: number;
      alpha: number;
    };
    platforms: {
      windows: number;
      macos: number;
      linux: number;
    };
  };
  recent: {
    downloads: Array<{
      filename: string;
      timestamp: string;
      ip: string;
      userAgent: string;
    }>;
    uploads: Array<{
      filename: string;
      version: string;
      timestamp: string;
      size: number;
    }>;
  };
}

// 日志查询参数
export interface LogQueryParams {
  module?: 'user_operation' | 'version_management' | 'ota_function' | 'system';
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
  search?: string;
}

// 日志条目
export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  meta?: any;
}

// 版本删除请求
export interface DeleteVersionRequest {
  version: string;
  platform?: string;
  architecture?: string;
  channel?: string;
}

// 系统清理请求
export interface CleanupRequest {
  cleanTempFiles?: boolean;
  cleanOldLogs?: boolean;
  cleanOldVersions?: boolean;
  olderThanDays?: number;
}

// 系统清理响应
export interface CleanupResponse {
  tempFilesDeleted: number;
  logsDeleted: number;
  versionsDeleted: number;
  spaceFreed: number; // bytes
}
