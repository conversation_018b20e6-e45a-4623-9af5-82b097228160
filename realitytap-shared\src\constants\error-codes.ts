/**
 * 错误代码常量
 */
export const ERROR_CODES = {
  // 版本相关错误
  VERSION_NOT_FOUND: 'VERSION_NOT_FOUND',
  INVALID_VERSION_FORMAT: 'INVALID_VERSION_FORMAT',
  VERSION_DEPRECATED: 'VERSION_DEPRECATED',
  CHANNEL_NOT_FOUND: 'CHANNEL_NOT_FOUND',
  INVALID_CHANNEL: 'INVALID_CHANNEL',

  // 平台相关错误
  PLATFORM_NOT_SUPPORTED: 'PLATFORM_NOT_SUPPORTED',
  ARCHITECTURE_NOT_SUPPORTED: 'ARCHITECTURE_NOT_SUPPORTED',
  INVALID_PLATFORM: 'INVALID_PLATFORM',
  INVALID_ARCHITECTURE: 'INVALID_ARCHITECTURE',

  // 下载相关错误
  DOWNLOAD_FAILED: 'DOWNLOAD_FAILED',
  CHECKSUM_MISMATCH: 'CHECKSUM_MISMATCH',
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  FILE_TYPE_NOT_ALLOWED: 'FILE_TYPE_NOT_ALLOWED',
  INVALID_FILENAME: 'INVALID_FILENAME',
  DOWNLOAD_TIMEOUT: 'DOWNLOAD_TIMEOUT',

  // 验证相关错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  MISSING_PARAMETERS: 'MISSING_PARAMETERS',
  INVALID_REQUEST: 'INVALID_REQUEST',

  // 认证和授权错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  ACCESS_DENIED: 'ACCESS_DENIED',

  // 限流和配额错误
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  TOO_MANY_REQUESTS: 'TOO_MANY_REQUESTS',

  // 服务器错误
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  GATEWAY_TIMEOUT: 'GATEWAY_TIMEOUT',

  // 网络相关错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  CONNECTION_TIMEOUT: 'CONNECTION_TIMEOUT',
  CONNECTION_REFUSED: 'CONNECTION_REFUSED',

  // 配置相关错误
  CONFIG_ERROR: 'CONFIG_ERROR',
  STORAGE_ERROR: 'STORAGE_ERROR',
  METADATA_ERROR: 'METADATA_ERROR',
} as const;

/**
 * 错误代码类型
 */
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

/**
 * 错误消息映射
 */
export const ERROR_MESSAGES: Record<ErrorCode, string> = {
  // 版本相关错误
  VERSION_NOT_FOUND: '版本未找到',
  INVALID_VERSION_FORMAT: '版本格式无效',
  VERSION_DEPRECATED: '版本已弃用',
  CHANNEL_NOT_FOUND: '发布渠道未找到',
  INVALID_CHANNEL: '无效的发布渠道',

  // 平台相关错误
  PLATFORM_NOT_SUPPORTED: '平台不支持',
  ARCHITECTURE_NOT_SUPPORTED: '架构不支持',
  INVALID_PLATFORM: '无效的平台',
  INVALID_ARCHITECTURE: '无效的架构',

  // 下载相关错误
  DOWNLOAD_FAILED: '下载失败',
  CHECKSUM_MISMATCH: '校验和不匹配',
  FILE_NOT_FOUND: '文件未找到',
  FILE_TYPE_NOT_ALLOWED: '文件类型不允许',
  INVALID_FILENAME: '无效的文件名',
  DOWNLOAD_TIMEOUT: '下载超时',

  // 验证相关错误
  VALIDATION_ERROR: '验证错误',
  MISSING_PARAMETERS: '缺少参数',
  INVALID_REQUEST: '无效的请求',

  // 认证和授权错误
  UNAUTHORIZED: '未授权',
  FORBIDDEN: '禁止访问',
  ACCESS_DENIED: '访问被拒绝',

  // 限流和配额错误
  RATE_LIMIT_EXCEEDED: '请求频率超限',
  QUOTA_EXCEEDED: '配额超限',
  TOO_MANY_REQUESTS: '请求过多',

  // 服务器错误
  INTERNAL_SERVER_ERROR: '内部服务器错误',
  SERVICE_UNAVAILABLE: '服务不可用',
  GATEWAY_TIMEOUT: '网关超时',

  // 网络相关错误
  NETWORK_ERROR: '网络错误',
  CONNECTION_TIMEOUT: '连接超时',
  CONNECTION_REFUSED: '连接被拒绝',

  // 配置相关错误
  CONFIG_ERROR: '配置错误',
  STORAGE_ERROR: '存储错误',
  METADATA_ERROR: '元数据错误',
} as const;