import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { invoke } from "@tauri-apps/api/core";
import type {
  TransmissionTask,
  TransmissionEvent,
  TransmissionConfig,
  TransmissionQueueStatus,
  TransmissionStatistics,
  TransmissionHistory,
  BatchTransmissionOptions,
  TransmissionResult,
} from "@/types/device-transmission";
import { TransmissionStatus, TransmissionType, TransmissionPriority, TransmissionEventType } from "@/types/device-transmission";
import { parseErrorMessage } from "@/utils/commonUtils";
import { logger, LogModule } from "@/utils/logger/logger";

// 默认传输配置
const DEFAULT_CONFIG: TransmissionConfig = {
  chunkSize: 1024 * 64, // 64KB
  timeout: 30000, // 30秒
  maxRetries: 3,
  retryDelay: 1000, // 1秒
  enableCompression: true,
  enableEncryption: false,
  enableChecksum: true,
  maxConcurrentTasks: 3,
};

export const useDeviceTransmissionStore = defineStore("deviceTransmission", () => {
  // === 状态 ===
  const tasks = ref<Map<string, TransmissionTask>>(new Map());
  const activeTaskIds = ref<Set<string>>(new Set());
  const config = ref<TransmissionConfig>({ ...DEFAULT_CONFIG });
  const events = ref<TransmissionEvent[]>([]);
  const history = ref<TransmissionHistory[]>([]);
  const maxEventHistory = ref(100);
  const maxHistoryRecords = ref(1000);

  // === 计算属性 ===
  const taskList = computed(() => Array.from(tasks.value.values()));

  const activeTasks = computed(() => taskList.value.filter((task) => activeTaskIds.value.has(task.taskId)));

  const pendingTasks = computed(() => taskList.value.filter((task) => task.status === TransmissionStatus.IDLE || task.status === TransmissionStatus.PREPARING));

  const completedTasks = computed(() => taskList.value.filter((task) => task.status === TransmissionStatus.COMPLETED));

  const failedTasks = computed(() => taskList.value.filter((task) => task.status === TransmissionStatus.FAILED));

  const queueStatus = computed((): TransmissionQueueStatus => {
    const total = taskList.value.length;
    const active = activeTasks.value.length;
    const pending = pendingTasks.value.length;
    const completed = completedTasks.value.length;
    const failed = failedTasks.value.length;

    // 计算总体进度
    const totalProgress = total > 0 ? taskList.value.reduce((sum, task) => sum + task.progress, 0) / total : 0;

    // 计算预估剩余时间
    const estimatedTime = activeTasks.value.reduce((sum, task) => sum + task.estimatedTimeRemaining, 0);

    return {
      totalTasks: total,
      activeTasks: active,
      pendingTasks: pending,
      completedTasks: completed,
      failedTasks: failed,
      totalProgress,
      estimatedTimeRemaining: estimatedTime,
    };
  });

  const statistics = computed((): TransmissionStatistics => {
    const allTasks = [...taskList.value, ...history.value];
    const completed = allTasks.filter((t) => ("status" in t ? t.status === TransmissionStatus.COMPLETED : true));
    const failed = allTasks.filter((t) => ("status" in t ? t.status === TransmissionStatus.FAILED : false));

    const totalBytes = completed.reduce((sum, t) => {
      if ("fileSize" in t) {
        return sum + t.fileSize;
      }
      return sum;
    }, 0);

    const totalDuration = completed.reduce((sum, t) => sum + ("duration" in t ? t.duration : 0), 0);

    const avgSpeed = totalDuration > 0 ? totalBytes / totalDuration : 0;
    const avgDuration = completed.length > 0 ? totalDuration / completed.length : 0;
    const successRate = allTasks.length > 0 ? (completed.length / allTasks.length) * 100 : 0;

    // 按类型统计
    const byType: Record<TransmissionType, number> = {
      [TransmissionType.HE_FILE]: 0,
      [TransmissionType.AUDIO_FILE]: 0,
      [TransmissionType.PROJECT_DATA]: 0,
      [TransmissionType.DEVICE_CONFIG]: 0,
    };

    allTasks.forEach((t) => {
      byType[t.type]++;
    });

    // 按设备统计
    const byDevice: Record<string, number> = {};
    allTasks.forEach((t) => {
      byDevice[t.deviceId] = (byDevice[t.deviceId] || 0) + 1;
    });

    const lastTransmission = completed.sort((a, b) => {
      const aTime = "timestamp" in a ? a.timestamp : "endTime" in a ? a.endTime : "";
      const bTime = "timestamp" in b ? b.timestamp : "endTime" in b ? b.endTime : "";
      return new Date(bTime || "").getTime() - new Date(aTime || "").getTime();
    })[0];

    return {
      totalTasksCreated: allTasks.length,
      totalTasksCompleted: completed.length,
      totalTasksFailed: failed.length,
      totalBytesTransmitted: totalBytes,
      averageSpeed: avgSpeed,
      averageTaskDuration: avgDuration,
      successRate,
      lastTransmissionTime: lastTransmission
        ? "timestamp" in lastTransmission
          ? lastTransmission.timestamp
          : "endTime" in lastTransmission
          ? lastTransmission.endTime
          : null
        : null,
      transmissionsByType: byType,
      transmissionsByDevice: byDevice,
    };
  });

  // === 内部工具函数 ===
  const addEvent = (event: Omit<TransmissionEvent, "timestamp">) => {
    const fullEvent: TransmissionEvent = {
      ...event,
      timestamp: new Date().toISOString(),
    };

    events.value.unshift(fullEvent);

    if (events.value.length > maxEventHistory.value) {
      events.value = events.value.slice(0, maxEventHistory.value);
    }
  };

  const addToHistory = (task: TransmissionTask) => {
    const historyRecord: TransmissionHistory = {
      historyId: `hist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      taskId: task.taskId,
      deviceId: task.deviceId,
      deviceName: task.deviceId, // TODO: 从设备管理器获取设备名称
      type: task.type,
      fileName: task.fileName,
      fileSize: task.fileSize,
      status: task.status,
      duration: task.endTime && task.startTime ? (new Date(task.endTime).getTime() - new Date(task.startTime).getTime()) / 1000 : 0,
      speed: task.speed,
      error: task.error,
      timestamp: task.endTime || new Date().toISOString(),
    };

    history.value.unshift(historyRecord);

    if (history.value.length > maxHistoryRecords.value) {
      history.value = history.value.slice(0, maxHistoryRecords.value);
    }
  };

  const generateTaskId = (): string => {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  // === 任务管理 ===
  const createTask = (
    deviceId: string,
    type: TransmissionType,
    filePath: string,
    fileName: string,
    fileSize: number,
    priority: TransmissionPriority = TransmissionPriority.NORMAL
  ): TransmissionTask => {
    const now = new Date().toISOString();
    const taskId = generateTaskId();

    const task: TransmissionTask = {
      taskId,
      deviceId,
      type,
      status: TransmissionStatus.IDLE,
      priority,
      filePath,
      fileName,
      fileSize,
      progress: 0,
      bytesTransmitted: 0,
      totalBytes: fileSize,
      speed: 0,
      estimatedTimeRemaining: 0,
      startTime: null,
      endTime: null,
      error: null,
      retryCount: 0,
      maxRetries: config.value.maxRetries,
      metadata: {
        originalFilePath: filePath,
        chunkSize: config.value.chunkSize,
        totalChunks: Math.ceil(fileSize / config.value.chunkSize),
        transmittedChunks: 0,
      },
      createdAt: now,
      updatedAt: now,
    };

    tasks.value.set(taskId, task);

    addEvent({
      type: TransmissionEventType.TASK_CREATED,
      taskId,
      deviceId,
      task,
    });

    return task;
  };

  const updateTask = (taskId: string, updates: Partial<TransmissionTask>): boolean => {
    const task = tasks.value.get(taskId);
    if (!task) return false;

    const updatedTask: TransmissionTask = {
      ...task,
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    tasks.value.set(taskId, updatedTask);

    // 如果任务完成或失败，添加到历史记录
    if (updatedTask.status === TransmissionStatus.COMPLETED || updatedTask.status === TransmissionStatus.FAILED) {
      addToHistory(updatedTask);
      activeTaskIds.value.delete(taskId);
    }

    return true;
  };

  const removeTask = (taskId: string): boolean => {
    const task = tasks.value.get(taskId);
    if (!task) return false;

    // 如果任务正在进行，先取消
    if (activeTaskIds.value.has(taskId)) {
      cancelTask(taskId);
    }

    tasks.value.delete(taskId);
    return true;
  };

  // === 传输操作 ===
  const startTask = async (taskId: string): Promise<TransmissionResult> => {
    const task = tasks.value.get(taskId);
    if (!task) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    if (activeTaskIds.value.size >= config.value.maxConcurrentTasks) {
      throw new Error("已达到最大并发任务数限制");
    }

    activeTaskIds.value.add(taskId);

    updateTask(taskId, {
      status: TransmissionStatus.PREPARING,
      startTime: new Date().toISOString(),
    });

    addEvent({
      type: TransmissionEventType.TASK_STARTED,
      taskId,
      deviceId: task.deviceId,
      task: tasks.value.get(taskId),
    });

    try {
      const result = await invoke<TransmissionResult>("start_transmission_task", {
        taskId,
        deviceId: task.deviceId,
        filePath: task.filePath,
        config: config.value,
      });

      updateTask(taskId, {
        status: TransmissionStatus.COMPLETED,
        progress: 100,
        bytesTransmitted: task.totalBytes,
        endTime: new Date().toISOString(),
      });

      addEvent({
        type: TransmissionEventType.TASK_COMPLETED,
        taskId,
        deviceId: task.deviceId,
        task: tasks.value.get(taskId),
      });

      return result;
    } catch (error: any) {
      const errorMessage = parseErrorMessage(error);

      updateTask(taskId, {
        status: TransmissionStatus.FAILED,
        error: errorMessage,
        endTime: new Date().toISOString(),
      });

      addEvent({
        type: TransmissionEventType.TASK_FAILED,
        taskId,
        deviceId: task.deviceId,
        error: errorMessage,
      });

      throw error;
    }
  };

  const cancelTask = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task) return false;

    try {
      await invoke("cancel_transmission_task", { taskId });

      updateTask(taskId, {
        status: TransmissionStatus.CANCELLED,
        endTime: new Date().toISOString(),
      });

      activeTaskIds.value.delete(taskId);

      addEvent({
        type: TransmissionEventType.TASK_CANCELLED,
        taskId,
        deviceId: task.deviceId,
      });

      return true;
    } catch (error) {
      logger.error(LogModule.DEVICE, `取消任务失败: ${taskId}`, error);
      return false;
    }
  };

  const pauseTask = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task || task.status !== TransmissionStatus.TRANSMITTING) return false;

    try {
      await invoke("pause_transmission_task", { taskId });

      updateTask(taskId, {
        status: TransmissionStatus.PAUSED,
      });

      addEvent({
        type: TransmissionEventType.TASK_PAUSED,
        taskId,
        deviceId: task.deviceId,
      });

      return true;
    } catch (error) {
      logger.error(LogModule.DEVICE, `暂停任务失败: ${taskId}`, error);
      return false;
    }
  };

  const resumeTask = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task || task.status !== TransmissionStatus.PAUSED) return false;

    try {
      await invoke("resume_transmission_task", { taskId });

      updateTask(taskId, {
        status: TransmissionStatus.TRANSMITTING,
      });

      addEvent({
        type: TransmissionEventType.TASK_RESUMED,
        taskId,
        deviceId: task.deviceId,
      });

      return true;
    } catch (error) {
      logger.error(LogModule.DEVICE, `恢复任务失败: ${taskId}`, error);
      return false;
    }
  };

  // === 批量操作 ===
  const createBatchTasks = (options: BatchTransmissionOptions): TransmissionTask[] => {
    const tasks: TransmissionTask[] = [];

    options.filePaths.forEach((filePath) => {
      const fileName = filePath.split("/").pop() || filePath;
      // TODO: 获取实际文件大小
      const fileSize = 0;

      options.deviceIds.forEach((deviceId) => {
        const task = createTask(
          deviceId,
          TransmissionType.HE_FILE, // TODO: 根据文件类型确定
          filePath,
          fileName,
          fileSize,
          options.priority
        );
        tasks.push(task);
      });
    });

    return tasks;
  };

  const startBatchTasks = async (taskIds: string[]): Promise<TransmissionResult[]> => {
    const results: TransmissionResult[] = [];

    for (const taskId of taskIds) {
      try {
        const result = await startTask(taskId);
        results.push(result);
      } catch (error) {
        logger.error(LogModule.DEVICE, `批量任务执行失败: ${taskId}`, error);
        // 根据配置决定是否继续执行其他任务
      }
    }

    return results;
  };

  // === 进度更新（由后端调用） ===
  const updateTaskProgress = (
    taskId: string,
    progress: {
      progress: number;
      bytesTransmitted: number;
      speed: number;
      estimatedTimeRemaining: number;
    }
  ): void => {
    updateTask(taskId, {
      status: TransmissionStatus.TRANSMITTING,
      progress: progress.progress,
      bytesTransmitted: progress.bytesTransmitted,
      speed: progress.speed,
      estimatedTimeRemaining: progress.estimatedTimeRemaining,
      metadata: {
        ...tasks.value.get(taskId)?.metadata,
        transmittedChunks: Math.floor(progress.bytesTransmitted / config.value.chunkSize),
      },
    });

    addEvent({
      type: TransmissionEventType.TASK_PROGRESS,
      taskId,
      deviceId: tasks.value.get(taskId)?.deviceId || "",
      progress: progress.progress,
    });
  };

  // === 清理操作 ===
  const clearCompletedTasks = (): number => {
    const completedTaskIds = taskList.value.filter((task) => task.status === TransmissionStatus.COMPLETED).map((task) => task.taskId);

    completedTaskIds.forEach((taskId) => {
      tasks.value.delete(taskId);
    });

    return completedTaskIds.length;
  };

  const clearFailedTasks = (): number => {
    const failedTaskIds = taskList.value.filter((task) => task.status === TransmissionStatus.FAILED).map((task) => task.taskId);

    failedTaskIds.forEach((taskId) => {
      tasks.value.delete(taskId);
    });

    return failedTaskIds.length;
  };

  const clearHistory = (): void => {
    history.value = [];
  };

  return {
    // 只读状态
    tasks: readonly(tasks),
    taskList,
    activeTasks,
    pendingTasks,
    completedTasks,
    failedTasks,
    queueStatus,
    statistics,
    events: readonly(events),
    history: readonly(history),
    config: readonly(config),

    // 操作方法
    createTask,
    updateTask,
    removeTask,
    startTask,
    cancelTask,
    pauseTask,
    resumeTask,
    createBatchTasks,
    startBatchTasks,
    updateTaskProgress,
    clearCompletedTasks,
    clearFailedTasks,
    clearHistory,
  };
});
