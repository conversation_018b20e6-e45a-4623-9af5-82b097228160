<template>
  <div class="redo-button-container">
    <n-tooltip :show-arrow="false" placement="bottom">
      <template #trigger>
        <n-button
          type="primary"
          @click="handleRedo"
          class="redo-btn"
          :class="{ 'redo-btn--disabled': !canRedo }"
          :disabled="!canRedo"
          secondary
        >
          <template #icon>
            <n-icon class="redo-icon">
              <RedoIcon />
            </n-icon>
          </template>
        </n-button>
      </template>
      <span>{{ tooltipText }}</span>
    </n-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { NButton, NIcon, NTooltip } from "naive-ui";
import { ArrowRedo20Regular as RedoIcon } from "@vicons/fluent";
import { useProjectStore } from "@/stores/haptics-project-store";
import { useFileWaveformEditorStore, validateFileUuid } from "@/stores/haptics-editor-store";
import { useI18n } from "@/composables/useI18n";


// 获取stores
const projectStore = useProjectStore();
const { t } = useI18n();

// 获取当前文件的 store 实例
const getCurrentFileStore = () => {
  const fileUuid = projectStore.selectedFileUuid;
  if (!fileUuid) {
    throw new Error(t('project.redo.noFileSelected'));
  }
  const validatedFileUuid = validateFileUuid(fileUuid);
  return useFileWaveformEditorStore(validatedFileUuid);
};

// 响应式状态
const canRedoState = ref(false);
const forceUpdate = ref(0);

// 计算属性：是否可以重做
const canRedo = computed(() => {
  // 强制响应式更新
  forceUpdate.value;

  if (!projectStore.selectedFileUuid) return false;
  try {
    const currentFileStore = getCurrentFileStore();

    // 确保历史记录系统已初始化
    currentFileStore.ensureHistoryInitialized();

    const result = currentFileStore.canRedo();
    canRedoState.value = result;
    return result;
  } catch (error) {
    canRedoState.value = false;
    return false;
  }
});

// 监听事件变化，强制更新按钮状态
watch(
  () => {
    if (!projectStore.selectedFileUuid) return null;
    try {
      const currentFileStore = getCurrentFileStore();
      return {
        events: currentFileStore.events,
        eventsLength: currentFileStore.events?.length || 0
      };
    } catch {
      return null;
    }
  },
  () => {
    // 延迟更新，确保历史记录已经处理完成
    setTimeout(() => {
      forceUpdate.value++;
    }, 100);
  },
  { deep: true, immediate: true }
);

// 定期检查状态更新（作为备用机制）
setInterval(() => {
  if (projectStore.selectedFileUuid) {
    forceUpdate.value++;
  }
}, 2000);

// 计算属性：tooltip文本
const tooltipText = computed(() => {
  if (!projectStore.selectedFileUuid) {
    return t('project.redo.noFileSelected');
  }
  if (canRedo.value) {
    return t('project.redo.redoAction');
  }
  return t('project.redo.noRedoAvailable');
});

// 重做处理函数
const handleRedo = () => {
  if (!canRedo.value) {
    return;
  }

  try {
    const currentFileStore = getCurrentFileStore();

    // 确保历史记录系统已初始化
    currentFileStore.ensureHistoryInitialized();

    currentFileStore.redo();
  } catch (error: any) {
    // 静默处理错误
  }
};
</script>

<style scoped>
/* ===== 重做按钮容器 ===== */
.redo-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* ===== 重做按钮样式 ===== */
.redo-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: transparent !important;
  border: 1px solid #9b59b6 !important;
  color: #9b59b6 !important;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.redo-btn:hover {
  background-color: #9b59b6 !important;
  color: #ffffff !important;
  border-color: #9b59b6 !important;
  transform: scale(1.05);
}

.redo-btn:active {
  transform: scale(0.95);
}

/* ===== 禁用状态样式 ===== */
.redo-btn--disabled {
  background-color: transparent !important;
  border: 1px solid #4a4a4a !important;
  color: #6a6a6a !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.redo-btn--disabled:hover {
  background-color: transparent !important;
  border: 1px solid #4a4a4a !important;
  color: #6a6a6a !important;
  transform: none !important;
}

.redo-btn--disabled:active {
  transform: none !important;
}

.redo-icon {
  font-size: 18px;
  color: inherit !important;
}
</style>
