import type { RenderableContinuousEvent, RenderableEvent, RenderableTransientEvent } from "@/types/haptic-editor";
import type { Ref } from "vue";
import { calculateRawIntensity } from "../utils/coordinate";
import type { DragTarget } from "./useDragState";
import { MIN_CONTINUOUS_DURATION } from "../config/waveform-constants";
import { debounce } from "@/utils/performance/UnifiedDebounceManager";
import { dragLogger } from "@/utils/logger/logger";

/**
 * 拖拽Store更新器 composable
 * 负责在拖拽过程中实时更新store状态
 */
export function useDragStoreUpdater(
  waveformStore: any,
  draggedEvent: Ref<RenderableEvent | null>,
  draggingTarget: Ref<DragTarget | null>,
  draggedCurveIndex: Ref<number>,
  dragStartEventTime: Ref<number>,
  currentDraggedTimeOffset: Ref<number>,
  currentDraggedIntensity: Ref<number>,
  currentDraggedRelativeFrequency: Ref<number>,
  isFrequencyAdjustmentKeyPressed: Ref<boolean>,
  floorTime: (time: number) => number
) {

  // 生成唯一的防抖键，用于拖拽实时更新
  const dragUpdateKey = `drag-update-${Date.now()}`;

  // 实时更新store中的selectedEvent（用于拖拽过程中）
  const updateStoreInRealTime = () => {
    if (!draggedEvent.value) return;

    // 使用统一防抖管理器进行拖拽更新，确保与其他组件协调
    debounce(
      dragUpdateKey,
      () => {
        try {
          // 根据拖动目标类型创建实时更新载荷，使用当前拖拽的临时值
          if (draggingTarget.value === "event") {
            updateEventPosition();
          } else if (draggingTarget.value === "transientPeak" && draggedEvent.value?.type === "transient") {
            updateTransientPeakIntensity();
          } else if (draggingTarget.value === "continuousCurvePoint" && draggedEvent.value?.type === "continuous") {
            updateCurvePoint();
          }
        } catch (error) {
          dragLogger.warn("实时更新store失败:", error);
        }
      },
      8, // 使用8ms延迟，提供更快的拖拽响应（约120fps）
      'high' // 拖拽是高优先级操作
    );
  };

  // 更新事件位置
  const updateEventPosition = () => {
    if (!draggedEvent.value) return;

    const newStartTime = dragStartEventTime.value + currentDraggedTimeOffset.value;
    const event = draggedEvent.value;

    const updatePayload: any = {
      Type: event.type,
      RelativeTime: Math.max(0, floorTime(newStartTime)),
      Parameters: {
        Intensity: event.type === "transient" ? (event as RenderableTransientEvent).intensity : (event as RenderableContinuousEvent).eventIntensity,
        Frequency: event.type === "transient" ? (event as RenderableTransientEvent).frequency : (event as RenderableContinuousEvent).eventFrequency,
      },
    };

    if (event.type === "continuous") {
      updatePayload.Duration = (event as RenderableContinuousEvent).duration;
    }

    waveformStore.updateSelectedEvent(updatePayload);
  };

  // 更新瞬态峰值强度
  const updateTransientPeakIntensity = () => {
    if (!draggedEvent.value || draggedEvent.value.type !== "transient") return;

    const event = draggedEvent.value as RenderableTransientEvent;
    const updatePayload: any = {
      Type: "transient",
      RelativeTime: event.startTime,
      Parameters: {
        Intensity: Math.max(0, Math.min(100, currentDraggedIntensity.value)),
        Frequency: event.frequency,
      },
    };

    waveformStore.updateSelectedEvent(updatePayload);
  };

  // 更新曲线点
  const updateCurvePoint = () => {
    if (!draggedEvent.value || draggedEvent.value.type !== "continuous" || draggedCurveIndex.value < 0) return;

    const continuousEvent = draggedEvent.value as RenderableContinuousEvent;
    const curves = continuousEvent.curves;

    // 特殊处理：第一个Curve点的水平拖拽（调整事件startTime和duration）
    if (draggedCurveIndex.value === 0 && !isFrequencyAdjustmentKeyPressed.value) {
      updateFirstCurvePointDrag(continuousEvent);
      return;
    }

    // 特殊处理：最后一个Curve点的水平拖拽（调整事件endTime和duration）
    if (draggedCurveIndex.value === curves.length - 1 && !isFrequencyAdjustmentKeyPressed.value) {
      updateLastCurvePointDrag(continuousEvent);
      return;
    }

    // 其他曲线点的常规处理
    updateRegularCurvePoint(continuousEvent, curves);
  };

  // 更新第一个Curve点拖拽
  const updateFirstCurvePointDrag = (continuousEvent: RenderableContinuousEvent) => {
    const newStartTime = continuousEvent.startTime + currentDraggedTimeOffset.value;
    const originalEndTime = continuousEvent.stopTime;
    const newDuration = originalEndTime - newStartTime;

    const updatePayload: any = {
      Type: "continuous",
      RelativeTime: Math.max(0, floorTime(newStartTime)),
      Duration: Math.max(MIN_CONTINUOUS_DURATION, Math.floor(newDuration)),
      Parameters: {
        Intensity: continuousEvent.eventIntensity,
        Frequency: continuousEvent.eventFrequency,
      },
    };

    waveformStore.updateSelectedEvent(updatePayload);
  };

  // 更新最后一个Curve点拖拽
  const updateLastCurvePointDrag = (continuousEvent: RenderableContinuousEvent) => {
    const originalStartTime = continuousEvent.startTime;
    const newDuration = continuousEvent.duration + currentDraggedTimeOffset.value;

    const updatePayload: any = {
      Type: "continuous",
      RelativeTime: originalStartTime,
      Duration: Math.max(MIN_CONTINUOUS_DURATION, Math.floor(newDuration)),
      Parameters: {
        Intensity: continuousEvent.eventIntensity,
        Frequency: continuousEvent.eventFrequency,
      },
    };

    waveformStore.updateSelectedEvent(updatePayload);
  };

  // 更新常规曲线点
  const updateRegularCurvePoint = (continuousEvent: RenderableContinuousEvent, curves: any[]) => {
    const updatePayload: any = {
      Type: "continuous",
      updateType: "UPDATE_CURVE_POINT",
      curveIndex: draggedCurveIndex.value,
    };

    if (isFrequencyAdjustmentKeyPressed.value) {
      // 频率调节键+拖拽：更新相对频率
      updatePayload.relativeCurveFrequency = currentDraggedRelativeFrequency.value;
    } else {
      // 普通拖拽：更新位置和强度
      if (draggedCurveIndex.value > 0 && draggedCurveIndex.value < curves.length - 1) {
        updatePayload.newTimeOffset = currentDraggedTimeOffset.value;
        updatePayload.rawIntensity = calculateRawIntensity(currentDraggedIntensity.value, continuousEvent.eventIntensity);
      }
    }

    // 只有当有实际需要更新的参数时才调用
    if (Object.keys(updatePayload).length > 3) {
      waveformStore.updateSelectedEvent(updatePayload);
    }
  };

  return {
    updateStoreInRealTime,
  };
}
