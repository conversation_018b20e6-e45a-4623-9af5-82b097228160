import type { TreeOption as NaiveTreeOption } from "naive-ui";
import type { VNode } from "vue";
import type { HapticFile, HapticsGroup } from "@/types/haptic-project";

export interface TreeNode extends NaiveTreeOption {
  key: string;
  label: string;
  isGroup: boolean;
  item: HapticFile | HapticsGroup; 
  children?: TreeNode[];
  prefix?: () => VNode;
}

export type ContextMenuType = "file" | "group" | "panel";

export interface ContextMenuEventPayload {
  type: ContextMenuType;
  event: MouseEvent;
  item?: HapticFile | HapticsGroup;
} 