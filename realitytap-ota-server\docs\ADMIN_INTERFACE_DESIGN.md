# RealityTap OTA 服务器 - Web 管理界面设计文档

## 📋 项目概述

为 RealityTap OTA 服务器添加基于 Vue3 + Naive UI 的 Web 管理界面，提供版本管理、系统监控和身份验证功能。

## 🎯 功能需求

### 核心功能
- ✅ 管理员身份验证（用户名/密码登录）
- ✅ OTA 版本管理（查看、上传、删除版本文件）
- ✅ 系统状态监控和统计信息展示
- ✅ 操作日志记录和查看

### 技术要求
- **前端**: Vue3 Composition API + `<script setup>` + TypeScript
- **UI库**: Naive UI v2 组件库
- **状态管理**: Pinia
- **构建工具**: Vite
- **后端扩展**: Express.js + JWT 身份验证
- **安全**: 环境变量配置，会话管理，访问控制

## 🏗️ 技术架构

### 后端架构扩展
```
src/
├── middleware/
│   └── auth.middleware.ts          # JWT 身份验证中间件
├── services/
│   └── auth.service.ts             # 身份验证服务
├── controllers/
│   └── admin.controller.ts         # 管理API控制器
└── types/
    └── auth.types.ts               # 身份验证类型定义
```

### 前端项目结构
```
admin-ui/
├── package.json
├── vite.config.ts
├── tsconfig.json
├── src/
│   ├── main.ts                     # 应用入口
│   ├── App.vue                     # 根组件
│   ├── router/
│   │   └── index.ts                # 路由配置
│   ├── stores/
│   │   ├── auth.ts                 # 身份验证状态
│   │   └── admin.ts                # 管理数据状态
│   ├── views/
│   │   ├── Login.vue               # 登录页面
│   │   ├── Dashboard.vue           # 仪表板
│   │   ├── VersionManagement.vue   # 版本管理
│   │   └── SystemStatus.vue        # 系统状态
│   ├── components/
│   │   ├── Layout/
│   │   │   ├── AdminLayout.vue     # 管理布局
│   │   │   └── Header.vue          # 头部组件
│   │   └── Common/
│   │       ├── FileUpload.vue      # 文件上传组件
│   │       └── VersionTable.vue    # 版本表格组件
│   ├── api/
│   │   ├── auth.ts                 # 身份验证API
│   │   ├── admin.ts                # 管理API
│   │   └── types.ts                # API类型定义
│   └── utils/
│       ├── request.ts              # HTTP请求工具
│       └── constants.ts            # 常量定义
```

## 🔐 安全设计

### 身份验证机制
- **JWT Token**: 用于会话管理和API访问控制
- **环境变量配置**: 管理员凭据不硬编码
- **会话超时**: 合理的token过期时间设置
- **访问控制**: 所有管理API需要身份验证

### 环境变量配置
```bash
# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_min_32_chars
JWT_EXPIRES_IN=1h

# 会话配置
SESSION_TIMEOUT=3600000  # 1小时（毫秒）
```

### 安全措施
- 输入验证和清理
- 文件上传类型和大小限制
- 路径遍历攻击防护
- 请求频率限制
- 错误信息不泄露敏感数据

## 🚀 API 设计

### 管理API端点
```typescript
// 身份验证
POST   /api/v1/admin/login          # 管理员登录
POST   /api/v1/admin/logout         # 管理员登出
GET    /api/v1/admin/profile        # 获取管理员信息
POST   /api/v1/admin/refresh        # 刷新token

// 版本管理
POST   /api/v1/admin/upload         # 上传版本文件
DELETE /api/v1/admin/version/:id    # 删除版本
PUT    /api/v1/admin/version/:id    # 更新版本信息
GET    /api/v1/admin/versions       # 获取版本列表

// 系统管理
GET    /api/v1/admin/stats          # 详细统计信息
GET    /api/v1/admin/logs           # 系统日志
GET    /api/v1/admin/health         # 系统健康状态
POST   /api/v1/admin/cleanup        # 清理临时文件
```

### API响应格式
```typescript
// 成功响应
interface SuccessResponse<T = any> {
  success: true;
  data: T;
  timestamp: string;
  version: string;
}

// 错误响应
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  version: string;
}
```

## 🎨 界面设计

### 1. 登录页面 (Login.vue)
- **布局**: 居中的登录卡片
- **表单**: 用户名、密码输入框
- **功能**: 表单验证、错误提示、记住登录状态
- **样式**: 简洁现代的设计风格

### 2. 管理布局 (AdminLayout.vue)
- **侧边栏**: 导航菜单
- **顶部栏**: 用户信息、登出按钮
- **主内容区**: 路由视图
- **响应式**: 支持移动端适配

### 3. 仪表板 (Dashboard.vue)
- **统计卡片**: 版本数量、下载统计、系统状态
- **图表展示**: 下载趋势、版本分布
- **最近活动**: 操作日志列表
- **快速操作**: 常用功能入口

### 4. 版本管理 (VersionManagement.vue)
- **版本列表**: 表格展示所有版本信息
- **文件上传**: 拖拽上传组件
- **操作按钮**: 下载、删除、编辑
- **筛选搜索**: 按平台、渠道筛选

### 5. 系统状态 (SystemStatus.vue)
- **健康检查**: 各组件状态指示器
- **性能指标**: CPU、内存、磁盘使用情况
- **日志查看**: 实时日志流和历史日志
- **系统信息**: 版本、配置信息

## 📋 实施计划

### 第一阶段：后端身份验证 (优先级：高) ✅ 已完成
- [x] 创建身份验证中间件 (`auth.middleware.ts`)
- [x] 实现身份验证服务 (`auth.service.ts`)
- [x] 创建管理API控制器 (`admin.controller.ts`)
- [x] 添加身份验证类型定义 (`auth.types.ts`)
- [x] 更新主应用配置 (`app.ts`)
- [x] 添加必要的依赖包
- [x] 修复所有编译错误
- [x] 测试API端点功能

### 第二阶段：前端基础 (优先级：高)
- [ ] 创建Vue3项目结构
- [ ] 配置Vite构建工具
- [ ] 实现登录页面和身份验证
- [ ] 创建管理布局组件
- [ ] 集成Naive UI组件库

### 第三阶段：版本管理 (优先级：高)
- [ ] 实现文件上传功能
- [ ] 创建版本管理页面
- [ ] 添加版本CRUD操作
- [ ] 实现文件删除功能

### 第四阶段：系统监控 (优先级：中)
- [ ] 创建仪表板页面
- [ ] 实现系统状态监控
- [ ] 添加日志查看功能
- [ ] 集成统计图表

### 第五阶段：部署集成 (优先级：中)
- [ ] 配置静态文件服务
- [ ] 更新Docker配置
- [ ] 添加构建和部署脚本
- [ ] 完善文档和测试

## ✅ 验证计划

### 单元测试
- 身份验证服务测试
- 管理API控制器测试
- 前端组件单元测试

### 集成测试
- API端点集成测试
- 身份验证流程测试
- 文件上传下载测试

### 安全测试
- 身份验证绕过测试
- 文件上传安全测试
- API访问控制测试

### 用户体验测试
- 界面响应性测试
- 操作流程测试
- 错误处理测试

## 📝 开发规范

### 代码规范
- 遵循项目现有的TypeScript严格模式
- 使用ESLint和Prettier进行代码格式化
- 所有函数必须有完整的类型定义
- 统一的错误处理和日志记录

### 安全规范
- 所有用户输入必须验证和清理
- 敏感信息不得硬编码
- 实施适当的访问控制
- 记录所有管理操作日志

### 性能规范
- 文件操作异步化
- 实施适当的缓存策略
- 优化前端资源加载
- 监控API响应时间

---

**文档版本**: v1.0  
**创建日期**: 2024-01-15  
**最后更新**: 2024-01-15  
**状态**: 开发中
