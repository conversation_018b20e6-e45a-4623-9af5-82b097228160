<template>
  <div class="y-axis-container">
    <canvas ref="yAxisCanvas" class="y-axis-canvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, onMounted, watch, defineExpose, computed } from "vue";
import { useWaveformCoordinate } from "../composables/useWaveformCoordinate";

const props = defineProps<{
  canvasHeight: number;
  paddingTop: number;
  paddingBottom: number;
  paddingLeft: number;
  axisColor?: string;
  labelColor?: string;
  fontStyle?: string;
}>();

// 引用和常量
const yAxisCanvas = ref<HTMLCanvasElement | null>(null);
const yAxisCtx = ref<CanvasRenderingContext2D | null>(null);

// 默认样式常量
const AXIS_COLOR = props.axisColor || "#888";
const AXIS_LABEL_COLOR = props.labelColor || "#aaa";
const FONT_STYLE = props.fontStyle || "10px sans-serif";

// 计算图形区域高度
const graphAreaHeight = () => props.canvasHeight - props.paddingTop - props.paddingBottom;

// 使用坐标管理组合式函数
const canvasState = {
  canvasWidth: computed(() => props.paddingLeft),
  canvasHeight: computed(() => props.canvasHeight),
  virtualScrollOffset: ref(0),
  getEffectiveDuration: () => 0,
  getGraphAreaWidth: () => props.paddingLeft,
  getLogicalGraphAreaWidth: () => props.paddingLeft,
  getGraphAreaHeight: graphAreaHeight,
};

const canvasConfig = {
  padding: {
    top: props.paddingTop,
    right: 0,
    bottom: props.paddingBottom,
    left: props.paddingLeft,
  },
  safeOffset: 0,
};

const { mapIntensityToYLocal } = useWaveformCoordinate(canvasState, canvasConfig);

// Y轴绘制函数
const drawYAxis = () => {
  if (!yAxisCtx.value || !yAxisCanvas.value) return;
  const ctx = yAxisCtx.value;

  // 清除画布
  ctx.clearRect(0, 0, props.paddingLeft, props.canvasHeight);

  // Y轴绘制逻辑
  ctx.lineWidth = 1;
  ctx.font = FONT_STYLE;
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";

  // Y-Axis line
  ctx.beginPath();
  ctx.strokeStyle = AXIS_COLOR;
  ctx.fillStyle = AXIS_LABEL_COLOR;
  ctx.moveTo(props.paddingLeft - 1, props.paddingTop);
  ctx.lineTo(props.paddingLeft - 1, props.paddingTop + graphAreaHeight());
  ctx.stroke();

  // Y-Axis Ticks and Labels
  for (let intensity = 0; intensity <= 100; intensity += 20) {
    const y = mapIntensityToYLocal(intensity);
    ctx.beginPath();
    ctx.moveTo(props.paddingLeft - 5, y);
    ctx.lineTo(props.paddingLeft - 1, y);
    ctx.strokeStyle = AXIS_COLOR;
    ctx.stroke();

    // Label
    ctx.textAlign = "right";
    ctx.fillText(intensity.toString(), props.paddingLeft - 8, y);
  }
};

// 调整画布大小函数
const resizeCanvas = () => {
  if (!yAxisCanvas.value || !yAxisCtx.value) return;

  const dpr = window.devicePixelRatio || 1;
  yAxisCanvas.value.width = props.paddingLeft * dpr;
  yAxisCanvas.value.height = props.canvasHeight * dpr;
  yAxisCanvas.value.style.width = `${props.paddingLeft}px`;
  yAxisCanvas.value.style.height = `${props.canvasHeight}px`;
  yAxisCtx.value.scale(dpr, dpr);

  drawYAxis();
};

// 生命周期钩子
onMounted(() => {
  if (yAxisCanvas.value) {
    yAxisCtx.value = yAxisCanvas.value.getContext('2d');
    resizeCanvas();
  }
});

// 监听props变化，触发重绘
watch(
  () => [props.canvasHeight, props.paddingTop, props.paddingBottom, props.paddingLeft],
  () => {
    resizeCanvas();
  },
  { deep: true }
);

// 提供方法供父组件触发重绘
defineExpose({
  redraw: resizeCanvas
});
</script>

<style scoped>
.y-axis-container {
  position: sticky;
  left: 0;
  width: 40px; /* 默认宽度，与paddingLeft一致 */
  height: 100%;
  z-index: 2;
}

.y-axis-canvas {
  display: block;
  width: 100%;
  height: 100%;
}
</style> 