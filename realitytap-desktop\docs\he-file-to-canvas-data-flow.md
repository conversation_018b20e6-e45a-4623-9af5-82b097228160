# .he文件到Canvas绘制的数据流程分析

## 概述

本文档详细分析了realitytap-desktop项目中，数据从.he文件加载到Canvas绘制的完整流程。这个过程涉及文件读取、JSON解析、数据转换、坐标映射和Canvas绘制等多个环节。

## 完整数据流程图

![.he文件到Canvas绘制的数据流程图](./images/he-file-data-flow.svg)

## 绘制技术架构图

![Canvas绘制技术架构图](./images/canvas-drawing-architecture.svg)

## 详细流程分析

### 1. 文件加载阶段

#### 1.1 文件读取
- **位置**: `HapticProjectEditor.vue`
- **方法**: `projectStore.readFileContentFromBackend(fullPath)`
- **后端命令**: `read_file_content_command`
- **输出**: JSON字符串

```typescript
// 文件读取示例
const fileContent = await projectStore.readFileContentFromBackend(fullPath);
```

#### 1.2 缓存机制
- 优先检查内存缓存 `projectStore.getFileCache(newUuid)`
- 缓存命中时直接使用缓存数据，避免磁盘IO
- 缓存未命中时从磁盘加载

### 2. 数据解析阶段

#### 2.1 JSON解析和验证
- **位置**: `types/haptic-file.ts`
- **函数**: `parseRealityTap(jsonString)`
- **验证**: 使用Zod schema进行数据验证
- **支持格式**: V1和V2两种版本

```typescript
// 解析示例
const effectData = parseRealityTap(fileContent);
// 输出: RealityTapEffectV1 | RealityTapEffectV2
```

#### 2.2 数据结构
- **V1格式**: `{Metadata, Pattern: [{Event}]}`
- **V2格式**: `{Metadata, PatternList: [{AbsoluteTime, Pattern: [Event]}]}`

### 3. 数据转换阶段

#### 3.1 扁平化处理
- **位置**: `utils/haptic-event-processor.ts`
- **函数**: `flattenRealityTapEffect(effectData)`
- **目标**: 将嵌套的事件结构转换为扁平的渲染数组

#### 3.2 事件处理
- **函数**: `processEvent(event, absoluteStartTimeOffset)`
- **处理内容**:
  - 生成唯一UUID
  - 计算绝对开始时间
  - 区分事件类型（transient/continuous）
  - 处理曲线点数据（continuous事件）

```typescript
// 转换结果
interface RenderableEvent {
  type: "transient" | "continuous";
  id: string;
  startTime: number;
  // ... 其他属性
}
```

### 4. Canvas绘制阶段

#### 4.1 坐标系统建立
- **组件**: `InteractiveWaveformCanvas.vue`
- **管理器**: `useWaveformCanvas`
- **功能**:
  - 画布尺寸管理
  - 虚拟滚动支持
  - 缩放级别控制
  - 坐标缓存机制

#### 4.2 坐标转换函数
```typescript
// 关键转换函数
mapTimeToXLocal(time: number): number     // 时间 → X坐标
mapIntensityToYLocal(intensity: number): number  // 强度 → Y坐标
```

#### 4.3 绘制编排
- **编排器**: `useWaveformDrawingOrchestrator`
- **主绘制**: `drawWaveformMain()`
- **轻量绘制**: `drawWaveformLightweight()`

### 5. 具体绘制实现

#### 5.1 绘制顺序
1. **清除画布**: `ctx.clearRect(0, 0, width, height)`
2. **绘制网格**: `drawFullGrid(ctx)` / `drawGrid(ctx)`
3. **绘制音频波形**: `drawAudioWaveform(ctx)` (如果有音频数据)
4. **绘制触觉事件**: `drawEventsWithDragOptimization(ctx, events)`
5. **绘制辅助线**: `drawGuideLines(ctx, isDragging)`

#### 5.2 事件绘制细节

##### Transient事件
- 绘制为垂直线条
- 位置: `mapTimeToXLocal(event.startTime)`
- 高度: `mapIntensityToYLocal(event.intensity)`
- 颜色: 根据频率映射

##### Continuous事件
- 绘制为连接的曲线
- 处理曲线点数组
- 使用坐标缓存优化性能
- 绘制控制点和连接线

#### 5.3 音频波形绘制
- **数据结构**: `AudioAmplitudeData`
- **路径生成**: `createAudioWaveformPath()`
- **缓存机制**: Path2D对象缓存
- **优化**: 无声区域过滤

## 性能优化策略

### 1. 缓存机制
- **坐标缓存**: 避免重复的坐标计算
- **事件绘制缓存**: 缓存连续事件的坐标点
- **音频波形缓存**: 缓存Path2D路径对象
- **文件缓存**: 内存中缓存已加载的文件数据

### 2. 虚拟滚动
- 只绘制当前视口内的可见事件
- 使用 `virtualScrollOffset` 管理滚动偏移
- 事件可见性检测避免不必要的绘制

### 3. 分层绘制
- **主绘制**: 完整的高质量绘制
- **轻量绘制**: 简化的实时绘制（用于拖拽等交互）
- 根据场景选择合适的绘制策略

### 4. 异步操作管理
- 使用 `useAsyncOperationManager` 管理异步操作
- 避免竞态条件和重复操作

## 关键文件和组件

| 文件/组件 | 职责 | 关键函数 |
|-----------|------|----------|
| `HapticProjectEditor.vue` | 文件加载和项目管理 | `readFileContentFromBackend` |
| `types/haptic-file.ts` | 数据类型定义和解析 | `parseRealityTap` |
| `utils/haptic-event-processor.ts` | 数据转换和处理 | `flattenRealityTapEffect` |
| `InteractiveWaveformCanvas.vue` | Canvas组件主体 | 组件协调 |
| `useWaveformCanvas.ts` | 画布管理 | 坐标系统管理 |
| `useWaveformDrawingOrchestrator.ts` | 绘制编排 | `drawWaveformMain` |
| `useWaveformEventDrawing.ts` | 事件绘制 | `drawTransientEvent`, `drawContinuousEvent` |
| `useAudioWaveform.ts` | 音频波形绘制 | `drawAudioWaveform` |

## 数据转换关键点

1. **JSON → 结构化对象**: 类型验证和格式标准化
2. **原始事件 → 渲染事件**: 时间计算和属性展开
3. **时间/强度值 → 像素坐标**: 坐标系转换和缩放处理
4. **坐标数据 → 视觉元素**: Canvas API调用和样式应用

这个完整的流程展现了从静态文件数据到动态视觉呈现的转换过程，体现了现代前端应用中数据处理和可视化的复杂性。

## 技术实现细节

### 坐标转换算法

#### 时间到X坐标转换
```typescript
function mapTimeToX(
  time: number,
  effectiveDuration: number,
  areaWidth: number,
  paddingLeft: number,
  safeOffset: number,
  virtualOffset: number = 0
): number {
  if (effectiveDuration <= 0) return paddingLeft + safeOffset;

  // 计算逻辑X坐标（基于逻辑宽度）
  const logicalX = (time / effectiveDuration) * areaWidth;

  // 应用虚拟偏移量，转换为物理坐标
  const physicalX = logicalX - virtualOffset + safeOffset;

  return physicalX;
}
```

#### 强度到Y坐标转换
```typescript
function mapIntensityToY(
  intensity: number,
  areaHeight: number,
  paddingTop: number,
  canvasHeight: number,
  paddingBottom: number
): number {
  const y = paddingTop + areaHeight - (intensity / 100) * areaHeight;
  return Math.max(paddingTop, Math.min(y, canvasHeight - paddingBottom));
}
```

### 事件绘制算法

#### Transient事件绘制
```typescript
function drawTransientEvent(ctx: CanvasRenderingContext2D, event: RenderableTransientEvent) {
  const x = mapTimeToXLocal(event.startTime);
  const baseY = mapIntensityToYLocal(0);
  const intensityY = mapIntensityToYLocal(event.intensity);

  // 设置样式
  ctx.strokeStyle = mapFrequencyToColor(event.frequency, isSelected);
  ctx.lineWidth = isSelected ? SELECTED_LINE_WIDTH : DEFAULT_LINE_WIDTH;

  // 绘制垂直线
  ctx.beginPath();
  ctx.moveTo(x, baseY);
  ctx.lineTo(x, intensityY);
  ctx.stroke();

  // 绘制顶部圆点
  ctx.fillStyle = ctx.strokeStyle;
  ctx.beginPath();
  ctx.arc(x, intensityY, pointRadius, 0, 2 * Math.PI);
  ctx.fill();
}
```

#### Continuous事件绘制
```typescript
function drawContinuousEvent(ctx: CanvasRenderingContext2D, event: RenderableContinuousEvent) {
  const curveCount = event.curves.length;
  if (curveCount < 2) return;

  // 检查缓存
  const cached = getCachedCoordinates(event.id);
  let points: {x: number, y: number, color: string}[];

  if (cached && isCacheValid(cached, event)) {
    points = cached.points;
  } else {
    // 重新计算坐标
    points = event.curves.map(curve => ({
      x: mapTimeToXLocal(event.startTime + curve.timeOffset),
      y: mapIntensityToYLocal(curve.drawIntensity),
      color: mapFrequencyToColor(curve.curveFrequency, isSelected)
    }));

    // 更新缓存
    updateCache(event.id, points, event);
  }

  // 绘制连接线
  ctx.beginPath();
  ctx.strokeStyle = points[0].color;
  ctx.lineWidth = isSelected ? SELECTED_LINE_WIDTH : DEFAULT_LINE_WIDTH;

  points.forEach((point, index) => {
    if (index === 0) {
      ctx.moveTo(point.x, point.y);
    } else {
      ctx.lineTo(point.x, point.y);
    }
  });
  ctx.stroke();

  // 绘制控制点
  points.forEach(point => {
    ctx.fillStyle = point.color;
    ctx.beginPath();
    ctx.arc(point.x, point.y, pointRadius, 0, 2 * Math.PI);
    ctx.fill();
  });
}
```

### 音频波形绘制算法

#### 波形路径生成
```typescript
function createAudioWaveformPath(audioData: AudioAmplitudeData): Path2D {
  const path = new Path2D();
  const samples = audioData.samples;
  const sampleCount = samples.length;

  if (sampleCount === 0) return path;

  const graphWidth = getGraphAreaWidth();
  const graphHeight = getGraphAreaHeight();
  const centerY = graphHeight / 2;

  // 应用振幅配置
  const { amplitudeScale, amplitudeBoost } = getAmplitudeConfig();
  const maxHeight = graphHeight * amplitudeScale * 0.5;

  // 无声区域过滤
  const silenceThreshold = audioData.max_amplitude * (silenceThresholdPercent / 100);

  let firstPoint = true;

  for (let i = 0; i < sampleCount; i++) {
    const sample = samples[i];

    // 跳过无声区域
    if (enableSilenceFiltering && Math.abs(sample) < silenceThreshold) {
      continue;
    }

    const x = (i / sampleCount) * graphWidth;
    const normalizedAmplitude = sample / audioData.max_amplitude;
    const y = centerY - (normalizedAmplitude * maxHeight * amplitudeBoost);

    if (firstPoint) {
      path.moveTo(x, y);
      firstPoint = false;
    } else {
      path.lineTo(x, y);
    }
  }

  return path;
}
```

### 缓存机制实现

#### 坐标缓存策略
```typescript
interface CoordinateCache {
  [eventId: string]: {
    points: {x: number, y: number, color: string}[];
    lastStartTime: number;
    lastCurveHash: string;
    lastSelected: boolean;
    lastVirtualOffset: number;
    lastZoomLevel: number;
    lastUsed: number;
  }
}

function isCacheValid(cached: CacheItem, event: RenderableContinuousEvent): boolean {
  const now = performance.now();
  const isExpired = (now - cached.lastUsed) > CACHE_EXPIRY_TIME;

  return !isExpired &&
         cached.lastStartTime === event.startTime &&
         cached.lastCurveHash === calculateCurveHash(event.curves) &&
         cached.lastSelected === isEventSelected(event.id) &&
         Math.abs(cached.lastVirtualOffset - virtualScrollOffset.value) < 1 &&
         Math.abs(cached.lastZoomLevel - currentZoomLevel.value) < 0.01;
}
```

## 性能分析和监控

### 绘制性能统计
```typescript
interface PerformanceStats {
  drawTime: number;           // 绘制耗时
  visibleEventCount: number;  // 可见事件数量
  cacheHitRate: number;       // 缓存命中率
  frameRate: number;          // 帧率
}

function updatePerformanceStats(drawTime: number, visibleEventCount: number) {
  const stats = {
    drawTime,
    visibleEventCount,
    cacheHitRate: calculateCacheHitRate(),
    frameRate: 1000 / drawTime
  };

  // 性能警告
  if (drawTime > 16.67) { // 超过60fps阈值
    console.warn('绘制性能警告:', stats);
  }
}
```

### 内存管理
- 定期清理过期的坐标缓存
- 限制缓存项数量，使用LRU策略
- 监控内存使用情况，防止内存泄漏

## 错误处理和容错机制

### 文件加载错误处理
```typescript
try {
  const fileContent = await readFileContentFromBackend(fullPath);
  const effectData = parseRealityTap(fileContent);
  // 处理成功
} catch (error) {
  if (error instanceof SyntaxError) {
    // JSON格式错误
    showError('文件格式错误，请检查JSON语法');
  } else if (error instanceof ZodError) {
    // 数据验证错误
    showError('文件数据格式不符合规范');
  } else {
    // 其他错误
    showError('文件加载失败');
  }
}
```

### Canvas绘制错误处理
```typescript
function safeDrawWaveform() {
  try {
    if (!canvasCtx.value || !waveformCanvas.value) {
      console.warn('Canvas未初始化');
      return;
    }

    drawWaveformMain();
  } catch (error) {
    console.error('绘制错误:', error);
    // 降级到简化绘制
    drawWaveformLightweight();
  }
}
```

## 扩展性设计

### 插件化绘制系统
- 支持自定义事件绘制器
- 可扩展的坐标转换系统
- 模块化的缓存策略

### 多格式支持
- 抽象的数据解析接口
- 可插拔的文件格式解析器
- 统一的内部数据表示

这个技术实现展现了一个高性能、可扩展的数据可视化系统的设计思路。

## 多文件数据存储和管理机制

### 概述

realitytap-desktop采用了**文件级别状态隔离**的架构设计，确保多个.he文件的数据完全独立存储和管理，每个Canvas实例都拥有独立的数据空间。

### 多文件数据存储架构图

<svg width="1000" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowBlue" markerWidth="10" markerHeight="7"
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#1976d2" />
    </marker>
    <marker id="arrowGreen" markerWidth="10" markerHeight="7"
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#388e3c" />
    </marker>
    <marker id="arrowRed" markerWidth="10" markerHeight="7"
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#d32f2f" />
    </marker>
  </defs>

  <!-- 背景分层 -->
  <rect x="20" y="20" width="960" height="120" rx="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="1" opacity="0.3"/>
  <text x="30" y="40" font-family="Arial" font-size="14" font-weight="bold" fill="#1976d2">项目级别存储 Project Level Storage</text>

  <rect x="20" y="160" width="960" height="180" rx="10" fill="#e8f5e8" stroke="#388e3c" stroke-width="1" opacity="0.3"/>
  <text x="30" y="180" font-family="Arial" font-size="14" font-weight="bold" fill="#388e3c">文件级别存储 File Level Storage</text>

  <rect x="20" y="360" width="960" height="180" rx="10" fill="#fff3e0" stroke="#f57c00" stroke-width="1" opacity="0.3"/>
  <text x="30" y="380" font-family="Arial" font-size="14" font-weight="bold" fill="#f57c00">Canvas级别存储 Canvas Level Storage</text>

  <!-- 项目级别组件 -->
  <rect x="50" y="60" width="150" height="60" rx="5" fill="#bbdefb" stroke="#1976d2" stroke-width="2"/>
  <text x="125" y="80" text-anchor="middle" font-family="Arial" font-size="10">ProjectStore</text>
  <text x="125" y="95" text-anchor="middle" font-family="Arial" font-size="9">项目元数据管理</text>
  <text x="125" y="110" text-anchor="middle" font-family="Arial" font-size="8">selectedFileUuid</text>

  <rect x="230" y="60" width="150" height="60" rx="5" fill="#c8e6c9" stroke="#388e3c" stroke-width="2"/>
  <text x="305" y="80" text-anchor="middle" font-family="Arial" font-size="10">FileCaches</text>
  <text x="305" y="95" text-anchor="middle" font-family="Arial" font-size="9">Map&lt;fileUuid, Cache&gt;</text>
  <text x="305" y="110" text-anchor="middle" font-family="Arial" font-size="8">文件缓存管理</text>

  <rect x="410" y="60" width="150" height="60" rx="5" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2"/>
  <text x="485" y="80" text-anchor="middle" font-family="Arial" font-size="10">UnsavedFiles</text>
  <text x="485" y="95" text-anchor="middle" font-family="Arial" font-size="9">Set&lt;fileUuid&gt;</text>
  <text x="485" y="110" text-anchor="middle" font-family="Arial" font-size="8">未保存状态</text>

  <!-- 文件级别组件 -->
  <rect x="50" y="200" width="180" height="80" rx="5" fill="#a5d6a7" stroke="#4caf50" stroke-width="2"/>
  <text x="140" y="220" text-anchor="middle" font-family="Arial" font-size="10">FileEditorStateManager</text>
  <text x="140" y="235" text-anchor="middle" font-family="Arial" font-size="9">Map&lt;fileUuid, FileState&gt;</text>
  <text x="140" y="250" text-anchor="middle" font-family="Arial" font-size="8">• events: RenderableEvent[]</text>
  <text x="140" y="265" text-anchor="middle" font-family="Arial" font-size="8">• UI状态 • 编辑历史</text>

  <rect x="260" y="200" width="180" height="80" rx="5" fill="#b39ddb" stroke="#673ab7" stroke-width="2"/>
  <text x="350" y="220" text-anchor="middle" font-family="Arial" font-size="10">FileStoreInstances</text>
  <text x="350" y="235" text-anchor="middle" font-family="Arial" font-size="9">Map&lt;fileUuid, Store&gt;</text>
  <text x="350" y="250" text-anchor="middle" font-family="Arial" font-size="8">useFileWaveformEditor</text>
  <text x="350" y="265" text-anchor="middle" font-family="Arial" font-size="8">Store(fileUuid)</text>

  <rect x="470" y="200" width="180" height="80" rx="5" fill="#ffab91" stroke="#ff5722" stroke-width="2"/>
  <text x="560" y="220" text-anchor="middle" font-family="Arial" font-size="10">TabManager</text>
  <text x="560" y="235" text-anchor="middle" font-family="Arial" font-size="9">EnhancedFileTab[]</text>
  <text x="560" y="250" text-anchor="middle" font-family="Arial" font-size="8">标签页状态管理</text>
  <text x="560" y="265" text-anchor="middle" font-family="Arial" font-size="8">LRU缓存策略</text>

  <rect x="680" y="200" width="180" height="80" rx="5" fill="#f8bbd9" stroke="#e91e63" stroke-width="2"/>
  <text x="770" y="220" text-anchor="middle" font-family="Arial" font-size="10">FileStateSync</text>
  <text x="770" y="235" text-anchor="middle" font-family="Arial" font-size="9">状态同步管理</text>
  <text x="770" y="250" text-anchor="middle" font-family="Arial" font-size="8">WaveformEditor ↔</text>
  <text x="770" y="265" text-anchor="middle" font-family="Arial" font-size="8">EventAdjustPanel</text>

  <!-- Canvas级别组件 -->
  <rect x="50" y="400" width="160" height="80" rx="5" fill="#fff9c4" stroke="#f57f17" stroke-width="2"/>
  <text x="130" y="420" text-anchor="middle" font-family="Arial" font-size="10">InteractiveWaveform</text>
  <text x="130" y="435" text-anchor="middle" font-family="Arial" font-size="10">Canvas.vue</text>
  <text x="130" y="450" text-anchor="middle" font-family="Arial" font-size="8">fileUuid: "file-1"</text>
  <text x="130" y="465" text-anchor="middle" font-family="Arial" font-size="8">独立Canvas实例</text>

  <rect x="240" y="400" width="160" height="80" rx="5" fill="#fff9c4" stroke="#f57f17" stroke-width="2"/>
  <text x="320" y="420" text-anchor="middle" font-family="Arial" font-size="10">InteractiveWaveform</text>
  <text x="320" y="435" text-anchor="middle" font-family="Arial" font-size="10">Canvas.vue</text>
  <text x="320" y="450" text-anchor="middle" font-family="Arial" font-size="8">fileUuid: "file-2"</text>
  <text x="320" y="465" text-anchor="middle" font-family="Arial" font-size="8">独立Canvas实例</text>

  <rect x="430" y="400" width="160" height="80" rx="5" fill="#fff9c4" stroke="#f57f17" stroke-width="2"/>
  <text x="510" y="420" text-anchor="middle" font-family="Arial" font-size="10">InteractiveWaveform</text>
  <text x="510" y="435" text-anchor="middle" font-family="Arial" font-size="10">Canvas.vue</text>
  <text x="510" y="450" text-anchor="middle" font-family="Arial" font-size="8">fileUuid: "file-3"</text>
  <text x="510" y="465" text-anchor="middle" font-family="Arial" font-size="8">独立Canvas实例</text>

  <!-- 数据隔离示意 -->
  <rect x="620" y="400" width="300" height="120" rx="8" fill="#e8eaf6" stroke="#3f51b5" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="630" y="420" font-family="Arial" font-size="12" font-weight="bold" fill="#3f51b5">数据隔离保证 Data Isolation</text>

  <text x="630" y="440" font-family="Arial" font-size="10" fill="#333">✓ 每个文件独立的Store实例</text>
  <text x="630" y="455" font-family="Arial" font-size="10" fill="#333">✓ 独立的事件数据数组</text>
  <text x="630" y="470" font-family="Arial" font-size="10" fill="#333">✓ 独立的UI状态管理</text>
  <text x="630" y="485" font-family="Arial" font-size="10" fill="#333">✓ 独立的编辑历史栈</text>
  <text x="630" y="500" font-family="Arial" font-size="10" fill="#333">✓ 独立的Canvas坐标系统</text>

  <!-- 连接线 -->
  <!-- 项目级到文件级 -->
  <line x1="125" y1="120" x2="140" y2="190" stroke="#1976d2" stroke-width="2" marker-end="url(#arrowBlue)"/>
  <line x1="305" y1="120" x2="350" y2="190" stroke="#388e3c" stroke-width="2" marker-end="url(#arrowGreen)"/>
  <line x1="485" y1="120" x2="560" y2="190" stroke="#d32f2f" stroke-width="2" marker-end="url(#arrowRed)"/>

  <!-- 文件级到Canvas级 -->
  <line x1="140" y1="280" x2="130" y2="390" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowGreen)"/>
  <line x1="350" y1="280" x2="320" y2="390" stroke="#673ab7" stroke-width="2" marker-end="url(#arrowGreen)"/>
  <line x1="560" y1="280" x2="510" y2="390" stroke="#ff5722" stroke-width="2" marker-end="url(#arrowGreen)"/>

  <!-- 文件UUID标识 -->
  <text x="80" y="520" font-family="Arial" font-size="9" fill="#666">file-uuid-1</text>
  <text x="270" y="520" font-family="Arial" font-size="9" fill="#666">file-uuid-2</text>
  <text x="460" y="520" font-family="Arial" font-size="9" fill="#666">file-uuid-3</text>
</svg>

### 数据存储层次结构

#### 1. 项目级别存储 (Project Level)

**ProjectStore** 负责项目整体的元数据管理：

```typescript
interface ProjectStore {
  currentProject: RealityTapProject | null;
  selectedFileUuid: string | null;           // 当前选中的文件
  fileCaches: Map<string, FileCache>;        // 文件缓存映射
  unsavedFileUuids: Set<string>;            // 未保存文件集合
}

interface FileCache {
  originalData: RealityTapEffect | null;    // 原始磁盘数据
  currentEvents: RenderableEvent[];         // 当前修改的事件数据
  isModified: boolean;                      // 是否已修改
  lastSavedEvents: RenderableEvent[];       // 最后保存时的事件数据
}
```

#### 2. 文件级别存储 (File Level)

每个.he文件都有独立的状态管理：

```typescript
// 文件状态管理器 - 全局单例
class FileEditorStateManager {
  private fileStates = new Map<string, FileEditorState>();

  getFileState(fileUuid: string): FileEditorState;
  setFileState(fileUuid: string, state: Partial<FileEditorState>);
}

// 文件级别的Store实例缓存
const fileStoreInstances = new Map<string, StoreFactory>();

// 获取文件专属的Store实例
function useFileWaveformEditorStore(fileUuid: string) {
  if (!fileStoreInstances.has(fileUuid)) {
    const storeFactory = createFileWaveformEditorStore(fileUuid);
    fileStoreInstances.set(fileUuid, storeFactory);
  }
  return fileStoreInstances.get(fileUuid)!();
}
```

#### 3. Canvas级别存储 (Canvas Level)

每个Canvas组件实例都是完全独立的：

```typescript
// InteractiveWaveformCanvas.vue
const props = defineProps<{
  fileUuid: string;  // 必需的文件UUID，确保数据隔离
  events: RenderableEvent[];
  // ... 其他props
}>();

// 验证并获取文件专属的Store
const validatedFileUuid = validateFileUuid(props.fileUuid);
const waveformStore = useFileWaveformEditorStore(validatedFileUuid);
```

### 数据隔离机制

#### 1. UUID驱动的隔离

每个.he文件都有唯一的UUID，作为数据隔离的关键：

```typescript
// 文件UUID验证
function validateFileUuid(fileUuid: string | null | undefined): string {
  if (!fileUuid || typeof fileUuid !== "string" || fileUuid.trim() === "") {
    throw new Error("无效的fileUuid: 文件级别状态管理需要有效的文件UUID");
  }
  return fileUuid.trim();
}
```

#### 2. Store实例隔离

每个文件都有独立的Pinia Store实例：

```typescript
// 为每个文件创建独立的Store
function createFileWaveformEditorStore(fileUuid: string) {
  return defineStore(`waveformEditor-${fileUuid}`, {
    state: (): FileEditorState => {
      return fileEditorStateManager.getFileState(fileUuid);
    },
    // ... actions和getters
  });
}
```

#### 3. 状态同步隔离

文件间的状态变化不会相互影响：

```typescript
// 文件状态同步 - 只影响指定文件
function useFileStateSync(options: { fileUuid: string | null }) {
  const waveformStore = computed(() => {
    if (!options.fileUuid) return createEmptyStoreInterface();

    const validatedFileUuid = validateFileUuid(options.fileUuid);
    return useFileWaveformEditorStore(validatedFileUuid);
  });
}
```

### Canvas数据独立性保证

#### 1. 组件级别隔离

每个Canvas组件都通过fileUuid获取独立的数据：

```typescript
// 每个Canvas实例都是独立的
<InteractiveWaveformCanvas
  :fileUuid="file1.uuid"
  :events="file1Events"
/>
<InteractiveWaveformCanvas
  :fileUuid="file2.uuid"
  :events="file2Events"
/>
```

#### 2. 坐标系统隔离

每个Canvas都有独立的坐标转换和缓存：

```typescript
// useWaveformCanvas - 每个实例独立
const {
  canvasWidth,
  canvasHeight,
  scrollLeftValue,
  virtualScrollOffset,
  // ... 独立的坐标状态
} = useWaveformCanvas(props, canvasConfig, currentZoomLevel);
```

#### 3. 绘制缓存隔离

每个Canvas的绘制缓存完全独立：

```typescript
// 事件绘制缓存 - 按Canvas实例隔离
const eventDrawingCache = new Map<string, CacheItem>(); // 每个Canvas实例独立
const audioWaveformCache = ref<AudioWaveformCacheItem | null>(null); // 每个实例独立
```

### 内存管理和清理

#### 1. 文件关闭时的清理

```typescript
// 清理文件Store实例
function clearFileStoreInstance(fileUuid: string) {
  fileStoreInstances.delete(fileUuid);
  fileEditorStateManager.removeFileState(fileUuid);
}

// 项目关闭时清理所有缓存
function clearAllFileCaches() {
  fileCaches.value.clear();
  fileStoreInstances.clear();
  fileEditorStateManager.clearAllStates();
}
```

#### 2. LRU缓存策略

标签页管理器实现LRU策略，自动清理不活跃的文件状态：

```typescript
interface TabManagerConfig {
  maxTabs?: number;        // 最大标签页数量
  enableLRU?: boolean;     // 启用LRU缓存策略
}
```

### 数据一致性保证

#### 1. 状态同步机制

```typescript
// 文件状态变化时同步到多个存储层
fileEditorStateManager.setFileState(fileUuid, {
  events: [...store.events],
  selectedEventId: store.selectedEventId,
  // ... 其他状态
});

// 同步到项目缓存
projectStore.updateFileCacheEvents(fileUuid, store.events);
```

#### 2. 保存状态管理

```typescript
// 保存时更新所有相关状态
async function saveFileEvents(fileUuid: string, events: RenderableEvent[]) {
  // 1. 保存到磁盘
  await saveEventsToHeFile(projectDirPath, file.path, events);

  // 2. 更新项目状态
  projectStore.markFileAsSaved(fileUuid);

  // 3. 更新文件缓存
  const cache = fileCaches.value.get(fileUuid);
  if (cache) {
    cache.isModified = false;
    cache.lastSavedEvents = [...events];
  }
}
```

### 总结

realitytap-desktop的多文件数据管理采用了**三层隔离架构**：

1. **项目级别**：管理文件元数据和缓存策略
2. **文件级别**：每个文件独立的状态管理和Store实例
3. **Canvas级别**：每个Canvas组件完全独立的数据和绘制状态

这种架构确保了：
- ✅ **完全的数据隔离**：文件间数据不会相互影响
- ✅ **独立的Canvas实例**：每个Canvas都有独立的坐标系统和缓存
- ✅ **高效的内存管理**：LRU策略和及时清理机制
- ✅ **状态一致性**：多层状态同步保证数据一致性
- ✅ **可扩展性**：支持任意数量的文件同时打开
