# RealityTap OTA Server Quick Start Script (PowerShell)
# This script provides a simple way to get started with the OTA server on Windows

param(
    [switch]$Stop,
    [switch]$Restart,
    [switch]$Logs,
    [switch]$Status,
    [switch]$Clean,
    [switch]$Help
)

# Script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$DockerDir = Join-Path $ProjectRoot "docker"

# Logging functions
function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param($Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Help function
function Show-Help {
    Write-Host @"
RealityTap OTA Server Quick Start Script (PowerShell)

This script helps you quickly deploy the OTA server for development and testing.

Usage: .\quick-start.ps1 [OPTIONS]

Options:
    -Stop           Stop the running services
    -Restart        Restart the services
    -Logs           Show service logs
    -Status         Show service status
    -Clean          Clean up containers and volumes
    -Help           Show this help message

Examples:
    .\quick-start.ps1              # Start the OTA server
    .\quick-start.ps1 -Stop        # Stop the OTA server
    .\quick-start.ps1 -Logs        # View logs
    .\quick-start.ps1 -Clean       # Clean up everything

Default Configuration:
    - Admin Username: admin
    - Admin Password: admin123
    - Port: 3000
    - Mode: HTTP (development)
    
SECURITY WARNING: 
Change the default password before using in production!

"@ -ForegroundColor Cyan
}

# Check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Docker
    try {
        $null = docker --version
        Write-Info "Docker found"
    }
    catch {
        Write-Error "Docker is not installed or not in PATH. Please install Docker Desktop."
        exit 1
    }
    
    # Check Docker Compose
    try {
        $null = docker-compose --version
        Write-Info "Docker Compose found"
    }
    catch {
        try {
            $null = docker compose version
            Write-Info "Docker Compose (v2) found"
        }
        catch {
            Write-Error "Docker Compose is not available. Please install Docker Compose."
            exit 1
        }
    }
    
    # Check if Docker is running
    try {
        $null = docker info 2>$null
        Write-Info "Docker daemon is running"
    }
    catch {
        Write-Error "Docker daemon is not running. Please start Docker Desktop."
        exit 1
    }
    
    Write-Info "Prerequisites check passed"
}

# Setup directories
function Initialize-Directories {
    Write-Info "Setting up directories..."
    
    Set-Location $DockerDir
    
    # Create data directories
    $directories = @(
        "data\storage\database",
        "data\storage\releases\stable",
        "data\storage\releases\beta",
        "data\storage\releases\alpha",
        "data\storage\metadata",
        "data\storage\temp",
        "data\storage\backup",
        "data\logs"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Info "Directories created successfully"
}

# Start services
function Start-Services {
    Write-Info "Starting RealityTap OTA Server..."
    
    Set-Location $DockerDir
    
    # Use the simple compose file
    docker-compose -f docker-compose.simple.yml up -d --build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "🎉 RealityTap OTA Server is now running!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📱 Access the application:" -ForegroundColor Cyan
        Write-Host "   Web Interface: http://localhost:3000" -ForegroundColor White
        Write-Host "   Admin Panel:   http://localhost:3000/admin" -ForegroundColor White
        Write-Host ""
        Write-Host "🔐 Default Admin Credentials:" -ForegroundColor Cyan
        Write-Host "   Username: admin" -ForegroundColor White
        Write-Host "   Password: admin123" -ForegroundColor White
        Write-Host ""
        Write-Warn "⚠️  SECURITY WARNING: Change the default password before production use!"
        Write-Host ""
        Write-Host "📋 Useful Commands:" -ForegroundColor Cyan
        Write-Host "   View logs:    .\quick-start.ps1 -Logs" -ForegroundColor White
        Write-Host "   Stop server:  .\quick-start.ps1 -Stop" -ForegroundColor White
        Write-Host "   Restart:      .\quick-start.ps1 -Restart" -ForegroundColor White
        Write-Host "   Status:       .\quick-start.ps1 -Status" -ForegroundColor White
    }
    else {
        Write-Error "Failed to start services. Check the logs for details."
    }
}

# Stop services
function Stop-Services {
    Write-Info "Stopping RealityTap OTA Server..."
    
    Set-Location $DockerDir
    docker-compose -f docker-compose.simple.yml down
    
    if ($LASTEXITCODE -eq 0) {
        Write-Info "Services stopped successfully!"
    }
    else {
        Write-Error "Failed to stop services."
    }
}

# Restart services
function Restart-Services {
    Write-Info "Restarting RealityTap OTA Server..."
    
    Set-Location $DockerDir
    docker-compose -f docker-compose.simple.yml restart
    
    if ($LASTEXITCODE -eq 0) {
        Write-Info "Services restarted successfully!"
    }
    else {
        Write-Error "Failed to restart services."
    }
}

# Show logs
function Show-Logs {
    Write-Info "Showing service logs (Press Ctrl+C to exit)..."
    
    Set-Location $DockerDir
    docker-compose -f docker-compose.simple.yml logs -f
}

# Show status
function Show-Status {
    Write-Info "Service Status:"
    
    Set-Location $DockerDir
    docker-compose -f docker-compose.simple.yml ps
    
    Write-Host ""
    Write-Info "Container Health:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=realitytap"
}

# Clean up
function Remove-Everything {
    Write-Warn "This will remove all containers, volumes, and data. Are you sure?"
    $confirmation = Read-Host "Type 'yes' to confirm"
    
    if ($confirmation -eq "yes") {
        Write-Info "Cleaning up..."
        
        Set-Location $DockerDir
        
        # Stop and remove containers
        docker-compose -f docker-compose.simple.yml down -v
        
        # Remove images
        try {
            docker rmi realitytap-ota-server_realitytap-ota-server 2>$null
        }
        catch {
            # Ignore errors if image doesn't exist
        }
        
        # Remove data directories
        if (Test-Path "data") {
            Remove-Item -Path "data" -Recurse -Force
        }
        
        Write-Info "Cleanup completed!"
    }
    else {
        Write-Info "Cleanup cancelled"
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Test-Prerequisites

if ($Stop) {
    Stop-Services
}
elseif ($Restart) {
    Restart-Services
}
elseif ($Logs) {
    Show-Logs
}
elseif ($Status) {
    Show-Status
}
elseif ($Clean) {
    Remove-Everything
}
else {
    # Default action: start services
    Initialize-Directories
    Start-Services
}
