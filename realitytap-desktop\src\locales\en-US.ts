/**
 * English Language Pack
 */

export default {
  // === common ===
  common: {
    confirm: "Confirm",
    cancel: "Cancel",
    cancelled: "Operation cancelled",
    save: "Save",
    delete: "Delete",
    close: "Close",
    loading: "Loading...",
    error: "Error",
    clear: "Clear",
    about: "About",
    retry: "Retry",
    dismiss: "Dismiss",
    enabled: "Enabled",
    disabled: "Disabled"
  },
  // === librtcore ===
  librtcore: {
    error: {
      general: "librtcore error: {message}",
      retrying: "librtcore initialization failed, retrying: {message}",
      manualRetryRequired: "librtcore initialization failed, manual retry required: {message}",
      fallbackMode: "librtcore entered fallback mode, some features may be unavailable",
      fallbackModeActive: "Currently in fallback mode, haptic playback features are limited",
      fallbackModeExited: "Exited fallback mode, features restored to normal",
      resetting: "Resetting librtcore system...",
      retrySuccess: "Retry successful, librtcore restored to normal",
      retryFailed: "Retry failed, please check device connection or configuration",
      retryError: "Error occurred during retry process"
    }
  },
  // === app ===
  app: {
    title: "RealityTap Haptics Studio"
  },
  // === dashboard ===
  dashboard: {
    // === tabs ===
    tabs: {
      projects: "Projects",
      learning: "Learning"
    },
    // === projects ===
    projects: {
      newProject: "New project",
      newProjectSubtitle: "from audio or haptic files",
      openProject: "Open project",
      openProjectSubtitle: "from your local files",
      recentProjects: "Recent Projects",
      exampleProjects: "Example Projects",
      noRecentProjects: "No recent project records",
      goToLearning: "Go to Learning Page"
    }
  },
  // === project ===
  project: {
    // === create ===
    create: {
      success: "Project created successfully",
      failed: "Failed to create project"
    },
    // === open ===
    open: {
      failed: "Failed to open project",
      notFound: "Project not found"
    },
    // === save ===
    save: {
      saving: "Saving...",
      success: "File saved successfully",
      noData: "No event data to save",
      noFileSelected: "No file selected",
      saveFile: "Save File (Ctrl+S)",
      noChanges: "File unchanged or already saved"
    },
    // === undo ===
    undo: {
      undoAction: "Undo (Ctrl+Z)",
      noUndoAvailable: "No undo operations available",
      noFileSelected: "No file selected"
    },
    // === redo ===
    redo: {
      redoAction: "Redo (Ctrl+Y)",
      noRedoAvailable: "No redo operations available",
      noFileSelected: "No file selected"
    }
  },
  // === editor ===
  editor: {
    // === navigation ===
    navigation: {
      projects: "Projects",
      doubleClickToEdit: "Double click to edit",
      unsavedChanges: "Unsaved changes"
    },
    // === eventProperties ===
    eventProperties: {
      title: "Event Properties",
      transientEvent: "Transient Event",
      continuousEvent: "Continuous Event",
      startTime: "Start Time",
      intensity: "Intensity",
      frequency: "Frequency",
      duration: "Duration",
      globalIntensity: "Intensity (Global)",
      globalFrequency: "Frequency (Global)",
      selectedCurvePoint: "Selected Curve Point",
      pointNumber: "Point #",
      time: "Time",
      pointRelativeIntensity: "Point Relative Intensity",
      pointRelativeFrequency: "Point Relative Frequency",
      computedAbsoluteIntensity: "Computed Absolute Intensity",
      computedAbsoluteFrequency: "Computed Absolute Frequency",
      firstLastPointZeroIntensity: "First and last points always have zero intensity",
      frequencyAdjustmentHint: "Tip: Hold {key} key and drag vertically to adjust frequency",
      selectEventToAdjust: "Select an event on the timeline to adjust its properties."
    },
    // === duration ===
    duration: {
      increased: "Duration increased by {duration} milliseconds"
    },
    // === durationPanel ===
    durationPanel: {
      increaseDuration: "Increase Duration",
      milliseconds: "ms",
      confirm: "Confirm",
      hideAudio: "Hide Audio",
      showAudio: "Show Audio",
      loadingAudio: "Loading Audio..."
    },
    // === empty ===
    empty: {
      title: "Add a new haptic file",
      description: "Drag and drop a single audio file or a folder of assets to start designing your haptics",
      addHapticFile: "Add haptic file",
      addAudioFile: "Add audio file",
      addVideoFile: "Add video file"
    },
    // === waveform ===
    waveform: {
      noFileSelected: "Please open a RealityTap Haptics waveform file first to edit",
      loading: "Loading RealityTap Haptic Effect..."
    },
    // === project ===
    project: {
      untitled: "Untitled Project",
      renameSuccess: "Project name changed and directory renamed",
      renameFailed: "Project rename failed",
      noProjectLoaded: "No project is currently loaded. Please create or open a project."
    },
    // === file ===
    file: {
      confirmDelete: "Confirm Delete",
      confirmDeleteMessage: "Are you sure you want to delete file \"{name}\"? This action cannot be undone.",
      createSuccess: "Haptic file created successfully",
      deleteSuccess: "File \"{name}\" deleted successfully",
      deleteFailed: "Failed to delete file \"{name}\": {error}",
      selectFirst: "Please select a file first",
      noFileSelected: "No file selected",
      noEventData: "No event data to save",
      saveSuccess: "File saved successfully"
    },
    // === audio ===
    audio: {
      importingToRoot: "Importing audio file to root directory...",
      importingToGroup: "Importing audio file to group \"{name}\"...",
      importSuccess: "Audio imported and .he file generated successfully",
      importFailed: "Audio import failed: {error}",
      metadataFailed: "Audio metadata parsing failed, imported but unable to get duration",
      amplitudeLoadFailed: "Audio amplitude data loading failed: {error}",
      processingFailed: "Audio processing failed: {error}",
      loadSuccess: "Audio data loaded successfully",
      loadFailed: "Audio data loading failed",
      noAudioFile: "Current file has no associated audio file",
      checkFailed: "Audio data check failed"
    },
    // === video ===
    video: {
      importingToRoot: "Importing video file to root directory...",
      importingToGroup: "Importing video file to group \"{name}\"...",
      importSuccess: "Video imported and .he file generated successfully",
      metadataFailed: "Video metadata parsing failed, imported but unable to get duration"
    },
    // === contextMenu ===
    contextMenu: {
      playEffect: "Play Effect",
      renameFile: "Rename File",
      deleteFile: "Delete Haptic File",
      renameGroup: "Rename Current Group",
      deleteGroup: "Delete Current Group",
      newHapticFile: "New Haptic File",
      importAudioFile: "Import Audio File",
      importVideoFile: "Import Video File",
      newRootGroup: "New Root Group",
      addFileToGroup: "Add File to Group \"{name}\"",
      importAudioToGroup: "Import Audio to Group \"{name}\"",
      importVideoToGroup: "Import Video to Group \"{name}\"",
      newChildGroup: "New Child Group under \"{name}\"",
      addEvent: "Add Event",
      transientEvent: "Transient Event",
      continuousEvent: "Continuous Event",
      deleteEvent: "Delete Event",
      needSpace: "Need at least {space}ms space",
      availableSpace: "Available space: {space}ms"
    },
    // === event ===
    event: {
      exceedsAudioDuration: "Event exceeds audio duration, cannot add/edit!"
    },
    // === hapticFiles ===
    hapticFiles: {
      title: "Haptic Files"
    },
    // === inlineEdit ===
    inlineEdit: {
      groupCreateSuccess: "Group \"{name}\" created successfully",
      groupCreateFailed: "Failed to create group: {error}",
      groupNameEmpty: "Group name cannot be empty",
      groupNamePlaceholder: "Enter group name",
      newGroupPlaceholder: "Enter new group name",
      newFileNamePlaceholder: "Enter new file name",
      groupRenameSuccess: "Group renamed from \"{oldName}\" to \"{newName}\"",
      groupRenameFailed: "Failed to rename group: {error}",
      fileNameEmpty: "File name cannot be empty",
      fileNameMustEndWithHe: "File name must end with .he",
      fileRenameSuccess: "File renamed from \"{oldName}\" to \"{newName}\"",
      fileRenameFailed: "Failed to rename file: {error}"
    },
    // === groupDelete ===
    groupDelete: {
      confirmTitle: "Confirm Delete Group",
      confirmMessage: "Are you sure you want to delete group \"{name}\"?",
      confirmWithContentTitle: "Confirm Delete Group and Contents",
      confirmWithContentMessage: "Group \"{name}\" contains {count} files. Are you sure you want to delete the group and all its contents?",
      confirmDelete: "Delete",
      deleteSuccess: "Group \"{name}\" deleted successfully",
      deleteWithContentSuccess: "Group \"{name}\" and its contents deleted successfully",
      deleteFailed: "Failed to delete group: {error}",
      cancelled: "Delete operation cancelled"
    },
    // === dragAndDrop ===
    dragAndDrop: {
      targetFileNotFound: "Target file not found",
      cannotDropOnSelfOrDescendant: "Cannot drop item on itself or its descendants",
      fileMovedToGroup: "File moved to group \"{groupName}\"",
      fileMovedToRoot: "File moved to root directory",
      fileMoveSuccess: "File moved successfully",
      groupMovedToGroup: "Group moved to group \"{groupName}\"",
      groupMovedToRoot: "Group moved to root directory",
      groupMoveSuccess: "Group moved successfully",
      moveFailed: "Move failed",
      moveToRootFailed: "Failed to move to root directory",
      unknownGroup: "Unknown group"
    }
  },
  // === device ===
  device: {
    // === status ===
    status: {
      connected: "Connected",
      disconnected: "Disconnected",
      connecting: "Connecting",
      disconnecting: "Disconnecting",
      error: "Error",
      unknown: "Unknown",
      noDevices: "No devices",
      title: "Device Status",
      total: "Total devices",
      errorDevices: "Error devices",
      defaultDevice: "Default device",
      clickToOpen: "Click to open device manager",
      default: "Default"
    },
    // === types ===
    types: {
      usb: "USB",
      wifi: "WiFi",
      bluetooth: "Bluetooth"
    },
    // === actions ===
    actions: {
      scan: "Scan Devices",
      refresh: "Refresh",
      connect: "Connect",
      disconnect: "Disconnect",
      setDefault: "Set as Default",
      rename: "Rename",
      remove: "Remove",
      sendFile: "Send File",
      addTestDevice: "Add Test Device",
      unsetDefault: "Unset Default"
    },
    // === messages ===
    messages: {
      scanComplete: "Device scan completed",
      scanFailed: "Device scan failed",
      refreshSuccess: "Device list refreshed",
      refreshFailed: "Refresh failed",
      connectSuccess: "Device connected successfully",
      connectFailed: "Device connection failed",
      disconnectSuccess: "Device disconnected successfully",
      disconnectFailed: "Device disconnection failed",
      setDefaultSuccess: "Default device set successfully",
      setDefaultFailed: "Failed to set default device",
      removeSuccess: "Device removed successfully",
      removeFailed: "Failed to remove device",
      renameSuccess: "Device renamed successfully",
      renameFailed: "Failed to rename device",
      sendFileInfo: "File sending feature is under development...",
      sendFileFailed: "Failed to send file",
      addTestDeviceSuccess: "Test device added successfully",
      addTestDeviceFailed: "Failed to add test device",
      initializeFailed: "Device manager initialization failed"
    },
    // === filter ===
    filter: {
      deviceType: "Device Type",
      connectionStatus: "Connection Status",
      searchDevices: "Search devices..."
    },
    // === details ===
    details: {
      deviceId: "Device ID",
      deviceType: "Device Type",
      connectionStatus: "Connection Status",
      lastConnected: "Last Connected",
      manufacturer: "Manufacturer",
      model: "Model"
    },
    // === rename ===
    rename: {
      title: "Rename Device",
      deviceName: "Device Name",
      placeholder: "Enter new device name",
      nameRequired: "Device name cannot be empty"
    },
    // === remove ===
    remove: {
      confirmTitle: "Confirm Delete",
      confirmMessage: "Are you sure you want to delete device \"{name}\"? This action cannot be undone."
    },
    // === transmission ===
    transmission: {
      heFile: "HE File",
      audioFile: "Audio File",
      projectData: "Project Data",
      deviceConfig: "Device Config",
      priority: {
        low: "Low",
        normal: "Normal",
        high: "High",
        urgent: "Urgent"
      }
    },
    // === testDevice ===
    testDevice: {
      name: "Test Device {number}",
      manufacturer: "Test Manufacturer",
      model: "Test Model"
    },
    // === errors ===
    errors: {
      unknownError: "Unknown error",
      timeoutError: "Connection timeout",
      permissionDenied: "Permission denied",
      deviceNotFound: "Device not found",
      deviceBusy: "Device busy",
      invalidParameter: "Invalid parameter"
    },
    // === management ===
    management: {
      title: "Device Management"
    }
  },
  // === errors ===
  errors: {
    unknown: "Unknown error occurred",
    networkError: "Network error",
    fileNotFound: "File not found",
    operationFailed: "Operation failed"
  },
  // === learning ===
  learning: {
    title: "Haptic Design Resources",
    // === gettingStarted ===
    gettingStarted: {
      title: "Getting Started with Haptic Design",
      description: "Learn the basics of haptic design and how to create effective haptic feedback.",
      // === tags ===
      tags: {
        beginner: "Beginner",
        tutorial: "Tutorial"
      }
    },
    // === audioToHaptics ===
    audioToHaptics: {
      title: "Audio to Haptics Conversion",
      description: "Tips and techniques for converting audio files to meaningful haptic feedback.",
      // === tags ===
      tags: {
        intermediate: "Intermediate",
        tutorial: "Tutorial"
      }
    },
    // === gaming ===
    gaming: {
      title: "Gaming Haptics",
      description: "Best practices for implementing haptic feedback in gaming applications.",
      // === tags ===
      tags: {
        gaming: "Gaming",
        caseStudy: "Case Study"
      }
    },
    // === uxDesign ===
    uxDesign: {
      title: "Haptic UX Design",
      description: "How to enhance user experience using haptic feedback in mobile applications.",
      // === tags ===
      tags: {
        ux: "UX",
        mobile: "Mobile"
      }
    },
    // === advanced ===
    advanced: {
      title: "Advanced Haptic Techniques",
      description: "Advanced techniques for creating complex and nuanced haptic experiences.",
      // === tags ===
      tags: {
        advanced: "Advanced",
        tutorial: "Tutorial"
      }
    },
    // === research ===
    research: {
      title: "Haptic Feedback Research",
      description: "Recent research and studies on the effectiveness of haptic feedback.",
      // === tags ===
      tags: {
        research: "Research",
        academic: "Academic"
      }
    }
  },
  // === examples ===
  examples: {
    // === populationOne ===
    populationOne: {
      haptics: "8 Haptics"
    },
    notImplemented: "Feature not implemented",
    notImplementedMessage: "The \"{title}\" example project is not yet implemented. Coming soon!"
  },
  // === update ===
  update: {
    downloading: "Downloading",
    installing: "Installing",
    updateAvailable: "Update Available",
    newVersionAvailable: "New Version Available",
    downloadNow: "Download now",
    installNow: "Install now",
    remindLater: "Remind later",
    installConfirmTitle: "Confirm Update Installation",
    installConfirmMessage: "Installing the update requires closing the application. It will automatically restart to the new version after installation.",
    installConfirmDetails: "The entire process takes about 30 seconds. Please ensure all work is saved.",
    installConfirmWarning: "Do not manually close the installer during the process.",
    currentVersion: "Current version",
    latestVersion: "Latest version",
    releaseNotes: "Release notes",
    fileSize: "File size",
    confirmInstallation: "Confirm Installation",
    confirmInstall: "Confirm Install",
    installationNotice: "Installation Notice",
    applicationWillClose: "The application will close to complete the installation and will automatically restart after completion.",
    error: "Update Error",
    installingMessage: "Installing update, please wait...",
    readyToInstall: "Ready to Install Update",
    downloadComplete: "Download Complete",
    downloadCompleteMessage: "Update file has been downloaded successfully. Click 'Install Now' to begin installation.",
    cancelling: "Cancelling",
    // === processManagement ===
    processManagement: {
      title: "Process Management Confirmation",
      warningTitle: "Warning",
      warningMessage: "Installing the update requires closing the following related processes. Please ensure you have saved all important work.",
      processListTitle: "Processes to Close",
      closeAndInstall: "Close Processes and Install",
      closeStrategyTitle: "Close Strategy",
      gracefulClose: "Graceful Close",
      gracefulCloseDesc: "Attempt to close processes normally, giving programs time to save data",
      forceClose: "Force Close",
      forceCloseDesc: "Immediately terminate processes, may cause data loss",
      critical: "Critical",
      noticeTitle: "Important Notes",
      notice1: "Please save all important work before closing processes",
      notice2: "Closing critical processes may affect system stability",
      notice3: "The application will restart automatically after installation",
      // === processTypes ===
      processTypes: {
        MainApplication: "Main App",
        EditorWindow: "Editor",
        RenderProcess: "Renderer",
        AudioService: "Audio Service",
        FileMonitor: "File Monitor",
        BackgroundService: "Background Service",
        ChildProcess: "Child Process",
        Unknown: "Unknown Process"
      }
    }
  },
  // === i18nTest ===
  i18nTest: {
    title: "Internationalization Test",
    languageSwitchTest: "Language Switch Test",
    currentLanguage: "Current Language",
    commonTextTest: "Common Text Test",
    errorMessageTest: "Error Message Test",
    languageDetectionInfo: "Language Detection Info",
    exampleError: "Insufficient disk space",
    // === labels ===
    labels: {
      confirm: "Confirm",
      cancel: "Cancel",
      save: "Save",
      delete: "Delete",
      loading: "Loading",
      saveFailedExample: "Save Failed (Example)",
      unknownError: "Unknown Error",
      networkError: "Network Error",
      fileNotFound: "File Not Found"
    }
  },
  // === forceUpdate ===
  forceUpdate: {
    title: "Force Update",
    notice: "Important Update",
    noticeMessage: "This is a mandatory update that must be installed before you can continue using the application.",
    newVersionRequired: "New version required",
    readyToInstall: "Update downloaded, ready to install",
    startDownload: "Start Download",
    installNow: "Install Now",
    retryDownload: "Retry Download",
    exitApplication: "Exit Application"
  },
  // === about ===
  about: {
    title: "About",
    loading: "Loading version information...",
    version: "Version",
    buildInfo: "Build Info",
    platform: "Platform",
    // === updateCheck ===
    updateCheck: {
      title: "Version Check",
      checking: "Checking for updates...",
      checkNow: "Check Now",
      newVersionAvailable: "New Version Available",
      latestVersion: "Latest Version",
      upToDate: "You are using the latest version",
      checkFailed: "Failed to check for updates",
      viewUpdate: "View Update"
    }
  },
  // === installer ===
  installer: {
    ready: "Ready",
    preparing: "Preparing",
    installing: "Installing",
    success: "Installation Successful",
    failed: "Installation Failed",
    cancelled: "Cancelled",
    completed: "Completed",
    // === operations ===
    operations: {
      initializing: "Initializing",
      validating: "Validating package",
      waitingForExit: "Waiting for app to exit",
      backingUp: "Backing up files",
      installing: "Installing",
      restarting: "Restarting application",
      completed: "Installation completed",
      failed: "Installation failed",
      cancelled: "Cancelled"
    },
    initializing: "Initializing installer",
    installSuccess: "Installation completed successfully",
    unknownError: "Unknown error",
    preparingExit: "Preparing to exit application",
    cancelling: "Cancelling",
    installCancelled: "Installation cancelled"
  },
  // === ota ===
  ota: {
    checkingUpdates: "Checking for updates",
    closingProcesses: "Closing processes",
    downloadFailed: "Download failed",
    downloadingUpdate: "Downloading update",
    installationCompleted: "Installation completed",
    installationFailed: "Installation failed",
    installingUpdate: "Installing update",
    noDownloadedFile: "No downloaded file",
    noUpdateInfo: "No update information",
    noUpdatesAvailable: "No updates available",
    preparingInstallation: "Preparing installation",
    verificationFailed: "Verification failed",
    verifyingUpdate: "Verifying update"
  },
  // === debug ===
  debug: {
    info: "Debug Info:",
    isLoading: "Loading Status",
    hasRecentProjects: "Has Recent Projects",
    recentProjectsLength: "Recent Projects Count",
    recentProjectsContent: "Recent Projects Content",
    title: "Debug Tools",
    activated: "Debug mode activated!",
    // === button ===
    button: {
      tooltip: "Debug Tools"
    },
    // === menu ===
    menu: {
      settings: "Debug Settings",
      viewLogs: "View Logs",
      openLogFolder: "Open Log Folder",
      exportDebugInfo: "Export Debug Info"
    },
    // === settings ===
    settings: {
      title: "Debug Settings",
      enableDebug: "Enable Debug Mode",
      debugEnabled: "Debug mode enabled",
      debugDisabled: "Debug mode disabled",
      debugEnabledDesc: "Currently running in development mode, debug features are automatically enabled.",
      debugDisabledDesc: "Currently running in production mode, debug features are automatically disabled.",
      buildMode: "Build Mode",
      currentConfig: "Current Configuration",
      logLevel: "Log Level",
      logOtaOperations: "Log OTA Operations",
      logDeviceOperations: "Log Device Operations",
      viewLogs: "View Logs",
      openLogFolder: "Open Log Folder",
      resetDefault: "Reset to Default",
      saveSuccess: "Debug settings saved successfully",
      resetSuccess: "Reset to default settings",
      // === notice ===
      notice: {
        title: "Important Notes",
        sessionOnly: "Debug settings only take effect in the current session and will revert to defaults after app restart",
        performance: "Enabling verbose logging may affect application performance",
        logLocation: "Log files are saved in the application data directory"
      },
      // === validation ===
      validation: {
        levelRequired: "Please select a log level"
      },
      // === errors ===
      errors: {
        loadFailed: "Failed to load debug configuration",
        saveFailed: "Failed to save debug configuration",
        openFolderFailed: "Failed to open log folder"
      }
    },
    // === logViewer ===
    logViewer: {
      title: "Log Viewer",
      refresh: "Refresh",
      clear: "Clear",
      export: "Export",
      maxLines: "Max Lines",
      autoRefreshOn: "Auto Refresh",
      autoRefreshOff: "Manual Refresh",
      loading: "Loading logs...",
      noLogs: "No log content",
      logPath: "Log Path",
      unknown: "Unknown",
      lines: "Lines",
      size: "Size",
      clearSuccess: "Logs cleared",
      exportSuccess: "Debug info exported to: {path}",
      // === errors ===
      errors: {
        getPathFailed: "Failed to get log file path",
        readFailed: "Failed to read log file",
        clearFailed: "Failed to clear log file",
        exportFailed: "Failed to export debug info"
      }
    },
    // === errors ===
    errors: {
      openFolderFailed: "Failed to open log folder",
      exportFailed: "Failed to export debug info"
    },
    exportSuccess: "Debug info exported to: {path}"
  },
  // === demo ===
  demo: {
    installProcessTitle: "Installation Process",
    step1Title: "User Confirms Installation",
    step1Description: "Display installation confirmation dialog, explaining that the app will close and restart automatically",
    step2Title: "Create Installation Script",
    step2Description: "Generate independent batch script containing installation and restart logic",
    step3Title: "Launch Independent Installation",
    step3Description: "Start independent installer, current app exits",
    step4Title: "Auto Restart Application",
    step4Description: "Automatically start new version after installation completes"
  },
  // === playEffect ===
  playEffect: {
    loading: "Loading file data...",
    dialogTitle: "Play Effect - {fileName}",
    dialogTitleDefault: "Play Effect",
    fileInfo: "File Information",
    fileName: "File Name",
    fileUuid: "File UUID",
    filePath: "File Path",
    lastModified: "Last Modified",
    jsonData: "JSON Data",
    copyJson: "Copy JSON",
    downloadJson: "Download JSON",
    copySuccess: "JSON data copied to clipboard",
    copyFailed: "Copy failed",
    downloadSuccess: "JSON file downloaded successfully",
    downloadFailed: "Download failed",
    errorPlaying: "Play effect failed: {error}",
    notImplemented: "Play effect feature not implemented",

    // New playback control related
    play: "Play",
    stop: "Stop",
    pause: "Pause",
    resume: "Resume",
    save: "Save",
    saveAsRtp: "Save as RTP",
    saveToFile: "Save to File",
    saving: "Saving...",
    saveSuccess: "Save successful",
    saveFailed: "Save failed: {error}",
    selectSaveLocation: "Select save location",
    saveDialogTitle: "Save haptic data file",
    noData: "No data to save",

    // Motor selection related
    motorSelection: "Motor Selection",
    motorModel: "Motor Model",
    selectMotor: "Please select motor model",
    motorLoadFailed: "Failed to load motor configurations, using default",

    // Sampling rate selection related
    samplingRateSelection: "Sampling Rate Selection",
    selectSamplingRate: "Please select sampling rate",
    samplingRate6Khz: "6KHz - Basic Effects",
    samplingRate8Khz: "8KHz - General Choice",
    samplingRate12Khz: "12KHz - Medium Precision",
    samplingRate24Khz: "24KHz - High Precision Effects",

    // Actual frequency selection related
    actualFrequencySelection: "Actual Frequency Selection",
    selectActualFrequency: "Please select actual frequency",

    // Canvas related
    canvas: "Canvas",
    noEvents: "No event data",
    timeAxis: "Time Axis",
    amplitudeAxis: "Amplitude Axis",



    // Control panel
    controls: "Playback Controls",
    settings: "Settings",
    volume: "Volume",
    loop: "Loop Playback",

    // librtcore integration related
    initializingLibrtcore: "Initializing haptic algorithm library...",
    initSuccess: "Haptic algorithm library initialized successfully",
    reinitSuccess: "Haptic algorithm library reinitialized successfully",
    librtcoreError: "Haptic algorithm library error",
    retryInit: "Retry Initialization",
    clearError: "Clear Error"
  }
} as const;
