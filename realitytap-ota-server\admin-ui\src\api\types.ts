// 基础响应类型
export interface BaseResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
  timestamp: string;
  version: string;
}

// 身份验证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface AdminUser {
  id: string;
  username: string;
  role: 'admin';
  loginTime: string;
}

export interface LoginResponse {
  token: string;
  user: AdminUser;
  expiresIn: number;
}

export interface RefreshTokenResponse {
  token: string;
  expiresIn: number;
}

// 系统统计类型
export interface SystemStats {
  system: {
    uptime: number;
    nodeVersion: string;
    platform: string;
    architecture: string;
    pid: number;
    startTime: string;
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
  };
  ota: {
    totalVersions: number;
    totalDownloads: number;
    totalSize: number;
    platformDistribution: Record<string, number>;
    architectureDistribution: Record<string, number>;
    averageFileSize: number;
  };
  storage: {
    basePath: string;
    releasesPath: string;
    metadataPath: string;
    logsPath: string;
  };
}

// 版本信息类型
export interface VersionInfo {
  id: string;
  version: string;
  platform: string;
  architecture: string;
  channel: string;
  filename: string;
  fileSize: number;
  checksum: string;
  uploadTime: string;
  downloadCount: number;
  releaseNotes?: string;
  // 签名相关字段
  hasSignature?: boolean;
  signatureFilename?: string;
  signature?: string;
  // 哈希文件相关字段
  hasHashFile?: boolean;
  hashFilename?: string;
  // 强制更新字段
  isForced?: boolean;
  // UI状态字段
  updating?: boolean;
}

// 文件上传响应
export interface UploadResponse {
  filename: string;
  size: number;
  checksum: string;
  version: string;
  platform: string;
  architecture: string;
}

// 健康检查类型
export interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  checks?: {
    storage: boolean;
    metadata: boolean;
    logs: boolean;
    disk: boolean;
    memory: boolean;
  };
  diagnostics?: {
    storage: {
      allPathsExist: boolean;
      paths: {
        basePath: boolean;
        releasesPath: boolean;
        metadataPath: boolean;
        logsPath: boolean;
      };
      basePath: string;
    };
    disk: {
      available: boolean;
      error?: string;
      lastAccess?: string;
    };
    memory: {
      usagePercentage: number;
      heapUsed: number;
      heapTotal: number;
      rss: number;
    };
    thresholds: {
      memoryLimit: number;
    };
  };
}

// 日志条目类型
export interface LogEntry {
  timestamp: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  meta?: Record<string, any>;
  // IP相关字段
  clientIP?: string;
  isPrivateIP?: boolean;
  anonymizedIP?: string;
  operation?: string;
  userAgent?: string;
  path?: string;
  method?: string;
}

// 日志响应类型
export interface LogsResponse {
  logs: LogEntry[];
  total: number;
  offset: number;
  limit: number;
}
