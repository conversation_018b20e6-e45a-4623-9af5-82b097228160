// Utility commands
use crate::{
    error::Result,
    models,
    project::{self, generate_project_from_directory, refresh_project_directory},
};
use std::path::PathBuf;
use tauri;

#[tauri::command]
pub async fn generate_project_from_directory_command(
    project_dir_path: String,
) -> Result<models::Project> {
    log::info!("根据目录自动生成 RealityTap 项目: {}", project_dir_path);
    let path = PathBuf::from(&project_dir_path);
    let project = generate_project_from_directory(&path)?;
    Ok(project)
}

#[tauri::command]
pub async fn refresh_project_directory_command(
    project_dir_path: String,
) -> Result<models::Project> {
    log::info!("刷新 RealityTap 项目: {}", project_dir_path);
    let path = PathBuf::from(&project_dir_path);
    let project = refresh_project_directory(&path)?;
    Ok(project)
}

#[tauri::command]
pub async fn check_file_exists_command(
    project_dir_path: String,
    relative_path: String,
) -> Result<bool> {
    let abs_path = PathBuf::from(&project_dir_path).join(&relative_path);
    Ok(abs_path.exists())
}

#[tauri::command]
pub async fn move_file_command(
    project_dir_path: String,
    file_uuid_str: String,
    new_group_uuid: Option<String>,
) -> Result<models::Project> {
    log::info!(
        "移动文件: {} 到分组: {:?} in project {}",
        file_uuid_str,
        new_group_uuid,
        project_dir_path
    );
    project::move_file_to_group(&project_dir_path, &file_uuid_str, new_group_uuid.as_deref())
}

#[tauri::command]
pub async fn move_group_command(
    project_dir_path: String,
    group_uuid_str: String,
    new_parent_group_uuid: Option<String>,
) -> Result<models::Project> {
    log::info!(
        "移动分组: {} 到父分组: {:?} in project {}",
        group_uuid_str,
        new_parent_group_uuid,
        project_dir_path
    );
    project::move_group_to_parent(
        &project_dir_path,
        &group_uuid_str,
        new_parent_group_uuid.as_deref(),
    )
}
