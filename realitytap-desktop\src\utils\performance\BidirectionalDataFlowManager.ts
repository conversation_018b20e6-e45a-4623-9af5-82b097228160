/**
 * 双向数据流管理器
 * 协调 Canvas 和属性面板之间的数据交互，避免冲突和重复更新
 */

export type DataSource = 'canvas' | 'panel' | 'external';
export type UpdateType = 'drag' | 'property' | 'selection' | 'creation' | 'deletion';
export type UpdatePriority = 'immediate' | 'high' | 'normal' | 'low';

export interface DataUpdate {
  id: string;
  source: DataSource;
  type: UpdateType;
  priority: UpdatePriority;
  data: any;
  timestamp: number;
  dependencies?: string[]; // 依赖的其他更新ID
  conflictsWith?: string[]; // 与之冲突的更新类型
}

export interface UpdateResult {
  success: boolean;
  appliedUpdates: string[];
  skippedUpdates: string[];
  conflicts: string[];
  processingTime: number;
}

export interface FlowConfig {
  maxQueueSize: number;
  batchSize: number;
  batchTimeout: number;
  enableConflictResolution: boolean;
  enableMetrics: boolean;
}

export interface FlowMetrics {
  totalUpdates: number;
  processedUpdates: number;
  skippedUpdates: number;
  conflictCount: number;
  averageProcessingTime: number;
  queueSize: number;
  batchCount: number;
}

/**
 * 双向数据流管理器
 * 
 * 特性：
 * 1. 队列化更新处理，避免并发冲突
 * 2. 基于优先级的更新调度
 * 3. 智能冲突检测和解决
 * 4. 批量处理优化
 * 5. 循环依赖检测
 * 6. 性能监控
 */
export class BidirectionalDataFlowManager {
  private updateQueue: DataUpdate[] = [];
  private processingQueue: DataUpdate[] = [];
  private isProcessing = false;
  private batchTimer: number | null = null;
  private config: FlowConfig;
  private metrics: FlowMetrics;
  private updateHandlers = new Map<string, (update: DataUpdate) => Promise<boolean>>();
  private conflictResolvers = new Map<string, (updates: DataUpdate[]) => DataUpdate[]>();

  // 更新类型的优先级权重
  private readonly PRIORITY_WEIGHTS: Record<UpdatePriority, number> = {
    immediate: 4,
    high: 3,
    normal: 2,
    low: 1
  };

  // 更新类型的冲突规则
  private readonly CONFLICT_RULES: Record<UpdateType, UpdateType[]> = {
    drag: ['property', 'selection'], // 拖拽与属性调整、选择变化冲突
    property: ['drag'],              // 属性调整与拖拽冲突
    selection: ['drag'],             // 选择变化与拖拽冲突
    creation: [],                    // 创建操作不冲突
    deletion: ['drag', 'property']   // 删除与拖拽、属性调整冲突
  };

  constructor(config: Partial<FlowConfig> = {}) {
    this.config = {
      maxQueueSize: 100,
      batchSize: 10,
      batchTimeout: 16, // 约60fps
      enableConflictResolution: true,
      enableMetrics: true,
      ...config
    };

    this.metrics = {
      totalUpdates: 0,
      processedUpdates: 0,
      skippedUpdates: 0,
      conflictCount: 0,
      averageProcessingTime: 0,
      queueSize: 0,
      batchCount: 0
    };

    this.setupDefaultConflictResolvers();
  }

  /**
   * 注册更新处理器
   */
  registerHandler(type: string, handler: (update: DataUpdate) => Promise<boolean>): void {
    this.updateHandlers.set(type, handler);
  }

  /**
   * 注册冲突解决器
   */
  registerConflictResolver(type: string, resolver: (updates: DataUpdate[]) => DataUpdate[]): void {
    this.conflictResolvers.set(type, resolver);
  }

  /**
   * 提交更新
   */
  async submitUpdate(
    source: DataSource,
    type: UpdateType,
    data: any,
    priority: UpdatePriority = 'normal',
    dependencies?: string[]
  ): Promise<string> {
    const update: DataUpdate = {
      id: this.generateUpdateId(),
      source,
      type,
      priority,
      data,
      timestamp: performance.now(),
      dependencies,
      conflictsWith: this.CONFLICT_RULES[type]
    };

    // 检查队列大小限制
    if (this.updateQueue.length >= this.config.maxQueueSize) {
      // 移除低优先级的旧更新
      this.evictLowPriorityUpdates();
    }

    this.updateQueue.push(update);
    this.metrics.totalUpdates++;
    this.metrics.queueSize = this.updateQueue.length;

    // 根据优先级决定处理策略
    if (priority === 'immediate') {
      await this.processImmediately(update);
    } else {
      this.scheduleBatchProcessing();
    }

    return update.id;
  }

  /**
   * 取消更新
   */
  cancelUpdate(updateId: string): boolean {
    const index = this.updateQueue.findIndex(u => u.id === updateId);
    if (index >= 0) {
      this.updateQueue.splice(index, 1);
      this.metrics.queueSize = this.updateQueue.length;
      return true;
    }
    return false;
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    this.updateQueue.length = 0;
    this.metrics.queueSize = 0;
  }

  /**
   * 获取队列状态
   */
  getQueueStatus() {
    return {
      queueSize: this.updateQueue.length,
      processingQueueSize: this.processingQueue.length,
      isProcessing: this.isProcessing,
      nextBatchIn: this.batchTimer ? this.config.batchTimeout : 0
    };
  }

  /**
   * 获取性能指标
   */
  getMetrics(): FlowMetrics {
    return { ...this.metrics };
  }

  /**
   * 立即处理更新
   */
  private async processImmediately(update: DataUpdate): Promise<void> {
    const startTime = performance.now();
    
    try {
      const handler = this.updateHandlers.get(`${update.source}-${update.type}`);
      if (handler) {
        const success = await handler(update);
        if (success) {
          this.metrics.processedUpdates++;
        } else {
          this.metrics.skippedUpdates++;
        }
      }
    } catch (error) {
      console.error('立即处理更新失败:', error);
      this.metrics.skippedUpdates++;
    }

    const processingTime = performance.now() - startTime;
    this.updateAverageProcessingTime(processingTime);
  }

  /**
   * 调度批量处理
   */
  private scheduleBatchProcessing(): void {
    if (this.batchTimer) {
      return; // 已经调度了
    }

    this.batchTimer = window.setTimeout(() => {
      this.processBatch();
      this.batchTimer = null;
    }, this.config.batchTimeout);
  }

  /**
   * 处理批量更新
   */
  private async processBatch(): Promise<UpdateResult> {
    if (this.isProcessing || this.updateQueue.length === 0) {
      return {
        success: true,
        appliedUpdates: [],
        skippedUpdates: [],
        conflicts: [],
        processingTime: 0
      };
    }

    const startTime = performance.now();
    this.isProcessing = true;

    try {
      // 准备处理队列
      this.processingQueue = this.updateQueue.splice(0, this.config.batchSize);
      this.metrics.queueSize = this.updateQueue.length;

      // 排序和冲突解决
      const sortedUpdates = this.sortUpdatesByPriority(this.processingQueue);
      const resolvedUpdates = this.config.enableConflictResolution 
        ? this.resolveConflicts(sortedUpdates)
        : sortedUpdates;

      // 检查依赖关系
      const orderedUpdates = this.resolveDependencies(resolvedUpdates);

      // 执行更新
      const result = await this.executeUpdates(orderedUpdates);

      this.metrics.batchCount++;
      const processingTime = performance.now() - startTime;
      this.updateAverageProcessingTime(processingTime);

      return {
        ...result,
        processingTime
      };

    } finally {
      this.isProcessing = false;
      this.processingQueue.length = 0;

      // 如果还有待处理的更新，继续调度
      if (this.updateQueue.length > 0) {
        this.scheduleBatchProcessing();
      }
    }
  }

  /**
   * 按优先级排序更新
   */
  private sortUpdatesByPriority(updates: DataUpdate[]): DataUpdate[] {
    return updates.sort((a, b) => {
      const priorityDiff = this.PRIORITY_WEIGHTS[b.priority] - this.PRIORITY_WEIGHTS[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      // 相同优先级按时间戳排序
      return a.timestamp - b.timestamp;
    });
  }

  /**
   * 解决冲突
   */
  private resolveConflicts(updates: DataUpdate[]): DataUpdate[] {
    const conflictGroups = this.groupConflictingUpdates(updates);
    const resolvedUpdates: DataUpdate[] = [];

    for (const group of conflictGroups) {
      if (group.length === 1) {
        resolvedUpdates.push(group[0]);
      } else {
        // 使用冲突解决器
        const conflictType = group[0].type;
        const resolver = this.conflictResolvers.get(conflictType);
        
        if (resolver) {
          const resolved = resolver(group);
          resolvedUpdates.push(...resolved);
          this.metrics.conflictCount += group.length - resolved.length;
        } else {
          // 默认策略：保留最高优先级的更新
          const highest = group.reduce((prev, curr) => 
            this.PRIORITY_WEIGHTS[curr.priority] > this.PRIORITY_WEIGHTS[prev.priority] ? curr : prev
          );
          resolvedUpdates.push(highest);
          this.metrics.conflictCount += group.length - 1;
        }
      }
    }

    return resolvedUpdates;
  }

  /**
   * 分组冲突的更新
   */
  private groupConflictingUpdates(updates: DataUpdate[]): DataUpdate[][] {
    const groups: DataUpdate[][] = [];
    const processed = new Set<string>();

    for (const update of updates) {
      if (processed.has(update.id)) continue;

      const conflictGroup = [update];
      processed.add(update.id);

      // 查找冲突的更新
      for (const other of updates) {
        if (other.id === update.id || processed.has(other.id)) continue;

        if (this.areConflicting(update, other)) {
          conflictGroup.push(other);
          processed.add(other.id);
        }
      }

      groups.push(conflictGroup);
    }

    return groups;
  }

  /**
   * 检查两个更新是否冲突
   */
  private areConflicting(update1: DataUpdate, update2: DataUpdate): boolean {
    return update1.conflictsWith?.includes(update2.type) || 
           update2.conflictsWith?.includes(update1.type) ||
           false;
  }

  /**
   * 解决依赖关系
   */
  private resolveDependencies(updates: DataUpdate[]): DataUpdate[] {
    const updateMap = new Map(updates.map(u => [u.id, u]));
    const resolved: DataUpdate[] = [];
    const visiting = new Set<string>();
    const visited = new Set<string>();

    const visit = (update: DataUpdate): void => {
      if (visited.has(update.id)) return;
      if (visiting.has(update.id)) {
        // 检测到循环依赖，忽略依赖关系
        console.warn('检测到循环依赖:', update.id);
        return;
      }

      visiting.add(update.id);

      // 先处理依赖
      if (update.dependencies) {
        for (const depId of update.dependencies) {
          const dep = updateMap.get(depId);
          if (dep && !visited.has(depId)) {
            visit(dep);
          }
        }
      }

      visiting.delete(update.id);
      visited.add(update.id);
      resolved.push(update);
    };

    for (const update of updates) {
      visit(update);
    }

    return resolved;
  }

  /**
   * 执行更新
   */
  private async executeUpdates(updates: DataUpdate[]): Promise<Omit<UpdateResult, 'processingTime'>> {
    const appliedUpdates: string[] = [];
    const skippedUpdates: string[] = [];
    const conflicts: string[] = [];

    for (const update of updates) {
      try {
        const handlerKey = `${update.source}-${update.type}`;
        const handler = this.updateHandlers.get(handlerKey);

        if (!handler) {
          skippedUpdates.push(update.id);
          continue;
        }

        const success = await handler(update);
        if (success) {
          appliedUpdates.push(update.id);
          this.metrics.processedUpdates++;
        } else {
          skippedUpdates.push(update.id);
          this.metrics.skippedUpdates++;
        }

      } catch (error) {
        console.error(`执行更新失败 [${update.id}]:`, error);
        skippedUpdates.push(update.id);
        this.metrics.skippedUpdates++;
      }
    }

    return {
      success: skippedUpdates.length === 0,
      appliedUpdates,
      skippedUpdates,
      conflicts
    };
  }

  /**
   * 淘汰低优先级更新
   */
  private evictLowPriorityUpdates(): void {
    // 按优先级排序，移除最低优先级的更新
    this.updateQueue.sort((a, b) => this.PRIORITY_WEIGHTS[a.priority] - this.PRIORITY_WEIGHTS[b.priority]);
    
    const toRemove = Math.ceil(this.config.maxQueueSize * 0.2); // 移除20%
    this.updateQueue.splice(0, toRemove);
    this.metrics.skippedUpdates += toRemove;
  }

  /**
   * 生成更新ID
   */
  private generateUpdateId(): string {
    return `update-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 更新平均处理时间
   */
  private updateAverageProcessingTime(newTime: number): void {
    const count = this.metrics.processedUpdates + this.metrics.skippedUpdates;
    if (count === 0) {
      this.metrics.averageProcessingTime = newTime;
    } else {
      this.metrics.averageProcessingTime = 
        (this.metrics.averageProcessingTime * (count - 1) + newTime) / count;
    }
  }

  /**
   * 设置默认冲突解决器
   */
  private setupDefaultConflictResolvers(): void {
    // 拖拽冲突解决器：保留最新的拖拽更新
    this.conflictResolvers.set('drag', (updates) => {
      return updates
        .filter(u => u.type === 'drag')
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, 1);
    });

    // 属性调整冲突解决器：合并属性更新
    this.conflictResolvers.set('property', (updates) => {
      const propertyUpdates = updates.filter(u => u.type === 'property');
      if (propertyUpdates.length <= 1) return propertyUpdates;

      // 合并属性更新
      const merged = propertyUpdates.reduce((acc, curr) => {
        return {
          ...acc,
          data: { ...acc.data, ...curr.data },
          timestamp: Math.max(acc.timestamp, curr.timestamp)
        };
      });

      return [merged];
    });
  }
}

/**
 * 全局双向数据流管理器实例
 */
export const globalDataFlowManager = new BidirectionalDataFlowManager();

/**
 * 便捷的更新提交函数
 */
export function submitDataUpdate(
  source: DataSource,
  type: UpdateType,
  data: any,
  priority: UpdatePriority = 'normal'
): Promise<string> {
  return globalDataFlowManager.submitUpdate(source, type, data, priority);
}
