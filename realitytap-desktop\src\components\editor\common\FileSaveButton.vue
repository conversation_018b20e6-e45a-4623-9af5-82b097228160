<template>
  <div class="save-button-container">
    <n-tooltip :show-arrow="false" placement="bottom">
      <template #trigger>
        <n-button
          type="primary"
          @click="handleSaveFile"
          class="save-btn"
          :class="{ 'save-btn--disabled': !isCurrentFileUnsaved }"
          :loading="isSaving"
          :disabled="!isCurrentFileUnsaved"
          secondary
        >
          <template #icon>
            <n-icon class="save-icon">
              <SaveIcon />
            </n-icon>
          </template>
        </n-button>
      </template>
      <span>{{ tooltipText }}</span>
    </n-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { NButton, NIcon, NTooltip, useMessage } from "naive-ui";
import { Save20Regular as SaveIcon } from "@vicons/fluent";
import { useProjectStore } from "@/stores/haptics-project-store";
import { useFileWaveformEditorStore, validateFileUuid } from "@/stores/haptics-editor-store";
import { useI18n } from "@/composables/useI18n";
import { logger, LogModule } from "@/utils/logger/logger";

// 获取stores和message
const projectStore = useProjectStore();
const message = useMessage();
const { t } = useI18n();

// 获取当前文件的 store 实例 - 强制要求有效的fileUuid
const getCurrentFileStore = () => {
  const fileUuid = projectStore.selectedFileUuid;
  if (!fileUuid) {
    throw new Error(t('project.save.noFileSelected'));
  }
  const validatedFileUuid = validateFileUuid(fileUuid);
  return useFileWaveformEditorStore(validatedFileUuid);
};

// 保存相关状态
const isSaving = ref(false);

// 计算属性：当前文件是否未保存
const isCurrentFileUnsaved = computed(() => {
  if (!projectStore.selectedFileUuid) return false;
  return projectStore.isFileUnsaved(projectStore.selectedFileUuid);
});

// 计算属性：tooltip文本
const tooltipText = computed(() => {
  if (isSaving.value) {
    return t('project.save.saving');
  }
  if (!projectStore.selectedFileUuid) {
    return t('project.save.noFileSelected');
  }
  if (isCurrentFileUnsaved.value) {
    return t('project.save.saveFile');
  }
  return t('project.save.noChanges');
});

// 保存文件处理函数
const handleSaveFile = async () => {
  if (isSaving.value) {
    logger.debug(LogModule.GENERAL, "保存操作正在进行中，忽略重复请求");
    return;
  }

  isSaving.value = true;

  try {
    const fileUuidToSave = projectStore.selectedFileUuid;

    if (!fileUuidToSave) {
      logger.warn(LogModule.GENERAL, "保存失败：没有选中的文件");
      message.warning(t('project.save.noFileSelected'));
      return;
    }

    // 获取当前文件对应的 store 实例
    const currentFileStore = getCurrentFileStore();

    logger.info(LogModule.GENERAL, "HapticFileSaver 保存操作开始", {
      fileUuidToSave,
      currentFileStoreEvents: currentFileStore.events?.length || 0,
    });

    // 检查是否有事件数据可以保存 - 使用当前文件的 store
    if (!currentFileStore.events || currentFileStore.events.length === 0) {
      logger.warn(LogModule.GENERAL, "保存失败：没有事件数据", {
        fileUuid: fileUuidToSave,
        currentFileStoreEvents: currentFileStore.events?.length || 0,
      });
      message.warning(t('project.save.noData'));
      return;
    }

    logger.info(LogModule.GENERAL, "开始保存文件", {
      fileUuid: fileUuidToSave,
      eventCount: currentFileStore.events.length
    });
    const events = currentFileStore.events;
    const totalDuration = currentFileStore.getEffectiveDuration;
    await projectStore.saveHeFile(fileUuidToSave, events, totalDuration);
    logger.info(LogModule.GENERAL, "文件保存成功");
    message.success(t('project.save.success'));
  } catch (error: any) {
    logger.error(LogModule.GENERAL, "保存文件失败", error);
    message.error(t('project.save.failed', { error: error.message || t('errors.unknown') }));
  } finally {
    // 延迟重置状态
    setTimeout(() => {
      isSaving.value = false;
    }, 500);
  }
};
</script>

<style scoped>
/* ===== 保存按钮容器 ===== */
.save-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* ===== 保存按钮样式 ===== */
.save-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: transparent !important;
  border: 1px solid #36ad6a !important;
  color: #36ad6a !important;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.save-btn:hover {
  background-color: #36ad6a !important;
  color: #ffffff !important;
  border-color: #36ad6a !important;
  transform: scale(1.05);
}

.save-btn:active {
  transform: scale(0.95);
}

/* ===== 禁用状态样式 ===== */
.save-btn--disabled {
  background-color: transparent !important;
  border: 1px solid #4a4a4a !important;
  color: #6a6a6a !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.save-btn--disabled:hover {
  background-color: transparent !important;
  border: 1px solid #4a4a4a !important;
  color: #6a6a6a !important;
  transform: none !important;
}

.save-btn--disabled:active {
  transform: none !important;
}

.save-icon {
  font-size: 18px;
  color: inherit !important;
}
</style>
