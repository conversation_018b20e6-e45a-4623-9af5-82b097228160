import type { RenderableEvent } from "@/types/haptic-editor";
import { invoke } from "@tauri-apps/api/core";

/**
 * 确保数值为整数，符合.he文件格式要求
 */
function ensureInteger(value: number): number {
  return Math.round(value);
}

/**
 * 确保强度值在[0,100]范围内且为整数（用于事件级别的强度）
 */
function ensureIntensity(value: number): number {
  return Math.round(Math.max(0, Math.min(100, value)));
}

/**
 * 确保曲线点强度值在[0,1.0]范围内且为浮点数（用于曲线点的强度）
 */
function ensureCurveIntensity(value: number): number {
  return Math.max(0, Math.min(1.0, value));
}

/**
 * 确保频率值在[0,100]范围内且为整数
 */
function ensureFrequency(value: number): number {
  return Math.round(Math.max(0, Math.min(100, value)));
}

/**
 * 将RenderableEvent数组转换为.he文件格式的JSON内容
 */
export function convertEventsToHeContent(events: RenderableEvent[], totalDuration?: number): string {
  const today = new Date();
  const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;

  const pattern = events
    .map((event) => {
      if (event.type === "transient") {
        return {
          Event: {
            Type: "transient",
            RelativeTime: ensureInteger(event.startTime),
            Parameters: {
              Frequency: ensureFrequency(event.frequency),
              Intensity: ensureIntensity(event.intensity),
            },
          },
        };
      } else if (event.type === "continuous") {
        // 转换曲线点格式，确保所有数值都符合.he文件格式要求
        const curve = event.curves.map((point) => ({
          Time: ensureInteger(point.timeOffset),
          Intensity: ensureCurveIntensity(point.rawIntensity), // 曲线点强度应该是0-1.0的浮点数
          Frequency: ensureFrequency(point.relativeCurveFrequency),
        }));

        return {
          Event: {
            Type: "continuous",
            RelativeTime: ensureInteger(event.startTime),
            Duration: ensureInteger(event.duration),
            Parameters: {
              Frequency: ensureFrequency(event.eventFrequency),
              Intensity: ensureIntensity(event.eventIntensity),
              Curve: curve,
            },
          },
        };
      }
      return null;
    })
    .filter(Boolean);

  const metadata: any = {
    Version: 1,
    Created: dateStr,
    Description: "AWA RealityTap Haptics Effect File",
  };

  // 如果提供了总时长，则保存到 Metadata 中
  if (totalDuration !== undefined && totalDuration > 0) {
    metadata.TotalDuration = Math.floor(totalDuration);
    console.log(`[heFileSaver] 保存总时长到 Metadata: ${metadata.TotalDuration}ms`);
  }

  const content = {
    Metadata: metadata,
    Pattern: pattern,
  };

  return JSON.stringify(content, null, 2);
}

/**
 * 保存.he文件内容到指定路径
 */
export async function saveHeFileContent(projectDirPath: string, filePath: string, content: string): Promise<void> {
  try {
    await invoke("write_file_content_command", {
      projectDirPath,
      filePath,
      content,
    });
  } catch (error) {
    console.error("Failed to save .he file:", error);
    throw new Error(`保存.he文件失败: ${error}`);
  }
}

/**
 * 保存events到.he文件的完整流程
 */
export async function saveEventsToHeFile(projectDirPath: string, filePath: string, events: RenderableEvent[], totalDuration?: number): Promise<void> {
  const heContent = convertEventsToHeContent(events, totalDuration);
  await saveHeFileContent(projectDirPath, filePath, heContent);
}
