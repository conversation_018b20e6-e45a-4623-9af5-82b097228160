// 数据传输相关类型定义

// 传输状态枚举
export enum TransmissionStatus {
  IDLE = "idle",
  PREPARING = "preparing",
  TRANSMITTING = "transmitting",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
  PAUSED = "paused",
}

// 传输类型枚举
export enum TransmissionType {
  HE_FILE = "he_file",
  AUDIO_FILE = "audio_file",
  PROJECT_DATA = "project_data",
  DEVICE_CONFIG = "device_config",
}

// 传输优先级枚举
export enum TransmissionPriority {
  LOW = "low",
  NORMAL = "normal",
  HIGH = "high",
  URGENT = "urgent",
}

// 传输任务接口
export interface TransmissionTask {
  taskId: string;
  deviceId: string;
  type: TransmissionType;
  status: TransmissionStatus;
  priority: TransmissionPriority;
  filePath: string;
  fileName: string;
  fileSize: number;
  progress: number; // 0-100
  bytesTransmitted: number;
  totalBytes: number;
  speed: number; // bytes per second
  estimatedTimeRemaining: number; // seconds
  startTime: string | null;
  endTime: string | null;
  error: string | null;
  retryCount: number;
  maxRetries: number;
  metadata: TransmissionMetadata;
  createdAt: string;
  updatedAt: string;
}

// 传输元数据
export interface TransmissionMetadata {
  originalFilePath?: string;
  checksum?: string;
  compression?: boolean;
  encryption?: boolean;
  chunkSize?: number;
  totalChunks?: number;
  transmittedChunks?: number;
  customData?: Record<string, any>;
}

// 传输配置
export interface TransmissionConfig {
  chunkSize: number; // 数据块大小（字节）
  timeout: number; // 传输超时时间（毫秒）
  maxRetries: number; // 最大重试次数
  retryDelay: number; // 重试延迟（毫秒）
  enableCompression: boolean; // 是否启用压缩
  enableEncryption: boolean; // 是否启用加密
  enableChecksum: boolean; // 是否启用校验和
  maxConcurrentTasks: number; // 最大并发任务数
}

// 传输事件类型
export enum TransmissionEventType {
  TASK_CREATED = "task_created",
  TASK_STARTED = "task_started",
  TASK_PROGRESS = "task_progress",
  TASK_COMPLETED = "task_completed",
  TASK_FAILED = "task_failed",
  TASK_CANCELLED = "task_cancelled",
  TASK_PAUSED = "task_paused",
  TASK_RESUMED = "task_resumed",
  QUEUE_UPDATED = "queue_updated",
}

// 传输事件接口
export interface TransmissionEvent {
  type: TransmissionEventType;
  taskId: string;
  deviceId: string;
  task?: TransmissionTask;
  progress?: number;
  error?: string;
  timestamp: string;
  data?: any;
}

// 传输队列状态
export interface TransmissionQueueStatus {
  totalTasks: number;
  activeTasks: number;
  pendingTasks: number;
  completedTasks: number;
  failedTasks: number;
  totalProgress: number; // 0-100
  estimatedTimeRemaining: number; // seconds
}

// 传输统计信息
export interface TransmissionStatistics {
  totalTasksCreated: number;
  totalTasksCompleted: number;
  totalTasksFailed: number;
  totalBytesTransmitted: number;
  averageSpeed: number; // bytes per second
  averageTaskDuration: number; // seconds
  successRate: number; // 0-100
  lastTransmissionTime: string | null;
  transmissionsByType: Record<TransmissionType, number>;
  transmissionsByDevice: Record<string, number>;
}

// 传输历史记录
export interface TransmissionHistory {
  historyId: string;
  taskId: string;
  deviceId: string;
  deviceName: string;
  type: TransmissionType;
  fileName: string;
  fileSize: number;
  status: TransmissionStatus;
  duration: number; // seconds
  speed: number; // bytes per second
  error: string | null;
  timestamp: string;
}

// 传输过滤选项
export interface TransmissionFilter {
  deviceIds?: string[];
  types?: TransmissionType[];
  statuses?: TransmissionStatus[];
  priorities?: TransmissionPriority[];
  dateRange?: {
    start: string;
    end: string;
  };
  searchText?: string;
}

// 传输排序选项
export interface TransmissionSort {
  field: "createdAt" | "priority" | "progress" | "fileName" | "fileSize" | "speed";
  order: "asc" | "desc";
}

// 批量传输选项
export interface BatchTransmissionOptions {
  deviceIds: string[];
  filePaths: string[];
  priority: TransmissionPriority;
  config?: Partial<TransmissionConfig>;
  stopOnFirstError?: boolean;
  maxConcurrentDevices?: number;
}

// 传输结果
export interface TransmissionResult {
  taskId: string;
  success: boolean;
  bytesTransmitted: number;
  duration: number; // seconds
  speed: number; // bytes per second
  error: string | null;
  checksum: string | null;
}

// 传输进度回调
export type TransmissionProgressCallback = (progress: {
  taskId: string;
  progress: number;
  bytesTransmitted: number;
  totalBytes: number;
  speed: number;
  estimatedTimeRemaining: number;
}) => void;

// 传输完成回调
export type TransmissionCompleteCallback = (result: TransmissionResult) => void;

// 传输错误回调
export type TransmissionErrorCallback = (error: { taskId: string; error: string; retryCount: number; maxRetries: number }) => void;
