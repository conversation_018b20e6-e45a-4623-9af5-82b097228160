import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import { createPinia } from "pinia"; // 导入 createPinia
import router from './router' // 导入路由配置
import { LoggerPlugin } from './plugins/logger' // 导入日志插件
import { i18n } from './locales' // 导入国际化配置

const app = createApp(App);
const pinia = createPinia(); // 创建 pinia 实例

app.use(pinia); // 使用 pinia 插件
app.use(router); // 使用路由
app.use(LoggerPlugin); // 使用日志插件
app.use(i18n); // 使用国际化插件


app.mount("#app");