<template>
  <NModal v-model:show="isVisible" preset="card" :title="t('debug.settings.title')" style="width:600px;" class="debug-settings-modal">
    <div class="debug-settings-container">
      <!-- 调试模式状态显示 -->
      <div class="debug-status-section">
        <NAlert :type="debugConfig.enabled ? 'success' : 'info'" :title="debugConfig.enabled ? t('debug.settings.debugEnabled') : t('debug.settings.debugDisabled')">
          <p>{{ debugConfig.enabled ? t('debug.settings.debugEnabledDesc') : t('debug.settings.debugDisabledDesc') }}</p>
          <p><strong>{{ t('debug.settings.buildMode') }}:</strong> {{ buildMode }}</p>
        </NAlert>
      </div>

      <!-- 调试配置控制 -->
      <div class="debug-config-section">
        <h3>{{ t('debug.settings.currentConfig') }}</h3>

        <!-- 调试模式开关 -->
        <div class="config-item">
          <span class="config-label">{{ t('debug.settings.enableDebug') }}</span>
          <NSwitch v-model:value="debugConfig.enabled" :disabled="!canModifyDebugMode">
            <template #checked>{{ t("common.enabled") }}</template>
            <template #unchecked>{{ t("common.disabled") }}</template>
          </NSwitch>
        </div>

        <!-- 日志级别 -->
        <div class="config-item">
          <span class="config-label">{{ t('debug.settings.logLevel') }}</span>
          <NSelect
            v-model:value="debugConfig.level"
            :options="logLevelOptions"
            :disabled="!debugConfig.enabled"
            style="width: 120px;"
          />
        </div>

        <!-- OTA 操作日志 -->
        <div class="config-item">
          <span class="config-label">{{ t('debug.settings.logOtaOperations') }}</span>
          <NSwitch v-model:value="debugConfig.logOtaOperations" :disabled="!debugConfig.enabled">
            <template #checked>{{ t("common.enabled") }}</template>
            <template #unchecked>{{ t("common.disabled") }}</template>
          </NSwitch>
        </div>

        <!-- 设备操作日志 -->
        <div class="config-item">
          <span class="config-label">{{ t('debug.settings.logDeviceOperations') }}</span>
          <NSwitch v-model:value="debugConfig.logDeviceOperations" :disabled="!debugConfig.enabled">
            <template #checked>{{ t("common.enabled") }}</template>
            <template #unchecked>{{ t("common.disabled") }}</template>
          </NSwitch>
        </div>
      </div>

      <!-- 说明信息 -->
      <NAlert type="info" :title="t('debug.settings.notice.title')" class="notice-alert">
        <ul class="notice-list">
          <li>{{ t("debug.settings.notice.sessionOnly") }}</li>
          <li>{{ t("debug.settings.notice.performance") }}</li>
          <li>{{ t("debug.settings.notice.logLocation") }}</li>
        </ul>
      </NAlert>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <NSpace>
          <NButton @click="openLogViewer" type="primary" ghost>
            <template #icon>
              <NIcon><DocumentTextIcon /></NIcon>
            </template>
            {{ t("debug.settings.viewLogs") }}
          </NButton>

          <NButton @click="openLogFolder" type="info" ghost>
            <template #icon>
              <NIcon><FolderOpenIcon /></NIcon>
            </template>
            {{ t("debug.settings.openLogFolder") }}
          </NButton>
        </NSpace>
      </div>
    </div>

    <template #action>
      <NSpace>
        <NButton @click="hideDebugSettings">{{ t("common.cancel") }}</NButton>
        <NButton @click="resetToDefault" type="warning">{{ t("debug.settings.resetDefault") }}</NButton>
        <NButton @click="saveSettings" :loading="isSaving" type="primary">{{ t("common.save") }}</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { useI18n } from "@/composables/useI18n";
import { useMessage } from "naive-ui";
import { invoke } from "@tauri-apps/api/core";
import { DocumentTextOutline as DocumentTextIcon, FolderOpenOutline as FolderOpenIcon } from "@vicons/ionicons5";
import { logger, LogModule } from "@/utils/logger/logger";

// === 接口定义 ===
interface DebugConfig {
  enabled: boolean;
  level: string;
  logOtaOperations: boolean;
  logDeviceOperations: boolean;
}

// === 组合函数 ===
const { t } = useI18n();
const message = useMessage();

// === 响应式状态 ===
const isVisible = ref(false);
const isSaving = ref(false);

const debugConfig = reactive<DebugConfig>({
  enabled: false, // 将由后端根据构建类型确定
  level: "info",
  logOtaOperations: true,
  logDeviceOperations: false,
});

// === 表单配置 ===
const logLevelOptions = [
  { label: "Error", value: "error" },
  { label: "Warn", value: "warn" },
  { label: "Info", value: "info" },
  { label: "Debug", value: "debug" },
  { label: "Trace", value: "trace" },
];

// === 计算属性 ===
const buildMode = computed(() => {
  return debugConfig.enabled ? "开发模式 (Debug)" : "生产模式 (Release)";
});

// 是否可以修改调试模式（生产环境下不允许启用调试模式）
const canModifyDebugMode = computed(() => {
  // 如果当前是开发环境，允许开关调试模式
  // 如果是生产环境，不允许启用调试模式（但可以禁用）
  return true; // 暂时允许所有情况下都可以修改，实际逻辑可以根据需要调整
});

// === 方法 ===

/**
 * 显示调试设置对话框
 */
const showDebugSettings = async () => {
  isVisible.value = true;
  await loadDebugConfig();
};

/**
 * 隐藏调试设置对话框
 */
const hideDebugSettings = () => {
  isVisible.value = false;
};

/**
 * 加载调试配置
 */
const loadDebugConfig = async () => {
  try {
    const config = await invoke<DebugConfig>("get_debug_mode");
    Object.assign(debugConfig, config);
  } catch (error) {
    logger.error(LogModule.GENERAL, "Failed to load debug config", error);
    message.error(t("debug.settings.errors.loadFailed"));
  }
};

/**
 * 保存设置（仅在当前会话中生效）
 */
const saveSettings = async () => {
  if (isSaving.value) return;

  isSaving.value = true;
  try {
    await invoke("set_debug_mode", { config: debugConfig });
    message.success(t("debug.settings.saveSuccess"));
    hideDebugSettings();
  } catch (error) {
    logger.error(LogModule.GENERAL, "Failed to save debug config", error);
    message.error(t("debug.settings.errors.saveFailed"));
  } finally {
    isSaving.value = false;
  }
};

/**
 * 重置为默认值
 */
const resetToDefault = () => {
  const defaultConfig = {
    enabled: true, // 开发环境默认启用
    level: "info",
    logOtaOperations: true,
    logDeviceOperations: false,
  };
  Object.assign(debugConfig, defaultConfig);
  message.info(t("debug.settings.resetSuccess"));
};

/**
 * 打开日志查看器
 */
const openLogViewer = () => {
  // 触发父组件的日志查看器
  emit("openLogViewer");
};

/**
 * 打开日志文件夹
 */
const openLogFolder = async () => {
  try {
    const logPath = await invoke<string>("get_log_file_path");
    // 获取文件夹路径（去掉文件名）
    const folderPath = logPath.substring(0, logPath.lastIndexOf("\\") || logPath.lastIndexOf("/"));
    // 使用系统默认方式打开文件夹
    await invoke("open_external_url", { url: `file://${folderPath}` });
  } catch (error) {
    logger.error(LogModule.GENERAL, "Failed to open log folder", error);
    message.error(t("debug.settings.errors.openFolderFailed"));
  }
};

// === 事件 ===
const emit = defineEmits<{
  openLogViewer: [];
}>();

// === 生命周期 ===
onMounted(() => {
  // 组件挂载时不自动加载，由父组件控制
});

// === 暴露给父组件的方法 ===
defineExpose({
  showDebugSettings,
  hideDebugSettings,
});
</script>

<style scoped>
.debug-settings-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.debug-status-section {
  margin-bottom: 16px;
}

.debug-config-section {
  padding: 16px;
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  background: var(--n-color-embedded);
}

.debug-config-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--n-text-color);
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--n-border-color);
}

.config-item:last-child {
  border-bottom: none;
}

.config-label {
  font-weight: 500;
  color: var(--n-text-color);
  flex: 1;
}

.config-value {
  color: var(--n-text-color-2);
  font-family: monospace;
}

.notice-alert {
  margin-top: 16px;
}

.notice-list {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.notice-list li {
  margin-bottom: 4px;
  color: var(--n-text-color-2);
}

.quick-actions {
  padding-top: 16px;
  border-top: 1px solid var(--n-border-color);
}

/* 表单样式调整 */
:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-form-item) {
  margin-bottom: 20px;
}

/* 深色主题适配 */
:deep(.n-modal-card) {
  background: var(--n-color);
}
</style>
