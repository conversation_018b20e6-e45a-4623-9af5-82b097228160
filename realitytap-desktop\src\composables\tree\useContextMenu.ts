import { ref } from "vue";
import { type DropdownOption as NaiveDropdownOption } from "naive-ui";
import { h } from "vue";
import { NIcon } from "naive-ui";
import type { Component } from "vue";
import type { HapticFile, HapticsGroup } from "@/types/haptic-project";

// Icon imports
import { DocumentAdd20Regular, Edit20Regular as EditIcon, MusicNote220Regular, Video20Regular, FolderAdd20Regular as NewFolderIcon, Delete20Regular as RemoveIcon, Play20Regular as PlayIcon } from "@vicons/fluent";

// Context Menu Types
export type ContextMenuType = "file" | "group" | "panel";

export interface ContextMenuEventPayload {
  type: ContextMenuType;
  event: MouseEvent;
  item?: HapticFile | HapticsGroup;
}

/**
 * 上下文菜单管理 Composable
 * 处理右键菜单的显示、选项生成和操作执行
 */
export function useContextMenu(
  expandedTreeKeys: ReturnType<typeof ref<string[]>>,
  ensureParentGroupsExpanded: (groupUuid: string) => void,
  t: (key: string, values?: Record<string, any>) => string) {

  // Context Menu State
  const showDropdown = ref(false);
  const dropdownX = ref(0);
  const dropdownY = ref(0);
  const dropdownOptions = ref<NaiveDropdownOption[]>([]);
  const currentItem = ref<HapticFile | HapticsGroup | null>(null);
  const currentType = ref<ContextMenuType | null>(null);

  // 导入进度状态
  const isImportingAudio = ref(false);
  const importSpinMessage = ref("");

  // Helper function to render icons
  const renderIcon = (icon: Component) => (): any => h(NIcon, null, { default: () => h(icon) });

  /**
   * 显示上下文菜单
   */
  const handleShowContextMenu = (payload: ContextMenuEventPayload) => {
    dropdownX.value = payload.event.clientX;
    dropdownY.value = payload.event.clientY;
    currentItem.value = payload.item || null;
    currentType.value = payload.type;

    // 如果是分组，确保它被展开
    if (payload.type === "group" && payload.item) {
      const group = payload.item as HapticsGroup;
      const groupKey = `group-${group.groupUuid}`;
      if ((expandedTreeKeys.value ?? []).includes(groupKey) === false) {
        (expandedTreeKeys.value ?? []).push(groupKey);
      }
      // 确保父分组也被展开
      if (group.parentGroupUuid) {
        ensureParentGroupsExpanded(group.parentGroupUuid);
      }
    }

    const options: NaiveDropdownOption[] = [];

    if (payload.type === "file") {
      options.push({
        label: t('editor.contextMenu.playEffect'),
        key: "play-effect",
        icon: renderIcon(PlayIcon),
      });
      options.push({ type: "divider" });
      options.push({
        label: t('editor.contextMenu.renameFile'),
        key: "rename-file",
        icon: renderIcon(EditIcon),
      });
      options.push({
        label: t('editor.contextMenu.deleteFile'),
        key: "delete-file",
        icon: renderIcon(RemoveIcon),
      });
    } else if (payload.type === "group") {
      const group = payload.item as HapticsGroup;
      options.push({
        label: t('editor.contextMenu.addFileToGroup', { name: group.name }),
        key: "new-file-in-group",
        icon: renderIcon(DocumentAdd20Regular),
      });
      options.push({
        label: t('editor.contextMenu.importAudioToGroup', { name: group.name }),
        key: "import-audio-to-group",
        icon: renderIcon(MusicNote220Regular),
      });
      options.push({
        label: t('editor.contextMenu.importVideoToGroup', { name: group.name }),
        key: "import-video-to-group",
        icon: renderIcon(Video20Regular),
      });
      options.push({ type: "divider" });
      options.push({
        label: t('editor.contextMenu.newChildGroup', { name: group.name }),
        key: "new-child-group",
        icon: renderIcon(NewFolderIcon),
      });
      options.push({ type: "divider" });
      options.push({
        label: t('editor.contextMenu.renameGroup'),
        key: "rename-group",
        icon: renderIcon(EditIcon),
      });
      options.push({
        label: t('editor.contextMenu.deleteGroup'),
        key: "delete-group",
        icon: renderIcon(RemoveIcon),
      });
    } else if (payload.type === "panel") {
      options.push({
        label: t('editor.contextMenu.newHapticFile'),
        key: "new-file-panel",
        icon: renderIcon(DocumentAdd20Regular),
      });
      options.push({
        label: t('editor.contextMenu.importAudioFile'),
        key: "import-audio-to-root",
        icon: renderIcon(MusicNote220Regular),
      });
      options.push({
        label: t('editor.contextMenu.importVideoFile'),
        key: "import-video-to-root",
        icon: renderIcon(Video20Regular),
      });
      options.push({
        label: t('editor.contextMenu.newRootGroup'),
        key: "new-top-group",
        icon: renderIcon(NewFolderIcon),
      });
    }

    dropdownOptions.value = options;
    if (options.length > 0) {
      showDropdown.value = true;
    } else {
      showDropdown.value = false;
    }
  };

  /**
   * 处理面板本身的右键菜单
   */
  const handlePanelItselfContextMenu = (event: MouseEvent) => {
    handleShowContextMenu({ type: "panel", event });
  };

  return {
    // 状态
    showDropdown,
    dropdownX,
    dropdownY,
    dropdownOptions,
    currentItem,
    currentType,
    isImportingAudio,
    importSpinMessage,

    // 方法
    handleShowContextMenu,
    handlePanelItselfContextMenu,
  };
}
