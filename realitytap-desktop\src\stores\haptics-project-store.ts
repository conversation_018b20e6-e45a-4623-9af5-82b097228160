import { defineStore } from "pinia";
import { ref, computed } from "vue";
import type { RealityTapProject, HapticFile, HapticsGroup } from "@/types/haptic-project";
import { invoke } from "@tauri-apps/api/core";
import { sep, join } from "@tauri-apps/api/path";
import { parseErrorMessage } from "@/utils/commonUtils";
import { generateHeFileContent, getUniqueHeFileName } from "@/utils/hapticFileUtils";
import type { RenderableEvent } from "@/types/haptic-editor";
import type { RealityTapEffect } from "@/types/haptic-file";
import { validateAudioDuration } from "@/utils/audio/audioValidation";
import { logger, LogModule } from "@/utils/logger/logger";

// 文件缓存数据结构
interface FileCache {
  originalData: RealityTapEffect | null; // 原始磁盘数据
  currentEvents: RenderableEvent[]; // 当前修改的事件数据
  isModified: boolean; // 是否已修改
  lastSavedEvents: RenderableEvent[]; // 最后保存时的事件数据
}

// 解决 window.__TAURI__ 类型报错
declare global {
  interface Window {
    __TAURI__?: any;
  }
}

export const useProjectStore = defineStore("project", () => {
  // === State ===
  const currentProject = ref<RealityTapProject | null>(null);
  const currentProjectDirPath = ref<string | null>(null);
  const isLoading = ref<boolean>(false);
  const selectedFileUuid = ref<string | null>(null);
  const lastRenameTime = ref<number>(0);
  const lastCreatedFileUuid = ref<string | null>(null);

  // 未保存状态管理
  const unsavedFileUuids = ref<Set<string>>(new Set());
  const hasUnsavedChanges = computed(() => unsavedFileUuids.value.size > 0);

  // 文件缓存管理
  const fileCaches = ref<Map<string, FileCache>>(new Map());

  // === Computed ===
  const projectName = computed(() => currentProject.value?.projectName || "Untitled Project");

  const hasFiles = computed(() => {
    return currentProject.value?.files?.length ? currentProject.value.files.length > 0 : false;
  });

  const selectedFile = computed(() => {
    if (!currentProject.value || !selectedFileUuid.value || !currentProject.value.files) return null;
    return currentProject.value.files.find((file) => file.fileUuid === selectedFileUuid.value) || null;
  });

  const groups = computed(() => {
    return currentProject.value?.groups || [];
  });

  const getFilesByGroup = (groupId: string): HapticFile[] => {
    if (!currentProject.value || !currentProject.value.files) return [];
    return currentProject.value.files.filter((file) => file.group === groupId);
  };

  // NEW Getters
  const projectPath = computed(() => currentProjectDirPath.value);
  const activeProject = computed(() => currentProject.value);

  // === Actions ===

  async function getResolvedFilePath(file: HapticFile): Promise<string | null> {
    if (!currentProjectDirPath.value) {
      logger.error(LogModule.PROJECT, "Project directory path is not set. Cannot resolve file path.");
      return null;
    }
    if (!file || !file.path) {
      logger.error(LogModule.PROJECT, "File or file path is missing. Cannot resolve file path.", file);
      return null;
    }
    try {
      const fullPath = await join(currentProjectDirPath.value, file.path);
      return fullPath;
    } catch (error) {
      logger.error(LogModule.PROJECT, "Error joining path", error);
      return null;
    }
  }

  function _updateLastModifiedTime() {
    if (currentProject.value) {
      currentProject.value.lastModifiedTime = new Date().toISOString();
    }
  }

  function setProjectData(project: RealityTapProject, dirPath: string) {
    isLoading.value = true;
    try {
      // 过滤掉 fileType 为 audio 的条目
      const normalizedProject: RealityTapProject = {
        ...project,
        files: (project.files || []).filter((f) => !f.fileType || f.fileType === "he"),
        groups: project.groups || [],
      };
      currentProject.value = structuredClone(normalizedProject);
      currentProjectDirPath.value = dirPath;
      selectedFileUuid.value = null;
      logger.info(LogModule.PROJECT, "Project set/loaded in store", {
        projectName: currentProject.value?.projectName,
        dirPath: currentProjectDirPath.value
      });
    } catch (e) {
      logger.error(LogModule.PROJECT, "Error in setProjectData", e);
      clearProjectData();
    } finally {
      isLoading.value = false;
    }
  }

  function clearProjectData() {
    currentProject.value = null;
    currentProjectDirPath.value = null;
    selectedFileUuid.value = null;
    lastCreatedFileUuid.value = null;
    unsavedFileUuids.value.clear();
    clearAllFileCaches(); // 清除所有文件缓存
    isLoading.value = false;
    logger.info(LogModule.PROJECT, "Project data cleared from store");
  }

  // 未保存状态管理方法
  function markFileAsUnsaved(fileUuid: string) {
    unsavedFileUuids.value.add(fileUuid);
  }

  function markFileAsSaved(fileUuid: string) {
    unsavedFileUuids.value.delete(fileUuid);
  }

  function isFileUnsaved(fileUuid: string): boolean {
    return unsavedFileUuids.value.has(fileUuid);
  }

  function clearAllUnsavedStates() {
    unsavedFileUuids.value.clear();
  }

  // === 文件缓存管理方法 ===

  // 设置文件缓存
  function setFileCache(fileUuid: string, originalData: RealityTapEffect | null, currentEvents: RenderableEvent[]) {
    // 检查文件当前的保存状态
    const fileIsUnsaved = isFileUnsaved(fileUuid);

    const cache: FileCache = {
      originalData,
      currentEvents: [...currentEvents], // 深拷贝事件数组
      isModified: fileIsUnsaved, // 根据文件的实际保存状态设置
      lastSavedEvents: fileIsUnsaved ? [] : [...currentEvents], // 如果文件未保存，则最后保存的事件为空
    };
    fileCaches.value.set(fileUuid, cache);
    logger.debug(LogModule.PROJECT, `文件缓存已设置: ${fileUuid}，修改状态: ${cache.isModified}，事件数量: ${currentEvents.length}`);
  }

  // 更新文件缓存中的事件数据
  function updateFileCacheEvents(fileUuid: string, events: RenderableEvent[]) {
    const cache = fileCaches.value.get(fileUuid);
    if (cache) {
      cache.currentEvents = [...events]; // 深拷贝事件数组

      // 检查当前事件是否与最后保存的事件相同（使用高效比较）
      const eventsChanged = !areEventsArraysEqual(cache.currentEvents, cache.lastSavedEvents);
      cache.isModified = eventsChanged;

      fileCaches.value.set(fileUuid, cache);
      logger.debug(LogModule.PROJECT, `文件缓存事件已更新: ${fileUuid}，事件数量: ${events.length}，修改状态: ${cache.isModified}`);
    }
  }

  // 获取文件缓存
  function getFileCache(fileUuid: string): FileCache | null {
    return fileCaches.value.get(fileUuid) || null;
  }

  // 检查文件是否有缓存
  function hasFileCache(fileUuid: string): boolean {
    return fileCaches.value.has(fileUuid);
  }

  // 清除单个文件的缓存
  function clearFileCache(fileUuid: string) {
    const hadCache = fileCaches.value.has(fileUuid);
    if (hadCache) {
      const cache = fileCaches.value.get(fileUuid);
      logger.debug(LogModule.PROJECT, `清除文件缓存: ${fileUuid}`, {
        isModified: cache?.isModified,
        eventCount: cache?.currentEvents.length,
        hasOriginalData: !!cache?.originalData
      });
      fileCaches.value.delete(fileUuid);
      logger.debug(LogModule.PROJECT, `文件缓存已清除: ${fileUuid}`);
    } else {
      logger.debug(LogModule.PROJECT, `尝试清除不存在的文件缓存: ${fileUuid}`);
    }
  }

  // 清除所有文件缓存
  function clearAllFileCaches() {
    fileCaches.value.clear();
    logger.debug(LogModule.PROJECT, "所有文件缓存已清除");
  }

  // 保存单个.he文件
  async function saveHeFile(fileUuid: string, events: any[], totalDuration?: number): Promise<void> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      throw new Error("没有打开的项目");
    }

    const file = currentProject.value.files?.find((f) => f.fileUuid === fileUuid);
    if (!file) {
      throw new Error("找不到指定的文件");
    }

    try {
      const { saveEventsToHeFile } = await import("@/utils/heFileSaver");
      await saveEventsToHeFile(currentProjectDirPath.value, file.path, events, totalDuration);

      // 标记文件为已保存
      markFileAsSaved(fileUuid);

      // 更新文件缓存的保存状态，但保留缓存数据以支持文件切换
      const cache = fileCaches.value.get(fileUuid);
      if (cache) {
        cache.isModified = false; // 标记为未修改状态
        cache.lastSavedEvents = [...events]; // 更新最后保存的事件数据
        fileCaches.value.set(fileUuid, cache);
        logger.debug(LogModule.PROJECT, `文件缓存状态已更新为已保存: ${fileUuid}`);
      }

      // 更新文件的最后修改时间
      file.lastModifiedTime = new Date().toISOString();
      _updateLastModifiedTime();

      logger.info(LogModule.PROJECT, `文件 ${file.name} 保存成功`);
    } catch (error) {
      logger.error(LogModule.PROJECT, `保存文件 ${file.name} 失败`, error);
      throw error;
    }
  }

  async function handleLoadProject(projectDirectoryPath: string): Promise<boolean> {
    isLoading.value = true;
    try {
      const loadedProjectData = await invoke<RealityTapProject>("load_project", {
        projectDirPath: projectDirectoryPath,
      });
      setProjectData(loadedProjectData, projectDirectoryPath);
      return true;
    } catch (rawError: any) {
      logger.error(LogModule.PROJECT, "Failed to load project via handleLoadProject", rawError);
      clearProjectData();
      isLoading.value = false;
      const userFriendlyMessage = parseErrorMessage(rawError);
      throw new Error(userFriendlyMessage);
    } finally {
      // isLoading.value = false; // Moved into catch and successful try path
    }
  }

  async function handleCreateNewProject(projectNameInput: string, authorInput: string, descriptionInput: string, targetDirInput: string): Promise<boolean> {
    isLoading.value = true;
    try {
      const createdProjectData = await invoke<RealityTapProject>("create_new_project", {
        projectName: projectNameInput,
        author: authorInput,
        description: descriptionInput,
        targetDir: targetDirInput,
      });
      const newProjectDirPath = `${targetDirInput}${await sep()}${projectNameInput}`.replace(/\\/g, "/");
      setProjectData(createdProjectData, newProjectDirPath);
      return true;
    } catch (rawError: any) {
      logger.error(LogModule.PROJECT, "Failed to create new project via handleCreateNewProject", rawError);
      clearProjectData();
      isLoading.value = false;
      const userFriendlyMessage = parseErrorMessage(rawError);
      throw new Error(userFriendlyMessage);
    }
  }

  async function handleSaveCurrentProject(): Promise<boolean> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      logger.warn(LogModule.PROJECT, "No active project to save");
      return false;
    }
    isLoading.value = true;
    try {
      _updateLastModifiedTime();
      logger.info(LogModule.PROJECT, `尝试保存项目到路径: "${currentProjectDirPath.value}"`);

      await invoke("save_project", {
        projectData: currentProject.value,
        projectDirPath: currentProjectDirPath.value,
      });
      logger.info(LogModule.PROJECT, "Project saved successfully", { projectName: currentProject.value.projectName });
      isLoading.value = false;
      return true;
    } catch (rawError: any) {
      logger.error(LogModule.PROJECT, `Failed to save project to "${currentProjectDirPath.value}"`, rawError);

      isLoading.value = false;
      const userFriendlyMessage = parseErrorMessage(rawError);
      throw new Error(userFriendlyMessage);
    }
  }

  function loadProject(project: RealityTapProject) {
    logger.warn(LogModule.PROJECT, "Deprecated: old frontend loadProject called. Use handleLoadProject or setProjectData");
    setProjectData(project, "unknown-loaded-path");
  }

  function selectFile(fileUuid: string) {
    if (currentProject.value?.files?.some((f) => f.fileUuid === fileUuid)) {
      selectedFileUuid.value = fileUuid;
    } else {
      logger.warn(LogModule.PROJECT, `File with UUID ${fileUuid} not found`);
      selectedFileUuid.value = null;
    }
  }

  async function removeHapticFile(fileUuidToRemove: string): Promise<void> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      logger.error(LogModule.PROJECT, "Cannot remove file: No project is loaded or directory path is unknown");
      return;
    }
    isLoading.value = true;
    try {
      const updatedProjectData = await invoke<RealityTapProject>("remove_file_from_project", {
        projectDirPath: currentProjectDirPath.value,
        fileUuidStr: fileUuidToRemove,
      });

      currentProject.value = updatedProjectData;
      _updateLastModifiedTime();

      // 如果删除的是当前选中的文件，清空选中状态
      // 让 UI 层（HapticsPanel）来决定是否选择其他文件
      if (selectedFileUuid.value === fileUuidToRemove) {
        selectedFileUuid.value = null;
      }

      // 清除被删除文件的缓存
      if (fileCaches.value.has(fileUuidToRemove)) {
        fileCaches.value.delete(fileUuidToRemove);
        logger.debug(LogModule.PROJECT, `已清除文件 ${fileUuidToRemove} 的缓存`);
      }

      // 清除被删除文件的未保存状态
      if (unsavedFileUuids.value.has(fileUuidToRemove)) {
        unsavedFileUuids.value.delete(fileUuidToRemove);
        logger.debug(LogModule.PROJECT, `已清除文件 ${fileUuidToRemove} 的未保存状态`);
      }

      // 清理文件级 Store 实例，确保完全清除相关状态
      try {
        const { clearFileStoreInstance } = await import("@/stores/haptics-editor-store");
        clearFileStoreInstance(fileUuidToRemove);
        logger.debug(LogModule.PROJECT, `已清理文件 ${fileUuidToRemove} 的 Store 实例`);
      } catch (error) {
        logger.warn(LogModule.PROJECT, `清理文件 Store 实例时出错: ${fileUuidToRemove}`, error);
      }

      logger.info(LogModule.PROJECT, "File removed and project updated from backend", { fileUuid: fileUuidToRemove });
    } catch (rawError: any) {
      logger.error(LogModule.PROJECT, "Failed to remove project file via backend", rawError);
      const userFriendlyMessage = parseErrorMessage(rawError);
      throw new Error(userFriendlyMessage);
    } finally {
      isLoading.value = false;
    }
  }

  function updateHapticFileMetadata(fileUuid: string, updates: Partial<Omit<HapticFile, "fileUuid" | "createTime">>) {
    logger.warn(LogModule.PROJECT, "updateHapticFileMetadata is currently frontend only and needs backend integration");
    if (!currentProject.value || !currentProject.value.files || !currentProject.value.groups) return;
    const fileIndex = currentProject.value.files.findIndex((file) => file.fileUuid === fileUuid);
    if (fileIndex === -1) {
      logger.warn(LogModule.PROJECT, `File with UUID ${fileUuid} not found for update`);
      return;
    }
    if (updates.group && !currentProject.value.groups.some((g) => g.groupUuid === updates.group)) {
      logger.error(LogModule.PROJECT, `Target group with UUID ${updates.group} does not exist`);
      return;
    }
    const originalFile = currentProject.value.files[fileIndex];
    const updatedFile: HapticFile = {
      ...originalFile,
      ...updates,
      fileUuid: originalFile.fileUuid,
      createTime: originalFile.createTime,
      lastModifiedTime: new Date().toISOString(),
    };
    currentProject.value.files[fileIndex] = updatedFile;
    _updateLastModifiedTime();
  }

  // 更新文件的音频信息
  function updateFileAudioInfo(fileUuid: string, audioInfo: { durationMs: number; sampleRate: number } | null) {
    if (!currentProject.value || !currentProject.value.files) return;
    const fileIndex = currentProject.value.files.findIndex((file) => file.fileUuid === fileUuid);
    if (fileIndex !== -1) {
      currentProject.value.files[fileIndex].audioInfo = audioInfo;
      _updateLastModifiedTime(); // 更新项目最后修改时间
      logger.debug(LogModule.PROJECT, `AudioInfo updated for file ${fileUuid}`, audioInfo);
    } else {
      logger.warn(LogModule.PROJECT, `File with UUID ${fileUuid} not found for updating audioInfo`);
    }
  }

  function updateGroup(groupUuid: string, updates: Partial<Omit<HapticsGroup, "groupUuid">>) {
    logger.warn(LogModule.PROJECT, "updateGroup is currently frontend only and needs backend integration");
    if (!currentProject.value || !currentProject.value.groups) return;
    const groupIndex = currentProject.value.groups.findIndex((g) => g.groupUuid === groupUuid);
    if (groupIndex === -1) {
      logger.warn(LogModule.PROJECT, `Group with UUID ${groupUuid} not found for update`);
      return;
    }

    const originalGroup = currentProject.value.groups[groupIndex];

    const updatedGroup: HapticsGroup = {
      ...originalGroup,
      ...updates,
      groupUuid: originalGroup.groupUuid,
    };

    currentProject.value.groups[groupIndex] = updatedGroup;

    _updateLastModifiedTime();
  }

  async function removeGroup(groupUuid: string, deletionMode: "restrict" | "cascade" = "restrict"): Promise<void> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      logger.error(LogModule.PROJECT, "Cannot remove group: No project is loaded or directory path is unknown");
      throw new Error("没有打开的项目或项目路径未知");
    }
    isLoading.value = true;
    try {
      logger.info(LogModule.PROJECT, `Attempting to remove group ${groupUuid} with mode ${deletionMode}`);
      const updatedProject = await invoke<RealityTapProject>("remove_group_command", {
        projectDirPath: currentProjectDirPath.value,
        groupUuidStr: groupUuid,
        deletionMode: deletionMode,
      });
      currentProject.value = updatedProject;

      // 如果当前选中的文件在删除的分组中，清空选中状态
      // 让 UI 层（HapticsPanel）来决定是否选择其他文件
      if (!selectedFile.value) {
        selectedFileUuid.value = null;
      }

      // 清除被删除分组中文件的缓存和未保存状态
      // 注意：这里需要在删除前收集要清理的文件UUID，但由于后端已经处理了删除
      // 我们只能清理当前不存在的文件缓存
      const currentFileUuids = new Set(currentProject.value?.files?.map((f) => f.fileUuid) || []);

      // 清理不存在文件的缓存
      for (const [fileUuid] of fileCaches.value.entries()) {
        if (!currentFileUuids.has(fileUuid)) {
          fileCaches.value.delete(fileUuid);
          logger.debug(LogModule.PROJECT, `已清除文件 ${fileUuid} 的缓存`);
        }
      }

      // 清理不存在文件的未保存状态
      for (const fileUuid of unsavedFileUuids.value) {
        if (!currentFileUuids.has(fileUuid)) {
          unsavedFileUuids.value.delete(fileUuid);
          logger.debug(LogModule.PROJECT, `已清除文件 ${fileUuid} 的未保存状态`);
        }
      }

      logger.info(LogModule.PROJECT, `Group ${groupUuid} removed (mode: ${deletionMode}). Project data updated`);
    } catch (error: any) {
      logger.error(LogModule.PROJECT, `Failed to remove group ${groupUuid} (mode: ${deletionMode})`, error);
      const errorMessage = typeof error === "string" ? error : error?.message || "删除分组时发生未知错误";
      throw new Error(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  async function createNewHeFile(groupId: string | null): Promise<void> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      throw new Error("没有打开的项目");
    }

    isLoading.value = true;
    try {
      // 生成唯一文件名
      const fileBaseName = "New_Haptic";
      const existingFiles = currentProject.value.files || [];
      const existingNames = new Set(existingFiles.map((f) => f.name));
      const newFileName = getUniqueHeFileName(fileBaseName, existingNames);

      // 生成.he文件内容
      const heContentJson = generateHeFileContent(null);

      // 调用后端命令创建文件
      const updatedProjectData = await invoke<RealityTapProject>("create_he_file_in_project", {
        projectDirPath: currentProjectDirPath.value,
        fileName: newFileName,
        heContent: heContentJson,
        groupId: groupId,
      });

      // 更新项目数据
      currentProject.value = updatedProjectData;
      _updateLastModifiedTime();

      // 自动选择新创建的文件
      const newFile = updatedProjectData.files?.find(
        (f) => f.name === newFileName && f.group === (updatedProjectData.groups?.find((g) => g.groupUuid === groupId)?.groupUuid || null)
      );

      if (newFile) {
        selectedFileUuid.value = newFile.fileUuid;
        lastCreatedFileUuid.value = newFile.fileUuid;
      } else {
        // Fallback: try to find by name only if group matching failed (should not happen with correct backend)
        const newFileByNameOnly = updatedProjectData.files?.find((f) => f.name === newFileName);
        if (newFileByNameOnly) {
          selectedFileUuid.value = newFileByNameOnly.fileUuid;
          lastCreatedFileUuid.value = newFileByNameOnly.fileUuid;
          logger.warn(LogModule.PROJECT, `New file found by name only for ${newFileName}, group matching might need check`);
        } else {
          logger.error(LogModule.PROJECT, `Could not find the newly created file ${newFileName} in project data to select it`);
        }
      }
    } catch (error) {
      logger.error(LogModule.PROJECT, "Create new .he file failed", error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  }

  async function readFileContentFromBackend(fullPath: string): Promise<string> {
    if (!fullPath) {
      logger.error(LogModule.PROJECT, "readFileContentFromBackend: fullPath is missing");
      return Promise.reject("File path is missing.");
    }
    try {
      const content = await invoke<string>("read_file_content_command", {
        filePath: fullPath,
      });
      return content;
    } catch (error) {
      logger.error(LogModule.PROJECT, `Failed to read file content from backend for path "${fullPath}"`, error);
      // Tauri errors are often strings, or objects with a message field
      const errorMessage = typeof error === "string" ? error : (error as Error).message || "Unknown error reading file from backend";
      return Promise.reject(errorMessage);
    }
  }

  // 项目重命名错误类型
  interface RenameProjectError {
    code: string; // 'NAME_INVALID' | 'NAME_ALREADY_EXISTS' | 'PATH_NOT_FOUND' | 'RENAME_IO_ERROR' | 'METADATA_ERROR' | 'UNKNOWN_ERROR'
    message: string;
  }

  // 重命名项目
  async function renameCurrentProject(newProjectName: string): Promise<boolean> {
    // 防重复调用逻辑 - 检查距离上次调用时间是否太短（1秒内）
    const now = Date.now();
    if (now - lastRenameTime.value < 1000) {
      logger.debug(LogModule.PROJECT, "重命名操作过于频繁，忽略此次调用");
      return false;
    }

    if (!currentProject.value || !currentProjectDirPath.value) {
      logger.warn(LogModule.PROJECT, "Cannot rename project: No project is loaded or directory path is unknown");
      return false;
    }

    if (!newProjectName || newProjectName.trim() === "") {
      logger.warn(LogModule.PROJECT, "Cannot rename project: New project name is empty");
      return false;
    }

    const trimmedNewName = newProjectName.trim();
    if (trimmedNewName === currentProject.value.projectName) {
      logger.debug(LogModule.PROJECT, "Project name unchanged, skipping rename operation");
      return true;
    }

    // 更新最后操作时间
    lastRenameTime.value = now;
    isLoading.value = true;
    const oldProjectName = currentProject.value.projectName;
    const oldDirPath = currentProjectDirPath.value;

    try {
      logger.info(LogModule.PROJECT, `尝试将项目从 "${oldDirPath}" 重命名为 "${trimmedNewName}"`);

      // 调用后端重命名命令 (只重命名文件夹)
      const response = await invoke<{
        new_project_name: string;
        new_project_dir_path: string;
      }>("rename_project", {
        projectPath: oldDirPath, // 参数名在Rust端是 project_path
        newProjectName: trimmedNewName,
      });

      // 更新store中的项目名称和路径 (前端状态)
      if (currentProject.value) {
        currentProject.value.projectName = response.new_project_name;
      }
      currentProjectDirPath.value = response.new_project_dir_path;

      logger.info(LogModule.PROJECT, `Project folder renamed by backend. New name: "${response.new_project_name}", New path: "${response.new_project_dir_path}"`);

      // 现在尝试保存 project.json 到新位置
      try {
        const saveSuccess = await handleSaveCurrentProject();

        if (!saveSuccess) {
          // 文件夹已重命名，但 project.json 保存失败
          // 记录错误，但保持新的前端状态
          logger.error(LogModule.PROJECT, `WARNING: Project folder was renamed to "${response.new_project_dir_path}", but saving project.json failed`);
          throw new Error(`项目文件夹已成功重命名为 "${trimmedNewName}"，但更新项目配置文件失败。请手动检查项目目录或尝试重新保存。`);
        }

        // 如果保存也成功了
        logger.info(LogModule.PROJECT, `Project successfully renamed to "${response.new_project_name}" and project.json saved to "${response.new_project_dir_path}"`);
        return true;
      } catch (saveError) {
        // 捕获保存过程中的错误，但不回滚目录名 - 因为目录已改名成功
        logger.error(LogModule.PROJECT, "Failed to save project after rename", saveError);
        throw new Error(`项目文件夹已成功重命名为 "${trimmedNewName}"，但保存项目数据时发生错误: ${saveError instanceof Error ? saveError.message : "未知错误"}`);
      }
    } catch (error: any) {
      logger.error(LogModule.PROJECT, "Failed to rename project or save metadata", error);

      // 区分是重命名失败还是保存失败
      const isRenameError = !error.message || !error.message.includes("已成功重命名");

      // 如果是重命名失败，回滚前端状态
      if (isRenameError) {
        if (currentProject.value) {
          currentProject.value.projectName = oldProjectName;
        }
        currentProjectDirPath.value = oldDirPath;
      }

      let typedError: RenameProjectError;
      if (error && typeof error === "object" && "code" in error && "message" in error) {
        typedError = error as RenameProjectError; // Rust 端定义的错误
      } else {
        typedError = {
          code: "FRONTEND_ERROR", // 自定义一个前端错误码
          message: error.message || "未知错误",
        };
      }

      // 注意：这些错误消息应该通过国际化系统处理，但由于这是 store 层，
      // 暂时保留英文错误消息，由上层组件负责国际化显示
      let userError: string;
      switch (typedError.code) {
        case "NAME_INVALID":
          userError = "Project name is invalid, please remove special characters or ensure name is not empty";
          break;
        case "NAME_ALREADY_EXISTS":
          userError = "A project with the same name already exists in the target directory, please choose another name";
          break;
        case "PATH_NOT_FOUND":
          userError = "Cannot find current project path, operation failed";
          break;
        case "RENAME_IO_ERROR": // 来自后端文件夹重命名失败
          userError = "Failed to rename project directory, please check if files are in use or if the program has sufficient permissions";
          break;
        case "METADATA_READ_ERROR":
        case "METADATA_WRITE_ERROR":
          userError = `Project configuration processing error: ${typedError.message}`;
          break;
        case "FRONTEND_ERROR":
          // 已定义了详细错误信息的情况（如我们自己抛出的保存失败错误）
          userError = typedError.message;
          break;
        default:
          userError = `Operation failed: ${typedError.message || "Unknown error"}`;
      }
      throw new Error(userError); // 抛给 ProjectEditor.vue 处理
    } finally {
      isLoading.value = false;
    }
  }

  async function addNewGroup(newGroupName: string, parentGroupUuid: string | null): Promise<string | undefined> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      logger.error(LogModule.PROJECT, "Cannot add group: No project is loaded or directory path is unknown");
      throw new Error("没有打开的项目或项目路径未知");
    }

    // 检查目录名是否合法
    const invalidChars = /[<>:"/\\|?*\x00-\x1F]/g;
    const trimmedName = newGroupName.trim();

    if (!trimmedName) {
      throw new Error("分组名称不能为空");
    }

    if (invalidChars.test(trimmedName)) {
      throw new Error('分组名称包含非法字符(如 < > : " / \\ | ? *)');
    }

    if (trimmedName === "." || trimmedName === "..") {
      throw new Error("不能使用 . 或 .. 作为分组名称");
    }

    // 检查是否存在同名分组
    let newGroupPathPrefix = "";
    if (parentGroupUuid && currentProject.value.groups) {
      const parentGroup = currentProject.value.groups.find((g) => g.groupUuid === parentGroupUuid);
      if (parentGroup) {
        newGroupPathPrefix = parentGroup.path + "/";
      }
    }
    const finalNewGroupName = trimmedName;
    const newFullPath = newGroupPathPrefix + finalNewGroupName;

    // 检查是否已存在同名分组
    const existingGroup = currentProject.value.groups?.find((g) => g.path === newFullPath);
    if (existingGroup) {
      throw new Error(`分组 "${finalNewGroupName}" 已存在于当前位置`);
    }

    isLoading.value = true;
    try {
      const updatedProject = await invoke<RealityTapProject>("create_group", {
        projectDirPath: currentProjectDirPath.value,
        newGroupName: finalNewGroupName,
        parentGroupUuidStr: parentGroupUuid, // Backend expects parentGroupUuidStr
      });

      currentProject.value = updatedProject; // Update store with the entire project returned from backend
      // _updateLastModifiedTime(); // Backend already updates it.

      // Find the newly created group to return its UUID or confirm creation.
      const createdGroup = updatedProject.groups?.find((g) => g.path === newFullPath && g.name === finalNewGroupName);

      if (createdGroup) {
        logger.info(LogModule.PROJECT, `Group '${createdGroup.name}' (UUID: ${createdGroup.groupUuid}) created successfully, project updated`);
        return createdGroup.groupUuid;
      } else {
        logger.warn(LogModule.PROJECT, "Group created on backend, but couldn't identify the new group in the returned project data immediately");
        // This might happen if name trimming/path logic slightly differs or if multiple operations are very fast.
        // The project data IS updated, so UI should reflect it.
        return undefined;
      }
    } catch (error) {
      logger.error(LogModule.PROJECT, "Failed to create group via backend", error);
      if (error instanceof Error) throw error;
      throw new Error(String(error));
    } finally {
      isLoading.value = false;
    }
  }

  async function renameGroup(groupUuid: string, newGroupName: string): Promise<void> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      logger.error(LogModule.PROJECT, "renameGroup: No project is loaded or directory path is unknown");
      throw new Error("没有打开的项目或项目路径未知");
    }

    const trimmedNewName = newGroupName.trim();
    if (!trimmedNewName) {
      // Already handled by backend
      throw new Error("分组名称不能为空");
    }

    isLoading.value = true;
    try {
      logger.info(LogModule.PROJECT, `Attempting to rename group ${groupUuid} to "${trimmedNewName}" in project ${currentProjectDirPath.value}`);
      const updatedProject = await invoke<RealityTapProject>("rename_group_command", {
        projectDirPath: currentProjectDirPath.value,
        groupUuidStr: groupUuid,
        newGroupName: trimmedNewName,
      });

      currentProject.value = updatedProject;
      // The selectedFileUuid should not change due to a group rename, as file UUIDs are stable.
      // Backend updates lastModifiedTime and returns the whole project.
      logger.info(LogModule.PROJECT, `Group ${groupUuid} renamed to "${trimmedNewName}" successfully. Project data updated`);
    } catch (error: any) {
      logger.error(LogModule.PROJECT, `Failed to rename group ${groupUuid} to "${trimmedNewName}"`, error);
      const errorMessage = typeof error === "string" ? error : error?.message || "重命名分组时发生未知错误";
      throw new Error(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  // ADD THE NEW ACTION HERE
  async function renameHapticFile(fileUuid: string, newFileNameWithExt: string): Promise<void> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      logger.error(LogModule.PROJECT, "renameHapticFile: No project is loaded or directory path is unknown");
      throw new Error("没有打开的项目或项目路径未知");
    }

    const trimmedNewName = newFileNameWithExt.trim();
    if (!trimmedNewName) {
      logger.error(LogModule.PROJECT, "renameHapticFile: New file name is empty");
      throw new Error("新文件名不能为空");
    }

    // Optional: Frontend check for .he extension, though backend enforces it.
    // if (!trimmedNewName.toLowerCase().endsWith('.he')) {
    //   throw new Error("文件名必须以 .he 结尾");
    // }

    isLoading.value = true;
    try {
      logger.info(LogModule.PROJECT, `Attempting to rename file ${fileUuid} to "${trimmedNewName}" in project ${currentProjectDirPath.value}`);
      const updatedProject = await invoke<RealityTapProject>("rename_file_command", {
        projectDirPath: currentProjectDirPath.value,
        fileUuidStr: fileUuid,
        newFileNameStr: trimmedNewName,
      });

      currentProject.value = updatedProject;
      // _updateLastModifiedTime(); // Backend should handle this, and project data is replaced.
      logger.info(LogModule.PROJECT, `File ${fileUuid} renamed to "${trimmedNewName}" successfully. Project data updated`);
    } catch (rawError: any) {
      // Log the raw error for debugging purposes
      logger.error(LogModule.PROJECT, `Failed to rename file ${fileUuid} to "${trimmedNewName}"`, rawError);
      // Use parseErrorMessage to get a user-friendly message
      const userFriendlyMessage = parseErrorMessage(rawError);
      throw new Error(userFriendlyMessage);
    } finally {
      isLoading.value = false;
    }
  }

  // 1. 新增 checkFileExists 方法
  async function checkFileExists(projectDir: string, relativePath: string): Promise<boolean> {
    try {
      return await invoke<boolean>("check_file_exists_command", {
        projectDirPath: projectDir,
        relativePath: relativePath,
      });
    } catch {
      return false;
    }
  }

  /**
   * 导入视频文件到video目录，并自动在haptics目录下新建同名.he文件
   * @param sourceFilePath 选中的视频文件绝对路径
   * @param parentGroupUuid 目标分组uuid或null
   * @param groupPath 目标分组path（如A/B），用于拼接子目录
   * @param message Naive UI message实例
   * @returns 新建he文件的fileUuid，失败返回null
   */
  async function addVideoFileToProject(sourceFilePath: string, parentGroupUuid: string | null, groupPath: string, message: any): Promise<string | null> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      message.error("没有打开的项目");
      return null;
    }
    try {
      // 1. 计算目标video子目录和文件名
      const videoDir = "video";
      const hapticsDir = "haptics";
      const fileName = sourceFilePath.split(/[/\\]/).pop()!;
      let targetRelativePath = groupPath ? `${groupPath}/${fileName}` : fileName;
      let targetVideoRelativePath = `${videoDir}/${targetRelativePath}`;

      // 2. 检查是否重名
      let finalFileName = fileName;
      let finalVideoRelPath = targetVideoRelativePath;
      let exists = false;
      try {
        exists = await checkFileExists(currentProjectDirPath.value, finalVideoRelPath);
      } catch {}

      // 如果视频已存在，判断.he文件是否也存在
      if (exists) {
        const heFileName = fileName.replace(/\.[^.]+$/, ".he");
        const heRelPath = groupPath ? `${hapticsDir}/${groupPath}/${heFileName}` : `${hapticsDir}/${heFileName}`;
        const heExists = await checkFileExists(currentProjectDirPath.value, heRelPath);
        if (heExists) {
          message.info("视频及.he文件均已存在，已跳过");
          return null;
        } else {
          // 仅视频存在，自动新建.he文件，无需弹窗
          const heContentJson = generateHeFileContent(null);
          const updatedProject2 = await invoke<RealityTapProject>("create_he_file_in_project", {
            projectDirPath: currentProjectDirPath.value,
            fileName: heFileName,
            heContent: heContentJson,
            groupId: parentGroupUuid,
            associatedVideo: groupPath ? `${groupPath}/${fileName}` : fileName,
          });
          currentProject.value = updatedProject2;
          // 选中新建的.he文件
          const newHeFileObj = updatedProject2.files?.find(
            (f) => f.name === heFileName && ((parentGroupUuid && f.group === parentGroupUuid) || (!parentGroupUuid && !f.group))
          );
          if (newHeFileObj) {
            // 先获取视频的音频信息，再设置选中状态
            if (newHeFileObj.associatedVideo) {
              try {
                await fetchAndSetAudioInfo(newHeFileObj.fileUuid, newHeFileObj.associatedVideo);
                logger.debug(LogModule.PROJECT, `已为新创建的 he 文件获取视频音频信息: ${newHeFileObj.associatedVideo}`);
              } catch (err: any) {
                logger.error(LogModule.PROJECT, "获取视频音频信息失败", err);
                // 不阻止文件创建流程，只是记录错误
              }
            }

            // 音频信息获取完成后，再设置选中状态
            selectedFileUuid.value = newHeFileObj.fileUuid;
            lastCreatedFileUuid.value = newHeFileObj.fileUuid;

            message.success("已为现有视频自动生成.he文件");
            return newHeFileObj.fileUuid;
          }
          return null;
        }
      }

      // 3. 只做物理复制，不写project.json
      await invoke("copy_video_file_to_project", {
        projectDirPath: currentProjectDirPath.value,
        sourceFilePath: sourceFilePath,
        targetRelativePath: groupPath ? `${groupPath}/${finalFileName}` : finalFileName,
      });

      // 4. 自动新建.he文件
      const heFileName = finalFileName.replace(/\.[^.]+$/, ".he");
      // 检查haptics目录下是否有同名.he文件，若有自动重命名
      let heFileExists = currentProject.value.files?.some(
        (f) => f.name === heFileName && ((parentGroupUuid && f.group === parentGroupUuid) || (!parentGroupUuid && !f.group))
      );
      let finalHeFileName = heFileName;
      let heCounter = 1;
      while (heFileExists) {
        finalHeFileName = heFileName.replace(/\.he$/, `_${heCounter}.he`);
        heFileExists = currentProject.value.files?.some(
          (f) => f.name === finalHeFileName && ((parentGroupUuid && f.group === parentGroupUuid) || (!parentGroupUuid && !f.group))
        );
        heCounter++;
      }

      const heContentJson = generateHeFileContent(null);
      const updatedProject2 = await invoke<RealityTapProject>("create_he_file_in_project", {
        projectDirPath: currentProjectDirPath.value,
        fileName: finalHeFileName,
        heContent: heContentJson,
        groupId: parentGroupUuid,
        associatedVideo: groupPath ? `${groupPath}/${finalFileName}` : finalFileName,
      });
      currentProject.value = updatedProject2;

      // 5. 返回新建he文件的fileUuid
      const newHeFileObj = updatedProject2.files?.find(
        (f) => f.name === finalHeFileName && ((parentGroupUuid && f.group === parentGroupUuid) || (!parentGroupUuid && !f.group))
      );
      if (newHeFileObj) {
        // 先获取视频的音频信息，再设置选中状态
        if (newHeFileObj.associatedVideo) {
          try {
            await fetchAndSetAudioInfo(newHeFileObj.fileUuid, newHeFileObj.associatedVideo);
            logger.debug(LogModule.PROJECT, `已为新创建的 he 文件获取视频音频信息: ${newHeFileObj.associatedVideo}`);
          } catch (err: any) {
            logger.error(LogModule.PROJECT, "获取视频音频信息失败", err);
            // 不阻止文件创建流程，只是记录错误
          }
        }

        // 音频信息获取完成后，再设置选中状态
        selectedFileUuid.value = newHeFileObj.fileUuid;
        lastCreatedFileUuid.value = newHeFileObj.fileUuid;

        return newHeFileObj.fileUuid;
      }
      return null;
    } catch (error: any) {
      message.error(`视频导入失败: ${error.message || error}`);
      return null;
    }
  }

  /**
   * 导入音频文件到audio目录，并自动在haptics目录下新建同名.he文件
   * @param sourceFilePath 选中的音频文件绝对路径
   * @param parentGroupUuid 目标分组uuid或null
   * @param groupPath 目标分组path（如A/B），用于拼接子目录
   * @param message Naive UI message实例
   * @returns 新建he文件的fileUuid，失败返回null
   */
  async function addAudioFileToProject(sourceFilePath: string, parentGroupUuid: string | null, groupPath: string, message: any): Promise<string | null> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      message.error("没有打开的项目");
      return null;
    }
    try {
      // 0. 首先验证音频文件时长（在复制文件前进行）
      const validationResult = await validateAudioDuration(sourceFilePath);
      if (!validationResult.isValid) {
        message.error(validationResult.errorMessage || "音频文件验证失败");
        return null;
      }

      // 1. 计算目标audio子目录和文件名
      const audioDir = "audio";
      const hapticsDir = "haptics";
      const fileName = sourceFilePath.split(/[/\\]/).pop()!;
      let targetRelativePath = groupPath ? `${groupPath}/${fileName}` : fileName;
      let targetAudioRelativePath = `${audioDir}/${targetRelativePath}`;
      // 2. 检查是否重名
      let finalFileName = fileName;
      let finalAudioRelPath = targetAudioRelativePath;
      let exists = false;
      try {
        exists = await checkFileExists(currentProjectDirPath.value, finalAudioRelPath);
      } catch {}
      // 如果音频已存在，判断.he文件是否也存在
      if (exists) {
        const heFileName = fileName.replace(/\.[^.]+$/, ".he");
        const heRelPath = groupPath ? `${hapticsDir}/${groupPath}/${heFileName}` : `${hapticsDir}/${heFileName}`;
        const heExists = await checkFileExists(currentProjectDirPath.value, heRelPath);
        if (heExists) {
          message.info("音频及.he文件均已存在，已跳过");
          return null;
        } else {
          // 仅音频存在，自动新建.he文件，无需弹窗
          const heContentJson = generateHeFileContent(null);
          const updatedProject2 = await invoke<RealityTapProject>("create_he_file_in_project", {
            projectDirPath: currentProjectDirPath.value,
            fileName: heFileName,
            heContent: heContentJson,
            groupId: parentGroupUuid,
            associatedAudio: groupPath ? `${groupPath}/${fileName}` : fileName,
          });
          currentProject.value = updatedProject2;
          // 选中新建的.he文件
          const newHeFileObj = updatedProject2.files?.find(
            (f) => f.name === heFileName && ((parentGroupUuid && f.group === parentGroupUuid) || (!parentGroupUuid && !f.group))
          );
          if (newHeFileObj) {
            // 先获取音频信息，再设置选中状态
            if (newHeFileObj.associatedAudio) {
              try {
                await fetchAndSetAudioInfo(newHeFileObj.fileUuid, newHeFileObj.associatedAudio);
                logger.debug(LogModule.PROJECT, `已为新创建的 he 文件获取音频信息: ${newHeFileObj.associatedAudio}`);
              } catch (err: any) {
                logger.error(LogModule.PROJECT, "获取音频信息失败", err);
                // 不阻止文件创建流程，只是记录错误
              }
            }

            // 音频信息获取完成后，再设置选中状态
            selectedFileUuid.value = newHeFileObj.fileUuid;
            lastCreatedFileUuid.value = newHeFileObj.fileUuid;

            message.success("已为现有音频自动生成.he文件");
            return newHeFileObj.fileUuid;
          }
          return null;
        }
      }
      // 3. 只做物理复制，不写project.json
      await invoke("copy_audio_file_to_project", {
        projectDirPath: currentProjectDirPath.value,
        sourceFilePath: sourceFilePath,
        targetRelativePath: groupPath ? `${groupPath}/${finalFileName}` : finalFileName,
      });

      // 等待文件系统操作完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 4. 自动新建.he文件
      const heFileName = finalFileName.replace(/\.[^.]+$/, ".he");
      // 检查haptics目录下是否有同名.he文件，若有自动重命名
      let heFileExists = currentProject.value.files?.some(
        (f) => f.name === heFileName && ((parentGroupUuid && f.group === parentGroupUuid) || (!parentGroupUuid && !f.group))
      );
      let finalHeFileName = heFileName;
      let heCounter = 1;
      while (heFileExists) {
        finalHeFileName = heFileName.replace(/\.he$/, `_${heCounter}.he`);
        heFileExists = currentProject.value.files?.some(
          (f) => f.name === finalHeFileName && ((parentGroupUuid && f.group === parentGroupUuid) || (!parentGroupUuid && !f.group))
        );
        heCounter++;
      }
      const heContentJson = generateHeFileContent(null);
      const updatedProject2 = await invoke<RealityTapProject>("create_he_file_in_project", {
        projectDirPath: currentProjectDirPath.value,
        fileName: finalHeFileName,
        heContent: heContentJson,
        groupId: parentGroupUuid,
        associatedAudio: groupPath ? `${groupPath}/${finalFileName}` : finalFileName,
      });
      currentProject.value = updatedProject2;
      // 5. 返回新建he文件的fileUuid
      const newHeFileObj = updatedProject2.files?.find(
        (f) => f.name === finalHeFileName && ((parentGroupUuid && f.group === parentGroupUuid) || (!parentGroupUuid && !f.group))
      );
      if (newHeFileObj) {
        // 先获取音频信息，再设置选中状态
        if (newHeFileObj.associatedAudio) {
          try {
            await fetchAndSetAudioInfo(newHeFileObj.fileUuid, newHeFileObj.associatedAudio);
            logger.debug(LogModule.PROJECT, `已为新创建的 he 文件获取音频信息: ${newHeFileObj.associatedAudio}`);
          } catch (err: any) {
            logger.error(LogModule.PROJECT, "获取音频信息失败", err);
            // 不阻止文件创建流程，只是记录错误
          }
        }

        // 音频信息获取完成后，再设置选中状态
        selectedFileUuid.value = newHeFileObj.fileUuid;
        lastCreatedFileUuid.value = newHeFileObj.fileUuid;

        return newHeFileObj.fileUuid;
      }
      return null;
    } catch (error: any) {
      logger.error(LogModule.PROJECT, "音频导入失败", error);
      message.error(`音频导入失败: ${parseErrorMessage(error)}`);
      return null;
    }
  }

  // 新增：移动文件到分组
  async function moveFile(fileUuid: string, targetGroupUuid: string | null): Promise<void> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      throw new Error("没有打开的项目");
    }
    isLoading.value = true;
    try {
      const updatedProject = await invoke<RealityTapProject>("move_file_command", {
        projectDirPath: currentProjectDirPath.value,
        fileUuidStr: fileUuid,
        newGroupUuid: targetGroupUuid,
      });
      currentProject.value = updatedProject;
      _updateLastModifiedTime();
    } catch (error: any) {
      logger.error(LogModule.PROJECT, "moveFile failed", error);
      throw new Error(parseErrorMessage(error));
    } finally {
      isLoading.value = false;
    }
  }

  // 新增：移动分组到其它分组
  async function moveGroup(groupUuid: string, newParentGroupUuid: string | null): Promise<void> {
    if (!currentProject.value || !currentProjectDirPath.value) {
      throw new Error("没有打开的项目");
    }
    isLoading.value = true;
    try {
      const updatedProject = await invoke<RealityTapProject>("move_group_command", {
        projectDirPath: currentProjectDirPath.value,
        groupUuidStr: groupUuid,
        newParentGroupUuid: newParentGroupUuid,
      });
      currentProject.value = updatedProject;
      _updateLastModifiedTime();
    } catch (error: any) {
      logger.error(LogModule.PROJECT, "moveGroup failed", error);
      throw new Error(parseErrorMessage(error));
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * 获取音频或视频的音频元数据并写入指定 HapticFile，自动保存项目。
   * @param fileUuid HapticFile 的 UUID
   * @param associatedMedia 关联媒体文件的相对路径（音频或视频）
   */
  async function fetchAndSetAudioInfo(fileUuid: string, associatedMedia: string): Promise<void> {
    const maxRetries = 3;
    const retryDelay = 500; // 500ms

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const projectDir = projectPath.value;

        logger.debug(LogModule.PROJECT, `尝试获取音频信息 (第${attempt}次): ${associatedMedia}`);

        // 根据文件扩展名判断是音频还是视频文件
        const fileExtension = associatedMedia.split('.').pop()?.toLowerCase();
        const isVideoFile = fileExtension === 'mp4';

        // 调用相应的后端命令
        logger.debug(LogModule.PROJECT, `调用${isVideoFile ? 'get_video_audio_info' : 'get_audio_info'}命令`, {
          projectDirPath: projectDir,
          relativePath: associatedMedia,
          attempt
        });

        const audioInfo: { durationMs: number; sampleRate: number } | null = isVideoFile
          ? await invoke("get_video_audio_info", {
              projectDirPath: projectDir,
              videoRelativePath: associatedMedia,
            })
          : await invoke("get_audio_info", {
              projectDirPath: projectDir,
              audioRelativePath: associatedMedia,
            });

        logger.debug(LogModule.PROJECT, `后端命令返回结果`, { audioInfo, attempt });

        if (audioInfo) {
          updateFileAudioInfo(fileUuid, {
            durationMs: audioInfo.durationMs,
            sampleRate: audioInfo.sampleRate,
          });
          await handleSaveCurrentProject();
          logger.info(LogModule.PROJECT, `音频信息获取成功: ${associatedMedia}`, audioInfo);
          return; // 成功，退出重试循环
        } else {
          logger.warn(LogModule.PROJECT, `第${attempt}次尝试: ${isVideoFile ? '视频' : '音频'} ${associatedMedia} 元数据解析返回null`);

          if (attempt === maxRetries) {
            // 最后一次尝试失败
            updateFileAudioInfo(fileUuid, null);
            await handleSaveCurrentProject();
            logger.warn(LogModule.PROJECT, `${isVideoFile ? '视频' : '音频'} ${associatedMedia} 元数据解析失败，已导入但无法获取时长`);
            return;
          }

          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      } catch (err) {
        logger.error(LogModule.PROJECT, `第${attempt}次尝试获取媒体文件音频信息失败`, err);

        if (attempt === maxRetries) {
          // 最后一次尝试失败
          updateFileAudioInfo(fileUuid, null);
          await handleSaveCurrentProject();
          logger.error(LogModule.PROJECT, `所有重试都失败，无法获取音频元数据: ${associatedMedia}`, err);
          throw new Error(`媒体文件音频元数据解析失败，已导入但无法获取时长。文件: ${associatedMedia}，错误: ${err instanceof Error ? err.message : String(err)}`);
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  return {
    currentProject,
    currentProjectDirPath,
    isLoading,
    selectedFileUuid,
    lastCreatedFileUuid,

    projectName,
    hasFiles,
    selectedFile,
    groups,
    getFilesByGroup,

    projectPath,
    activeProject,

    // 未保存状态管理
    hasUnsavedChanges,
    markFileAsUnsaved,
    markFileAsSaved,
    isFileUnsaved,
    clearAllUnsavedStates,
    saveHeFile,

    // 文件缓存管理
    setFileCache,
    updateFileCacheEvents,
    getFileCache,
    hasFileCache,
    clearFileCache,
    clearAllFileCaches,

    loadProject,
    selectFile,
    updateHapticFileMetadata,
    removeHapticFile,
    addNewGroup,
    updateGroup,
    removeGroup,
    createNewHeFile,
    renameCurrentProject,
    renameGroup,
    renameHapticFile,

    handleLoadProject,
    handleCreateNewProject,
    handleSaveCurrentProject,

    setProjectData,
    clearProjectData,
    getResolvedFilePath,
    readFileContentFromBackend,
    addAudioFileToProject,
    addVideoFileToProject,
    moveFile,
    moveGroup,
    updateFileAudioInfo,
    fetchAndSetAudioInfo,
  };
});

/**
 * 高效的事件数组比较（避免JSON序列化）
 */
function areEventsArraysEqual(events1: RenderableEvent[], events2: RenderableEvent[]): boolean {
  if (events1.length !== events2.length) {
    return false;
  }

  return events1.every((event1, index) => {
    const event2 = events2[index];

    // 基础字段比较
    if (event1.id !== event2.id ||
        Math.abs(event1.startTime - event2.startTime) >= 0.001 ||
        event1.type !== event2.type) {
      return false;
    }

    // 根据事件类型比较特定字段
    if (event1.type === 'transient' && event2.type === 'transient') {
      const t1 = event1 as any;
      const t2 = event2 as any;
      return Math.abs((t1.intensity || 0) - (t2.intensity || 0)) < 0.001 &&
             Math.abs((t1.frequency || 0) - (t2.frequency || 0)) < 0.001;
    } else if (event1.type === 'continuous' && event2.type === 'continuous') {
      const c1 = event1 as any;
      const c2 = event2 as any;
      return Math.abs((c1.eventIntensity || 0) - (c2.eventIntensity || 0)) < 0.001 &&
             Math.abs((c1.eventFrequency || 0) - (c2.eventFrequency || 0)) < 0.001 &&
             Math.abs((c1.duration || 0) - (c2.duration || 0)) < 0.001;
    }

    return true;
  });
}
