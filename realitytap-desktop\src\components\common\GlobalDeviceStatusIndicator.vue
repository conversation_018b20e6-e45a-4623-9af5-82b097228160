<template>
  <div class="global-device-status" @click="handleClick">
    <!-- 设备状态图标 -->
    <div class="status-icon-container">
      <!-- 使用 ionicons 组件图标 -->
      <n-icon v-if="statusIcon" :component="statusIcon" :color="statusColor" size="16" class="status-icon" />
      <!-- 使用 SVG 图片图标 -->
      <img
        v-else
        :src="DevicesIconUrl"
        :style="{ filter: `brightness(0) saturate(100%) invert(1)`, opacity: 0.7 }"
        width="16"
        height="16"
        class="status-icon"
        :alt="t('device.status.noDevices')"
      />
      <!-- 连接数量徽章 -->
      <div v-if="connectedCount > 0" class="connection-badge" :style="{ backgroundColor: statusColor }">
        {{ connectedCount }}
      </div>
    </div>

    <!-- 状态文本（可选显示） -->
    <span v-if="showText" class="status-text">
      {{ statusText }}
    </span>

    <!-- 悬浮提示 -->
    <n-tooltip placement="bottom" trigger="hover">
      <template #trigger>
        <div class="tooltip-trigger"></div>
      </template>
      <div class="device-tooltip">
        <div class="tooltip-header">{{ t("device.status.title") }}</div>
        <div class="tooltip-stats">
          <div class="stat-item">
            <span class="stat-label">{{ t("device.status.total") }}:</span>
            <span class="stat-value">{{ statistics.totalDevices }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ t("device.status.connected") }}:</span>
            <span class="stat-value connected">{{ statistics.connectedDevices }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ t("device.status.disconnected") }}:</span>
            <span class="stat-value disconnected">{{ statistics.disconnectedDevices }}</span>
          </div>
          <div v-if="statistics.errorDevices > 0" class="stat-item">
            <span class="stat-label">{{ t("device.status.error") }}:</span>
            <span class="stat-value error">{{ statistics.errorDevices }}</span>
          </div>
        </div>
        <div v-if="defaultDevice" class="tooltip-default">
          <span class="default-label">{{ t("device.status.defaultDevice") }}:</span>
          <span class="default-name">{{ defaultDevice.name }}</span>
        </div>
        <div class="tooltip-action">{{ t("device.status.clickToOpen") }}</div>
      </div>
    </n-tooltip>

    <!-- 设备管理抽屉 -->
    <n-drawer v-model:show="showDeviceDrawer" :width="800" placement="right" :trap-focus="false" :block-scroll="false">
      <n-drawer-content :title="t('device.management.title')" closable>
        <DeviceManager />
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from "vue";
import { NIcon, NTooltip, NDrawer, NDrawerContent } from "naive-ui";
import { CheckmarkCircle as ConnectedIcon, Warning as ErrorIcon, Close as DisconnectedIcon } from "@vicons/ionicons5";
import DevicesIconUrl from "@/assets/ic_devices.svg";
import { useDeviceManager } from "@/composables/useDeviceManager";
import { useI18n } from "@/composables/useI18n";
import DeviceManager from "@/components/device/DeviceManager.vue";

// === Props ===
interface Props {
  showText?: boolean;
  compact?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showText: false,
  compact: true,
});

// 使用 props 来避免 TypeScript 警告
const { showText } = props;

// === 本地状态 ===
const showDeviceDrawer = ref(false);

// === 组合函数 ===
const { t } = useI18n();
const deviceManager = useDeviceManager({
  autoInitialize: true,
  enableAutoScan: true,
  enableLogging: false,
});

// === 计算属性 ===
const statistics = computed(() => deviceManager.statistics);
const defaultDevice = computed(() => deviceManager.defaultDevice.value);
const connectedCount = computed(() => statistics.value.connectedDevices);
const totalCount = computed(() => statistics.value.totalDevices);
const errorCount = computed(() => statistics.value.errorDevices);

// 状态图标类型
const statusIconType = computed(() => {
  if (errorCount.value > 0) {
    return "error";
  } else if (connectedCount.value > 0) {
    return "connected";
  } else if (totalCount.value > 0) {
    return "disconnected";
  } else {
    return "devices";
  }
});

// 状态图标组件
const statusIcon = computed(() => {
  if (statusIconType.value === "error") {
    return ErrorIcon;
  } else if (statusIconType.value === "connected") {
    return ConnectedIcon;
  } else if (statusIconType.value === "disconnected") {
    return DisconnectedIcon;
  } else {
    return null; // 使用 SVG 图片
  }
});

// 状态颜色
const statusColor = computed(() => {
  if (errorCount.value > 0) {
    return "#d03050"; // 错误红色
  } else if (connectedCount.value > 0) {
    return "#18a058"; // 连接绿色
  } else if (totalCount.value > 0) {
    return "#909399"; // 断开灰色
  } else {
    return "#606266"; // 无设备深灰色
  }
});

// 状态文本
const statusText = computed(() => {
  if (errorCount.value > 0) {
    return t("device.status.errorCount", { count: errorCount.value });
  } else if (connectedCount.value > 0) {
    return t("device.status.deviceCount", { count: connectedCount.value });
  } else if (totalCount.value > 0) {
    return t("device.status.disconnected");
  } else {
    return t("device.status.noDevices");
  }
});

// === 事件处理 ===
const handleClick = () => {
  showDeviceDrawer.value = true;
};

// ESC键处理函数
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === "Escape" && showDeviceDrawer.value) {
    showDeviceDrawer.value = false;
  }
};

// 组件挂载时添加键盘事件监听
onMounted(() => {
  document.addEventListener("keydown", handleKeyDown);
});

// 组件卸载时移除键盘事件监听
onUnmounted(() => {
  document.removeEventListener("keydown", handleKeyDown);
});
</script>

<style scoped>
.global-device-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 9px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
  min-height: 38px;
  max-height: 38px;
}

.global-device-status:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #7fe7c4 !important;
}

/* 悬停时强制覆盖图标颜色 */
.global-device-status:hover .status-icon {
  color: #7fe7c4 !important;
  filter: brightness(0) saturate(100%) invert(84%) sepia(36%) saturate(1347%) hue-rotate(120deg) brightness(98%) contrast(89%) !important;
}

/* 悬停时强制覆盖文字颜色 */
.global-device-status:hover .status-text {
  color: #7fe7c4 !important;
}

.status-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  width: 18px;
  height: 18px;
}

.connection-badge {
  position: absolute;
  top: -4px;
  right: -6px;
  min-width: 14px;
  height: 14px;
  border-radius: 7px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  color: white;
  padding: 0 3px;
  box-shadow: 0 0 0 1px #1a1a1a;
}

.status-text {
  font-size: 12px;
  color: #e6e6e6;
  font-weight: 500;
  white-space: nowrap;
}

.tooltip-trigger {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.device-tooltip {
  padding: 12px;
  min-width: 200px;
}

.tooltip-header {
  font-size: 14px;
  font-weight: 600;
  color: var(--n-text-color-1);
  margin-bottom: 8px;
  border-bottom: 1px solid var(--n-divider-color);
  padding-bottom: 6px;
}

.tooltip-stats {
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.stat-label {
  color: var(--n-text-color-2);
}

.stat-value {
  font-weight: 600;
  color: var(--n-text-color-1);
}

.stat-value.connected {
  color: #18a058;
}

.stat-value.disconnected {
  color: #909399;
}

.stat-value.error {
  color: #d03050;
}

.tooltip-default {
  margin-bottom: 8px;
  padding: 6px 8px;
  background: var(--n-color-target);
  border-radius: 4px;
  font-size: 12px;
}

.default-label {
  color: var(--n-text-color-2);
  margin-right: 6px;
}

.default-name {
  color: var(--n-text-color-1);
  font-weight: 500;
}

.tooltip-action {
  font-size: 11px;
  color: var(--n-text-color-3);
  text-align: center;
  padding-top: 6px;
  border-top: 1px solid var(--n-divider-color);
}

/* 紧凑模式 */
.global-device-status.compact {
  padding: 2px 6px;
}

.global-device-status.compact .status-text {
  font-size: 11px;
}

.global-device-status.compact .connection-badge {
  min-width: 12px;
  height: 12px;
  font-size: 9px;
  top: -3px;
  right: -5px;
}
</style>
