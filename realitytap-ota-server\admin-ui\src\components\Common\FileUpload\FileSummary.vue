<template>
  <div class="file-summary">
    <n-card size="small">
      <template #header>
        <n-space align="center">
          <n-icon size="20">
            <DocumentOutline />
          </n-icon>
          <span>批量上传 ({{ fileCount }} 个文件)</span>
        </n-space>
      </template>

      <n-space vertical>
        <!-- 安装包文件信息 -->
        <div v-if="installerFile" class="file-summary-item">
          <n-space align="center" justify="space-between">
            <n-space align="center">
              <n-icon size="16" color="#18a058">
                <DocumentOutline />
              </n-icon>
              <span>{{ installerFile.name }}</span>
              <n-tag size="small" type="success">安装包</n-tag>
            </n-space>
            <n-text depth="3">{{ formatBytes(installerFile.size) }}</n-text>
          </n-space>
        </div>

        <!-- 签名文件信息 -->
        <div v-if="signatureFile && signatureValue" class="file-summary-item">
          <n-space align="center" justify="space-between">
            <n-space align="center">
              <n-icon size="16" color="#f0a020">
                <DocumentOutline />
              </n-icon>
              <n-tag size="small" type="warning">签名内容</n-tag>
            </n-space>
            <n-text depth="3">{{ formatBytes(signatureFile.size) }}</n-text>
          </n-space>
          <!-- 显示签名内容预览 -->
          <div class="signature-preview">
            <n-text depth="3" style="font-family: monospace; font-size: 11px; word-break: break-all; line-height: 1.4">
              {{ signatureValue.length > 200 ? signatureValue.substring(0, 200) + '...' : signatureValue }}
            </n-text>
          </div>
        </div>

        <!-- 哈希文件信息 -->
        <div v-if="hashFile && hashValue" class="file-summary-item">
          <n-space align="center" justify="space-between">
            <n-space align="center">
              <n-icon size="16" color="#2080f0">
                <DocumentOutline />
              </n-icon>
              <span style="font-family: monospace; font-size: 12px">{{ hashValue }}</span>
              <n-tag size="small" type="info">哈希值</n-tag>
            </n-space>
            <n-text depth="3">{{ formatBytes(hashFile.size) }}</n-text>
          </n-space>
        </div>
      </n-space>

      <n-descriptions :column="2" size="small" style="margin-top: 12px">
        <n-descriptions-item label="总文件大小">
          {{ formatBytes(totalSize) }}
        </n-descriptions-item>
        <n-descriptions-item label="文件数量">
          {{ fileCount }}
        </n-descriptions-item>
      </n-descriptions>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { DocumentOutline } from '@vicons/ionicons5';
import { NCard, NDescriptions, NDescriptionsItem, NIcon, NSpace, NTag, NText } from 'naive-ui';
import { computed } from 'vue';

// Props
interface Props {
  installerFile: File | null;
  signatureFile: File | null;
  hashFile: File | null;
  signatureValue: string | null;
  hashValue: string | null;
}

const props = defineProps<Props>();

// 计算属性
const fileCount = computed(() => {
  let count = 0;
  if (props.installerFile) count++;
  if (props.signatureFile) count++;
  if (props.hashFile) count++;
  return count;
});

const totalSize = computed(() => {
  let size = 0;
  if (props.installerFile) size += props.installerFile.size;
  if (props.signatureFile) size += props.signatureFile.size;
  if (props.hashFile) size += props.hashFile.size;
  return size;
});

// 工具函数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<style scoped>
.file-summary {
  margin: 16px 0;
}

.file-summary-item {
  padding: 8px 0;
  border-bottom: 1px solid var(--n-border-color);
}

.file-summary-item:last-child {
  border-bottom: none;
}

.signature-preview {
  margin-top: 8px;
  padding: 8px;
  background-color: var(--n-modal-color);
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}
</style>
