<!--
  安全限制功能演示组件
  仅在开发环境中显示，用于测试和演示安全限制功能
-->
<template>
  <div v-if="isDevelopment" class="security-demo">
    <n-card title="🔒 安全限制功能演示" class="demo-card">
      <div class="demo-content">
        <!-- 状态显示 -->
        <div class="status-section">
          <h3>当前状态</h3>
          <n-tag :type="status.enabled ? 'success' : 'warning'">
            {{ status.enabled ? "已启用" : "已禁用" }}
          </n-tag>
          <n-tag type="info" style="margin-left: 8px">
            {{ status.environment === "development" ? "开发环境" : "生产环境" }}
          </n-tag>
        </div>

        <!-- 功能测试区域 -->
        <div class="test-section">
          <h3>功能测试</h3>

          <!-- 键盘事件测试 -->
          <div class="test-group">
            <h4>危险按键测试</h4>
            <p>在生产环境中，以下按键将被拦截：</p>
            <ul>
              <li><kbd>F5</kbd> - 刷新页面</li>
              <li><kbd>Ctrl</kbd> + <kbd>R</kbd> - 刷新页面</li>
              <li><kbd>F12</kbd> - 开发者工具</li>
              <li><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>I</kbd> - 开发者工具</li>
              <li><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>J</kbd> - 控制台</li>
              <li><kbd>Ctrl</kbd> + <kbd>U</kbd> - 查看源代码</li>
            </ul>
            <n-alert type="info" style="margin-top: 8px"> 在开发环境中这些按键仍然有效，只有在生产环境中才会被拦截。 </n-alert>
          </div>

          <!-- 文本选择测试 -->
          <div class="test-group">
            <h4>文本选择测试</h4>
            <div class="selectable-text" :class="{ 'production-mode': !isDevelopment }">
              这是一段测试文本。在生产环境中，用户无法选择这段文字。 尝试用鼠标拖拽选择这段文字，在生产环境中将被阻止。
            </div>
            <n-alert type="info" style="margin-top: 8px"> 在开发环境中文本仍可选择，生产环境中将被禁用。 </n-alert>
          </div>

          <!-- 右键菜单测试 -->
          <div class="test-group">
            <h4>右键菜单测试</h4>
            <div class="right-click-area">在这个区域右键点击。生产环境中右键菜单将被禁用。</div>
            <n-alert type="info" style="margin-top: 8px"> 在开发环境中右键菜单仍然可用，生产环境中将被禁用。 </n-alert>
          </div>

          <!-- 拖拽测试 -->
          <div class="test-group">
            <h4>拖拽测试</h4>
            <div class="draggable-area">
              <img src="/vite.svg" alt="测试图片" class="test-image" draggable="true" />
              <p>尝试拖拽上面的图片。在生产环境中拖拽将被禁用。</p>
            </div>
          </div>
        </div>

        <!-- 控制按钮 -->
        <div class="control-section">
          <h3>控制面板</h3>
          <n-space>
            <n-button type="primary" @click="toggleRestrictions" :disabled="!isDevelopment"> {{ status.enabled ? "禁用" : "启用" }}安全限制 </n-button>
            <n-button @click="refreshStatus"> 刷新状态 </n-button>
            <n-button @click="showInfo"> 显示详细信息 </n-button>
          </n-space>
          <n-alert v-if="!isDevelopment" type="warning" style="margin-top: 8px"> 生产环境中无法手动控制安全限制，它们将自动启用。 </n-alert>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { NCard, NTag, NAlert, NSpace, NButton, useMessage } from "naive-ui";
import { securityRestrictions } from "@/utils/security-restrictions";

const message = useMessage();

// 响应式状态
const status = ref({
  enabled: false,
  environment: "development",
});

// 计算属性
const isDevelopment = computed(() => import.meta.env.DEV);

// 刷新状态
const refreshStatus = () => {
  status.value = securityRestrictions.getStatus();
};

// 切换安全限制（仅开发环境）
const toggleRestrictions = () => {
  if (!isDevelopment.value) {
    message.warning("生产环境中无法手动控制安全限制");
    return;
  }

  if (status.value.enabled) {
    securityRestrictions.disable();
    message.success("安全限制已禁用");
  } else {
    securityRestrictions.enable();
    message.success("安全限制已启用");
  }

  refreshStatus();
};

// 显示详细信息
const showInfo = () => {
  const info = `
环境: ${status.value.environment}
状态: ${status.value.enabled ? "已启用" : "已禁用"}
当前时间: ${new Date().toLocaleString()}
用户代理: ${navigator.userAgent}
  `.trim();

  message.info(info);
};

// 组件挂载时刷新状态
onMounted(() => {
  refreshStatus();
});
</script>

<style scoped>
.security-demo {
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-card {
  margin-bottom: 16px;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.status-section,
.test-section,
.control-section {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 16px;
}

.test-group {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--card-color);
  border-radius: 4px;
}

.test-group h4 {
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.selectable-text {
  padding: 12px;
  background: var(--input-color);
  border-radius: 4px;
  border: 1px dashed var(--border-color);
  margin: 8px 0;
  line-height: 1.5;
}

.right-click-area {
  padding: 20px;
  background: var(--input-color);
  border: 2px dashed var(--primary-color);
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  margin: 8px 0;
}

.right-click-area:hover {
  background: var(--primary-color-hover);
}

.draggable-area {
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  text-align: center;
  margin: 8px 0;
}

.test-image {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
  cursor: grab;
}

.test-image:active {
  cursor: grabbing;
}

kbd {
  background: var(--code-color);
  border: 1px solid var(--border-color);
  border-radius: 3px;
  padding: 2px 4px;
  font-size: 0.85em;
  font-family: monospace;
}

ul {
  margin: 8px 0;
  padding-left: 20px;
}

li {
  margin: 4px 0;
}

h3 {
  margin: 0 0 12px 0;
  color: var(--text-color-1);
}

h4 {
  color: var(--text-color-2);
}
</style>
