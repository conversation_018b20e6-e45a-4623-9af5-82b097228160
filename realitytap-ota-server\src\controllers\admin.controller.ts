import { config } from '@/config/server.config';
import { authMiddleware } from '@/middleware/auth.middleware';
import { authService } from '@/services/auth.service';
import { batchUploadService } from '@/services/batch-upload.service';
import { fileService } from '@/services/file.service';
import {
  AuthenticatedRequest,
  BatchUploadRequest,
  LoginRequest,
} from '@/types/auth.types';
import { ErrorResponse, SuccessResponse } from '@/types/server.types';
import { FileUtil } from '@/utils/file.util';
import { logger } from '@/utils/logger.util';
import { NextFunction, Request, Response, Router } from 'express';
import fs from 'fs-extra';
import multer from 'multer';
import path from 'path';
import { z } from 'zod';

const router: Router = Router();



// 登录请求验证模式
const LoginRequestSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

/**
 * 管理员登录
 * POST /api/v1/admin/login
 */
router.post('/login', async (req: Request, res: Response, next: NextFunction) => {
  try {
    // 验证请求数据
    const validatedData = LoginRequestSchema.parse(req.body);
    const loginRequest: LoginRequest = validatedData;

    // 执行登录
    const loginResponse = await authService.login(loginRequest);

    // 构建响应
    const response: SuccessResponse = {
      success: true,
      data: loginResponse,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('管理员登录失败', {
      error: {
        name: error.name || 'Error',
        message: error.message,
        code: error.code,
        stack: error.stack,
        type: typeof error,
      },
      loginAttempt: {
        username: req.body?.username,
        providedPassword: req.body?.password ? '[REDACTED]' : undefined,
        requestBody: req.body ? Object.keys(req.body) : [],
      },
      request: {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.url,
        headers: {
          'content-type': req.get('Content-Type'),
          'origin': req.get('Origin'),
          'referer': req.get('Referer'),
        },
      },
      timing: {
        timestamp: new Date().toISOString(),
      },
      module: 'user_operation',
      operation: 'login_failed',
    });

    if (error.message === 'Invalid credentials') {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_CREDENTIALS',
          message: 'Invalid username or password',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(401).json(response);
    } else {
      next(error);
    }
  }
});

/**
 * 管理员登出
 * POST /api/v1/admin/logout
 */
router.post('/logout', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    logger.info('Admin user logged out', {
      username: req.user?.username,
      ip: req.ip,
    });

    const response: SuccessResponse = {
      success: true,
      data: { message: 'Logged out successfully' },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Logout error', { error: error.message, username: req.user?.username });
    res.status(500).json({
      success: false,
      error: {
        code: 'LOGOUT_ERROR',
        message: 'Error during logout',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    });
  }
});

/**
 * 获取管理员信息
 * GET /api/v1/admin/profile
 */
router.get('/profile', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const response: SuccessResponse = {
      success: true,
      data: {
        user: req.user,
        serverInfo: {
          version: '1.0.0',
          nodeVersion: process.version,
          platform: process.platform,
          uptime: process.uptime(),
        },
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('获取管理员信息失败', {
      error: {
        name: error.name || 'Error',
        message: error.message,
        code: error.code,
        stack: error.stack,
        type: typeof error,
      },
      user: {
        username: req.user?.username,
        id: req.user?.id,
        role: req.user?.role,
      },
      request: {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.url,
      },
      serverInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
      },
      timing: {
        timestamp: new Date().toISOString(),
      },
      module: 'user_operation',
      operation: 'get_profile_failed',
    });
    res.status(500).json({
      success: false,
      error: {
        code: 'PROFILE_ERROR',
        message: 'Failed to get profile information',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    });
  }
});

/**
 * 刷新token
 * POST /api/v1/admin/refresh
 */
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: 'Authorization token is required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(401).json(response);
      return;
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: 'Token is required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(401).json(response);
      return;
    }

    const refreshResponse = authService.refreshToken(token);

    const response: SuccessResponse = {
      success: true,
      data: refreshResponse,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Token refresh failed', { error: error.message, ip: req.ip });

    const response: ErrorResponse = {
      success: false,
      error: {
        code: 'REFRESH_FAILED',
        message: 'Unable to refresh token',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };
    res.status(401).json(response);
  }
});

/**
 * 获取详细统计信息
 * GET /api/v1/admin/stats
 */
router.get('/stats', authMiddleware, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const memUsage = process.memoryUsage();

    // 获取OTA统计信息
    const releasesPath = config.storage.releasesPath;
    let totalVersions = 0;
    let totalSize = 0;
    const platformStats: Record<string, number> = {};
    const archStats: Record<string, number> = {};

    try {
      const files = await fs.readdir(releasesPath);

      for (const filename of files) {
        if (filename.startsWith('.')) continue;

        const filePath = path.join(releasesPath, filename);
        const stats = await fs.stat(filePath);

        totalVersions++;
        totalSize += stats.size;

        // 统计平台分布
        const platformMatch = filename.match(/(windows|macos|linux)/i);
        if (platformMatch && platformMatch[1]) {
          const platform = platformMatch[1].toLowerCase();
          platformStats[platform] = (platformStats[platform] || 0) + 1;
        }

        // 统计架构分布
        const archMatch = filename.match(/(x86_64|aarch64|x86)/i);
        if (archMatch && archMatch[1]) {
          const arch = archMatch[1].toLowerCase();
          archStats[arch] = (archStats[arch] || 0) + 1;
        }
      }
    } catch (error) {
      logger.warn('Failed to read releases directory for stats', { error });
    }

    const stats = {
      system: {
        uptime: process.uptime(),
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch,
        memory: {
          used: memUsage.heapUsed,
          total: memUsage.heapTotal,
          percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
        },
        pid: process.pid,
        startTime: new Date(Date.now() - process.uptime() * 1000).toISOString(),
      },
      ota: {
        totalVersions,
        totalDownloads: 0, // TODO: 实现下载统计
        totalSize,
        platformDistribution: platformStats,
        architectureDistribution: archStats,
        averageFileSize: totalVersions > 0 ? Math.round(totalSize / totalVersions) : 0,
      },
      storage: {
        basePath: config.storage.basePath,
        releasesPath: config.storage.releasesPath,
        metadataPath: config.storage.metadataPath,
        logsPath: config.storage.logsPath,
      },
    };

    const response: SuccessResponse = {
      success: true,
      data: stats,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('获取管理员统计信息失败', {
      error: {
        name: error.name || 'Error',
        message: error.message,
        code: error.code,
        stack: error.stack,
        type: typeof error,
      },
      user: {
        username: req.user?.username,
        id: req.user?.id,
        role: req.user?.role,
      },
      request: {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.url,
      },
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        storageConfig: {
          releasesPath: config.storage.releasesPath,
          logsPath: config.storage.logsPath,
        },
      },
      timing: {
        timestamp: new Date().toISOString(),
      },
      module: 'user_operation',
      operation: 'get_stats_failed',
    });
    res.status(500).json({
      success: false,
      error: {
        code: 'STATS_ERROR',
        message: 'Failed to get statistics',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    });
  }
});

// 配置文件上传
// Multer 配置 - 现在仅用于分块上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(config.storage.basePath, 'temp');
    fs.ensureDirSync(uploadPath);
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // 保持原始文件名，添加时间戳避免冲突
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}-${timestamp}${ext}`);
  },
});

// 注意：此 multer 实例现在仅用于分块上传，简单上传功能已弃用
const upload = multer({
  storage,
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB
  },
  fileFilter: (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = ['.exe', '.dmg', '.deb', '.rpm', '.zip', '.tar.gz', '.msi', '.pkg', '.sig'];
    const ext = path.extname(file.originalname).toLowerCase();

    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error(`不支持的文件类型: ${ext}`));
    }
  },
});

// 简单上传功能已弃用，请使用分块上传 (/upload/init, /upload/chunk, /upload/complete)

/**
 * 解析文件名获取版本信息
 */
function parseFileMetadata(filename: string) {
  // 改进的文件名解析逻辑
  const versionMatch = filename.match(/(\d+\.\d+\.\d+)/);

  // 平台检测 - 支持更多格式
  let platform = 'unknown';
  if (/windows|win|\.exe|\.msi/i.test(filename)) {
    platform = 'windows';
  } else if (/macos|mac|osx|\.dmg|\.pkg/i.test(filename)) {
    platform = 'macos';
  } else if (/linux|\.deb|\.rpm|\.appimage/i.test(filename)) {
    platform = 'linux';
  }

  // 架构检测 - 支持更多格式
  let architecture = 'unknown';
  if (/x86_64|x64|amd64/i.test(filename)) {
    architecture = 'x86_64';
  } else if (/aarch64|arm64/i.test(filename)) {
    architecture = 'aarch64';
  } else if (/x86|i386|i686/i.test(filename)) {
    architecture = 'x86';
  }

  // 渠道检测
  let channel = 'stable';
  if (/alpha/i.test(filename)) {
    channel = 'alpha';
  } else if (/beta/i.test(filename)) {
    channel = 'beta';
  }

  return {
    version: versionMatch?.[1] || 'unknown',
    platform,
    architecture,
    channel,
  };
}

/**
 * 获取版本元数据
 */
async function getVersionMetadata(filename: string): Promise<any | null> {
  try {
    const metadataPath = path.join(config.storage.metadataPath, 'versions', `${filename}.json`);
    if (await fs.pathExists(metadataPath)) {
      return await fs.readJSON(metadataPath);
    }
  } catch (error) {
    logger.warn('Failed to read version metadata', { filename, error });
  }
  return null;
}

/**
 * 检查签名信息（优先从数据库，然后从文件系统）
 */
async function getSignatureInfo(filename: string, channel: string, databaseSignature?: string): Promise<{
  hasSignature: boolean;
  signatureFilename?: string;
  signature?: string;
}> {
  try {
    // 优先使用数据库中的签名信息
    if (databaseSignature && databaseSignature.trim()) {
      return {
        hasSignature: true,
        signatureFilename: `${filename}.sig`,
        signature: databaseSignature.trim(),
      };
    }

    // 如果数据库中没有签名，尝试从文件系统读取（向后兼容）
    const signatureFilename = `${filename}.sig`;
    const signaturePath = path.join(config.storage.releasesPath, channel, signatureFilename);

    if (await fs.pathExists(signaturePath)) {
      try {
        const signature = await fs.readFile(signaturePath, 'utf8');
        return {
          hasSignature: true,
          signatureFilename,
          signature: signature.trim(),
        };
      } catch (error) {
        logger.warn('Failed to read signature file', { signaturePath, error });
        return {
          hasSignature: true,
          signatureFilename,
        };
      }
    }

    // 也检查根目录（向后兼容）
    const rootSignaturePath = path.join(config.storage.releasesPath, signatureFilename);
    if (await fs.pathExists(rootSignaturePath)) {
      try {
        const signature = await fs.readFile(rootSignaturePath, 'utf8');
        return {
          hasSignature: true,
          signatureFilename,
          signature: signature.trim(),
        };
      } catch (error) {
        logger.warn('Failed to read signature file from root', { rootSignaturePath, error });
        return {
          hasSignature: true,
          signatureFilename,
        };
      }
    }

    return { hasSignature: false };
  } catch (error) {
    logger.warn('Failed to check signature file', { filename, channel, error });
    return { hasSignature: false };
  }
}

/**
 * 检查哈希信息（从数据库checksum字段或物理.hash文件）
 */
async function getHashFileInfo(filename: string, channel: string, checksum?: string): Promise<{
  hasHashFile: boolean;
  hashFilename?: string;
}> {
  try {
    // 优先检查数据库中是否有checksum值
    if (checksum && checksum.trim()) {
      return {
        hasHashFile: true,
        hashFilename: `${filename}.hash`, // 虚拟文件名，用于显示
      };
    }

    // 向后兼容：检查物理.hash文件是否存在
    const hashFilename = `${filename}.hash`;
    const hashPath = path.join(config.storage.releasesPath, channel, hashFilename);

    if (await fs.pathExists(hashPath)) {
      return {
        hasHashFile: true,
        hashFilename,
      };
    }

    // 也检查根目录（向后兼容）
    const rootHashPath = path.join(config.storage.releasesPath, hashFilename);
    if (await fs.pathExists(rootHashPath)) {
      return {
        hasHashFile: true,
        hashFilename,
      };
    }

    return { hasHashFile: false };
  } catch (error) {
    logger.warn('Failed to check hash file', { filename, channel, error });
    return { hasHashFile: false };
  }
}

/**
 * 扫描所有渠道目录获取文件列表
 */
async function getAllReleaseFiles(): Promise<Array<{ filename: string; filePath: string; channel: string }>> {
  const releasesPath = config.storage.releasesPath;
  const allFiles: Array<{ filename: string; filePath: string; channel: string }> = [];

  try {
    const items = await fs.readdir(releasesPath);

    for (const item of items) {
      const itemPath = path.join(releasesPath, item);
      const stats = await fs.stat(itemPath);

      if (stats.isDirectory()) {
        // 这是一个渠道目录，扫描其中的文件
        try {
          const channelFiles = await fs.readdir(itemPath);
          for (const filename of channelFiles) {
            if (!filename.startsWith('.') && !filename.endsWith('.sig')) {
              // 过滤掉签名文件，只保留安装包文件
              const filePath = path.join(itemPath, filename);
              const fileStats = await fs.stat(filePath);
              if (fileStats.isFile()) {
                allFiles.push({
                  filename,
                  filePath,
                  channel: item, // 目录名就是渠道名
                });
              }
            }
          }
        } catch (error) {
          logger.warn('Failed to read channel directory', { channel: item, error });
        }
      } else if (stats.isFile() && !item.startsWith('.') && !item.endsWith('.sig')) {
        // 这是根目录中的文件（向后兼容），过滤掉签名文件
        allFiles.push({
          filename: item,
          filePath: itemPath,
          channel: 'stable', // 默认为stable渠道
        });
      }
    }
  } catch (error) {
    logger.error('Failed to scan releases directory', { error });
  }

  return allFiles;
}

/**
 * 获取版本列表
 * GET /api/v1/admin/versions
 */
router.get('/versions', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    // 使用数据库获取版本信息
    const { ChannelDAO } = await import('@/dao/channel.dao');
    const { VersionDAO } = await import('@/dao/version.dao');
    const { PlatformReleaseDAO } = await import('@/dao/platform-release.dao');

    const platformReleaseDAO = new PlatformReleaseDAO();

    // 获取所有平台发布记录（带版本和渠道信息）
    const platformReleases = await platformReleaseDAO.getAllPlatformReleasesWithDetails();

    const versions = await Promise.all(
      platformReleases.map(async (release: any) => {
        // 调试：记录从数据库获取的原始数据
        logger.info('Processing platform release from database', {
          filename: release.filename,
          version: release.version,
          platform: release.platform,
          architecture: release.architecture,
          channel: release.channel_name,
          hasSignatureInDB: !!release.signature,
          signatureLength: release.signature ? release.signature.length : 0,
          signaturePreview: release.signature ? release.signature.substring(0, 50) + '...' : null,
          hasChecksumInDB: !!release.checksum,
          checksumLength: release.checksum ? release.checksum.length : 0,
        });

        // 获取签名信息（优先从数据库）
        const signatureInfo = await getSignatureInfo(release.filename, release.channel_name, release.signature);

        // 获取哈希文件信息（优先从数据库checksum字段）
        const hashFileInfo = await getHashFileInfo(release.filename, release.channel_name, release.checksum);

        // 调试：记录处理后的信息
        logger.info('Processed signature and hash info', {
          filename: release.filename,
          signatureInfo,
          hashFileInfo,
        });

        // 获取文件统计信息（如果文件存在）
        let fileSize = release.file_size;
        let uploadTime = release.release_date;
        try {
          const filePath = path.join(config.storage.releasesPath, release.channel_name, release.filename);
          if (await fs.pathExists(filePath)) {
            const stats = await fs.stat(filePath);
            fileSize = stats.size; // 使用实际文件大小
            // 保持数据库中的上传时间，不使用文件系统时间
          }
        } catch (error) {
          logger.warn('Failed to get file stats', { filename: release.filename, error });
        }

        return {
          id: release.filename,
          filename: release.filename,
          version: release.version,
          platform: release.platform,
          architecture: release.architecture,
          channel: release.channel_name,
          fileSize,
          checksum: release.checksum,
          uploadTime,
          downloadCount: 0, // TODO: 从下载记录表获取
          releaseNotes: release.release_notes || `Release ${release.version}`,
          isForced: Boolean(release.force_update), // 添加强制更新标志，转换为布尔值
          // 添加签名信息
          ...signatureInfo,
          // 添加哈希文件信息
          ...hashFileInfo,
        };
      })
    );

    // 过滤掉 null 值
    const validVersions = versions.filter(v => v !== null);

    // 按版本和创建时间排序
    validVersions.sort((a, b) => {
      const versionCompare = b.version.localeCompare(a.version, undefined, { numeric: true });
      if (versionCompare !== 0) return versionCompare;
      return new Date(b.uploadTime).getTime() - new Date(a.uploadTime).getTime();
    });

    const response: SuccessResponse = {
      success: true,
      data: validVersions,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get versions list', {
      error: error.message,
      requestedBy: req.user?.username,
    });
    next(error);
  }
});

/**
 * 删除版本
 * DELETE /api/v1/admin/version/:id
 */
router.delete('/version/:id', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    if (!id) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'MISSING_PARAMETER',
          message: 'Version ID is required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }
    const filename = decodeURIComponent(id);

    // 验证文件名安全性
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_FILENAME',
          message: 'Invalid filename',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }

    // 在所有渠道目录中查找文件
    let filePath: string | null = null;
    let foundChannel: string | null = null;

    try {
      // 首先检查根目录（向后兼容）
      const rootFilePath = path.join(config.storage.releasesPath, filename);
      if (await fs.pathExists(rootFilePath)) {
        filePath = rootFilePath;
        foundChannel = 'root';
      } else {
        // 遍历所有渠道目录查找文件
        const channelDirs = await fs.readdir(config.storage.releasesPath);

        for (const channelDir of channelDirs) {
          const channelPath = path.join(config.storage.releasesPath, channelDir);
          const stat = await fs.stat(channelPath);

          if (stat.isDirectory()) {
            const channelFilePath = path.join(channelPath, filename);
            if (await fs.pathExists(channelFilePath)) {
              filePath = channelFilePath;
              foundChannel = channelDir;
              break;
            }
          }
        }
      }
    } catch (error) {
      logger.warn('Error while searching for file in channels', { filename, error });
    }

    // 如果文件不存在，仍然尝试从数据库中删除记录
    let fileDeleted = false;
    if (!filePath) {
      logger.warn('Version file not found in filesystem, will only delete database record', { filename });
    } else {
      // 删除文件
      await fs.remove(filePath);
      fileDeleted = true;
      logger.info('Version file deleted from filesystem', { filename, filePath });
    }

    // 删除对应的签名文件（如果存在）
    let signatureDeleted = false;
    let signatureFilename: string | null = null;
    try {
      signatureFilename = `${filename}.sig`;

      // 首先尝试在同一渠道目录中查找签名文件
      if (foundChannel && foundChannel !== 'root') {
        const signaturePath = path.join(config.storage.releasesPath, foundChannel, signatureFilename);
        if (await fs.pathExists(signaturePath)) {
          await fs.remove(signaturePath);
          signatureDeleted = true;
          logger.debug('Signature file deleted from channel directory', {
            filename,
            signatureFilename,
            signaturePath,
            channel: foundChannel
          });
        }
      }

      // 如果在渠道目录中没有找到，或者文件在根目录，则检查根目录
      if (!signatureDeleted) {
        const rootSignaturePath = path.join(config.storage.releasesPath, signatureFilename);
        if (await fs.pathExists(rootSignaturePath)) {
          await fs.remove(rootSignaturePath);
          signatureDeleted = true;
          logger.debug('Signature file deleted from root directory', {
            filename,
            signatureFilename,
            signaturePath: rootSignaturePath
          });
        }
      }

      if (!signatureDeleted) {
        logger.debug('No signature file found for deletion', { filename, signatureFilename });
      }
    } catch (error) {
      logger.warn('Failed to delete signature file', {
        filename,
        signatureFilename,
        error: error instanceof Error ? error.message : error
      });
      // 不阻止删除操作，只记录警告
    }

    // 删除对应的哈希文件（如果存在）
    let hashDeleted = false;
    let hashFilename: string | null = null;
    try {
      hashFilename = `${filename}.hash`;

      // 首先尝试在同一渠道目录中查找哈希文件
      if (foundChannel && foundChannel !== 'root') {
        const hashPath = path.join(config.storage.releasesPath, foundChannel, hashFilename);
        if (await fs.pathExists(hashPath)) {
          await fs.remove(hashPath);
          hashDeleted = true;
          logger.debug('Hash file deleted from channel directory', {
            filename,
            hashFilename,
            hashPath,
            channel: foundChannel
          });
        }
      }

      // 如果在渠道目录中没有找到，或者文件在根目录，则检查根目录
      if (!hashDeleted) {
        const rootHashPath = path.join(config.storage.releasesPath, hashFilename);
        if (await fs.pathExists(rootHashPath)) {
          await fs.remove(rootHashPath);
          hashDeleted = true;
          logger.debug('Hash file deleted from root directory', {
            filename,
            hashFilename,
            hashPath: rootHashPath
          });
        }
      }

      if (!hashDeleted) {
        logger.debug('No hash file found for deletion', { filename, hashFilename });
      }
    } catch (error) {
      logger.warn('Failed to delete hash file', {
        filename,
        hashFilename,
        error: error instanceof Error ? error.message : error
      });
      // 不阻止删除操作，只记录警告
    }

    // 删除对应的元数据文件
    try {
      const metadataPath = path.join(config.storage.metadataPath, 'versions', `${filename}.json`);
      if (await fs.pathExists(metadataPath)) {
        await fs.remove(metadataPath);
        logger.debug('Version metadata deleted', { filename, metadataPath });
      }
    } catch (error) {
      logger.warn('Failed to delete version metadata', { filename, error });
      // 不阻止删除操作，只记录警告
    }

    logger.info('版本文件删除成功', {
      filename,
      channel: foundChannel,
      filePath,
      signatureDeleted,
      signatureFilename: signatureDeleted ? signatureFilename : null,
      hashDeleted,
      hashFilename: hashDeleted ? hashFilename : null,
      deletedBy: req.user?.username,
      clientIP: req.ip,
      module: 'version_management',
      operation: 'file_delete',
    });

    // 从数据库中删除版本记录
    let databaseDeleted = false;
    try {
      const { PlatformReleaseDAO } = await import('@/dao/platform-release.dao');
      const { VersionDAO } = await import('@/dao/version.dao');

      const platformReleaseDAO = new PlatformReleaseDAO();
      const versionDAO = new VersionDAO();

      // 查找并删除平台发布记录
      const platformRelease = await platformReleaseDAO.getPlatformReleaseByFilename(filename);
      if (platformRelease) {
        // 删除平台发布记录
        await platformReleaseDAO.deletePlatformRelease(platformRelease.id);

        // 检查是否还有其他平台发布使用同一个版本
        const otherReleases = await platformReleaseDAO.getPlatformReleasesByVersionId(platformRelease.version_id);

        // 如果没有其他平台发布使用这个版本，删除版本记录
        if (otherReleases.length === 0) {
          await versionDAO.deleteVersion(platformRelease.version_id);
          logger.info('Version record deleted from database', {
            filename,
            versionId: platformRelease.version_id
          });
        } else {
          logger.info('Version record kept (used by other platform releases)', {
            filename,
            versionId: platformRelease.version_id,
            otherReleasesCount: otherReleases.length
          });
        }

        databaseDeleted = true;
        logger.info('Platform release record deleted from database', { filename, releaseId: platformRelease.id });
      } else {
        logger.warn('Platform release record not found in database', { filename });
      }
    } catch (syncError) {
      logger.error('Failed to delete version record from database', {
        filename,
        error: syncError,
      });
      // 不阻止删除成功响应，只记录错误
    }

    // 构建响应消息
    let message = 'Version deleted successfully';
    if (!fileDeleted && databaseDeleted) {
      message = 'Version record deleted from database (file was already missing)';
    } else if (fileDeleted && databaseDeleted) {
      // 构建详细的删除消息
      const deletedFiles = ['Version file'];
      if (signatureDeleted) deletedFiles.push('signature file');
      if (hashDeleted) deletedFiles.push('hash file');
      deletedFiles.push('database record');
      message = `${deletedFiles.join(', ')} deleted successfully`;
    } else if (fileDeleted && !databaseDeleted) {
      // 构建详细的删除消息（无数据库记录）
      const deletedFiles = ['Version file'];
      if (signatureDeleted) deletedFiles.push('signature file');
      if (hashDeleted) deletedFiles.push('hash file');
      message = `${deletedFiles.join(', ')} deleted successfully (database record not found)`;
    }

    const response: SuccessResponse = {
      success: true,
      data: {
        message,
        filename,
        channel: foundChannel,
        fileDeleted,
        databaseDeleted,
        signatureDeleted,
        signatureFilename: signatureDeleted ? signatureFilename : null,
        hashDeleted,
        hashFilename: hashDeleted ? hashFilename : null,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('删除版本文件失败', {
      error: {
        name: error.name || 'Error',
        message: error.message,
        code: error.code,
        stack: error.stack,
        type: typeof error,
      },
      deleteOperation: {
        filename: req.params.id,
        decodedFilename: req.params.id ? decodeURIComponent(req.params.id) : undefined,
        requestedBy: req.user?.username,
        userId: req.user?.id,
      },
      request: {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.url,
        params: req.params,
      },
      systemInfo: {
        releasesPath: config.storage.releasesPath,
        timestamp: new Date().toISOString(),
      },
      module: 'version_management',
      operation: 'delete_version_failed',
    });
    next(error);
  }
});

/**
 * 切换版本强制更新状态
 * PATCH /api/v1/admin/version/:filename/force-update
 */
router.patch(
  '/version/:filename/force-update',
  authMiddleware,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { filename } = req.params;
      const { isForced } = req.body;

      logger.info('Toggle force update request received', {
        filename,
        decodedFilename: filename ? decodeURIComponent(filename) : null,
        isForced,
        username: req.user?.username,
        url: req.url,
        method: req.method
      });

      if (!filename) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'MISSING_FILENAME',
            message: 'Filename is required',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(400).json(response);
        return;
      }

      if (typeof isForced !== 'boolean') {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'INVALID_FORCE_UPDATE_VALUE',
            message: 'isForced must be a boolean value',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(400).json(response);
        return;
      }

      const decodedFilename = decodeURIComponent(filename);

      // 导入DAO类
      const { PlatformReleaseDAO } = await import('@/dao/platform-release.dao');
      const { VersionDAO } = await import('@/dao/version.dao');

      const platformReleaseDAO = new PlatformReleaseDAO();
      const versionDAO = new VersionDAO();

      // 查找平台发布记录
      const platformRelease = await platformReleaseDAO.getPlatformReleaseByFilename(decodedFilename);
      if (!platformRelease) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'VERSION_NOT_FOUND',
            message: `Version with filename ${decodedFilename} not found`,
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(404).json(response);
        return;
      }

      // 更新版本的强制更新标志
      const updateSuccess = await versionDAO.updateVersion(platformRelease.version_id, {
        force_update: isForced
      });

      if (!updateSuccess) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'UPDATE_FAILED',
            message: 'Failed to update force update status',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(500).json(response);
        return;
      }

      // 清理版本数据库服务缓存，确保下次获取版本列表时读取最新数据
      const { versionDatabaseService } = await import('@/services/version-db.service');
      versionDatabaseService.clearCache();

      // 记录操作日志
      logger.info('版本强制更新状态已更新', {
        filename: decodedFilename,
        isForced,
        versionId: platformRelease.version_id,
        username: req.user?.username,
        module: 'user_operation',
        operation: 'toggle_force_update',
      });

      const response: SuccessResponse<null> = {
        success: true,
        data: null,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.json(response);
    } catch (error) {
      logger.error('Error toggling force update status', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        filename: req.params.filename,
        body: req.body,
        username: req.user?.username,
        module: 'system_log',
        operation: 'toggle_force_update_error',
      });
      next(error);
    }
  }
);

/**
 * 更新版本元数据
 * PUT /api/v1/admin/version/:filename
 */
router.put(
  '/version/:filename',
  authMiddleware,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { filename } = req.params;

      logger.info('Update version metadata request received', {
        filename,
        decodedFilename: filename ? decodeURIComponent(filename) : null,
        body: req.body,
        username: req.user?.username,
        url: req.url,
        method: req.method
      });

      if (!filename) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'MISSING_FILENAME',
            message: 'Filename is required',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(400).json(response);
        return;
      }

      const decodedFilename = decodeURIComponent(filename);

      // 验证文件名安全性
      if (decodedFilename.includes('..') || decodedFilename.includes('/') || decodedFilename.includes('\\')) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'INVALID_FILENAME',
            message: 'Invalid filename',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(400).json(response);
        return;
      }

      // 验证请求数据
      const UpdateMetadataSchema = z.object({
        version: z.string().min(1, 'Version is required'),
        platform: z.enum(['windows', 'macos', 'linux']),
        architecture: z.enum(['x86_64', 'aarch64', 'x86']),
        channel: z.enum(['stable', 'beta', 'alpha']),
        isForced: z.boolean().optional().default(false),
        releaseNotes: z.string().optional().default(''),
      });

      const validationResult = UpdateMetadataSchema.safeParse(req.body);
      if (!validationResult.success) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request parameters',
            details: validationResult.error.errors,
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(400).json(response);
        return;
      }

      const updateData = validationResult.data;

      // 使用数据库服务直接操作
      const { ChannelDAO } = await import('@/dao/channel.dao');
      const { VersionDAO } = await import('@/dao/version.dao');
      const { PlatformReleaseDAO } = await import('@/dao/platform-release.dao');

      const channelDAO = new ChannelDAO();
      const versionDAO = new VersionDAO();
      const platformReleaseDAO = new PlatformReleaseDAO();

      try {
        // 获取渠道信息
        const channel = await channelDAO.getChannelByName(updateData.channel);
        if (!channel) {
          const response: ErrorResponse = {
            success: false,
            error: {
              code: 'CHANNEL_NOT_FOUND',
              message: `Channel not found: ${updateData.channel}`,
            },
            timestamp: new Date().toISOString(),
            version: '1.0.0',
          };
          res.status(404).json(response);
          return;
        }

        // 查找对应的平台发布记录
        const existingPlatformRelease = await platformReleaseDAO.getPlatformReleaseByFilename(decodedFilename);

        if (!existingPlatformRelease) {
          const response: ErrorResponse = {
            success: false,
            error: {
              code: 'PLATFORM_RELEASE_NOT_FOUND',
              message: `Platform release not found for filename: ${decodedFilename}`,
            },
            timestamp: new Date().toISOString(),
            version: '1.0.0',
          };
          res.status(404).json(response);
          return;
        }

        // 获取当前版本信息
        const currentVersion = await versionDAO.getVersionById(existingPlatformRelease.version_id);
        if (!currentVersion) {
          const response: ErrorResponse = {
            success: false,
            error: {
              code: 'VERSION_NOT_FOUND',
              message: 'Associated version not found',
            },
            timestamp: new Date().toISOString(),
            version: '1.0.0',
          };
          res.status(500).json(response);
          return;
        }

        // 检查是否需要更新版本信息
        let targetVersionId = currentVersion.id;

        // 如果版本号发生变化，需要创建新版本或查找现有版本
        if (currentVersion.version !== updateData.version) {
          // 查找目标版本是否已存在
          let targetVersion = await versionDAO.getVersionByChannelAndVersion(channel.id, updateData.version);

          if (!targetVersion) {
            // 创建新版本，继承当前版本的强制更新设置
            targetVersionId = await versionDAO.createVersion({
              channel_id: channel.id,
              version: updateData.version,
              force_update: currentVersion.force_update
            });

            logger.info('Created new version', {
              versionId: targetVersionId,
              version: updateData.version,
              channel: updateData.channel,
              force_update: currentVersion.force_update
            });
          } else {
            targetVersionId = targetVersion.id;
          }
        }

        // 更新平台发布记录
        await platformReleaseDAO.updatePlatformRelease(existingPlatformRelease.id, {
          version_id: targetVersionId,
          platform: updateData.platform,
          architecture: updateData.architecture,
          release_notes: updateData.releaseNotes || existingPlatformRelease.release_notes
        });

        // 更新版本的强制更新标志
        if (updateData.isForced !== undefined) {
          await versionDAO.updateVersion(targetVersionId, {
            force_update: updateData.isForced
          });
        }

        // 获取更新后的数据用于响应
        const updatedPlatformRelease = await platformReleaseDAO.getPlatformRelease(
          targetVersionId,
          updateData.platform,
          updateData.architecture
        );


        const updatedMetadata = {
          filename: decodedFilename,
          version: updateData.version,
          platform: updateData.platform,
          architecture: updateData.architecture,
          channel: updateData.channel,
          isForced: updateData.isForced,
          releaseNotes: updateData.releaseNotes,
          fileSize: updatedPlatformRelease?.file_size || existingPlatformRelease.file_size,
          checksum: updatedPlatformRelease?.checksum || existingPlatformRelease.checksum,
          signature: updatedPlatformRelease?.signature || existingPlatformRelease.signature,
          uploadTime: updatedPlatformRelease?.release_date || existingPlatformRelease.release_date,
          updatedTime: new Date().toISOString(),
        };

        logger.info('Version metadata updated successfully', {
          filename: decodedFilename,
          updateData,
          username: req.user?.username,
          targetVersionId,
          forceUpdate: updateData.isForced
        });

        const response: SuccessResponse = {
          success: true,
          data: {
            message: 'Version metadata updated successfully',
            metadata: updatedMetadata,
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };

        res.json(response);

      } catch (databaseError) {
        logger.error('Failed to update version metadata in database', {
          error: databaseError,
          filename: decodedFilename,
          updateData,
          username: req.user?.username,
        });

        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: 'Failed to update version metadata',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(500).json(response);
        return;
      }
    } catch (error: any) {
      logger.error('更新版本元数据失败', {
        error: {
          name: error.name || 'Error',
          message: error.message,
          code: error.code,
          stack: error.stack,
          type: typeof error,
        },
        updateOperation: {
          filename: req.params.filename,
          decodedFilename: req.params.filename ? decodeURIComponent(req.params.filename) : undefined,
          requestBody: req.body ? Object.keys(req.body) : [],
          updateData: req.body ? {
            version: req.body.version,
            platform: req.body.platform,
            architecture: req.body.architecture,
            channel: req.body.channel,
            releaseNotes: req.body.releaseNotes?.substring(0, 100) + (req.body.releaseNotes?.length > 100 ? '...' : ''),
            isForced: req.body.isForced,
          } : undefined,
          requestedBy: req.user?.username,
          userId: req.user?.id,
        },
        request: {
          clientIP: req.ip,
          userAgent: req.get('User-Agent'),
          method: req.method,
          url: req.url,
          params: req.params,
        },
        timing: {
          timestamp: new Date().toISOString(),
        },
        module: 'version_management',
        operation: 'update_metadata_failed',
      });
      next(error);
    }
  },
);

/**
 * 手动同步版本配置
 * POST /api/v1/admin/sync-versions
 */
router.post('/sync-versions', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    logger.info('Manual version sync requested', {
      username: req.user?.username,
      ip: req.ip,
    });

    // 注意：不再调用版本同步服务，因为现在使用数据库存储
    // 版本同步服务是为文件系统设计的，会覆盖数据库中的签名信息
    logger.info('Manual version sync completed successfully (database mode)', {
      username: req.user?.username,
    });

    const response: SuccessResponse = {
      success: true,
      data: {
        message: 'Version configuration is managed by database (no sync needed)',
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('手动版本同步失败', {
      error: {
        name: error.name || 'Error',
        message: error.message,
        code: error.code,
        stack: error.stack,
        type: typeof error,
      },
      syncOperation: {
        requestedBy: req.user?.username,
        userId: req.user?.id,
        role: req.user?.role,
      },
      request: {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.url,
      },
      systemInfo: {
        releasesPath: config.storage.releasesPath,
        metadataPath: config.storage.metadataPath,
        timestamp: new Date().toISOString(),
      },
      timing: {
        timestamp: new Date().toISOString(),
      },
      module: 'version_management',
      operation: 'manual_sync_failed',
    });
    next(error);
  }
});

/**
 * 获取系统日志
 * GET /api/v1/admin/logs
 */
router.get('/logs', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { module, limit = 100, offset = 0, search } = req.query;

    // 优先从数据库获取日志
    const isDbEnabled = process.env.DB_ENABLED === 'true';

    if (isDbEnabled) {
      // 从数据库获取日志
      const { getSystemLogService } = await import('@/services/system-log.service');
      const systemLogService = getSystemLogService();

      const queryParams = {
        module: module as string,
        limit: parseInt(limit as string, 10),
        offset: parseInt(offset as string, 10),
        search: search as string
      };

      const result = await systemLogService.getLogsFromDatabase(queryParams);

      // 转换数据库格式到前端期望的格式
      const logs = result.logs.map(log => ({
        timestamp: log.timestamp,
        level: log.level,
        message: log.message,
        meta: {
          ...log.meta,
          module: log.module,
          type: 'database'
        },
        clientIP: log.client_ip,
        isPrivateIP: log.is_private_ip,
        operation: log.operation,
        userAgent: log.user_agent,
        path: log.path,
        method: log.method
      }));

      const response: SuccessResponse = {
        success: true,
        data: {
          logs,
          total: result.total,
          offset: parseInt(offset as string, 10),
          limit: parseInt(limit as string, 10),
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.json(response);
      return;
    }

    // 回退到文件日志
    const logsPath = config.storage.logsPath;

    // 获取所有日志文件
    const logFiles = await getLogFiles(logsPath);

    if (logFiles.length === 0) {
      const response: SuccessResponse = {
        success: true,
        data: {
          logs: [],
          total: 0,
          offset: 0,
          limit: parseInt(limit as string, 10),
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.json(response);
      return;
    }

    // 读取并合并所有日志文件
    const allLogs = await readAndMergeLogFiles(logFiles);

    // 按模块过滤
    let filteredLogs = allLogs;
    if (module && typeof module === 'string') {
      filteredLogs = filteredLogs.filter(log => {
        if (!log || !log.meta) return false;
        return log.meta.module === module;
      });
    }

    // 按搜索关键词过滤
    if (search && typeof search === 'string') {
      const keyword = search.toLowerCase();
      filteredLogs = filteredLogs.filter(log => {
        if (!log) return false;

        return (
          log.message.toLowerCase().includes(keyword) ||
          JSON.stringify(log.meta || {}).toLowerCase().includes(keyword) ||
          (log.clientIP && log.clientIP.toLowerCase().includes(keyword)) ||
          (log.operation && log.operation.toLowerCase().includes(keyword)) ||
          (log.path && log.path.toLowerCase().includes(keyword)) ||
          (log.userAgent && log.userAgent.toLowerCase().includes(keyword))
        );
      });
    }

    // 分页
    const startIndex = parseInt(offset as string, 10);
    const endIndex = startIndex + parseInt(limit as string, 10);
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    const response: SuccessResponse = {
      success: true,
      data: {
        logs: paginatedLogs,
        total: filteredLogs.length,
        offset: startIndex,
        limit: parseInt(limit as string, 10),
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('获取系统日志失败', {
      error: {
        name: error.name || 'Error',
        message: error.message,
        code: error.code,
        stack: error.stack,
        type: typeof error,
      },
      logQuery: {
        module: req.query.module,
        limit: req.query.limit,
        offset: req.query.offset,
        search: req.query.search,
        requestedBy: req.user?.username,
        userId: req.user?.id,
      },
      request: {
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        url: req.url,
        query: req.query,
      },
      systemInfo: {
        dbEnabled: process.env.DB_ENABLED === 'true',
        logsPath: config.storage.logsPath,
        timestamp: new Date().toISOString(),
      },
      timing: {
        timestamp: new Date().toISOString(),
      },
      module: 'user_operation',
      operation: 'get_logs_failed',
    });
    next(error);
  }
});

/**
 * 清空系统日志
 * DELETE /api/v1/admin/logs
 */
router.delete('/logs', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    logger.info('管理员清空系统日志', {
      username: req.user?.username,
      clientIP: req.ip,
      module: 'user_operation',
      operation: 'clear_logs',
    });

    let deletedCount = 0;
    let dbDeletedCount = 0;

    // 清空数据库日志
    const isDbEnabled = process.env.DB_ENABLED === 'true';
    if (isDbEnabled) {
      const { getSystemLogService } = await import('@/services/system-log.service');
      const systemLogService = getSystemLogService();
      dbDeletedCount = await systemLogService.clearDatabaseLogs();
      logger.info('Cleared database logs', { count: dbDeletedCount });
    }

    // 清空文件日志
    const logsPath = config.storage.logsPath;
    const logFiles = await getLogFiles(logsPath);

    for (const logFile of logFiles) {
      try {
        await fs.unlink(logFile);
        deletedCount++;
        logger.info('Deleted log file', { file: logFile });
      } catch (error) {
        logger.warn('Failed to delete log file', { file: logFile, error });
      }
    }

    const response: SuccessResponse = {
      success: true,
      data: {
        message: `成功清空 ${deletedCount} 个日志文件${isDbEnabled ? ` 和 ${dbDeletedCount} 条数据库日志` : ''}`,
        deletedFileCount: deletedCount,
        deletedDbCount: dbDeletedCount,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    logger.info('系统日志清空完成', {
      deletedFileCount: deletedCount,
      deletedDbCount: dbDeletedCount,
      username: req.user?.username,
      clientIP: req.ip,
      module: 'user_operation',
      operation: 'clear_logs_complete',
    });

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to clear logs', {
      error: error.message,
      username: req.user?.username,
    });
    next(error);
  }
});

/**
 * 清理无效版本
 * POST /api/v1/admin/cleanup-invalid-versions
 */
router.post('/cleanup-invalid-versions', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    logger.info('开始清理无效版本', {
      operation: 'cleanup_invalid_versions_start',
      module: 'version_management',
      requestedBy: req.user?.username,
      clientIP: req.ip,
      timestamp: new Date().toISOString(),
    });

    // 使用数据库获取所有平台发布记录
    const { PlatformReleaseDAO } = await import('@/dao/platform-release.dao');
    const { VersionDAO } = await import('@/dao/version.dao');
    const { ChannelDAO } = await import('@/dao/channel.dao');

    const platformReleaseDAO = new PlatformReleaseDAO();
    const versionDAO = new VersionDAO();
    const channelDAO = new ChannelDAO();

    // 获取所有平台发布记录（带版本和渠道信息）
    const platformReleases = await platformReleaseDAO.getAllPlatformReleasesWithDetails();

    const invalidVersions: Array<{
      filename: string;
      reason: string;
      releaseId: number;
      versionId: number;
      channelName: string;
    }> = [];

    // 检查每个平台发布记录对应的文件是否存在
    for (const release of platformReleases) {
      let fileExists = false;
      let checkedPaths: string[] = [];

      // 检查各个可能的位置
      const possiblePaths = [
        // 渠道目录
        path.join(config.storage.releasesPath, release.channel_name, release.filename),
        // 根目录（向后兼容）
        path.join(config.storage.releasesPath, release.filename),
      ];

      for (const possiblePath of possiblePaths) {
        checkedPaths.push(possiblePath);
        if (await fs.pathExists(possiblePath)) {
          fileExists = true;
          break;
        }
      }

      if (!fileExists) {
        invalidVersions.push({
          filename: release.filename,
          reason: '文件不存在',
          releaseId: release.id,
          versionId: release.version_id,
          channelName: release.channel_name,
        });

        logger.info('发现无效版本', {
          filename: release.filename,
          version: release.version,
          channel: release.channel_name,
          reason: '安装包文件不存在',
          checkedPaths,
          operation: 'invalid_version_found',
          module: 'version_management',
        });
      }
    }

    // 删除无效版本的数据库记录
    let cleanedCount = 0;
    const cleanedVersions: Array<{
      filename: string;
      reason: string;
    }> = [];

    for (const invalidVersion of invalidVersions) {
      try {
        // 删除平台发布记录
        await platformReleaseDAO.deletePlatformRelease(invalidVersion.releaseId);

        // 检查是否还有其他平台发布使用同一个版本
        const otherReleases = await platformReleaseDAO.getPlatformReleasesByVersionId(invalidVersion.versionId);

        // 如果没有其他平台发布使用这个版本，删除版本记录
        if (otherReleases.length === 0) {
          await versionDAO.deleteVersion(invalidVersion.versionId);
          logger.info('删除版本记录', {
            versionId: invalidVersion.versionId,
            filename: invalidVersion.filename
          });
        }

        cleanedCount++;
        cleanedVersions.push({
          filename: invalidVersion.filename,
          reason: invalidVersion.reason,
        });

        logger.info('清理无效版本数据库记录', {
          filename: invalidVersion.filename,
          releaseId: invalidVersion.releaseId,
          versionId: invalidVersion.versionId,
          channelName: invalidVersion.channelName,
          reason: invalidVersion.reason,
          operation: 'invalid_version_cleaned',
          module: 'version_management',
        });
      } catch (error) {
        logger.error('清理无效版本失败', {
          filename: invalidVersion.filename,
          releaseId: invalidVersion.releaseId,
          versionId: invalidVersion.versionId,
          error: error instanceof Error ? error.message : String(error),
          operation: 'cleanup_invalid_version_failed',
          module: 'version_management',
        });
      }
    }

    // 数据库操作已完成，无需清除缓存

    logger.info('无效版本清理完成', {
      totalFound: invalidVersions.length,
      totalCleaned: cleanedCount,
      cleanedVersions,
      operation: 'cleanup_invalid_versions_complete',
      module: 'version_management',
      requestedBy: req.user?.username,
      clientIP: req.ip,
      timestamp: new Date().toISOString(),
    });

    const response: SuccessResponse = {
      success: true,
      data: {
        totalFound: invalidVersions.length,
        totalCleaned: cleanedCount,
        cleanedVersions,
        message: `成功清理 ${cleanedCount} 个无效版本`,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('清理无效版本失败', {
      error: {
        name: error.name || 'Error',
        message: error.message,
        stack: error.stack,
      },
      operation: 'cleanup_invalid_versions_failed',
      module: 'version_management',
      requestedBy: req.user?.username,
      clientIP: req.ip,
      timestamp: new Date().toISOString(),
    });
    next(error);
  }
});

/**
 * 清理临时文件
 * POST /api/v1/admin/cleanup
 */
router.post('/cleanup', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const tempPath = path.join(config.storage.basePath, 'temp');
    const backupPath = path.join(config.storage.basePath, 'backup');

    let cleanedFiles = 0;
    let freedSpace = 0;

    // 清理临时文件
    if (await fs.pathExists(tempPath)) {
      const tempFiles = await fs.readdir(tempPath);
      for (const file of tempFiles) {
        const filePath = path.join(tempPath, file);
        const stats = await fs.stat(filePath);

        // 删除超过1小时的临时文件
        const oneHourAgo = Date.now() - 60 * 60 * 1000;
        if (stats.mtime.getTime() < oneHourAgo) {
          freedSpace += stats.size;
          await fs.remove(filePath);
          cleanedFiles++;
        }
      }
    }

    // 清理旧的备份文件（保留最近7天）
    if (await fs.pathExists(backupPath)) {
      const backupFiles = await fs.readdir(backupPath);
      const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

      for (const file of backupFiles) {
        const filePath = path.join(backupPath, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime.getTime() < sevenDaysAgo) {
          freedSpace += stats.size;
          await fs.remove(filePath);
          cleanedFiles++;
        }
      }
    }

    logger.info('Cleanup completed', {
      cleanedFiles,
      freedSpace,
      performedBy: req.user?.username,
    });

    const response: SuccessResponse = {
      success: true,
      data: {
        message: 'Cleanup completed successfully',
        cleanedFiles,
        freedSpace,
        freedSpaceFormatted: formatBytes(freedSpace),
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Cleanup failed', {
      error: error.message,
      performedBy: req.user?.username,
    });
    next(error);
  }
});

// 辅助函数：格式化字节数
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

















/**
 * 获取日志文件列表
 */
async function getLogFiles(logsPath: string): Promise<string[]> {
  try {
    if (!(await fs.pathExists(logsPath))) {
      return [];
    }

    const files = await fs.readdir(logsPath);
    const logFiles = files
      .filter(file => {
        // 匹配各种日志文件格式
        return (
          file.endsWith('.log') &&
          (file.includes('combined') || file.includes('app') || file.includes('error') || file.includes('access'))
        );
      })
      .map(file => path.join(logsPath, file))
      .sort((a, b) => {
        // 按文件修改时间排序，最新的在前
        try {
          const statA = fs.statSync(a);
          const statB = fs.statSync(b);
          return statB.mtime.getTime() - statA.mtime.getTime();
        } catch {
          return 0;
        }
      });

    return logFiles;
  } catch (error) {
    logger.warn('Failed to get log files', { error, logsPath });
    return [];
  }
}

/**
 * 读取并合并多个日志文件
 */
async function readAndMergeLogFiles(logFiles: string[]): Promise<any[]> {
  const allLogs: any[] = [];

  for (const logFile of logFiles) {
    try {
      if (!(await fs.pathExists(logFile))) {
        continue;
      }

      const logContent = await fs.readFile(logFile, 'utf-8');
      const logLines = logContent.split('\n').filter(line => line.trim());

      const logs = logLines
        .map(line => {
          try {
            const logEntry = JSON.parse(line);

            // 提取IP相关信息
            const extractedData: any = {
              timestamp: logEntry.timestamp,
              level: logEntry.level,
              message: logEntry.message,
              meta: logEntry.meta || {},
            };

            // 从日志条目中提取IP信息
            if (logEntry.clientIP) {
              extractedData.clientIP = logEntry.clientIP;
            }
            if (logEntry.isPrivateIP !== undefined) {
              extractedData.isPrivateIP = logEntry.isPrivateIP;
            }
            if (logEntry.anonymizedIP) {
              extractedData.anonymizedIP = logEntry.anonymizedIP;
            }
            if (logEntry.operation) {
              extractedData.operation = logEntry.operation;
            }
            if (logEntry.userAgent) {
              extractedData.userAgent = logEntry.userAgent;
            }
            if (logEntry.path) {
              extractedData.path = logEntry.path;
            }
            if (logEntry.method) {
              extractedData.method = logEntry.method;
            }

            // 也从meta中提取IP信息（兼容旧格式）
            if (logEntry.meta) {
              if (logEntry.meta.clientIP && !extractedData.clientIP) {
                extractedData.clientIP = logEntry.meta.clientIP;
              }
              if (logEntry.meta.isPrivateIP !== undefined && extractedData.isPrivateIP === undefined) {
                extractedData.isPrivateIP = logEntry.meta.isPrivateIP;
              }
              if (logEntry.meta.ip && !extractedData.clientIP) {
                extractedData.clientIP = logEntry.meta.ip;
              }
              if (logEntry.meta.operation && !extractedData.operation) {
                extractedData.operation = logEntry.meta.operation;
              }
              if (logEntry.meta.userAgent && !extractedData.userAgent) {
                extractedData.userAgent = logEntry.meta.userAgent;
              }
              if (logEntry.meta.path && !extractedData.path) {
                extractedData.path = logEntry.meta.path;
              }
              if (logEntry.meta.method && !extractedData.method) {
                extractedData.method = logEntry.meta.method;
              }
            }

            // 检查是否是访问日志（JSON格式但message包含访问日志字符串）
            if (logEntry.message && typeof logEntry.message === 'string') {
              const accessLogMatch = logEntry.message.match(/^(\S+)\s+-\s+-\s+\[([^\]]+)\]\s+"([^"]+)"\s+(\d+)\s+(\S+)\s+"([^"]*)"\s+"([^"]*)"/);
              if (accessLogMatch && accessLogMatch.length >= 8) {
                const clientIP = accessLogMatch[1] || '';
                const timestampStr = accessLogMatch[2] || '';
                const request = accessLogMatch[3] || '';
                const status = accessLogMatch[4] || '200';
                const size = accessLogMatch[5] || '0';
                const referer = accessLogMatch[6] || '';
                const userAgent = accessLogMatch[7] || '';

                // 解析请求字符串 "GET /path HTTP/1.1"
                const requestParts = request.split(' ');
                const method = requestParts[0] || 'GET';
                const path = requestParts[1] || '/';

                // 转换时间戳格式
                const timestamp = convertAccessLogTimestamp(timestampStr);

                // 判断是否为私有IP
                const isPrivateIP = isPrivateIPAddress(clientIP);

                // 根据路径确定模块类型
                let module = 'system';
                let operation = 'http_request';

                if (path.includes('/admin/')) {
                  // 管理页面操作
                  if (path.includes('/upload') || path.includes('/delete') || path.includes('/versions') ||
                      path.includes('/batch-upload') || path.includes('/chunk')) {
                    // 版本管理：上传、下载、删除等版本控制操作
                    module = 'version_management';
                    operation = method === 'GET' ? 'version_view' : 'version_operation';
                  } else if (path.includes('/login') || path.includes('/logout') || path.includes('/auth')) {
                    // 用户操作：登录、退出等认证操作
                    module = 'user_operation';
                    operation = 'auth_operation';
                  } else if (path.includes('/logs') || path.includes('/cleanup') || path.includes('/clear')) {
                    // 用户操作：清空日志、清理临时文件、清理缓存等管理操作
                    module = 'user_operation';
                    operation = 'admin_management';
                  } else {
                    // 其他管理页面访问
                    module = 'user_operation';
                    operation = 'admin_access';
                  }
                } else if (path.includes('/download')) {
                  // 版本管理：文件下载
                  module = 'version_management';
                  operation = 'file_download';
                } else if (path.includes('/version') || path.includes('/updates') || path.includes('/check')) {
                  // OTA功能：桌面程序请求的OTA服务
                  module = 'ota_function';
                  operation = 'ota_request';
                } else if (path.includes('/health') || path.includes('/status')) {
                  // OTA功能：连接状态检查
                  module = 'ota_function';
                  operation = 'health_check';
                }

                return {
                  timestamp,
                  level: parseInt(status) >= 400 ? 'error' : 'info',
                  message: `${method} ${path} - ${status}`,
                  meta: {
                    type: 'access',
                    method,
                    path,
                    status: parseInt(status),
                    size: size !== '-' ? parseInt(size) : 0,
                    referer: referer !== '-' ? referer : undefined,
                    module,
                  },
                  clientIP,
                  isPrivateIP,
                  userAgent: userAgent !== '-' ? userAgent : undefined,
                  operation,
                };
              }
            }

            return extractedData;
          } catch {
            // 如果不是JSON格式，尝试解析不同的日志格式

            // 1. 尝试解析标准应用日志格式: "2024-01-01T12:00:00.000Z level: message"
            const standardMatch = line.match(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z)\s+(\w+):\s+(.+)$/);
            if (standardMatch && standardMatch[1] && standardMatch[2] && standardMatch[3]) {
              const message = standardMatch[3];

              // 根据日志消息内容确定模块类型
              let module = 'system';
              let operation = 'system_log';

              // 检查是否包含特定关键词来分类
              if (message.includes('登录') || message.includes('退出') || message.includes('认证') ||
                  message.includes('清空日志') || message.includes('清理') || message.includes('管理员')) {
                module = 'user_operation';
                operation = message.includes('登录') || message.includes('退出') || message.includes('认证') ? 'auth_operation' : 'admin_management';
              } else if (message.includes('上传') || message.includes('下载') || message.includes('删除') ||
                        message.includes('版本') || message.includes('文件') || message.includes('chunk') ||
                        message.includes('batch')) {
                module = 'version_management';
                operation = message.includes('下载') ? 'file_download' : 'version_operation';
              } else if (message.includes('OTA') || message.includes('更新') || message.includes('检查') ||
                        message.includes('health') || message.includes('status')) {
                module = 'ota_function';
                operation = message.includes('health') || message.includes('status') ? 'health_check' : 'ota_request';
              }

              return {
                timestamp: standardMatch[1],
                level: standardMatch[2].toLowerCase(),
                message: standardMatch[3],
                meta: { module },
                operation,
              };
            }

            // 2. 尝试解析 access log 格式: "IP - - [timestamp] "method path protocol" status size "referer" "user-agent""
            const accessLogMatch = line.match(/^(\S+)\s+-\s+-\s+\[([^\]]+)\]\s+"([^"]+)"\s+(\d+)\s+(\S+)\s+"([^"]*)"\s+"([^"]*)"/);
            if (accessLogMatch && accessLogMatch.length >= 8) {
              const clientIP = accessLogMatch[1] || '';
              const timestampStr = accessLogMatch[2] || '';
              const request = accessLogMatch[3] || '';
              const status = accessLogMatch[4] || '200';
              const size = accessLogMatch[5] || '0';
              const referer = accessLogMatch[6] || '';
              const userAgent = accessLogMatch[7] || '';

              // 解析请求字符串 "GET /path HTTP/1.1"
              const requestParts = request.split(' ');
              const method = requestParts[0] || 'GET';
              const path = requestParts[1] || '/';

              // 转换时间戳格式 "17/Jun/2025:10:54:29 +0000" -> ISO格式
              const timestamp = convertAccessLogTimestamp(timestampStr);

              // 判断是否为私有IP
              const isPrivateIP = isPrivateIPAddress(clientIP);

              // 根据路径确定模块类型
              let module = 'system';
              let operation = 'http_request';

              if (path.includes('/admin/')) {
                // 管理页面操作
                if (path.includes('/upload') || path.includes('/delete') || path.includes('/versions') ||
                    path.includes('/batch-upload') || path.includes('/chunk')) {
                  // 版本管理：上传、下载、删除等版本控制操作
                  module = 'version_management';
                  operation = method === 'GET' ? 'version_view' : 'version_operation';
                } else if (path.includes('/login') || path.includes('/logout') || path.includes('/auth')) {
                  // 用户操作：登录、退出等认证操作
                  module = 'user_operation';
                  operation = 'auth_operation';
                } else if (path.includes('/logs') || path.includes('/cleanup') || path.includes('/clear')) {
                  // 用户操作：清空日志、清理临时文件、清理缓存等管理操作
                  module = 'user_operation';
                  operation = 'admin_management';
                } else {
                  // 其他管理页面访问
                  module = 'user_operation';
                  operation = 'admin_access';
                }
              } else if (path.includes('/download')) {
                // 版本管理：文件下载
                module = 'version_management';
                operation = 'file_download';
              } else if (path.includes('/version') || path.includes('/updates') || path.includes('/check')) {
                // OTA功能：桌面程序请求的OTA服务
                module = 'ota_function';
                operation = 'ota_request';
              } else if (path.includes('/health') || path.includes('/status')) {
                // OTA功能：连接状态检查
                module = 'ota_function';
                operation = 'health_check';
              }

              return {
                timestamp,
                level: parseInt(status) >= 400 ? 'error' : 'info',
                message: `${method} ${path} - ${status}`,
                meta: {
                  type: 'access',
                  method,
                  path,
                  status: parseInt(status),
                  size: size !== '-' ? parseInt(size) : 0,
                  referer: referer !== '-' ? referer : undefined,
                  module,
                },
                clientIP,
                isPrivateIP,
                userAgent: userAgent !== '-' ? userAgent : undefined,
                operation,
              };
            }

            return null;
          }
        })
        .filter(Boolean);

      allLogs.push(...logs);
    } catch (error) {
      logger.warn('Failed to read log file', { error, logFile });
    }
  }

  // 按时间戳排序，最新的在前面
  return allLogs.sort((a, b) => {
    const timeA = new Date(a.timestamp).getTime();
    const timeB = new Date(b.timestamp).getTime();
    return timeB - timeA;
  });
}

/**
 * 转换 access log 时间戳格式为 ISO 格式
 * 从 "17/Jun/2025:10:54:29 +0000" 转换为 "2025-06-17T10:54:29.000Z"
 */
function convertAccessLogTimestamp(timestampStr: string): string {
  try {
    // 解析格式: "17/Jun/2025:10:54:29 +0000"
    const match = timestampStr.match(/^(\d{2})\/(\w{3})\/(\d{4}):(\d{2}):(\d{2}):(\d{2})\s+([+-]\d{4})$/);
    if (!match) {
      return new Date().toISOString();
    }

    const day = match[1] || '01';
    const monthStr = match[2] || 'Jan';
    const year = match[3] || '2025';
    const hour = match[4] || '00';
    const minute = match[5] || '00';
    const second = match[6] || '00';
    const timezone = match[7] || '+0000';

    // 月份映射
    const months: Record<string, string> = {
      'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
      'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
      'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
    };

    const month = months[monthStr] || '01';

    // 构建 ISO 时间戳
    const isoTimestamp = `${year}-${month}-${day}T${hour}:${minute}:${second}.000Z`;

    // 验证时间戳是否有效
    const date = new Date(isoTimestamp);
    if (isNaN(date.getTime())) {
      return new Date().toISOString();
    }

    return isoTimestamp;
  } catch (error) {
    return new Date().toISOString();
  }
}

/**
 * 判断是否为私有IP地址
 */
function isPrivateIPAddress(ip: string): boolean {
  if (!ip || ip === '-') return false;

  // IPv4 私有地址范围
  const privateRanges = [
    /^127\./,                    // *********/8 (localhost)
    /^10\./,                     // 10.0.0.0/8
    /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
    /^192\.168\./,               // ***********/16
    /^169\.254\./,               // ***********/16 (link-local)
  ];

  return privateRanges.some(range => range.test(ip));
}

// 批量上传相关的验证模式
const BatchUploadInitSchema = z.object({
  files: z.array(z.object({
    filename: z.string().min(1, 'Filename is required'),
    fileSize: z.number().min(1, 'File size must be greater than 0'),
    fileHash: z.string().min(1, 'File hash is required'),
    fileType: z.enum(['installer', 'signature'], {
      errorMap: () => ({ message: 'File type must be installer or signature' }),
    }),
  })).min(1, 'At least one file is required').max(2, 'Maximum 2 files allowed'),
  metadata: z.object({
    version: z.string().min(1, 'Version is required'),
    platform: z.enum(['windows', 'macos', 'linux']),
    architecture: z.enum(['x86_64', 'aarch64', 'x86']),
    channel: z.enum(['stable', 'beta', 'alpha']).optional(),
    releaseNotes: z.string().optional(),
    isForced: z.boolean().optional(),
    fileHash: z.string().optional(), // 可选的文件hash值，从.hash文件中读取
    signature: z.string().optional(), // 可选的签名值，从.sig文件中读取
  }),
});

// 配置批量上传的 multer
const batchUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB per file
  },
  fileFilter: (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = ['.exe', '.dmg', '.deb', '.rpm', '.zip', '.tar.gz', '.msi', '.pkg', '.sig'];
    const ext = path.extname(file.originalname).toLowerCase();

    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error(`不支持的文件类型: ${ext}`));
    }
  },
});

/**
 * 初始化批量上传
 * POST /api/v1/admin/batch-upload/init
 */
router.post('/batch-upload/init', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    // 验证请求数据
    const validatedData = BatchUploadInitSchema.parse(req.body);
    const batchRequest: BatchUploadRequest = {
      ...validatedData,
      metadata: {
        ...validatedData.metadata,
        channel: validatedData.metadata.channel || 'stable',
      },
    };

    // 初始化批量上传会话
    const initResponse = await batchUploadService.initializeBatchUpload(batchRequest);

    logger.info('Batch upload session initialized', {
      sessionId: initResponse.sessionId,
      totalFiles: initResponse.totalFiles,
      totalSize: initResponse.totalSize,
      username: req.user?.username,
    });

    const response: SuccessResponse = {
      success: true,
      data: initResponse,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to initialize batch upload session', {
      error: error.message,
      username: req.user?.username,
      body: req.body,
    });
    next(error);
  }
});

/**
 * 批量上传文件
 * POST /api/v1/admin/batch-upload/:sessionId/files
 */
router.post(
  '/batch-upload/:sessionId/files',
  authMiddleware,
  batchUpload.array('files', 2), // 最多2个文件
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { sessionId } = req.params;
      const files = req.files as Express.Multer.File[];

      if (!sessionId) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'MISSING_SESSION_ID',
            message: 'Session ID is required',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(400).json(response);
        return;
      }

      if (!files || files.length === 0) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'NO_FILES',
            message: 'No files uploaded',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(400).json(response);
        return;
      }

      // 上传每个文件
      for (const file of files) {
        await batchUploadService.uploadFile(sessionId, file.originalname, file.buffer);
      }

      logger.info('Batch files uploaded successfully', {
        sessionId,
        fileCount: files.length,
        filenames: files.map(f => f.originalname),
        username: req.user?.username,
      });

      const response: SuccessResponse = {
        success: true,
        data: {
          message: 'Files uploaded successfully',
          uploadedFiles: files.map(f => f.originalname),
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.json(response);
    } catch (error: any) {
      logger.error('Failed to upload batch files', {
        error: error.message,
        sessionId: req.params.sessionId,
        username: req.user?.username,
      });
      next(error);
    }
  },
);

/**
 * 完成批量上传
 * POST /api/v1/admin/batch-upload/:sessionId/complete
 */
router.post(
  '/batch-upload/:sessionId/complete',
  authMiddleware,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'MISSING_SESSION_ID',
            message: 'Session ID is required',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(400).json(response);
        return;
      }

      // 完成批量上传
      const finalPaths = await batchUploadService.completeBatchUpload(sessionId);

      logger.info('Batch upload completed successfully', {
        sessionId,
        finalPaths,
        username: req.user?.username,
      });

      const response: SuccessResponse = {
        success: true,
        data: {
          message: 'Batch upload completed successfully',
          files: finalPaths.map(p => path.basename(p)),
          paths: finalPaths,
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.json(response);
    } catch (error: any) {
      logger.error('Failed to complete batch upload', {
        error: error.message,
        sessionId: req.params.sessionId,
        username: req.user?.username,
      });
      next(error);
    }
  },
);

/**
 * 取消批量上传
 * DELETE /api/v1/admin/batch-upload/:sessionId
 */
router.delete(
  '/batch-upload/:sessionId',
  authMiddleware,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { sessionId } = req.params;
      if (!sessionId) {
        const response: ErrorResponse = {
          success: false,
          error: {
            code: 'MISSING_SESSION_ID',
            message: 'Session ID is required',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        };
        res.status(400).json(response);
        return;
      }

      await batchUploadService.cancelBatchUpload(sessionId);

      logger.info('Batch upload cancelled', {
        sessionId,
        username: req.user?.username,
      });

      const response: SuccessResponse = {
        success: true,
        data: { message: 'Batch upload cancelled successfully' },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.json(response);
    } catch (error: any) {
      logger.error('Failed to cancel batch upload', {
        error: error.message,
        sessionId: req.params.sessionId,
        username: req.user?.username,
      });
      next(error);
    }
  }
);

/**
 * 获取批量上传进度
 * GET /api/v1/admin/batch-upload/:sessionId/progress
 */
router.get('/batch-upload/:sessionId/progress', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'MISSING_SESSION_ID',
          message: 'Session ID is required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }

    // 获取上传进度
    const progress = batchUploadService.getBatchUploadProgress(sessionId);

    if (!progress) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Upload session not found',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(404).json(response);
      return;
    }

    const response: SuccessResponse = {
      success: true,
      data: progress,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get batch upload progress', {
      error: error.message,
      sessionId: req.params.sessionId,
      username: req.user?.username,
    });
    next(error);
  }
});

export { router as adminController };
