import type { RenderableEvent } from "@/types/haptic-editor";
import { useMessage } from "naive-ui";
import { ref, type Ref, computed } from "vue";
import { checkAvailableSpace, checkEventStopTimeWithinAudioDuration } from "../utils/event-space";
import { useI18n } from "@/composables/useI18n";


// 右键菜单配置接口
export interface ContextMenuConfig {
  waveformStore: {
    selectedEventId: string | null;
    selectEvent: (eventId: string | null) => void;
    deleteSelectedEvent: () => void;
    createNewEvent: (config: any) => void;
    // addHistoryRecord 已移除 - 现在由统一机制自动处理
  };
  getEffectiveDuration: () => number;
  audioDuration?: number | null;
  showRightClickGuide: (x: number, y: number, timePoint: number) => void;
  updateRightClickGuide: (x: number, y: number, timePoint: number) => void;
  hideRightClickGuide: (shouldRedraw?: boolean) => void;
  resetDrawState: () => void;
  throttledDrawWaveform: (forceRedraw?: boolean) => void;
  handleCanvasContextMenuFromComposable: (
    mouseEvent: MouseEvent,
    canvas: HTMLCanvasElement,
    events: RenderableEvent[],
    mapTimeToXLocal: (time: number) => number,
    mapIntensityToYLocal: (intensity: number) => number,
    convertXToTimeLocal: (x: number) => number,
    closeCanvasMenu: () => void
  ) => any;
  // 撤销/重做系统接口
  undoRedoSystem?: {
    recordContextMenuOperation: (description: string, presetEvents?: RenderableEvent[]) => boolean;
    recordCurrentState?: (operationType: string, description: string) => boolean;
    canUndo?: () => boolean;
    canRedo?: () => boolean;
  };
  // 数据访问接口（用于调试）
  dataAccess?: {
    getEvents: () => RenderableEvent[];
  };
}

export function useWaveformContextMenu(config: ContextMenuConfig) {
  const message = useMessage();
  const { t } = useI18n();

  // 菜单状态管理
  const isCanvasMenuVisible = ref<boolean>(false);
  const canvasMenuPosition = ref<{ x: number; y: number }>({ x: 0, y: 0 });
  const canvasMenuTimePosition = ref<number>(0);

  // 空间可用性状态
  const canAddTransient = ref<boolean>(false);
  const canAddContinuous = ref<boolean>(false);
  const MIN_TRANSIENT_DURATION = 8;
  const MIN_CONTINUOUS_DURATION = 25;

  // 菜单自动隐藏
  const menuHideTimer = ref<number | null>(null);
  const MENU_AUTO_HIDE_DELAY = 5000;

  // 当前菜单使用的事件数据
  const currentMenuEvents = ref<RenderableEvent[]>([]);

  // 计算可用空间文本的计算属性，确保与按钮状态同步
  const availableSpaceText = computed(() => {
    if (!isCanvasMenuVisible.value || currentMenuEvents.value.length === 0) return "";

    const { availableSpace } = checkAvailableSpace({
      events: currentMenuEvents.value,
      timePoint: canvasMenuTimePosition.value,
      totalDuration: config.getEffectiveDuration(),
      minTransientSpace: MIN_TRANSIENT_DURATION,
      minContinuousSpace: MIN_CONTINUOUS_DURATION,
    });
    return t('editor.contextMenu.availableSpace', { space: Math.round(availableSpace) });
  });

  // 计算可用空间文本的函数（保持向后兼容）
  const getAvailableSpaceText = (events: RenderableEvent[]) => {
    if (!isCanvasMenuVisible.value) return "";
    // 优先使用当前菜单的事件数据，确保与按钮可用性判断一致
    const eventsToUse = currentMenuEvents.value.length > 0 ? currentMenuEvents.value : events;
    const { availableSpace } = checkAvailableSpace({
      events: eventsToUse,
      timePoint: canvasMenuTimePosition.value,
      totalDuration: config.getEffectiveDuration(),
      minTransientSpace: MIN_TRANSIENT_DURATION,
      minContinuousSpace: MIN_CONTINUOUS_DURATION,
    });
    return t('editor.contextMenu.availableSpace', { space: Math.round(availableSpace) });
  };

  // 处理右键菜单
  const handleCanvasContextMenu = (
    mouseEvent: MouseEvent,
    canvas: HTMLCanvasElement,
    events: RenderableEvent[],
    mapTimeToXLocal: (time: number) => number,
    mapIntensityToYLocal: (intensity: number) => number,
    convertXToTimeLocal: (x: number) => number
  ) => {
    if (!canvas) return;

    // 使用 Composable 中的右键菜单处理逻辑
    const result = config.handleCanvasContextMenuFromComposable(mouseEvent, canvas, events, mapTimeToXLocal, mapIntensityToYLocal, convertXToTimeLocal, closeCanvasMenu);

    if (!result) return;

    const { timePoint, position, canvasPosition } = result;

    // 更新菜单时间位置
    canvasMenuTimePosition.value = timePoint;

    // 保存当前菜单使用的事件数据，确保所有计算使用相同的数据源
    currentMenuEvents.value = [...events];

    // 检查该位置的可用空间
    const { transientAvailable, continuousAvailable } = checkAvailableSpace({
      events: currentMenuEvents.value,
      timePoint: canvasMenuTimePosition.value,
      totalDuration: config.getEffectiveDuration(),
      minTransientSpace: MIN_TRANSIENT_DURATION,
      minContinuousSpace: MIN_CONTINUOUS_DURATION,
    });

    canAddTransient.value = transientAvailable;
    canAddContinuous.value = continuousAvailable;
    canvasMenuPosition.value = position;

    // 如果菜单已经可见，则实时更新辅助线位置；否则显示菜单和辅助线
    if (isCanvasMenuVisible.value) {
      // 实时更新辅助线位置
      config.updateRightClickGuide(canvasPosition.x, canvasPosition.y, timePoint);
    } else {
      // 首次显示菜单和辅助线
      isCanvasMenuVisible.value = true;
      config.showRightClickGuide(canvasPosition.x, canvasPosition.y, timePoint);

      // 设置自动隐藏计时器
      menuHideTimer.value = window.setTimeout(() => {
        closeCanvasMenu();
      }, MENU_AUTO_HIDE_DELAY);
    }
  };

  // 处理添加瞬态事件
  const handleAddTransientEvent = (events: RenderableEvent[]) => {
    if (!canAddTransient.value) return;

    // 使用当前菜单的事件数据，确保与可用性判断一致
    const eventsToUse = currentMenuEvents.value.length > 0 ? currentMenuEvents.value : events;

    // 获取当前可用空间
    const { availableSpace } = checkAvailableSpace({
      events: eventsToUse,
      timePoint: canvasMenuTimePosition.value,
      totalDuration: config.getEffectiveDuration(),
      minTransientSpace: MIN_TRANSIENT_DURATION,
      minContinuousSpace: MIN_CONTINUOUS_DURATION,
    });

    // 校验音频时长约束
    const startTime = canvasMenuTimePosition.value;
    const width = 25 - (50 / 100) * (25 - 8); // 默认频率50对应的宽度
    const stopTime = startTime + width;

    if (!checkEventStopTimeWithinAudioDuration(stopTime, config.audioDuration ?? null)) {
      message.error(t('editor.event.exceedsAudioDuration'));
      closeCanvasMenu();
      return;
    }

    config.waveformStore.createNewEvent({
      type: "transient",
      startTime: canvasMenuTimePosition.value,
      intensity: 50,
      frequency: 50,
      availableSpace,
    });

    // 历史记录现在由store的统一机制自动处理

    closeCanvasMenu();
  };

  // 处理添加连续事件
  const handleAddContinuousEvent = (events: RenderableEvent[]) => {
    if (!canAddContinuous.value) return;

    // 历史记录现在由store的统一机制自动处理

    // 使用当前菜单的事件数据，确保与可用性判断一致
    const eventsToUse = currentMenuEvents.value.length > 0 ? currentMenuEvents.value : events;

    // 获取当前可用空间
    const { availableSpace } = checkAvailableSpace({
      events: eventsToUse,
      timePoint: canvasMenuTimePosition.value,
      totalDuration: config.getEffectiveDuration(),
      minTransientSpace: MIN_TRANSIENT_DURATION,
      minContinuousSpace: MIN_CONTINUOUS_DURATION,
    });

    // 校验音频时长约束
    const startTime = canvasMenuTimePosition.value;
    const duration = 100; // 默认duration
    const stopTime = startTime + duration;

    if (!checkEventStopTimeWithinAudioDuration(stopTime, config.audioDuration ?? null)) {
      message.error(t('editor.event.exceedsAudioDuration'));
      closeCanvasMenu();
      return;
    }

    config.waveformStore.createNewEvent({
      type: "continuous",
      startTime: canvasMenuTimePosition.value,
      intensity: 50,
      frequency: 50,
      duration: 100,
      availableSpace,
    });

    closeCanvasMenu();
  };

  // 处理删除事件
  const handleDeleteEvent = (contextMenuEventId: Ref<string | null>) => {
    if (contextMenuEventId.value) {
      // 选中要删除的事件
      config.waveformStore.selectEvent(contextMenuEventId.value);
      // 删除选中的事件
      config.waveformStore.deleteSelectedEvent();

      // 历史记录现在由store的统一机制自动处理

      // 关闭菜单
      closeCanvasMenu();
      // 重置绘制状态并重绘波形
      config.resetDrawState();
      config.throttledDrawWaveform(true);
    }
  };

  // 关闭菜单
  const closeCanvasMenu = () => {
    isCanvasMenuVisible.value = false;

    // 清除当前菜单事件数据
    currentMenuEvents.value = [];

    // 清除菜单自动隐藏计时器
    if (menuHideTimer.value !== null) {
      clearTimeout(menuHideTimer.value);
      menuHideTimer.value = null;
    }

    // 清除右键辅助线
    config.hideRightClickGuide(true); // 立即重绘以移除辅助线
  };

  // 菜单鼠标事件处理
  const handleMenuMouseEnter = () => {
    // 鼠标进入菜单时重置自动隐藏计时器
    if (menuHideTimer.value !== null) {
      clearTimeout(menuHideTimer.value);
      menuHideTimer.value = null;
    }
  };

  const handleMenuMouseLeave = () => {
    // 鼠标离开菜单时重新设置自动隐藏计时器
    if (isCanvasMenuVisible.value && menuHideTimer.value === null) {
      menuHideTimer.value = window.setTimeout(() => {
        closeCanvasMenu();
      }, MENU_AUTO_HIDE_DELAY);
    }
  };

  // 处理文档点击事件（关闭菜单）
  const handleDocumentClick = (event: MouseEvent) => {
    // 只有当菜单已显示时才处理
    if (isCanvasMenuVisible.value) {
      // 检查点击是否在菜单外部
      const menu = document.getElementById("add-event-menu");
      // 只要不是点击菜单本身，就关闭菜单（包括点击画布）
      if (menu && !menu.contains(event.target as Node)) {
        closeCanvasMenu(); // 这会同时隐藏菜单和辅助线
      }
    }
  };

  // 清理函数（用于组件卸载时）
  const cleanup = () => {
    // 清除菜单自动隐藏计时器
    if (menuHideTimer.value !== null) {
      clearTimeout(menuHideTimer.value);
      menuHideTimer.value = null;
    }
  };

  return {
    // 状态
    isCanvasMenuVisible,
    canvasMenuPosition,
    canvasMenuTimePosition,
    canAddTransient,
    canAddContinuous,
    availableSpaceText,
    getAvailableSpaceText,
    menuHideTimer,

    // 常量
    MIN_TRANSIENT_DURATION,
    MIN_CONTINUOUS_DURATION,
    MENU_AUTO_HIDE_DELAY,

    // 事件处理函数
    handleCanvasContextMenu,
    handleAddTransientEvent,
    handleAddContinuousEvent,
    handleDeleteEvent,
    closeCanvasMenu,
    handleMenuMouseEnter,
    handleMenuMouseLeave,
    handleDocumentClick,
    cleanup,
  };
}
