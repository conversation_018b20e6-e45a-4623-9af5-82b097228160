package android.os.vibrator.realitytap.parser;

import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_CURVE;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_DURATION;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_EVENT;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_FREQUENCY;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_INDEX;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_INTENSITY;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_PARAMETERS;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_PATTERN;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_RELATIVE_TIME;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_TIME;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_TYPE;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import android.os.vibrator.realitytap.he.Curve;
import android.os.vibrator.realitytap.he.Event;
import android.os.vibrator.realitytap.he.RealityTapEffect;
import android.os.vibrator.realitytap.he.RealityTapEffectV1;
import android.os.vibrator.realitytap.he.Parameters;

/** @hide */
class JsonParserV1 extends AbstractJsonParser {
    private static final int CURVE_POINT_COUNT = 4;

    @Override
    RealityTapEffect parseInternal(JSONObject topNode) throws JSONException {
        if (!topNode.has(KEY_PATTERN)) {
            throw new JSONException("Haptic json lost pattern information.");
        }
        RealityTapEffectV1 effect = new RealityTapEffectV1();
        JSONArray patternArray = topNode.getJSONArray(KEY_PATTERN);
        if (patternArray.length() == 0) {
            throw new JSONException("Haptic json lost pattern information.");
        }
        for (int i = 0; i < patternArray.length(); i++) {
            JSONObject eventNode = patternArray.getJSONObject(i).getJSONObject(KEY_EVENT);
            JSONObject paramNode = eventNode.getJSONObject(KEY_PARAMETERS);

            final int patternIntensity = paramNode.getInt(KEY_INTENSITY);
            final int patternFrequency = paramNode.getInt(KEY_FREQUENCY);

            validatePatternIntensity(patternIntensity);
            validatePatternFrequency(patternFrequency);

            Event event = new Event();
            Parameters parameters = new Parameters();
            parameters.setIntensity(patternIntensity);
            parameters.setFrequency(patternFrequency);
            event.setParameters(parameters);

            if (paramNode.has(KEY_CURVE)) {
                JSONArray curveArray = paramNode.getJSONArray(KEY_CURVE);
                final int curveCount = curveArray.length();
                validateCurvePointCount(curveCount);

                for (int j = 0; j < curveCount; j++) {
                    JSONObject curveNode = curveArray.getJSONObject(j);
                    Curve curve = new Curve();
                    curve.setTime(curveNode.getInt(KEY_TIME));

                    final double intensity = curveNode.getDouble(KEY_INTENSITY);
                    validateCurveIntensity(intensity);
                    curve.setIntensity(intensity);

                    final int frequency = curveNode.getInt(KEY_FREQUENCY);
                    validateCurveFrequency(frequency);
                    curve.setFrequency(frequency);

                    parameters.getCurves().add(curve);
                }
            }

            if (eventNode.has(KEY_DURATION)) {
                event.setDuration(eventNode.getInt(KEY_DURATION));
            }
            if (eventNode.has(KEY_RELATIVE_TIME)) {
                event.setRelativeTime(eventNode.getInt(KEY_RELATIVE_TIME));
            }
            if (eventNode.has(KEY_INDEX)) {
                event.setIndex(eventNode.getInt(KEY_INDEX));
            }
            if (eventNode.has(KEY_TYPE)) {
                event.setType(eventNode.getString(KEY_TYPE));
            } else {
                throw new JSONException("Haptic json lost event type information.");
            }

            effect.getPattern().add(event);
        }

        return effect;
    }

    private static void validateCurvePointCount(int curveCount) throws JSONException {
        if (curveCount != CURVE_POINT_COUNT) {
            throw new JSONException("Curve must have " + CURVE_POINT_COUNT + " points.");
        }
    }

    private static void validatePatternIntensity(int patternIntensity) throws JSONException {
        if (patternIntensity < 0 || patternIntensity > 100) {
            throw new JSONException("Pattern intensity must be between 0 and 100.");
        }
    }

    private static void validatePatternFrequency(int patternFrequency) throws JSONException {
        if (patternFrequency < -100 || patternFrequency > 100) {
            throw new JSONException("Pattern frequency must be between -100 and 100.");
        }
    }

    private static void validateCurveIntensity(double intensity) throws JSONException {
        if (intensity < 0 || intensity > 1) {
            throw new JSONException("Curve point intensity must be between 0 and 1.");
        }
    }

    private static void validateCurveFrequency(int frequency) throws JSONException {
        if (frequency < -100 || frequency > 100) {
            throw new JSONException("Curve point frequency must be between -100 and 100.");
        }
    }
}
