import type { RenderableEvent, CreateEventConfig } from "../types";
import { createEvent } from "../utils/event-factory";
import { sortEventsByStartTime, calculateEventsTotalDuration } from "../utils/converters";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 事件操作相关接口
 */
export interface EventActions {
  createNewEvent: (eventConfig: CreateEventConfig) => void;
  deleteSelectedEvent: () => void;
  deleteEventById: (eventId: string) => void;
  updateEvent: (eventId: string, updates: Partial<RenderableEvent>) => void;
  setEvents: (events: RenderableEvent[]) => void;
}

/**
 * 创建事件操作
 */
export function createEventActions(
  state: {
    events: RenderableEvent[];
    selectedEventId: string | null;
    selectedCurvePointIndex: number;
    totalDuration: number;
    isDurationLockedByAudio: boolean;
  },
  setState: (updates: Partial<typeof state>) => void,
  fileUuid: string,
  onFileStateChange?: (fileUuid: string, events: RenderableEvent[]) => void
): EventActions {
  const markFileAsUnsaved = () => {
    try {
      import("../../haptics-project-store").then(({ useProjectStore }) => {
        const projectStore = useProjectStore();
        if (projectStore.selectedFileUuid) {
          projectStore.markFileAsUnsaved(projectStore.selectedFileUuid);
          projectStore.updateFileCacheEvents(projectStore.selectedFileUuid, state.events);
        }
      });
    } catch (error) {
      logger.warn(LogModule.WAVEFORM, "Failed to mark file as unsaved or update cache", error);
    }
  };

  return {
    /**
     * 创建新事件
     */
    createNewEvent(eventConfig: CreateEventConfig) {
      logger.debug(LogModule.WAVEFORM, "创建新事件", eventConfig);

      // 标记文件为未保存状态
      markFileAsUnsaved();

      // 创建新事件
      const newEvent = createEvent(eventConfig);

      // 添加到事件列表并排序
      const updatedEvents = sortEventsByStartTime([...state.events, newEvent]);

      // 计算新的总时长
      let newTotalDuration = state.totalDuration;
      if (!state.isDurationLockedByAudio) {
        const calculatedDuration = calculateEventsTotalDuration(updatedEvents);
        if (calculatedDuration > state.totalDuration) {
          newTotalDuration = calculatedDuration;
        }
      }

      // 更新状态
      setState({
        events: updatedEvents,
        totalDuration: newTotalDuration,
        selectedEventId: newEvent.id,
        selectedCurvePointIndex: -1,
      });

      // 通知文件状态变化
      onFileStateChange?.(fileUuid, updatedEvents);

      logger.debug(LogModule.WAVEFORM, `新事件创建完成: ${newEvent.id}`);
    },

    /**
     * 删除选中的事件
     */
    deleteSelectedEvent() {
      if (!state.selectedEventId) {
        logger.warn(LogModule.WAVEFORM, "没有选中的事件可删除");
        return;
      }

      this.deleteEventById(state.selectedEventId);
    },

    /**
     * 根据ID删除事件
     */
    deleteEventById(eventId: string) {
      logger.debug(LogModule.WAVEFORM, `删除事件: ${eventId}`);

      const eventIndex = state.events.findIndex((e) => e.id === eventId);
      if (eventIndex === -1) {
        logger.warn(LogModule.WAVEFORM, `事件不存在: ${eventId}`);
        return;
      }

      // 标记文件为未保存状态
      markFileAsUnsaved();

      // 删除事件
      const updatedEvents = state.events.filter((e) => e.id !== eventId);

      // 清除选中状态（如果删除的是选中的事件）
      const newSelectedEventId = state.selectedEventId === eventId ? null : state.selectedEventId;
      const newSelectedCurvePointIndex = state.selectedEventId === eventId ? -1 : state.selectedCurvePointIndex;

      // 更新状态
      setState({
        events: updatedEvents,
        selectedEventId: newSelectedEventId,
        selectedCurvePointIndex: newSelectedCurvePointIndex,
      });

      // 通知文件状态变化
      onFileStateChange?.(fileUuid, updatedEvents);

      logger.debug(LogModule.WAVEFORM, `事件删除完成，剩余事件数: ${updatedEvents.length}`);
    },

    /**
     * 更新事件
     */
    updateEvent(eventId: string, updates: Partial<RenderableEvent>) {
      logger.debug(LogModule.WAVEFORM, `更新事件: ${eventId}`, updates);

      const eventIndex = state.events.findIndex((e) => e.id === eventId);
      if (eventIndex === -1) {
        logger.warn(LogModule.WAVEFORM, `事件不存在: ${eventId}`);
        return;
      }

      // 标记文件为未保存状态
      markFileAsUnsaved();

      // 更新事件
      const updatedEvents = [...state.events];
      updatedEvents[eventIndex] = { ...updatedEvents[eventIndex], ...updates } as RenderableEvent;

      // 重新排序
      const sortedEvents = sortEventsByStartTime(updatedEvents);

      // 重新计算总时长
      let newTotalDuration = state.totalDuration;
      if (!state.isDurationLockedByAudio) {
        const calculatedDuration = calculateEventsTotalDuration(sortedEvents);
        if (calculatedDuration > state.totalDuration) {
          newTotalDuration = calculatedDuration;
        }
      }

      // 更新状态
      setState({
        events: sortedEvents,
        totalDuration: newTotalDuration,
      });

      // 通知文件状态变化
      onFileStateChange?.(fileUuid, sortedEvents);

      logger.debug(LogModule.WAVEFORM, `事件更新完成: ${eventId}`);
    },

    /**
     * 设置事件列表
     */
    setEvents(events: RenderableEvent[]) {
      logger.debug(LogModule.WAVEFORM, `设置事件列表: ${events.length} 个事件`);

      // 排序事件
      const sortedEvents = sortEventsByStartTime([...events]);

      // 检查选中的事件是否仍然存在
      const stillExists = sortedEvents.find((e) => e.id === state.selectedEventId);
      const newSelectedEventId = stillExists ? state.selectedEventId : null;
      const newSelectedCurvePointIndex = stillExists ? state.selectedCurvePointIndex : -1;

      // 更新状态
      setState({
        events: sortedEvents,
        selectedEventId: newSelectedEventId,
        selectedCurvePointIndex: newSelectedCurvePointIndex,
      });

      // 通知文件状态变化
      onFileStateChange?.(fileUuid, sortedEvents);

      logger.debug(LogModule.WAVEFORM, `事件列表设置完成: ${sortedEvents.length} 个事件`);
    },
  };
}
