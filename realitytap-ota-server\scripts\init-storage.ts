#!/usr/bin/env tsx

/**
 * 存储初始化脚本
 * 用于初始化OTA服务器的存储结构和配置文件
 */

import fs from 'fs/promises';
import path from 'path';
import { logger } from '../src/utils/logger.util';

interface InitOptions {
  force?: boolean;
  verbose?: boolean;
}

/**
 * 默认版本配置
 */
const DEFAULT_VERSIONS_CONFIG = {
  channels: {
    stable: {
      version: "1.0.0",
      platforms: {
        windows: {
          x86_64: {
            filename: "realitytap-1.0.0-windows-x86_64.exe",
            size: 52428800,
            checksum: "sha256:placeholder_checksum_replace_with_actual",
            releaseDate: new Date().toISOString(),
            releaseNotes: "Initial stable release"
          }
        },
        macos: {
          x86_64: {
            filename: "realitytap-1.0.0-macos-x86_64.dmg",
            size: 48234567,
            checksum: "sha256:placeholder_checksum_replace_with_actual",
            releaseDate: new Date().toISOString(),
            releaseNotes: "Initial stable release"
          },
          aarch64: {
            filename: "realitytap-1.0.0-macos-aarch64.dmg",
            size: 45123456,
            checksum: "sha256:placeholder_checksum_replace_with_actual",
            releaseDate: new Date().toISOString(),
            releaseNotes: "Initial stable release"
          }
        },
        linux: {
          x86_64: {
            filename: "realitytap-1.0.0-linux-x86_64.tar.gz",
            size: 42345678,
            checksum: "sha256:placeholder_checksum_replace_with_actual",
            releaseDate: new Date().toISOString(),
            releaseNotes: "Initial stable release"
          }
        }
      }
    }
  },
  minimumVersions: {
    stable: "0.9.0",
    beta: "1.0.0",
    alpha: "1.0.0"
  },
  deprecatedVersions: []
};

/**
 * 默认渠道配置
 */
const DEFAULT_CHANNELS_CONFIG = {
  stable: {
    enabled: true,
    description: "稳定版本，推荐生产环境使用",
    autoUpdate: true,
    rolloutPercentage: 100,
    priority: 1
  },
  beta: {
    enabled: true,
    description: "测试版本，包含最新功能",
    autoUpdate: false,
    rolloutPercentage: 50,
    priority: 2
  },
  alpha: {
    enabled: false,
    description: "开发版本，仅供内部测试",
    autoUpdate: false,
    rolloutPercentage: 10,
    priority: 3
  }
};

/**
 * 需要创建的目录结构
 */
const REQUIRED_DIRECTORIES = [
  'storage',
  'storage/releases',
  'storage/releases/stable',
  'storage/releases/beta',
  'storage/releases/alpha',
  'storage/metadata',
  'storage/logs',
  'storage/temp',
  'storage/backup'
];

/**
 * 创建目录
 */
async function createDirectories(baseDir: string, options: InitOptions): Promise<void> {
  for (const dir of REQUIRED_DIRECTORIES) {
    const fullPath = path.join(baseDir, dir);
    
    try {
      await fs.access(fullPath);
      if (options.verbose) {
        console.log(`✓ 目录已存在: ${fullPath}`);
      }
    } catch {
      await fs.mkdir(fullPath, { recursive: true });
      console.log(`✓ 创建目录: ${fullPath}`);
    }
  }
}

/**
 * 创建配置文件
 */
async function createConfigFiles(baseDir: string, options: InitOptions): Promise<void> {
  const versionsPath = path.join(baseDir, 'storage/metadata/versions.json');
  const channelsPath = path.join(baseDir, 'storage/metadata/channels.json');

  // 创建版本配置文件
  try {
    await fs.access(versionsPath);
    if (!options.force) {
      console.log(`⚠ 版本配置文件已存在: ${versionsPath}`);
      console.log(`  使用 --force 参数强制覆盖`);
    } else {
      await fs.writeFile(versionsPath, JSON.stringify(DEFAULT_VERSIONS_CONFIG, null, 2));
      console.log(`✓ 覆盖版本配置文件: ${versionsPath}`);
    }
  } catch {
    await fs.writeFile(versionsPath, JSON.stringify(DEFAULT_VERSIONS_CONFIG, null, 2));
    console.log(`✓ 创建版本配置文件: ${versionsPath}`);
  }

  // 创建渠道配置文件
  try {
    await fs.access(channelsPath);
    if (!options.force) {
      console.log(`⚠ 渠道配置文件已存在: ${channelsPath}`);
      console.log(`  使用 --force 参数强制覆盖`);
    } else {
      await fs.writeFile(channelsPath, JSON.stringify(DEFAULT_CHANNELS_CONFIG, null, 2));
      console.log(`✓ 覆盖渠道配置文件: ${channelsPath}`);
    }
  } catch {
    await fs.writeFile(channelsPath, JSON.stringify(DEFAULT_CHANNELS_CONFIG, null, 2));
    console.log(`✓ 创建渠道配置文件: ${channelsPath}`);
  }
}

/**
 * 创建示例文件
 */
async function createExampleFiles(baseDir: string): Promise<void> {
  const readmePath = path.join(baseDir, 'storage/README.md');
  const readmeContent = `# RealityTap OTA 存储目录

## 目录结构

- \`releases/\` - 发布文件存储
  - \`stable/\` - 稳定版本
  - \`beta/\` - 测试版本
  - \`alpha/\` - 开发版本
- \`metadata/\` - 元数据文件
  - \`versions.json\` - 版本配置
  - \`channels.json\` - 渠道配置
- \`logs/\` - 日志文件
- \`temp/\` - 临时文件
- \`backup/\` - 备份文件

## 使用说明

1. 将发布文件放入对应的 \`releases/\` 子目录
2. 更新 \`metadata/versions.json\` 中的版本信息
3. 根据需要调整 \`metadata/channels.json\` 中的渠道配置

## 注意事项

- 确保文件名与配置中的 \`filename\` 字段一致
- 计算并更新正确的文件校验和
- 定期清理临时文件和旧版本
`;

  try {
    await fs.access(readmePath);
    console.log(`⚠ README文件已存在: ${readmePath}`);
  } catch {
    await fs.writeFile(readmePath, readmeContent);
    console.log(`✓ 创建README文件: ${readmePath}`);
  }
}

/**
 * 验证存储结构
 */
async function validateStorage(baseDir: string): Promise<boolean> {
  let isValid = true;

  // 检查必需目录
  for (const dir of REQUIRED_DIRECTORIES) {
    const fullPath = path.join(baseDir, dir);
    try {
      const stat = await fs.stat(fullPath);
      if (!stat.isDirectory()) {
        console.error(`❌ ${fullPath} 不是目录`);
        isValid = false;
      }
    } catch {
      console.error(`❌ 缺少目录: ${fullPath}`);
      isValid = false;
    }
  }

  // 检查配置文件
  const configFiles = [
    'storage/metadata/versions.json',
    'storage/metadata/channels.json'
  ];

  for (const file of configFiles) {
    const fullPath = path.join(baseDir, file);
    try {
      const content = await fs.readFile(fullPath, 'utf-8');
      JSON.parse(content); // 验证JSON格式
    } catch (error) {
      console.error(`❌ 配置文件错误: ${fullPath}`, error);
      isValid = false;
    }
  }

  return isValid;
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const options: InitOptions = {
    force: args.includes('--force') || args.includes('-f'),
    verbose: args.includes('--verbose') || args.includes('-v')
  };

  const baseDir = process.cwd();

  console.log('🚀 初始化 RealityTap OTA 存储结构...\n');

  try {
    // 创建目录结构
    console.log('📁 创建目录结构...');
    await createDirectories(baseDir, options);

    // 创建配置文件
    console.log('\n⚙️ 创建配置文件...');
    await createConfigFiles(baseDir, options);

    // 创建示例文件
    console.log('\n📄 创建示例文件...');
    await createExampleFiles(baseDir);

    // 验证存储结构
    console.log('\n✅ 验证存储结构...');
    const isValid = await validateStorage(baseDir);

    if (isValid) {
      console.log('\n🎉 存储结构初始化完成！');
      console.log('\n📝 下一步：');
      console.log('1. 将发布文件放入 storage/releases/ 对应目录');
      console.log('2. 更新 storage/metadata/versions.json 中的版本信息');
      console.log('3. 计算并更新文件校验和');
      console.log('4. 启动 OTA 服务器');
    } else {
      console.error('\n❌ 存储结构验证失败，请检查错误信息');
      process.exit(1);
    }

  } catch (error) {
    console.error('\n❌ 初始化失败:', error);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

export { main as initStorage };
