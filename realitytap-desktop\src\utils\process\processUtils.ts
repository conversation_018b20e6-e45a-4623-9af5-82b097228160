/**
 * 进程相关工具函数
 * 提供进程管理的辅助功能和常量定义
 */

import type { ProcessInfo, ProcessCloseResult } from '@/services/process-manager.service';

// === 常量定义 ===

export const PROCESS_TYPES = {
  MAIN_APPLICATION: 'MainApplication',
  EDITOR_WINDOW: 'EditorWindow',
  RENDER_PROCESS: 'RenderProcess',
  AUDIO_SERVICE: 'AudioService',
  FILE_MONITOR: 'FileMonitor',
  BACKGROUND_SERVICE: 'BackgroundService',
  CHILD_PROCESS: 'ChildProcess',
  UNKNOWN: 'Unknown',
} as const;

export const CLOSE_METHODS = {
  GRACEFUL: 'Graceful',
  FORCED: 'Forced',
  FAILED: 'Failed',
  SKIPPED: 'Skipped',
} as const;

export const PROCESS_PRIORITIES = {
  CRITICAL: 100,
  HIGH: 90,
  NORMAL: 80,
  LOW: 70,
  BACKGROUND: 60,
  MINIMAL: 50,
} as const;

// === 类型定义 ===

export type ProcessType = typeof PROCESS_TYPES[keyof typeof PROCESS_TYPES];
export type CloseMethod = typeof CLOSE_METHODS[keyof typeof CLOSE_METHODS];

export interface ProcessStatistics {
  totalProcesses: number;
  criticalProcesses: number;
  runningProcesses: number;
  totalMemoryUsage: number;
  averageCpuUsage: number;
}

export interface ProcessCloseStatistics {
  totalAttempts: number;
  successfulCloses: number;
  failedCloses: number;
  gracefulCloses: number;
  forcedCloses: number;
  averageCloseTime: number;
  totalCloseTime: number;
}

// === 工具函数 ===

/**
 * 获取进程类型的显示名称
 */
export function getProcessTypeDisplayName(processType: string): string {
  switch (processType) {
    case PROCESS_TYPES.MAIN_APPLICATION:
      return '主应用程序';
    case PROCESS_TYPES.EDITOR_WINDOW:
      return '编辑器窗口';
    case PROCESS_TYPES.RENDER_PROCESS:
      return '渲染进程';
    case PROCESS_TYPES.AUDIO_SERVICE:
      return '音频服务';
    case PROCESS_TYPES.FILE_MONITOR:
      return '文件监控';
    case PROCESS_TYPES.BACKGROUND_SERVICE:
      return '后台服务';
    case PROCESS_TYPES.CHILD_PROCESS:
      return '子进程';
    default:
      return '未知进程';
  }
}

/**
 * 获取关闭方法的显示名称
 */
export function getCloseMethodDisplayName(closeMethod: string): string {
  switch (closeMethod) {
    case CLOSE_METHODS.GRACEFUL:
      return '优雅关闭';
    case CLOSE_METHODS.FORCED:
      return '强制关闭';
    case CLOSE_METHODS.FAILED:
      return '关闭失败';
    case CLOSE_METHODS.SKIPPED:
      return '已跳过';
    default:
      return '未知方法';
  }
}

// 导入统一的格式化函数
import { formatMemoryUsage, formatCpuUsage } from '@/utils/format';

// 重新导出格式化函数以保持向后兼容
export { formatMemoryUsage, formatCpuUsage };

/**
 * 格式化持续时间
 */
export function formatDuration(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(1)}s`;
  } else {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = ((milliseconds % 60000) / 1000).toFixed(1);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * 判断进程是否为关键进程
 */
export function isCriticalProcess(process: ProcessInfo): boolean {
  return process.isCritical || process.processType === PROCESS_TYPES.MAIN_APPLICATION;
}

/**
 * 按优先级排序进程（优先级高的先关闭）
 */
export function sortProcessesByPriority(processes: ProcessInfo[]): ProcessInfo[] {
  return [...processes].sort((a, b) => {
    // 首先按优先级排序
    if (a.priority !== b.priority) {
      return b.priority - a.priority;
    }
    
    // 优先级相同时，关键进程排在后面
    if (a.isCritical !== b.isCritical) {
      return a.isCritical ? 1 : -1;
    }
    
    // 最后按PID排序
    return a.pid - b.pid;
  });
}

/**
 * 过滤关键进程
 */
export function filterCriticalProcesses(processes: ProcessInfo[]): ProcessInfo[] {
  return processes.filter(process => isCriticalProcess(process));
}

/**
 * 过滤非关键进程
 */
export function filterNonCriticalProcesses(processes: ProcessInfo[]): ProcessInfo[] {
  return processes.filter(process => !isCriticalProcess(process));
}

/**
 * 计算进程统计信息
 */
export function calculateProcessStatistics(processes: ProcessInfo[]): ProcessStatistics {
  const totalProcesses = processes.length;
  const criticalProcesses = processes.filter(p => isCriticalProcess(p)).length;
  const runningProcesses = processes.length; // 所有检测到的进程都是运行中的
  
  const totalMemoryUsage = processes.reduce((sum, p) => sum + p.memoryUsage, 0);
  const averageCpuUsage = processes.length > 0 
    ? processes.reduce((sum, p) => sum + p.cpuUsage, 0) / processes.length 
    : 0;

  return {
    totalProcesses,
    criticalProcesses,
    runningProcesses,
    totalMemoryUsage,
    averageCpuUsage,
  };
}

/**
 * 计算进程关闭统计信息
 */
export function calculateCloseStatistics(results: ProcessCloseResult[]): ProcessCloseStatistics {
  const totalAttempts = results.length;
  const successfulCloses = results.filter(r => r.success).length;
  const failedCloses = results.filter(r => !r.success).length;
  
  const gracefulCloses = results.filter(r => r.closeMethod === CLOSE_METHODS.GRACEFUL && r.success).length;
  const forcedCloses = results.filter(r => r.closeMethod === CLOSE_METHODS.FORCED && r.success).length;
  
  const totalCloseTime = results.reduce((sum, r) => sum + r.closeDurationMs, 0);
  const averageCloseTime = totalAttempts > 0 ? totalCloseTime / totalAttempts : 0;

  return {
    totalAttempts,
    successfulCloses,
    failedCloses,
    gracefulCloses,
    forcedCloses,
    averageCloseTime,
    totalCloseTime,
  };
}

/**
 * 验证进程信息
 */
export function validateProcessInfo(process: ProcessInfo): boolean {
  return (
    typeof process.pid === 'number' &&
    process.pid > 0 &&
    typeof process.name === 'string' &&
    process.name.length > 0 &&
    typeof process.processType === 'string' &&
    typeof process.priority === 'number' &&
    typeof process.isCritical === 'boolean'
  );
}

/**
 * 验证进程关闭结果
 */
export function validateCloseResult(result: ProcessCloseResult): boolean {
  return (
    typeof result.pid === 'number' &&
    result.pid > 0 &&
    typeof result.name === 'string' &&
    result.name.length > 0 &&
    typeof result.success === 'boolean' &&
    typeof result.closeMethod === 'string' &&
    typeof result.closeDurationMs === 'number' &&
    result.closeDurationMs >= 0
  );
}

/**
 * 创建进程摘要信息
 */
export function createProcessSummary(processes: ProcessInfo[]): string {
  const stats = calculateProcessStatistics(processes);
  const memoryMB = (stats.totalMemoryUsage / (1024 * 1024)).toFixed(1);
  
  return `检测到 ${stats.totalProcesses} 个进程（${stats.criticalProcesses} 个关键进程），` +
         `总内存使用: ${memoryMB}MB，平均CPU使用: ${stats.averageCpuUsage.toFixed(1)}%`;
}

/**
 * 创建关闭结果摘要
 */
export function createCloseResultSummary(results: ProcessCloseResult[]): string {
  const stats = calculateCloseStatistics(results);
  const successRate = stats.totalAttempts > 0 
    ? ((stats.successfulCloses / stats.totalAttempts) * 100).toFixed(1)
    : '0';
  
  return `关闭 ${stats.totalAttempts} 个进程，成功率: ${successRate}%，` +
         `平均耗时: ${formatDuration(stats.averageCloseTime)}`;
}

/**
 * 检查是否所有进程都成功关闭
 */
export function areAllProcessesClosed(results: ProcessCloseResult[]): boolean {
  return results.length > 0 && results.every(result => result.success);
}

/**
 * 获取失败的进程关闭结果
 */
export function getFailedCloseResults(results: ProcessCloseResult[]): ProcessCloseResult[] {
  return results.filter(result => !result.success);
}

/**
 * 获取成功的进程关闭结果
 */
export function getSuccessfulCloseResults(results: ProcessCloseResult[]): ProcessCloseResult[] {
  return results.filter(result => result.success);
}
