/**
 * 更新对话框共享组合函数
 * 提供 ForceUpdateDialog 和 UpdateDialog 的通用逻辑
 */

import { computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useUpdater } from '@/composables/useUpdater';
import type { UpdateInfo } from '@/composables/useUpdater';
import { logger, LogModule } from '@/utils/logger/logger';

// === 共享的 Props 接口 ===
export interface UpdateDialogProps {
  visible: boolean;
  updateInfo: UpdateInfo | null;
  currentVersion: string;
}

// === 共享的 Emits 接口 ===
export interface UpdateDialogEmits {
  'update:visible': [value: boolean];
  'remind-later': [];
  'install-later': [];
}

/**
 * 更新对话框共享逻辑
 */
export function useUpdateDialog(props: UpdateDialogProps) {
  const { t } = useI18n();
  const updater = useUpdater();

  // === 同步 props.updateInfo 到 useUpdater ===
  watch(
    () => props.updateInfo,
    (val) => {
      if (val && typeof updater.setUpdateInfo === 'function') {
        updater.setUpdateInfo(val);
      }
    },
    { immediate: true }
  );

  // === 计算属性 ===
  const downloadProgressPercentage = computed(() => {
    return updater.downloadProgress.value.percentage || 0;
  });

  const updateStatusTitle = computed(() => {
    if (updater.isDownloaded.value) {
      return t('update.readyToInstall');
    }
    return t('update.newVersionAvailable');
  });

  const forceUpdateStatusTitle = computed(() => {
    if (updater.isDownloaded.value) {
      return t('forceUpdate.readyToInstall');
    }
    return t('forceUpdate.newVersionRequired');
  });

  // === 工具函数 ===
  
  /**
   * 获取预期文件大小
   */
  const getExpectedFileSize = (): number => {
    // 优先使用下载进度中的总大小，如果为0则使用更新信息中的文件大小
    const progressTotal = updater.downloadProgress.value.total || 0;
    const updateInfoSize = props.updateInfo?.file_size || 0;
    return progressTotal || updateInfoSize;
  };

  // === 事件处理函数 ===
  
  /**
   * 开始下载更新
   */
  const handleStartDownload = async () => {
    logger.info(LogModule.GENERAL, '开始下载更新');
    await updater.downloadUpdate();
  };

  /**
   * 开始安装更新
   */
  const handleStartInstall = async () => {
    logger.info(LogModule.GENERAL, '开始安装更新');
    await updater.installUpdate();
  };

  /**
   * 重试下载
   */
  const handleRetryDownload = async () => {
    logger.info(LogModule.GENERAL, '重试下载更新');
    updater.clearError();
    await updater.downloadUpdate();
  };

  /**
   * 退出应用程序（用于强制更新）
   */
  const handleExitApplication = async () => {
    logger.info(LogModule.GENERAL, '用户选择退出应用程序');
    await updater.exitApplication();
  };

  /**
   * 下载并安装（一步完成）
   */
  const handleDownloadAndInstall = async () => {
    logger.info(LogModule.GENERAL, '开始下载并安装更新');
    await updater.downloadAndInstall();
  };

  return {
    // 组合函数
    t,
    updater,
    
    // 计算属性
    downloadProgressPercentage,
    updateStatusTitle,
    forceUpdateStatusTitle,
    
    // 工具函数
    getExpectedFileSize,
    
    // 事件处理函数
    handleStartDownload,
    handleStartInstall,
    handleRetryDownload,
    handleExitApplication,
    handleDownloadAndInstall,
  };
}

/**
 * 获取更新对话框的默认 props
 */
export function getUpdateDialogDefaultProps(): UpdateDialogProps {
  return {
    visible: false,
    updateInfo: null,
    currentVersion: '1.0.0',
  };
}

/**
 * 创建更新对话框的 props 定义
 */
export function createUpdateDialogProps() {
  return {
    visible: {
      type: Boolean,
      default: false,
    },
    updateInfo: {
      type: Object as () => UpdateInfo | null,
      default: null,
    },
    currentVersion: {
      type: String,
      default: '1.0.0',
    },
  };
}
