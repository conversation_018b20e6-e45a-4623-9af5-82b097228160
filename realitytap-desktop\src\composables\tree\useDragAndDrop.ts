import { ref } from "vue";
import { useMessage } from "naive-ui";
import type { useProjectStore } from "@/stores/haptics-project-store";
import { dragLogger } from "@/utils/logger/logger";

/**
 * 拖拽操作管理 Composable
 * 处理文件和分组的拖拽移动功能
 */
export function useDragAndDrop(
  projectStore: ReturnType<typeof useProjectStore>,
  expandedTreeKeys: ReturnType<typeof ref<string[]>>,
  selectedTreeKeys: ReturnType<typeof ref<string[]>>,
  findNodeByKey: (nodes: any[], key: string) => any,
  treeData: ReturnType<typeof ref<any[]>>,
  t: (key: string, values?: Record<string, any>) => string
) {
  const message = useMessage();

  // 拖拽状态管理
  const draggedNodeType = ref<"file" | "group" | null>(null);
  const draggedNodeKey = ref<string | null>(null);
  const isDragging = ref(false);

  /**
   * 处理树形拖拽
   */
  const handleTreeDrop = async (info: any) => {
    const dragKey = String(info.dragNode?.key || "");
    const dropKey = info.node ? String(info.node.key) : null;
    const isDragGroup = dragKey.startsWith("group-");
    const isDragFile = dragKey.startsWith("file-");
    const isDropGroup = dropKey && dropKey.startsWith("group-");
    const isDropFile = dropKey && dropKey.startsWith("file-");

    // 智能重定向：当拖拽到文件节点时，重定向到该文件所在的分组
    let finalDropKey = dropKey;
    let finalIsDropGroup = isDropGroup;

    if (isDropFile) {
      // 获取目标文件的分组信息
      const targetFileUuid = dropKey.replace(/^file-/, "");
      const targetFile = projectStore.activeProject?.files?.find(f => f.fileUuid === targetFileUuid);

      if (targetFile) {
        if (targetFile.group) {
          // 文件在某个分组中，重定向到该分组
          finalDropKey = `group-${targetFile.group}`;
          finalIsDropGroup = true;
          dragLogger.debug(`智能重定向：拖拽到文件 ${targetFile.name}，重定向到分组 ${targetFile.group}`);
        } else {
          // 文件在根目录，重定向到根目录（null）
          finalDropKey = null;
          finalIsDropGroup = false;
          dragLogger.debug(`智能重定向：拖拽到文件 ${targetFile.name}，重定向到根目录`);
        }
      } else {
        message.warning(t('editor.dragAndDrop.targetFileNotFound'));
        return;
      }
    }

    if (isDragGroup && finalIsDropGroup) {
      const isDescendant = (parentKey: string, childKey: string): boolean => {
        const parentNode = findNodeByKey(treeData.value ?? [], parentKey);
        if (!parentNode || !parentNode.children) return false;
        for (const child of parentNode.children) {
          if (child.key === childKey) return true;
          if (child.children && isDescendant(child.key, childKey)) return true;
        }
        return false;
      };

      if (dragKey === finalDropKey || (finalDropKey && isDescendant(dragKey, finalDropKey))) {
        message.warning(t('editor.dragAndDrop.cannotDropOnSelfOrDescendant'));
        return;
      }
    }

    try {
      isDragging.value = true; // 标记正在处理拖拽

      if (isDragFile) {
        // 文件拖到分组或根目录（使用重定向后的目标）
        const fileUuid = dragKey.replace(/^file-/, "");
        const targetGroupUuid = finalIsDropGroup && finalDropKey ? finalDropKey.replace(/^group-/, "") : null;



        await projectStore.moveFile(fileUuid, targetGroupUuid);

        // 拖拽后自动选中目标分组并展开
        if (finalIsDropGroup && finalDropKey) {
          selectedTreeKeys.value = [finalDropKey];
          if (!(expandedTreeKeys.value ?? []).includes(finalDropKey)) {
            (expandedTreeKeys.value ?? []).push(finalDropKey);
          }
        } else {
          selectedTreeKeys.value = [];
        }

        // 根据实际移动的目标显示成功消息
        if (targetGroupUuid) {
          // 移动到了分组
          const targetGroup = projectStore.activeProject?.groups?.find(g => g.groupUuid === targetGroupUuid);
          message.success(t('editor.dragAndDrop.fileMovedToGroup', { groupName: targetGroup?.name || t('editor.dragAndDrop.unknownGroup') }));
        } else {
          // 移动到了根目录
          message.success(t('editor.dragAndDrop.fileMovedToRoot'));
        }
      } else if (isDragGroup) {
        // 分组拖到其它分组或根目录（使用重定向后的目标）
        const groupUuid = dragKey.replace(/^group-/, "");
        const newParentGroupUuid = finalIsDropGroup && finalDropKey ? finalDropKey.replace(/^group-/, "") : null;
        await projectStore.moveGroup(groupUuid, newParentGroupUuid);

        if (finalIsDropGroup && finalDropKey) {
          selectedTreeKeys.value = [finalDropKey];
          if (!(expandedTreeKeys.value ?? []).includes(finalDropKey)) {
            (expandedTreeKeys.value ?? []).push(finalDropKey);
          }
        } else {
          selectedTreeKeys.value = [];
        }

        // 根据实际移动的目标显示成功消息
        if (newParentGroupUuid) {
          // 移动到了父分组
          const targetGroup = projectStore.activeProject?.groups?.find(g => g.groupUuid === newParentGroupUuid);
          message.success(t('editor.dragAndDrop.groupMovedToGroup', { groupName: targetGroup?.name || t('editor.dragAndDrop.unknownGroup') }));
        } else {
          // 移动到了根目录
          message.success(t('editor.dragAndDrop.groupMovedToRoot'));
        }
      }
    } catch (error: any) {
      message.error(error.message || t('editor.dragAndDrop.moveFailed'));
    } finally {
      isDragging.value = false; // 重置拖拽标志
    }
  };

  /**
   * 处理树节点拖拽开始
   */
  const handleTreeNodeDragStart = ({ node }: { event: DragEvent; node: any }) => {
    if (!node || !node.key) return;

    if (String(node.key).startsWith("file-")) {
      draggedNodeType.value = "file";
      draggedNodeKey.value = String(node.key);
    } else if (String(node.key).startsWith("group-")) {
      draggedNodeType.value = "group";
      draggedNodeKey.value = String(node.key);
    } else {
      draggedNodeType.value = null;
      draggedNodeKey.value = null;
    }
  };

  /**
   * 处理树节点拖拽结束
   */
  const handleTreeNodeDragEnd = () => {
    draggedNodeType.value = null;
    draggedNodeKey.value = null;
  };

  /**
   * 根目录Drop事件处理
   */
  const onRootDropEnter = (e: DragEvent) => {
    e.preventDefault();
  };

  const onRootDropOver = (e: DragEvent) => {
    e.preventDefault();
  };

  const onRootDrop = async (e: DragEvent) => {
    e.preventDefault();

    // 如果树的拖拽处理器正在处理，则不在这里重复处理
    if (isDragging.value) {
      return;
    }

    // 检查是否拖拽到了树节点上，如果是则不处理（让树组件处理）
    const target = e.target as HTMLElement;
    if (target.closest('.n-tree-node')) {
      return;
    }

    if (!draggedNodeType.value || !draggedNodeKey.value) return;

    try {
      if (draggedNodeType.value === "file") {
        const fileUuid = draggedNodeKey.value.replace(/^file-/, "");
        await projectStore.moveFile(fileUuid, null);
        selectedTreeKeys.value = [];
        message.success(t('editor.dragAndDrop.fileMovedToRoot'));
      } else if (draggedNodeType.value === "group") {
        const groupUuid = draggedNodeKey.value.replace(/^group-/, "");
        await projectStore.moveGroup(groupUuid, null);
        selectedTreeKeys.value = [];
        message.success(t('editor.dragAndDrop.groupMovedToRoot'));
      }
    } catch (err: any) {
      message.error(err.message || t('editor.dragAndDrop.moveToRootFailed'));
    } finally {
      draggedNodeType.value = null;
      draggedNodeKey.value = null;
    }
  };

  return {
    // 状态
    draggedNodeType,
    draggedNodeKey,

    // 方法
    handleTreeDrop,
    handleTreeNodeDragStart,
    handleTreeNodeDragEnd,
    onRootDropEnter,
    onRootDropOver,
    onRootDrop,
  };
}
