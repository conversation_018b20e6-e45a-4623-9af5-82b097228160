#!/usr/bin/env node

/**
 * 查找在语言文件中存在但前端代码中未使用的翻译键
 */

const fs = require('fs');
const path = require('path');

// 加载使用的键
const usedKeysFile = path.join(__dirname, 'used-i18n-keys.json');
if (!fs.existsSync(usedKeysFile)) {
  console.error('❌ 请先运行 find-used-i18n-keys.cjs 生成使用键列表');
  process.exit(1);
}

const { allUsedKeys } = JSON.parse(fs.readFileSync(usedKeysFile, 'utf8'));
const usedKeysSet = new Set(allUsedKeys);

// 语言文件路径
const localeFiles = {
  'zh-CN': path.join(__dirname, '../src/locales/zh-CN.ts'),
  'en-US': path.join(__dirname, '../src/locales/en-US.ts'),
  'ja-JP': path.join(__dirname, '../src/locales/ja-JP.ts'),
  'ko-KR': path.join(__dirname, '../src/locales/ko-KR.ts'),
};

// 提取翻译键的函数
function extractKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

// 加载语言文件
function loadLocaleFile(filePath) {
  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 移除 TypeScript 语法，提取 export default 的对象
    const match = content.match(/export\s+default\s+(\{[\s\S]*\})\s*as\s+const;?/);
    if (!match) {
      throw new Error('无法解析语言文件格式');
    }
    
    // 使用 eval 解析对象（注意：这在生产环境中不安全，但在构建脚本中可以接受）
    const obj = eval(`(${match[1]})`);
    return extractKeys(obj);
  } catch (error) {
    console.error(`❌ 加载语言文件失败: ${filePath} - ${error.message}`);
    return [];
  }
}

console.log('🔍 查找在语言文件中存在但前端代码中未使用的翻译键...\n');

// 加载所有语言文件的键
const localeData = {};
for (const [locale, filePath] of Object.entries(localeFiles)) {
  console.log(`📖 加载 ${locale} 语言文件...`);
  const keys = loadLocaleFile(filePath);
  localeData[locale] = keys;
  console.log(`   ✅ 找到 ${keys.length} 个翻译键`);
}

console.log(`\n📊 前端代码使用了 ${allUsedKeys.length} 个翻译键\n`);

// 找出每个语言文件中未使用的键
const unusedByLocale = {};
let hasUnusedKeys = false;

for (const [locale, keys] of Object.entries(localeData)) {
  const unusedKeys = keys.filter(key => !usedKeysSet.has(key));
  
  if (unusedKeys.length > 0) {
    hasUnusedKeys = true;
    unusedByLocale[locale] = unusedKeys;
    console.log(`⚠️ ${locale} 有 ${unusedKeys.length} 个未使用的翻译键:`);
    
    // 按类别分组显示
    const keysByCategory = {};
    unusedKeys.forEach(key => {
      const category = key.split('.')[0];
      if (!keysByCategory[category]) {
        keysByCategory[category] = [];
      }
      keysByCategory[category].push(key);
    });
    
    for (const [category, categoryKeys] of Object.entries(keysByCategory)) {
      console.log(`   📂 ${category} (${categoryKeys.length} 个):`);
      categoryKeys.forEach(key => {
        console.log(`      - ${key}`);
      });
    }
    console.log();
  } else {
    console.log(`✅ ${locale} 没有未使用的翻译键`);
  }
}

if (!hasUnusedKeys) {
  console.log('🎉 所有语言文件的翻译键都在前端代码中使用！');
} else {
  console.log('⚠️ 发现未使用的翻译键，可以考虑清理以减少文件大小。');
  
  // 保存结果
  const resultFile = path.join(__dirname, 'unused-keys-analysis.json');
  fs.writeFileSync(resultFile, JSON.stringify({
    usedKeysCount: allUsedKeys.length,
    unusedByLocale,
    timestamp: new Date().toISOString()
  }, null, 2));
  
  console.log(`\n💾 分析结果已保存到: ${resultFile}`);
}
