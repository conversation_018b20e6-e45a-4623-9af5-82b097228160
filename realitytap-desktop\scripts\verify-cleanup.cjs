#!/usr/bin/env node

/**
 * 验证清理后的国际化文件是否保留了关键翻译键
 */

const fs = require('fs');
const path = require('path');

// 关键翻译键列表
const CRITICAL_KEYS = [
  'update.confirmInstallation',
  'update.updateAvailable',
  'update.installNow',
  'update.downloading',
  'update.installing',
  'update.currentVersion',
  'update.latestVersion',
  'update.releaseNotes',
  'update.fileSize',
  'about.title',
  'about.version',
  'about.updateCheck.checkNow',
  'common.confirm',
  'common.cancel',
  'common.loading',
  'common.error',
];

// 语言文件路径
const localeFiles = {
  'zh-CN': path.join(__dirname, '../src/locales/zh-CN.ts'),
  'en-US': path.join(__dirname, '../src/locales/en-US.ts'),
  'ja-JP': path.join(__dirname, '../src/locales/ja-JP.ts'),
  'ko-KR': path.join(__dirname, '../src/locales/ko-KR.ts'),
};

// 提取翻译键的函数
function extractKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

// 加载语言文件
function loadLocaleFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const match = content.match(/export default\s+({[\s\S]*})\s*(?:as const)?;?\s*$/);
    if (!match) {
      throw new Error('无法解析语言文件格式');
    }
    
    const objStr = match[1];
    const obj = new Function('return ' + objStr)();
    
    return obj;
  } catch (error) {
    console.error(`❌ 加载语言文件失败: ${filePath}`);
    console.error(`   错误: ${error.message}`);
    return null;
  }
}

// 验证清理结果
function verifyCleanup() {
  console.log('🔍 验证清理后的国际化文件...\n');
  
  let allGood = true;
  
  for (const [locale, filePath] of Object.entries(localeFiles)) {
    console.log(`📖 检查 ${locale} 语言文件...`);
    
    const data = loadLocaleFile(filePath);
    if (!data) {
      allGood = false;
      continue;
    }
    
    const keys = extractKeys(data);
    const keySet = new Set(keys);
    
    console.log(`   总键数: ${keys.length}`);
    
    // 检查关键键是否存在
    const missingCriticalKeys = CRITICAL_KEYS.filter(key => !keySet.has(key));
    
    if (missingCriticalKeys.length === 0) {
      console.log(`   ✅ 所有关键翻译键都存在`);
    } else {
      console.log(`   ❌ 缺失关键翻译键:`);
      missingCriticalKeys.forEach(key => {
        console.log(`     - ${key}`);
      });
      allGood = false;
    }
    
    console.log();
  }
  
  // 检查文件大小变化
  console.log('📊 文件大小对比:');
  for (const [locale, filePath] of Object.entries(localeFiles)) {
    const currentSize = fs.statSync(filePath).size;
    const backupFiles = fs.readdirSync(path.dirname(filePath))
      .filter(f => f.startsWith(`${locale}.ts.backup-`))
      .sort()
      .reverse();
    
    if (backupFiles.length > 0) {
      const backupPath = path.join(path.dirname(filePath), backupFiles[0]);
      const originalSize = fs.statSync(backupPath).size;
      const reduction = ((originalSize - currentSize) / originalSize * 100).toFixed(1);
      
      console.log(`   ${locale}: ${originalSize} → ${currentSize} bytes (-${reduction}%)`);
    }
  }
  
  if (allGood) {
    console.log('\n🎉 验证通过！清理成功，所有关键翻译键都已保留。');
    return true;
  } else {
    console.log('\n⚠️ 验证失败！发现缺失的关键翻译键。');
    return false;
  }
}

// 运行验证
if (require.main === module) {
  const success = verifyCleanup();
  process.exit(success ? 0 : 1);
}

module.exports = { verifyCleanup };
