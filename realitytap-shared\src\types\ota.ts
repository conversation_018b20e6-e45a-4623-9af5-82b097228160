import type { UpdateError } from './client';

export interface UpdateCheckRequest {
  currentVersion: string;
  platform: string;
  architecture: string;
  channel: 'stable' | 'beta' | 'alpha';
  locale?: string;
}

export interface UpdateCheckResponse {
  hasUpdate: boolean;
  latestVersion?: string;
  downloadUrl?: string;
  releaseNotes?: string;
  fileSize?: number;
  checksum?: string;
  isForced?: boolean;
  minimumVersion?: string;
}

export interface DownloadProgress {
  sessionId: string;
  progress: number;
  downloadedBytes: number;
  totalBytes: number;
  speed: number;
  estimatedTimeRemaining: number;
  status: 'downloading' | 'paused' | 'completed' | 'failed';
}

// 第四阶段新增类型定义

/**
 * 调度器配置
 */
export interface SchedulerConfig {
  enabled: boolean;
  checkInterval: number;
  retryInterval: number;
  maxRetries: number;
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM格式
    end: string;   // HH:MM格式
  };
  networkConditions: {
    requireWifi: boolean;
    requireUnmetered: boolean;
    minBandwidth: number;
  };
  batteryConditions: {
    minBatteryLevel: number;
    requireCharging: boolean;
  };
  userActivityConditions: {
    checkWhenIdle: boolean;
    idleThreshold: number;
  };
}

/**
 * 调度器状态
 */
export type SchedulerStatus = 'stopped' | 'running' | 'paused' | 'checking' | 'error';

/**
 * 调度事件
 */
export interface ScheduleEvent {
  type: string;
  message: string;
  timestamp: Date;
}

/**
 * 错误严重程度
 */
export type ErrorSeverity = 'info' | 'warning' | 'critical';

/**
 * 错误上下文
 */
export interface ErrorContext {
  operation: string;
  timestamp: Date;
  userAgent?: string;
  platform?: string;
  version?: string;
  [key: string]: any;
}

/**
 * 重试策略
 */
export interface RetryStrategy {
  shouldRetry: boolean;
  maxRetries: number;
  delay: number;
  backoffMultiplier: number;
}

/**
 * 错误恢复操作
 */
export interface ErrorRecoveryAction {
  type: 'retry' | 'manual' | 'config';
  label: string;
  action: string;
}

/**
 * 回滚策略
 */
export type RollbackStrategy = 'backup_restore' | 'restore_point' | 'config_only' | 'reinstall';

/**
 * 备份信息
 */
export interface BackupInfo {
  id: string;
  version: string;
  description: string;
  timestamp: Date;
  size: number;
  path: string;
  checksum: string;
  type: 'full' | 'incremental';
}

/**
 * 还原点
 */
export interface RestorePoint {
  id: string;
  version: string;
  type: 'pre_update' | 'post_update' | 'manual';
  description: string;
  timestamp: Date;
  configSnapshot: Record<string, any>;
  fileHashes: Record<string, string>;
  registrySnapshot: Record<string, any>;
}

/**
 * 回滚信息
 */
export interface RollbackInfo {
  strategy: RollbackStrategy;
  targetVersion: string;
  timestamp: Date;
  reason: string;
  backupUsed: BackupInfo | null;
  restorePointUsed: RestorePoint | null;
}

/**
 * 回滚结果
 */
export interface RollbackResult {
  success: boolean;
  rollbackInfo: RollbackInfo;
  error?: UpdateError;
  duration: number;
}

/**
 * 后台检查配置
 */
export interface BackgroundCheckConfig {
  enabled: boolean;
  checkInterval: number;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
  systemConditions: {
    requireIdle: boolean;
    idleThreshold: number;
    requireWifi: boolean;
    minBatteryLevel: number;
    requireCharging: boolean;
  };
  notificationSettings: {
    showUpdateAvailable: boolean;
    showDownloadComplete: boolean;
    showErrors: boolean;
    soundEnabled: boolean;
  };
  autoActions: {
    autoDownload: boolean;
    autoInstall: boolean;
    autoRestart: boolean;
  };
}

/**
 * 后台检查状态
 */
export type BackgroundCheckStatus = 'stopped' | 'running' | 'paused' | 'checking' | 'error';

/**
 * 系统条件
 */
export interface SystemConditions {
  isIdle: boolean;
  isOnWifi: boolean;
  batteryLevel: number;
  isCharging: boolean;
  hasInternetConnection: boolean;
}