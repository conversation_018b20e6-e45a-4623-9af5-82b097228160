import { authService } from '@/services/auth.service';
import { AuthenticatedRequest } from '@/types/auth.types';
import { ErrorResponse } from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import { NextFunction, Request, Response } from 'express';

/**
 * 检查是否为私有IP地址
 */
function isPrivateIP(ip: string): boolean {
  if (!ip) return true; // 无IP视为私有

  // IPv4 私有地址范围
  const privateRanges = [
    /^127\./,                    // *********/8 (localhost)
    /^10\./,                     // 10.0.0.0/8
    /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
    /^192\.168\./,               // ***********/16
    /^169\.254\./,               // ***********/16 (link-local)
    /^::1$/,                     // IPv6 localhost
    /^fe80:/,                    // IPv6 link-local
  ];

  return privateRanges.some(range => range.test(ip));
}

/**
 * JWT身份验证中间件
 * 验证请求头中的Authorization token
 */
export const authMiddleware = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    // 获取Authorization头
    const authHeader = req.headers.authorization as string;

    if (!authHeader) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: 'Authorization token is required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.status(401).json(response);
      return;
    }

    // 检查token格式 (Bearer <token>)
    const tokenParts = authHeader.split(' ');
    if (tokenParts.length !== 2 || tokenParts[0] !== 'Bearer') {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_TOKEN_FORMAT',
          message: 'Authorization token must be in format: Bearer <token>',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.status(401).json(response);
      return;
    }

    const token = tokenParts[1];

    if (!token) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_TOKEN_FORMAT',
          message: 'Token is missing',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.status(401).json(response);
      return;
    }

    // 验证token
    const user = authService.verifyToken(token);

    // 将用户信息添加到请求对象
    req.user = user;

    // 获取客户端IP信息
    const clientIP = req.ip || '';
    const isPrivate = isPrivateIP(clientIP);

    // 只在安全相关情况下记录认证成功日志
    // 记录条件：
    // 1. 远程客户端的认证（非私有IP）
    // 2. 敏感操作路径（登录、上传、删除、配置）
    // 3. 非GET请求的管理操作
    const shouldLogAuth =
      !isPrivate ||                             // 远程客户端
      req.path.includes('/login') ||            // 登录操作
      req.path.includes('/logout') ||           // 登出操作
      req.path.includes('/upload') ||           // 上传操作
      req.path.includes('/delete') ||           // 删除操作
      req.path.includes('/config') ||           // 配置操作
      req.path.includes('/logs') && req.method === 'DELETE'; // 清空日志操作

    if (shouldLogAuth) {
      const operationType = !isPrivate ? '远程客户端认证' : '敏感操作认证';

      logger.info(`认证成功: ${operationType}`, {
        username: user.username,
        clientIP: clientIP,
        isPrivateIP: isPrivate,
        userAgent: req.get('User-Agent'),
        path: req.path,
        method: req.method,
        module: 'user_operation',
        operation: 'authentication_success',
      });
    }

    next();
  } catch (error: any) {
    logger.warn('认证失败', {
      error: error.message,
      clientIP: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      module: 'user_operation',
      operation: 'authentication_failed',
    });

    const response: ErrorResponse = {
      success: false,
      error: {
        code: 'AUTHENTICATION_FAILED',
        message: 'Invalid or expired token',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.status(401).json(response);
  }
};

/**
 * 可选的身份验证中间件
 * 如果提供了token则验证，否则继续处理
 */
export const optionalAuthMiddleware = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.headers.authorization as string;

    if (!authHeader) {
      // 没有token，继续处理
      next();
      return;
    }

    const tokenParts = authHeader.split(' ');
    if (tokenParts.length !== 2 || tokenParts[0] !== 'Bearer') {
      // token格式错误，继续处理但不设置用户信息
      next();
      return;
    }

    const token = tokenParts[1];
    if (!token) {
      next();
      return;
    }

    const user = authService.verifyToken(token);
    req.user = user;

    // 可选认证成功时不记录日志，避免日志污染
    // 如果需要调试，可以临时启用下面的日志
    // logger.debug('Optional authentication successful', {
    //   username: user.username,
    //   ip: req.ip,
    //   path: req.path,
    // });

    next();
  } catch (error: any) {
    // 验证失败，但继续处理（不设置用户信息）
    // 可选认证失败时不记录日志，避免日志污染
    // 如果需要调试，可以临时启用下面的日志
    // logger.debug('Optional authentication failed', {
    //   error: error.message,
    //   ip: req.ip,
    //   path: req.path,
    // });

    next();
  }
};

/**
 * 角色验证中间件
 * 验证用户是否具有指定角色
 */
export const requireRole = (role: 'admin') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication is required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.status(401).json(response);
      return;
    }

    if (req.user.role !== role) {
      logger.warn('Access denied - insufficient permissions', {
        username: req.user.username,
        requiredRole: role,
        userRole: req.user.role,
        ip: req.ip,
        path: req.path,
      });

      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Insufficient permissions for this operation',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };

      res.status(403).json(response);
      return;
    }

    next();
  };
};

/**
 * 管理员身份验证中间件（组合中间件）
 * 同时验证身份和管理员角色
 */
export const adminAuthMiddleware = [authMiddleware, requireRole('admin')];

/**
 * API密钥验证中间件（备用认证方式）
 * 用于脚本或自动化工具访问
 */
export const apiKeyMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const apiKey = req.headers['x-api-key'] as string;
  const configApiKey = process.env.API_KEY;

  if (!configApiKey) {
    // 如果没有配置API密钥，跳过此验证
    next();
    return;
  }

  if (!apiKey || apiKey !== configApiKey) {
    logger.warn('API key authentication failed', {
      providedKey: apiKey ? '***' : 'none',
      ip: req.ip,
      path: req.path,
    });

    const response: ErrorResponse = {
      success: false,
      error: {
        code: 'INVALID_API_KEY',
        message: 'Invalid or missing API key',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.status(401).json(response);
    return;
  }

  logger.debug('API key authentication successful', {
    ip: req.ip,
    path: req.path,
  });

  next();
};

/**
 * 会话超时检查中间件
 * 检查用户会话是否超时
 */
export const sessionTimeoutMiddleware = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    next();
    return;
  }

  const loginTime = new Date(req.user.loginTime).getTime();
  const now = Date.now();
  const sessionDuration = now - loginTime;

  // 检查会话是否超时
  const { config } = require('@/config/server.config');
  if (sessionDuration > config.admin.sessionTimeout) {
    logger.warn('Session timeout', {
      username: req.user.username,
      sessionDuration,
      maxDuration: config.admin.sessionTimeout,
    });

    const response: ErrorResponse = {
      success: false,
      error: {
        code: 'SESSION_TIMEOUT',
        message: 'Session has expired, please login again',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.status(401).json(response);
    return;
  }

  next();
};
