import type { RenderableEvent, SelectedEvent } from "../types";
import { calculateTotalDurationFromEvents } from "@/utils/haptic-event-processor";

/**
 * 数据转换工具函数
 */

/**
 * 将RenderableEvent转换为SelectedEvent
 */
export function convertToSelectedEvent(event: RenderableEvent | null): SelectedEvent | null {
  if (!event) return null;

  if (event.type === "transient") {
    return {
      Type: "transient",
      RelativeTime: event.startTime,
      Parameters: {
        Intensity: event.intensity,
        Frequency: event.frequency,
      },
    };
  } else if (event.type === "continuous") {
    return {
      Type: "continuous",
      RelativeTime: event.startTime,
      Duration: event.duration, // 使用event.duration而不是计算值
      Parameters: {
        Intensity: event.eventIntensity,
        Frequency: event.eventFrequency,
      },
    };
  }

  return null;
}

/**
 * 计算事件列表的总时长
 */
export function calculateEventsTotalDuration(events: RenderableEvent[]): number {
  return calculateTotalDurationFromEvents(events);
}

/**
 * 按开始时间对事件进行排序
 */
export function sortEventsByStartTime(events: RenderableEvent[]): RenderableEvent[] {
  return [...events].sort((a, b) => a.startTime - b.startTime);
}

/**
 * 过滤指定时间范围内的事件
 */
export function filterEventsInTimeRange(events: RenderableEvent[], startTime: number, endTime: number): RenderableEvent[] {
  return events.filter((event) => event.startTime >= startTime && event.startTime <= endTime);
}

/**
 * 过滤指定时间范围外的事件
 */
export function filterEventsOutsideTimeRange(events: RenderableEvent[], startTime: number, endTime: number): RenderableEvent[] {
  return events.filter((event) => event.startTime < startTime || event.startTime > endTime);
}

/**
 * 查找指定ID的事件
 */
export function findEventById(events: RenderableEvent[], eventId: string): RenderableEvent | null {
  return events.find((event) => event.id === eventId) || null;
}

/**
 * 检查事件ID是否存在
 */
export function eventExists(events: RenderableEvent[], eventId: string): boolean {
  return events.some((event) => event.id === eventId);
}

/**
 * 获取事件的索引
 */
export function getEventIndex(events: RenderableEvent[], eventId: string): number {
  return events.findIndex((event) => event.id === eventId);
}

/**
 * 深拷贝事件数组
 */
export function cloneEvents(events: RenderableEvent[]): RenderableEvent[] {
  return JSON.parse(JSON.stringify(events));
}

/**
 * 合并两个事件数组并按时间排序
 */
export function mergeAndSortEvents(events1: RenderableEvent[], events2: RenderableEvent[]): RenderableEvent[] {
  return sortEventsByStartTime([...events1, ...events2]);
}

/**
 * 检查两个事件数组是否相等
 */
export function areEventsEqual(events1: RenderableEvent[], events2: RenderableEvent[]): boolean {
  if (events1.length !== events2.length) {
    return false;
  }

  // 简单的深度比较
  return JSON.stringify(events1) === JSON.stringify(events2);
}
