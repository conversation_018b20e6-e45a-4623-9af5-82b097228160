/**
 * 坐标与时间/强度映射相关工具函数
 *
 * 注意：此文件已重构，大部分功能已迁移到专门的文件中：
 * - 坐标转换: coordinate-transforms.ts
 * - 画布计算: canvas-calculations.ts
 * - 绘制辅助: drawing-helpers.ts
 * - 性能工具: performance-utils.ts
 *
 * 此文件保留为兼容性导出，建议直接使用新的专门文件
 */

// 重新导出常用的常量和函数以保持向后兼容
export {
  DEFAULT_LINE_WIDTH,
  SELECTED_LINE_WIDTH,
  DEFAULT_POINT_RADIUS,
  SELECTED_POINT_RADIUS,
  MIN_FREQUENCY,
  MAX_FREQUENCY,
  WAVEFORM_SAFE_OFFSET,
  ENABLE_GUIDE_LINES,
  drawXAxisTicksAndLabels,
  drawGridLinesAndXAxis
} from "./drawing-helpers";

export {
  MAX_CANVAS_WIDTH,
  calculateTargetGraphWidth,
  calculateActualCanvasWidth,
  graphAreaWidth,
  graphAreaHeight
} from "./canvas-calculations";

// 从coordinate-transforms导入函数
export {
  calculateVirtualScrollOffset,
  mapTimeToX,
  mapTimeToLogicalX,
  mapIntensityToY,
  mapYToIntensity,
  mapXToTimeOffset,
  mapXOffsetToTimeOffset,
  convertXToTime,
  isPointInRadius,
  calculateRawIntensity
} from "./coordinate-transforms";

export {
  throttle
} from "./performance-utils";
