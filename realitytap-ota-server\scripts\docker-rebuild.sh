#!/bin/bash

# RealityTap OTA Server Docker Rebuild Script
# This script cleans Docker cache and rebuilds the application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

print_info "RealityTap OTA Server Docker Rebuild"
print_info "===================================="

# Change to project directory
cd "$PROJECT_DIR"

print_info "Stopping existing containers..."
docker-compose -f docker/docker-compose.http.yml down 2>/dev/null || true
docker-compose -f docker/docker-compose.https.yml down 2>/dev/null || true

print_info "Cleaning Docker build cache..."
docker builder prune -f

print_info "Removing old images..."
docker rmi realitytap-ota-server:latest 2>/dev/null || true
docker rmi realitytap-ota-server_realitytap-ota-server:latest 2>/dev/null || true

print_info "Rebuilding Docker image..."
docker-compose -f docker/docker-compose.http.yml build --no-cache

if [ $? -eq 0 ]; then
    print_success "Docker image rebuilt successfully!"
    
    print_info "Starting services..."
    docker-compose -f docker/docker-compose.http.yml up -d
    
    if [ $? -eq 0 ]; then
        print_success "Services started successfully!"
        print_info "You can now access the application at:"
        print_info "- Main service: http://localhost:3000"
        print_info "- Admin interface: http://localhost:3000/admin"
        
        print_info "Checking service health..."
        sleep 10
        
        if curl -f http://localhost:3000/health >/dev/null 2>&1; then
            print_success "Service is healthy and running!"
        else
            print_warning "Service may still be starting up. Check logs with:"
            print_warning "docker-compose -f docker/docker-compose.http.yml logs -f"
        fi
    else
        print_error "Failed to start services"
        exit 1
    fi
else
    print_error "Failed to rebuild Docker image"
    exit 1
fi
