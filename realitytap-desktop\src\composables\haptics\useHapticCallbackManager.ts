/**
 * 触觉回调管理器（单例）
 * 与后端单例 outputHandler 建立稳定绑定
 */

import { invoke } from "@tauri-apps/api/core";
import { listen, type UnlistenFn } from "@tauri-apps/api/event";
import { logger, LogModule } from "@/utils/logger/logger";

export interface HapticEventData {
  device_id?: number;
  timestamp?: number;
  [key: string]: any;
}

export type HapticEventCallback = (data: HapticEventData) => void;

/**
 * 触觉回调管理器单例类
 */
export class HapticCallbackManager {
  private static instance: HapticCallbackManager;
  private isInitialized = false;
  private eventHandlers = new Map<string, Set<HapticEventCallback>>();
  private unlistenFunctions: UnlistenFn[] = [];

  private constructor() {
    // 私有构造函数，确保单例
  }

  /**
   * 获取单例实例
   */
  static getInstance(): HapticCallbackManager {
    if (!this.instance) {
      this.instance = new HapticCallbackManager();
    }
    return this.instance;
  }

  /**
   * 初始化回调系统（应用启动时调用一次）
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn(LogModule.GENERAL, "触觉回调管理器已初始化，跳过重复初始化");
      return;
    }

    try {
      // 建立稳定的事件监听
      await this.setupEventListeners();

      this.isInitialized = true;
      logger.info(LogModule.GENERAL, "触觉回调管理器初始化完成");
    } catch (error) {
      logger.error(LogModule.GENERAL, "触觉回调管理器初始化失败", error);
      throw error;
    }
  }

  /**
   * 设置事件监听器
   */
  private async setupEventListeners(): Promise<void> {
    try {
      // 生命周期事件 - 监听通用的 haptic:lifecycle 事件
      const unlistenLifecycle = await listen("haptic:lifecycle", (event) => {
        const payload = event.payload as any;
        const eventType = payload.type; // start, complete, stop

        // 根据事件类型分发到具体的处理器
        switch (eventType) {
          case "start":
            this.dispatchEvent("lifecycle:start", payload as HapticEventData);
            break;
          case "complete":
            this.dispatchEvent("lifecycle:complete", payload as HapticEventData);
            break;
          case "stop":
            this.dispatchEvent("lifecycle:stop", payload as HapticEventData);
            break;
          default:
            logger.warn(LogModule.GENERAL, `未知的生命周期事件类型: ${eventType}`);
        }
      });
      this.unlistenFunctions.push(unlistenLifecycle);

      // 数据事件 - 监听通用的 haptic:data 事件
      const unlistenData = await listen("haptic:data", (event) => {
        const payload = event.payload as any;
        const eventType = payload.type; // waveform_sample, chunk_start, etc.

        // 根据事件类型分发到具体的处理器
        switch (eventType) {
          case "waveform_sample":
            this.dispatchEvent("data:waveform", payload as HapticEventData);
            break;
          case "chunk_start":
            this.dispatchEvent("data:chunk_start", payload as HapticEventData);
            break;
          default:
            // 对于其他数据事件，使用通用处理
            this.dispatchEvent(`data:${eventType}`, payload as HapticEventData);
        }
      });
      this.unlistenFunctions.push(unlistenData);

      // 错误事件 - 监听通用的 haptic:error 事件
      const unlistenError = await listen("haptic:error", (event) => {
        const payload = event.payload as any;
        const eventType = payload.type; // system, processing, etc.

        // 根据事件类型分发到具体的处理器
        this.dispatchEvent(`error:${eventType}`, payload as HapticEventData);
      });
      this.unlistenFunctions.push(unlistenError);

      // 状态事件 - 监听通用的 haptic:status 事件
      const unlistenStatus = await listen("haptic:status", (event) => {
        const payload = event.payload as any;
        const eventType = payload.type; // amplitude_change, etc.

        // 根据事件类型分发到具体的处理器
        this.dispatchEvent(`status:${eventType}`, payload as HapticEventData);
      });
      this.unlistenFunctions.push(unlistenStatus);

      logger.info(LogModule.GENERAL, "触觉事件监听器设置完成");
    } catch (error) {
      logger.error(LogModule.GENERAL, "设置事件监听器失败", error);
      throw error;
    }
  }

  /**
   * 注册事件回调
   * @param eventType 事件类型
   * @param callback 回调函数
   * @returns 取消监听的函数
   */
  on(eventType: string, callback: HapticEventCallback): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }

    this.eventHandlers.get(eventType)!.add(callback);

    logger.debug(LogModule.GENERAL, `注册触觉事件回调: ${eventType}`);

    // 返回取消监听的函数
    return () => {
      this.off(eventType, callback);
    };
  }

  /**
   * 取消事件回调
   * @param eventType 事件类型
   * @param callback 回调函数
   */
  off(eventType: string, callback: HapticEventCallback): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.delete(callback);
      if (handlers.size === 0) {
        this.eventHandlers.delete(eventType);
      }
      logger.debug(LogModule.GENERAL, `取消触觉事件回调: ${eventType}`);
    }
  }

  /**
   * 分发事件到所有注册的回调
   * @param eventType 事件类型
   * @param data 事件数据
   */
  private dispatchEvent(eventType: string, data: HapticEventData): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers && handlers.size > 0) {
      logger.debug(LogModule.GENERAL, `分发触觉事件: ${eventType}`, data);

      handlers.forEach((handler) => {
        try {
          handler(data);
        } catch (error) {
          logger.error(LogModule.GENERAL, `事件处理器执行失败 [${eventType}]:`, error);
        }
      });
    }
  }

  /**
   * 获取已注册的事件类型
   */
  getRegisteredEventTypes(): string[] {
    return Array.from(this.eventHandlers.keys());
  }

  /**
   * 获取指定事件类型的回调数量
   * @param eventType 事件类型
   */
  getCallbackCount(eventType: string): number {
    return this.eventHandlers.get(eventType)?.size || 0;
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 清理资源（应用关闭时调用）
   */
  async cleanup(): Promise<void> {
    logger.info(LogModule.GENERAL, "开始清理触觉回调管理器");

    // 取消所有事件监听
    for (const unlisten of this.unlistenFunctions) {
      try {
        unlisten();
      } catch (error) {
        logger.warn(LogModule.GENERAL, "取消事件监听失败", error);
      }
    }
    this.unlistenFunctions = [];

    // 清除所有回调
    this.eventHandlers.clear();

    this.isInitialized = false;
    logger.info(LogModule.GENERAL, "触觉回调管理器清理完成");
  }
}

/**
 * 初始化触觉回调系统（应用启动时调用）
 */
export async function initializeHapticCallbackSystem(): Promise<void> {
  const manager = HapticCallbackManager.getInstance();
  await manager.initialize();
}

/**
 * 获取触觉回调管理器实例
 */
export function useHapticCallbackManager() {
  return HapticCallbackManager.getInstance();
}

/**
 * 清理触觉回调系统（应用关闭时调用）
 */
export async function cleanupHapticCallbackSystem(): Promise<void> {
  const manager = HapticCallbackManager.getInstance();
  await manager.cleanup();
}
