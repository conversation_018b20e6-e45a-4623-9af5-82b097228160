import type { BatchUploadInitRequest, BatchUploadInitResponse, BatchUploadProgress } from '@/types/upload';
import request from '@/utils/request';
import type { BaseResponse, HealthStatus, LogsResponse, SystemStats, VersionInfo } from './types';

export const adminApi = {
  // 获取系统统计信息
  getStats: (): Promise<BaseResponse<SystemStats>> => {
    return request.get('/admin/stats').then(res => res.data);
  },

  // 获取版本列表
  getVersions: (): Promise<BaseResponse<VersionInfo[]>> => {
    return request.get('/admin/versions').then(res => res.data);
  },

  // 简单上传功能已弃用，请使用分块上传

  // 批量上传相关接口
  // 初始化批量上传
  initBatchUpload: (requestData: BatchUploadInitRequest): Promise<BaseResponse<BatchUploadInitResponse>> => {
    return request.post('/admin/batch-upload/init', requestData).then((res: any) => res.data);
  },

  // 批量上传文件
  uploadBatchFiles: (
    sessionId: string,
    files: File[],
    onProgress?: (progress: { uploadedBytes: number; totalBytes: number; percentage: number }) => void,
  ): Promise<BaseResponse<any>> => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    // 模拟慢速网络进度（仅用于开发测试）
    const simulateSlowProgress = (totalBytes: number, onProgress: any) => {
      let uploadedBytes = 0;
      const chunkSize = Math.max(1024 * 100, totalBytes / 20); // 每次上传5%或100KB
      const interval = 200; // 每200ms更新一次

      const timer = setInterval(() => {
        uploadedBytes = Math.min(uploadedBytes + chunkSize, totalBytes);
        const percentage = Math.round((uploadedBytes / totalBytes) * 100);

        console.log('🐌 Simulated progress:', { uploadedBytes, totalBytes, percentage });

        onProgress({
          uploadedBytes,
          totalBytes,
          percentage
        });

        if (uploadedBytes >= totalBytes) {
          clearInterval(timer);
        }
      }, interval);

      return timer;
    };

    // 检查是否启用模拟慢速网络（通过localStorage控制）
    const enableSlowSimulation = localStorage.getItem('debug_slow_upload') === 'true';

    if (enableSlowSimulation && onProgress) {
      console.log('🐌 启用模拟慢速上传进度');
      const totalBytes = files.reduce((sum, file) => sum + file.size, 0);
      const timer = simulateSlowProgress(totalBytes, onProgress);

      // 实际上传完成后清除模拟进度
      return request
        .post(`/admin/batch-upload/${sessionId}/files`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then(res => {
          clearInterval(timer);
          // 确保最终进度是100%
          onProgress({
            uploadedBytes: totalBytes,
            totalBytes,
            percentage: 100
          });
          return res.data;
        });
    }

    return request
      .post(`/admin/batch-upload/${sessionId}/files`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: progressEvent => {
          console.log('📊 Upload progress event:', {
            loaded: progressEvent.loaded,
            total: progressEvent.total,
            hasOnProgress: !!onProgress
          });

          if (onProgress && progressEvent.total) {
            const progressData = {
              uploadedBytes: progressEvent.loaded,
              totalBytes: progressEvent.total,
              percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total)
            };

            console.log('📈 Calling onProgress with:', progressData);
            onProgress(progressData);
          }
        },
      })
      .then(res => res.data);
  },

  // 完成批量上传
  completeBatchUpload: (sessionId: string): Promise<BaseResponse<any>> => {
    return request.post(`/admin/batch-upload/${sessionId}/complete`).then(res => res.data);
  },

  // 获取批量上传进度
  getBatchUploadProgress: (sessionId: string): Promise<BaseResponse<BatchUploadProgress>> => {
    return request.get(`/admin/batch-upload/${sessionId}/progress`).then(res => res.data);
  },

  // 取消批量上传
  cancelBatchUpload: (sessionId: string): Promise<BaseResponse> => {
    return request.delete(`/admin/batch-upload/${sessionId}`).then(res => res.data);
  },

  // 删除版本
  deleteVersion: (versionId: string): Promise<BaseResponse> => {
    return request.delete(`/admin/version/${versionId}`).then(res => res.data);
  },

  // 删除上传的文件
  deleteUploadedFile: (filename: string): Promise<BaseResponse> => {
    return request.delete(`/admin/version/${filename}`).then(res => res.data);
  },

  // 更新版本元数据
  updateVersionMetadata: (
    filename: string,
    metadata: {
      version: string;
      platform: 'windows' | 'macos' | 'linux';
      architecture: 'x86_64' | 'aarch64' | 'x86';
      channel: 'stable' | 'beta' | 'alpha';
      isForced?: boolean;
      releaseNotes?: string;
    },
  ): Promise<BaseResponse> => {
    return request.put(`/admin/version/${encodeURIComponent(filename)}`, metadata).then(res => res.data);
  },

  // 切换版本强制更新状态
  toggleForceUpdate: (filename: string, isForced: boolean): Promise<BaseResponse> => {
    return request.patch(`/admin/version/${encodeURIComponent(filename)}/force-update`, { isForced }).then(res => res.data);
  },

  // 获取健康状态
  getHealth: (): Promise<BaseResponse<HealthStatus>> => {
    return request.get('/health/detailed').then(res => res.data);
  },

  // 获取系统日志
  getLogs: (params?: {
    module?: string;
    limit?: number;
    offset?: number;
    search?: string;
  }): Promise<BaseResponse<LogsResponse>> => {
    return request.get('/admin/logs', { params }).then(res => res.data);
  },

  // 清空系统日志
  clearLogs: (): Promise<BaseResponse> => {
    return request.delete('/admin/logs').then(res => res.data);
  },

  // 清理临时文件
  cleanup: (): Promise<BaseResponse> => {
    return request.post('/admin/cleanup').then(res => res.data);
  },

  // 清理无效版本
  cleanupInvalidVersions: (): Promise<
    BaseResponse<{
      totalFound: number;
      totalCleaned: number;
      cleanedVersions: Array<{
        filename: string;
        reason: string;
      }>;
      message: string;
    }>
  > => {
    return request.post('/admin/cleanup-invalid-versions').then(res => res.data);
  },
};
