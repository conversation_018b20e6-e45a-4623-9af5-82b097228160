import cors from 'cors';
import { config } from '@/config/server.config';

const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    const allowedOrigins = Array.isArray(config.security.corsOrigin) 
      ? config.security.corsOrigin 
      : [config.security.corsOrigin];

    // Allow all origins in development
    if (config.server.nodeEnv === 'development' && allowedOrigins.includes('*')) {
      return callback(null, true);
    }

    // Check if origin is allowed
    if (allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
      return callback(null, true);
    }

    return callback(new Error('Not allowed by CORS'));
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Range',
  ],
  exposedHeaders: [
    'Content-Range',
    'Accept-Ranges',
    'Content-Length',
    'Content-Type',
  ],
  credentials: false,
  maxAge: 86400, // 24 hours
};

export const corsMiddleware = cors(corsOptions);
