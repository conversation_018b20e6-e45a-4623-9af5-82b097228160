import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite' // 导入 auto-import 插件
import Components from 'unplugin-vue-components/vite' // 导入 components 插件
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers' // 导入 Naive UI 的 resolver
import path from 'path'
import { fileURLToPath } from 'url'; // <-- 导入 fileURLToPath

// 获取当前文件目录 (ES 模块方式)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename); // <-- 计算 __dirname

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 配置 AutoImport
    AutoImport({
      imports: [
        'vue', // 自动导入 Vue 相关函数，如 ref, reactive, computed 等
        {
          'naive-ui': [ // 自动导入 Naive UI 组件和函数
            'useDialog',
            'useMessage',
            'useNotification',
            'useLoadingBar'
          ]
        },
        {
          'pinia': ['defineStore', 'createPinia', 'useStore'], // 自动导入 Pinia 相关函数
          // 自动导入你的 Pinia Store (假设你的 store 文件名符合约定，例如 useCounterStore)
          // '@stores/counter': ['useCounterStore'], // 如果使用了路径别名，例如 '@stores'
          // 如果没有路径别名，根据实际路径调整
          './src/stores/counter': ['useCounterStore'] // 示例: 导入 counter store
        }
      ],
      // 可以在这里配置 auto-import 生成的声明文件路径
      // 例如: dts: './auto-imports.d.ts',
    }),
    // 配置 Components
    Components({
      resolvers: [
        // 自动导入 Naive UI 组件
        NaiveUiResolver()
      ],
      // 可以在这里配置 components 扫描的目录
      // 例如: dirs: ['src/components', 'src/views/components'],
      // 可以在这里配置 components 生成的声明文件路径
      // 例如: dts: './components.d.ts',
    })
  ],
  // 添加 resolve.alias 配置 -->
  resolve: {
    alias: [
      {
        find: '@',
        // 使用计算出的 __dirname
        replacement: path.resolve(__dirname, 'src')
      }
    ]
  },
  // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
  //
  // 1. prevent vite from obscuring rust errors
  clearScreen: false,
  // 2. tauri expects a fixed port, fail if that port is not available
  server: {
    port: 5173,
    strictPort: true,
    watch: {
      // 3. tell vite to ignore watching `src-tauri`
      ignored: ["**/src-tauri/**"],
    },
  },
})