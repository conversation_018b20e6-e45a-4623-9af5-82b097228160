# Tauri Plugin Updater 迁移指南

本文档详细说明如何将 realitytap-ota-server 迁移到与 tauri-plugin-updater 兼容的更新机制。

## 概述

realitytap-desktop 项目已从自定义更新机制迁移到使用 tauri-plugin-updater 插件。为了支持新的更新机制，服务器端需要进行相应的修改。

## 主要变更

### 1. 新增 API 端点

新增了与 tauri-plugin-updater 兼容的 API 端点：

```
GET /api/v1/updates/{target}/{current_version}?channel=stable
```

**支持的 target 格式：**
- `windows-x86_64` - Windows 64位
- `windows-i686` - Windows 32位
- `darwin-x86_64` - macOS Intel
- `darwin-aarch64` - macOS Apple Silicon
- `linux-x86_64` - Linux 64位
- `linux-aarch64` - Linux ARM64

### 2. 响应格式变更

**有更新时 (200 OK)：**
```json
{
  "url": "https://releases.realitytap.com/api/v1/download/realitytap-1.1.0-windows-x86_64.msi",
  "version": "1.1.0",
  "notes": "修复了若干问题，提升了性能",
  "pub_date": "2024-01-15T10:30:00Z",
  "signature": "dW50cnVzdGVkIGNvbW1lbnQ6..."
}
```

**无更新时 (204 No Content)：**
```
(空响应体)
```

### 3. 签名支持

新增了 Ed25519 数字签名支持，确保更新包的安全性和完整性。

## 部署步骤

### 1. 生成签名密钥对

```bash
# 运行密钥生成脚本
./scripts/generate-keypair.sh

# 或手动生成
tauri signer generate -w ./keys/realitytap-updater.key
```

### 2. 配置环境变量

在 `.env` 文件中添加签名配置：

```bash
# Tauri 签名配置
TAURI_PRIVATE_KEY_PATH=./keys/realitytap-updater.key
TAURI_KEY_PASSWORD=your-private-key-password
```

### 3. 更新版本元数据

更新 `storage/metadata/versions.json` 文件，为每个平台发布添加 `signature` 字段：

```json
{
  "channels": {
    "stable": {
      "version": "1.2.0",
      "platforms": {
        "windows": {
          "x86_64": {
            "filename": "realitytap-1.2.0-windows-x86_64.msi",
            "size": 52428800,
            "checksum": "sha256:abc123...",
            "releaseDate": "2024-01-15T10:30:00Z",
            "releaseNotes": "修复了若干问题，提升了性能",
            "signature": "dW50cnVzdGVkIGNvbW1lbnQ6..."
          }
        }
      }
    }
  }
}
```

### 4. 更新客户端配置

在 realitytap-desktop 项目的 `tauri.conf.json` 中配置更新端点：

```json
{
  "plugins": {
    "updater": {
      "active": true,
      "endpoints": [
        "https://releases.realitytap.com/api/v1/updates/{{target}}/{{current_version}}",
        "https://backup-releases.realitytap.com/api/v1/updates/{{target}}/{{current_version}}"
      ],
      "dialog": false,
      "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6...",
      "windows": {
        "installMode": "passive"
      }
    }
  }
}
```

## 兼容性说明

### API 变更

- **移除**：原有的 `POST /api/v1/version/check` API 已被移除
- **新增**：tauri-plugin-updater 兼容的 `GET /api/v1/updates/{target}/{current_version}` API
- **保留**：管理相关的 API 端点（`/api/v1/version/available`、`/api/v1/version/channels` 等）
- **保持**：现有的文件下载 API 保持不变

### 新功能

- 完全兼容 tauri-plugin-updater 的 API 规范
- 支持 Ed25519 数字签名验证
- 改进的错误处理和日志记录
- 标准化的响应格式

## 安全考虑

### 私钥管理

1. **安全存储**：私钥文件应存储在安全的位置，设置适当的文件权限
2. **备份**：建议将私钥备份到安全的离线存储
3. **访问控制**：限制对私钥文件的访问权限
4. **密码保护**：建议为私钥设置密码保护

### 签名验证

1. **完整性**：每个更新包都应该有对应的签名
2. **验证**：客户端会自动验证签名的有效性
3. **撤销**：如果私钥泄露，需要生成新的密钥对并更新所有客户端

## 测试验证

### 1. 端点测试

```bash
# 测试 Tauri 更新检查（有更新）
curl -X GET "http://localhost:3000/api/v1/updates/windows-x86_64/1.0.0?channel=stable"

# 测试 Tauri 更新检查（无更新）
curl -X GET "http://localhost:3000/api/v1/updates/windows-x86_64/1.2.0?channel=stable"

# 测试平台列表
curl -X GET "http://localhost:3000/api/v1/updates/platforms"

# 测试管理 API
curl -X GET "http://localhost:3000/api/v1/version/available"
curl -X GET "http://localhost:3000/api/v1/version/channels"
curl -X GET "http://localhost:3000/api/v1/version/latest/stable"
```

### 2. 签名验证

```bash
# 验证文件签名
tauri signer verify ./storage/releases/stable/realitytap-1.2.0-windows-x86_64.msi \
  --signature "dW50cnVzdGVkIGNvbW1lbnQ6..." \
  --public-key ./keys/realitytap-updater.pub
```

## 故障排除

### 常见问题

1. **签名生成失败**
   - 检查 Tauri CLI 是否正确安装
   - 验证私钥文件路径和权限
   - 确认环境变量配置正确

2. **客户端更新失败**
   - 检查端点 URL 是否正确
   - 验证公钥配置是否匹配
   - 查看服务器日志获取详细错误信息

3. **签名验证失败**
   - 确认签名是否正确生成
   - 检查公钥和私钥是否匹配
   - 验证文件是否在签名后被修改

### 日志分析

服务器会记录详细的更新请求和签名操作日志，可以通过以下方式查看：

```bash
# 查看实时日志
tail -f storage/logs/app.log

# 搜索特定错误
grep "signature" storage/logs/app.log
grep "updater" storage/logs/app.log
```

## 性能优化

### 缓存策略

- 版本元数据会被缓存以提高响应速度
- 签名验证结果可以缓存避免重复计算
- 静态文件使用适当的 HTTP 缓存头

### 监控指标

建议监控以下指标：

- 更新检查请求频率
- 签名生成和验证耗时
- 文件下载成功率
- 错误率和响应时间

## 总结

通过以上步骤，realitytap-ota-server 已成功迁移到与 tauri-plugin-updater 兼容的更新机制。新的实现提供了更好的安全性、标准化的 API 接口，同时保持了向后兼容性。
