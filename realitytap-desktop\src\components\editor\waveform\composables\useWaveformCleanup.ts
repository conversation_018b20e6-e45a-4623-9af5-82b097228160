/**
 * 波形图清理管理 Composable
 * 统一管理所有清理逻辑，减少主组件的复杂度
 */

import { waveformLogger } from "@/utils/logger/logger";

export interface CleanupConfig {
  // 监听器清理
  stopAllWatchers: () => void;
  
  // 异步操作清理
  asyncManager: {
    getOperationStats: () => any;
    cleanupAllOperations: () => void;
  };
  
  // 音频数据清理
  clearAudioData: () => void;
  resetAudioWaveformState: () => void;
  
  // 事件绘制缓存清理
  clearAllCache: () => void;
  resetEventDrawingState: () => void;
  
  // 右键菜单清理
  contextMenuCleanup: () => void;
  
  // 绘制状态清理
  resetDrawState: () => void;
  
  // 拖拽状态清理
  dragResetState: () => void;
  
  // 性能监控清理已移除
  
  // 生命周期管理器
  lifecycleManager?: {
    getListenerStats: () => any;
  };
}

export interface CleanupManager {
  performCompleteCleanup: () => void;
  addCustomCleanupTask: (task: () => void) => void;
  getCleanupTaskCount: () => number;
}

export function useWaveformCleanup(config: CleanupConfig): CleanupManager {
  const customCleanupTasks: (() => void)[] = [];

  // 执行完整清理
  const performCompleteCleanup = () => {
    waveformLogger.debug("开始执行完整清理...");

    // 1. 停止所有 watch 监听器
    waveformLogger.debug("停止所有监听器");
    try {
      config.stopAllWatchers();
    } catch (error) {
      waveformLogger.warn("停止监听器时出错", error);
    }

    // 2. 清理异步操作
    waveformLogger.debug("清理异步操作");
    try {
      const stats = config.asyncManager.getOperationStats();
      waveformLogger.debug("清理前异步操作统计", stats);
      config.asyncManager.cleanupAllOperations();
    } catch (error) {
      waveformLogger.warn("清理异步操作时出错", error);
    }

    // 3. 清理音频相关数据和缓存
    waveformLogger.debug("清理音频数据和缓存");
    try {
      config.clearAudioData();
      config.resetAudioWaveformState();
    } catch (error) {
      waveformLogger.warn("清理音频数据时出错", error);
    }

    // 4. 清理事件绘制缓存
    waveformLogger.debug("清理事件绘制缓存");
    try {
      config.clearAllCache();
      config.resetEventDrawingState();
    } catch (error) {
      waveformLogger.warn("清理事件绘制缓存时出错:", error);
    }

    // 5. 清理右键菜单相关资源
    waveformLogger.debug("清理右键菜单资源");
    try {
      config.contextMenuCleanup();
    } catch (error) {
      waveformLogger.warn("清理右键菜单资源时出错:", error);
    }

    // 6. 重置绘制状态
    waveformLogger.debug("重置绘制状态");
    try {
      config.resetDrawState();
    } catch (error) {
      waveformLogger.warn("重置绘制状态时出错:", error);
    }

    // 7. 重置拖拽状态
    waveformLogger.debug("重置拖拽状态");
    try {
      config.dragResetState();
    } catch (error) {
      waveformLogger.warn("重置拖拽状态时出错:", error);
    }

    // 8. 性能监控状态重置已移除

    // 8. 执行自定义清理任务
    waveformLogger.debug(`执行 ${customCleanupTasks.length} 个自定义清理任务`);
    customCleanupTasks.forEach((cleanup, index) => {
      try {
        cleanup();
        waveformLogger.debug(`已执行清理任务 ${index + 1}`);
      } catch (error) {
        waveformLogger.warn(`执行清理任务 ${index + 1} 时出错:`, error);
      }
    });
    customCleanupTasks.length = 0;

    // 9. 输出生命周期管理器统计信息
    if (config.lifecycleManager) {
      try {
        const stats = config.lifecycleManager.getListenerStats();
        waveformLogger.debug("事件监听器统计:", stats);
      } catch (error) {
        waveformLogger.warn("获取事件监听器统计时出错:", error);
      }
    }

    waveformLogger.debug("完整清理完成");
  };

  // 添加自定义清理任务
  const addCustomCleanupTask = (task: () => void) => {
    customCleanupTasks.push(task);
    waveformLogger.debug(`添加自定义清理任务，当前任务数: ${customCleanupTasks.length}`);
  };

  // 获取清理任务数量
  const getCleanupTaskCount = () => customCleanupTasks.length;

  return {
    performCompleteCleanup,
    addCustomCleanupTask,
    getCleanupTaskCount,
  };
}
