<template>
  <NModal v-model:show="isVisible" preset="card" :title="t('debug.logViewer.title')" size="huge" :style="{ width: '90vw', height: '80vh' }" class="log-viewer-modal">
    <div class="log-viewer-container">
      <!-- 工具栏 -->
      <div class="toolbar">
        <NSpace>
          <NButton @click="refreshLogs" :loading="isLoading" type="primary">
            <template #icon>
              <NIcon><RefreshIcon /></NIcon>
            </template>
            {{ t("debug.logViewer.refresh") }}
          </NButton>

          <NButton @click="clearLogs" :loading="isClearing" type="warning">
            <template #icon>
              <NIcon><TrashIcon /></NIcon>
            </template>
            {{ t("debug.logViewer.clear") }}
          </NButton>

          <NButton @click="exportDebugInfo" :loading="isExporting" type="info">
            <template #icon>
              <NIcon><DownloadIcon /></NIcon>
            </template>
            {{ t("debug.logViewer.export") }}
          </NButton>

          <NInputNumber v-model:value="maxLines" :min="50" :max="10000" :step="50" :style="{ width: '120px' }" @update:value="refreshLogs">
            <template #prefix>{{ t("debug.logViewer.maxLines") }}</template>
          </NInputNumber>

          <NSwitch v-model:value="autoRefresh" @update:value="toggleAutoRefresh">
            <template #checked>{{ t("debug.logViewer.autoRefreshOn") }}</template>
            <template #unchecked>{{ t("debug.logViewer.autoRefreshOff") }}</template>
          </NSwitch>
        </NSpace>
      </div>

      <!-- 日志内容 -->
      <div class="log-content-container">
        <NScrollbar class="log-scrollbar">
          <div class="log-content" ref="logContentRef">
            <pre v-if="logContent" class="log-text">{{ logContent }}</pre>
            <div v-else-if="isLoading" class="log-placeholder">
              <NSpin size="medium" />
              <p>{{ t("debug.logViewer.loading") }}</p>
            </div>
            <div v-else class="log-placeholder">
              <p>{{ t("debug.logViewer.noLogs") }}</p>
            </div>
          </div>
        </NScrollbar>
      </div>

      <!-- 状态栏 -->
      <div class="status-bar">
        <NSpace justify="space-between">
          <span class="log-info"> {{ t("debug.logViewer.logPath") }}: {{ logFilePath || t("debug.logViewer.unknown") }} </span>
          <span class="log-stats">
            {{ t("debug.logViewer.lines") }}: {{ logLineCount }} | {{ t("debug.logViewer.size") }}: {{ formatFileSize(logContent?.length || 0) }}
          </span>
        </NSpace>
      </div>
    </div>

    <template #action>
      <NButton @click="hideLogViewer">{{ t("common.close") }}</NButton>
    </template>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from "vue";
import { useI18n } from "@/composables/useI18n";
import { useMessage } from "naive-ui";
import { invoke } from "@tauri-apps/api/core";
import { RefreshOutline as RefreshIcon, TrashOutline as TrashIcon, DownloadOutline as DownloadIcon } from "@vicons/ionicons5";

// === 组合函数 ===
const { t } = useI18n();
const message = useMessage();

// === 响应式状态 ===
const isVisible = ref(false);
const isLoading = ref(false);
const isClearing = ref(false);
const isExporting = ref(false);
const logContent = ref<string>("");
const logFilePath = ref<string>("");
const maxLines = ref(1000);
const autoRefresh = ref(false);
const logContentRef = ref<HTMLElement>();

// 自动刷新定时器
let autoRefreshTimer: number | null = null;

// === 计算属性 ===
const logLineCount = computed(() => {
  return logContent.value ? logContent.value.split("\n").length : 0;
});

// === 方法 ===

/**
 * 显示日志查看器
 */
const showLogViewer = async () => {
  isVisible.value = true;
  await loadLogFilePath();
  await refreshLogs();
};

/**
 * 隐藏日志查看器
 */
const hideLogViewer = () => {
  isVisible.value = false;
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer);
    autoRefreshTimer = null;
  }
  autoRefresh.value = false;
};

/**
 * 加载日志文件路径
 */
const loadLogFilePath = async () => {
  try {
    logFilePath.value = await invoke<string>("get_log_file_path");
  } catch (error) {
    console.error("Failed to get log file path:", error);
    message.error(t("debug.logViewer.errors.getPathFailed"));
  }
};

/**
 * 刷新日志内容
 */
const refreshLogs = async () => {
  if (isLoading.value) return;

  isLoading.value = true;
  try {
    logContent.value = await invoke<string>("read_log_file", {
      lines: maxLines.value,
    });

    // 滚动到底部
    await nextTick();
    scrollToBottom();
  } catch (error) {
    console.error("Failed to read log file:", error);
    message.error(t("debug.logViewer.errors.readFailed"));
    logContent.value = "";
  } finally {
    isLoading.value = false;
  }
};

/**
 * 清空日志
 */
const clearLogs = async () => {
  if (isClearing.value) return;

  isClearing.value = true;
  try {
    await invoke("clear_log_file");
    logContent.value = "";
    message.success(t("debug.logViewer.clearSuccess"));
  } catch (error) {
    console.error("Failed to clear log file:", error);
    message.error(t("debug.logViewer.errors.clearFailed"));
  } finally {
    isClearing.value = false;
  }
};

/**
 * 导出调试信息
 */
const exportDebugInfo = async () => {
  if (isExporting.value) return;

  isExporting.value = true;
  try {
    const exportPath = await invoke<string>("export_debug_info");
    message.success(t("debug.logViewer.exportSuccess", { path: exportPath }));
  } catch (error) {
    console.error("Failed to export debug info:", error);
    message.error(t("debug.logViewer.errors.exportFailed"));
  } finally {
    isExporting.value = false;
  }
};

/**
 * 切换自动刷新
 */
const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    autoRefreshTimer = setInterval(refreshLogs, 5000); // 每5秒刷新一次
  } else {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer);
      autoRefreshTimer = null;
    }
  }
};

/**
 * 滚动到底部
 */
const scrollToBottom = () => {
  if (logContentRef.value) {
    logContentRef.value.scrollTop = logContentRef.value.scrollHeight;
  }
};

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// === 生命周期 ===
onMounted(() => {
  // 组件挂载时不自动显示，由父组件控制
});

onUnmounted(() => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer);
  }
});

// === 暴露给父组件的方法 ===
defineExpose({
  showLogViewer,
  hideLogViewer,
});
</script>

<style scoped>
.log-viewer-container {
  display: flex;
  flex-direction: column;
  height: 70vh;
  gap: 12px;
}

.toolbar {
  flex-shrink: 0;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--n-border-color);
}

.log-content-container {
  flex: 1;
  min-height: 0;
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.log-scrollbar {
  height: 100%;
}

.log-content {
  height: 100%;
  padding: 12px;
}

.log-text {
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  color: var(--n-text-color);
  background: transparent;
}

.log-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--n-text-color-3);
  gap: 12px;
}

.status-bar {
  flex-shrink: 0;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid var(--n-border-color);
  font-size: 12px;
  color: var(--n-text-color-2);
}

.log-info {
  font-family: monospace;
}

.log-stats {
  font-family: monospace;
}

/* 深色主题适配 */
:deep(.n-modal-card) {
  background: var(--n-color);
}

/* 滚动条样式 */
:deep(.n-scrollbar-rail) {
  right: 2px;
}
</style>
