import { z } from 'zod';

/**
 * 下载进度 Schema
 */
export const DownloadProgressSchema = z.object({
  sessionId: z.string().uuid('Invalid session ID format'),
  progress: z.number().min(0).max(100, 'Progress must be between 0 and 100'),
  downloadedBytes: z.number().min(0, 'Downloaded bytes must be non-negative'),
  totalBytes: z.number().positive('Total bytes must be positive'),
  speed: z.number().min(0, 'Speed must be non-negative'),
  estimatedTimeRemaining: z.number().min(0, 'Estimated time must be non-negative'),
  status: z.enum(['downloading', 'paused', 'completed', 'failed'], {
    errorMap: () => ({ message: 'Invalid download status' }),
  }),
});

/**
 * 平台发布信息 Schema
 */
export const PlatformReleaseSchema = z.object({
  filename: z.string().min(1, 'Filename is required'),
  size: z.number().positive('File size must be positive'),
  checksum: z.string().regex(/^(sha256|md5):[a-fA-F0-9]+$/, 'Invalid checksum format'),
  releaseDate: z.string().datetime('Invalid release date format'),
  releaseNotes: z.string().min(1, 'Release notes are required'),
  downloadUrl: z.string().url().optional(),
  mirrors: z.array(z.string().url()).optional(),
});

/**
 * 平台信息 Schema
 */
export const PlatformInfoSchema = z.object({
  platform: z.enum(['windows', 'macos', 'linux'], {
    errorMap: () => ({ message: 'Invalid platform' }),
  }),
  architecture: z.enum(['x86_64', 'aarch64', 'x86'], {
    errorMap: () => ({ message: 'Invalid architecture' }),
  }),
  osVersion: z.string().optional(),
  kernelVersion: z.string().optional(),
  locale: z.string().optional(),
});

/**
 * 平台兼容性 Schema
 */
export const PlatformCompatibilitySchema = z.object({
  platform: z.enum(['windows', 'macos', 'linux']),
  architecture: z.enum(['x86_64', 'aarch64', 'x86']),
  minimumOSVersion: z.string().optional(),
  maximumOSVersion: z.string().optional(),
  supportedFeatures: z.array(z.string()),
  deprecatedFeatures: z.array(z.string()),
});

/**
 * 平台检测结果 Schema
 */
export const PlatformDetectionResultSchema = z.object({
  platform: z.enum(['windows', 'macos', 'linux']),
  architecture: z.enum(['x86_64', 'aarch64', 'x86']),
  osVersion: z.string(),
  isSupported: z.boolean(),
  compatibility: PlatformCompatibilitySchema,
});

/**
 * 文件验证请求 Schema
 */
export const FileVerificationRequestSchema = z.object({
  filename: z.string().min(1, 'Filename is required'),
  checksum: z.string().regex(/^(sha256|md5):[a-fA-F0-9]+$/, 'Invalid checksum format'),
});

/**
 * 文件验证响应 Schema
 */
export const FileVerificationResponseSchema = z.object({
  filename: z.string(),
  checksum: z.string(),
  isValid: z.boolean(),
  message: z.string(),
});

/**
 * 文件信息 Schema
 */
export const FileInfoSchema = z.object({
  filename: z.string(),
  size: z.number().min(0),
  lastModified: z.string().datetime(),
  downloadUrl: z.string().url(),
  error: z.string().optional(),
});

/**
 * 文件列表响应 Schema
 */
export const FileListResponseSchema = z.object({
  channel: z.string(),
  files: z.array(FileInfoSchema),
  total: z.number().min(0),
});
