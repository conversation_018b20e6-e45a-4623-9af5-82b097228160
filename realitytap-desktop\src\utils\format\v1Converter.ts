/**
 * RealityTap V1 格式转换器
 * 基于 RealityTapEffectV1.java 的 convertToArray 实现
 */

import type { 
  ConvertToArrayResult,
  ConvertToArrayOptions,
  ConvertToArrayMetadata
} from '@/types/reality-tap-converter';
import {
  V1_CONSTANTS,
  EVENT_TYPES,
  EVENT_TYPE_STRINGS
} from '@/types/reality-tap-converter';
import type { RenderableEvent } from '@/types/haptic-editor';
import type { RealityTapEffectV1 } from '@/types/haptic-file';
import { logger, LogModule } from '@/utils/logger/logger';

/**
 * 将 V1 格式数据转换为数组
 * @param input V1 格式的输入数据
 * @param options 转换选项
 * @returns 转换结果
 */
export function convertV1ToArray(
  input: RealityTapEffectV1 | RenderableEvent[],
  options: ConvertToArrayOptions = {}
): ConvertToArrayResult {
  const startTime = performance.now();
  
  try {
    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, '[V1Converter] 开始 V1 转换', {
        inputType: Array.isArray(input) ? 'RenderableEvent[]' : 'RealityTapEffectV1'
      });
    }

    // 提取事件数据
    const events = extractEventsFromV1Input(input);
    if (!events || events.length === 0) {
      const errorMsg = '没有找到有效的事件数据';

      if (options.enableLogging) {
        logger.error(LogModule.WAVEFORM, '[V1Converter] 事件数据提取失败', {
          inputType: Array.isArray(input) ? 'RenderableEvent[]' : 'RealityTapEffectV1',
          hasPattern: !!(input as any)?.Pattern,
          patternLength: Array.isArray((input as any)?.Pattern) ? (input as any).Pattern.length : 0,
          isInputArray: Array.isArray(input),
          inputLength: Array.isArray(input) ? input.length : 0
        });
      }

      return {
        success: false,
        error: errorMsg,
        version: 1
      };
    }

    if (options.enableLogging) {
      logger.info(LogModule.WAVEFORM, '[V1Converter] 事件数据提取成功', {
        totalEvents: events.length,
        inputType: Array.isArray(input) ? 'RenderableEvent[]' : 'RealityTapEffectV1'
      });
    }

    // 计算数组长度
    const arrayLength = events.length * V1_CONSTANTS.POSITIONS_PER_EVENT;
    const array = new Array(arrayLength).fill(0);

    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, '[V1Converter] 初始化输出数组', {
        totalEvents: events.length,
        positionsPerEvent: V1_CONSTANTS.POSITIONS_PER_EVENT,
        arrayLength,
        memoryUsage: `${(arrayLength * 8 / 1024).toFixed(2)} KB` // 假设每个数字8字节
      });
    }

    let processedEvents = 0;
    let skippedEvents = 0;
    const warnings: string[] = [];

    // 处理每个事件
    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, '[V1Converter] 开始处理事件', {
        totalEvents: events.length,
        batchSize: events.length
      });
    }

    for (let i = 0; i < events.length; i++) {
      try {
        const event = events[i];
        const baseIndex = i * V1_CONSTANTS.POSITIONS_PER_EVENT;

        // 记录事件处理开始（仅在调试模式下记录部分事件）
        if (options.enableLogging && (i < 5 || i % 100 === 0 || i === events.length - 1)) {
          logger.debug(LogModule.WAVEFORM, '[V1Converter] 处理事件', {
            eventIndex: i,
            eventType: event.type || event.Type,
            baseIndex,
            progress: `${i + 1}/${events.length}`
          });
        }

        const processed = processV1Event(event, array, baseIndex, i, options);
        if (processed.success) {
          processedEvents++;
        } else {
          skippedEvents++;
          if (processed.warning) {
            warnings.push(processed.warning);
          }

          if (options.enableLogging) {
            logger.warn(LogModule.WAVEFORM, '[V1Converter] 事件处理跳过', {
              eventIndex: i,
              eventType: event.type || event.Type,
              warning: processed.warning
            });
          }
        }
      } catch (error) {
        skippedEvents++;
        const errorMsg = `事件[${i}]处理失败: ${error instanceof Error ? error.message : String(error)}`;
        warnings.push(errorMsg);

        if (options.enableLogging) {
          logger.warn(LogModule.WAVEFORM, '[V1Converter] 事件处理异常', {
            eventIndex: i,
            error: error instanceof Error ? error.message : String(error),
            eventType: events[i]?.type || events[i]?.Type || 'unknown'
          });
        }
      }
    }

    if (options.enableLogging) {
      logger.info(LogModule.WAVEFORM, '[V1Converter] 事件处理完成', {
        processedEvents,
        skippedEvents,
        totalEvents: events.length,
        successRate: `${((processedEvents / events.length) * 100).toFixed(1)}%`,
        warningCount: warnings.length
      });
    }

    const processingTime = performance.now() - startTime;
    
    const metadata: ConvertToArrayMetadata = {
      totalEvents: events.length,
      arrayLength,
      processingTime,
      detectedVersion: 1,
      skippedEvents: skippedEvents > 0 ? skippedEvents : undefined
    };

    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, '[V1Converter] V1 转换完成', {
        processedEvents,
        skippedEvents,
        arrayLength,
        processingTime: `${processingTime.toFixed(2)}ms`
      });
    }

    return {
      success: true,
      data: array,
      version: 1,
      metadata,
      warnings: warnings.length > 0 ? warnings : undefined
    };

  } catch (error) {
    const errorMessage = `V1 转换失败: ${error instanceof Error ? error.message : String(error)}`;
    
    if (options.enableLogging) {
      logger.error(LogModule.WAVEFORM, '[V1Converter] 转换过程异常', error);
    }

    return {
      success: false,
      error: errorMessage,
      version: 1
    };
  }
}

/**
 * 从 V1 输入中提取事件数据
 */
function extractEventsFromV1Input(input: RealityTapEffectV1 | RenderableEvent[]): any[] | null {
  try {
    if (Array.isArray(input)) {
      return input;
    }

    // 从 RealityTapEffectV1 中提取事件
    const pattern = input.Pattern;
    if (!pattern || !Array.isArray(pattern)) {
      return null;
    }

    // 提取包装的事件数据
    return pattern.map(wrappedEvent => {
      return wrappedEvent.Event || wrappedEvent;
    });

  } catch (error) {
    return null;
  }
}

/**
 * 处理单个 V1 事件
 */
function processV1Event(
  event: any,
  array: number[],
  baseIndex: number,
  eventIndex: number,
  options: ConvertToArrayOptions
): { success: boolean; warning?: string } {
  try {
    // 确定事件类型
    const eventType = normalizeEventType(event);
    if (!eventType) {
      const warning = `事件[${eventIndex}]: 无效的事件类型 "${event.type || event.Type}"`;

      if (options.enableLogging) {
        logger.warn(LogModule.WAVEFORM, '[V1Converter] 无效事件类型', {
          eventIndex,
          providedType: event.type || event.Type,
          availableTypes: Object.values(EVENT_TYPE_STRINGS)
        });
      }

      return {
        success: false,
        warning
      };
    }

    let index = baseIndex;

    if (eventType === EVENT_TYPE_STRINGS.TRANSIENT) {
      // 处理瞬时事件
      const relativeTime = extractRelativeTime(event);
      const intensity = extractIntensity(event);
      const frequency = extractFrequency(event);

      array[index++] = EVENT_TYPES.TRANSIENT;
      array[index++] = relativeTime;
      array[index++] = intensity;
      array[index++] = frequency;

      if (options.enableLogging && eventIndex < 3) {
        logger.debug(LogModule.WAVEFORM, '[V1Converter] 瞬时事件处理', {
          eventIndex,
          relativeTime,
          intensity,
          frequency,
          baseIndex,
          positions: [EVENT_TYPES.TRANSIENT, relativeTime, intensity, frequency]
        });
      }

      // 剩余位置保持为 0（已在初始化时设置）

    } else if (eventType === EVENT_TYPE_STRINGS.CONTINUOUS) {
      // 处理连续事件
      const relativeTime = extractRelativeTime(event);
      const intensity = extractIntensity(event);
      const frequency = extractFrequency(event);
      const duration = extractDuration(event);

      array[index++] = EVENT_TYPES.CONTINUOUS;
      array[index++] = relativeTime;
      array[index++] = intensity;
      array[index++] = frequency;
      array[index++] = duration;

      // 处理曲线数据（固定4个曲线点）
      const curves = extractCurveData(event);

      if (options.enableLogging && eventIndex < 3) {
        logger.debug(LogModule.WAVEFORM, '[V1Converter] 连续事件处理', {
          eventIndex,
          relativeTime,
          intensity,
          frequency,
          duration,
          curveCount: curves.length,
          baseIndex
        });
      }

      for (let j = 0; j < V1_CONSTANTS.FIXED_CURVE_COUNT; j++) {
        if (j < curves.length) {
          const curve = curves[j];
          const scaledIntensity = Math.round(curve.intensity * V1_CONSTANTS.INTENSITY_SCALE_FACTOR);

          array[index++] = curve.time;
          array[index++] = scaledIntensity;
          array[index++] = curve.frequency;

          if (options.enableLogging && eventIndex < 3) {
            logger.debug(LogModule.WAVEFORM, '[V1Converter] 曲线点处理', {
              eventIndex,
              curveIndex: j,
              originalIntensity: curve.intensity,
              scaledIntensity,
              time: curve.time,
              frequency: curve.frequency
            });
          }
        } else {
          // 填充空的曲线点
          array[index++] = 0;
          array[index++] = 0;
          array[index++] = 0;
        }
      }
    }

    return { success: true };

  } catch (error) {
    const warning = `事件[${eventIndex}]: 处理失败 - ${error instanceof Error ? error.message : String(error)}`;

    if (options.enableLogging) {
      logger.error(LogModule.WAVEFORM, '[V1Converter] 事件处理异常', {
        eventIndex,
        error: error instanceof Error ? error.message : String(error),
        eventType: event.type || event.Type,
        baseIndex
      });
    }

    return {
      success: false,
      warning
    };
  }
}

/**
 * 标准化事件类型
 */
function normalizeEventType(event: any): string | null {
  const type = event.type || event.Type;
  if (typeof type === 'string') {
    const lowerType = type.toLowerCase();
    if (lowerType === 'transient') return EVENT_TYPE_STRINGS.TRANSIENT;
    if (lowerType === 'continuous') return EVENT_TYPE_STRINGS.CONTINUOUS;
  }
  return null;
}

/**
 * 提取相对时间
 */
function extractRelativeTime(event: any): number {
  // 优先使用 RelativeTime，然后是 relativeTime，最后是 startTime
  const relativeTime = event.RelativeTime ?? event.relativeTime ?? event.startTime ?? 0;
  return Math.max(0, Math.floor(Number(relativeTime) || 0));
}

/**
 * 提取强度值
 */
function extractIntensity(event: any): number {
  // 尝试多种可能的字段名
  const intensity = event.Parameters?.Intensity ?? 
                   event.parameters?.intensity ??
                   event.intensity ??
                   event.eventIntensity ??
                   0;
  
  return Math.max(0, Math.min(100, Math.floor(Number(intensity) || 0)));
}

/**
 * 提取频率值
 */
function extractFrequency(event: any): number {
  // 尝试多种可能的字段名
  const frequency = event.Parameters?.Frequency ?? 
                   event.parameters?.frequency ??
                   event.frequency ??
                   event.eventFrequency ??
                   0;
  
  return Math.max(0, Math.min(100, Math.floor(Number(frequency) || 0)));
}

/**
 * 提取持续时间
 */
function extractDuration(event: any): number {
  const duration = event.Duration ?? event.duration ?? 0;
  return Math.max(0, Math.floor(Number(duration) || 0));
}

/**
 * 提取曲线数据
 */
function extractCurveData(event: any): Array<{ time: number; intensity: number; frequency: number }> {
  try {
    // 尝试从不同路径获取曲线数据
    let curves = event.Parameters?.Curves ?? 
                event.parameters?.curves ??
                event.curves ??
                [];

    if (!Array.isArray(curves)) {
      return [];
    }

    return curves.map((curve: any) => ({
      time: Math.max(0, Math.floor(Number(curve.Time ?? curve.time ?? curve.timeOffset ?? 0))),
      intensity: Math.max(0, Math.min(1, Number(curve.Intensity ?? curve.intensity ?? curve.rawIntensity ?? 0))),
      frequency: Math.max(-100, Math.min(100, Math.floor(Number(curve.Frequency ?? curve.frequency ?? curve.relativeCurveFrequency ?? 0))))
    }));

  } catch (error) {
    return [];
  }
}

/**
 * 验证 V1 转换结果
 */
export function validateV1ConversionResult(
  array: number[],
  originalEventCount: number
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 检查数组长度
  const expectedLength = originalEventCount * V1_CONSTANTS.POSITIONS_PER_EVENT;
  if (array.length !== expectedLength) {
    errors.push(`数组长度不匹配: 期望 ${expectedLength}, 实际 ${array.length}`);
  }

  // 检查数组内容
  for (let i = 0; i < array.length; i += V1_CONSTANTS.POSITIONS_PER_EVENT) {
    const eventType = array[i];
    if (eventType !== EVENT_TYPES.TRANSIENT && eventType !== EVENT_TYPES.CONTINUOUS && eventType !== 0) {
      errors.push(`位置 ${i}: 无效的事件类型 ${eventType}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
