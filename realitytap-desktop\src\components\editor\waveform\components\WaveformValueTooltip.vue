<template>
  <div v-if="showTooltip" class="event-tooltip" :style="tooltipStyle">
    <!-- 按住频率调节键时显示频率 -->
    <template v-if="config.isFrequencyAdjustmentKeyPressed.value">
      <div class="relative-frequency-value">
        <span class="relative-frequency-label">频率:</span>
        <span class="relative-frequency-number">{{ displayRelativeFrequency }}</span>
      </div>
    </template>

    <!-- 没有按住Alt键时显示时间和振幅 -->
    <template v-else>
      <div class="time-value">
        <span class="time-label">时间:</span>
        <span class="time-number">{{ displayTime }}</span>
      </div>
      <div class="amplitude-value">
        <span class="amplitude-label">振幅:</span>
        <span class="amplitude-number">{{ displayAmplitude }}</span>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useEventTooltip, type EventTooltipConfig } from "../composables/useEventTooltip";

const props = defineProps<{
  config: EventTooltipConfig;
}>();

// 使用事件tooltip composable
const { showTooltip, tooltipStyle, displayRelativeFrequency, displayTime, displayAmplitude, showEventTooltip, hideEventTooltip, updateTooltipPosition } = useEventTooltip(
  props.config
);

// 暴露方法给父组件使用
defineExpose({
  showEventTooltip,
  hideEventTooltip,
  updateTooltipPosition,
});
</script>

<style scoped>
/* tooltip样式 */
.event-tooltip {
  background: rgba(0, 0, 0, 0.85);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  min-width: 120px;
}

.frequency-value,
.relative-frequency-value,
.time-value,
.amplitude-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 2px 0;
}

.frequency-label,
.relative-frequency-label,
.time-label,
.amplitude-label {
  color: #cccccc;
  margin-right: 8px;
}

.frequency-number {
  color: #4caf50;
  font-weight: 600;
}

.relative-frequency-number {
  color: #2196f3;
  font-weight: 600;
}

.time-number {
  color: #ff9800;
  font-weight: 600;
}

.amplitude-number {
  color: #e91e63;
  font-weight: 600;
}
</style>
