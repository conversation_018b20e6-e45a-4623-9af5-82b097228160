import { createRouter, createWebHistory } from "vue-router";
import type { RouteRecordRaw } from "vue-router";

const routes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "Home",
    component: () => import("@/views/ProjectDashboardView.vue"),
    meta: {
      title: "RealityTap Studio",
    },
  },
  {
    path: "/editor",
    name: "Editor",
    component: () => import("@/views/HapticEditorView.vue"),
    props: (route) => ({
      projectId: route.query.id,
    }),
    meta: {
      title: "RealityTap Studio - Editor",
    },
  },
  {
    path: "/learning",
    name: "Learning",
    component: () => import("@/views/LearningResourcesView.vue"),
    meta: {
      title: "RealityTap Studio - Learning",
    },
  },
  {
    path: "/i18n-test",
    name: "I18nTest",
    component: () => import("@/views/I18nTestView.vue"),
    meta: {
      title: "RealityTap Studio - I18n Test",
    },
  },
  // 开发工具路由（仅开发环境）
  ...(import.meta.env.DEV ? [{
    path: "/dev-tools",
    name: "DevTools",
    component: () => import("@/views/DevToolsView.vue"),
    meta: {
      title: "RealityTap Studio - 开发工具",
    },
  }] : []),
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由导航守卫，用于更新页面标题
router.beforeEach((to, _from, next) => {
  document.title = to.meta.title as string;
  next();
});

export default router;
