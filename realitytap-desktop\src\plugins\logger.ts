/**
 * RealityTap Studio 日志管理 Vue 插件
 * 
 * 功能：
 * - 全局日志配置初始化
 * - 开发工具集成
 * - 性能监控集成
 * - 错误处理集成
 */

import type { App } from 'vue';
import { logger, LogModule, LogLevel } from '@/utils/logger/logger';
import { LOGGER_CONFIG } from '@/utils/logger/loggerConfig';

// 开发工具扩展
interface DevToolsLogger {
  exportLogs: () => void;
  clearLogs: () => void;
  setLogLevel: (level: LogLevel) => void;
  getStats: () => any;
}

// 声明全局属性
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $logger: typeof logger;
    $log: {
      debug: (module: LogModule, message: string, data?: any) => void;
      info: (module: LogModule, message: string, data?: any) => void;
      warn: (module: LogModule, message: string, data?: any) => void;
      error: (module: LogModule, message: string, data?: any) => void;
      performance: (module: LogModule, operation: string, duration: number, data?: any) => void;
    };
  }
}

// 全局 window 扩展（开发环境）
declare global {
  interface Window {
    __REALITYTAP_LOGGER__?: DevToolsLogger;
  }
}

/**
 * 初始化开发工具
 */
function initDevTools() {
  if (import.meta.env.DEV && typeof window !== 'undefined') {
    const devTools: DevToolsLogger = {
      exportLogs: () => {
        const logs = logger.exportLogs();
        const blob = new Blob([logs], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `realitytap-logs-${new Date().toISOString().slice(0, 19)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        console.log('📥 日志已导出');
      },
      
      clearLogs: () => {
        logger.clearLogBuffer();
        console.log('🗑️ 日志缓冲区已清空');
      },
      
      setLogLevel: (level: LogLevel) => {
        // 这里可以动态调整日志级别
        console.log(`📊 日志级别已设置为: ${LogLevel[level]}`);
      },
      
      getStats: () => {
        const buffer = logger.getLogBuffer();
        const stats = {
          totalLogs: buffer.length,
          byLevel: buffer.reduce((acc, log) => {
            const level = log.match(/\[(DEBUG|INFO|WARN|ERROR)\]/)?.[1] || 'UNKNOWN';
            acc[level] = (acc[level] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
          byModule: buffer.reduce((acc, log) => {
            const module = log.match(/\[([A-Z_]+)\]/g)?.[1]?.replace(/[\[\]]/g, '') || 'UNKNOWN';
            acc[module] = (acc[module] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
          config: LOGGER_CONFIG
        };
        console.table(stats.byLevel);
        console.table(stats.byModule);
        return stats;
      }
    };
    
    window.__REALITYTAP_LOGGER__ = devTools;

    logger.info(LogModule.GENERAL, 'RealityTap Studio 日志系统已初始化', {
      mode: LOGGER_CONFIG.productionMode ? '生产模式' : '开发模式',
      devToolsAvailable: true,
      commands: [
        '__REALITYTAP_LOGGER__.exportLogs() - 导出日志',
        '__REALITYTAP_LOGGER__.clearLogs() - 清空日志',
        '__REALITYTAP_LOGGER__.getStats() - 查看统计信息',
        '__REALITYTAP_LOGGER__.setLogLevel(level) - 设置日志级别'
      ]
    });
  }
}

/**
 * 初始化错误处理
 */
function initErrorHandling() {
  // Vue 错误处理
  const originalErrorHandler = console.error;
  console.error = (...args) => {
    // 检查是否是 Vue 相关错误
    const errorMessage = args[0]?.toString() || '';
    if (errorMessage.includes('Vue') || errorMessage.includes('component')) {
      logger.error(LogModule.GENERAL, 'Vue 错误', args);
    }
    originalErrorHandler.apply(console, args);
  };
  
  // 全局未捕获错误
  if (typeof window !== 'undefined') {
    window.addEventListener('error', (event) => {
      logger.error(LogModule.GENERAL, '全局错误', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      logger.error(LogModule.GENERAL, '未处理的 Promise 拒绝', {
        reason: event.reason
      });
    });
  }
}

/**
 * 初始化性能监控
 */
function initPerformanceMonitoring() {
  if (LOGGER_CONFIG.enablePerformanceLogging && typeof window !== 'undefined') {
    // 监控页面加载性能
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (perfData) {
          logger.performance(LogModule.GENERAL, '页面加载', perfData.loadEventEnd - perfData.fetchStart, {
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.fetchStart,
            firstPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-paint')?.startTime,
            firstContentfulPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-contentful-paint')?.startTime
          });
        }
      }, 0);
    });
  }
}

/**
 * 日志管理 Vue 插件
 */
export const LoggerPlugin = {
  install(app: App) {
    // 初始化各种功能
    initDevTools();
    initErrorHandling();
    initPerformanceMonitoring();
    
    // 注册全局属性
    app.config.globalProperties.$logger = logger;
    app.config.globalProperties.$log = {
      debug: (module: LogModule, message: string, data?: any) => logger.debug(module, message, data),
      info: (module: LogModule, message: string, data?: any) => logger.info(module, message, data),
      warn: (module: LogModule, message: string, data?: any) => logger.warn(module, message, data),
      error: (module: LogModule, message: string, data?: any) => logger.error(module, message, data),
      performance: (module: LogModule, operation: string, duration: number, data?: any) => 
        logger.performance(module, operation, duration, data)
    };
    
    // 开发环境下的额外配置
    if (import.meta.env.DEV) {
      // 启用详细的 Vue 警告
      app.config.warnHandler = (msg, instance, trace) => {
        logger.warn(LogModule.GENERAL, 'Vue 警告', {
          message: msg,
          componentTrace: trace,
          component: instance?.$options.name || 'Anonymous'
        });
      };
    }
    
    logger.info(LogModule.GENERAL, 'RealityTap Studio 日志系统已启动', {
      mode: LOGGER_CONFIG.productionMode ? 'production' : 'development',
      globalLevel: LogLevel[LOGGER_CONFIG.globalLevel],
      performanceLogging: LOGGER_CONFIG.enablePerformanceLogging,
      dragLogging: LOGGER_CONFIG.enableDragLogging
    });
  }
};

// 导出便捷函数
export { logger, LogModule, LogLevel } from '@/utils/logger/logger';
export { LOGGER_CONFIG } from '@/utils/logger/loggerConfig';
