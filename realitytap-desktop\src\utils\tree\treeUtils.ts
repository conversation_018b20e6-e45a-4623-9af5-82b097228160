import type { HapticFile, HapticsGroup, RealityTapProject } from '@/types/haptic-project'
import type { TreeOption as NaiveTreeOption } from 'naive-ui'
import type { VNode } from 'vue'

// TreeNode interface for tree structure
export interface TreeNode extends NaiveTreeOption {
  key: string;
  label: string; // 保持为 string，通过 render-label 处理自定义渲染
  isGroup: boolean;
  item: HapticFile | HapticsGroup; // Store the original item
  children?: TreeNode[];
  prefix?: () => VNode; // For custom icons
}

/**
 * Natural string sorting function for file and group names
 * Uses localeCompare with numeric option for natural sorting
 */
export const naturalSort = (a: string, b: string): number => {
  return a.localeCompare(b, undefined, { numeric: true, sensitivity: "base" });
};

/**
 * Sort tree nodes: groups first, then files, then by name naturally
 */
export const sortTreeNodes = (nodesToSort: TreeNode[]): void => {
  nodesToSort.sort((nodeA, nodeB) => {
    // Rule 1: Groups come before files
    if (nodeA.isGroup && !nodeB.isGroup) return -1; // nodeA (group) comes before nodeB (file)
    if (!nodeA.isGroup && nodeB.isGroup) return 1; // nodeA (file) comes after nodeB (group)

    // Rule 2 & 3: Both are groups or both are files, sort by label (name) naturally
    // For files, we need to get the actual name from the item since label is now a VNode
    const labelA = nodeA.isGroup ? (nodeA.label as string) : (nodeA.item as HapticFile).name;
    const labelB = nodeB.isGroup ? (nodeB.label as string) : (nodeB.item as HapticsGroup | HapticFile).name;
    return naturalSort(labelA, labelB);
  });
};

/**
 * Get original project item (file or group) from tree node key
 */
export const getItemFromNodeKey = (key: string, project: RealityTapProject | null): HapticFile | HapticsGroup | undefined => {
  if (!project) return undefined;

  const parts = String(key).split("-");
  if (parts.length < 2) return undefined; // 确保至少有两个部分

  // 第一部分是类型(group或file)，剩余部分组合为UUID
  const type = parts[0];
  const uuid = parts.slice(1).join("-"); // 重新组合UUID部分(可能包含多个-)

  if (type === "group" && project.groups) {
    return project.groups.find((g) => g.groupUuid === uuid);
  } else if (type === "file" && project.files) {
    return project.files.find((f) => f.fileUuid === uuid);
  }

  return undefined;
};

/**
 * Find a tree node by its key in the tree structure (recursive)
 */
export const findNodeByKey = (nodesToSearch: TreeNode[], key: string): TreeNode | null => {
  for (const n of nodesToSearch) {
    if (n.key === key) return n;
    if (n.children && n.children.length > 0) {
      const foundInChildren = findNodeByKey(n.children, key);
      if (foundInChildren) return foundInChildren;
    }
  }
  return null;
};
