import { z } from 'zod';
import { 
  UpdateCheckRequestSchema,
  UpdateCheckResponseSchema,
  AppVersionInfoSchema,
  OTAVersionInfoSchema,
  VersionComparisonSchema 
} from '../schemas/version.schema';
import {
  PlatformInfoSchema,
  PlatformCompatibilitySchema,
  FileVerificationRequestSchema,
  FileVerificationResponseSchema,
  FileInfoSchema,
  FileListResponseSchema
} from '../schemas/update.schema';
import {
  SuccessResponseSchema,
  ErrorResponseSchema,
  HealthResponseSchema,
  ReadinessResponseSchema,
  LivenessResponseSchema,
  VersionStatsResponseSchema,
  CacheClearResponseSchema,
  ChecksumResponseSchema
} from '../schemas/response.schema';

/**
 * 验证结果接口
 */
export interface ValidationResult<T = any> {
  success: boolean;
  data?: T;
  errors?: z.ZodError;
}

/**
 * 请求验证器类
 */
export class RequestValidator {
  /**
   * 验证更新检查请求
   */
  static validateUpdateCheckRequest(data: unknown): ValidationResult {
    const result = UpdateCheckRequestSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证更新检查响应
   */
  static validateUpdateCheckResponse(data: unknown): ValidationResult {
    const result = UpdateCheckResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证应用版本信息
   */
  static validateAppVersionInfo(data: unknown): ValidationResult {
    const result = AppVersionInfoSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证 OTA 版本信息
   */
  static validateOTAVersionInfo(data: unknown): ValidationResult {
    const result = OTAVersionInfoSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证版本比较
   */
  static validateVersionComparison(data: unknown): ValidationResult {
    const result = VersionComparisonSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证平台信息
   */
  static validatePlatformInfo(data: unknown): ValidationResult {
    const result = PlatformInfoSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证平台兼容性
   */
  static validatePlatformCompatibility(data: unknown): ValidationResult {
    const result = PlatformCompatibilitySchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证文件验证请求
   */
  static validateFileVerificationRequest(data: unknown): ValidationResult {
    const result = FileVerificationRequestSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证文件验证响应
   */
  static validateFileVerificationResponse(data: unknown): ValidationResult {
    const result = FileVerificationResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证文件信息
   */
  static validateFileInfo(data: unknown): ValidationResult {
    const result = FileInfoSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证文件列表响应
   */
  static validateFileListResponse(data: unknown): ValidationResult {
    const result = FileListResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证成功响应
   */
  static validateSuccessResponse<T>(data: unknown, dataSchema: z.ZodTypeAny): ValidationResult<T> {
    const schema = SuccessResponseSchema(dataSchema);
    const result = schema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证错误响应
   */
  static validateErrorResponse(data: unknown): ValidationResult {
    const result = ErrorResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证健康检查响应
   */
  static validateHealthResponse(data: unknown): ValidationResult {
    const result = HealthResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证就绪检查响应
   */
  static validateReadinessResponse(data: unknown): ValidationResult {
    const result = ReadinessResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证存活检查响应
   */
  static validateLivenessResponse(data: unknown): ValidationResult {
    const result = LivenessResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证版本统计响应
   */
  static validateVersionStatsResponse(data: unknown): ValidationResult {
    const result = VersionStatsResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证缓存清除响应
   */
  static validateCacheClearResponse(data: unknown): ValidationResult {
    const result = CacheClearResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 验证校验和响应
   */
  static validateChecksumResponse(data: unknown): ValidationResult {
    const result = ChecksumResponseSchema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }

  /**
   * 通用验证方法
   */
  static validate<T>(data: unknown, schema: z.ZodSchema<T>): ValidationResult<T> {
    const result = schema.safeParse(data);
    return {
      success: result.success,
      data: result.success ? result.data : undefined,
      errors: result.success ? undefined : result.error,
    };
  }
}
