import { createPinia } from 'pinia';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';

// 添加启动日志
console.log('🚀 开始初始化 RealityTap OTA 管理界面...');

// 检查必要的DOM元素
const appElement = document.getElementById('app');
if (!appElement) {
  console.error('❌ 找不到 #app 元素！');
  document.body.innerHTML = '<div style="padding: 20px; color: red;">错误：找不到应用挂载点 #app</div>';
} else {
  console.log('✅ 找到 #app 元素');
}

// 在开发环境下引入调试工具和控制台过滤器
if (import.meta.env.DEV) {
  import('./utils/debug');
  import('./utils/console-filter').then(({ enableConsoleFilter }) => {
    enableConsoleFilter();
  });
}

try {
  console.log('📦 创建 Vue 应用实例...');
  const app = createApp(App);

  console.log('🗃️ 配置 Pinia 状态管理...');
  app.use(createPinia());

  console.log('🛣️ 配置 Vue Router...');
  app.use(router);

  // 添加全局错误处理
  app.config.errorHandler = (err, instance, info) => {
    console.error('❌ Vue应用错误:', err);
    console.error('📍 错误信息:', info);
    console.error('🔧 组件实例:', instance);

    // 在页面上显示错误信息
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText =
      'position: fixed; top: 0; left: 0; right: 0; background: #ff4757; color: white; padding: 10px; z-index: 9999;';
    errorDiv.innerHTML = `Vue应用错误: ${err.message}`;
    document.body.appendChild(errorDiv);
  };

  // 添加未捕获的Promise错误处理
  window.addEventListener('unhandledrejection', event => {
    console.error('❌ 未捕获的Promise错误:', event.reason);
    event.preventDefault();
  });

  console.log('🎯 挂载 Vue 应用到 #app...');
  app.mount('#app');
  console.log('✅ Vue 应用挂载成功！');
} catch (error) {
  console.error('❌ Vue 应用初始化失败:', error);

  // 在页面上显示错误信息
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; color: red; font-family: monospace;">
        <h2>应用初始化失败</h2>
        <p><strong>错误信息:</strong> ${error.message}</p>
        <p><strong>错误堆栈:</strong></p>
        <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">${error.stack}</pre>
        <p>请检查浏览器控制台获取更多信息。</p>
      </div>
    `;
  }
}
