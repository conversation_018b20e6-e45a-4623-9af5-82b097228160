import { SystemLogDAO, CreateSystemLogData, SystemLogQueryParams } from '@/dao/system-log.dao';
import { logger } from '@/utils/logger.util';

export class SystemLogService {
  private systemLogDAO: SystemLogDAO | null = null;
  private isDbEnabled: boolean;

  constructor() {
    this.isDbEnabled = process.env.DB_ENABLED === 'true';
  }

  private getDAO(): SystemLogDAO {
    if (!this.systemLogDAO) {
      this.systemLogDAO = new SystemLogDAO();
    }
    return this.systemLogDAO;
  }

  /**
   * 记录系统日志到数据库
   */
  async logToDatabase(data: CreateSystemLogData): Promise<void> {
    if (!this.isDbEnabled) {
      return; // 如果数据库未启用，跳过数据库写入
    }

    try {
      await this.getDAO().createSystemLog(data);
    } catch (error) {
      // 数据库写入失败不应该影响主要功能，只记录错误
      console.error('系统日志数据库写入失败:', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        logData: {
          level: data.level,
          message: data.message?.substring(0, 100) + (data.message?.length > 100 ? '...' : ''),
          module: data.module,
          operation: data.operation,
          client_ip: data.client_ip,
          path: data.path,
          method: data.method,
          status_code: data.status_code,
          timestamp: data.timestamp,
        },
        operation: 'log_to_database',
        module: 'system',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 批量记录系统日志到数据库
   */
  async batchLogToDatabase(logs: CreateSystemLogData[]): Promise<void> {
    if (!this.isDbEnabled || logs.length === 0) {
      return;
    }

    try {
      await this.getDAO().batchCreateSystemLogs(logs);
    } catch (error) {
      console.error('批量系统日志数据库写入失败:', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        batchInfo: {
          totalLogs: logs.length,
          logLevels: [...new Set(logs.map(log => log.level))],
          modules: [...new Set(logs.map(log => log.module))],
          operations: [...new Set(logs.map(log => log.operation).filter(Boolean))],
          timeRange: logs.length > 0 ? {
            earliest: logs[0]?.timestamp,
            latest: logs[logs.length - 1]?.timestamp,
          } : null,
        },
        operation: 'batch_log_to_database',
        module: 'system',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 从数据库获取系统日志
   */
  async getLogsFromDatabase(params: SystemLogQueryParams) {
    if (!this.isDbEnabled) {
      return { logs: [], total: 0 };
    }

    try {
      return await this.getDAO().getSystemLogs(params);
    } catch (error) {
      logger.error('从数据库获取系统日志失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        queryParams: {
          module: params.module,
          level: params.level,
          operation: params.operation,
          client_ip: params.client_ip,
          start_date: params.start_date,
          end_date: params.end_date,
          limit: params.limit,
          offset: params.offset,
          search: params.search?.substring(0, 50) + (params.search && params.search.length > 50 ? '...' : ''),
        },
        operation: 'get_logs_from_database',
        module: 'system',
        timestamp: new Date().toISOString(),
      });
      return { logs: [], total: 0 };
    }
  }

  /**
   * 清空数据库中的日志
   */
  async clearDatabaseLogs(): Promise<number> {
    if (!this.isDbEnabled) {
      return 0;
    }

    try {
      return await this.getDAO().clearAllLogs();
    } catch (error) {
      logger.error('清空数据库日志失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        operation: 'clear_database_logs',
        module: 'system',
        timestamp: new Date().toISOString(),
      });
      return 0;
    }
  }

  /**
   * 获取日志统计信息
   */
  async getLogStats() {
    if (!this.isDbEnabled) {
      return {
        total: 0,
        byLevel: {},
        byModule: {},
        recentCount: 0
      };
    }

    try {
      return await this.getDAO().getLogStats();
    } catch (error) {
      logger.error('获取日志统计信息失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        operation: 'get_log_stats',
        module: 'system',
        timestamp: new Date().toISOString(),
      });
      return {
        total: 0,
        byLevel: {},
        byModule: {},
        recentCount: 0
      };
    }
  }

  /**
   * 清理旧日志（保留指定天数）
   */
  async cleanupOldLogs(retentionDays: number = 30): Promise<number> {
    if (!this.isDbEnabled) {
      return 0;
    }

    try {
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000).toISOString();
      return await this.getDAO().deleteLogsBefore(cutoffDate);
    } catch (error) {
      logger.error('清理旧日志失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        cleanupParams: {
          retentionDays,
          cutoffDate: new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000).toISOString(),
          currentDate: new Date().toISOString(),
        },
        operation: 'cleanup_old_logs',
        module: 'system',
        timestamp: new Date().toISOString(),
      });
      return 0;
    }
  }

  /**
   * 解析路径确定模块类型
   */
  static determineModuleFromPath(path: string): string {
    if (path.includes('/admin/')) {
      if (path.includes('/upload') || path.includes('/delete') || path.includes('/versions') ||
          path.includes('/batch-upload') || path.includes('/chunk')) {
        return 'version_management';
      } else if (path.includes('/login') || path.includes('/logout') || path.includes('/auth')) {
        return 'user_operation';
      } else if (path.includes('/logs') || path.includes('/cleanup') || path.includes('/clear')) {
        return 'user_operation';
      } else {
        return 'user_operation';
      }
    } else if (path.includes('/download')) {
      return 'version_management';
    } else if (path.includes('/version') || path.includes('/updates') || path.includes('/check')) {
      return 'ota_function';
    } else if (path.includes('/health') || path.includes('/status')) {
      return 'ota_function';
    }
    return 'system';
  }

  /**
   * 解析路径确定操作类型
   */
  static determineOperationFromPath(path: string, method: string = 'GET'): string {
    if (path.includes('/admin/')) {
      if (path.includes('/upload') || path.includes('/delete') || path.includes('/versions') ||
          path.includes('/batch-upload') || path.includes('/chunk')) {
        return method === 'GET' ? 'version_view' : 'version_operation';
      } else if (path.includes('/login') || path.includes('/logout') || path.includes('/auth')) {
        return 'auth_operation';
      } else if (path.includes('/logs') || path.includes('/cleanup') || path.includes('/clear')) {
        return 'admin_management';
      } else {
        return 'admin_access';
      }
    } else if (path.includes('/download')) {
      return 'file_download';
    } else if (path.includes('/version') || path.includes('/updates') || path.includes('/check')) {
      return 'ota_request';
    } else if (path.includes('/health') || path.includes('/status')) {
      return 'health_check';
    }
    return 'http_request';
  }

  /**
   * 解析消息内容确定模块类型（用于标准日志）
   */
  static determineModuleFromMessage(message: string): string {
    if (message.includes('登录') || message.includes('退出') || message.includes('认证') || 
        message.includes('清空日志') || message.includes('清理') || message.includes('管理员')) {
      return 'user_operation';
    } else if (message.includes('上传') || message.includes('下载') || message.includes('删除') || 
              message.includes('版本') || message.includes('文件') || message.includes('chunk') || 
              message.includes('batch')) {
      return 'version_management';
    } else if (message.includes('OTA') || message.includes('更新') || message.includes('检查') || 
              message.includes('health') || message.includes('status')) {
      return 'ota_function';
    }
    return 'system';
  }

  /**
   * 解析消息内容确定操作类型（用于标准日志）
   */
  static determineOperationFromMessage(message: string): string {
    if (message.includes('登录') || message.includes('退出') || message.includes('认证')) {
      return 'auth_operation';
    } else if (message.includes('清空日志') || message.includes('清理') || message.includes('管理')) {
      return 'admin_management';
    } else if (message.includes('下载')) {
      return 'file_download';
    } else if (message.includes('上传') || message.includes('删除') || message.includes('版本') || 
              message.includes('文件') || message.includes('chunk') || message.includes('batch')) {
      return 'version_operation';
    } else if (message.includes('health') || message.includes('status') || message.includes('检查')) {
      return 'health_check';
    } else if (message.includes('OTA') || message.includes('更新')) {
      return 'ota_request';
    }
    return 'system_log';
  }

  /**
   * 判断是否为私有IP地址
   */
  static isPrivateIP(ip: string): boolean {
    if (!ip || ip === '-') return false;

    const privateRanges = [
      /^127\./,                    // *********/8 (localhost)
      /^10\./,                     // 10.0.0.0/8
      /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
      /^192\.168\./,               // ***********/16
      /^169\.254\./,               // ***********/16 (link-local)
    ];

    return privateRanges.some(range => range.test(ip));
  }

  /**
   * 创建日志记录数据
   */
  static createLogData(
    level: string,
    message: string,
    options: {
      module?: string;
      operation?: string;
      clientIP?: string;
      userAgent?: string;
      path?: string;
      method?: string;
      statusCode?: number;
      meta?: any;
    } = {}
  ): CreateSystemLogData {
    const timestamp = new Date().toISOString();
    
    // 自动确定模块和操作类型
    let module = options.module;
    let operation = options.operation;
    
    if (!module && options.path) {
      module = this.determineModuleFromPath(options.path);
    }
    
    if (!module) {
      module = this.determineModuleFromMessage(message);
    }
    
    if (!operation && options.path) {
      operation = this.determineOperationFromPath(options.path, options.method);
    }
    
    if (!operation) {
      operation = this.determineOperationFromMessage(message);
    }

    return {
      timestamp,
      level: level.toLowerCase(),
      message,
      module: module || 'system',
      operation,
      client_ip: options.clientIP,
      is_private_ip: options.clientIP ? this.isPrivateIP(options.clientIP) : undefined,
      user_agent: options.userAgent,
      path: options.path,
      method: options.method,
      status_code: options.statusCode,
      meta: options.meta
    };
  }
}

// 延迟创建单例实例
let systemLogServiceInstance: SystemLogService | null = null;

export function getSystemLogService(): SystemLogService {
  if (!systemLogServiceInstance) {
    systemLogServiceInstance = new SystemLogService();
  }
  return systemLogServiceInstance;
}
