<template>
  <div
    id="add-event-menu"
    class="add-event-menu"
    v-if="visible"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="menu-title">
      <span>{{ t('editor.contextMenu.addEvent') }} ({{ timePosition }}ms)</span>
      <span class="available-space">{{ availableSpaceText }}</span>
    </div>

    <button
      class="menu-button"
      @click="handleAddTransientEvent"
      :disabled="!canAddTransient"
      :class="{ 'button-disabled': !canAddTransient }"
    >
      <span
        class="button-icon transient-icon"
        :class="{ 'icon-disabled': !canAddTransient }"
      ></span>
      <div class="button-content">
        <div class="button-title">{{ t('editor.contextMenu.transientEvent') }}</div>
        <div class="button-desc" v-if="!canAddTransient">
          {{ t('editor.contextMenu.needSpace', { space: minTransientSpace }) }}
        </div>
      </div>
    </button>

    <button
      class="menu-button"
      @click="handleAddContinuousEvent"
      :disabled="!canAddContinuous"
      :class="{ 'button-disabled': !canAddContinuous }"
    >
      <span
        class="button-icon continuous-icon"
        :class="{ 'icon-disabled': !canAddContinuous }"
      ></span>
      <div class="button-content">
        <div class="button-title">{{ t('editor.contextMenu.continuousEvent') }}</div>
        <div class="button-desc" v-if="!canAddContinuous">
          {{ t('editor.contextMenu.needSpace', { space: minContinuousSpace }) }}
        </div>
      </div>
    </button>

    <!-- 添加删除按钮，仅当右键点击在事件上时显示 -->
    <button
      class="menu-button delete-button"
      v-if="contextMenuEventId !== null"
      @click="handleDeleteEvent"
    >
      <span class="button-icon delete-icon"></span>
      <div class="button-content">
        <div class="button-title">{{ t('editor.contextMenu.deleteEvent') }}</div>
      </div>
    </button>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import { useI18n } from '@/composables/useI18n';

const props = defineProps<{
  visible: boolean;
  position: { x: number; y: number };
  timePosition: number;
  availableSpaceText: string;
  canAddTransient: boolean;
  canAddContinuous: boolean;
  contextMenuEventId: string | null;
  minTransientSpace: number;
  minContinuousSpace: number;
}>();

const emit = defineEmits<{
  (e: 'add-transient'): void;
  (e: 'add-continuous'): void;
  (e: 'delete-event'): void;
  (e: 'close'): void;
  (e: 'mouse-enter'): void;
  (e: 'mouse-leave'): void;
}>();

// 国际化
const { t } = useI18n();

// 处理鼠标事件
const handleMouseEnter = () => {
  emit('mouse-enter');
};

const handleMouseLeave = () => {
  emit('mouse-leave');
};

// 处理添加事件
const handleAddTransientEvent = () => {
  if (props.canAddTransient) {
    emit('add-transient');
  }
};

const handleAddContinuousEvent = () => {
  if (props.canAddContinuous) {
    emit('add-continuous');
  }
};

// 处理删除事件
const handleDeleteEvent = () => {
  emit('delete-event');
};
</script>

<style scoped>
.add-event-menu {
  position: fixed;
  z-index: 1000;
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  min-width: 180px;
  transition: opacity 0.2s, transform 0.2s;
  animation: menu-appear 0.2s ease-out;
}

@keyframes menu-appear {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.menu-title {
  font-size: 12px;
  color: #bbb;
  margin-bottom: 8px;
  padding-bottom: 0;
  border-bottom: none;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.available-space {
  color: #48a1e6;
  font-size: 11px;
  display: block;
  margin-top: 4px;
  border-bottom: 1px dotted #444;
  padding-top: 4px;
  padding-bottom: 8px;
  width: 100%;
}

.menu-button {
  display: flex;
  align-items: center;
  background-color: transparent;
  border: none;
  color: #e0e0e0;
  padding: 8px;
  margin-top: 4px;
  text-align: left;
  cursor: pointer;
  border-radius: 3px;
  transition: background-color 0.15s, transform 0.15s;
}

.menu-button:hover:not(.button-disabled) {
  background-color: #3a3a3a;
  transform: translateX(2px);
}

.menu-button:active:not(.button-disabled) {
  transform: translateX(4px);
}

.button-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  display: inline-block;
}

.transient-icon {
  background-color: rgba(255, 140, 0, 0.8);
  clip-path: polygon(0% 100%, 50% 0%, 100% 100%);
}

.continuous-icon {
  background-color: rgba(48, 158, 155, 0.8);
  clip-path: polygon(0% 100%, 25% 30%, 50% 70%, 75% 20%, 100% 100%);
}

.button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  background-color: transparent !important;
}

.icon-disabled {
  opacity: 0.3;
}

.button-content {
  display: flex;
  flex-direction: column;
}

.button-title {
  font-size: 12px;
  color: inherit;
}

.button-desc {
  font-size: 10px;
  color: #ff9966;
  margin-top: 2px;
}

/* 删除按钮样式 */
.delete-button {
  margin-top: 8px;
  border-top: 1px dotted #444;
  padding-top: 10px;
}

.delete-icon {
  background-color: rgba(255, 80, 80, 0.8);
  clip-path: polygon(
    20% 0%,
    0% 20%,
    30% 50%,
    0% 80%,
    20% 100%,
    50% 70%,
    80% 100%,
    100% 80%,
    70% 50%,
    100% 20%,
    80% 0%,
    50% 30%
  );
}
</style>
