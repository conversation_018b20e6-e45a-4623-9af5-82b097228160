#!/usr/bin/env node

// RealityTap OTA 服务器和 Admin UI 同时启动脚本 (Windows 兼容版本)
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`)
};

// 存储进程对象
let otaServerProcess = null;
let adminUIProcess = null;

// 清理函数
function cleanup() {
  log.info('正在停止服务...');
  
  if (otaServerProcess) {
    log.info(`停止 OTA 服务器 (PID: ${otaServerProcess.pid})`);
    otaServerProcess.kill('SIGTERM');
  }
  
  if (adminUIProcess) {
    log.info(`停止 Admin UI (PID: ${adminUIProcess.pid})`);
    adminUIProcess.kill('SIGTERM');
  }
  
  log.success('所有服务已停止');
  process.exit(0);
}

// 设置信号处理
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

// 检查Node.js版本
function checkNode() {
  const nodeVersion = process.version.slice(1).split('.')[0];
  if (parseInt(nodeVersion) < 18) {
    log.error(`Node.js 版本过低，需要 18 或更高版本，当前版本: ${process.version}`);
    process.exit(1);
  }
  log.success(`Node.js 版本检查通过: ${process.version}`);
}

// 检查依赖
function checkDependencies() {
  log.info('检查项目依赖...');
  
  // 检查主项目依赖
  if (!fs.existsSync('node_modules')) {
    log.warning('主项目依赖未安装，正在安装...');
    const npmInstall = spawn('npm', ['install'], { stdio: 'inherit', shell: true });
    return new Promise((resolve, reject) => {
      npmInstall.on('close', (code) => {
        if (code === 0) {
          log.success('主项目依赖安装完成');
          resolve();
        } else {
          log.error('主项目依赖安装失败');
          reject(new Error('npm install failed'));
        }
      });
    });
  } else {
    log.success('主项目依赖已安装');
    return Promise.resolve();
  }
}

// 检查 Admin UI 依赖
function checkAdminUIDependencies() {
  log.info('检查 Admin UI 依赖...');
  
  if (!fs.existsSync('admin-ui/node_modules')) {
    log.warning('Admin UI 依赖未安装，正在安装...');
    const npmInstall = spawn('npm', ['install'], { 
      stdio: 'inherit', 
      shell: true, 
      cwd: path.join(__dirname, 'admin-ui') 
    });
    return new Promise((resolve, reject) => {
      npmInstall.on('close', (code) => {
        if (code === 0) {
          log.success('Admin UI 依赖安装完成');
          resolve();
        } else {
          log.error('Admin UI 依赖安装失败');
          reject(new Error('npm install failed for admin-ui'));
        }
      });
    });
  } else {
    log.success('Admin UI 依赖已安装');
    return Promise.resolve();
  }
}

// 初始化存储结构
function initStorage() {
  log.info('检查存储结构...');
  
  if (!fs.existsSync('storage')) {
    log.warning('存储结构未初始化，正在初始化...');
    const initScript = spawn('npm', ['run', 'init-storage'], { stdio: 'inherit', shell: true });
    return new Promise((resolve, reject) => {
      initScript.on('close', (code) => {
        if (code === 0) {
          log.success('存储结构初始化完成');
          resolve();
        } else {
          log.error('存储结构初始化失败');
          reject(new Error('init-storage failed'));
        }
      });
    });
  } else {
    log.success('存储结构已存在');
    return Promise.resolve();
  }
}

// 检查环境变量
function checkEnv() {
  log.info('检查环境配置...');
  
  if (!fs.existsSync('.env')) {
    if (fs.existsSync('.env.example')) {
      log.warning('环境配置文件不存在，从示例文件创建...');
      fs.copyFileSync('.env.example', '.env');
      log.success('已创建 .env 文件，请根据需要修改配置');
    } else {
      log.warning('未找到环境配置文件');
    }
  } else {
    log.success('环境配置文件已存在');
  }
}

// 启动 OTA 服务器
function startOTAServer() {
  return new Promise((resolve, reject) => {
    log.info('启动 OTA 服务器 (端口: 3000)...');
    
    // 设置环境变量
    const env = {
      ...process.env,
      NODE_ENV: 'development',
      LOG_LEVEL: 'debug',
      PORT: '3000'
    };
    
    // 启动 OTA 服务器
    otaServerProcess = spawn('npm', ['run', 'dev'], {
      env,
      shell: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });
    
    // 创建日志文件流
    const otaLogStream = fs.createWriteStream('ota-server.log');
    otaServerProcess.stdout.pipe(otaLogStream);
    otaServerProcess.stderr.pipe(otaLogStream);
    
    log.success(`OTA 服务器已启动 (PID: ${otaServerProcess.pid})`);
    
    // 等待服务器启动
    setTimeout(() => {
      if (otaServerProcess && !otaServerProcess.killed) {
        log.success('OTA 服务器启动成功: http://localhost:3000');
        resolve();
      } else {
        log.error('OTA 服务器启动失败');
        reject(new Error('OTA server failed to start'));
      }
    }, 3000);
    
    otaServerProcess.on('error', (err) => {
      log.error(`OTA 服务器启动错误: ${err.message}`);
      reject(err);
    });
  });
}

// 启动 Admin UI
function startAdminUI() {
  return new Promise((resolve, reject) => {
    log.info('启动 Admin UI (端口: 3100)...');
    
    // 启动 Admin UI
    adminUIProcess = spawn('npm', ['run', 'dev'], {
      shell: true,
      cwd: path.join(__dirname, 'admin-ui'),
      stdio: ['ignore', 'pipe', 'pipe']
    });
    
    // 创建日志文件流
    const adminUILogStream = fs.createWriteStream('admin-ui.log');
    adminUIProcess.stdout.pipe(adminUILogStream);
    adminUIProcess.stderr.pipe(adminUILogStream);
    
    log.success(`Admin UI 已启动 (PID: ${adminUIProcess.pid})`);
    
    // 等待 Admin UI 启动
    setTimeout(() => {
      if (adminUIProcess && !adminUIProcess.killed) {
        log.success('Admin UI 启动成功: http://localhost:3100');
        resolve();
      } else {
        log.error('Admin UI 启动失败');
        reject(new Error('Admin UI failed to start'));
      }
    }, 3000);
    
    adminUIProcess.on('error', (err) => {
      log.error(`Admin UI 启动错误: ${err.message}`);
      reject(err);
    });
  });
}

// 主函数
async function main() {
  console.log('');
  log.info('🚀 RealityTap OTA 服务器和 Admin UI 启动脚本');
  console.log('');
  
  // 检查当前目录
  if (!fs.existsSync('package.json')) {
    log.error('请在 realitytap-ota-server 目录中运行此脚本');
    process.exit(1);
  }
  
  try {
    // 执行检查和初始化
    checkNode();
    await checkDependencies();
    await checkAdminUIDependencies();
    checkEnv();
    await initStorage();
    
    console.log('');
    log.success('所有检查完成，准备启动服务...');
    console.log('');
    
    // 启动服务
    await startOTAServer();
    await startAdminUI();
    
    console.log('');
    log.success('🎉 所有服务启动完成！');
    log.info('📊 OTA 服务器: http://localhost:3000');
    log.info('🎛️  Admin UI: http://localhost:3100');
    log.info('📝 日志文件: ota-server.log, admin-ui.log');
    console.log('');
    log.info('按 Ctrl+C 停止所有服务');
    
    // 保持进程运行
    process.stdin.resume();
    
  } catch (error) {
    log.error(`启动失败: ${error.message}`);
    cleanup();
    process.exit(1);
  }
}

// 运行主函数
main().catch((error) => {
  log.error(`未处理的错误: ${error.message}`);
  cleanup();
  process.exit(1);
});
