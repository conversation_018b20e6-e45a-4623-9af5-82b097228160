// OTA Configuration commands
use crate::commands::app_data;
use crate::error::Result;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use tauri;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OTAConfig {
    #[serde(rename = "serverBaseURL")]
    pub server_base_url: String,
    pub channel: String,
    #[serde(rename = "checkInterval")]
    pub check_interval: u64,
    #[serde(rename = "autoDownload")]
    pub auto_download: bool,
    #[serde(rename = "autoInstall")]
    pub auto_install: bool,
    #[serde(rename = "enableNotifications")]
    pub enable_notifications: bool,
    #[serde(rename = "enableBackgroundCheck")]
    pub enable_background_check: bool,
    #[serde(rename = "maxRetries")]
    pub max_retries: u32,
    pub timeout: u64,
    #[serde(rename = "lastSaved")]
    pub last_saved: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OTAServerInfo {
    pub name: String,
    pub url: String,
    pub description: String,
    #[serde(rename = "isDefault")]
    pub is_default: bool,
    #[serde(rename = "isCustom")]
    pub is_custom: bool,
}

impl Default for OTAConfig {
    fn default() -> Self {
        Self {
            server_base_url: "http://localhost:3000".to_string(),
            channel: "stable".to_string(),
            check_interval: 24 * 60 * 60 * 1000, // 24小时
            auto_download: false,
            auto_install: false,
            enable_notifications: true,
            enable_background_check: true,
            max_retries: 3,
            timeout: 30000,
            last_saved: chrono::Utc::now().to_rfc3339(),
        }
    }
}

/// 获取配置文件路径
fn get_config_dir() -> Result<PathBuf> {
    // 使用统一的应用数据目录
    let app_data_dir = app_data::get_app_data_dir()?;
    let config_dir = app_data_dir.join("config");

    // 确保目录存在
    if !config_dir.exists() {
        fs::create_dir_all(&config_dir)
            .map_err(|e| crate::error::Error::Io(format!("创建配置目录失败: {}", e)))?;
    }

    Ok(config_dir)
}

/// 获取OTA配置文件路径
fn get_ota_config_path() -> Result<PathBuf> {
    Ok(get_config_dir()?.join("ota_config.json"))
}

/// 获取自定义服务器配置文件路径
fn get_custom_servers_path() -> Result<PathBuf> {
    Ok(get_config_dir()?.join("custom_ota_servers.json"))
}

#[tauri::command]
pub async fn get_ota_config() -> Result<Option<OTAConfig>> {
    let config_path = get_ota_config_path()?;
    
    if !config_path.exists() {
        log::info!("OTA配置文件不存在，返回默认配置");
        return Ok(Some(OTAConfig::default()));
    }
    
    match fs::read_to_string(&config_path) {
        Ok(content) => {
            match serde_json::from_str::<OTAConfig>(&content) {
                Ok(config) => {
                    log::info!("成功加载OTA配置");
                    Ok(Some(config))
                }
                Err(e) => {
                    log::error!("解析OTA配置文件失败: {}", e);
                    // 返回默认配置而不是错误
                    Ok(Some(OTAConfig::default()))
                }
            }
        }
        Err(e) => {
            log::error!("读取OTA配置文件失败: {}", e);
            Ok(Some(OTAConfig::default()))
        }
    }
}

#[tauri::command]
pub async fn save_ota_config(config: OTAConfig) -> Result<()> {
    let config_path = get_ota_config_path()?;
    
    let json_content = serde_json::to_string_pretty(&config)
        .map_err(|e| crate::error::Error::Json(format!("序列化OTA配置失败: {}", e)))?;

    fs::write(&config_path, json_content)
        .map_err(|e| crate::error::Error::Io(format!("保存OTA配置失败: {}", e)))?;
    
    log::info!("OTA配置已保存到: {:?}", config_path);
    Ok(())
}

#[tauri::command]
pub async fn get_custom_ota_servers() -> Result<Vec<OTAServerInfo>> {
    let servers_path = get_custom_servers_path()?;
    
    if !servers_path.exists() {
        log::info!("自定义服务器配置文件不存在，返回空列表");
        return Ok(vec![]);
    }
    
    match fs::read_to_string(&servers_path) {
        Ok(content) => {
            match serde_json::from_str::<Vec<OTAServerInfo>>(&content) {
                Ok(servers) => {
                    log::info!("成功加载{}个自定义OTA服务器", servers.len());
                    Ok(servers)
                }
                Err(e) => {
                    log::error!("解析自定义服务器配置文件失败: {}", e);
                    Ok(vec![])
                }
            }
        }
        Err(e) => {
            log::error!("读取自定义服务器配置文件失败: {}", e);
            Ok(vec![])
        }
    }
}

#[tauri::command]
pub async fn save_custom_ota_servers(servers: Vec<OTAServerInfo>) -> Result<()> {
    let servers_path = get_custom_servers_path()?;
    
    let json_content = serde_json::to_string_pretty(&servers)
        .map_err(|e| crate::error::Error::Json(format!("序列化自定义服务器配置失败: {}", e)))?;

    fs::write(&servers_path, json_content)
        .map_err(|e| crate::error::Error::Io(format!("保存自定义服务器配置失败: {}", e)))?;
    
    log::info!("自定义OTA服务器配置已保存到: {:?}", servers_path);
    Ok(())
}

#[tauri::command]
pub async fn test_ota_server_connection(server_url: String) -> Result<bool> {
    log::info!("测试OTA服务器连接: {}", server_url);

    // 构建健康检查URL
    let health_url = if server_url.ends_with('/') {
        format!("{}health", server_url)
    } else {
        format!("{}/health", server_url)
    };

    // 使用reqwest进行HTTP请求测试
    match reqwest::Client::new()
        .get(&health_url)
        .timeout(std::time::Duration::from_secs(10))
        .send()
        .await
    {
        Ok(response) => {
            let is_success = response.status().is_success();
            log::info!("服务器连接测试结果: {} - {}", health_url, is_success);
            Ok(is_success)
        }
        Err(e) => {
            log::warn!("服务器连接测试失败: {} - {}", health_url, e);
            Ok(false)
        }
    }
}

#[tauri::command]
pub async fn log_update_server_info(server_url: String, action: String) -> Result<()> {
    log::info!("🔍 [UPDATE CHECK] {} - 使用服务器: {}", action, server_url);
    Ok(())
}

#[tauri::command]
pub async fn log_ota_operation(operation: String, message: String, details: Option<serde_json::Value>) -> Result<()> {
    if let Some(details_value) = details {
        log::info!("📥 [{}] {} - 详情: {}", operation, message, details_value);
    } else {
        log::info!("📥 [{}] {}", operation, message);
    }
    Ok(())
}
