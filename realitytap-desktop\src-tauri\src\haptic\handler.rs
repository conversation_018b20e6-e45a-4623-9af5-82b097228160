// RealityTap 简化触觉输出处理器实现
//
// 该模块提供了简化版的触觉输出处理器，用于替代复杂的消息系统架构。
// 主要特点：
// - 直接发送 Tauri 事件，无需复杂的消息处理链路
// - 保持与前端的接口兼容性
// - 大幅减少代码复杂度（约 90%）
// - 支持基本的高频数据节流

use std::os::raw::{c_char, c_void};
use std::sync::{Arc, atomic::{AtomicBool, Ordering}, Mutex, Condvar, OnceLock};
use std::time::{Instant, Duration};
use std::panic::{catch_unwind, AssertUnwindSafe};
use std::collections::HashSet;
use std::pin::Pin;
use std::fs::File;
use std::io::{Write, BufWriter};
use parking_lot::RwLock;
use once_cell::sync::Lazy;
use tauri::{AppHandle, Emitter};
use serde_json::json;

use crate::haptic::ffi::{HapticOutputHandlerObject, HapticOutputHandlerVTable, WaveformProcessingStatus, SamplingRateType};

/// 全局错误状态，用于跨 FFI 边界的错误传播
static GLOBAL_ERROR_STATE: AtomicBool = AtomicBool::new(false);

/// 全局有效 handler 指针跟踪，用于防止访问已释放的内存
static VALID_HANDLERS: Lazy<Arc<Mutex<HashSet<usize>>>> =
    Lazy::new(|| Arc::new(Mutex::new(HashSet::new())));

/// 待注销的 handler 指针集合，用于延迟注销机制
static PENDING_UNREGISTER_HANDLERS: Lazy<Arc<Mutex<HashSet<usize>>>> =
    Lazy::new(|| Arc::new(Mutex::new(HashSet::new())));

/// 全局回调完成状态
static CALLBACK_COMPLETED: AtomicBool = AtomicBool::new(false);

/// 全局等待回调状态
static WAITING_FOR_CALLBACK: AtomicBool = AtomicBool::new(false);

/// 数据块处理状态跟踪
#[derive(Debug, Clone)]
struct ChunkProcessingState {
    /// 当前处理的样本数
    current_sample_count: i32,
    /// 处理开始时间
    processing_start_time: Instant,
    /// 是否正在处理
    is_processing: bool,
    /// 处理完成标志
    is_completed: bool,
}

impl Default for ChunkProcessingState {
    fn default() -> Self {
        Self {
            current_sample_count: 0,
            processing_start_time: Instant::now(),
            is_processing: false,
            is_completed: false,
        }
    }
}

/// 简化的处理器状态
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum SimpleHandlerState {
    Idle,
    Active,
    Stopped,
}

/// 输出模式枚举
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum OutputMode {
    /// 事件模式：发送 Tauri 事件到前端
    Event,
    /// 文件模式：保存数据到文件
    File,
}

/// 简化的处理器配置
#[derive(Debug, Clone)]
pub struct SimpleHandlerConfig {
    pub device_id: u32,
    pub enable_logging: bool,
    pub enable_events: bool,
    pub sample_throttle_ms: u64, // 高频数据节流间隔（毫秒）
    pub sampling_rate: SamplingRateType, // 采样率类型
    pub output_mode: OutputMode, // 输出模式：事件或文件
    pub file_path: Option<String>, // 文件保存路径（仅在 File 模式下使用）
}

impl Default for SimpleHandlerConfig {
    fn default() -> Self {
        Self {
            device_id: 0,
            enable_logging: true,
            enable_events: true,
            sample_throttle_ms: 10, // 默认 10ms 节流，即最多 100Hz
            sampling_rate: SamplingRateType::Sampling24Khz, // 默认 24KHz 采样率
            output_mode: OutputMode::Event, // 默认事件模式
            file_path: None, // 默认无文件路径
        }
    }
}

/// 简化的触觉输出处理器
pub struct SimpleHapticHandler {
    /// 配置
    config: SimpleHandlerConfig,
    /// 当前状态
    state: Arc<RwLock<SimpleHandlerState>>,
    /// 样本计数
    sample_count: Arc<RwLock<u64>>,
    /// 当前振幅
    amplitude: Arc<RwLock<u32>>,
    /// Tauri 应用句柄
    app_handle: Option<AppHandle>,
    /// 上次样本发送时间（用于节流）
    last_sample_time: Arc<Mutex<Instant>>,
    /// 数据块处理状态
    chunk_processing_state: Arc<Mutex<ChunkProcessingState>>,
    /// 处理完成条件变量
    processing_condvar: Arc<Condvar>,
    /// 文件写入器（仅在 File 模式下使用）
    file_writer: Arc<Mutex<Option<BufWriter<File>>>>,
    /// 是否为临时 Handler（用于自动清理）
    is_temporary: bool,
    /// 保存的原始配置（用于操作完成后恢复）
    saved_config: Arc<Mutex<Option<SimpleHandlerConfig>>>,
}

impl SimpleHapticHandler {
    /// 创建新的简化处理器
    pub fn new(config: SimpleHandlerConfig, app_handle: Option<AppHandle>) -> Self {
        Self::new_with_temporary_flag(config, app_handle, false)
    }

    /// 创建新的简化处理器（可指定是否为临时 Handler）
    pub fn new_with_temporary_flag(config: SimpleHandlerConfig, app_handle: Option<AppHandle>, is_temporary: bool) -> Self {
        Self {
            config,
            state: Arc::new(RwLock::new(SimpleHandlerState::Idle)),
            sample_count: Arc::new(RwLock::new(0)),
            amplitude: Arc::new(RwLock::new(255)),
            app_handle,
            last_sample_time: Arc::new(Mutex::new(Instant::now())),
            chunk_processing_state: Arc::new(Mutex::new(ChunkProcessingState::default())),
            processing_condvar: Arc::new(Condvar::new()),
            file_writer: Arc::new(Mutex::new(None)),
            is_temporary,
            saved_config: Arc::new(Mutex::new(None)),
        }
    }

    /// 将采样率类型转换为实际的 Hz 值
    fn sampling_rate_to_hz(&self) -> u32 {
        match self.config.sampling_rate {
            SamplingRateType::Sampling6Khz => 6000,
            SamplingRateType::Sampling8Khz => 8000,
            SamplingRateType::Sampling12Khz => 12000,
            SamplingRateType::Sampling24Khz => 24000,
        }
    }

    /// 计算等待时间（用于测试和调试）
    pub fn calculate_wait_time_ms(&self, sample_count: i32) -> f64 {
        let sampling_rate_hz = self.sampling_rate_to_hz();
        (sample_count as f64 / sampling_rate_hz as f64) * 1000.0
    }

    /// 设置 Tauri 应用句柄
    pub fn set_app_handle(&mut self, app_handle: AppHandle) {
        self.app_handle = Some(app_handle);
    }

    /// 获取 C 兼容对象指针
    pub fn get_c_object_mut(&mut self) -> *mut HapticOutputHandlerObject {
        // 创建临时的 C 对象
        let vtable = Self::create_vtable();
        let mut c_object = HapticOutputHandlerObject {
            vtable: &vtable as *const HapticOutputHandlerVTable,
            data: self as *mut Self as *mut c_void,
        };

        // 注意：这里返回的指针需要调用者负责生命周期管理
        // 在实际使用中，应该将 c_object 存储在调用者的作用域中
        &mut c_object as *mut HapticOutputHandlerObject
    }

    /// 注册处理器（用于内存安全跟踪）
    pub fn register(&self) {
        let handler_ptr = self as *const Self as *mut Self;
        register_handler(handler_ptr);
    }

    /// 注销处理器
    pub fn unregister(&self) {
        let handler_ptr = self as *const Self as *mut Self;
        unregister_handler(handler_ptr);
    }

    /// 获取当前状态
    pub fn get_state(&self) -> SimpleHandlerState {
        *self.state.read()
    }

    /// 设置状态
    fn set_state(&self, new_state: SimpleHandlerState) {
        *self.state.write() = new_state;
    }

    /// 重置样本计数
    fn reset_sample_count(&self) {
        *self.sample_count.write() = 0;
    }

    /// 获取当前时间戳
    fn current_timestamp() -> u64 {
        std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64
    }

    /// 发送事件到前端（保持与现有前端接口兼容）
    fn emit_event(&self, event_type: &str, event_subtype: &str, data: serde_json::Value) {
        if !self.config.enable_events {
            return;
        }

        if let Some(app_handle) = &self.app_handle {
            // 构造与现有前端兼容的事件格式
            let event_data = json!({
                "type": event_subtype,
                "device_id": self.config.device_id,
                "timestamp": Self::current_timestamp(),
                "data": data,
                "metadata": {
                    "source": "simple_handler",
                    "is_aggregated": false,
                    "priority": if event_type == "lifecycle" { 2 } else { 0 }
                }
            });

            // 发送到对应的事件通道，保持与前端监听器兼容
            let event_name = match event_type {
                "lifecycle" => "haptic:lifecycle",
                "data" => "haptic:data", 
                "error" => "haptic:error",
                "status" => "haptic:status",
                _ => "haptic:event"
            };

            if let Err(e) = app_handle.emit(event_name, &event_data) {
                if self.config.enable_logging {
                    log::error!("发送事件失败 {}: {}", event_name, e);
                }
            } else if self.config.enable_logging {
                log::debug!("发送事件: {} -> {}", event_name, event_subtype);
            }
        }
    }

    /// 创建 C 兼容的虚函数表
    fn create_vtable() -> HapticOutputHandlerVTable {
        HapticOutputHandlerVTable {
            on_haptic_output_start: Self::on_haptic_output_start_c,
            on_haptic_output_complete: Self::on_haptic_output_complete_c,
            on_haptic_output_stop: Self::on_haptic_output_stop_c,
            on_waveform_chunk_start: Self::on_waveform_chunk_start_c,
            process_waveform_sample: Self::process_waveform_sample_c,
            wait_chunk_processing_complete: Self::wait_chunk_processing_complete_c,
            set_output_amplitude: Self::set_output_amplitude_c,
        }
    }

    // ===== C 回调函数实现 =====

    extern "C" fn on_haptic_output_start_c(data: *mut c_void) {
        let result = catch_unwind(AssertUnwindSafe(|| {
            if data.is_null() {
                log::error!("FFI 调用 on_haptic_output_start_c 传入空指针");
                return;
            }

            // 修复：data是c_object的指针，需要提取c_object.data
            let c_object_ptr = data as *mut HapticOutputHandlerObject;
            let handler_ptr = unsafe {
                let c_object = &*c_object_ptr;
                c_object.data as *mut Self
            };

            if !is_handler_valid(handler_ptr) {
                log::warn!("尝试回调已释放的 handler (on_haptic_output_start_c)，忽略回调");
                return;
            }

            if let Some(handler) = unsafe { handler_ptr.as_mut() } {
                handler.on_haptic_output_start();
            }
        }));

        if let Err(panic_info) = result {
            log::error!("FFI 回调 on_haptic_output_start_c 中发生 panic: {:?}", panic_info);
            GLOBAL_ERROR_STATE.store(true, Ordering::SeqCst);
        }
    }

    extern "C" fn on_haptic_output_complete_c(data: *mut c_void) {
        let result = catch_unwind(AssertUnwindSafe(|| {
            if data.is_null() {
                log::error!("FFI 调用 on_haptic_output_complete_c 传入空指针");
                return;
            }

            // 修复：data是c_object的指针，需要提取c_object.data
            let c_object_ptr = data as *mut HapticOutputHandlerObject;
            let handler_ptr = unsafe {
                let c_object = &*c_object_ptr;
                c_object.data as *mut Self
            };

            if !is_handler_valid(handler_ptr) {
                log::warn!("尝试回调已释放的 handler (on_haptic_output_complete_c)，忽略回调");
                return;
            }

            if let Some(handler) = unsafe { handler_ptr.as_mut() } {
                handler.on_haptic_output_complete();
            }
        }));

        if let Err(panic_info) = result {
            log::error!("FFI 回调 on_haptic_output_complete_c 中发生 panic: {:?}", panic_info);
            GLOBAL_ERROR_STATE.store(true, Ordering::SeqCst);
        }
    }

    extern "C" fn on_haptic_output_stop_c(data: *mut c_void) {
        let result = catch_unwind(AssertUnwindSafe(|| {
            if data.is_null() {
                log::error!("FFI 调用 on_haptic_output_stop_c 传入空指针");
                return;
            }

            // 修复：data是c_object的指针，需要提取c_object.data
            let c_object_ptr = data as *mut HapticOutputHandlerObject;
            let handler_ptr = unsafe {
                let c_object = &*c_object_ptr;
                c_object.data as *mut Self
            };

            if !is_handler_valid(handler_ptr) {
                log::warn!("尝试回调已释放的 handler (on_haptic_output_stop_c)，忽略回调");
                return;
            }

            if let Some(handler) = unsafe { handler_ptr.as_mut() } {
                handler.on_haptic_output_stop();
            }
        }));

        if let Err(panic_info) = result {
            log::error!("FFI 回调 on_haptic_output_stop_c 中发生 panic: {:?}", panic_info);
            GLOBAL_ERROR_STATE.store(true, Ordering::SeqCst);
        }
    }

    extern "C" fn on_waveform_chunk_start_c(data: *mut c_void) {
        let result = catch_unwind(AssertUnwindSafe(|| {
            if data.is_null() {
                log::error!("FFI 调用 on_waveform_chunk_start_c 传入空指针");
                return;
            }

            // 修复：data是c_object的指针，需要提取c_object.data
            let c_object_ptr = data as *mut HapticOutputHandlerObject;
            let handler_ptr = unsafe {
                let c_object = &*c_object_ptr;
                c_object.data as *mut Self
            };

            if !is_handler_valid(handler_ptr) {
                log::warn!("尝试回调已释放的 handler (on_waveform_chunk_start_c)，忽略回调");
                return;
            }

            if let Some(handler) = unsafe { handler_ptr.as_mut() } {
                handler.on_waveform_chunk_start();
            }
        }));

        if let Err(panic_info) = result {
            log::error!("FFI 回调 on_waveform_chunk_start_c 中发生 panic: {:?}", panic_info);
            GLOBAL_ERROR_STATE.store(true, Ordering::SeqCst);
        }
    }

    extern "C" fn process_waveform_sample_c(data: *mut c_void, sample: c_char) {
        let result = catch_unwind(AssertUnwindSafe(|| {
            if data.is_null() {
                log::error!("FFI 调用 process_waveform_sample_c 传入空指针");
                return;
            }

            // 修复：data是c_object的指针，需要提取c_object.data
            let c_object_ptr = data as *mut HapticOutputHandlerObject;
            let handler_ptr = unsafe {
                let c_object = &*c_object_ptr;
                c_object.data as *mut Self
            };

            if !is_handler_valid(handler_ptr) {
                // 对于高频回调，不记录警告日志以避免性能影响
                return;
            }

            if let Some(handler) = unsafe { handler_ptr.as_mut() } {
                handler.process_waveform_sample(sample);
            }
        }));

        if let Err(panic_info) = result {
            log::error!("FFI 回调 process_waveform_sample_c 中发生 panic: {:?}", panic_info);
            GLOBAL_ERROR_STATE.store(true, Ordering::SeqCst);
        }
    }

    extern "C" fn wait_chunk_processing_complete_c(data: *mut c_void, sample_count: i32) -> i32 {
        let result = catch_unwind(AssertUnwindSafe(|| {
            if data.is_null() {
                log::error!("FFI 调用 wait_chunk_processing_complete_c 传入空指针");
                return WaveformProcessingStatus::ProcessingError as i32;
            }

            // 修复：data是c_object的指针，需要提取c_object.data
            let c_object_ptr = data as *mut HapticOutputHandlerObject;
            let handler_ptr = unsafe {
                let c_object = &*c_object_ptr;
                c_object.data as *mut Self
            };

            if !is_handler_valid(handler_ptr) {
                log::warn!("尝试回调已释放的 handler (wait_chunk_processing_complete_c)，忽略回调");
                return WaveformProcessingStatus::ProcessingError as i32;
            }

            if let Some(handler) = unsafe { handler_ptr.as_mut() } {
                handler.wait_chunk_processing_complete(sample_count) as i32
            } else {
                WaveformProcessingStatus::ProcessingError as i32
            }
        }));

        match result {
            Ok(status) => status,
            Err(panic_info) => {
                log::error!("FFI 回调 wait_chunk_processing_complete_c 中发生 panic: {:?}", panic_info);
                GLOBAL_ERROR_STATE.store(true, Ordering::SeqCst);
                WaveformProcessingStatus::ProcessingError as i32
            }
        }
    }

    extern "C" fn set_output_amplitude_c(data: *mut c_void, amplitude: u32) {
        let result = catch_unwind(AssertUnwindSafe(|| {
            if data.is_null() {
                log::error!("FFI 调用 set_output_amplitude_c 传入空指针");
                return;
            }

            // 修复：data是c_object的指针，需要提取c_object.data
            let c_object_ptr = data as *mut HapticOutputHandlerObject;
            let handler_ptr = unsafe {
                let c_object = &*c_object_ptr;
                c_object.data as *mut Self
            };

            if !is_handler_valid(handler_ptr) {
                log::warn!("尝试回调已释放的 handler (set_output_amplitude_c)，忽略回调");
                return;
            }

            if let Some(handler) = unsafe { handler_ptr.as_mut() } {
                handler.set_output_amplitude(amplitude);
            }
        }));

        if let Err(panic_info) = result {
            log::error!("FFI 回调 set_output_amplitude_c 中发生 panic: {:?}", panic_info);
            GLOBAL_ERROR_STATE.store(true, Ordering::SeqCst);
        }
    }
}

// ===== 内存安全管理函数 =====

/// 注册有效的 handler 指针
pub fn register_handler(handler_ptr: *mut SimpleHapticHandler) {
    let handler_addr = handler_ptr as usize;
    if let Ok(mut valid_handlers) = VALID_HANDLERS.lock() {
        valid_handlers.insert(handler_addr);
        log::debug!("注册简化 handler 指针: {:p}", handler_ptr);
    }

    // 同时从待注销列表中移除（如果存在）
    if let Ok(mut pending_handlers) = PENDING_UNREGISTER_HANDLERS.lock() {
        pending_handlers.remove(&handler_addr);
    }
}

/// 注销有效的 handler 指针（立即注销）
pub fn unregister_handler(handler_ptr: *mut SimpleHapticHandler) {
    let handler_addr = handler_ptr as usize;
    if let Ok(mut valid_handlers) = VALID_HANDLERS.lock() {
        valid_handlers.remove(&handler_addr);
        log::debug!("注销简化 handler 指针: {:p}", handler_ptr);
    }

    // 同时从待注销列表中移除
    if let Ok(mut pending_handlers) = PENDING_UNREGISTER_HANDLERS.lock() {
        pending_handlers.remove(&handler_addr);
    }
}

/// 标记 handler 为待注销（延迟注销机制）
pub fn mark_handler_for_unregister(handler_ptr: *mut SimpleHapticHandler) {
    let handler_addr = handler_ptr as usize;
    if let Ok(mut pending_handlers) = PENDING_UNREGISTER_HANDLERS.lock() {
        pending_handlers.insert(handler_addr);
        log::debug!("标记 handler 为待注销: {:p}", handler_ptr);
    }
}

/// 检查 handler 指针是否有效（考虑待注销状态）
pub fn is_handler_valid(handler_ptr: *mut SimpleHapticHandler) -> bool {
    let handler_addr = handler_ptr as usize;

    // 首先检查是否在待注销列表中
    if let Ok(pending_handlers) = PENDING_UNREGISTER_HANDLERS.lock() {
        if pending_handlers.contains(&handler_addr) {
            log::debug!("Handler {:p} 在待注销列表中，视为无效", handler_ptr);
            return false;
        }
    }

    // 然后检查是否在有效列表中
    let is_valid = if let Ok(valid_handlers) = VALID_HANDLERS.lock() {
        valid_handlers.contains(&handler_addr)
    } else {
        false
    };

    if !is_valid {
        log::debug!("Handler {:p} 不在有效列表中", handler_ptr);
    }

    is_valid
}

/// 清理待注销的 handler（在确认安全后调用）
pub fn cleanup_pending_unregister_handlers() {
    if let (Ok(mut valid_handlers), Ok(mut pending_handlers)) =
        (VALID_HANDLERS.lock(), PENDING_UNREGISTER_HANDLERS.lock()) {

        let count = pending_handlers.len();
        for &handler_addr in pending_handlers.iter() {
            valid_handlers.remove(&handler_addr);
        }
        pending_handlers.clear();

        if count > 0 {
            log::info!("清理了 {} 个待注销的 handler", count);
        }
    }
}



// ===== 业务逻辑实现 =====

impl SimpleHapticHandler {
    /// 触觉输出开始
    fn on_haptic_output_start(&mut self) {
        if self.config.enable_logging {
            log::info!("触觉输出开始 - 模式: {:?}", self.config.output_mode);
        }

        self.set_state(SimpleHandlerState::Active);
        self.reset_sample_count();

        match self.config.output_mode {
            OutputMode::Event => {
                // 事件模式：发送生命周期事件，保持与前端兼容
                self.emit_event("lifecycle", "start", json!({}));
            }
            OutputMode::File => {
                // 文件模式：打开文件准备写入
                if let Err(e) = self.open_file_for_writing() {
                    log::error!("打开文件失败: {}", e);
                    // 发送错误事件到前端
                    self.emit_event("error", "file_open_failed", json!({
                        "error": e.to_string()
                    }));
                } else {
                    if self.config.enable_logging {
                        log::info!("文件已打开，准备写入数据");
                    }
                    // 发送文件开始事件
                    self.emit_event("lifecycle", "file_start", json!({
                        "file_path": self.config.file_path.as_ref().unwrap_or(&"unknown".to_string())
                    }));
                }
            }
        }
    }

    /// 触觉输出完成
    fn on_haptic_output_complete(&mut self) {
        if self.config.enable_logging {
            log::info!("触觉输出完成 - 模式: {:?}", self.config.output_mode);
        }

        self.set_state(SimpleHandlerState::Idle);

        let sample_count = *self.sample_count.read();

        match self.config.output_mode {
            OutputMode::Event => {
                // 事件模式：发送生命周期事件，保持与前端兼容
                self.emit_event("lifecycle", "complete", json!({
                    "sample_count": sample_count
                }));
            }
            OutputMode::File => {
                // 文件模式：关闭文件并确保数据写入
                if let Err(e) = self.close_file() {
                    log::error!("关闭文件失败: {}", e);
                    // 发送错误事件
                    self.emit_event("error", "file_close_failed", json!({
                        "error": e.to_string(),
                        "sample_count": sample_count
                    }));
                } else {
                    if self.config.enable_logging {
                        log::info!("文件保存完成，共写入 {} 个样本", sample_count);
                    }
                    // 发送文件完成事件
                    self.emit_event("lifecycle", "file_complete", json!({
                        "sample_count": sample_count,
                        "file_path": self.config.file_path.as_ref().unwrap_or(&"unknown".to_string())
                    }));
                }
            }
        }

        // 如果有保存的配置，恢复它
        if self.has_saved_config() {
            self.restore_saved_config();
        }
    }

    /// 触觉输出停止
    fn on_haptic_output_stop(&mut self) {
        if self.config.enable_logging {
            log::info!("触觉输出停止 - 模式: {:?}", self.config.output_mode);
        }

        self.set_state(SimpleHandlerState::Stopped);
        let sample_count = *self.sample_count.read();
        self.reset_sample_count();

        match self.config.output_mode {
            OutputMode::Event => {
                // 事件模式：发送生命周期事件，保持与前端兼容
                self.emit_event("lifecycle", "stop", json!({
                    "reason": "normal"
                }));
            }
            OutputMode::File => {
                // 文件模式：关闭文件（如果还未关闭）
                if let Err(e) = self.close_file() {
                    log::error!("停止时关闭文件失败: {}", e);
                    // 发送错误事件
                    self.emit_event("error", "file_close_failed", json!({
                        "error": e.to_string(),
                        "sample_count": sample_count
                    }));
                } else {
                    // 发送文件停止事件
                    self.emit_event("lifecycle", "file_stop", json!({
                        "reason": "normal",
                        "sample_count": sample_count,
                        "file_path": self.config.file_path.as_ref().unwrap_or(&"unknown".to_string())
                    }));
                }
            }
        }

        // 如果有保存的配置，恢复它
        if self.has_saved_config() {
            self.restore_saved_config();
        }
    }

    /// 波形数据块开始
    fn on_waveform_chunk_start(&mut self) {
        if self.config.enable_logging {
            log::trace!("波形数据块开始 (简化版)");
        }

        // 更新处理状态
        if let Ok(mut state) = self.chunk_processing_state.lock() {
            state.processing_start_time = Instant::now();
            state.is_processing = true;
            state.is_completed = false;
            state.current_sample_count = 0;
        }

        // 发送数据事件，保持与前端兼容
        self.emit_event("data", "chunk_start", json!({}));
    }

    /// 处理波形样本（高频调用，需要节流）
    fn process_waveform_sample(&mut self, sample: c_char) {
        // 增加样本计数并获取当前索引
        let sample_index = {
            let mut count = self.sample_count.write();
            let current_index = *count;
            *count += 1;
            current_index
        };

        // 更新处理状态中的样本计数
        if let Ok(mut state) = self.chunk_processing_state.lock() {
            state.current_sample_count += 1;
        }

        match self.config.output_mode {
            OutputMode::Event => {
                // 事件模式：发送事件到前端（需要节流）

                // 高频数据节流处理
                if self.config.sample_throttle_ms > 0 {
                    let now = Instant::now();
                    let mut last_time = self.last_sample_time.lock().unwrap();

                    if (now.duration_since(*last_time).as_millis() as u64) < self.config.sample_throttle_ms {
                        // 跳过此次发送，但仍然计数
                        return;
                    }
                    *last_time = now;
                }

                if self.config.enable_logging {
                    log::trace!("处理样本: {} (索引: {})", sample, sample_index);
                }

                // 发送数据事件，保持与前端兼容
                self.emit_event("data", "waveform_sample", json!({
                    "sample": sample as i8,
                    "sample_index": sample_index
                }));
            }
            OutputMode::File => {
                // 文件模式：直接写入文件（无需节流，最大速度处理）
                if let Err(e) = self.write_sample_to_file(sample) {
                    log::error!("写入样本到文件失败: {}", e);
                    // 发送错误事件
                    self.emit_event("error", "file_write_failed", json!({
                        "error": e.to_string(),
                        "sample_index": sample_index
                    }));
                }

                if self.config.enable_logging && sample_index % 1000 == 0 {
                    log::trace!("已写入样本到文件: {} (索引: {})", sample, sample_index);
                }
            }
        }
    }

    /// 等待数据块处理完成
    ///
    /// 根据输出模式决定是否等待：
    /// - Event 模式：根据采样率计算等待时间，精确等待
    /// - File 模式：立即返回，不等待（最大速度处理）
    fn wait_chunk_processing_complete(&mut self, sample_count: i32) -> WaveformProcessingStatus {
        match self.config.output_mode {
            OutputMode::Event => {
                // 事件模式：需要等待以模拟实际播放时间

                // 获取当前采样率（Hz）
                let sampling_rate_hz = self.sampling_rate_to_hz();

                // 计算等待时间：N字节 / 采样率 * 1000ms
                let wait_time_ms = (sample_count as f64 / sampling_rate_hz as f64) * 1000.0;

                if self.config.enable_logging {
                    log::debug!(
                        "等待数据块处理完成 (事件模式): {}字节, 采样率: {}Hz, 等待时间: {:.2}ms",
                        sample_count,
                        sampling_rate_hz,
                        wait_time_ms
                    );
                }

                // 检查等待时间是否合理（防止异常值）
                if wait_time_ms < 0.0 || wait_time_ms > 10000.0 {
                    log::warn!(
                        "计算出的等待时间异常: {:.2}ms (样本数: {}, 采样率: {}Hz)",
                        wait_time_ms,
                        sample_count,
                        sampling_rate_hz
                    );
                    return WaveformProcessingStatus::ProcessingError;
                }

                // 精确等待计算出的时间
                let wait_duration = Duration::from_millis(wait_time_ms as u64);
                std::thread::sleep(wait_duration);

                if self.config.enable_logging {
                    log::debug!("数据块处理等待完成，实际等待时间: {:.2}ms", wait_time_ms);
                }

                WaveformProcessingStatus::ProcessingReady
            }
            OutputMode::File => {
                // 文件模式：立即返回，不等待，以最大速度处理数据
                if self.config.enable_logging {
                    log::debug!(
                        "数据块处理完成 (文件模式): {}字节, 无需等待",
                        sample_count
                    );
                }

                WaveformProcessingStatus::ProcessingReady
            }
        }
    }

    /// 标记数据块处理完成
    pub fn mark_chunk_processing_complete(&mut self) {
        if let Ok(mut state) = self.chunk_processing_state.lock() {
            state.is_completed = true;
            state.is_processing = false;
        }
        // 通知等待的线程
        self.processing_condvar.notify_all();
    }

    /// 设置输出振幅
    fn set_output_amplitude(&mut self, amplitude: u32) {
        let old_amplitude = *self.amplitude.read();
        *self.amplitude.write() = amplitude;

        if self.config.enable_logging {
            log::debug!("设置输出振幅: {} -> {}", old_amplitude, amplitude);
        }

        // 发送状态事件，保持与前端兼容
        self.emit_event("status", "amplitude_change", json!({
            "old_amplitude": old_amplitude,
            "new_amplitude": amplitude
        }));
    }

    /// 打开文件准备写入（仅在 File 模式下使用）
    fn open_file_for_writing(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(file_path) = &self.config.file_path {
            // 检查文件是否存在
            if std::path::Path::new(file_path).exists() {
                if self.config.enable_logging {
                    log::info!("文件已存在，将覆盖: {}", file_path);
                }
            }

            // 创建或覆盖文件
            let file = File::create(file_path)?;
            let buf_writer = BufWriter::new(file);

            // 存储文件写入器
            let mut writer_guard = self.file_writer.lock().unwrap();
            *writer_guard = Some(buf_writer);

            if self.config.enable_logging {
                log::info!("文件已打开用于写入: {}", file_path);
            }

            Ok(())
        } else {
            Err("文件路径未设置".into())
        }
    }

    /// 关闭文件并确保数据写入（仅在 File 模式下使用）
    fn close_file(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let mut writer_guard = self.file_writer.lock().unwrap();
        if let Some(mut writer) = writer_guard.take() {
            writer.flush()?;
            if self.config.enable_logging {
                log::info!("文件已关闭并刷新缓冲区");
            }
        }
        Ok(())
    }

    /// 写入单个样本到文件（仅在 File 模式下使用）
    fn write_sample_to_file(&mut self, sample: c_char) -> Result<(), Box<dyn std::error::Error>> {
        let mut writer_guard = self.file_writer.lock().unwrap();
        if let Some(ref mut writer) = *writer_guard {
            // 将 c_char 转换为字节并写入文件
            let byte = sample as u8;
            writer.write_all(&[byte])?;
            Ok(())
        } else {
            Err("文件写入器未初始化".into())
        }
    }

    /// 获取当前配置的副本
    pub fn get_config(&self) -> SimpleHandlerConfig {
        self.config.clone()
    }

    /// 更新配置
    pub fn update_config(&mut self, new_config: SimpleHandlerConfig) {
        self.config = new_config;
    }

    /// 检查是否为临时 Handler
    pub fn is_temporary(&self) -> bool {
        self.is_temporary
    }

    /// 保存当前配置（用于后续恢复）
    pub fn save_current_config(&mut self) {
        let mut saved = self.saved_config.lock().unwrap();
        *saved = Some(self.config.clone());
        if self.config.enable_logging {
            log::info!("已保存当前配置，模式: {:?}", self.config.output_mode);
        }
    }

    /// 恢复保存的配置
    pub fn restore_saved_config(&mut self) {
        let mut saved = self.saved_config.lock().unwrap();
        if let Some(original_config) = saved.take() {
            if self.config.enable_logging {
                log::info!("恢复原始配置，模式: {:?} -> {:?}", self.config.output_mode, original_config.output_mode);
            }
            self.config = original_config;
        }
    }

    /// 检查是否有保存的配置
    pub fn has_saved_config(&self) -> bool {
        let saved = self.saved_config.lock().unwrap();
        saved.is_some()
    }
}

impl Drop for SimpleHapticHandler {
    fn drop(&mut self) {
        self.unregister();
        if self.config.enable_logging {
            log::debug!("简化 handler 已销毁");
        }
    }
}

// ===== 全局回调等待函数（从 handler.rs 迁移） =====

/// 开始等待回调完成
/// 在调用 stop 相关操作前调用，准备等待异步回调
pub fn start_waiting_for_callback() {
    CALLBACK_COMPLETED.store(false, Ordering::SeqCst);
    WAITING_FOR_CALLBACK.store(true, Ordering::SeqCst);
    log::debug!("开始等待 librtcore 异步回调完成");
}

/// 停止等待回调完成
/// 清理等待状态，通常在超时或完成后调用
pub fn stop_waiting_for_callback() {
    WAITING_FOR_CALLBACK.store(false, Ordering::SeqCst);
    log::debug!("停止等待 librtcore 异步回调");
}

/// 等待回调完成，带超时
/// 返回 true 表示回调已完成，false 表示超时
pub async fn wait_for_callback_completion(timeout_ms: u64) -> bool {
    if !WAITING_FOR_CALLBACK.load(Ordering::SeqCst) {
        log::debug!("未在等待回调状态，直接返回");
        return true;
    }

    let start_time = Instant::now();
    let timeout_duration = std::time::Duration::from_millis(timeout_ms);

    log::debug!("开始等待回调完成，超时时间: {}ms", timeout_ms);

    while start_time.elapsed() < timeout_duration {
        if CALLBACK_COMPLETED.load(Ordering::SeqCst) {
            log::debug!("回调已完成，等待时间: {:?}", start_time.elapsed());
            return true;
        }

        // 短暂休眠避免忙等待
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
    }

    log::warn!("等待回调完成超时，等待时间: {:?}", start_time.elapsed());
    false
}



/// 同步等待回调完成（用于非异步上下文）
/// 返回 true 表示回调已完成，false 表示超时
pub fn wait_for_callback_completion_sync(timeout_ms: u64) -> bool {
    if !WAITING_FOR_CALLBACK.load(Ordering::SeqCst) {
        log::debug!("未在等待回调状态，直接返回");
        return true;
    }

    let start_time = Instant::now();
    let timeout_duration = std::time::Duration::from_millis(timeout_ms);

    log::debug!("开始同步等待回调完成，超时时间: {}ms", timeout_ms);

    while start_time.elapsed() < timeout_duration {
        if CALLBACK_COMPLETED.load(Ordering::SeqCst) {
            log::debug!("回调已完成，等待时间: {:?}", start_time.elapsed());
            return true;
        }

        // 短暂休眠避免忙等待
        std::thread::sleep(std::time::Duration::from_millis(10));
    }

    log::warn!("同步等待回调完成超时，等待时间: {:?}", start_time.elapsed());
    false
}

// ===== 全局单例 Handler 管理器 =====

/// 全局单例 Handler 管理器
///
/// 该结构体管理一个全局的 SimpleHapticHandler 实例，确保在整个应用生命周期内
/// Handler 对象始终有效，避免 librtcore 回调时访问已释放内存的问题。
pub struct GlobalHapticHandler {
    /// 固定在堆上的 Handler 实例
    handler: Pin<Box<SimpleHapticHandler>>,
    /// 固定在堆上的 vtable
    vtable: Pin<Box<HapticOutputHandlerVTable>>,
    /// 固定在堆上的 C 对象
    c_object: Pin<Box<HapticOutputHandlerObject>>,
    /// 是否已初始化
    is_initialized: bool,
}

impl GlobalHapticHandler {
    /// 创建新的全局 Handler 实例
    fn new(app_handle: Option<AppHandle>) -> Self {
        log::info!("创建全局单例 Handler");

        // 创建默认配置的 handler
        let handler_config = SimpleHandlerConfig::default();
        let mut handler = Box::pin(SimpleHapticHandler::new(handler_config, app_handle));

        // 创建稳定的 vtable
        let vtable = Box::pin(SimpleHapticHandler::create_vtable());

        // 获取handler指针（确保与c_object.data一致）
        let handler_data_ptr = handler.as_mut().get_mut() as *mut SimpleHapticHandler as *mut c_void;

        // 注册 handler（使用与c_object.data相同的指针）
        register_handler(handler_data_ptr as *mut SimpleHapticHandler);

        let c_object = Box::pin(HapticOutputHandlerObject {
            vtable: vtable.as_ref().get_ref() as *const HapticOutputHandlerVTable,
            data: handler_data_ptr,
        });

        log::info!("全局单例 Handler 创建完成");

        Self {
            handler,
            vtable,
            c_object,
            is_initialized: true,
        }
    }

    /// 获取稳定的 C 对象指针
    ///
    /// 该指针在 Handler 的整个生命周期内保持有效，可以安全地传递给 librtcore
    pub fn get_c_object_ptr(&self) -> *mut HapticOutputHandlerObject {
        self.c_object.as_ref().get_ref() as *const _ as *mut _
    }

    /// 更新应用句柄
    pub fn set_app_handle(&mut self, app_handle: AppHandle) {
        self.handler.as_mut().get_mut().set_app_handle(app_handle);
        log::info!("全局 Handler 应用句柄已更新");
    }

    /// 直接更新采样率配置（不触发重新配置流程）
    ///
    /// 该方法用于在初始化阶段设置正确的采样率，避免重新配置导致的等待问题
    pub fn update_sampling_rate(&mut self, sampling_rate: SamplingRateType) {
        self.handler.as_mut().get_mut().config.sampling_rate = sampling_rate;
        log::info!("全局 Handler 采样率已更新: {:?}", sampling_rate);
    }

    /// 重新配置 handler（支持更换马达配置）
    ///
    /// 该方法允许在运行时更换马达配置，而不需要销毁和重新创建 Handler
    /// 修复：添加同步等待机制，避免竞态条件
    pub fn reconfigure(&mut self, config: SimpleHandlerConfig, app_handle: Option<AppHandle>) {
        log::info!("重新配置全局 Handler，设备ID: {}", config.device_id);

        // 1. 先停止当前操作并等待完成
        log::info!("停止当前操作以准备重新配置");
        if let Err(e) = self.safe_stop() {
            log::warn!("停止当前操作时出现错误: {}", e);
        }

        // 2. 使用同步等待机制等待回调完成
        log::info!("等待librtcore完成pending操作");
        let callback_completed = wait_for_callback_completion_sync(1000); // 等待最多1秒

        if !callback_completed {
            log::warn!("等待回调完成超时，强制继续重新配置");
        } else {
            log::info!("回调完成确认");
        }

        // 3. 额外等待一段时间确保所有操作完成
        std::thread::sleep(std::time::Duration::from_millis(200));

        // 4. 现在安全地标记旧handler为待注销（而不是立即注销）
        log::info!("标记旧handler为待注销");
        let old_handler_ptr = self.handler.as_ref().get_ref() as *const SimpleHapticHandler as *mut SimpleHapticHandler;
        mark_handler_for_unregister(old_handler_ptr);

        // 5. 创建新handler
        log::info!("创建新handler");
        let mut new_handler = Box::pin(SimpleHapticHandler::new(config, app_handle));

        // 6. 获取新handler指针并注册（确保与c_object.data一致）
        let new_handler_ptr = new_handler.as_mut().get_mut() as *mut SimpleHapticHandler;
        register_handler(new_handler_ptr);

        // 7. 安全地更新 c_object 的 data 指针
        unsafe {
            let c_object_mut = self.c_object.as_mut().get_unchecked_mut();
            c_object_mut.data = new_handler_ptr as *mut c_void;
        }

        // 8. 替换 handler
        self.handler = new_handler;

        // 9. 延迟清理待注销的handler（给librtcore一些时间完成回调）
        std::thread::spawn(|| {
            std::thread::sleep(std::time::Duration::from_millis(1000)); // 等待1秒
            cleanup_pending_unregister_handlers();
        });

        log::info!("全局 Handler 重新配置完成");
    }

    /// 获取 handler 引用
    pub fn get_handler(&self) -> &SimpleHapticHandler {
        self.handler.as_ref().get_ref()
    }

    /// 获取当前handler配置
    pub fn get_handler_config(&self) -> &SimpleHandlerConfig {
        &self.handler.as_ref().get_ref().config
    }

    /// 获取可变 handler 引用
    pub fn get_handler_mut(&mut self) -> &mut SimpleHapticHandler {
        self.handler.as_mut().get_mut()
    }

    /// 检查是否已初始化
    pub fn is_initialized(&self) -> bool {
        self.is_initialized
    }

    /// 安全停止（等待回调完成）
    ///
    /// 该方法确保在停止时等待所有异步回调完成，但不销毁 Handler 实例
    pub fn safe_stop(&self) -> Result<(), String> {
        log::info!("开始安全停止全局 Handler");

        // 1. 开始等待回调完成
        start_waiting_for_callback();

        // 2. 调用 librtcore 停止
        if let Ok(api_guard) = crate::haptic::ffi::get_api() {
            if let Ok(api) = api_guard.lock() {
                if let Some(ref api) = *api {
                    let _ = unsafe { (api.append_stop)() };
                    log::info!("已调用 librtcore 停止");
                }
            }
        }

        log::info!("全局 Handler 停止调用完成");
        Ok(())
    }

    /// 获取 Handler 的原始指针（用于配置修改）
    ///
    /// 注意：这是一个不安全的操作，调用者需要确保线程安全
    pub fn get_handler_ptr(&self) -> *mut SimpleHapticHandler {
        self.handler.as_ref().get_ref() as *const SimpleHapticHandler as *mut SimpleHapticHandler
    }


}

impl Drop for GlobalHapticHandler {
    fn drop(&mut self) {
        log::info!("销毁全局单例 Handler");
        self.handler.unregister();
        log::info!("全局单例 Handler 已销毁");
    }
}

// 安全性说明：
// GlobalHapticHandler 包含原始指针，但这些指针的生命周期是受控的：
// 1. handler 指针在整个 GlobalHapticHandler 生命周期内有效
// 2. vtable 和 c_object 都是固定在堆上的，地址不会改变
// 3. 所有访问都通过 Mutex 保护，确保线程安全
unsafe impl Send for GlobalHapticHandler {}
unsafe impl Sync for GlobalHapticHandler {}

// ===== 测试模块 =====

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_wait_time_calculation() {
        // 测试不同采样率下的等待时间计算
        let test_cases = vec![
            (SamplingRateType::Sampling6Khz, 60, 10.0),    // 60字节 / 6000Hz = 10ms
            (SamplingRateType::Sampling8Khz, 80, 10.0),    // 80字节 / 8000Hz = 10ms
            (SamplingRateType::Sampling12Khz, 120, 10.0),  // 120字节 / 12000Hz = 10ms
            (SamplingRateType::Sampling24Khz, 240, 10.0),  // 240字节 / 24000Hz = 10ms
        ];

        for (sampling_rate, sample_count, expected_ms) in test_cases {
            let config = SimpleHandlerConfig {
                device_id: 0,
                enable_logging: false,
                enable_events: false,
                sample_throttle_ms: 10,
                sampling_rate,
            };

            let handler = SimpleHapticHandler::new(config, None);
            let actual_ms = handler.calculate_wait_time_ms(sample_count);

            assert!((actual_ms - expected_ms).abs() < 0.001,
                   "采样率 {:?}: 期望 {}ms, 实际 {}ms",
                   sampling_rate, expected_ms, actual_ms);
        }
    }

    #[test]
    fn test_sampling_rate_conversion() {
        let test_cases = vec![
            (SamplingRateType::Sampling6Khz, 6000),
            (SamplingRateType::Sampling8Khz, 8000),
            (SamplingRateType::Sampling12Khz, 12000),
            (SamplingRateType::Sampling24Khz, 24000),
        ];

        for (sampling_rate, expected_hz) in test_cases {
            let config = SimpleHandlerConfig {
                device_id: 0,
                enable_logging: false,
                enable_events: false,
                sample_throttle_ms: 10,
                sampling_rate,
            };

            let handler = SimpleHapticHandler::new(config, None);
            let actual_hz = handler.sampling_rate_to_hz();

            assert_eq!(actual_hz, expected_hz,
                      "采样率转换错误: {:?} 应该是 {}Hz, 实际是 {}Hz",
                      sampling_rate, expected_hz, actual_hz);
        }
    }

    #[test]
    fn test_sampling_rate_update() {
        // 测试采样率更新功能
        let config = SimpleHandlerConfig {
            device_id: 0,
            enable_logging: false,
            enable_events: false,
            sample_throttle_ms: 10,
            sampling_rate: SamplingRateType::Sampling8Khz,
        };

        let mut handler = SimpleHapticHandler::new(config, None);

        // 验证初始采样率
        assert_eq!(handler.sampling_rate_to_hz(), 8000);

        // 更新采样率
        handler.config.sampling_rate = SamplingRateType::Sampling24Khz;

        // 验证更新后的采样率
        assert_eq!(handler.sampling_rate_to_hz(), 24000);

        // 测试等待时间计算是否使用了新的采样率
        let wait_time = handler.calculate_wait_time_ms(240);
        assert!((wait_time - 10.0).abs() < 0.001, "240字节在24KHz下应该等待10ms，实际: {}ms", wait_time);
    }
}

// 全局单例实例
static GLOBAL_HAPTIC_HANDLER: OnceLock<Arc<Mutex<GlobalHapticHandler>>> = OnceLock::new();

/// 获取全局 Handler 单例
///
/// 该函数返回全局 Handler 单例的引用，如果尚未初始化则会自动创建
pub fn get_global_haptic_handler() -> &'static Arc<Mutex<GlobalHapticHandler>> {
    GLOBAL_HAPTIC_HANDLER.get_or_init(|| {
        log::info!("初始化全局 Handler 单例");
        Arc::new(Mutex::new(GlobalHapticHandler::new(None)))
    })
}

/// 初始化全局 Handler
///
/// 该函数在应用启动时调用，设置应用句柄
pub fn initialize_global_handler(app_handle: AppHandle) {
    let mut handler = get_global_haptic_handler().lock().unwrap();
    handler.set_app_handle(app_handle);
    log::info!("全局单例 Handler 初始化完成");
}



/// 重新配置全局 Handler
///
/// 该函数允许在运行时更换马达配置
pub fn reconfigure_global_handler(config: SimpleHandlerConfig, app_handle: Option<AppHandle>) {
    let mut handler = get_global_haptic_handler().lock().unwrap();
    handler.reconfigure(config, app_handle);
}

/// 更新全局 Handler 的采样率
///
/// 该函数用于在初始化阶段设置正确的采样率，避免重新配置导致的等待问题
pub fn update_global_handler_sampling_rate(sampling_rate: SamplingRateType) {
    let mut handler = get_global_haptic_handler().lock().unwrap();
    handler.update_sampling_rate(sampling_rate);
}

/// 获取全局 Handler 的 C 对象指针
///
/// 该函数返回可以传递给 librtcore 的稳定指针
pub fn get_global_handler_c_ptr() -> *mut HapticOutputHandlerObject {
    let handler = get_global_haptic_handler().lock().unwrap();
    handler.get_c_object_ptr()
}

/// 临时切换全局 Handler 到文件模式
pub fn switch_global_handler_to_file_mode(file_path: String) -> Result<(), String> {
    let handler_guard = get_global_haptic_handler();
    let handler = handler_guard.lock().unwrap();

    unsafe {
        let handler_ptr = handler.get_handler_ptr();
        if let Some(h) = handler_ptr.as_mut() {
            // 保存当前配置
            h.save_current_config();

            // 创建文件模式配置
            let mut file_config = h.get_config();
            file_config.output_mode = OutputMode::File;
            file_config.file_path = Some(file_path.clone());

            // 更新配置
            h.update_config(file_config);
            log::info!("已切换到文件保存模式，文件路径: {}", file_path);
            Ok(())
        } else {
            Err("Handler 指针无效".to_string())
        }
    }
}



/// 安全停止全局 Handler
///
/// 该函数确保在停止时等待所有异步回调完成
pub async fn safe_stop_global_handler() -> Result<(), String> {
    // 1. 调用停止（不持有 Mutex）
    {
        let handler = get_global_haptic_handler().lock().unwrap();
        handler.safe_stop()?;
    }

    // 2. 等待异步回调完成（不持有 Mutex）
    let completed = wait_for_callback_completion(1000).await;
    if !completed {
        log::warn!("等待回调完成超时，但 Handler 仍然有效");
    } else {
        log::info!("所有回调已完成");
    }

    // 3. 停止等待
    stop_waiting_for_callback();

    log::info!("全局 Handler 安全停止完成");
    Ok(())
}

// ===== 兼容性类型别名（用于替代 handler.rs 中的类型） =====

/// 兼容性：处理器状态类型别名
pub type HandlerState = SimpleHandlerState;

/// 兼容性：处理器配置类型别名
pub type HandlerConfig = SimpleHandlerConfig;

/// 兼容性：处理器类型别名
pub type HapticOutputHandler = SimpleHapticHandler;
