# AWA RealityTap Studio 技术架构文档

## 项目概述

AWA RealityTap Studio 是一个使用 Tauri 框架构建的桌面应用程序，它结合了 Vue.js 前端和 Rust 后端。这是一个针对触觉设计（Haptic Design）的工具，允许用户创建、编辑和管理触觉效果项目。

## 技术栈

### 前端

- **框架**: Vue 3.5.13 (使用 Composition API)
- **状态管理**: Pinia 3.0.2
- **路由管理**: Vue Router 4
- **UI组件库**: Naive UI 2.41.0
- **构建工具**: Vite 6.3.1
- **语言**: TypeScript

### 后端

- **框架**: Tauri 2.5.0
- **语言**: Rust
- **日志管理**: tauri-plugin-log 2.0.0

## 项目结构

```
realitytap-desktop/
├── src/                                 # Vue 前端源代码
│   ├── assets/                          # 静态资源
│   │   └── vue.svg                      # Vue 框架图标
│   ├── components/                      # Vue 组件
│   │   ├── common/                      # 通用组件
│   │   │   ├── ConnectionStatus.vue     # 连接状态指示器组件
│   │   │   └── ExportButton.vue         # 导出按钮组件
│   │   ├── editor/                      # 编辑器相关组件
│   │   │   ├── ProjectEditor.vue        # 主编辑器容器组件
│   │   │   ├── header/                  # 编辑器头部组件
│   │   │   │   ├── ProjectHeader.vue    # 项目标题和返回按钮
│   │   │   │   └── EditorTabs.vue       # 编辑模式切换标签
│   │   │   ├── waveform/                # 波形编辑器组件
│   │   │   │   ├── WaveformEditor.vue   # 波形编辑主组件
│   │   │   │   └── WaveformGraph.vue    # 波形图显示组件
│   │   │   ├── clips/                   # 剪辑管理组件
│   │   │   │   ├── ClipsPanel.vue       # 剪辑面板组件
│   │   │   │   ├── ClipCategory.vue     # 剪辑分类组件
│   │   │   │   ├── ClipItem.vue         # 剪辑项目组件
│   │   │   │   └── EmptyClipArea.vue    # 空白剪辑区域组件
│   │   │   └── adjustments/             # 参数调整组件
│   │   │       ├── EventAdjustPanel.vue # 事件细调面板
│   │   │       └── SliderControl.vue    # 滑块控制组件
│   │   ├── AppMenu.vue                  # 应用菜单组件 (包含文件操作等)
│   │   └── ...                          # 其他组件
│   ├── router/                          # Vue Router 路由配置
│   │   └── index.ts                     # 路由定义和配置
│   ├── stores/                          # Pinia 状态管理
│   │   ├── projectStore.ts              # 项目状态管理
│   │   └── counter.ts                   # 计数器状态管理示例
│   ├── types/                           # TypeScript 类型定义
│   │   ├── project.ts                   # 项目类型定义
│   │   └── index.ts                     # 类型导出索引
│   ├── views/                           # 视图组件
│   │   ├── HomeView.vue                 # 首页/项目列表视图
│   │   ├── EditorView.vue               # 编辑器视图
│   │   └── LearningView.vue             # 学习资源视图
│   ├── App.vue                          # 主应用组件
│   ├── main.ts                          # 应用入口文件
│   ├── vite-env.d.ts                    # Vite 环境类型声明
│   └── style.css                        # 全局样式定义
├── src-tauri/                           # Tauri/Rust 后端
│   ├── src/                             # Rust 源代码
│   │   ├── main.rs                      # 主入口点
│   │   └── lib.rs                       # 库函数和应用初始化
│   │   ├── models.rs                    # 项目数据结构定义 (Project, Group, FileEntry)
│   │   ├── error.rs                     # 自定义错误类型和 Result 别名
│   │   ├── project_io.rs                # ARPF (.arpf) 文件读写与验证逻辑
│   │   ├── commands.rs                  # Tauri 命令定义 (暴露给前端的接口)
│   │   ├── validation.rs                # 项目内部逻辑验证
│   │   └── he_file.rs                   # .he 文件内容处理逻辑 (待实现)
│   ├── capabilities/                    # Tauri 功能配置
│   │   └── default.json                 # 默认权限配置
│   ├── icons/                           # 应用图标资源
│   │   ├── icon.ico                     # Windows 图标
│   │   ├── icon.icns                    # macOS 图标
│   │   ├── icon.png                     # 通用图标
│   │   └── ...                          # 各种大小和格式的图标
│   ├── Cargo.toml                       # Rust 依赖配置
│   ├── Cargo.lock                       # Rust 依赖锁定文件
│   ├── tauri.conf.json                  # Tauri 应用配置
│   └── build.rs                         # Rust 构建脚本
├── public/                              # 静态公共资源
│   ├── resources/                       # 公共资源目录
│   └── vite.svg                         # Vite 图标
├── vite.config.ts                       # Vite 构建配置
├── tsconfig.json                        # TypeScript 主配置
├── tsconfig.app.json                    # 应用 TypeScript 配置
├── tsconfig.node.json                   # Node 环境 TypeScript 配置
├── package.json                         # NPM 依赖配置
├── package-lock.json                    # NPM 依赖锁定文件
├── index.html                           # 主 HTML 入口文件
├── components.d.ts                      # 组件类型自动生成文件
└── auto-imports.d.ts                    # 自动导入类型定义
```

## 架构设计

### 前端架构

AWA RealityTap Studio 的前端采用组件化设计，基于 Vue 3 的 Composition API。应用程序使用 Vue Router 进行路由管理，实现了三个主要页面：

1. **首页/项目列表页面** - 显示最近的项目和样例项目，包括:
   - 创建新项目选项
   - 打开现有项目选项
   - 浏览示例项目列表

2. **编辑器页面** - 提供触觉效果的编辑功能，包括:
   - 剪辑管理
   - 波形编辑
   - 参数调整
   - 项目导出

3. **学习页面** - 提供触觉设计相关的教程和学习资源

应用使用 Pinia 进行状态管理，组织和管理应用的全局状态。

#### 路由架构

应用使用 Vue Router 实现页面导航和状态管理，路由系统包括以下主要路由：

- **/** - 首页/项目列表页面
- **/editor** - 编辑器页面，支持通过查询参数加载特定项目
- **/learning** - 学习资源页面

路由配置还包括路由守卫，用于设置页面标题和处理导航逻辑。

#### 关键组件

- **App.vue**: 应用程序的主要容器组件，处理页面切换和整体布局
- **HomeView.vue**: 首页组件，显示项目列表和示例项目
- **EditorView.vue**: 编辑器页面的容器组件，加载或创建项目
- **ProjectEditor.vue**: 编辑器的主要组件，管理项目编辑功能
- **LearningView.vue**: 学习资源页面组件
- **AppMenu.vue**: 应用菜单，提供各种操作如创建项目、打开项目等

### 后端架构

应用的后端使用 Rust 和 Tauri 框架实现，主要职责包括:

1.  文件系统交互（通过 `.arpf` 项目文件）
2.  本地资源管理（处理 `.he` 文件和关联音频）
3.  提供应用窗口和原生功能
4.  实现核心业务逻辑，如项目创建、加载、保存、验证、文件和分组管理。

Tauri 框架允许将 Web 前端与 Rust 后端集成，提供更好的性能和安全性。后端通过 Tauri 提供的 API 与前端进行通信。

后端代码结构化为多个模块：
- **`models.rs`**: 定义核心数据结构，如 `Project`, `Group`, `FileEntry`, 并使用 `serde` 进行 JSON 序列化/反序列化。
- **`error.rs`**: 定义统一的错误枚举 `Error` (使用 `thiserror`) 和 `Result` 类型别名，用于处理和向前端传递错误信息。
- **`project_io.rs`**: 封装了对 `.arpf` (ZIP 格式) 文件的所有底层操作，包括使用 `zip` crate 进行解压缩和压缩，使用 `serde_json` 解析/序列化 `project.json`，以及使用 `md5` crate 计算和验证 checksum。
- **`commands.rs`**: 包含所有使用 `#[tauri::command]` 宏定义的函数，这些函数是前端可以直接调用的接口，用于执行如 `load_project`, `save_project`, `rename_file`, `create_group` 等操作。
- **`validation.rs`**: 包含项目内部结构的逻辑验证，例如检查 UUID 的唯一性。
- **`he_file.rs`**: (目前为占位符) 计划用于处理 `.he` 文件内容的具体解析、序列化和格式转换逻辑。

关键依赖库包括: `tauri`, `serde`, `serde_json`, `zip`, `md5`, `uuid`, `chrono`, `thiserror`, `log`。

## 数据流

1. 用户通过 UI 组件或菜单触发操作（如"创建新项目", "保存项目", "重命名文件"）
2. 操作通过 Vue Router 导航到相应页面或直接调用 Pinia store 中的 action
3. 目标页面组件挂载后，根据路由参数决定是创建新项目还是加载现有项目 (如 `EditorView.vue`)
4. 需要修改项目状态的操作通过 Pinia 状态管理 (如 `projectStore`) 更新前端状态
5. 需要后端处理的操作（如文件 I/O、项目验证、复杂逻辑）通过 Tauri API (`invoke`) 发送给 Rust 后端，调用在 `commands.rs` 中定义的相应命令 (例如 `invoke('load_project', { arpfPath: '...' })`, `invoke('save_project', { projectData: {...}, arpfPath: '...' })`)。
6. Rust 后端处理请求（可能涉及 `project_io`, `validation` 等模块），完成后将结果（成功时可能带有更新后的项目数据，失败时带有 `Error` 信息）通过 `Result<T, Error>` 返回给前端的 Promise。
7. 前端接收后端返回的结果，更新 UI 或显示错误信息。

### 项目导航流程

以"创建新项目"为例：

1. 用户点击首页上的"新建项目"或使用应用菜单中的"New Project"
2. 触发 `router.push('/editor')` 导航操作
3. 路由系统加载 EditorView 组件
4. EditorView 组件挂载后，检查URL参数，发现没有项目ID
5. 调用 projectStore.createNewProject() 创建新的空白项目
6. ProjectEditor 组件渲染新项目的编辑界面

## 界面设计

应用程序的界面采用深色主题设计，包括以下主要元素:

1. 顶部应用菜单，提供文件操作和其他功能
2. 顶部标题栏，显示应用名称和连接状态
3. 标签页导航，用于切换不同页面
4. 项目卡片布局，便于浏览和选择项目
5. 编辑器页面中的三栏布局:
   - 左侧剪辑面板
   - 中央波形编辑区
   - 右侧事件细调面板

## 状态管理

应用使用 Pinia 进行状态管理，主要的存储包括：

1. **projectStore** - 管理项目状态:
   - 当前项目数据
   - 选中的剪辑
   - 播放状态
   - 项目操作方法

这种状态管理方式允许不同组件之间共享数据，并在导航过程中保持状态。

## 构建和部署

应用程序使用 Vite 作为前端构建工具，Tauri CLI 用于打包和构建桌面应用。构建过程包括:

1. 使用 `npm run build` 构建 Vue 前端
2. 使用 `npm run tauri:build` 构建 Tauri 应用程序

构建的桌面应用可以部署到 Windows、macOS 和 Linux 平台。

## 未来发展方向

根据现有代码结构，可能的发展方向包括:

1. 增强编辑器功能，支持更复杂的触觉效果编辑
2. 添加更多示例项目和学习资源
3. 改进用户界面和体验
4. 扩展设备连接和测试功能
5. 添加导出和共享功能
6. 实现项目自动保存和历史记录功能
7. 添加用户账户和云同步功能

## 结论

AWA RealityTap Studio 是一个现代化的触觉设计工具，采用 Vue.js 和 Rust 技术栈，通过 Tauri 框架实现跨平台桌面应用。其架构设计清晰，组件化结构良好，路由系统实现了流畅的页面导航，为未来的功能扩展和维护提供了坚实的基础。
