<svg width="1000" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowBlue" markerWidth="10" markerHeight="7" 
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#1976d2" />
    </marker>
    <marker id="arrowGreen" markerWidth="10" markerHeight="7" 
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#388e3c" />
    </marker>
    <marker id="arrowRed" markerWidth="10" markerHeight="7" 
     refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#d32f2f" />
    </marker>
  </defs>
  
  <!-- 背景分层 -->
  <rect x="20" y="20" width="960" height="120" rx="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="1" opacity="0.3"/>
  <text x="30" y="40" font-family="Arial" font-size="14" font-weight="bold" fill="#1976d2">项目级别存储 Project Level Storage</text>
  
  <rect x="20" y="160" width="960" height="180" rx="10" fill="#e8f5e8" stroke="#388e3c" stroke-width="1" opacity="0.3"/>
  <text x="30" y="180" font-family="Arial" font-size="14" font-weight="bold" fill="#388e3c">文件级别存储 File Level Storage</text>
  
  <rect x="20" y="360" width="960" height="180" rx="10" fill="#fff3e0" stroke="#f57c00" stroke-width="1" opacity="0.3"/>
  <text x="30" y="380" font-family="Arial" font-size="14" font-weight="bold" fill="#f57c00">Canvas级别存储 Canvas Level Storage</text>
  
  <!-- 项目级别组件 -->
  <rect x="50" y="60" width="150" height="60" rx="5" fill="#bbdefb" stroke="#1976d2" stroke-width="2"/>
  <text x="125" y="80" text-anchor="middle" font-family="Arial" font-size="10">ProjectStore</text>
  <text x="125" y="95" text-anchor="middle" font-family="Arial" font-size="9">项目元数据管理</text>
  <text x="125" y="110" text-anchor="middle" font-family="Arial" font-size="8">selectedFileUuid</text>
  
  <rect x="230" y="60" width="150" height="60" rx="5" fill="#c8e6c9" stroke="#388e3c" stroke-width="2"/>
  <text x="305" y="80" text-anchor="middle" font-family="Arial" font-size="10">FileCaches</text>
  <text x="305" y="95" text-anchor="middle" font-family="Arial" font-size="9">Map&lt;fileUuid, Cache&gt;</text>
  <text x="305" y="110" text-anchor="middle" font-family="Arial" font-size="8">文件缓存管理</text>
  
  <rect x="410" y="60" width="150" height="60" rx="5" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2"/>
  <text x="485" y="80" text-anchor="middle" font-family="Arial" font-size="10">UnsavedFiles</text>
  <text x="485" y="95" text-anchor="middle" font-family="Arial" font-size="9">Set&lt;fileUuid&gt;</text>
  <text x="485" y="110" text-anchor="middle" font-family="Arial" font-size="8">未保存状态</text>
  
  <!-- 文件级别组件 -->
  <rect x="50" y="200" width="180" height="80" rx="5" fill="#a5d6a7" stroke="#4caf50" stroke-width="2"/>
  <text x="140" y="220" text-anchor="middle" font-family="Arial" font-size="10">FileEditorStateManager</text>
  <text x="140" y="235" text-anchor="middle" font-family="Arial" font-size="9">Map&lt;fileUuid, FileState&gt;</text>
  <text x="140" y="250" text-anchor="middle" font-family="Arial" font-size="8">• events: RenderableEvent[]</text>
  <text x="140" y="265" text-anchor="middle" font-family="Arial" font-size="8">• UI状态 • 编辑历史</text>
  
  <rect x="260" y="200" width="180" height="80" rx="5" fill="#b39ddb" stroke="#673ab7" stroke-width="2"/>
  <text x="350" y="220" text-anchor="middle" font-family="Arial" font-size="10">FileStoreInstances</text>
  <text x="350" y="235" text-anchor="middle" font-family="Arial" font-size="9">Map&lt;fileUuid, Store&gt;</text>
  <text x="350" y="250" text-anchor="middle" font-family="Arial" font-size="8">useFileWaveformEditor</text>
  <text x="350" y="265" text-anchor="middle" font-family="Arial" font-size="8">Store(fileUuid)</text>
  
  <rect x="470" y="200" width="180" height="80" rx="5" fill="#ffab91" stroke="#ff5722" stroke-width="2"/>
  <text x="560" y="220" text-anchor="middle" font-family="Arial" font-size="10">TabManager</text>
  <text x="560" y="235" text-anchor="middle" font-family="Arial" font-size="9">EnhancedFileTab[]</text>
  <text x="560" y="250" text-anchor="middle" font-family="Arial" font-size="8">标签页状态管理</text>
  <text x="560" y="265" text-anchor="middle" font-family="Arial" font-size="8">LRU缓存策略</text>
  
  <rect x="680" y="200" width="180" height="80" rx="5" fill="#f8bbd9" stroke="#e91e63" stroke-width="2"/>
  <text x="770" y="220" text-anchor="middle" font-family="Arial" font-size="10">FileStateSync</text>
  <text x="770" y="235" text-anchor="middle" font-family="Arial" font-size="9">状态同步管理</text>
  <text x="770" y="250" text-anchor="middle" font-family="Arial" font-size="8">WaveformEditor ↔</text>
  <text x="770" y="265" text-anchor="middle" font-family="Arial" font-size="8">EventAdjustPanel</text>
  
  <!-- Canvas级别组件 -->
  <rect x="50" y="400" width="160" height="80" rx="5" fill="#fff9c4" stroke="#f57f17" stroke-width="2"/>
  <text x="130" y="420" text-anchor="middle" font-family="Arial" font-size="10">InteractiveWaveform</text>
  <text x="130" y="435" text-anchor="middle" font-family="Arial" font-size="10">Canvas.vue</text>
  <text x="130" y="450" text-anchor="middle" font-family="Arial" font-size="8">fileUuid: "file-1"</text>
  <text x="130" y="465" text-anchor="middle" font-family="Arial" font-size="8">独立Canvas实例</text>
  
  <rect x="240" y="400" width="160" height="80" rx="5" fill="#fff9c4" stroke="#f57f17" stroke-width="2"/>
  <text x="320" y="420" text-anchor="middle" font-family="Arial" font-size="10">InteractiveWaveform</text>
  <text x="320" y="435" text-anchor="middle" font-family="Arial" font-size="10">Canvas.vue</text>
  <text x="320" y="450" text-anchor="middle" font-family="Arial" font-size="8">fileUuid: "file-2"</text>
  <text x="320" y="465" text-anchor="middle" font-family="Arial" font-size="8">独立Canvas实例</text>
  
  <rect x="430" y="400" width="160" height="80" rx="5" fill="#fff9c4" stroke="#f57f17" stroke-width="2"/>
  <text x="510" y="420" text-anchor="middle" font-family="Arial" font-size="10">InteractiveWaveform</text>
  <text x="510" y="435" text-anchor="middle" font-family="Arial" font-size="10">Canvas.vue</text>
  <text x="510" y="450" text-anchor="middle" font-family="Arial" font-size="8">fileUuid: "file-3"</text>
  <text x="510" y="465" text-anchor="middle" font-family="Arial" font-size="8">独立Canvas实例</text>
  
  <!-- 数据隔离示意 -->
  <rect x="620" y="400" width="300" height="120" rx="8" fill="#e8eaf6" stroke="#3f51b5" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="630" y="420" font-family="Arial" font-size="12" font-weight="bold" fill="#3f51b5">数据隔离保证 Data Isolation</text>
  
  <text x="630" y="440" font-family="Arial" font-size="10" fill="#333">✓ 每个文件独立的Store实例</text>
  <text x="630" y="455" font-family="Arial" font-size="10" fill="#333">✓ 独立的事件数据数组</text>
  <text x="630" y="470" font-family="Arial" font-size="10" fill="#333">✓ 独立的UI状态管理</text>
  <text x="630" y="485" font-family="Arial" font-size="10" fill="#333">✓ 独立的编辑历史栈</text>
  <text x="630" y="500" font-family="Arial" font-size="10" fill="#333">✓ 独立的Canvas坐标系统</text>
  
  <!-- 连接线 -->
  <!-- 项目级到文件级 -->
  <line x1="125" y1="120" x2="140" y2="190" stroke="#1976d2" stroke-width="2" marker-end="url(#arrowBlue)"/>
  <line x1="305" y1="120" x2="350" y2="190" stroke="#388e3c" stroke-width="2" marker-end="url(#arrowGreen)"/>
  <line x1="485" y1="120" x2="560" y2="190" stroke="#d32f2f" stroke-width="2" marker-end="url(#arrowRed)"/>
  
  <!-- 文件级到Canvas级 -->
  <line x1="140" y1="280" x2="130" y2="390" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowGreen)"/>
  <line x1="350" y1="280" x2="320" y2="390" stroke="#673ab7" stroke-width="2" marker-end="url(#arrowGreen)"/>
  <line x1="560" y1="280" x2="510" y2="390" stroke="#ff5722" stroke-width="2" marker-end="url(#arrowGreen)"/>
  
  <!-- 文件UUID标识 -->
  <text x="80" y="520" font-family="Arial" font-size="9" fill="#666">file-uuid-1</text>
  <text x="270" y="520" font-family="Arial" font-size="9" fill="#666">file-uuid-2</text>
  <text x="460" y="520" font-family="Arial" font-size="9" fill="#666">file-uuid-3</text>
</svg>
