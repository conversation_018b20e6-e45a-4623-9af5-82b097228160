/**
 * 波形错误边界和恢复机制
 * 提供错误捕获、日志记录和自动恢复功能
 */

import { ref, computed, onErrorCaptured } from "vue";
import { waveformLogger } from "@/utils/logger/logger";

/**
 * 错误类型
 */
export type WaveformErrorType =
  | "rendering" // 渲染错误
  | "dataProcessing" // 数据处理错误
  | "stateManagement" // 状态管理错误
  | "eventHandling" // 事件处理错误
  | "performance" // 性能相关错误
  | "consistency" // 一致性错误
  | "unknown"; // 未知错误

/**
 * 错误严重程度
 */
export type ErrorSeverity = "low" | "medium" | "high" | "critical";

/**
 * 错误信息
 */
export interface WaveformError {
  id: string;
  type: WaveformErrorType;
  severity: ErrorSeverity;
  message: string;
  stack?: string;
  context?: any;
  timestamp: number;
  recovered: boolean;
  recoveryAttempts: number;
  maxRecoveryAttempts: number;
}

/**
 * 恢复策略
 */
export interface RecoveryStrategy {
  type: WaveformErrorType;
  handler: (error: WaveformError, context: any) => Promise<boolean>;
  maxAttempts: number;
  retryDelay: number;
}

/**
 * 错误边界配置
 */
export interface ErrorBoundaryConfig {
  enableAutoRecovery?: boolean;
  enableLogging?: boolean;
  maxErrorHistory?: number;
  recoveryStrategies?: RecoveryStrategy[];
  onError?: (error: WaveformError) => void;
  onRecovery?: (error: WaveformError) => void;
}

/**
 * 错误统计
 */
interface ErrorStats {
  totalErrors: number;
  recoveredErrors: number;
  criticalErrors: number;
  errorsByType: Record<WaveformErrorType, number>;
  lastErrorTime: number;
  averageRecoveryTime: number;
}

/**
 * 波形错误边界Hook
 */
export function useWaveformErrorBoundary(waveformStore: any, config: ErrorBoundaryConfig = {}) {
  const { enableAutoRecovery = true, enableLogging = true, maxErrorHistory = 50, recoveryStrategies = [], onError, onRecovery } = config;

  // 错误历史
  const errorHistory = ref<WaveformError[]>([]);
  const currentError = ref<WaveformError | null>(null);

  // 错误统计
  const errorStats = ref<ErrorStats>({
    totalErrors: 0,
    recoveredErrors: 0,
    criticalErrors: 0,
    errorsByType: {
      rendering: 0,
      dataProcessing: 0,
      stateManagement: 0,
      eventHandling: 0,
      performance: 0,
      consistency: 0,
      unknown: 0,
    },
    lastErrorTime: 0,
    averageRecoveryTime: 0,
  });

  // 恢复状态
  const isRecovering = ref(false);
  const recoveryProgress = ref(0);

  /**
   * 生成错误ID
   */
  const generateErrorId = (): string => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  /**
   * 确定错误类型
   */
  const determineErrorType = (error: Error, _context?: any): WaveformErrorType => {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || "";

    if (message.includes("render") || stack.includes("draw") || stack.includes("canvas")) {
      return "rendering";
    }
    if (message.includes("data") || message.includes("process") || message.includes("parse")) {
      return "dataProcessing";
    }
    if (message.includes("state") || message.includes("store") || message.includes("reactive")) {
      return "stateManagement";
    }
    if (message.includes("event") || message.includes("handler") || message.includes("listener")) {
      return "eventHandling";
    }
    if (message.includes("performance") || message.includes("timeout") || message.includes("memory")) {
      return "performance";
    }
    if (message.includes("consistency") || message.includes("validation")) {
      return "consistency";
    }

    return "unknown";
  };

  /**
   * 确定错误严重程度
   */
  const determineErrorSeverity = (error: Error, type: WaveformErrorType): ErrorSeverity => {
    const message = error.message.toLowerCase();

    if (type === "rendering" && message.includes("critical")) return "critical";
    if (type === "dataProcessing" && message.includes("corrupt")) return "critical";
    if (type === "stateManagement") return "high";
    if (message.includes("memory") || message.includes("stack overflow")) return "critical";
    if (message.includes("timeout")) return "medium";

    return "medium";
  };

  /**
   * 默认恢复策略
   */
  const defaultRecoveryStrategies: RecoveryStrategy[] = [
    {
      type: "rendering",
      handler: async (_error, context) => {
        try {
          // 重置绘制状态
          if (context.resetDrawState) {
            context.resetDrawState();
          }

          // 清除缓存
          if (context.clearAllCache) {
            context.clearAllCache();
          }

          // 强制重绘
          if (context.smartDrawWaveform) {
            await new Promise((resolve) => {
              setTimeout(() => {
                context.smartDrawWaveform(true);
                resolve(true);
              }, 100);
            });
          }

          return true;
        } catch (recoveryError) {
          waveformLogger.warn("Rendering recovery failed:", recoveryError);
          return false;
        }
      },
      maxAttempts: 3,
      retryDelay: 500,
    },
    {
      type: "stateManagement",
      handler: async (_error, _context) => {
        try {
          // 重置选中状态
          waveformStore.selectEvent(null);
          waveformStore.selectCurvePoint(-1);

          // 重置调整状态
          if (waveformStore.setAdjustingProperties) {
            waveformStore.setAdjustingProperties(false);
          }

          return true;
        } catch (recoveryError) {
          waveformLogger.warn("State management recovery failed:", recoveryError);
          return false;
        }
      },
      maxAttempts: 2,
      retryDelay: 200,
    },
    {
      type: "dataProcessing",
      handler: async (_error, context) => {
        try {
          // 验证数据完整性
          if (context.performConsistencyCheck) {
            const result = context.performConsistencyCheck(context.events || []);
            return result.isConsistent;
          }

          return true;
        } catch (recoveryError) {
          waveformLogger.warn("Data processing recovery failed:", recoveryError);
          return false;
        }
      },
      maxAttempts: 2,
      retryDelay: 300,
    },
  ];

  /**
   * 合并恢复策略
   */
  const allRecoveryStrategies = [...defaultRecoveryStrategies, ...recoveryStrategies];

  /**
   * 记录错误
   */
  const recordError = (error: Error, type?: WaveformErrorType, context?: any): WaveformError => {
    const errorType = type || determineErrorType(error, context);
    const severity = determineErrorSeverity(error, errorType);

    const waveformError: WaveformError = {
      id: generateErrorId(),
      type: errorType,
      severity,
      message: error.message,
      stack: error.stack,
      context,
      timestamp: Date.now(),
      recovered: false,
      recoveryAttempts: 0,
      maxRecoveryAttempts: allRecoveryStrategies.find((s) => s.type === errorType)?.maxAttempts || 1,
    };

    // 更新统计
    errorStats.value.totalErrors++;
    errorStats.value.errorsByType[errorType]++;
    errorStats.value.lastErrorTime = waveformError.timestamp;

    if (severity === "critical") {
      errorStats.value.criticalErrors++;
    }

    // 添加到历史
    errorHistory.value.push(waveformError);
    if (errorHistory.value.length > maxErrorHistory) {
      errorHistory.value.shift();
    }

    currentError.value = waveformError;

    // 触发错误回调
    if (onError) {
      try {
        onError(waveformError);
      } catch (callbackError) {
        waveformLogger.warn("Error callback failed:", callbackError);
      }
    }

    if (enableLogging) {
      waveformLogger.error(`${errorType} error:`, {
        id: waveformError.id,
        message: waveformError.message,
        severity: waveformError.severity,
        context: waveformError.context,
      });
    }

    return waveformError;
  };

  /**
   * 尝试恢复错误
   */
  const attemptRecovery = async (waveformError: WaveformError, context: any): Promise<boolean> => {
    if (!enableAutoRecovery || waveformError.recoveryAttempts >= waveformError.maxRecoveryAttempts) {
      return false;
    }

    const strategy = allRecoveryStrategies.find((s) => s.type === waveformError.type);
    if (!strategy) {
      return false;
    }

    isRecovering.value = true;
    recoveryProgress.value = 0;
    waveformError.recoveryAttempts++;

    try {
      const startTime = performance.now();

      // 添加延迟
      if (strategy.retryDelay > 0) {
        await new Promise((resolve) => setTimeout(resolve, strategy.retryDelay));
      }

      recoveryProgress.value = 50;

      // 执行恢复策略
      const recovered = await strategy.handler(waveformError, context);

      recoveryProgress.value = 100;

      if (recovered) {
        waveformError.recovered = true;
        errorStats.value.recoveredErrors++;

        const recoveryTime = performance.now() - startTime;
        errorStats.value.averageRecoveryTime =
          (errorStats.value.averageRecoveryTime * (errorStats.value.recoveredErrors - 1) + recoveryTime) / errorStats.value.recoveredErrors;

        if (onRecovery) {
          try {
            onRecovery(waveformError);
          } catch (callbackError) {
            waveformLogger.warn("Recovery callback failed:", callbackError);
          }
        }

        if (enableLogging) {
          waveformLogger.info(`Successfully recovered from ${waveformError.type} error:`, {
            id: waveformError.id,
            attempts: waveformError.recoveryAttempts,
            recoveryTime: recoveryTime.toFixed(2) + "ms",
          });
        }

        currentError.value = null;
      }

      return recovered;
    } catch (recoveryError) {
      if (enableLogging) {
        waveformLogger.warn(`Recovery attempt failed:`, recoveryError);
      }
      return false;
    } finally {
      isRecovering.value = false;
      recoveryProgress.value = 0;
    }
  };

  /**
   * 处理错误
   */
  const handleError = async (error: Error, type?: WaveformErrorType, context?: any): Promise<boolean> => {
    const waveformError = recordError(error, type, context);

    if (enableAutoRecovery) {
      return await attemptRecovery(waveformError, context);
    }

    return false;
  };

  /**
   * 清除错误历史
   */
  const clearErrorHistory = () => {
    errorHistory.value = [];
    currentError.value = null;
    errorStats.value = {
      totalErrors: 0,
      recoveredErrors: 0,
      criticalErrors: 0,
      errorsByType: {
        rendering: 0,
        dataProcessing: 0,
        stateManagement: 0,
        eventHandling: 0,
        performance: 0,
        consistency: 0,
        unknown: 0,
      },
      lastErrorTime: 0,
      averageRecoveryTime: 0,
    };
  };

  /**
   * 获取错误统计
   */
  const getErrorStats = () => {
    const stats = errorStats.value;
    return {
      ...stats,
      recoveryRate: stats.totalErrors > 0 ? ((stats.recoveredErrors / stats.totalErrors) * 100).toFixed(2) + "%" : "0%",
      criticalErrorRate: stats.totalErrors > 0 ? ((stats.criticalErrors / stats.totalErrors) * 100).toFixed(2) + "%" : "0%",
      timeSinceLastError: stats.lastErrorTime > 0 ? Date.now() - stats.lastErrorTime : 0,
    };
  };

  // Vue错误捕获
  onErrorCaptured((error: Error, instance, info) => {
    handleError(error, "unknown", { instance, info });
    return false; // 阻止错误继续传播
  });

  return {
    // 主要功能
    handleError,
    recordError,
    attemptRecovery,

    // 状态访问
    currentError: computed(() => currentError.value),
    errorHistory: computed(() => errorHistory.value),
    isRecovering: computed(() => isRecovering.value),
    recoveryProgress: computed(() => recoveryProgress.value),

    // 统计信息
    getErrorStats,
    errorStats: computed(() => getErrorStats()),

    // 管理功能
    clearErrorHistory,

    // 配置信息
    config: computed(() => ({
      enableAutoRecovery,
      enableLogging,
      maxErrorHistory,
      strategiesCount: allRecoveryStrategies.length,
    })),
  };
}
