{"allUsedKeys": ["about.buildInfo", "about.loading", "about.platform", "about.title", "about.updateCheck.checkFailed", "about.updateCheck.checkNow", "about.updateCheck.checking", "about.updateCheck.latestVersion", "about.updateCheck.newVersionAvailable", "about.updateCheck.title", "about.updateCheck.upToDate", "about.updateCheck.viewUpdate", "about.version", "app.title", "common.about", "common.cancel", "common.cancelled", "common.clear", "common.close", "common.confirm", "common.delete", "common.disabled", "common.enabled", "common.error", "common.loading", "common.save", "dashboard.projects.exampleProjects", "dashboard.projects.goToLearning", "dashboard.projects.newProject", "dashboard.projects.newProjectSubtitle", "dashboard.projects.noRecentProjects", "dashboard.projects.openProject", "dashboard.projects.openProjectSubtitle", "dashboard.projects.recentProjects", "dashboard.tabs.learning", "dashboard.tabs.projects", "debug.activated", "debug.button.tooltip", "debug.errors.exportFailed", "debug.errors.openFolderFailed", "debug.hasRecentProjects", "debug.info", "debug.isLoading", "debug.logViewer.autoRefreshOff", "debug.logViewer.autoRefreshOn", "debug.logViewer.clear", "debug.logViewer.clearSuccess", "debug.logViewer.errors.clearFailed", "debug.logViewer.errors.exportFailed", "debug.logViewer.errors.getPathFailed", "debug.logViewer.errors.readFailed", "debug.logViewer.export", "debug.logViewer.lines", "debug.logViewer.loading", "debug.logViewer.logPath", "debug.logViewer.maxLines", "debug.logViewer.noLogs", "debug.logViewer.refresh", "debug.logViewer.size", "debug.logViewer.title", "debug.logViewer.unknown", "debug.menu.exportDebugInfo", "debug.menu.openLogFolder", "debug.menu.settings", "debug.menu.viewLogs", "debug.recentProjectsContent", "debug.recentProjectsLength", "debug.settings.buildMode", "debug.settings.currentConfig", "debug.settings.debugDisabled", "debug.settings.debugDisabledDesc", "debug.settings.debugEnabled", "debug.settings.debugEnabledDesc", "debug.settings.enableDebug", "debug.settings.errors.loadFailed", "debug.settings.errors.openFolderFailed", "debug.settings.errors.saveFailed", "debug.settings.logDeviceOperations", "debug.settings.logLevel", "debug.settings.logOtaOperations", "debug.settings.notice.logLocation", "debug.settings.notice.performance", "debug.settings.notice.sessionOnly", "debug.settings.notice.title", "debug.settings.openLogFolder", "debug.settings.resetDefault", "debug.settings.resetSuccess", "debug.settings.saveSuccess", "debug.settings.title", "debug.settings.viewLogs", "debug.title", "device.actions.addTestDevice", "device.actions.connect", "device.actions.disconnect", "device.actions.refresh", "device.actions.remove", "device.actions.rename", "device.actions.scan", "device.actions.sendFile", "device.actions.setDefault", "device.actions.unsetDefault", "device.details.connectionStatus", "device.details.deviceId", "device.details.deviceType", "device.details.lastConnected", "device.details.manufacturer", "device.details.model", "device.filter.connectionStatus", "device.filter.deviceType", "device.filter.searchDevices", "device.management.title", "device.messages.addTestDeviceFailed", "device.messages.addTestDeviceSuccess", "device.messages.connectFailed", "device.messages.connectSuccess", "device.messages.disconnectFailed", "device.messages.disconnectSuccess", "device.messages.initializeFailed", "device.messages.refreshFailed", "device.messages.refreshSuccess", "device.messages.removeFailed", "device.messages.removeSuccess", "device.messages.renameFailed", "device.messages.renameSuccess", "device.messages.scanComplete", "device.messages.scanFailed", "device.messages.sendFileFailed", "device.messages.sendFileInfo", "device.messages.setDefaultFailed", "device.messages.setDefaultSuccess", "device.remove.confirmTitle", "device.rename.deviceName", "device.rename.nameRequired", "device.rename.placeholder", "device.rename.title", "device.status.clickToOpen", "device.status.connected", "device.status.default", "device.status.defaultDevice", "device.status.disconnected", "device.status.error", "device.status.errorDevices", "device.status.noDevices", "device.status.title", "device.status.total", "device.testDevice.manufacturer", "device.testDevice.model", "editor.audio.importSuccess", "editor.audio.importingToRoot", "editor.audio.metadataFailed", "editor.contextMenu.addEvent", "editor.contextMenu.continuousEvent", "editor.contextMenu.deleteEvent", "editor.contextMenu.deleteFile", "editor.contextMenu.deleteGroup", "editor.contextMenu.importAudioFile", "editor.contextMenu.newHapticFile", "editor.contextMenu.newRootGroup", "editor.contextMenu.renameFile", "editor.contextMenu.renameGroup", "editor.contextMenu.transientEvent", "editor.durationPanel.confirm", "editor.durationPanel.hideAudio", "editor.durationPanel.increaseDuration", "editor.durationPanel.milliseconds", "editor.durationPanel.showAudio", "editor.empty.addAudioFile", "editor.empty.addHapticFile", "editor.empty.description", "editor.empty.title", "editor.event.exceedsAudioDuration", "editor.eventProperties.computedAbsoluteFrequency", "editor.eventProperties.computedAbsoluteIntensity", "editor.eventProperties.continuousEvent", "editor.eventProperties.duration", "editor.eventProperties.firstLastPointZeroIntensity", "editor.eventProperties.frequency", "editor.eventProperties.frequencyAdjustmentHint", "editor.eventProperties.globalFrequency", "editor.eventProperties.globalIntensity", "editor.eventProperties.intensity", "editor.eventProperties.pointNumber", "editor.eventProperties.pointRelativeFrequency", "editor.eventProperties.pointRelativeIntensity", "editor.eventProperties.selectEventToAdjust", "editor.eventProperties.selectedCurvePoint", "editor.eventProperties.startTime", "editor.eventProperties.time", "editor.eventProperties.title", "editor.eventProperties.transientEvent", "editor.file.confirmDelete", "editor.file.createSuccess", "editor.file.noEventData", "editor.file.noFileSelected", "editor.file.saveSuccess", "editor.file.selectFirst", "editor.hapticFiles.title", "editor.navigation.doubleClickToEdit", "editor.navigation.projects", "editor.navigation.unsavedChanges", "editor.project.noProjectLoaded", "editor.project.renameFailed", "editor.project.renameSuccess", "editor.project.untitled", "editor.waveform.loading", "editor.waveform.noFileSelected", "errors.fileNotFound", "errors.networkError", "errors.operationFailed", "errors.unknown", "examples.notImplemented", "examples.populationOne.haptics", "forceUpdate.exitApplication", "forceUpdate.installNow", "forceUpdate.newVersionRequired", "forceUpdate.notice", "forceUpdate.noticeMessage", "forceUpdate.readyToInstall", "forceUpdate.retryDownload", "forceUpdate.startDownload", "forceUpdate.title", "i18nTest.commonTextTest", "i18nTest.currentLanguage", "i18nTest.errorMessageTest", "i18nTest.exampleError", "i18nTest.labels.cancel", "i18nTest.labels.confirm", "i18nTest.labels.delete", "i18nTest.labels.fileNotFound", "i18nTest.labels.loading", "i18nTest.labels.networkError", "i18nTest.labels.save", "i18nTest.labels.saveFailedExample", "i18nTest.labels.unknownError", "i18nTest.languageDetectionInfo", "i18nTest.languageSwitchTest", "i18nTest.title", "installer.cancelled", "installer.cancelling", "installer.completed", "installer.failed", "installer.initializing", "installer.installCancelled", "installer.installSuccess", "installer.installing", "installer.operations.backingUp", "installer.operations.cancelled", "installer.operations.completed", "installer.operations.failed", "installer.operations.initializing", "installer.operations.installing", "installer.operations.restarting", "installer.operations.validating", "installer.operations.waitingForExit", "installer.preparing", "installer.preparingExit", "installer.ready", "installer.success", "installer.unknownError", "learning.advanced.description", "learning.advanced.tags.advanced", "learning.advanced.tags.tutorial", "learning.advanced.title", "learning.audioToHaptics.description", "learning.audioToHaptics.tags.intermediate", "learning.audioToHaptics.tags.tutorial", "learning.audioToHaptics.title", "learning.gaming.description", "learning.gaming.tags.caseStudy", "learning.gaming.tags.gaming", "learning.gaming.title", "learning.gettingStarted.description", "learning.gettingStarted.tags.beginner", "learning.gettingStarted.tags.tutorial", "learning.gettingStarted.title", "learning.research.description", "learning.research.tags.academic", "learning.research.tags.research", "learning.research.title", "learning.title", "learning.uxDesign.description", "learning.uxDesign.tags.mobile", "learning.uxDesign.tags.ux", "learning.uxDesign.title", "ota.checkingUpdates", "ota.closingProcesses", "ota.downloadFailed", "ota.downloadingUpdate", "ota.installationCompleted", "ota.installationFailed", "ota.installingUpdate", "ota.noDownloadedFile", "ota.noUpdateInfo", "ota.noUpdatesAvailable", "ota.preparingInstallation", "ota.verificationFailed", "ota.verifyingUpdate", "project.create.failed", "project.create.success", "project.open.failed", "project.open.notFound", "project.redo.noFileSelected", "project.redo.noRedoAvailable", "project.redo.redoAction", "project.save.noChanges", "project.save.noData", "project.save.noFileSelected", "project.save.saveFile", "project.save.saving", "project.save.success", "project.undo.noFileSelected", "project.undo.noUndoAvailable", "project.undo.undoAction", "update.applicationWillClose", "update.confirmInstall", "update.confirmInstallation", "update.currentVersion", "update.downloadComplete", "update.downloadCompleteMessage", "update.downloadNow", "update.downloading", "update.error", "update.fileSize", "update.installNow", "update.installationNotice", "update.installing", "update.installingMessage", "update.latestVersion", "update.newVersionAvailable", "update.readyToInstall", "update.releaseNotes", "update.remindLater", "update.updateAvailable"], "keysByCategory": {"about": ["about.buildInfo", "about.loading", "about.platform", "about.title", "about.updateCheck.checkFailed", "about.updateCheck.checkNow", "about.updateCheck.checking", "about.updateCheck.latestVersion", "about.updateCheck.newVersionAvailable", "about.updateCheck.title", "about.updateCheck.upToDate", "about.updateCheck.viewUpdate", "about.version"], "app": ["app.title"], "common": ["common.about", "common.cancel", "common.cancelled", "common.clear", "common.close", "common.confirm", "common.delete", "common.disabled", "common.enabled", "common.error", "common.loading", "common.save"], "dashboard": ["dashboard.projects.exampleProjects", "dashboard.projects.goToLearning", "dashboard.projects.newProject", "dashboard.projects.newProjectSubtitle", "dashboard.projects.noRecentProjects", "dashboard.projects.openProject", "dashboard.projects.openProjectSubtitle", "dashboard.projects.recentProjects", "dashboard.tabs.learning", "dashboard.tabs.projects"], "debug": ["debug.activated", "debug.button.tooltip", "debug.errors.exportFailed", "debug.errors.openFolderFailed", "debug.hasRecentProjects", "debug.info", "debug.isLoading", "debug.logViewer.autoRefreshOff", "debug.logViewer.autoRefreshOn", "debug.logViewer.clear", "debug.logViewer.clearSuccess", "debug.logViewer.errors.clearFailed", "debug.logViewer.errors.exportFailed", "debug.logViewer.errors.getPathFailed", "debug.logViewer.errors.readFailed", "debug.logViewer.export", "debug.logViewer.lines", "debug.logViewer.loading", "debug.logViewer.logPath", "debug.logViewer.maxLines", "debug.logViewer.noLogs", "debug.logViewer.refresh", "debug.logViewer.size", "debug.logViewer.title", "debug.logViewer.unknown", "debug.menu.exportDebugInfo", "debug.menu.openLogFolder", "debug.menu.settings", "debug.menu.viewLogs", "debug.recentProjectsContent", "debug.recentProjectsLength", "debug.settings.buildMode", "debug.settings.currentConfig", "debug.settings.debugDisabled", "debug.settings.debugDisabledDesc", "debug.settings.debugEnabled", "debug.settings.debugEnabledDesc", "debug.settings.enableDebug", "debug.settings.errors.loadFailed", "debug.settings.errors.openFolderFailed", "debug.settings.errors.saveFailed", "debug.settings.logDeviceOperations", "debug.settings.logLevel", "debug.settings.logOtaOperations", "debug.settings.notice.logLocation", "debug.settings.notice.performance", "debug.settings.notice.sessionOnly", "debug.settings.notice.title", "debug.settings.openLogFolder", "debug.settings.resetDefault", "debug.settings.resetSuccess", "debug.settings.saveSuccess", "debug.settings.title", "debug.settings.viewLogs", "debug.title"], "device": ["device.actions.addTestDevice", "device.actions.connect", "device.actions.disconnect", "device.actions.refresh", "device.actions.remove", "device.actions.rename", "device.actions.scan", "device.actions.sendFile", "device.actions.setDefault", "device.actions.unsetDefault", "device.details.connectionStatus", "device.details.deviceId", "device.details.deviceType", "device.details.lastConnected", "device.details.manufacturer", "device.details.model", "device.filter.connectionStatus", "device.filter.deviceType", "device.filter.searchDevices", "device.management.title", "device.messages.addTestDeviceFailed", "device.messages.addTestDeviceSuccess", "device.messages.connectFailed", "device.messages.connectSuccess", "device.messages.disconnectFailed", "device.messages.disconnectSuccess", "device.messages.initializeFailed", "device.messages.refreshFailed", "device.messages.refreshSuccess", "device.messages.removeFailed", "device.messages.removeSuccess", "device.messages.renameFailed", "device.messages.renameSuccess", "device.messages.scanComplete", "device.messages.scanFailed", "device.messages.sendFileFailed", "device.messages.sendFileInfo", "device.messages.setDefaultFailed", "device.messages.setDefaultSuccess", "device.remove.confirmTitle", "device.rename.deviceName", "device.rename.nameRequired", "device.rename.placeholder", "device.rename.title", "device.status.clickToOpen", "device.status.connected", "device.status.default", "device.status.defaultDevice", "device.status.disconnected", "device.status.error", "device.status.errorDevices", "device.status.noDevices", "device.status.title", "device.status.total", "device.testDevice.manufacturer", "device.testDevice.model"], "editor": ["editor.audio.importSuccess", "editor.audio.importingToRoot", "editor.audio.metadataFailed", "editor.contextMenu.addEvent", "editor.contextMenu.continuousEvent", "editor.contextMenu.deleteEvent", "editor.contextMenu.deleteFile", "editor.contextMenu.deleteGroup", "editor.contextMenu.importAudioFile", "editor.contextMenu.newHapticFile", "editor.contextMenu.newRootGroup", "editor.contextMenu.renameFile", "editor.contextMenu.renameGroup", "editor.contextMenu.transientEvent", "editor.durationPanel.confirm", "editor.durationPanel.hideAudio", "editor.durationPanel.increaseDuration", "editor.durationPanel.milliseconds", "editor.durationPanel.showAudio", "editor.empty.addAudioFile", "editor.empty.addHapticFile", "editor.empty.description", "editor.empty.title", "editor.event.exceedsAudioDuration", "editor.eventProperties.computedAbsoluteFrequency", "editor.eventProperties.computedAbsoluteIntensity", "editor.eventProperties.continuousEvent", "editor.eventProperties.duration", "editor.eventProperties.firstLastPointZeroIntensity", "editor.eventProperties.frequency", "editor.eventProperties.frequencyAdjustmentHint", "editor.eventProperties.globalFrequency", "editor.eventProperties.globalIntensity", "editor.eventProperties.intensity", "editor.eventProperties.pointNumber", "editor.eventProperties.pointRelativeFrequency", "editor.eventProperties.pointRelativeIntensity", "editor.eventProperties.selectEventToAdjust", "editor.eventProperties.selectedCurvePoint", "editor.eventProperties.startTime", "editor.eventProperties.time", "editor.eventProperties.title", "editor.eventProperties.transientEvent", "editor.file.confirmDelete", "editor.file.createSuccess", "editor.file.noEventData", "editor.file.noFileSelected", "editor.file.saveSuccess", "editor.file.selectFirst", "editor.hapticFiles.title", "editor.navigation.doubleClickToEdit", "editor.navigation.projects", "editor.navigation.unsavedChanges", "editor.project.noProjectLoaded", "editor.project.renameFailed", "editor.project.renameSuccess", "editor.project.untitled", "editor.waveform.loading", "editor.waveform.noFileSelected"], "errors": ["errors.fileNotFound", "errors.networkError", "errors.operationFailed", "errors.unknown"], "examples": ["examples.notImplemented", "examples.populationOne.haptics"], "forceUpdate": ["forceUpdate.exitApplication", "forceUpdate.installNow", "forceUpdate.newVersionRequired", "forceUpdate.notice", "forceUpdate.noticeMessage", "forceUpdate.readyToInstall", "forceUpdate.retryDownload", "forceUpdate.startDownload", "forceUpdate.title"], "i18nTest": ["i18nTest.commonTextTest", "i18nTest.currentLanguage", "i18nTest.errorMessageTest", "i18nTest.exampleError", "i18nTest.labels.cancel", "i18nTest.labels.confirm", "i18nTest.labels.delete", "i18nTest.labels.fileNotFound", "i18nTest.labels.loading", "i18nTest.labels.networkError", "i18nTest.labels.save", "i18nTest.labels.saveFailedExample", "i18nTest.labels.unknownError", "i18nTest.languageDetectionInfo", "i18nTest.languageSwitchTest", "i18nTest.title"], "installer": ["installer.cancelled", "installer.cancelling", "installer.completed", "installer.failed", "installer.initializing", "installer.installCancelled", "installer.installSuccess", "installer.installing", "installer.operations.backingUp", "installer.operations.cancelled", "installer.operations.completed", "installer.operations.failed", "installer.operations.initializing", "installer.operations.installing", "installer.operations.restarting", "installer.operations.validating", "installer.operations.waitingForExit", "installer.preparing", "installer.preparingExit", "installer.ready", "installer.success", "installer.unknownError"], "learning": ["learning.advanced.description", "learning.advanced.tags.advanced", "learning.advanced.tags.tutorial", "learning.advanced.title", "learning.audioToHaptics.description", "learning.audioToHaptics.tags.intermediate", "learning.audioToHaptics.tags.tutorial", "learning.audioToHaptics.title", "learning.gaming.description", "learning.gaming.tags.caseStudy", "learning.gaming.tags.gaming", "learning.gaming.title", "learning.gettingStarted.description", "learning.gettingStarted.tags.beginner", "learning.gettingStarted.tags.tutorial", "learning.gettingStarted.title", "learning.research.description", "learning.research.tags.academic", "learning.research.tags.research", "learning.research.title", "learning.title", "learning.uxDesign.description", "learning.uxDesign.tags.mobile", "learning.uxDesign.tags.ux", "learning.uxDesign.title"], "ota": ["ota.checkingUpdates", "ota.closingProcesses", "ota.downloadFailed", "ota.downloadingUpdate", "ota.installationCompleted", "ota.installationFailed", "ota.installingUpdate", "ota.noDownloadedFile", "ota.noUpdateInfo", "ota.noUpdatesAvailable", "ota.preparingInstallation", "ota.verificationFailed", "ota.verifyingUpdate"], "project": ["project.create.failed", "project.create.success", "project.open.failed", "project.open.notFound", "project.redo.noFileSelected", "project.redo.noRedoAvailable", "project.redo.redoAction", "project.save.noChanges", "project.save.noData", "project.save.noFileSelected", "project.save.saveFile", "project.save.saving", "project.save.success", "project.undo.noFileSelected", "project.undo.noUndoAvailable", "project.undo.undoAction"], "update": ["update.applicationWillClose", "update.confirmInstall", "update.confirmInstallation", "update.currentVersion", "update.downloadComplete", "update.downloadCompleteMessage", "update.downloadNow", "update.downloading", "update.error", "update.fileSize", "update.installNow", "update.installationNotice", "update.installing", "update.installingMessage", "update.latestVersion", "update.newVersionAvailable", "update.readyToInstall", "update.releaseNotes", "update.remindLater", "update.updateAvailable"]}, "updateKeys": ["update.applicationWillClose", "update.confirmInstall", "update.confirmInstallation", "update.currentVersion", "update.downloadComplete", "update.downloadCompleteMessage", "update.downloadNow", "update.downloading", "update.error", "update.fileSize", "update.installNow", "update.installationNotice", "update.installing", "update.installingMessage", "update.latestVersion", "update.newVersionAvailable", "update.readyToInstall", "update.releaseNotes", "update.remindLater", "update.updateAvailable"], "aboutKeys": ["about.buildInfo", "about.loading", "about.platform", "about.title", "about.updateCheck.checkFailed", "about.updateCheck.checkNow", "about.updateCheck.checking", "about.updateCheck.latestVersion", "about.updateCheck.newVersionAvailable", "about.updateCheck.title", "about.updateCheck.upToDate", "about.updateCheck.viewUpdate", "about.version"], "fileKeyMap": {}}