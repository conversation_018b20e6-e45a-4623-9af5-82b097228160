package android.os.vibrator.realitytap.he;

import android.annotation.NonNull;
import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/** @hide */
public class PatternListItem implements Parcelable {
    /** Pattern 列表项的绝对时间，单位毫秒 */
    private int absoluteTime;

    /** Pattern 列表项的序号 */
    private int patternIndex;

    /** Pattern 列表项的事件列表 */
    private final List<Event> patterns = new ArrayList<>();

    public PatternListItem() {}

    protected PatternListItem(Parcel in) {
        absoluteTime = in.readInt();
        patternIndex = in.readInt();
        patterns.addAll(in.createTypedArrayList(Event.CREATOR));
    }

    public static final @NonNull Creator<PatternListItem> CREATOR = new Creator<>() {
        @Override
        public PatternListItem createFromParcel(@NonNull Parcel in) {
            return new PatternListItem(in);
        }

        @Override
        public PatternListItem[] newArray(int size) {
            return new PatternListItem[size];
        }
    };

    public int getAbsoluteTime() {
        return absoluteTime;
    }

    public void setAbsoluteTime(int absoluteTime) {
        this.absoluteTime = absoluteTime;
    }

    public int getPatternIndex() {
        return patternIndex;
    }

    public void setPatternIndex(int patternIndex) {
        this.patternIndex = patternIndex;
    }

    public List<Event> getPatterns() {
        return patterns;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(absoluteTime);
        dest.writeInt(patternIndex);
        dest.writeTypedList(patterns);
    }

    @NonNull
    public PatternListItem copy() {
        PatternListItem copy = new PatternListItem();
        copy.setAbsoluteTime(absoluteTime);
        copy.setPatternIndex(patternIndex);
        for (Event event : patterns) {
            copy.getPatterns().add(event.copy());
        }
        return copy;
    }

    @NonNull
    @Override
    public String toString() {
        return "PatternListItem{" +
                "absoluteTime=" + absoluteTime +
                ", patternIndex=" + patternIndex +
                ", patterns=" + patterns +
                '}';
    }
}
