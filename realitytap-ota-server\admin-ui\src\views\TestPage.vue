<template>
  <div class="test-page">
    <h1>🧪 测试页面</h1>
    <p>如果您能看到这个页面，说明Vue应用基本功能正常。</p>
    
    <div class="test-section">
      <h2>基本信息</h2>
      <ul>
        <li>当前时间: {{ currentTime }}</li>
        <li>页面路径: {{ currentPath }}</li>
        <li>用户代理: {{ userAgent }}</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>本地存储检查</h2>
      <ul>
        <li>Token存在: {{ !!token }}</li>
        <li>User存在: {{ !!user }}</li>
        <li>Theme设置: {{ theme }}</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>操作测试</h2>
      <button @click="testAlert">测试弹窗</button>
      <button @click="testConsole">测试控制台</button>
      <button @click="testLocalStorage">测试本地存储</button>
    </div>

    <div class="test-section">
      <h2>导航测试</h2>
      <button @click="goToLogin">跳转到登录页</button>
      <button @click="goToDashboard">跳转到仪表板</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const currentTime = ref('');
const currentPath = ref('');
const userAgent = ref('');
const token = ref('');
const user = ref('');
const theme = ref('');

onMounted(() => {
  updateInfo();
  
  // 每秒更新时间
  setInterval(updateInfo, 1000);
});

const updateInfo = () => {
  currentTime.value = new Date().toLocaleString();
  currentPath.value = window.location.pathname;
  userAgent.value = navigator.userAgent;
  token.value = localStorage.getItem('admin_token') || '';
  user.value = localStorage.getItem('admin_user') || '';
  theme.value = localStorage.getItem('theme') || '';
};

const testAlert = () => {
  alert('测试弹窗正常工作！');
};

const testConsole = () => {
  console.log('🧪 测试控制台输出');
  console.warn('⚠️ 测试警告信息');
  console.error('❌ 测试错误信息');
};

const testLocalStorage = () => {
  const testKey = 'test_key';
  const testValue = 'test_value_' + Date.now();
  
  try {
    localStorage.setItem(testKey, testValue);
    const retrieved = localStorage.getItem(testKey);
    
    if (retrieved === testValue) {
      alert('本地存储测试成功！');
    } else {
      alert('本地存储测试失败：值不匹配');
    }
    
    localStorage.removeItem(testKey);
  } catch (error) {
    alert('本地存储测试失败：' + error.message);
  }
};

const goToLogin = () => {
  router.push('/login');
};

const goToDashboard = () => {
  router.push('/');
};
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: monospace;
}

.test-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background: #f9f9f9;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.test-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-section li {
  margin: 5px 0;
  word-break: break-all;
}

button {
  margin: 5px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style>
