<template>
  <div class="home-view">
    <main class="main-content">
      <div class="nav-tabs">
        <div class="tab active" @click="router.push('/')">{{ t('dashboard.tabs.projects') }}</div>
        <div class="tab" @click="router.push('/learning')">{{ t('dashboard.tabs.learning') }}</div>
        <!-- 开发工具标签（仅开发环境） -->
        <div v-if="isDevelopment" class="tab dev-tab" @click="router.push('/dev-tools')">
          🛠️ 开发工具
        </div>
      </div>

      <div class="projects-section">
        <div class="projects-grid">
          <div class="project-card new-project" @click="createNewProject">
            <div class="card-icon">+</div>
            <div class="card-content">
              <div class="card-title">{{ t('dashboard.projects.newProject') }}</div>
              <div class="card-subtitle">{{ t('dashboard.projects.newProjectSubtitle') }}</div>
            </div>
          </div>

          <div class="project-card open-project" @click="openProject">
            <div class="card-icon">📁</div>
            <div class="card-content">
              <div class="card-title">{{ t('dashboard.projects.openProject') }}</div>
              <div class="card-subtitle">{{ t('dashboard.projects.openProjectSubtitle') }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="divider"></div>

      <div class="recent-projects">
        <div class="header-with-action">
          <h2>{{ t('dashboard.projects.recentProjects') }}</h2>
          <button
            v-if="hasRecentProjects"
            class="clear-btn"
            @click="clearRecentProjects"
          >
            {{ t('common.clear') }}
          </button>
        </div>

        <!-- Skeleton 占位 -->
        <n-skeleton v-if="isLoading || recentProjects === undefined" :repeat="3" style="height: 80px; margin-bottom: 16px; border-radius: 8px;" />

        <!-- 简化条件渲染逻辑 -->
        <div v-else>
          <!-- 有项目时显示列表 -->
          <div
            v-if="recentProjects && recentProjects.length > 0"
            class="recent-grid"
          >
            <div
              v-for="(project, index) in recentProjects"
              :key="index"
              class="recent-card"
              @click="openRecentProject(project)"
            >
              <div class="recent-icon">{{ project.icon }}</div>
              <div class="recent-content">
                <div class="recent-title">{{ project.projectName }}</div>
                <div class="recent-path">{{ project.projectPath }}</div>
                <div class="recent-meta">
                  <span>{{ formatTime(project.lastAccessed) }}</span>
                  <span>{{ project.fileCount }} files</span>
                </div>
              </div>
              <button
                class="remove-btn"
                @click.stop="removeProject(project.projectPath)"
              >
                ×
              </button>
            </div>
          </div>

          <!-- 没有项目时显示空状态 -->
          <div v-else class="empty-recent">
            <div class="empty-icon">📂</div>
            <p>{{ t('dashboard.projects.noRecentProjects') }}</p>
            <button class="go-learning-btn" @click="router.push('/learning')">
              {{ t('dashboard.projects.goToLearning') }}
            </button>
          </div>
        </div>

        <!-- 添加调试信息 -->
        <div
          class="debug-info"
          style="
            padding: 10px;
            margin-top: 20px;
            background-color: #333;
            border-radius: 8px;
          "
        >
          <p>{{ t('debug.info') }}</p>
          <pre>{{ t('debug.isLoading') }}: {{ isLoading }}</pre>
          <pre>{{ t('debug.hasRecentProjects') }}: {{ hasRecentProjects }}</pre>
          <pre>{{ t('debug.recentProjectsLength') }}: {{ recentProjects?.length || 0 }}</pre>
          <pre>
{{ t('debug.recentProjectsContent') }}: {{ JSON.stringify(recentProjects, null, 2) }}</pre
          >
        </div>
      </div>

      <div class="example-projects">
        <h2>{{ t('dashboard.projects.exampleProjects') }}</h2>
        <div class="examples-grid">
          <div
            class="example-card"
            v-for="(project, index) in exampleProjects"
            :key="index"
            @click="openExampleProject(project)"
          >
            <div class="example-content">
              <div class="example-title">
                {{ project.title }}
                <span class="new-tag" v-if="project.isNew">NEW</span>
              </div>
              <div class="example-haptics">{{ project.haptics }} {{ t('examples.populationOne.haptics').split('个')[1] || 'Haptics' }}</div>
            </div>
            <div class="example-icon">{{ project.icon }}</div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { invoke } from "@tauri-apps/api/core";
import { open } from "@tauri-apps/plugin-dialog";
import { basename, sep } from "@tauri-apps/api/path";
import { useNotification, NSkeleton } from "naive-ui";
import {
  useRecentProjectsStore,
  type RecentProject,
} from "@/stores/recent-projects-store";
import { useProjectStore } from "@/stores/haptics-project-store";
import type { RealityTapProject } from "@/types/haptic-project";
import { storeToRefs } from "pinia";
import {
  parseErrorMessage,
  refreshProjectDirectory,
} from "@/utils/commonUtils";
import { useI18n } from "@/composables/useI18n";
import { logger, LogModule } from "@/utils/logger/logger";

const router = useRouter();
const recentProjectsStore = useRecentProjectsStore();
const projectStore = useProjectStore();
const notification = useNotification();
const { t } = useI18n();
// 使用 storeToRefs 保持响应性
const { recentProjects, isLoading } = storeToRefs(recentProjectsStore);

// 计算属性
const hasRecentProjects = computed(() => {
  return recentProjects.value && recentProjects.value.length > 0;
});

// 检查是否为开发环境
const isDevelopment = computed(() => import.meta.env.DEV);

// 在组件挂载时加载最近项目，并异步刷新文件数
onMounted(async () => {
  await recentProjectsStore.loadRecentProjects();
  // 异步无感刷新每个项目的文件数
  if (recentProjects.value && recentProjects.value.length > 0) {
    recentProjects.value.forEach(async (project, idx) => {
      try {
        const refreshed = await refreshProjectDirectory(project.projectPath);
        // 只在文件数有变化时更新
        if (refreshed.files && refreshed.files.length !== project.fileCount) {
          recentProjects.value[idx].fileCount = refreshed.files.length;
        }
      } catch (e) {
        logger.error(LogModule.GENERAL, "Refresh project directory failed", e);
      }
    });
  }
});

// 格式化时间显示
const formatTime = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.toLocaleDateString("zh-CN");
};

// 打开最近项目
const openRecentProject = async (project: RecentProject) => {
  try {
    // Explicitly type the expected return of invoke
    const loadedProject = await invoke<RealityTapProject>("load_project", {
      projectDirPath: project.projectPath,
    });
    projectStore.setProjectData(loadedProject, project.projectPath);
    router.push("/editor");
  } catch (error) {
    logger.error(LogModule.GENERAL, "Open recent project failed", error);
    const parsedErrorMessage = parseErrorMessage(error);

    // 检查是否为NotFound错误
    const isNotFoundError =
      typeof error === "object" &&
      error !== null &&
      ("NotFound" in error ||
        parsedErrorMessage.includes("NotFound") ||
        parsedErrorMessage.includes("找不到") ||
        parsedErrorMessage.includes("不存在"));

    if (isNotFoundError) {
      // 如果项目不存在，则从列表中移除
      setTimeout(() => {
        removeProject(project.projectPath);
      }, 1000);
      notification.warning({
        title: t('project.open.notFound'),
        content: t('project.open.notFoundMessage', { name: project.projectName }),
        duration: 5000,
        closable: true,
      });
    } else {
      // 其他错误保持原有处理逻辑
      notification.error({
        title: t('project.open.failed'),
        content: t('project.open.failedMessage', { error: parsedErrorMessage }),
        duration: 5000,
        closable: true,
      });
    }
  }
};

// 从列表中移除项目
const removeProject = (projectPath: string) => {
  recentProjectsStore.removeRecentProject(projectPath);
};

// 清空最近项目列表
const clearRecentProjects = () => {
  recentProjectsStore.clearRecentProjects();
};

const exampleProjects = ref([
  {
    title: "Population: One",
    haptics: 8,
    icon: "🎮",
    isNew: true,
    id: "population_one",
  },
  { title: "Asgard's Wrath II", haptics: 8, icon: "⚔️", id: "asgards_wrath" },
  { title: "Sense Of Touch", haptics: 3, icon: "👆", id: "sense_of_touch" },
  { title: "Application UX", haptics: 9, icon: "📱", id: "application_ux" },
  { title: "Footsteps", haptics: 4, icon: "👣", id: "footsteps" },
  { title: "Impacts", haptics: 3, icon: "💥", id: "impacts" },
  { title: "Music", haptics: 3, icon: "🎵", id: "music" },
  { title: "Nature", haptics: 4, icon: "🌳", id: "nature" },
  { title: "Objects", haptics: 10, icon: "🏀", id: "objects" },
  { title: "Sound FX", haptics: 3, icon: "🔊", id: "sound_fx" },
  { title: "Weapons", haptics: 4, icon: "🔫", id: "weapons" },
]);

// 创建新项目
const createNewProject = async () => {
  try {
    const selectedParentDir = await open({
      directory: true,
      multiple: false,
      title: "Select Parent Directory for New Project",
    });

    if (typeof selectedParentDir !== "string") {
      return;
    }

    // 使用建议的项目名，不再弹窗询问
    const projectName = await basename(selectedParentDir);
    logger.info(LogModule.GENERAL, "Create new project", { projectName });

    // Explicitly type the expected return of invoke
    const createdProject = await invoke<RealityTapProject>(
      "create_new_project",
      {
        projectName: projectName,
        author: "AWA RealityTap Studio", // Consider making these configurable
        description: "", // Consider making these configurable
        targetDir: selectedParentDir,
      }
    );

    const projectDirPath = `${selectedParentDir}${sep}${projectName}`.replace(
      /\\/g,
      "/"
    );

    projectStore.setProjectData(createdProject, projectDirPath);
    await recentProjectsStore.loadRecentProjects();

    router.push("/editor");
  } catch (error) {
    logger.error(LogModule.GENERAL, "Failed to create new project", error);
    const errorMessage = parseErrorMessage(error);

    notification.error({
      title: t('project.create.failed'),
      content: t('project.create.failedMessage', { error: errorMessage }),
      duration: 5000,
      closable: true,
    });
  }
};

// 打开项目
const openProject = async () => {
  try {
    const selected = await open({
      directory: true,
      multiple: false,
      title: "Open Project Directory",
    });

    if (typeof selected === "string") {
      // Explicitly type the expected return of invoke
      const loadedProject = await invoke<RealityTapProject>("load_project", {
        projectDirPath: selected,
      });
      projectStore.setProjectData(loadedProject, selected);
      router.push("/editor");
    } else {
      logger.info(LogModule.GENERAL, "Directory selection cancelled");
    }
  } catch (error) {
    logger.error(LogModule.GENERAL, "Failed to open project directory", error);
    const errorMessage = parseErrorMessage(error);

    notification.error({
      title: t('project.open.failed'),
      content: t('project.open.failedMessage', { error: errorMessage }),
      duration: 5000,
      closable: true,
    });
  }
};

// 打开示例项目
const openExampleProject = (project: any) => {
  notification.info({
    title: t('examples.notImplemented'),
    content: t('examples.notImplementedMessage', { title: project.title }),
    duration: 3000,
    closable: true,
  });
};
</script>

<style scoped>
.home-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-y: auto;
}

.main-content {
  flex: 1;
  padding: 1.25rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.nav-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #333;
}

.tab {
  padding: 0.625rem 1.25rem;
  cursor: pointer;
  font-weight: 500;
  color: #888;
  transition: color 0.2s;
}

.tab:hover {
  color: #e6e6e6;
}

/* 开发工具标签样式 */
.dev-tab {
  background: linear-gradient(135deg, #ff6b6b, #feca57);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  position: relative;
}

.dev-tab::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, #ff6b6b, #feca57);
  opacity: 0;
  transition: opacity 0.2s;
}

.dev-tab:hover::after {
  opacity: 1;
}

.tab.active {
  color: #fff;
  border-bottom: 2px solid #fff;
}

.projects-grid {
  display: flex;
  gap: 1.25rem;
  margin-bottom: 1.875rem;
}

.project-card {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.9375rem;
  background-color: #2a2a2a;
  padding: 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.project-card:hover {
  background-color: #333;
}

.card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: #3b3b3b;
  border-radius: 50%;
  font-size: 1.25rem;
}

.card-title {
  font-weight: 500;
  margin-bottom: 0.3125rem;
}

.card-subtitle {
  font-size: 0.875rem;
  color: #888;
}

.divider {
  height: 1px;
  background-color: #333;
  margin: 1.875rem 0;
}

.recent-projects,
.example-projects {
  margin-top: 1.875rem;
}

.header-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.9375rem;
}

.clear-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 0.75rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
}

.clear-btn:hover {
  color: #e6e6e6;
  text-decoration: underline;
}

h2 {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.9375rem;
}

.recent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 0.9375rem;
  margin-bottom: 1.875rem;
}

.recent-card {
  display: flex;
  align-items: center;
  padding: 0.9375rem;
  background-color: #2a2a2a;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.recent-card:hover {
  background-color: #333;
}

.recent-icon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
}

.recent-content {
  flex: 1;
  overflow: hidden;
}

.recent-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-path {
  font-size: 0.75rem;
  color: #888;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #888;
}

.remove-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.recent-card:hover .remove-btn {
  opacity: 1;
}

.remove-btn:hover {
  color: #e6e6e6;
}

.empty-recent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #2a2a2a;
  border-radius: 8px;
  margin-bottom: 1.875rem;
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-recent p {
  margin-bottom: 1rem;
  color: #888;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  padding: 2rem;
  color: #888;
  background-color: #2a2a2a;
  border-radius: 8px;
  margin-bottom: 1.875rem;
}

.go-learning-btn {
  background-color: rgba(71, 133, 235, 0.7);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.go-learning-btn:hover {
  background-color: rgba(71, 133, 235, 0.9);
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 0.9375rem;
}

.example-card {
  display: flex;
  justify-content: space-between;
  padding: 0.9375rem;
  background-color: #2a2a2a;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.example-card:hover {
  background-color: #333;
}

.example-title {
  font-weight: 500;
  margin-bottom: 0.3125rem;
  display: flex;
  align-items: center;
}

.new-tag {
  background-color: #4785eb;
  color: white;
  font-size: 0.625rem;
  padding: 0.125rem 0.3125rem;
  border-radius: 4px;
  margin-left: 0.5rem;
}

.example-haptics {
  font-size: 0.875rem;
  color: #888;
}

.example-icon {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
}

.debug-info {
  position: fixed;
  bottom: 10px;
  right: 10px;
  background-color: rgba(42, 42, 42, 0.9);
  padding: 10px;
  border-radius: 8px;
  font-size: 0.8rem;
  color: #ccc;
  z-index: 9999;
  display: none;
}

.debug-info h3 {
  margin-top: 0;
  margin-bottom: 5px;
}

.debug-info .warning {
  color: #ff9900;
  margin-top: 5px;
}

.debug-info code {
  background-color: #333;
  padding: 2px 4px;
  border-radius: 4px;
}
</style>
