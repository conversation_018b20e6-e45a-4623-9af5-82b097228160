/**
 * 统一防抖管理器
 * 协调所有组件的更新时序，避免防抖策略不一致造成的卡顿问题
 */

export type DebouncePriority = 'high' | 'normal' | 'low';

interface DebounceTask {
  key: string;
  fn: Function;
  delay: number;
  priority: DebouncePriority;
  timer: ReturnType<typeof setTimeout>;
  createdAt: number;
}

/**
 * 统一防抖管理器
 * 
 * 特性：
 * 1. 支持优先级机制：高优先级任务可以打断低优先级任务
 * 2. 防止重复任务：相同key的任务会被合并
 * 3. 性能监控：记录任务执行时间和频率
 * 4. 自动清理：清理过期的任务引用
 */
export class UnifiedDebounceManager {
  private static instance: UnifiedDebounceManager;
  private tasks = new Map<string, DebounceTask>();
  private executionStats = new Map<string, {
    count: number;
    totalTime: number;
    lastExecuted: number;
  }>();

  // 优先级权重
  private readonly PRIORITY_WEIGHTS = {
    high: 3,
    normal: 2,
    low: 1
  };

  // 默认延迟配置
  private readonly DEFAULT_DELAYS = {
    high: 8,    // 高优先级：约120fps，用于用户直接交互
    normal: 16, // 正常优先级：60fps，用于一般更新
    low: 32     // 低优先级：30fps，用于后台任务
  };

  private constructor() {
    // 定期清理过期的统计数据
    setInterval(() => {
      this.cleanupStats();
    }, 60000); // 每分钟清理一次
  }

  static getInstance(): UnifiedDebounceManager {
    if (!this.instance) {
      this.instance = new UnifiedDebounceManager();
    }
    return this.instance;
  }

  /**
   * 防抖执行函数
   * @param key 唯一标识符
   * @param fn 要执行的函数
   * @param delay 延迟时间（可选，会根据优先级自动选择）
   * @param priority 优先级
   */
  debounce(
    key: string, 
    fn: Function, 
    delay?: number, 
    priority: DebouncePriority = 'normal'
  ): void {
    const actualDelay = delay ?? this.DEFAULT_DELAYS[priority];
    const now = performance.now();

    // 检查是否有现有任务
    const existingTask = this.tasks.get(key);
    
    if (existingTask) {
      // 比较优先级
      const existingWeight = this.PRIORITY_WEIGHTS[existingTask.priority];
      const newWeight = this.PRIORITY_WEIGHTS[priority];
      
      // 如果新任务优先级更高，或者优先级相同，则取消现有任务
      if (newWeight >= existingWeight) {
        clearTimeout(existingTask.timer);
        this.tasks.delete(key);
      } else {
        // 新任务优先级较低，忽略
        return;
      }
    }

    // 创建新任务
    const timer = setTimeout(() => {
      this.executeTask(key, fn, now);
    }, actualDelay);

    const task: DebounceTask = {
      key,
      fn,
      delay: actualDelay,
      priority,
      timer,
      createdAt: now
    };

    this.tasks.set(key, task);
  }

  /**
   * 立即执行任务（跳过防抖）
   * @param key 任务标识符
   * @param fn 要执行的函数
   */
  immediate(key: string, fn: Function): void {
    // 取消现有的防抖任务
    this.cancel(key);
    
    // 立即执行
    const startTime = performance.now();
    this.executeTask(key, fn, startTime);
  }

  /**
   * 取消指定的防抖任务
   * @param key 任务标识符
   */
  cancel(key: string): void {
    const task = this.tasks.get(key);
    if (task) {
      clearTimeout(task.timer);
      this.tasks.delete(key);
    }
  }

  /**
   * 取消所有防抖任务
   */
  cancelAll(): void {
    for (const task of this.tasks.values()) {
      clearTimeout(task.timer);
    }
    this.tasks.clear();
  }

  /**
   * 获取当前活跃任务数量
   */
  getActiveTaskCount(): number {
    return this.tasks.size;
  }

  /**
   * 获取任务执行统计
   * @param key 任务标识符
   */
  getStats(key: string) {
    return this.executionStats.get(key);
  }

  /**
   * 获取所有统计信息
   */
  getAllStats() {
    const stats: Record<string, any> = {};
    for (const [key, stat] of this.executionStats.entries()) {
      stats[key] = {
        ...stat,
        averageTime: stat.count > 0 ? stat.totalTime / stat.count : 0
      };
    }
    return stats;
  }

  /**
   * 执行任务并记录统计信息
   */
  private executeTask(key: string, fn: Function, startTime: number): void {
    try {
      // 执行任务
      fn();
      
      // 记录执行时间
      const executionTime = performance.now() - startTime;
      this.updateStats(key, executionTime);
      
    } catch (error) {
      console.error(`防抖任务执行失败 [${key}]:`, error);
    } finally {
      // 清理任务引用
      this.tasks.delete(key);
    }
  }

  /**
   * 更新执行统计
   */
  private updateStats(key: string, executionTime: number): void {
    const existing = this.executionStats.get(key);
    if (existing) {
      existing.count++;
      existing.totalTime += executionTime;
      existing.lastExecuted = performance.now();
    } else {
      this.executionStats.set(key, {
        count: 1,
        totalTime: executionTime,
        lastExecuted: performance.now()
      });
    }
  }

  /**
   * 清理过期的统计数据
   */
  private cleanupStats(): void {
    const now = performance.now();
    const CLEANUP_THRESHOLD = 5 * 60 * 1000; // 5分钟

    for (const [key, stats] of this.executionStats.entries()) {
      if (now - stats.lastExecuted > CLEANUP_THRESHOLD) {
        this.executionStats.delete(key);
      }
    }
  }
}

/**
 * 便捷的防抖函数，使用全局实例
 */
export function debounce(
  key: string, 
  fn: Function, 
  delay?: number, 
  priority: DebouncePriority = 'normal'
): void {
  UnifiedDebounceManager.getInstance().debounce(key, fn, delay, priority);
}

/**
 * 立即执行函数，使用全局实例
 */
export function immediate(key: string, fn: Function): void {
  UnifiedDebounceManager.getInstance().immediate(key, fn);
}

/**
 * 取消防抖任务，使用全局实例
 */
export function cancelDebounce(key: string): void {
  UnifiedDebounceManager.getInstance().cancel(key);
}

/**
 * 获取防抖管理器实例（用于高级用法）
 */
export function getDebounceManager(): UnifiedDebounceManager {
  return UnifiedDebounceManager.getInstance();
}
