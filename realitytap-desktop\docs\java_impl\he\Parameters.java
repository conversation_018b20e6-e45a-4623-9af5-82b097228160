package android.os.vibrator.realitytap.he;

import android.annotation.NonNull;
import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/** @hide */
public class Parameters implements Parcelable {
    /** Continuous 包络波形全局强度 */
    private int intensity;

    /** Continuous 包络波形全局频率 */
    private int frequency;

    /** Continuous 包络波形曲线点 */
    private final List<Curve> curves = new ArrayList<>();  // 曲线点列表

    public Parameters() {}

    protected Parameters(@NonNull Parcel in) {
        intensity = in.readInt();
        frequency = in.readInt();
        curves.addAll(in.createTypedArrayList(Curve.CREATOR));
    }

    public static final @NonNull Creator<Parameters> CREATOR = new Creator<>() {
        @Override
        public Parameters createFromParcel(@NonNull Parcel in) {
            return new Parameters(in);
        }

        @Override
        public Parameters[] newArray(int size) {
            return new Parameters[size];
        }
    };

    public int getIntensity() {
        return intensity;
    }

    public void setIntensity(int intensity) {
        this.intensity = intensity;
    }

    public int getFrequency() {
        return frequency;
    }

    public void setFrequency(int frequency) {
        this.frequency = frequency;
    }

    public List<Curve> getCurves() {
        return curves;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(intensity);
        dest.writeInt(frequency);
        dest.writeTypedList(curves, flags);
    }

    @NonNull
    public Parameters copy() {
        Parameters copy = new Parameters();
        copy.setIntensity(intensity);
        copy.setFrequency(frequency);
        for (Curve curve : curves) {
            copy.getCurves().add(curve.copy());
        }
        return copy;
    }

    @NonNull
    @Override
    public String toString() {
        return "Parameters{" +
                "intensity=" + intensity +
                ", frequency=" + frequency +
                ", curves=" + curves +
                '}';
    }
}
