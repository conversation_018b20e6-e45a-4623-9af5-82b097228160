/**
 * 统一错误处理工具
 * 提供错误分类、重试策略和用户友好的错误消息
 */

import type {
  UpdateError,
  OTAErrorCode,
  ErrorSeverity,
  RetryStrategy,
  ErrorContext,
  ErrorRecoveryAction,
} from '@realitytap/shared';
import { OTA_ERROR_MESSAGES } from '@realitytap/shared';

/**
 * 错误处理器类
 */
export class ErrorHandler {
  private errorHistory: UpdateError[] = [];
  private maxHistorySize = 50;

  /**
   * 处理错误
   */
  handleError(
    error: Error | UpdateError,
    context?: ErrorContext
  ): UpdateError {
    const updateError = this.normalizeError(error, context);
    this.recordError(updateError);
    return updateError;
  }

  /**
   * 标准化错误对象
   */
  private normalizeError(
    error: Error | UpdateError,
    context?: ErrorContext
  ): UpdateError {
    if (this.isUpdateError(error)) {
      return error;
    }

    // 将普通错误转换为UpdateError
    const code = this.classifyError(error);
    const updateError: UpdateError & { severity?: ErrorSeverity; context?: ErrorContext; originalError?: Error } = {
      code,
      message: error.message,
      timestamp: new Date(),
      retryable: this.isRetryableError(code),
      severity: this.getErrorSeverity(code),
      context,
      originalError: error,
    };

    return updateError;
  }

  /**
   * 检查是否为UpdateError
   */
  private isUpdateError(error: any): error is UpdateError {
    return error && typeof error.code === 'string' && error.timestamp instanceof Date;
  }

  /**
   * 错误分类
   */
  private classifyError(error: Error): OTAErrorCode {
    const message = error.message.toLowerCase();

    // 网络相关错误
    if (message.includes('network') || message.includes('fetch')) {
      return 'NETWORK_ERROR';
    }
    if (message.includes('timeout')) {
      return 'TIMEOUT_ERROR';
    }
    if (message.includes('connection')) {
      return 'CONNECTION_ERROR';
    }

    // 文件相关错误
    if (message.includes('file not found') || message.includes('404')) {
      return 'FILE_NOT_FOUND';
    }
    if (message.includes('checksum') || message.includes('hash')) {
      return 'CHECKSUM_MISMATCH';
    }
    if (message.includes('space') || message.includes('disk')) {
      return 'INSUFFICIENT_SPACE';
    }

    // 权限相关错误
    if (message.includes('permission') || message.includes('access denied')) {
      return 'PERMISSION_DENIED';
    }

    // 版本相关错误
    if (message.includes('version')) {
      return 'INVALID_VERSION';
    }

    // 默认为系统错误
    return 'SYSTEM_ERROR';
  }

  /**
   * 判断错误是否可重试
   */
  private isRetryableError(code: OTAErrorCode): boolean {
    const retryableCodes: OTAErrorCode[] = [
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
      'CONNECTION_ERROR',
      'SERVER_ERROR',
      'TEMPORARY_ERROR',
    ];
    return retryableCodes.includes(code);
  }

  /**
   * 获取错误严重程度
   */
  private getErrorSeverity(code: OTAErrorCode): ErrorSeverity {
    const criticalErrors: OTAErrorCode[] = [
      'SYSTEM_ERROR',
      'PERMISSION_DENIED',
      'INSTALL_FAILED',
    ];

    const warningErrors: OTAErrorCode[] = [
      'CHECKSUM_MISMATCH',
      'FILE_CORRUPTED',
      'INSUFFICIENT_SPACE',
    ];

    if (criticalErrors.includes(code)) {
      return 'critical';
    }
    if (warningErrors.includes(code)) {
      return 'warning';
    }
    return 'info';
  }

  /**
   * 记录错误
   */
  private recordError(error: UpdateError): void {
    this.errorHistory.unshift(error);
    
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * 获取用户友好的错误消息
   */
  getUserFriendlyMessage(error: UpdateError): string {
    const baseMessage = OTA_ERROR_MESSAGES[error.code] || error.message;
    
    // 根据错误类型添加建议
    const suggestions = this.getErrorSuggestions(error.code);
    
    if (suggestions.length > 0) {
      return `${baseMessage}\n\n建议：${suggestions.join('；')}`;
    }
    
    return baseMessage;
  }

  /**
   * 获取错误建议
   */
  private getErrorSuggestions(code: OTAErrorCode): string[] {
    const suggestions: Partial<Record<OTAErrorCode, string[]>> = {
      'NETWORK_ERROR': ['检查网络连接', '尝试切换网络'],
      'TIMEOUT_ERROR': ['稍后重试', '检查网络速度'],
      'CONNECTION_ERROR': ['检查服务器地址', '确认防火墙设置'],
      'FILE_NOT_FOUND': ['联系技术支持', '检查版本配置'],
      'CHECKSUM_MISMATCH': ['重新下载文件', '检查网络稳定性'],
      'INSUFFICIENT_SPACE': ['清理磁盘空间', '选择其他安装位置'],
      'PERMISSION_DENIED': ['以管理员身份运行', '检查文件权限'],
      'INSTALL_FAILED': ['关闭杀毒软件', '重启后重试'],
      'USER_CANCELLED': [],
      'SYSTEM_ERROR': ['重启应用', '联系技术支持'],
    };

    return suggestions[code] || [];
  }

  /**
   * 获取重试策略
   */
  getRetryStrategy(error: UpdateError): RetryStrategy {
    if (!error.retryable) {
      return {
        shouldRetry: false,
        maxRetries: 0,
        delay: 0,
        backoffMultiplier: 1,
      };
    }

    // 根据错误类型确定重试策略
    const strategies: Partial<Record<OTAErrorCode, RetryStrategy>> = {
      'NETWORK_ERROR': {
        shouldRetry: true,
        maxRetries: 3,
        delay: 5000,
        backoffMultiplier: 2,
      },
      'TIMEOUT_ERROR': {
        shouldRetry: true,
        maxRetries: 2,
        delay: 10000,
        backoffMultiplier: 1.5,
      },
      'CONNECTION_ERROR': {
        shouldRetry: true,
        maxRetries: 3,
        delay: 3000,
        backoffMultiplier: 2,
      },
      'SERVER_ERROR': {
        shouldRetry: true,
        maxRetries: 2,
        delay: 15000,
        backoffMultiplier: 1,
      },
    };

    return strategies[error.code] || {
      shouldRetry: false,
      maxRetries: 0,
      delay: 0,
      backoffMultiplier: 1,
    };
  }

  /**
   * 获取恢复操作建议
   */
  getRecoveryActions(error: UpdateError): ErrorRecoveryAction[] {
    const actions: Partial<Record<OTAErrorCode, ErrorRecoveryAction[]>> = {
      'NETWORK_ERROR': [
        { type: 'retry', label: '重试', action: 'retry' },
        { type: 'manual', label: '手动检查网络', action: 'check_network' },
      ],
      'TIMEOUT_ERROR': [
        { type: 'retry', label: '重试', action: 'retry' },
        { type: 'config', label: '增加超时时间', action: 'increase_timeout' },
      ],
      'FILE_NOT_FOUND': [
        { type: 'manual', label: '联系支持', action: 'contact_support' },
        { type: 'config', label: '检查配置', action: 'check_config' },
      ],
      'CHECKSUM_MISMATCH': [
        { type: 'retry', label: '重新下载', action: 'redownload' },
        { type: 'manual', label: '清除缓存', action: 'clear_cache' },
      ],
      'INSUFFICIENT_SPACE': [
        { type: 'manual', label: '清理空间', action: 'cleanup_space' },
        { type: 'config', label: '更改位置', action: 'change_location' },
      ],
      'PERMISSION_DENIED': [
        { type: 'manual', label: '以管理员运行', action: 'run_as_admin' },
        { type: 'manual', label: '检查权限', action: 'check_permissions' },
      ],
    };

    return actions[error.code] || [
      { type: 'manual', label: '联系技术支持', action: 'contact_support' },
    ];
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): UpdateError[] {
    return [...this.errorHistory];
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number;
    byCode: Record<OTAErrorCode, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recent: UpdateError[];
  } {
    const byCode: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};

    this.errorHistory.forEach(error => {
      byCode[error.code] = (byCode[error.code] || 0) + 1;
      const severity = (error as any).severity || 'info';
      bySeverity[severity] = (bySeverity[severity] || 0) + 1;
    });

    return {
      total: this.errorHistory.length,
      byCode: byCode as Record<OTAErrorCode, number>,
      bySeverity: bySeverity as Record<ErrorSeverity, number>,
      recent: this.errorHistory.slice(0, 10),
    };
  }

  /**
   * 检查是否为重复错误
   */
  isDuplicateError(error: UpdateError, timeWindow = 60000): boolean {
    const now = error.timestamp.getTime();
    
    return this.errorHistory.some(historyError => {
      const timeDiff = now - historyError.timestamp.getTime();
      return (
        historyError.code === error.code &&
        timeDiff < timeWindow &&
        historyError.message === error.message
      );
    });
  }

  /**
   * 格式化错误用于日志
   */
  formatErrorForLogging(error: UpdateError): string {
    const parts = [
      `[${error.code}]`,
      error.message,
    ];

    const extendedError = error as any;
    if (extendedError.context) {
      parts.push(`Context: ${JSON.stringify(extendedError.context)}`);
    }

    if (extendedError.originalError) {
      parts.push(`Original: ${extendedError.originalError.message}`);
    }

    return parts.join(' | ');
  }
}

/**
 * 全局错误处理器实例
 */
export const globalErrorHandler = new ErrorHandler();

/**
 * 便捷的错误处理函数
 */
export function handleUpdateError(
  error: Error | UpdateError,
  context?: ErrorContext
): UpdateError {
  return globalErrorHandler.handleError(error, context);
}

/**
 * 获取用户友好的错误消息
 */
export function getErrorMessage(error: UpdateError): string {
  return globalErrorHandler.getUserFriendlyMessage(error);
}

/**
 * 获取重试策略
 */
export function getRetryStrategy(error: UpdateError): RetryStrategy {
  return globalErrorHandler.getRetryStrategy(error);
}

/**
 * 获取恢复操作
 */
export function getRecoveryActions(error: UpdateError): ErrorRecoveryAction[] {
  return globalErrorHandler.getRecoveryActions(error);
}
