/* 导入图标字体 */
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");

/* 导入安全限制样式 */
@import "./styles/security-restrictions.css";

/* Reset CSS */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #1c1c1c;
  color: #ffffff;
  overflow-x: hidden;
}

#app {
  width: 100%;
  height: 100vh;
}

/* 使用Material设计图标作为替代方案 */
/* 这些图标是作为替代方案，可能不完全匹配 */
.plus-icon:after {
  content: "+";
  font-weight: bold;
}

.folder-icon:after {
  content: "📁";
}

.status-icon:after {
  content: "⚠️";
}

.settings-icon:after {
  content: "⚙️";
}

.example-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

/* 针对Mac OS和Windows不同的字体调整 */
@media screen and (max-width: 768px) {
  .examples-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media screen and (max-width: 480px) {
  .examples-grid {
    grid-template-columns: 1fr !important;
  }

  .learn-card {
    flex-direction: column;
  }

  .learn-image {
    width: 100% !important;
    height: 150px;
  }
}
