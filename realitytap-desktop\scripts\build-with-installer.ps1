# RealityTap 完整构建脚本
# 包含 installer 构建和主应用程序构建

param(
    [string]$Configuration = "release",
    [switch]$Clean = $false,
    [switch]$Verbose = $false,
    [switch]$SkipInstaller = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取脚本目录和项目根目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

Write-Host "🚀 RealityTap 完整构建脚本" -ForegroundColor Green
Write-Host "📁 项目根目录: $ProjectRoot" -ForegroundColor Cyan
Write-Host "⚙️ 构建配置: $Configuration" -ForegroundColor Cyan

# 切换到项目根目录
Push-Location $ProjectRoot

try {
    # 1. 构建 installer（如果需要）
    if (-not $SkipInstaller) {
        Write-Host ""
        Write-Host "🔨 第一步: 构建外部安装器..." -ForegroundColor Yellow
        
        $InstallerScriptPath = Join-Path $ScriptDir "build-installer.ps1"
        $InstallerArgs = @(
            "-Configuration", $Configuration,
            "-ForBundle"
        )
        
        if ($Clean) {
            $InstallerArgs += "-Clean"
        }
        
        if ($Verbose) {
            $InstallerArgs += "-Verbose"
        }
        
        Write-Host "🔧 执行: $InstallerScriptPath $($InstallerArgs -join ' ')" -ForegroundColor Cyan
        & $InstallerScriptPath @InstallerArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "安装器构建失败，退出代码: $LASTEXITCODE"
        }
        
        Write-Host "✅ 安装器构建完成" -ForegroundColor Green
    } else {
        Write-Host "⏭️ 跳过安装器构建" -ForegroundColor Yellow
    }
    
    # 2. 验证安装器文件存在
    $InstallerPath = Join-Path $ProjectRoot "installer/src-tauri/target/release/updater.exe"
    if (-not (Test-Path $InstallerPath)) {
        Write-Host "⚠️ 警告: 安装器文件不存在: $InstallerPath" -ForegroundColor Yellow
        Write-Host "   这可能导致打包失败。请确保安装器已正确构建。" -ForegroundColor Yellow
    } else {
        Write-Host "✅ 安装器文件已就绪: $InstallerPath" -ForegroundColor Green
    }
    
    # 3. 构建主应用程序
    Write-Host ""
    Write-Host "🔨 第二步: 构建主应用程序..." -ForegroundColor Yellow
    
    # 清理构建（如果需要）
    if ($Clean) {
        Write-Host "🧹 清理前端构建缓存..." -ForegroundColor Yellow
        if (Test-Path "dist") {
            Remove-Item -Recurse -Force "dist"
        }
        if (Test-Path "node_modules/.vite") {
            Remove-Item -Recurse -Force "node_modules/.vite"
        }
        
        Write-Host "🧹 清理 Tauri 构建缓存..." -ForegroundColor Yellow
        Push-Location "src-tauri"
        try {
            cargo clean
        } catch {
            Write-Host "⚠️ 警告: 清理 Tauri 构建缓存失败: $_" -ForegroundColor Yellow
        } finally {
            Pop-Location
        }
    }
    
    # 构建命令
    $BuildCommand = if ($Configuration -eq "release") {
        "npm run tauri build"
    } else {
        "npm run tauri dev"
    }
    
    Write-Host "🔧 执行构建命令: $BuildCommand" -ForegroundColor Cyan
    
    if ($Configuration -eq "release") {
        # 生产构建
        Invoke-Expression $BuildCommand
    } else {
        # 开发构建 - 只编译不运行
        npm run tauri build -- --debug
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "主应用程序构建失败，退出代码: $LASTEXITCODE"
    }
    
    Write-Host "✅ 主应用程序构建完成" -ForegroundColor Green
    
    # 4. 显示构建结果
    Write-Host ""
    Write-Host "🎉 构建完成！" -ForegroundColor Green
    
    if ($Configuration -eq "release") {
        $OutputDir = Join-Path $ProjectRoot "src-tauri/target/release/bundle"
        if (Test-Path $OutputDir) {
            Write-Host "📦 构建产物位置:" -ForegroundColor Cyan
            Get-ChildItem $OutputDir -Recurse -File | Where-Object { $_.Extension -in @('.msi', '.exe', '.deb', '.dmg', '.app') } | ForEach-Object {
                Write-Host "   $($_.FullName)" -ForegroundColor White
            }
        }
    }
    
    # 在构建完成后验证 DLL 打包
    Write-Host "`n🔍 验证 DLL 打包情况..." -ForegroundColor Yellow

    $BundleDir = Join-Path $ProjectRoot "src-tauri/target/release/bundle"
    $MsiFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.msi" -ErrorAction SilentlyContinue

    if ($MsiFiles) {
        foreach ($msi in $MsiFiles) {
            Write-Host "📦 检查 MSI: $($msi.Name)" -ForegroundColor Cyan
            
            # 简单检查：验证源 DLL 目录
            $DllSourceDir = Join-Path $ProjectRoot "src-tauri/libs/windows/x64"
            if (Test-Path $DllSourceDir) {
                $SourceDlls = Get-ChildItem $DllSourceDir -Filter "*.dll"
                Write-Host "✅ 源目录包含 $($SourceDlls.Count) 个 DLL 文件" -ForegroundColor Green
                foreach ($dll in $SourceDlls) {
                    Write-Host "   - $($dll.Name) ($([math]::Round($dll.Length/1MB, 2)) MB)" -ForegroundColor White
                }
            }
        }
    } else {
        Write-Host "❌ 未找到 MSI 文件" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ 构建失败: $_" -ForegroundColor Red
    exit 1
} finally {
    Pop-Location
}

Write-Host ""
Write-Host "💡 使用说明:" -ForegroundColor Yellow
Write-Host "   构建发布版本: .\scripts\build-with-installer.ps1 -Configuration release" -ForegroundColor White
Write-Host "   构建调试版本: .\scripts\build-with-installer.ps1 -Configuration debug" -ForegroundColor White
Write-Host "   清理构建:     .\scripts\build-with-installer.ps1 -Clean" -ForegroundColor White
Write-Host "   跳过安装器:   .\scripts\build-with-installer.ps1 -SkipInstaller" -ForegroundColor White
Write-Host ""

