# 批量上传功能使用指南

## 概述

批量上传功能允许您同时上传桌面应用的安装包文件（如 `.msi`）和对应的签名文件（`.sig`），系统会自动关联这两个文件并处理签名验证。

## 功能特点

- ✅ 支持同时上传安装包和签名文件
- ✅ 自动文件关联和签名处理
- ✅ 实时上传进度显示
- ✅ 文件完整性验证（SHA-256）
- ✅ 支持断点续传
- ✅ 详细的错误处理和日志记录

## 支持的文件类型

### 安装包文件
- `.msi` - Windows Installer 包
- `.exe` - Windows 可执行文件
- `.dmg` - macOS 磁盘映像
- `.pkg` - macOS 安装包
- `.deb` - Debian 包
- `.rpm` - Red Hat 包

### 签名文件
- `.sig` - Tauri 签名文件

## API 接口

### 1. 初始化批量上传

```http
POST /api/v1/admin/batch-upload/init
```

**请求体：**
```json
{
  "files": [
    {
      "filename": "RealityTap-1.0.0-windows-x86_64.msi",
      "fileSize": 52428800,
      "fileHash": "sha256_hash_here",
      "fileType": "installer"
    },
    {
      "filename": "RealityTap-1.0.0-windows-x86_64.msi.sig",
      "fileSize": 128,
      "fileHash": "sha256_hash_here",
      "fileType": "signature"
    }
  ],
  "metadata": {
    "version": "1.0.0",
    "platform": "windows",
    "architecture": "x86_64",
    "channel": "stable",
    "releaseNotes": "新版本发布",
    "isForced": false
  }
}
```

**响应：**
```json
{
  "success": true,
  "data": {
    "sessionId": "abc123def456",
    "files": [
      {
        "filename": "RealityTap-1.0.0-windows-x86_64.msi",
        "fileType": "installer",
        "status": "initializing",
        "uploadedBytes": 0,
        "totalBytes": 52428800
      },
      {
        "filename": "RealityTap-1.0.0-windows-x86_64.msi.sig",
        "fileType": "signature",
        "status": "initializing",
        "uploadedBytes": 0,
        "totalBytes": 128
      }
    ],
    "totalFiles": 2,
    "totalSize": 52428928
  }
}
```

### 2. 上传文件

```http
POST /api/v1/admin/batch-upload/{sessionId}/files
```

**请求：** `multipart/form-data`
- `files`: 文件数组（最多2个文件）

### 3. 完成批量上传

```http
POST /api/v1/admin/batch-upload/{sessionId}/complete
```

### 4. 获取上传进度

```http
GET /api/v1/admin/batch-upload/{sessionId}/progress
```

## 使用流程

### 1. 准备文件

确保您有以下文件：
- 安装包文件（必需）
- 签名文件（可选，但推荐）

### 2. 文件命名规范

建议使用以下命名格式：
```
{AppName}-{Version}-{Platform}-{Architecture}.{Extension}
{AppName}-{Version}-{Platform}-{Architecture}.{Extension}.sig
```

示例：
```
RealityTap-1.0.0-windows-x86_64.msi
RealityTap-1.0.0-windows-x86_64.msi.sig
```

### 3. 上传步骤

1. **初始化上传会话**
   - 计算文件的 SHA-256 哈希值
   - 调用初始化接口获取会话ID

2. **上传文件**
   - 使用会话ID上传所有文件
   - 系统会验证文件完整性

3. **完成上传**
   - 调用完成接口
   - 系统处理文件关联和签名

### 4. 前端集成示例

```typescript
import { adminApi } from '@/api/admin'

// 检查是否支持 Web Crypto API
const isWebCryptoSupported = (): boolean => {
  return typeof crypto !== 'undefined' &&
         typeof crypto.subtle !== 'undefined' &&
         typeof crypto.subtle.digest === 'function'
}

// 备用哈希计算函数（使用简单的哈希算法）
const calculateFileHashFallback = async (file: File): Promise<string> => {
  const buffer = await file.arrayBuffer()
  const uint8Array = new Uint8Array(buffer)

  // 使用简单的哈希算法作为备用方案
  let hash = 0
  for (let i = 0; i < uint8Array.length; i++) {
    const char = uint8Array[i]
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }

  // 将哈希值转换为十六进制字符串，并确保长度一致
  const hashHex = Math.abs(hash).toString(16).padStart(8, '0')

  // 为了提供更好的唯一性，添加文件大小和时间戳
  const sizeHex = file.size.toString(16).padStart(8, '0')
  const timeHex = file.lastModified.toString(16).padStart(8, '0')

  return `${hashHex}${sizeHex}${timeHex}`
}

// 计算文件哈希
const calculateFileHash = async (file: File): Promise<string> => {
  try {
    if (isWebCryptoSupported()) {
      // 使用 Web Crypto API（推荐方式）
      const buffer = await file.arrayBuffer()
      const hashBuffer = await crypto.subtle.digest('SHA-256', buffer)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
    } else {
      // 在非安全环境下使用备用方案
      console.warn('Web Crypto API 不可用，使用备用哈希计算方法')
      return await calculateFileHashFallback(file)
    }
  } catch (error) {
    console.error('计算文件哈希失败，使用备用方案:', error)
    return await calculateFileHashFallback(file)
  }
}

// 批量上传
const uploadFiles = async (installerFile: File, signatureFile?: File) => {
  // 1. 准备文件信息
  const files = [{
    filename: installerFile.name,
    fileSize: installerFile.size,
    fileHash: await calculateFileHash(installerFile),
    fileType: 'installer'
  }]

  if (signatureFile) {
    files.push({
      filename: signatureFile.name,
      fileSize: signatureFile.size,
      fileHash: await calculateFileHash(signatureFile),
      fileType: 'signature'
    })
  }

  // 2. 初始化上传
  const initResponse = await adminApi.initBatchUpload({
    files,
    metadata: {
      version: '1.0.0',
      platform: 'windows',
      architecture: 'x86_64',
      channel: 'stable'
    }
  })

  const sessionId = initResponse.data.sessionId

  // 3. 上传文件
  const filesToUpload = [installerFile]
  if (signatureFile) filesToUpload.push(signatureFile)
  
  await adminApi.uploadBatchFiles(sessionId, filesToUpload)

  // 4. 完成上传
  await adminApi.completeBatchUpload(sessionId)
}
```

## 错误处理

### 常见错误

1. **文件类型不支持**
   ```json
   {
     "success": false,
     "error": {
       "code": "INVALID_FILE_TYPE",
       "message": "不支持的文件类型: .txt"
     }
   }
   ```

2. **文件大小超限**
   ```json
   {
     "success": false,
     "error": {
       "code": "FILE_TOO_LARGE",
       "message": "文件大小超过限制 (500MB)"
     }
   }
   ```

3. **文件哈希不匹配**
   ```json
   {
     "success": false,
     "error": {
       "code": "HASH_MISMATCH",
       "message": "文件哈希验证失败"
     }
   }
   ```

4. **签名文件名不匹配**
   ```json
   {
     "success": false,
     "error": {
       "code": "SIGNATURE_NAME_MISMATCH",
       "message": "签名文件名必须与安装包文件名对应"
     }
   }
   ```

### 重试机制

- 网络错误：自动重试 3 次
- 服务器错误：自动重试 2 次
- 文件验证失败：不重试，需要重新上传

## 安全考虑

1. **文件验证**
   - 所有文件都会进行 SHA-256 哈希验证
   - 签名文件会与安装包文件自动关联

2. **访问控制**
   - 需要管理员身份验证
   - 支持基于角色的权限控制

3. **文件存储**
   - 文件存储在安全的服务器目录
   - 支持文件加密存储

## 监控和日志

### 上传日志

系统会记录详细的上传日志：
```
2024-01-15 10:30:00 INFO Batch upload session initialized sessionId=abc123 totalFiles=2
2024-01-15 10:30:05 INFO File uploaded successfully sessionId=abc123 filename=app.msi
2024-01-15 10:30:06 INFO File uploaded successfully sessionId=abc123 filename=app.msi.sig
2024-01-15 10:30:07 INFO File association processed sessionId=abc123 installer=app.msi signature=app.msi.sig
2024-01-15 10:30:08 INFO Batch upload completed successfully sessionId=abc123
```

### 性能监控

- 上传速度统计
- 文件处理时间
- 错误率统计
- 存储空间使用情况

## 故障排除

### 上传失败

1. **检查网络连接**
2. **验证文件完整性**
3. **确认文件命名规范**
4. **检查服务器日志**

### 签名验证失败

1. **确认签名文件格式正确**
2. **验证签名文件与安装包的对应关系**
3. **检查签名密钥配置**

## 最佳实践

1. **文件命名**
   - 使用统一的命名规范
   - 包含版本、平台、架构信息

2. **版本管理**
   - 使用语义化版本号
   - 提供详细的发布说明

3. **测试验证**
   - 上传前在测试环境验证
   - 确认签名文件有效性

4. **监控告警**
   - 设置上传失败告警
   - 监控存储空间使用

## 技术支持

如果遇到问题，请：

1. 查看服务器日志
2. 检查网络连接
3. 验证文件格式
4. 联系技术支持团队

---

**注意：** 此功能需要服务器端支持，确保已部署最新版本的 OTA 服务器。
