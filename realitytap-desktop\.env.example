# RealityTap Desktop 环境变量配置示例
# Environment Variables Configuration Example

# === 更新配置 ===
# Update Configuration
# 注意：更新服务器端点和公钥现在通过 tauri.conf.json 配置
# Note: Update server endpoints and public key are now configured in tauri.conf.json

# 更新通道 (stable, beta, alpha)
# Update channel (stable, beta, alpha)
VITE_UPDATE_CHANNEL=stable

# === 应用配置 ===
# Application Configuration

# 应用版本 (自动从 package.json 读取)
# Application version (automatically read from package.json)
# VITE_APP_VERSION=1.0.0

# 构建环境 (development, production)
# Build environment (development, production)
# NODE_ENV=development

# === 调试配置 ===
# Debug Configuration

# 启用更新器调试面板 (仅开发环境)
# Enable updater debug panel (development only)
VITE_ENABLE_UPDATER_DEBUG=true

# 启用详细日志
# Enable verbose logging
VITE_ENABLE_VERBOSE_LOGGING=false

# === API 配置 ===
# API Configuration

# API 基础 URL
# API base URL
VITE_API_BASE_URL=https://api.realitytap.com

# API 超时时间 (毫秒)
# API timeout (milliseconds)
VITE_API_TIMEOUT=30000

# === 功能开关 ===
# Feature Flags

# 启用自动更新检查
# Enable automatic update checking
VITE_ENABLE_AUTO_UPDATE=true

# 启用后台更新检查
# Enable background update checking
VITE_ENABLE_BACKGROUND_UPDATE=true

# 启用更新通知
# Enable update notifications
VITE_ENABLE_UPDATE_NOTIFICATIONS=true

# === 安全配置 ===
# Security Configuration

# 允许的更新服务器域名 (逗号分隔)
# Allowed update server domains (comma separated)
VITE_ALLOWED_UPDATE_DOMAINS=releases.realitytap.com,backup-releases.realitytap.com

# 强制 HTTPS 更新
# Force HTTPS for updates
VITE_FORCE_HTTPS_UPDATES=true

# === 性能配置 ===
# Performance Configuration

# 更新检查间隔 (分钟)
# Update check interval (minutes)
VITE_UPDATE_CHECK_INTERVAL=30

# 最大重试次数
# Maximum retry attempts
VITE_MAX_UPDATE_RETRIES=3

# 下载超时时间 (秒)
# Download timeout (seconds)
VITE_DOWNLOAD_TIMEOUT=300

# === 用户体验配置 ===
# User Experience Configuration

# 默认语言
# Default language
VITE_DEFAULT_LOCALE=zh-CN

# 启用动画效果
# Enable animations
VITE_ENABLE_ANIMATIONS=true

# 主题模式 (light, dark, auto)
# Theme mode (light, dark, auto)
VITE_THEME_MODE=auto
