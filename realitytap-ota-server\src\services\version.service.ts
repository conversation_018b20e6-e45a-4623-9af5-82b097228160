import { config } from '@/config/server.config';
import {
  ChannelsConfig,
  PlatformRelease,
  ServerVersionInfo,
  UpdateCheckRequest,
  UpdateCheckResponse,
  VersionsConfig,
} from '@/types/server.types';
import { FileUtil } from '@/utils/file.util';
import { logger } from '@/utils/logger.util';
import { VersionUtil } from '@/utils/version.util';
import path from 'path';

/**
 * 版本管理服务
 */
export class VersionService {
  private versionsConfigPath: string;
  private channelsConfigPath: string;
  private versionsCache: VersionsConfig | null = null;
  private channelsCache: ChannelsConfig | null = null;
  private lastCacheUpdate: number = 0;
  private readonly cacheTimeout = 60000; // 1分钟缓存

  constructor() {
    this.versionsConfigPath = path.join(config.storage.metadataPath, 'versions.json');
    this.channelsConfigPath = path.join(config.storage.metadataPath, 'channels.json');
  }

  /**
   * 检查版本更新
   * @param request 更新检查请求
   * @returns 更新检查响应
   */
  async checkForUpdates(request: UpdateCheckRequest): Promise<UpdateCheckResponse> {
    try {
      logger.info('Checking for updates', { request });

      // 验证请求参数
      this.validateUpdateRequest(request);

      // 获取配置
      const versionsConfig = await this.getVersionsConfig();
      const channelsConfig = await this.getChannelsConfig();

      // 检查渠道是否启用
      const channelConfig = channelsConfig[request.channel];
      if (!channelConfig || !channelConfig.enabled) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      // 获取渠道版本信息
      const channelVersionInfo = versionsConfig.channels[request.channel];
      if (!channelVersionInfo) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      // 获取平台和架构的发布信息
      const platformRelease = this.getPlatformRelease(channelVersionInfo, request.platform, request.architecture);

      if (!platformRelease) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      const latestVersion = channelVersionInfo.version;

      // 检查是否有更新
      const hasUpdate = VersionUtil.hasUpdate(request.currentVersion, latestVersion);

      if (!hasUpdate) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      // 检查最小版本要求
      const minimumVersion = versionsConfig.minimumVersions[request.channel];
      const isForced = minimumVersion && !VersionUtil.satisfiesMinimum(request.currentVersion, minimumVersion);

      // 检查灰度发布
      const shouldReceiveUpdate = this.shouldReceiveUpdate(channelConfig, request);
      if (!shouldReceiveUpdate) {
        return {
          hasUpdate: false,
          latestVersion: request.currentVersion,
        };
      }

      // 构建下载 URL（对文件名进行URL编码以处理特殊字符）
      const encodedFilename = encodeURIComponent(platformRelease.filename);
      const downloadPath = `/api/v1/download/${encodedFilename}`;

      // 如果配置了基础URL，返回完整URL；否则返回相对路径
      const downloadUrl = config.server.baseUrl ? `${config.server.baseUrl}${downloadPath}` : downloadPath;

      const response: UpdateCheckResponse = {
        hasUpdate: true,
        latestVersion,
        downloadUrl,
        releaseNotes: platformRelease.releaseNotes,
        fileSize: platformRelease.size,
        checksum: platformRelease.checksum,
        isForced: isForced || false,
        signature: platformRelease.signature,
        releaseDate: platformRelease.releaseDate,
        // minimumVersion, // 暂时注释掉，因为类型定义中没有这个字段
      };

      logger.info('Update check completed', { request, response });
      return response;
    } catch (error) {
      logger.error('检查更新失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          stack: error instanceof Error ? error.stack : undefined,
          type: typeof error,
        },
        updateRequest: {
          channel: request.channel,
          platform: request.platform,
          architecture: request.architecture,
          currentVersion: request.currentVersion,
        },
        systemInfo: {
          versionsConfigPath: this.versionsConfigPath,
          channelsConfigPath: this.channelsConfigPath,
          cacheStatus: {
            hasCache: !!this.versionsCache,
            lastUpdate: this.lastCacheUpdate,
            cacheAge: this.lastCacheUpdate ? Date.now() - this.lastCacheUpdate : null,
            cacheTimeout: this.cacheTimeout,
          },
          timestamp: new Date().toISOString(),
        },
        timing: {
          timestamp: new Date().toISOString(),
        },
        operation: 'check_updates_failed',
        module: 'ota_function',
      });
      throw new Error(`Failed to check for updates: ${error}`);
    }
  }

  /**
   * 获取版本配置
   */
  private async getVersionsConfig(): Promise<VersionsConfig> {
    const now = Date.now();
    if (this.versionsCache && now - this.lastCacheUpdate < this.cacheTimeout) {
      return this.versionsCache;
    }

    try {
      this.versionsCache = await FileUtil.readJSON<VersionsConfig>(this.versionsConfigPath);
      this.lastCacheUpdate = now;
      return this.versionsCache;
    } catch (error) {
      logger.error('加载版本配置失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          stack: error instanceof Error ? error.stack : undefined,
          type: typeof error,
        },
        configInfo: {
          versionsConfigPath: this.versionsConfigPath,
          cacheStatus: {
            hasCache: !!this.versionsCache,
            lastUpdate: this.lastCacheUpdate,
            cacheAge: this.lastCacheUpdate ? Date.now() - this.lastCacheUpdate : null,
            cacheTimeout: this.cacheTimeout,
          },
        },
        systemInfo: {
          fileExists: await FileUtil.exists(this.versionsConfigPath).catch(() => false),
          timestamp: new Date().toISOString(),
        },
        timing: {
          timestamp: new Date().toISOString(),
        },
        operation: 'load_versions_config_failed',
        module: 'ota_function',
      });
      throw new Error(`Failed to load versions config: ${error}`);
    }
  }

  /**
   * 获取渠道配置
   */
  private async getChannelsConfig(): Promise<ChannelsConfig> {
    const now = Date.now();
    if (this.channelsCache && now - this.lastCacheUpdate < this.cacheTimeout) {
      return this.channelsCache;
    }

    try {
      this.channelsCache = await FileUtil.readJSON<ChannelsConfig>(this.channelsConfigPath);
      return this.channelsCache;
    } catch (error) {
      throw new Error(`Failed to load channels config: ${error}`);
    }
  }

  /**
   * 获取平台发布信息
   */
  private getPlatformRelease(
    versionInfo: ServerVersionInfo,
    platform: string,
    architecture: string,
  ): PlatformRelease | null {
    const platformData = versionInfo.platforms[platform];
    if (!platformData) {
      return null;
    }

    return platformData[architecture] || null;
  }

  /**
   * 验证更新请求
   */
  private validateUpdateRequest(request: UpdateCheckRequest): void {
    if (!VersionUtil.isValid(request.currentVersion)) {
      throw new Error('Invalid current version format');
    }

    const supportedPlatforms = ['windows', 'macos', 'linux'];
    if (!supportedPlatforms.includes(request.platform)) {
      throw new Error(`Unsupported platform: ${request.platform}`);
    }

    const supportedArchitectures = ['x86_64', 'aarch64', 'x86'];
    if (!supportedArchitectures.includes(request.architecture)) {
      throw new Error(`Unsupported architecture: ${request.architecture}`);
    }

    const supportedChannels = ['stable', 'beta', 'alpha'];
    if (!supportedChannels.includes(request.channel)) {
      throw new Error(`Unsupported channel: ${request.channel}`);
    }
  }

  /**
   * 判断是否应该接收更新（灰度发布）
   */
  private shouldReceiveUpdate(channelConfig: any, request: UpdateCheckRequest): boolean {
    const rolloutPercentage = channelConfig.rolloutPercentage || 100;

    if (rolloutPercentage >= 100) {
      return true;
    }

    // 基于用户标识生成一个稳定的随机数
    const userIdentifier = `${request.platform}-${request.architecture}`;
    const hash = require('crypto').createHash('md5').update(userIdentifier).digest('hex');
    const hashNumber = parseInt(hash.substring(0, 8), 16);
    const percentage = (hashNumber % 100) + 1;

    return percentage <= rolloutPercentage;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.versionsCache = null;
    this.channelsCache = null;
    this.lastCacheUpdate = 0;
    logger.info('Version service cache cleared');
  }

  /**
   * 获取所有可用版本
   */
  async getAvailableVersions(): Promise<VersionsConfig> {
    return this.getVersionsConfig();
  }

  /**
   * 获取渠道信息
   */
  async getChannelInfo(): Promise<ChannelsConfig> {
    return this.getChannelsConfig();
  }

  /**
   * 更新版本配置
   */
  async updateVersionsConfig(config: VersionsConfig): Promise<void> {
    try {
      await FileUtil.writeJSONAtomic(this.versionsConfigPath, config);
      this.clearCache();
      logger.info('Versions config updated');
    } catch (error) {
      logger.error('更新版本配置失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          stack: error instanceof Error ? error.stack : undefined,
          type: typeof error,
        },
        configUpdate: {
          versionsConfigPath: this.versionsConfigPath,
          configKeys: config ? Object.keys(config) : [],
          channelsCount: config?.channels ? Object.keys(config.channels).length : 0,
          minimumVersionsCount: config?.minimumVersions ? Object.keys(config.minimumVersions).length : 0,
          deprecatedVersionsCount: config?.deprecatedVersions ? config.deprecatedVersions.length : 0,
        },
        systemInfo: {
          fileExists: await FileUtil.exists(this.versionsConfigPath).catch(() => false),
          timestamp: new Date().toISOString(),
        },
        timing: {
          timestamp: new Date().toISOString(),
        },
        operation: 'update_versions_config_failed',
        module: 'version_management',
      });
      throw new Error(`Failed to update versions config: ${error}`);
    }
  }

  /**
   * 更新渠道配置
   */
  async updateChannelsConfig(config: ChannelsConfig): Promise<void> {
    try {
      await FileUtil.writeJSONAtomic(this.channelsConfigPath, config);
      this.clearCache();
      logger.info('Channels config updated');
    } catch (error) {
      throw new Error(`Failed to update channels config: ${error}`);
    }
  }
}

// 导出单例实例
export const versionService = new VersionService();
