// File-related data models
use crate::models::{AudioInfo, HeFormatVersion};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct HapticFile {
    pub file_uuid: Uuid,
    pub name: String,        // e.g., "click.he"
    pub path: String, // Relative path to the .he file within the project's "haptics" directory, e.g., "gunshot.he" or "vehicles/car_rumble.he"
    pub group: Option<Uuid>, // Link to Group UUID
    pub format_version: HeFormatVersion,
    pub create_time: DateTime<Utc>,
    pub last_modified_time: DateTime<Utc>,
    pub description: String,
    pub version: String, // Version of the .he file content itself
    pub associated_audio: Option<String>, // Relative path to the audio file within the project's "audio" directory
    pub associated_video: Option<String>, // Relative path to the video file within the project's "video" directory
    #[serde(skip_serializing_if = "Option::is_none")]
    pub audio_info: Option<AudioInfo>,
    #[serde(default)] // Allow files without tags initially
    pub tags: Vec<String>, // Added based on file management features
    #[serde(default)]
    pub file_type: String, // 'he' | 'audio' | ...
    #[serde(skip_serializing_if = "Option::is_none")]
    pub zoom_level: Option<f64>, // 波形缩放率，用于持久化存储用户的缩放偏好
}
