#!/usr/bin/env node

/**
 * RealityTap Desktop 更新器签名修复工具
 * 修复 "Invalid encoding in minisign data" 错误
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step} ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'cyan');
}

// 检查 Tauri CLI 是否可用
function checkTauriCLI() {
  try {
    execSync('tauri --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

// 生成新的密钥对
function generateKeyPair() {
  logStep('1️⃣', '生成新的密钥对');
  
  if (!checkTauriCLI()) {
    logError('Tauri CLI 未安装或不可用');
    logInfo('请先安装 Tauri CLI: npm install -g @tauri-apps/cli');
    return false;
  }
  
  try {
    // 确保密钥目录存在
    const keyDir = 'keys';
    if (!fs.existsSync(keyDir)) {
      fs.mkdirSync(keyDir, { recursive: true });
      logInfo(`创建密钥目录: ${keyDir}`);
    }
    
    const keyPath = path.join(keyDir, 'realitytap-updater.key');
    
    // 生成密钥对
    logInfo('正在生成密钥对...');
    execSync(`tauri signer generate -w "${keyPath}"`, { stdio: 'inherit' });
    
    logSuccess('密钥对生成成功');
    return true;
  } catch (error) {
    logError(`生成密钥对失败: ${error.message}`);
    return false;
  }
}

// 读取公钥并转换为 Base64
function readPublicKey() {
  const pubKeyPath = 'keys/realitytap-updater.pub';
  
  if (!fs.existsSync(pubKeyPath)) {
    logError(`公钥文件不存在: ${pubKeyPath}`);
    return null;
  }
  
  try {
    const pubKeyContent = fs.readFileSync(pubKeyPath, 'utf8');
    const base64PubKey = Buffer.from(pubKeyContent).toString('base64');
    
    logSuccess('公钥读取成功');
    logInfo(`公钥内容: ${pubKeyContent.substring(0, 50)}...`);
    logInfo(`Base64 编码: ${base64PubKey.substring(0, 50)}...`);
    
    return base64PubKey;
  } catch (error) {
    logError(`读取公钥失败: ${error.message}`);
    return null;
  }
}

// 更新配置文件中的公钥
function updateConfigFiles(base64PubKey) {
  logStep('2️⃣', '更新配置文件中的公钥');
  
  const configFiles = [
    'src-tauri/tauri.conf.json',
    'src-tauri/tauri.dev.conf.json'
  ];
  
  configFiles.forEach(configFile => {
    if (!fs.existsSync(configFile)) {
      logWarning(`配置文件不存在: ${configFile}`);
      return;
    }
    
    try {
      const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
      
      if (!config.plugins) {
        config.plugins = {};
      }
      
      if (!config.plugins.updater) {
        config.plugins.updater = {};
      }
      
      config.plugins.updater.pubkey = base64PubKey;
      
      fs.writeFileSync(configFile, JSON.stringify(config, null, 2));
      logSuccess(`已更新: ${configFile}`);
    } catch (error) {
      logError(`更新 ${configFile} 失败: ${error.message}`);
    }
  });
}

// 复制密钥到 OTA 服务器
function copyKeysToOTAServer() {
  logStep('3️⃣', '复制密钥到 OTA 服务器');
  
  const otaServerKeyDir = '../realitytap-ota-server/keys';
  
  if (!fs.existsSync(otaServerKeyDir)) {
    try {
      fs.mkdirSync(otaServerKeyDir, { recursive: true });
      logInfo(`创建 OTA 服务器密钥目录: ${otaServerKeyDir}`);
    } catch (error) {
      logError(`创建目录失败: ${error.message}`);
      return;
    }
  }
  
  const keyFiles = [
    'keys/realitytap-updater.key',
    'keys/realitytap-updater.pub'
  ];
  
  keyFiles.forEach(keyFile => {
    if (fs.existsSync(keyFile)) {
      try {
        const destPath = path.join(otaServerKeyDir, path.basename(keyFile));
        fs.copyFileSync(keyFile, destPath);
        logSuccess(`已复制: ${keyFile} -> ${destPath}`);
      } catch (error) {
        logError(`复制 ${keyFile} 失败: ${error.message}`);
      }
    } else {
      logWarning(`密钥文件不存在: ${keyFile}`);
    }
  });
}

// 更新 OTA 服务器环境变量
function updateOTAServerEnv() {
  logStep('4️⃣', '更新 OTA 服务器环境变量');
  
  const envFiles = [
    '../realitytap-ota-server/.env',
    '../realitytap-ota-server/.env.development'
  ];
  
  envFiles.forEach(envFile => {
    if (fs.existsSync(envFile)) {
      try {
        let envContent = fs.readFileSync(envFile, 'utf8');
        
        // 更新私钥路径
        const keyPath = './keys/realitytap-updater.key';
        if (envContent.includes('TAURI_PRIVATE_KEY_PATH=')) {
          envContent = envContent.replace(
            /TAURI_PRIVATE_KEY_PATH=.*/,
            `TAURI_PRIVATE_KEY_PATH=${keyPath}`
          );
        } else {
          envContent += `\nTAURI_PRIVATE_KEY_PATH=${keyPath}\n`;
        }
        
        // 添加密码提示（如果不存在）
        if (!envContent.includes('TAURI_KEY_PASSWORD=')) {
          envContent += `# TAURI_KEY_PASSWORD=your-password-here\n`;
        }
        
        fs.writeFileSync(envFile, envContent);
        logSuccess(`已更新: ${envFile}`);
      } catch (error) {
        logError(`更新 ${envFile} 失败: ${error.message}`);
      }
    } else {
      logInfo(`环境文件不存在: ${envFile}`);
    }
  });
}

// 验证修复结果
function verifyFix() {
  logStep('5️⃣', '验证修复结果');
  
  // 检查配置文件
  const configFiles = [
    'src-tauri/tauri.conf.json',
    'src-tauri/tauri.dev.conf.json'
  ];
  
  let allValid = true;
  
  configFiles.forEach(configFile => {
    if (fs.existsSync(configFile)) {
      try {
        const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
        const pubkey = config.plugins?.updater?.pubkey;
        
        if (pubkey) {
          // 验证 Base64 格式
          const decoded = Buffer.from(pubkey, 'base64').toString('utf8');
          if (decoded.includes('minisign public key')) {
            logSuccess(`${configFile} 公钥格式正确`);
          } else {
            logError(`${configFile} 公钥格式无效`);
            allValid = false;
          }
        } else {
          logError(`${configFile} 缺少公钥配置`);
          allValid = false;
        }
      } catch (error) {
        logError(`验证 ${configFile} 失败: ${error.message}`);
        allValid = false;
      }
    }
  });
  
  return allValid;
}

// 提供后续步骤
function provideNextSteps() {
  logStep('6️⃣', '后续步骤');
  
  log('\n🚀 修复完成！请按以下步骤继续:', 'yellow');
  
  log('\n1. 重启 OTA 服务器:', 'cyan');
  log('   cd ../realitytap-ota-server');
  log('   npm run dev');
  
  log('\n2. 清理并重新构建桌面应用:', 'cyan');
  log('   rm -rf src-tauri/target');
  log('   npm run build');
  
  log('\n3. 测试更新功能:', 'cyan');
  log('   npm run dev');
  log('   在应用中点击检查更新');
  
  log('\n4. 如果仍有问题，运行诊断工具:', 'cyan');
  log('   node scripts/diagnose-updater.js');
}

// 主函数
async function main() {
  log('🔧 RealityTap Desktop 更新器签名修复工具', 'magenta');
  log('=' .repeat(50), 'magenta');
  
  try {
    // 生成新的密钥对
    if (!generateKeyPair()) {
      process.exit(1);
    }
    
    // 读取公钥
    const base64PubKey = readPublicKey();
    if (!base64PubKey) {
      process.exit(1);
    }
    
    // 更新配置文件
    updateConfigFiles(base64PubKey);
    
    // 复制密钥到 OTA 服务器
    copyKeysToOTAServer();
    
    // 更新 OTA 服务器环境变量
    updateOTAServerEnv();
    
    // 验证修复结果
    if (verifyFix()) {
      logSuccess('所有配置验证通过');
    } else {
      logWarning('部分配置可能需要手动检查');
    }
    
    // 提供后续步骤
    provideNextSteps();
    
    log('\n✨ 修复完成！', 'green');
  } catch (error) {
    logError(`修复过程中发生错误: ${error.message}`);
    process.exit(1);
  }
}

// 运行修复
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
