import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// 创建 axios 实例
const request: AxiosInstance = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加认证token - 直接从localStorage获取，避免使用Vue store
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  error => {
    console.error('Request error:', error);
    return Promise.reject(error);
  },
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async error => {
    // 创建一个简单的消息显示函数，避免使用Vue组合式API
    const showMessage = (message: string, type: 'error' | 'success' = 'error') => {
      console.error(`[${type.toUpperCase()}] ${message}`);
      // 可以在这里添加其他消息显示逻辑，比如原生alert或自定义通知
    };

    if (error.response) {
      const { status, data, config } = error.response;

      // 检查是否是登录请求
      const isLoginRequest = config?.url?.includes('/admin/login');

      switch (status) {
        case 401:
          if (isLoginRequest) {
            // 登录请求的401错误，不清除token，不跳转，让组件处理
            // 直接抛出错误，让登录组件显示正确的错误信息
            break;
          } else {
            // 其他请求的401错误，表示token过期或无效
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_user');

            // 避免在登录页面重复跳转
            if (window.location.pathname !== '/login' && !window.location.pathname.includes('/login')) {
              window.location.href = '/login';
              showMessage('登录已过期，请重新登录');
            }
          }
          break;

        case 403:
          showMessage('权限不足');
          break;

        case 404:
          showMessage('请求的资源不存在');
          break;

        case 429:
          showMessage('请求过于频繁，请稍后再试');
          break;

        case 500:
          showMessage('服务器内部错误');
          break;

        default:
          const errorMessage = data?.error?.message || data?.message || `请求失败 (${status})`;
          showMessage(errorMessage);
      }
    } else if (error.request) {
      // 网络错误
      showMessage('网络连接失败，请检查网络设置');
    } else {
      // 其他错误
      showMessage('请求失败，请稍后重试');
    }

    return Promise.reject(error);
  },
);

export default request;
