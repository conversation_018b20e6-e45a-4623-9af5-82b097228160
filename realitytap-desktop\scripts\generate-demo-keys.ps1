#!/usr/bin/env pwsh

<#
.SYNOPSIS
    为 RealityTap Desktop 生成演示用签名密钥
    Generate demo signing keys for RealityTap Desktop

.DESCRIPTION
    此脚本生成用于演示的签名密钥对，并自动配置环境变量。
    仅用于开发和测试目的，生产环境请使用安全的密钥管理方式。

.PARAMETER KeyName
    密钥名称（默认：realitytap-demo）

.PARAMETER KeyDir
    密钥存储目录（默认：./keys）

.PARAMETER SetEnv
    是否自动设置环境变量（默认：true）

.EXAMPLE
    .\scripts\generate-demo-keys.ps1
    .\scripts\generate-demo-keys.ps1 -KeyName "my-app" -KeyDir "C:\keys"
#>

param(
    [string]$KeyName = "realitytap-demo",
    [string]$KeyDir = "./keys",
    [switch]$SetEnv = $true
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot

Write-Host "🔐 生成 RealityTap Desktop 演示签名密钥" -ForegroundColor Cyan
Write-Host "⚠️  注意：此脚本仅用于开发和测试，生产环境请使用安全的密钥管理" -ForegroundColor Yellow

# 检查 Tauri CLI
Write-Host "`n🔍 检查 Tauri CLI..." -ForegroundColor Yellow

try {
    $tauriVersion = npm run tauri -- --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Tauri CLI 已安装" -ForegroundColor Green
    } else {
        throw "Tauri CLI 未找到"
    }
} catch {
    Write-Host "❌ 错误: 未找到 Tauri CLI" -ForegroundColor Red
    Write-Host "请先安装 Tauri CLI:" -ForegroundColor Yellow
    Write-Host "  npm install -g @tauri-apps/cli" -ForegroundColor Gray
    Write-Host "  或在项目中: npm install @tauri-apps/cli" -ForegroundColor Gray
    exit 1
}

# 创建密钥目录
Write-Host "`n📁 创建密钥目录..." -ForegroundColor Yellow

if (-not (Test-Path $KeyDir)) {
    New-Item -ItemType Directory -Path $KeyDir -Force | Out-Null
    Write-Host "✅ 创建目录: $KeyDir" -ForegroundColor Green
} else {
    Write-Host "✅ 目录已存在: $KeyDir" -ForegroundColor Green
}

# 设置密钥文件路径
$PrivateKeyPath = Join-Path $KeyDir "$KeyName.key"
$PublicKeyPath = Join-Path $KeyDir "$KeyName.pub"

# 检查密钥是否已存在
if (Test-Path $PrivateKeyPath) {
    Write-Host "`n⚠️  警告: 私钥文件已存在: $PrivateKeyPath" -ForegroundColor Yellow
    $overwrite = Read-Host "是否覆盖现有密钥? (y/N)"
    if ($overwrite -ne "y" -and $overwrite -ne "Y") {
        Write-Host "操作已取消" -ForegroundColor Yellow
        exit 0
    }
}

# 生成密钥对
Write-Host "`n🔨 生成密钥对..." -ForegroundColor Yellow

try {
    # 使用 npm run tauri 来调用 Tauri CLI
    npm run tauri signer generate -- -w $PrivateKeyPath
    
    if ($LASTEXITCODE -ne 0) {
        throw "密钥生成失败"
    }
    
    Write-Host "✅ 密钥对生成成功!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 密钥生成失败: $_" -ForegroundColor Red
    exit 1
}

# 验证生成的文件
Write-Host "`n🔍 验证生成的文件..." -ForegroundColor Yellow

if (Test-Path $PrivateKeyPath) {
    Write-Host "✅ 私钥文件: $PrivateKeyPath" -ForegroundColor Green
} else {
    Write-Host "❌ 私钥文件未找到: $PrivateKeyPath" -ForegroundColor Red
    exit 1
}

if (Test-Path $PublicKeyPath) {
    Write-Host "✅ 公钥文件: $PublicKeyPath" -ForegroundColor Green
} else {
    Write-Host "❌ 公钥文件未找到: $PublicKeyPath" -ForegroundColor Red
    exit 1
}

# 显示公钥内容
Write-Host "`n📄 公钥内容 (用于 tauri.conf.json):" -ForegroundColor Cyan
try {
    $PublicKeyContent = Get-Content $PublicKeyPath -Raw
    Write-Host $PublicKeyContent -ForegroundColor Gray
} catch {
    Write-Host "❌ 无法读取公钥文件" -ForegroundColor Red
}

# 设置环境变量
if ($SetEnv) {
    Write-Host "`n🔧 设置环境变量..." -ForegroundColor Yellow
    
    $FullPrivateKeyPath = (Resolve-Path $PrivateKeyPath).Path
    $env:TAURI_SIGNING_PRIVATE_KEY = $FullPrivateKeyPath
    
    Write-Host "✅ 已设置 TAURI_SIGNING_PRIVATE_KEY = $FullPrivateKeyPath" -ForegroundColor Green
    
    # 创建 .env.signing 文件
    $EnvSigningFile = ".env.signing"
    $EnvContent = @"
# RealityTap Desktop 签名配置 (演示用)
# 此文件由 generate-demo-keys.ps1 自动生成

# Tauri 签名私钥
TAURI_SIGNING_PRIVATE_KEY=$FullPrivateKeyPath

# Tauri 签名私钥密码（演示密钥无密码）
TAURI_SIGNING_PRIVATE_KEY_PASSWORD=
"@
    
    $EnvContent | Out-File -FilePath $EnvSigningFile -Encoding UTF8
    Write-Host "✅ 已创建环境变量配置文件: $EnvSigningFile" -ForegroundColor Green
}

# 显示下一步操作
Write-Host "`n💡 下一步操作:" -ForegroundColor Yellow
Write-Host "1. 现在可以运行构建命令:" -ForegroundColor White
Write-Host "   npm run build:with-updater" -ForegroundColor Cyan
Write-Host "   npm run generate:update-files" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. 如果需要在新的终端中使用，请先加载环境变量:" -ForegroundColor White
Write-Host "   npm run setup:signing -- -LoadFromFile" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. 生产环境部署时，请:" -ForegroundColor White
Write-Host "   - 使用安全的密钥管理系统" -ForegroundColor Gray
Write-Host "   - 妥善保管私钥文件" -ForegroundColor Gray
Write-Host "   - 定期轮换密钥" -ForegroundColor Gray

Write-Host "`n🎉 演示密钥生成完成！" -ForegroundColor Green
Write-Host "📁 密钥文件位置:" -ForegroundColor Cyan
Write-Host "   私钥: $PrivateKeyPath" -ForegroundColor White
Write-Host "   公钥: $PublicKeyPath" -ForegroundColor White
