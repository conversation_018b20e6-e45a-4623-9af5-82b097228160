# RealityTap 外部安装器构建脚本
# 用于构建独立的安装器程序

param(
    [string]$Configuration = "release",
    [string]$TargetDir = "src-tauri/installer",
    [switch]$Clean = $false,
    [switch]$Verbose = $false,
    [switch]$ForBundle = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取脚本目录和项目根目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$InstallerDir = Join-Path $ProjectRoot "installer"
$OutputDir = Join-Path $ProjectRoot $TargetDir

Write-Host "🚀 RealityTap 外部安装器构建脚本" -ForegroundColor Green
Write-Host "📁 项目根目录: $ProjectRoot" -ForegroundColor Cyan
Write-Host "📦 安装器源码: $InstallerDir" -ForegroundColor Cyan
Write-Host "🎯 输出目录: $OutputDir" -ForegroundColor Cyan
Write-Host "⚙️ 构建配置: $Configuration" -ForegroundColor Cyan

# 检查 Rust 环境
try {
    $RustVersion = cargo --version
    Write-Host "✅ Rust 环境: $RustVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到 Rust 环境，请先安装 Rust" -ForegroundColor Red
    Write-Host "   下载地址: https://rustup.rs/" -ForegroundColor Yellow
    exit 1
}

# 检查安装器源码目录
if (-not (Test-Path $InstallerDir)) {
    Write-Host "❌ 错误: 安装器源码目录不存在: $InstallerDir" -ForegroundColor Red
    exit 1
}

# 检查 Cargo.toml
$CargoToml = Join-Path $InstallerDir "Cargo.toml"
if (-not (Test-Path $CargoToml)) {
    Write-Host "❌ 错误: 未找到 Cargo.toml: $CargoToml" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-Host "📁 创建输出目录: $OutputDir" -ForegroundColor Yellow
}

# 清理构建（如果需要）
if ($Clean) {
    Write-Host "🧹 清理构建缓存..." -ForegroundColor Yellow
    Push-Location $InstallerDir
    try {
        cargo clean
        Write-Host "✅ 构建缓存已清理" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ 警告: 清理构建缓存失败: $_" -ForegroundColor Yellow
    } finally {
        Pop-Location
    }
}

# 构建安装器
Write-Host "🔨 开始构建安装器..." -ForegroundColor Yellow
Push-Location $InstallerDir

try {
    # 构建参数
    $BuildArgs = @("build")
    
    if ($Configuration -eq "release") {
        $BuildArgs += "--release"
    }
    
    if ($Verbose) {
        $BuildArgs += "--verbose"
    }
    
    # 执行构建
    Write-Host "🔧 执行命令: cargo $($BuildArgs -join ' ')" -ForegroundColor Cyan
    & cargo @BuildArgs
    
    if ($LASTEXITCODE -ne 0) {
        throw "构建失败，退出代码: $LASTEXITCODE"
    }
    
    Write-Host "✅ 安装器构建成功" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 构建失败: $_" -ForegroundColor Red
    Pop-Location
    exit 1
} finally {
    Pop-Location
}

# 复制构建产物
Write-Host "📦 复制构建产物..." -ForegroundColor Yellow

$SourceBinDir = if ($Configuration -eq "release") {
    Join-Path $InstallerDir "target/release"
} else {
    Join-Path $InstallerDir "target/debug"
}

# 确定可执行文件名
$ExeName = if ($IsWindows -or $env:OS -eq "Windows_NT") {
    "updater.exe"
} else {
    "updater"
}

$SourceExe = Join-Path $SourceBinDir $ExeName
$TargetExe = Join-Path $OutputDir $ExeName

if (-not (Test-Path $SourceExe)) {
    Write-Host "❌ 错误: 构建产物不存在: $SourceExe" -ForegroundColor Red
    exit 1
}

# 如果是为打包准备，也复制到 src-tauri 目录
if ($ForBundle) {
    $BundleTargetDir = Join-Path $ProjectRoot "src-tauri/installer"
    $BundleTargetExe = Join-Path $BundleTargetDir $ExeName

    Write-Host "📦 为打包复制到: $BundleTargetExe" -ForegroundColor Cyan

    # 确保目录存在
    if (-not (Test-Path $BundleTargetDir)) {
        New-Item -ItemType Directory -Path $BundleTargetDir -Force | Out-Null
    }

    Copy-Item $SourceExe $BundleTargetExe -Force
    Write-Host "✅ 已复制到打包目录" -ForegroundColor Green
}

try {
    Copy-Item $SourceExe $TargetExe -Force
    Write-Host "✅ 已复制: $SourceExe -> $TargetExe" -ForegroundColor Green
    
    # 显示文件信息
    $FileInfo = Get-Item $TargetExe
    $FileSize = [math]::Round($FileInfo.Length / 1MB, 2)
    Write-Host "📊 文件大小: $FileSize MB" -ForegroundColor Cyan
    Write-Host "📅 修改时间: $($FileInfo.LastWriteTime)" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ 复制文件失败: $_" -ForegroundColor Red
    exit 1
}

# 验证可执行文件
Write-Host "🔍 验证可执行文件..." -ForegroundColor Yellow
try {
    $VersionOutput = & $TargetExe --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 安装器版本: $VersionOutput" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 警告: 无法获取安装器版本信息" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ 警告: 验证可执行文件失败: $_" -ForegroundColor Yellow
}

# 获取 Rust 版本信息
try {
    $RustVersion = (cargo --version 2>$null) -replace "cargo ", ""
} catch {
    $RustVersion = "Unknown"
}

# 创建版本信息文件
$VersionFile = Join-Path $OutputDir "installer-version.txt"
$VersionInfo = @"
RealityTap External Installer
Build Time: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Configuration: $Configuration
Platform: $env:OS
Rust Version: $RustVersion
File Size: $FileSize MB
"@

$VersionInfo | Out-File -FilePath $VersionFile -Encoding UTF8
Write-Host "📄 版本信息已保存: $VersionFile" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 安装器构建完成！" -ForegroundColor Green
Write-Host "📁 输出目录: $OutputDir" -ForegroundColor Cyan
Write-Host "🔧 可执行文件: $TargetExe" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 使用方法:" -ForegroundColor Yellow
Write-Host "   $TargetExe --help" -ForegroundColor White
Write-Host ""
