import { v4 as uuidv4 } from "uuid";
import type { RenderableTransientEvent, RenderableContinuousEvent, RenderableContinuousCurvePoint, CreateEventConfig } from "../types";
import { getTransientDuration } from "@/types/haptic-file";

/**
 * 事件创建工厂函数
 */

/**
 * 创建瞬态事件
 */
export function createTransientEvent(config: CreateEventConfig): RenderableTransientEvent {
  const { startTime, intensity, frequency, availableSpace } = config;

  // 获取可用空间，如果未提供则使用一个大值
  const availableTimeSpace = availableSpace || 1000;

  // 生成唯一ID
  const id = uuidv4();

  // 根据可用空间动态调整频率，频率影响transient的width
  // 频率越高，宽度越窄；频率越低，宽度越宽
  let adjustedFrequency = frequency;
  let width = getTransientDuration(adjustedFrequency);

  // 如果计算出的宽度超过可用空间，动态调整频率使宽度合适
  if (width > availableTimeSpace && availableTimeSpace >= 8) {
    // 频率越高，width越小，所以增加频率
    // 最高频率100对应的width约为8ms
    adjustedFrequency = Math.min(100, Math.ceil(frequency * (width / availableTimeSpace) * 1.1));
    width = getTransientDuration(adjustedFrequency);
  }

  const peakTime = Math.floor(startTime + width / 2);
  const stopTime = Math.floor(startTime + width);

  return {
    type: "transient",
    id,
    startTime,
    peakTime,
    stopTime,
    intensity,
    frequency: adjustedFrequency,
    width,
  };
}

/**
 * 创建连续事件
 */
export function createContinuousEvent(config: CreateEventConfig): RenderableContinuousEvent {
  const { startTime, intensity, frequency, duration, availableSpace } = config;

  // 获取可用空间，如果未提供则使用一个大值
  const availableTimeSpace = availableSpace || 1000;

  // 生成唯一ID
  const id = uuidv4();

  // 根据可用空间动态调整持续时间
  // 确保持续时间不会超过可用空间
  let adjustedDuration = duration || 100;

  // 如果持续时间超过可用空间，则使用可用空间的95%作为持续时间
  // 保留5%作为安全边际
  if (adjustedDuration > availableTimeSpace && availableTimeSpace >= 25) {
    adjustedDuration = Math.floor(availableTimeSpace * 0.95);
  } else {
    // 确保最小持续时间为25ms
    adjustedDuration = Math.max(25, adjustedDuration);
  }

  const stopTime = Math.floor(startTime + adjustedDuration);

  // 创建默认的曲线点，时间点按照比例分布
  const curves: RenderableContinuousCurvePoint[] = [
    {
      timeOffset: 0,
      drawIntensity: 0,
      rawIntensity: 0,
      relativeCurveFrequency: 0,
      curveFrequency: frequency,
    },
    {
      timeOffset: adjustedDuration * 0.25,
      drawIntensity: intensity * 0.7,
      rawIntensity: 0.7,
      relativeCurveFrequency: 0,
      curveFrequency: frequency,
    },
    {
      timeOffset: adjustedDuration * 0.5,
      drawIntensity: intensity * 0.9,
      rawIntensity: 0.9,
      relativeCurveFrequency: 0,
      curveFrequency: frequency,
    },
    {
      timeOffset: adjustedDuration,
      drawIntensity: 0,
      rawIntensity: 0,
      relativeCurveFrequency: 0,
      curveFrequency: frequency,
    },
  ];

  return {
    type: "continuous",
    id,
    startTime,
    stopTime,
    duration: adjustedDuration,
    originalEventDuration: adjustedDuration,
    eventIntensity: intensity,
    eventFrequency: frequency,
    curves,
  };
}

/**
 * 根据配置创建事件
 */
export function createEvent(config: CreateEventConfig): RenderableTransientEvent | RenderableContinuousEvent {
  if (config.type === "transient") {
    return createTransientEvent(config);
  } else if (config.type === "continuous") {
    return createContinuousEvent(config);
  } else {
    throw new Error(`Unsupported event type: ${config.type}`);
  }
}
