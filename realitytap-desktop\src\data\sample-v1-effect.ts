// src/data/sample-v1-effect.ts
import type { RealityTapEffectV1 } from "@/types/haptic-file";

export const sampleV1TestData: RealityTapEffectV1 = {
  Metadata: {
    Created: "2020-08-10",
    Description: "Haptic editor design (Sample V1 Data)",
    Version: 1,
  },
  Pattern: [
    // 第一个事件
    {
      Event: {
        Type: "transient",
        RelativeTime: 40,
        Parameters: {
          Frequency: 10,
          Intensity: 100,
        },
      },
    },
    // 第二个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 100,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: -100 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: 100 },
          ],
        },
      },
    },
    // 第三个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 800,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
    // 第四个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 1200,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
    // 第五个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 1600,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
    // 第六个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 2000,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
    // 第七个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 2400,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
    // 第八个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 2800,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
    // 第九个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 3200,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
    // 第十个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 3600,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
    // 第十一个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 4000,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
    // 第十二个事件
    {
      Event: {
        Type: "continuous",
        RelativeTime: 4400,
        Duration: 400,
        Parameters: {
          Frequency: 50,
          Intensity: 100,
          Curve: [
            { Time: 0, Intensity: 0, Frequency: 10 },
            { Time: 50, Intensity: 0.5, Frequency: 10 },
            { Time: 180, Intensity: 0.1, Frequency: -10 },
            { Time: 400, Intensity: 0, Frequency: -10 },
          ],
        },
      },
    },
  ],
};
