import { nextTick, ref, type Ref } from "vue";
import { ENABLE_GUIDE_LINES } from "../utils/drawing-helpers";
import { waveformLogger } from "@/utils/logger/logger";

// 辅助线配置接口
export interface GuideConfig {
  canvasHeight: Ref<number>;
  padding: { top: number; right: number; bottom: number; left: number };
  mapYToIntensityLocal: (y: number) => number;
  getGraphAreaWidth: () => number;
}

export function useWaveformGuides(config: GuideConfig, drawWaveform: (forceRedraw?: boolean) => void) {
  // 右键辅助线状态
  const isRightClickGuideVisible = ref<boolean>(false);
  const rightClickGuidePosition = ref<{ x: number; y: number }>({ x: 0, y: 0 });
  const rightClickGuideTime = ref<number>(0);
  const rightClickGuideIntensity = ref<number>(0);

  // 隐藏右键辅助线
  const hideRightClickGuide = (shouldRedraw: boolean = true) => {
    if (isRightClickGuideVisible.value) {
      isRightClickGuideVisible.value = false;
      if (shouldRedraw) {
        nextTick(() => {
          drawWaveform(true);
        });
      }
    }
  };

  // 显示右键辅助线
  const showRightClickGuide = (x: number, y: number, timePoint: number) => {
    waveformLogger.debug("showRightClickGuide called:", {
      x,
      y,
      timePoint,
      ENABLE_GUIDE_LINES,
    });

    if (ENABLE_GUIDE_LINES) {
      isRightClickGuideVisible.value = true;
      rightClickGuidePosition.value = { x, y };
      rightClickGuideTime.value = timePoint;
      rightClickGuideIntensity.value = config.mapYToIntensityLocal(y);

      waveformLogger.debug("Guide line state set:", {
        isVisible: isRightClickGuideVisible.value,
        position: rightClickGuidePosition.value,
        time: rightClickGuideTime.value,
        intensity: rightClickGuideIntensity.value,
      });

      nextTick(() => {
        waveformLogger.debug("About to call drawWaveform for guide lines");
        drawWaveform(true);
      });
    }
  };

  // 实时更新右键辅助线位置（用于拖拽时更新）
  const updateRightClickGuide = (x: number, y: number, timePoint: number) => {
    if (ENABLE_GUIDE_LINES && isRightClickGuideVisible.value) {
      rightClickGuidePosition.value = { x, y };
      rightClickGuideTime.value = timePoint;
      rightClickGuideIntensity.value = config.mapYToIntensityLocal(y);

      // 立即重绘以更新辅助线位置
      nextTick(() => {
        drawWaveform(true);
      });
    }
  };

  // 绘制辅助线
  const drawGuideLines = (ctx: CanvasRenderingContext2D, isDragging: boolean) => {    
    if (!ENABLE_GUIDE_LINES || !isRightClickGuideVisible.value || isDragging) {
      return;
    }

    const position = rightClickGuidePosition.value;
    const intensity = rightClickGuideIntensity.value;
    const timeValue = rightClickGuideTime.value;

    // 设置虚线样式
    ctx.setLineDash([5, 3]);
    ctx.lineWidth = 1;
    ctx.strokeStyle = "#ffffff";

    // 绘制Y轴虚线 (垂直线)
    ctx.beginPath();
    ctx.moveTo(position.x, config.padding.top);
    ctx.lineTo(position.x, config.canvasHeight.value - config.padding.bottom);
    ctx.stroke();

    // 只有当强度大于0时才绘制X轴虚线 (水平线)
    if (intensity > 0) {
      ctx.beginPath();
      ctx.moveTo(0, position.y);
      ctx.lineTo(config.getGraphAreaWidth(), position.y);
      ctx.stroke();
    }

    // 在坐标轴上标记数值
    ctx.setLineDash([]); // 恢复实线
    ctx.font = "12px Arial";
    ctx.fillStyle = "#ffffff";

    // 标记时间值 (底部X轴)
    ctx.textAlign = "center";
    ctx.textBaseline = "top";
    ctx.fillText(`${timeValue.toFixed(0)}ms`, position.x, config.canvasHeight.value - config.padding.bottom + 5);

    // 标记强度值 (左侧Y轴)
    ctx.textAlign = "right";
    ctx.textBaseline = "middle";
    ctx.fillText(`${Math.round(intensity)}`, config.padding.left - 5, position.y);
  };

  return {
    // 状态
    isRightClickGuideVisible,
    rightClickGuidePosition,
    rightClickGuideTime,
    rightClickGuideIntensity,

    // 方法
    hideRightClickGuide,
    showRightClickGuide,
    updateRightClickGuide,
    drawGuideLines,
  };
}
