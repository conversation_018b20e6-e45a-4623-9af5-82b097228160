import { ref, computed } from "vue";
import type { RenderableEvent } from "@/types/haptic-editor";
import { useFileWaveformEditorStore } from "@/stores/haptics-editor-store";
import { hitTestEvents } from "../utils/hit-test-service";
import { SELECTED_POINT_RADIUS } from "../utils/drawing-helpers";

/**
 * 波形图事件选择管理 Composable
 * 负责处理事件的选择、命中检测和选择状态管理
 *
 * @param waveformStore 文件级别的 waveform store 实例（必需）
 */
export function useWaveformSelection(waveformStore: ReturnType<typeof useFileWaveformEditorStore>) {
  const actualStore = waveformStore;

  // 本地选择状态
  const currentlySelectedEventId = ref<string | null>(null);
  const contextMenuEventId = ref<string | null>(null);

  // 拖动后点击忽略机制
  let lastDragEndTime = 0;
  const CLICK_IGNORE_THRESHOLD_MS = 100;

  /**
   * 判断事件是否处于选中状态
   */
  const isEventSelected = (event: RenderableEvent): boolean => {
    return actualStore.selectedEventId === event.id;
  };

  /**
   * 获取当前选中的事件ID
   */
  const selectedEventId = computed(() => actualStore.selectedEventId);

  /**
   * 获取当前选中的曲线点索引
   */
  const selectedCurvePointIndex = computed(() => actualStore.selectedCurvePointIndex);

  /**
   * 执行命中检测
   */
  const performHitTest = (x: number, y: number, events: RenderableEvent[], mapTimeToX: (time: number) => number, mapIntensityToY: (intensity: number) => number) => {
    return hitTestEvents(x, y, events, mapTimeToX, mapIntensityToY, {
      pointRadius: SELECTED_POINT_RADIUS * 2,
      curvePointRadius: SELECTED_POINT_RADIUS * 2,
    });
  };

  /**
   * 处理事件选择逻辑
   */
  const handleEventSelection = (hitResult: ReturnType<typeof hitTestEvents>, emit: (event: string, ...args: any[]) => void) => {
    const clickedEventId = hitResult.event?.id || null;
    const clickedCurvePointIndex = hitResult.curvePointIndex;

    // 使用store管理选中状态
    if (clickedEventId) {
      if (actualStore.selectedEventId !== clickedEventId) {
        actualStore.selectEvent(clickedEventId);
        currentlySelectedEventId.value = clickedEventId;
      } else {
        if (clickedCurvePointIndex !== actualStore.selectedCurvePointIndex) {
          actualStore.selectCurvePoint(clickedCurvePointIndex);
        }
      }

      // 如果点击事件体但之前选中了曲线点，则取消曲线点选择
      if (actualStore.selectedEventId === clickedEventId && clickedCurvePointIndex === -1 && actualStore.selectedCurvePointIndex !== -1) {
        actualStore.selectCurvePoint(-1);
      }
    } else {
      // 点击空白区域，取消选择
      actualStore.selectEvent(null);
      currentlySelectedEventId.value = null;
    }

    // 发射选择事件
    emit("event-selected", currentlySelectedEventId.value);
  };

  /**
   * 处理右键菜单的命中检测
   */
  const handleContextMenuHitTest = (x: number, y: number, events: RenderableEvent[], mapTimeToX: (time: number) => number, mapIntensityToY: (intensity: number) => number) => {
    contextMenuEventId.value = null;

    const hitResult = performHitTest(x, y, events, mapTimeToX, mapIntensityToY);
    if (hitResult.event) {
      contextMenuEventId.value = hitResult.event.id;
    }

    return hitResult;
  };

  /**
   * 检查是否应该忽略点击（拖动后的幽灵点击）
   */
  const shouldIgnoreClick = (): boolean => {
    return Date.now() - lastDragEndTime < CLICK_IGNORE_THRESHOLD_MS;
  };

  /**
   * 设置拖动结束时间（用于忽略幽灵点击）
   */
  const setDragEndTime = () => {
    lastDragEndTime = Date.now();
  };

  /**
   * 选择指定的事件
   */
  const selectEvent = (eventId: string | null) => {
    actualStore.selectEvent(eventId);
    currentlySelectedEventId.value = eventId;
  };

  /**
   * 选择指定的曲线点
   */
  const selectCurvePoint = (index: number) => {
    actualStore.selectCurvePoint(index);
  };

  /**
   * 清除所有选择
   */
  const clearSelection = () => {
    actualStore.selectEvent(null);
    actualStore.selectCurvePoint(-1);
    currentlySelectedEventId.value = null;
    contextMenuEventId.value = null;
  };

  return {
    // 状态
    currentlySelectedEventId,
    contextMenuEventId,
    selectedEventId,
    selectedCurvePointIndex,

    // 方法
    isEventSelected,
    performHitTest,
    handleEventSelection,
    handleContextMenuHitTest,
    shouldIgnoreClick,
    setDragEndTime,
    selectEvent,
    selectCurvePoint,
    clearSelection,
  };
}
