import type { RenderableEvent } from "../types";
import { HistoryManager, type HistoryConfig } from "@/utils/waveform/HistoryManager";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 历史管理相关接口
 */
export interface HistoryActions {
  initHistorySystem: () => HistoryManager | null;
  ensureHistoryInitialized: () => void;
  getHistorySystem: () => HistoryManager | null;
  undo: () => boolean;
  redo: () => boolean;
  canUndo: () => boolean;
  canRedo: () => boolean;
  addHistoryRecord: (description?: string) => boolean;
  getHistoryState: () => any;
  configureHistory: (options: HistoryConfig) => void;
  clearHistory: () => void;
  destroyHistorySystem: () => void;
}

/**
 * 创建历史管理操作
 */
export function createHistoryActions(
  state: {
    events: RenderableEvent[];
  },
  setBatchEvents: (events: RenderableEvent[], options?: any) => void
): HistoryActions {
  let historyManager: HistoryManager | null = null;

  return {
    /**
     * 初始化历史管理系统
     */
    initHistorySystem() {
      if (!historyManager) {
        // 创建历史管理器
        const config: HistoryConfig = {
          maxRecords: 50,
          enableDebug: true,
          enableChangeDetection: true,
          changeThreshold: 0.001,
        };

        historyManager = new HistoryManager(config);

        // 延迟初始化：只有当events不为空时才初始化历史记录
        // 这样可以避免将空数组作为初始状态记录
        if (state.events.length > 0) {
          historyManager.initialize([...state.events]);
          logger.debug(LogModule.HISTORY, "HistoryManager: 初始化完成", {
            eventsCount: state.events.length
          });
        } else {
          logger.debug(LogModule.HISTORY, "HistoryManager: 创建完成，等待真实数据后初始化");
        }
      }
      return historyManager;
    },

    /**
     * 确保历史记录系统已正确初始化
     * 当检测到events从空变为非空时调用此方法
     */
    ensureHistoryInitialized() {
      if (!historyManager) {
        this.initHistorySystem();
        return;
      }

      // 检查历史记录是否已经初始化（undoStack是否为空）
      const historyState = historyManager.getState();
      if (historyState.undoStackSize === 0 && state.events.length > 0) {
        // 历史记录未初始化但现在有数据了，进行初始化
        historyManager.initialize([...state.events]);
        logger.debug(LogModule.HISTORY, "HistoryManager: 延迟初始化完成", {
          eventsCount: state.events.length
        });

        // 立即添加一个额外的记录，确保有足够的历史记录来启用撤销
        // 这样用户在第一次修改后就能立即使用撤销功能
        setTimeout(() => {
          if (historyManager && state.events.length > 0) {
            historyManager.addRecord([...state.events], "准备撤销");
          }
        }, 50);
      }
    },

    /**
     * 获取历史管理系统实例
     */
    getHistorySystem() {
      // 如果历史系统还没有初始化，自动初始化它
      if (!historyManager) {
        this.initHistorySystem();
      }
      return historyManager;
    },

    /**
     * 撤销操作
     */
    undo(): boolean {
      logger.info(LogModule.HISTORY, "🔄 Store.undo() 被调用", {
        hasHistoryManager: !!historyManager,
        currentEventsCount: state.events.length,
        timestamp: new Date().toLocaleTimeString(),
      });

      if (!historyManager) {
        logger.warn(LogModule.HISTORY, "❌ 撤销失败：历史系统不可用");
        return false;
      }

      const resultData = historyManager.undo();

      if (resultData !== null) {
        logger.info(LogModule.HISTORY, "✅ 撤销成功", {
          dataLength: resultData.length,
          canUndo: historyManager.canUndo(),
          canRedo: historyManager.canRedo(),
          firstEventData: resultData.length > 0 ? {
            id: resultData[0].id,
            type: resultData[0].type,
            startTime: resultData[0].startTime,
            ...(resultData[0].type === 'transient' ? {
              frequency: (resultData[0] as any).frequency,
              intensity: (resultData[0] as any).intensity
            } : {
              eventFrequency: (resultData[0] as any).eventFrequency,
              eventIntensity: (resultData[0] as any).eventIntensity,
              curvesCount: (resultData[0] as any).curves?.length || 0,
              firstCurveFrequency: (resultData[0] as any).curves?.[0]?.curveFrequency
            })
          } : null
        });

        // 应用撤销后的数据
        setBatchEvents(resultData, {
          skipFileStateSync: false,
          preserveSelection: false,
          skipHistoryRecord: true, // 重要：跳过历史记录，避免循环
        });

        return true;
      } else {
        logger.warn(LogModule.HISTORY, "⚠️ 撤销失败：无可撤销的操作");
        return false;
      }
    },

    /**
     * 重做操作
     */
    redo(): boolean {
      if (!historyManager) return false;

      const resultData = historyManager.redo();

      if (resultData !== null) {
        logger.debug(LogModule.HISTORY, `重做操作成功: ${resultData.length} 个事件`);

        // 应用重做后的数据
        setBatchEvents(resultData, {
          skipFileStateSync: false,
          preserveSelection: false,
          skipHistoryRecord: true, // 重要：跳过历史记录，避免循环
        });

        return true;
      } else {
        logger.debug(LogModule.HISTORY, "重做失败：无可重做的操作");
        return false;
      }
    },

    /**
     * 检查是否可以撤销
     */
    canUndo(): boolean {
      return historyManager ? historyManager.canUndo() : false;
    },

    /**
     * 检查是否可以重做
     */
    canRedo(): boolean {
      return historyManager ? historyManager.canRedo() : false;
    },

    /**
     * 添加历史记录
     */
    addHistoryRecord(description?: string): boolean {
      if (!historyManager) {
        logger.warn(LogModule.HISTORY, "addHistoryRecord失败：historyManager未初始化");
        return false;
      }

      const result = historyManager.addRecord([...state.events], description);

      if (!result) {
        logger.debug(LogModule.HISTORY, "addHistoryRecord被拒绝", {
          description,
          eventsCount: state.events.length,
          canUndo: historyManager.canUndo(),
          canRedo: historyManager.canRedo(),
          historyState: historyManager.getState()
        });
      }

      return result;
    },

    /**
     * 获取历史系统状态
     */
    getHistoryState() {
      return historyManager ? historyManager.getState() : null;
    },

    /**
     * 配置历史系统
     */
    configureHistory(options: HistoryConfig): void {
      if (historyManager) {
        historyManager.configure(options);
      }
    },

    /**
     * 清空历史记录
     */
    clearHistory(): void {
      if (historyManager) {
        historyManager.clear();
        // 重新初始化当前状态
        historyManager.initialize([...state.events]);
      }
    },

    /**
     * 销毁历史管理系统
     */
    destroyHistorySystem() {
      if (historyManager) {
        historyManager.destroy();
        historyManager = null;
      }
    },
  };
}
