#!/bin/bash
# FFmpeg 下载脚本
# 此脚本会下载适用于不同平台的 FFmpeg 二进制文件

set -e

# 默认参数
PLATFORM="all"
FORCE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --force)
            FORCE=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [--platform <platform>] [--force]"
            echo "  --platform: 指定平台 (linux-x64, linux-arm64, macos, all)"
            echo "  --force: 强制重新下载"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# FFmpeg 版本和下载 URL
FFMPEG_VERSION="6.0"
BASE_URL="https://github.com/BtbN/FFmpeg-Builds/releases/download/latest"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FFMPEG_DIR="$SCRIPT_DIR/../src-tauri/ffmpeg"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 下载并解压函数
download_and_extract() {
    local url="$1"
    local target_dir="$2"
    local binaries=("${@:3}")
    
    local temp_dir=$(mktemp -d)
    local archive_file="$temp_dir/ffmpeg.tar.xz"
    
    trap "rm -rf '$temp_dir'" EXIT
    
    # 创建目标目录
    mkdir -p "$target_dir"
    
    log_info "下载 FFmpeg 从 $url"
    if command_exists curl; then
        curl -L -o "$archive_file" "$url"
    elif command_exists wget; then
        wget -O "$archive_file" "$url"
    else
        log_error "需要 curl 或 wget 来下载文件"
        return 1
    fi
    
    log_info "解压文件"
    tar -xf "$archive_file" -C "$temp_dir"
    
    # 查找解压后的目录
    local extracted_dir=$(find "$temp_dir" -maxdepth 1 -type d -name "*ffmpeg*" | head -n 1)
    if [[ -z "$extracted_dir" ]]; then
        log_error "无法找到解压后的 FFmpeg 目录"
        return 1
    fi
    
    local bin_dir="$extracted_dir/bin"
    if [[ ! -d "$bin_dir" ]]; then
        log_error "无法找到 FFmpeg bin 目录"
        return 1
    fi
    
    # 复制二进制文件
    for binary in "${binaries[@]}"; do
        local source_path="$bin_dir/$binary"
        local target_path="$target_dir/$binary"
        
        if [[ -f "$source_path" ]]; then
            log_info "复制 $binary 到 $target_path"
            cp "$source_path" "$target_path"
            chmod +x "$target_path"
        else
            log_warning "未找到 $binary 在 $source_path"
        fi
    done
    
    log_info "成功下载并提取 FFmpeg 二进制文件到 $target_dir"
}

# 主函数
main() {
    log_info "开始下载 FFmpeg 二进制文件"
    
    # 创建 FFmpeg 目录
    mkdir -p "$FFMPEG_DIR"
    
    # 平台配置
    declare -A platforms
    platforms["linux-x64"]="$BASE_URL/ffmpeg-master-latest-linux64-gpl.tar.xz linux/x64 ffmpeg ffprobe"
    platforms["linux-arm64"]="$BASE_URL/ffmpeg-master-latest-linuxarm64-gpl.tar.xz linux/arm64 ffmpeg ffprobe"
    platforms["macos"]="$BASE_URL/ffmpeg-master-latest-macos64-gpl.tar.xz macos ffmpeg ffprobe"
    
    # 确定要下载的平台
    local platforms_to_download=()
    if [[ "$PLATFORM" == "all" ]]; then
        platforms_to_download=($(echo "${!platforms[@]}"))
    elif [[ -n "${platforms[$PLATFORM]}" ]]; then
        platforms_to_download=("$PLATFORM")
    else
        log_error "不支持的平台: $PLATFORM. 支持的平台: ${!platforms[@]}, all"
        exit 1
    fi
    
    for platform_key in "${platforms_to_download[@]}"; do
        local config=(${platforms[$platform_key]})
        local url="${config[0]}"
        local dir="${config[1]}"
        local binaries=("${config[@]:2}")
        
        local target_dir="$FFMPEG_DIR/$dir"
        
        # 检查是否已存在
        local all_binaries_exist=true
        for binary in "${binaries[@]}"; do
            if [[ ! -f "$target_dir/$binary" ]]; then
                all_binaries_exist=false
                break
            fi
        done
        
        if [[ "$all_binaries_exist" == true && "$FORCE" == false ]]; then
            log_info "平台 $platform_key 的 FFmpeg 二进制文件已存在，跳过下载"
            continue
        fi
        
        log_info "下载平台 $platform_key 的 FFmpeg"
        if ! download_and_extract "$url" "$target_dir" "${binaries[@]}"; then
            log_error "下载平台 $platform_key 失败"
            exit 1
        fi
    done
    
    log_info "所有 FFmpeg 二进制文件下载完成"
}

# 运行主函数
main
