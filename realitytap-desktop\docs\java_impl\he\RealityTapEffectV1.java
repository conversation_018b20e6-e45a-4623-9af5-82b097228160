package android.os.vibrator.realitytap.he;

import android.annotation.NonNull;
import android.os.Parcel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/** @hide */
public class RealityTapEffectV1 extends RealityTapEffect {
    private final List<Event> pattern = new ArrayList<>();

    public RealityTapEffectV1() {
        super(VERSION_1);
    }

    protected RealityTapEffectV1(@NonNull Parcel in) {
        super(VERSION_1);
        setMetadata(in.readParcelable(Metadata.class.getClassLoader()));
        pattern.addAll(in.createTypedArrayList(Event.CREATOR));
    }

    public static final @NonNull Creator<RealityTapEffectV1> CREATOR = new Creator<>() {
        @Override
        public RealityTapEffectV1 createFromParcel(@NonNull Parcel in) {
            return new RealityTapEffectV1(in);
        }

        @Override
        public RealityTapEffectV1[] newArray(int size) {
            return new RealityTapEffectV1[size];
        }
    };

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeParcelable(getMetadata(), flags);
        dest.writeTypedList(pattern);
    }

    public List<Event> getPattern() {
        return pattern;
    }

    @Override
    public int getDuration() {
        if (pattern.isEmpty()) {
            return 0;
        }

        final int size = pattern.size();
        Event lastEvent = pattern.get(size - 1);
        return lastEvent.getRelativeTime() + lastEvent.getDuration();
    }

    /** @hide */
    public RealityTapEffectV2 convertToV2() {
        RealityTapEffectV2 v2 = new RealityTapEffectV2();
        Metadata metadata = getMetadata().copy();
        metadata.setVersion(VERSION_2);
        v2.setMetadata(metadata);

        PatternListItem patternListItem = new PatternListItem();
        for (Event event : pattern) {
            Event newEvent = event.copy();
            patternListItem.getPatterns().add(newEvent);
        }
        v2.getPatternList().add(patternListItem);
        return v2;
    }

    @Override
    public int[] convertToArray() {
        final int length = getPattern().size();
        int[] array = new int[length * 17];
        Arrays.fill(array, 0);

        for (int i = 0; i < length; i++) {
            int index = i * 17;
            Event event = getPattern().get(i);
            String type = event.getType();

            if (Event.TYPE_TRANSIENT.equals(type)) {
                array[index++] = Event.TRANSIENT;
                array[index++] = event.getRelativeTime();
                array[index++] = event.getParameters().getIntensity();
                array[index] = event.getParameters().getFrequency();
            } else if (Event.TYPE_CONTINUOUS.equals(type)) {
                List<Curve> curves = event.getParameters().getCurves();
                array[index++] = Event.CONTINUOUS;
                array[index++] = event.getRelativeTime();
                array[index++] = event.getParameters().getIntensity();
                array[index++] = event.getParameters().getFrequency();
                array[index++] = event.getDuration();
                for (int j = 0; j < 4; j++) {
                    array[index++] = curves.get(j).getTime();
                    array[index++] = (int) (curves.get(j).getIntensity() * 100);
                    array[index++] = curves.get(j).getFrequency();
                }
            }
        }

        return array;
    }

    @NonNull
    @Override
    public RealityTapEffectV1 copy() {
        RealityTapEffectV1 copy = new RealityTapEffectV1();
        copy.setMetadata(getMetadata() != null ? getMetadata().copy() : null);
        for (Event event : pattern) {
            copy.getPattern().add(event.copy());
        }
        return copy;
    }

    @NonNull
    @Override
    public String toString() {
        return "RealityTapEffectV1{" +
                "pattern=" + pattern +
                ", metadata=" + metadata +
                '}';
    }
}
