import { Request } from 'express';
import { logger } from './logger.util';

/**
 * IP地址工具类
 * 提供获取真实客户端IP地址的功能，正确处理代理服务器情况
 */
export class IPUtil {
  /**
   * 获取客户端真实IP地址
   * 按优先级检查各种IP来源：
   * 1. X-Forwarded-For (最左边的IP，即原始客户端IP)
   * 2. X-Real-IP
   * 3. X-Client-IP
   * 4. CF-Connecting-IP (Cloudflare)
   * 5. req.ip (Express默认，已考虑trust proxy设置)
   * 6. req.connection.remoteAddress
   * 7. req.socket.remoteAddress
   * 
   * @param req Express请求对象
   * @returns 客户端真实IP地址
   */
  static getRealClientIP(req: Request): string {
    try {
      // 1. 检查 X-Forwarded-For 头部（最常用的代理头部）
      const xForwardedFor = req.headers['x-forwarded-for'] as string;
      if (xForwardedFor) {
        // X-Forwarded-For 可能包含多个IP，格式：client, proxy1, proxy2
        // 第一个IP是原始客户端IP
        const ips = xForwardedFor.split(',').map(ip => ip.trim());
        const clientIP = ips[0];
        if (clientIP && this.isValidIP(clientIP)) {
          return clientIP;
        }
      }

      // 2. 检查 X-Real-IP 头部（nginx常用）
      const xRealIP = req.headers['x-real-ip'] as string;
      if (xRealIP && this.isValidIP(xRealIP)) {
        return xRealIP;
      }

      // 3. 检查 X-Client-IP 头部
      const xClientIP = req.headers['x-client-ip'] as string;
      if (xClientIP && this.isValidIP(xClientIP)) {
        return xClientIP;
      }

      // 4. 检查 CF-Connecting-IP 头部（Cloudflare）
      const cfConnectingIP = req.headers['cf-connecting-ip'] as string;
      if (cfConnectingIP && this.isValidIP(cfConnectingIP)) {
        return cfConnectingIP;
      }

      // 5. 使用 Express 的 req.ip（已考虑 trust proxy 设置）
      if (req.ip && this.isValidIP(req.ip)) {
        return req.ip;
      }

      // 6. 检查连接的远程地址
      const connectionIP = (req as any).connection?.remoteAddress;
      if (connectionIP && this.isValidIP(connectionIP)) {
        return connectionIP;
      }

      // 7. 检查socket的远程地址
      const socketIP = (req as any).socket?.remoteAddress;
      if (socketIP && this.isValidIP(socketIP)) {
        return socketIP;
      }

      // 如果都没有找到有效IP，返回默认值
      return 'unknown';
    } catch (error) {
      logger.warn('Failed to get real client IP', { error });
      return 'unknown';
    }
  }

  /**
   * 验证IP地址格式是否有效
   * @param ip IP地址字符串
   * @returns 是否为有效IP地址
   */
  static isValidIP(ip: string): boolean {
    if (!ip || typeof ip !== 'string') {
      return false;
    }

    // 移除可能的端口号
    const cleanIP = ip.split(':')[0];

    if (!cleanIP) {
      return false;
    }

    // IPv4 正则表达式
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

    // IPv6 正则表达式（简化版）
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

    return ipv4Regex.test(cleanIP) || ipv6Regex.test(cleanIP);
  }

  /**
   * 获取详细的IP信息，包括所有可能的IP来源
   * 用于调试和日志记录
   * @param req Express请求对象
   * @returns IP信息对象
   */
  static getDetailedIPInfo(req: Request): {
    realIP: string;
    sources: {
      xForwardedFor?: string;
      xRealIP?: string;
      xClientIP?: string;
      cfConnectingIP?: string;
      reqIP?: string;
      connectionIP?: string;
      socketIP?: string;
    };
  } {
    const realIP = this.getRealClientIP(req);
    
    const sources = {
      xForwardedFor: req.headers['x-forwarded-for'] as string,
      xRealIP: req.headers['x-real-ip'] as string,
      xClientIP: req.headers['x-client-ip'] as string,
      cfConnectingIP: req.headers['cf-connecting-ip'] as string,
      reqIP: req.ip,
      connectionIP: (req as any).connection?.remoteAddress,
      socketIP: (req as any).socket?.remoteAddress,
    };

    // 移除undefined值
    Object.keys(sources).forEach(key => {
      if (sources[key as keyof typeof sources] === undefined) {
        delete sources[key as keyof typeof sources];
      }
    });

    return { realIP, sources };
  }

  /**
   * 脱敏IP地址（继承自现有的StatsUtil，但增强功能）
   * @param ip IP地址
   * @returns 脱敏后的IP地址
   */
  static anonymizeIP(ip: string): string {
    try {
      if (!ip || ip === 'unknown') {
        return 'unknown';
      }

      // IPv4 脱敏：保留前三段，最后一段替换为 xxx
      if (ip.includes('.') && !ip.includes(':')) {
        const parts = ip.split('.');
        if (parts.length === 4) {
          return `${parts[0]}.${parts[1]}.${parts[2]}.xxx`;
        }
      }

      // IPv6 脱敏：保留前四段，后面替换为 xxxx
      if (ip.includes(':')) {
        const parts = ip.split(':');
        if (parts.length >= 4) {
          return `${parts.slice(0, 4).join(':')}:xxxx:xxxx:xxxx:xxxx`;
        }
      }

      return 'xxx.xxx.xxx.xxx';
    } catch (error) {
      logger.warn('Failed to anonymize IP', { ip, error });
      return 'xxx.xxx.xxx.xxx';
    }
  }

  /**
   * 检查IP是否为内网地址
   * @param ip IP地址
   * @returns 是否为内网地址
   */
  static isPrivateIP(ip: string): boolean {
    if (!this.isValidIP(ip)) {
      return false;
    }

    // IPv4 内网地址范围
    const privateIPv4Ranges = [
      /^10\./,                    // 10.0.0.0/8
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./, // **********/12
      /^192\.168\./,              // ***********/16
      /^127\./,                   // *********/8 (localhost)
      /^169\.254\./,              // ***********/16 (link-local)
    ];

    // IPv6 内网地址
    const privateIPv6Patterns = [
      /^::1$/,                    // localhost
      /^fe80:/,                   // link-local
      /^fc00:/,                   // unique local
      /^fd00:/,                   // unique local
    ];

    return privateIPv4Ranges.some(pattern => pattern.test(ip)) ||
           privateIPv6Patterns.some(pattern => pattern.test(ip));
  }
}
