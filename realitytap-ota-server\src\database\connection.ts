import path from 'path';
import fs from 'fs-extra';
import sqlite3 from 'sqlite3';
import { logger } from '@/utils/logger.util';

// 定义基本的数据库类型
interface RunResult {
  lastID?: number;
  changes?: number;
}

export interface DatabaseConfig {
  path: string;
  backupPath: string;
  maxConnections: number;
  busyTimeout: number;
  enableWAL: boolean;
}

export class DatabaseConnection {
  private static instance: DatabaseConnection;
  private db: any | null = null;
  private config: DatabaseConfig;
  private isInitialized = false;

  private constructor(config: DatabaseConfig) {
    this.config = config;
  }

  public static getInstance(config?: DatabaseConfig): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      if (!config) {
        throw new Error('Database config is required for first initialization');
      }
      DatabaseConnection.instance = new DatabaseConnection(config);
    }
    return DatabaseConnection.instance;
  }

  /**
   * 初始化数据库连接
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // 确保数据库目录存在
      await fs.ensureDir(path.dirname(this.config.path));
      await fs.ensureDir(this.config.backupPath);

      // 创建数据库连接
      logger.info('Initializing SQLite database', { path: this.config.path });

      // 检查是否启用真正的数据库
      const dbEnabled = process.env.DB_ENABLED === 'true';

      if (dbEnabled) {
        // 使用真正的SQLite数据库
        this.db = await this.createRealDatabase();
        logger.info('Real SQLite database initialized');
      } else {
        // 使用模拟数据库（向后兼容）
        this.db = this.createMockDatabase();
        logger.info('Mock database initialized');
      }

      // 配置数据库
      await this.configureDatabase();

      this.isInitialized = true;
      logger.info('Database initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database', { error });
      throw new Error(`Database initialization failed: ${error}`);
    }
  }

  /**
   * 创建真正的SQLite数据库
   */
  private async createRealDatabase(): Promise<any> {
    return new Promise((resolve, reject) => {
      // 检查数据库目录权限
      const dbDir = path.dirname(this.config.path);
      try {
        fs.accessSync(dbDir, fs.constants.W_OK);
      } catch (error) {
        const permError = new Error(`Database directory is not writable: ${dbDir}. Please check permissions.`);
        (permError as any).code = 'EACCES';
        reject(permError);
        return;
      }

      const db = new sqlite3.Database(this.config.path, async (err) => {
        if (err) {
          // 提供更详细的错误信息
          const detailedError = new Error(
            `Failed to open SQLite database at ${this.config.path}. ` +
            `Error: ${err.message}. ` +
            `Code: ${(err as any).code || 'UNKNOWN'}. ` +
            `Please check: 1) Directory permissions, 2) Disk space, 3) File locks.`
          );
          (detailedError as any).code = (err as any).code;
          (detailedError as any).errno = (err as any).errno;
          reject(detailedError);
          return;
        }

        try {
          // 初始化数据库结构
          await this.initializeDatabaseSchema(db);

          // 包装数据库方法为Promise
          const wrappedDb = {
            run: (sql: string, params: any[] = []) => {
              return new Promise<RunResult>((resolve, reject) => {
                db.run(sql, params, function(err) {
                  if (err) reject(err);
                  else resolve({ lastID: this.lastID, changes: this.changes });
                });
              });
            },
            get: (sql: string, params: any[] = []) => {
              return new Promise((resolve, reject) => {
                db.get(sql, params, (err, row) => {
                  if (err) reject(err);
                  else resolve(row);
                });
              });
            },
            all: (sql: string, params: any[] = []) => {
              return new Promise((resolve, reject) => {
                db.all(sql, params, (err, rows) => {
                  if (err) reject(err);
                  else resolve(rows || []);
                });
              });
            },
            close: () => {
              return new Promise<void>((resolve, reject) => {
                db.close((err) => {
                  if (err) reject(err);
                  else resolve();
                });
              });
            }
          };

          resolve(wrappedDb);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * 初始化数据库结构
   */
  private async initializeDatabaseSchema(db: sqlite3.Database): Promise<void> {
    const schemas = [
      // 渠道表
      `CREATE TABLE IF NOT EXISTS channels (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(50) UNIQUE NOT NULL,
        enabled BOOLEAN NOT NULL DEFAULT 1,
        description TEXT,
        auto_update BOOLEAN NOT NULL DEFAULT 1,
        rollout_percentage INTEGER NOT NULL DEFAULT 100,
        priority INTEGER NOT NULL DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 版本表（包含强制更新字段）
      `CREATE TABLE IF NOT EXISTS versions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        channel_id INTEGER NOT NULL,
        version VARCHAR(50) NOT NULL,
        force_update BOOLEAN NOT NULL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (channel_id) REFERENCES channels(id) ON DELETE CASCADE,
        UNIQUE(channel_id, version)
      )`,

      // 平台发布表
      `CREATE TABLE IF NOT EXISTS platform_releases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version_id INTEGER NOT NULL,
        platform VARCHAR(20) NOT NULL,
        architecture VARCHAR(20) NOT NULL,
        filename VARCHAR(255) NOT NULL,
        file_size BIGINT NOT NULL,
        checksum VARCHAR(128) NOT NULL,
        signature TEXT,
        release_date DATETIME NOT NULL,
        release_notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (version_id) REFERENCES versions(id) ON DELETE CASCADE,
        UNIQUE(version_id, platform, architecture)
      )`,

      // 系统配置表
      `CREATE TABLE IF NOT EXISTS system_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        config_key VARCHAR(100) UNIQUE NOT NULL,
        config_value TEXT NOT NULL,
        config_type VARCHAR(20) NOT NULL DEFAULT 'string',
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 下载记录表
      `CREATE TABLE IF NOT EXISTS download_records (
        id VARCHAR(36) PRIMARY KEY,
        filename VARCHAR(255) NOT NULL,
        version VARCHAR(50) NOT NULL,
        platform VARCHAR(20) NOT NULL,
        architecture VARCHAR(20) NOT NULL,
        channel VARCHAR(50) NOT NULL,
        download_time DATETIME NOT NULL,
        client_ip VARCHAR(45) NOT NULL,
        user_agent TEXT,
        file_size BIGINT NOT NULL,
        download_duration INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 最低版本要求表
      `CREATE TABLE IF NOT EXISTS minimum_versions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        platform VARCHAR(20) NOT NULL,
        architecture VARCHAR(20) NOT NULL,
        minimum_version VARCHAR(50) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(platform, architecture)
      )`,

      // 废弃版本表
      `CREATE TABLE IF NOT EXISTS deprecated_versions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version VARCHAR(50) UNIQUE NOT NULL,
        deprecated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        reason TEXT
      )`,

      // 配置变更日志表
      `CREATE TABLE IF NOT EXISTS config_change_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        config_key VARCHAR(100) NOT NULL,
        old_value TEXT,
        new_value TEXT,
        user_id VARCHAR(50),
        change_reason TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 系统日志表
      `CREATE TABLE IF NOT EXISTS system_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp DATETIME NOT NULL,
        level VARCHAR(10) NOT NULL,
        message TEXT NOT NULL,
        module VARCHAR(50) NOT NULL,
        operation VARCHAR(100),
        client_ip VARCHAR(45),
        is_private_ip BOOLEAN DEFAULT 0,
        user_agent TEXT,
        path VARCHAR(500),
        method VARCHAR(10),
        status_code INTEGER,
        meta TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    // 执行所有schema创建语句
    for (const schema of schemas) {
      await new Promise<void>((resolve, reject) => {
        db.run(schema, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    }

    // 创建所有索引
    const indexes = [
      // 下载记录表索引
      'CREATE INDEX IF NOT EXISTS idx_download_records_time ON download_records(download_time)',
      'CREATE INDEX IF NOT EXISTS idx_download_records_version ON download_records(version)',
      'CREATE INDEX IF NOT EXISTS idx_download_records_platform ON download_records(platform)',
      'CREATE INDEX IF NOT EXISTS idx_download_records_channel ON download_records(channel)',
      'CREATE INDEX IF NOT EXISTS idx_download_records_composite ON download_records(version, platform, channel)',

      // 平台发布表索引
      'CREATE INDEX IF NOT EXISTS idx_platform_releases_version ON platform_releases(version_id)',
      'CREATE INDEX IF NOT EXISTS idx_platform_releases_platform ON platform_releases(platform, architecture)',

      // 渠道表索引
      'CREATE INDEX IF NOT EXISTS idx_channels_name ON channels(name)',
      'CREATE INDEX IF NOT EXISTS idx_channels_enabled ON channels(enabled)',

      // 版本表索引
      'CREATE INDEX IF NOT EXISTS idx_versions_channel ON versions(channel_id)',
      'CREATE INDEX IF NOT EXISTS idx_versions_version ON versions(version)',
      'CREATE INDEX IF NOT EXISTS idx_versions_force_update ON versions(force_update)',

      // 系统配置表索引
      'CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(config_key)',

      // 配置变更日志表索引
      'CREATE INDEX IF NOT EXISTS idx_config_logs_key ON config_change_logs(config_key)',
      'CREATE INDEX IF NOT EXISTS idx_config_logs_time ON config_change_logs(created_at)',

      // 系统日志表索引
      'CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level)',
      'CREATE INDEX IF NOT EXISTS idx_system_logs_module ON system_logs(module)',
      'CREATE INDEX IF NOT EXISTS idx_system_logs_operation ON system_logs(operation)',
      'CREATE INDEX IF NOT EXISTS idx_system_logs_client_ip ON system_logs(client_ip)',
      'CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at)'
    ];

    for (const indexSql of indexes) {
      await new Promise<void>((resolve, reject) => {
        db.run(indexSql, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    }

    // 插入默认渠道
    const defaultChannels = [
      ['stable', 1, '稳定版本，推荐生产环境使用', 1, 100, 1],
      ['beta', 1, '测试版本，包含最新功能', 0, 50, 2],
      ['alpha', 0, '开发版本，仅供内部测试', 0, 10, 3]
    ];

    const insertChannelSql = `INSERT OR IGNORE INTO channels (name, enabled, description, auto_update, rollout_percentage, priority) VALUES (?, ?, ?, ?, ?, ?)`;

    for (const channel of defaultChannels) {
      await new Promise<void>((resolve, reject) => {
        db.run(insertChannelSql, channel, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    }

    logger.info('Database schema initialized successfully');
  }

  /**
   * 创建模拟数据库对象
   */
  private createMockDatabase(): any {
    return {
      run: (sql: string, params: any[] = []) => {
        logger.debug('Mock DB run', { sql, params });
        return Promise.resolve({ lastID: 1, changes: 1 });
      },
      get: (sql: string, params: any[] = []) => {
        logger.debug('Mock DB get', { sql, params });
        // 返回一些模拟数据
        if (sql.includes('channels')) {
          return Promise.resolve({ id: 1, name: 'stable', enabled: 1 });
        }
        return Promise.resolve(undefined);
      },
      all: (sql: string, params: any[] = []) => {
        logger.debug('Mock DB all', { sql, params });
        // 返回一些模拟数据
        if (sql.includes('channels')) {
          return Promise.resolve([
            { id: 1, name: 'stable', enabled: 1, description: '稳定版本' },
            { id: 2, name: 'beta', enabled: 1, description: '测试版本' },
            { id: 3, name: 'alpha', enabled: 0, description: '开发版本' }
          ]);
        }
        return Promise.resolve([]);
      }
    };
  }

  /**
   * 配置数据库设置
   */
  private async configureDatabase(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const dbEnabled = process.env.DB_ENABLED === 'true';

    if (dbEnabled) {
      // 真正的SQLite数据库配置
      if (this.config.enableWAL) {
        await this.db.run('PRAGMA journal_mode = WAL');
      }
      await this.db.run('PRAGMA foreign_keys = ON');
      await this.db.run(`PRAGMA busy_timeout = ${this.config.busyTimeout}`);
      logger.debug('Real SQLite database configuration completed');
    } else {
      // 模拟模式下，配置是空操作
      logger.debug('Database configuration completed (mock mode)');
    }
  }

  /**
   * 获取数据库连接
   */
  public getConnection(): any {
    if (!this.db || !this.isInitialized) {
      throw new Error('Database not initialized');
    }
    return this.db;
  }

  /**
   * 执行 SQL 语句
   */
  public async run(sql: string, params: any[] = []): Promise<RunResult> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const startTime = Date.now();
    try {
      const result = await this.db.run(sql, params);
      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('SQL 执行失败', {
        sql: sql.trim(),
        params: params.length > 0 ? params : undefined,
        paramCount: params.length,
        executionTimeMs: executionTime,
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        operation: 'database_execute',
        module: 'database',
        dbPath: this.config.path,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * 查询单行数据
   */
  public async get<T = any>(sql: string, params: any[] = []): Promise<T | undefined> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const startTime = Date.now();
    try {
      const result = await this.db.get(sql, params);
      return result as T;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('SQL 单行查询失败', {
        sql: sql.trim(),
        params: params.length > 0 ? params : undefined,
        paramCount: params.length,
        executionTimeMs: executionTime,
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        operation: 'database_get',
        module: 'database',
        dbPath: this.config.path,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * 查询多行数据
   */
  public async all<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const startTime = Date.now();
    try {
      const result = await this.db.all(sql, params);
      return result as T[];
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('SQL 多行查询失败', {
        sql: sql.trim(),
        params: params.length > 0 ? params : undefined,
        paramCount: params.length,
        executionTimeMs: executionTime,
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        operation: 'database_all',
        module: 'database',
        dbPath: this.config.path,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * 执行事务
   */
  public async transaction<T>(callback: (db: any) => Promise<T>): Promise<T> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const transactionId = Math.random().toString(36).substr(2, 9);
    const startTime = Date.now();

    logger.debug('开始数据库事务', {
      transactionId,
      operation: 'transaction_begin',
      module: 'database',
      timestamp: new Date().toISOString(),
    });

    await this.run('BEGIN TRANSACTION');

    try {
      const result = await callback(this.db);
      await this.run('COMMIT');

      const executionTime = Date.now() - startTime;
      logger.debug('数据库事务提交成功', {
        transactionId,
        executionTimeMs: executionTime,
        operation: 'transaction_commit',
        module: 'database',
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;

      try {
        await this.run('ROLLBACK');
        logger.warn('数据库事务回滚成功', {
          transactionId,
          executionTimeMs: executionTime,
          error: {
            name: error instanceof Error ? error.name : 'Unknown',
            message: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
          },
          operation: 'transaction_rollback',
          module: 'database',
          timestamp: new Date().toISOString(),
        });
      } catch (rollbackError) {
        logger.error('数据库事务回滚失败', {
          transactionId,
          executionTimeMs: executionTime,
          originalError: {
            name: error instanceof Error ? error.name : 'Unknown',
            message: error instanceof Error ? error.message : String(error),
          },
          rollbackError: {
            name: rollbackError instanceof Error ? rollbackError.name : 'Unknown',
            message: rollbackError instanceof Error ? rollbackError.message : String(rollbackError),
            stack: rollbackError instanceof Error ? rollbackError.stack : undefined,
          },
          operation: 'transaction_rollback_failed',
          module: 'database',
          timestamp: new Date().toISOString(),
        });
      }

      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  public async close(): Promise<void> {
    if (this.db) {
      try {
        const dbEnabled = process.env.DB_ENABLED === 'true';

        if (dbEnabled && this.db.close) {
          // 真正的SQLite数据库
          await this.db.close();
          logger.info('Real SQLite database connection closed');
        } else {
          // 模拟模式下，简单地清理状态
          logger.info('Mock database connection closed');
        }

        this.db = null;
        this.isInitialized = false;
      } catch (error) {
        logger.error('Failed to close database', { error });
        throw error;
      }
    }
  }

  /**
   * 备份数据库
   */
  public async backup(backupPath?: string): Promise<string> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = backupPath || path.join(this.config.backupPath, `ota-backup-${timestamp}.db`);
    
    await fs.ensureDir(path.dirname(backupFile));
    await fs.copy(this.config.path, backupFile);
    
    logger.info('Database backup created', { backupFile });
    return backupFile;
  }

  /**
   * 检查数据库健康状态
   */
  public async healthCheck(): Promise<boolean> {
    const startTime = Date.now();
    try {
      await this.get('SELECT 1 as test');
      const executionTime = Date.now() - startTime;
      logger.debug('数据库健康检查成功', {
        executionTimeMs: executionTime,
        operation: 'health_check_success',
        module: 'database',
        dbPath: this.config.path,
        timestamp: new Date().toISOString(),
      });
      return true;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('数据库健康检查失败', {
        executionTimeMs: executionTime,
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          errno: (error as any)?.errno,
          stack: error instanceof Error ? error.stack : undefined,
        },
        operation: 'health_check_failed',
        module: 'database',
        dbPath: this.config.path,
        dbConnected: !!this.db,
        timestamp: new Date().toISOString(),
      });
      return false;
    }
  }
}

// 默认配置
export const getDefaultDatabaseConfig = (): DatabaseConfig => ({
  path: process.env.DB_PATH || './storage/database/ota.db',
  backupPath: process.env.DB_BACKUP_PATH || './storage/backup/database',
  maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
  busyTimeout: parseInt(process.env.DB_BUSY_TIMEOUT || '30000'),
  enableWAL: process.env.DB_ENABLE_WAL !== 'false',
});

// 导出单例实例
export const dbConnection = DatabaseConnection.getInstance(getDefaultDatabaseConfig());
