/**
 * API 端点常量
 */
export const API_ENDPOINTS = {
  // 版本相关端点
  VERSION_CHECK: '/api/v1/version/check',
  VERSION_AVAILABLE: '/api/v1/version/available',
  VERSION_CHANNELS: '/api/v1/version/channels',
  VERSION_LATEST: '/api/v1/version/latest/:channel',
  VERSION_STATS: '/api/v1/version/stats',
  VERSION_CACHE_CLEAR: '/api/v1/version/cache/clear',

  // 下载相关端点
  DOWNLOAD_FILE: '/api/v1/download/:filename',
  DOWNLOAD_LIST: '/api/v1/download/list/:channel?',
  DOWNLOAD_VERIFY: '/api/v1/download/verify/:filename',
  DOWNLOAD_CHECKSUM: '/api/v1/download/checksum/:filename',

  // 健康检查端点
  HEALTH: '/health',
  HEALTH_DETAILED: '/health/detailed',
  HEALTH_READY: '/health/ready',
  HEALTH_LIVE: '/health/live',

  // 根端点
  ROOT: '/',
} as const;

/**
 * API 基础 URL 配置
 */
export const API_BASE_URL = {
  PRODUCTION: 'https://ota.realitytap.com',
  STAGING: 'https://ota-staging.realitytap.com',
  DEVELOPMENT: 'http://localhost:3000',
} as const;

/**
 * API 版本
 */
export const API_VERSION = 'v1' as const;

/**
 * API 前缀
 */
export const API_PREFIX = `/api/${API_VERSION}` as const;