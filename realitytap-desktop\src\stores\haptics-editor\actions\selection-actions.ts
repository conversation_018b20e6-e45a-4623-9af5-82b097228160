import type { RenderableEvent, RenderableContinuousEvent, SelectedEvent } from "../types";
import { convertToSelectedEvent, findEventById } from "../utils/converters";
import { logger, LogModule } from "@/utils/logger/logger";
import { reprocessRenderableEvent } from "@/utils/haptic-event-processor";

/**
 * 选择相关操作
 */
export interface SelectionActions {
  selectEvent: (eventId: string | null) => void;
  selectCurvePoint: (index: number) => void;
  setAdjustingProperties: (isAdjusting: boolean) => void;
  clearSelection: () => void;
  updateSelectedEvent: (eventDetail: SelectedEvent) => void;
}

/**
 * 创建选择相关操作
 */
export function createSelectionActions(
  state: {
    selectedEventId: string | null;
    selectedCurvePointIndex: number;
    events: RenderableEvent[];
    isAdjustingProperties: boolean;
  },
  setState: (updates: Partial<typeof state>) => void
): SelectionActions {
  return {
    /**
     * 选择事件
     */
    selectEvent(eventId: string | null) {
      logger.debug(LogModule.WAVEFORM, `选择事件: ${eventId}`);

      setState({
        selectedEventId: eventId,
        selectedCurvePointIndex: eventId === null ? -1 : state.selectedCurvePointIndex,
      });
    },

    /**
     * 选择曲线点
     */
    selectCurvePoint(index: number) {
      logger.debug(LogModule.WAVEFORM, `选择曲线点: ${index}`);

      setState({
        selectedCurvePointIndex: index,
      });
    },

    /**
     * 设置属性调整状态
     */
    setAdjustingProperties(isAdjusting: boolean) {
      logger.debug(LogModule.WAVEFORM, `设置属性调整状态: ${isAdjusting}`);

      setState({
        isAdjustingProperties: isAdjusting,
      });
    },

    /**
     * 清除所有选择
     */
    clearSelection() {
      logger.debug(LogModule.WAVEFORM, "清除所有选择");

      setState({
        selectedEventId: null,
        selectedCurvePointIndex: -1,
      });
    },

    /**
     * 更新选中的事件
     */
    updateSelectedEvent(eventDetail: any) {
      if (!state.selectedEventId) {
        logger.warn(LogModule.WAVEFORM, "没有选中的事件可以更新");
        return;
      }

      const selectedEvent = findEventById(state.events, state.selectedEventId);
      if (!selectedEvent) {
        logger.warn(LogModule.WAVEFORM, `选中的事件不存在: ${state.selectedEventId}`);
        return;
      }

      logger.debug(LogModule.WAVEFORM, `更新选中事件: ${state.selectedEventId}`, eventDetail);

      // 检查是否是曲线点更新
      if (eventDetail.updateType === "UPDATE_CURVE_POINT" && selectedEvent.type === "continuous") {
        updateCurvePoint(selectedEvent as RenderableContinuousEvent, eventDetail);
        return;
      }

      // 标准事件更新
      updateStandardEvent(selectedEvent, eventDetail);
    },
  };

  /**
   * 更新曲线点
   */
  function updateCurvePoint(continuousEvent: RenderableContinuousEvent, updatePayload: any) {
    const curveIndex = updatePayload.curveIndex !== undefined ? updatePayload.curveIndex : state.selectedCurvePointIndex;

    logger.debug(LogModule.WAVEFORM, "开始更新曲线点", {
      eventId: continuousEvent.id,
      curveIndex,
      updatePayload,
      curvesLength: continuousEvent.curves.length,
      selectedCurvePointIndex: state.selectedCurvePointIndex
    });

    // 验证索引有效性
    if (curveIndex < 0 || curveIndex >= continuousEvent.curves.length) {
      logger.warn(LogModule.WAVEFORM, `无效的曲线点索引: ${curveIndex}`, {
        curvesLength: continuousEvent.curves.length,
        providedIndex: curveIndex
      });
      return;
    }

    // 创建更新后的事件
    const updatedEvent: RenderableContinuousEvent = {
      ...continuousEvent,
      curves: continuousEvent.curves.map((curve, index) => {
        if (index === curveIndex) {
          let updatedCurvePoint = { ...curve };

          // 更新时间偏移
          if (updatePayload.newTimeOffset !== undefined) {
            updatedCurvePoint.timeOffset = updatePayload.newTimeOffset;

            // 如果是最后一个曲线点，更新事件的持续时间
            if (curveIndex === continuousEvent.curves.length - 1) {
              updatedEvent.duration = updatePayload.newTimeOffset;
              updatedEvent.stopTime = updatedEvent.startTime + updatedEvent.duration;
            }
          }

          // 更新强度
          if (updatePayload.rawIntensity !== undefined) {
            const isFirstOrLastPoint = curveIndex === 0 || curveIndex === continuousEvent.curves.length - 1;

            logger.debug(LogModule.WAVEFORM, "更新曲线点强度", {
              curveIndex,
              isFirstOrLastPoint,
              rawIntensity: updatePayload.rawIntensity,
              eventIntensity: continuousEvent.eventIntensity,
              oldRawIntensity: curve.rawIntensity,
              oldDrawIntensity: curve.drawIntensity
            });

            if (isFirstOrLastPoint) {
              updatedCurvePoint.drawIntensity = 0;
              updatedCurvePoint.rawIntensity = 0;
              logger.debug(LogModule.WAVEFORM, "首尾点强度设为0");
            } else {
              const clampedRawIntensity = Number(Math.max(0, Math.min(1, updatePayload.rawIntensity)).toFixed(2));
              updatedCurvePoint.rawIntensity = clampedRawIntensity;
              updatedCurvePoint.drawIntensity = clampedRawIntensity * continuousEvent.eventIntensity;

              logger.debug(LogModule.WAVEFORM, "中间点强度已更新", {
                newRawIntensity: clampedRawIntensity,
                newDrawIntensity: updatedCurvePoint.drawIntensity
              });
            }
          }

          // 更新相对频率
          if (updatePayload.relativeCurveFrequency !== undefined) {
            logger.debug(LogModule.WAVEFORM, "更新曲线点频率", {
              curveIndex,
              relativeCurveFrequency: updatePayload.relativeCurveFrequency,
              eventFrequency: continuousEvent.eventFrequency,
              oldRelativeFrequency: curve.relativeCurveFrequency,
              oldCurveFrequency: curve.curveFrequency
            });

            updatedCurvePoint.relativeCurveFrequency = updatePayload.relativeCurveFrequency;
            updatedCurvePoint.curveFrequency = updatePayload.relativeCurveFrequency + continuousEvent.eventFrequency;

            logger.debug(LogModule.WAVEFORM, "曲线点频率已更新", {
              newRelativeFrequency: updatedCurvePoint.relativeCurveFrequency,
              newCurveFrequency: updatedCurvePoint.curveFrequency
            });
          }

          return updatedCurvePoint;
        }
        return curve;
      }),
    };

    logger.debug(LogModule.WAVEFORM, "曲线点更新完成，准备更新状态", {
      eventId: updatedEvent.id,
      updatedCurves: updatedEvent.curves.length,
      targetCurvePoint: updatedEvent.curves[curveIndex]
    });

    // 更新事件数组
    const updatedEvents = state.events.map(event =>
      event.id === state.selectedEventId ? updatedEvent : event
    );

    // 按开始时间排序
    updatedEvents.sort((a, b) => a.startTime - b.startTime);

    logger.debug(LogModule.WAVEFORM, "状态即将更新", {
      eventsCount: updatedEvents.length,
      selectedEventId: state.selectedEventId
    });

    setState({
      events: updatedEvents,
    });

    logger.debug(LogModule.WAVEFORM, "曲线点更新状态已提交");
  }

  /**
   * 更新标准事件
   */
  function updateStandardEvent(selectedEvent: RenderableEvent, eventDetail: SelectedEvent) {
    logger.debug(LogModule.WAVEFORM, "开始更新标准事件", {
      eventId: selectedEvent.id,
      eventType: selectedEvent.type,
      eventDetail
    });

    // 创建更新后的事件
    const updatedEvent = { ...selectedEvent };

    // 更新基本属性
    if (eventDetail.RelativeTime !== undefined) {
      updatedEvent.startTime = eventDetail.RelativeTime;
      logger.debug(LogModule.WAVEFORM, "更新事件开始时间", {
        eventId: selectedEvent.id,
        oldStartTime: selectedEvent.startTime,
        newStartTime: updatedEvent.startTime
      });
    }

    // 更新参数
    if (eventDetail.Parameters) {
      if (updatedEvent.type === "transient") {
        if (eventDetail.Parameters.Intensity !== undefined) {
          updatedEvent.intensity = eventDetail.Parameters.Intensity;
        }
        if (eventDetail.Parameters.Frequency !== undefined) {
          updatedEvent.frequency = eventDetail.Parameters.Frequency;
        }
      } else if (updatedEvent.type === "continuous") {
        if (eventDetail.Parameters.Intensity !== undefined) {
          updatedEvent.eventIntensity = eventDetail.Parameters.Intensity;

          // 重新计算所有曲线点的绘制强度
          updatedEvent.curves = updatedEvent.curves.map(curve => ({
            ...curve,
            drawIntensity: curve.rawIntensity * updatedEvent.eventIntensity
          }));

          logger.debug(LogModule.WAVEFORM, "连续事件全局强度已更新，重新计算曲线点绘制强度", {
            eventId: updatedEvent.id,
            newEventIntensity: updatedEvent.eventIntensity,
            curvesCount: updatedEvent.curves.length
          });
        }
        if (eventDetail.Parameters.Frequency !== undefined) {
          updatedEvent.eventFrequency = eventDetail.Parameters.Frequency;

          // 重新计算所有曲线点的绝对频率
          updatedEvent.curves = updatedEvent.curves.map(curve => ({
            ...curve,
            curveFrequency: curve.relativeCurveFrequency + updatedEvent.eventFrequency
          }));

          logger.debug(LogModule.WAVEFORM, "连续事件全局频率已更新，重新计算曲线点绝对频率", {
            eventId: updatedEvent.id,
            newEventFrequency: updatedEvent.eventFrequency,
            curvesCount: updatedEvent.curves.length
          });
        }
      }
    }

    // 更新持续时间（仅适用于连续事件）
    if (updatedEvent.type === "continuous" && eventDetail.Duration !== undefined) {
      // 使用reprocessRenderableEvent来正确更新Duration和曲线点
      const reprocessedEvent = reprocessRenderableEvent(updatedEvent, eventDetail) as RenderableContinuousEvent;

      // 更新事件的所有属性
      Object.assign(updatedEvent, reprocessedEvent);
    }

    // 重新计算瞬态事件的停止时间
    if (updatedEvent.type === "transient") {
      // 需要导入 getTransientDuration 函数
      import("@/types/haptic-file").then(({ getTransientDuration }) => {
        updatedEvent.width = getTransientDuration(updatedEvent.frequency);
        updatedEvent.peakTime = Math.floor(updatedEvent.startTime + updatedEvent.width / 2);
        updatedEvent.stopTime = Math.floor(updatedEvent.startTime + updatedEvent.width);
      });
    }

    // 更新事件数组
    const updatedEvents = state.events.map(event =>
      event.id === state.selectedEventId ? updatedEvent : event
    );

    // 按开始时间排序
    updatedEvents.sort((a, b) => a.startTime - b.startTime);

    setState({
      events: updatedEvents,
    });
  }
}

/**
 * 选择相关的 Getters
 */
export interface SelectionGetters {
  selectedEvent: () => any;
  selectedRenderableEvent: () => RenderableEvent | null;
  selectedCurvePoint: () => any;
  hasSelection: () => boolean;
  hasCurvePointSelection: () => boolean;
}

/**
 * 创建选择相关的 Getters
 */
export function createSelectionGetters(state: { selectedEventId: string | null; selectedCurvePointIndex: number; events: RenderableEvent[] }): SelectionGetters {
  return {
    /**
     * 获取选中的事件（转换为SelectedEvent格式）
     */
    selectedEvent() {
      const event = findEventById(state.events, state.selectedEventId || "");
      return convertToSelectedEvent(event);
    },

    /**
     * 获取选中的可渲染事件
     */
    selectedRenderableEvent() {
      return findEventById(state.events, state.selectedEventId || "");
    },

    /**
     * 获取选中的曲线点
     */
    selectedCurvePoint() {
      const event = findEventById(state.events, state.selectedEventId || "");
      if (
        !event ||
        event.type !== "continuous" ||
        state.selectedCurvePointIndex < 0 ||
        state.selectedCurvePointIndex >= (event as RenderableContinuousEvent).curves.length
      ) {
        return null;
      }

      return (event as RenderableContinuousEvent).curves[state.selectedCurvePointIndex];
    },

    /**
     * 检查是否有选中的事件
     */
    hasSelection() {
      return state.selectedEventId !== null;
    },

    /**
     * 检查是否有选中的曲线点
     */
    hasCurvePointSelection() {
      return state.selectedEventId !== null && state.selectedCurvePointIndex >= 0;
    },
  };
}
