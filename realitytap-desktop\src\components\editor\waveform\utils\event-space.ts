// 事件空间与碰撞检测相关工具函数
// 注意：依赖外部传入的事件数组、时间点、持续时间等参数

import { MIN_EVENT_INTERVAL_MS } from "../config/waveform-constants";

// 获取事件的有效持续时间（transient为width，continuous为duration）
export function getEventEffectiveDuration(event: any) {
  return event.type === "transient" ? event.width : event.duration;
}

// 检查某一时间点的可用空间（只计算右侧空间）
// events: 事件数组，timePoint: 当前时间点，totalDuration: 总时长
// minTransientSpace, minContinuousSpace: 各类型事件最小空间
export function checkAvailableSpace({
  events,
  timePoint,
  totalDuration,
  minTransientSpace = 8,
  minContinuousSpace = 25,
}: {
  events: any[];
  timePoint: number;
  totalDuration: number;
  minTransientSpace?: number;
  minContinuousSpace?: number;
}) {
  if (!events || events.length === 0) {
    return {
      transientAvailable: true,
      continuousAvailable: true,
      availableSpace: totalDuration - timePoint,
    };
  }
  // 按开始时间排序
  const sortedEvents = [...events].sort((a, b) => a.startTime - b.startTime);

  // 检查是否在任何事件内部
  for (const event of sortedEvents) {
    if (timePoint >= event.startTime && timePoint <= event.stopTime) {
      return {
        transientAvailable: false,
        continuousAvailable: false,
        availableSpace: 0,
      };
    }
  }

  // 找到左侧最近的事件（用于检查间隔）
  let prevEvent = null;
  for (const event of sortedEvents) {
    if (event.stopTime <= timePoint) {
      if (!prevEvent || event.stopTime > prevEvent.stopTime) {
        prevEvent = event;
      }
    }
  }

  // 找到右侧最近的事件
  let nextEvent = null;
  for (const event of sortedEvents) {
    if (event.startTime > timePoint) {
      if (!nextEvent || event.startTime < nextEvent.startTime) {
        nextEvent = event;
      }
    }
  }

  // 计算实际可用空间，考虑Event间隔要求
  let availableSpace = nextEvent ? nextEvent.startTime - timePoint : totalDuration - timePoint;

  // 如果存在左侧事件，需要确保与左侧事件保持最小间隔
  if (prevEvent) {
    const minStartTime = prevEvent.stopTime + MIN_EVENT_INTERVAL_MS;
    if (timePoint < minStartTime) {
      // 当前时间点不满足与左侧事件的最小间隔要求
      return {
        transientAvailable: false,
        continuousAvailable: false,
        availableSpace: 0,
      };
    }
  }

  // 如果存在右侧事件，需要预留与右侧事件的最小间隔
  if (nextEvent) {
    availableSpace = Math.max(0, availableSpace - MIN_EVENT_INTERVAL_MS);
  }

  return {
    transientAvailable: availableSpace >= minTransientSpace,
    continuousAvailable: availableSpace >= minContinuousSpace,
    availableSpace,
  };
}

// 校验事件 stopTime 是否在音频时长范围内
export function checkEventStopTimeWithinAudioDuration(stopTime: any, audioDuration: any) {
  if (audioDuration == null) return true;
  return stopTime <= audioDuration;
}

// 拖动事件时的边界检查和碰撞检测（返回合法的新开始时间）
// events: 事件数组，event: 当前事件，newStartTime: 拖动后新开始时间，totalDuration: 总时长
export function clampEventStartTime({
  events,
  event,
  newStartTime,
  totalDuration,
}: {
  events: any[];
  event: any;
  newStartTime: number;
  totalDuration: number;
}) {
  let clampedStartTime = Math.max(0, newStartTime);
  const eventDuration = getEventEffectiveDuration(event);
  if (clampedStartTime + eventDuration > totalDuration) {
    clampedStartTime = totalDuration - eventDuration;
  }
  const currentIndex = events.findIndex((e) => e.id === event.id);

  // 检查与前一个事件的碰撞（包含最小间隔要求）
  if (currentIndex > 0) {
    const prevEvent = events[currentIndex - 1];
    const minAllowedStartTime = prevEvent.stopTime + MIN_EVENT_INTERVAL_MS;
    if (clampedStartTime < minAllowedStartTime) {
      clampedStartTime = minAllowedStartTime;
    }
  }
  // 检查与后一个事件的碰撞（包含最小间隔要求）
  if (currentIndex < events.length - 1 && currentIndex !== -1) {
    const nextEvent = events[currentIndex + 1];
    const maxAllowedEndTime = nextEvent.startTime - MIN_EVENT_INTERVAL_MS;
    if (clampedStartTime + eventDuration > maxAllowedEndTime) {
      clampedStartTime = maxAllowedEndTime - eventDuration;
    }
  }
  return Math.floor(Math.max(0, clampedStartTime));
}
