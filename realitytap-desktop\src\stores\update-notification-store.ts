import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { logger, LogModule } from '@/utils/logger/logger'

/**
 * 更新信息接口
 */
export interface UpdateInfo {
  version: string
  date?: string
  body?: string
  file_size?: number
}

/**
 * 更新通知状态管理
 * 用于管理全局的更新通知状态，包括红点显示等
 */
export const useUpdateNotificationStore = defineStore('updateNotification', () => {
  // === 状态 ===
  const hasNewVersion = ref(false)
  const updateInfo = ref<UpdateInfo | null>(null)
  const lastCheckTime = ref<number>(0)
  const notificationDismissed = ref(false) // 用户是否已经查看过通知

  // === 计算属性 ===
  const shouldShowBadge = computed(() => {
    return hasNewVersion.value && !notificationDismissed.value
  })

  // === 本地存储键名 ===
  const STORAGE_KEYS = {
    HAS_NEW_VERSION: 'realitytap_has_new_version',
    UPDATE_INFO: 'realitytap_update_info',
    LAST_CHECK_TIME: 'realitytap_last_check_time',
    NOTIFICATION_DISMISSED: 'realitytap_notification_dismissed'
  }

  // === 方法 ===

  /**
   * 从本地存储加载状态
   */
  const loadFromStorage = () => {
    try {
      const storedHasNewVersion = localStorage.getItem(STORAGE_KEYS.HAS_NEW_VERSION)
      const storedUpdateInfo = localStorage.getItem(STORAGE_KEYS.UPDATE_INFO)
      const storedLastCheckTime = localStorage.getItem(STORAGE_KEYS.LAST_CHECK_TIME)
      const storedNotificationDismissed = localStorage.getItem(STORAGE_KEYS.NOTIFICATION_DISMISSED)

      if (storedHasNewVersion) {
        hasNewVersion.value = JSON.parse(storedHasNewVersion)
      }

      if (storedUpdateInfo) {
        updateInfo.value = JSON.parse(storedUpdateInfo)
      }

      if (storedLastCheckTime) {
        lastCheckTime.value = parseInt(storedLastCheckTime, 10)
      }

      if (storedNotificationDismissed) {
        notificationDismissed.value = JSON.parse(storedNotificationDismissed)
      }

      logger.debug(LogModule.GENERAL, '更新通知状态已从本地存储加载', {
        hasNewVersion: hasNewVersion.value,
        updateInfo: updateInfo.value,
        shouldShowBadge: shouldShowBadge.value
      })
    } catch (error) {
      logger.error(LogModule.GENERAL, '加载更新通知状态失败', error)
    }
  }

  /**
   * 保存状态到本地存储
   */
  const saveToStorage = () => {
    try {
      localStorage.setItem(STORAGE_KEYS.HAS_NEW_VERSION, JSON.stringify(hasNewVersion.value))
      localStorage.setItem(STORAGE_KEYS.UPDATE_INFO, JSON.stringify(updateInfo.value))
      localStorage.setItem(STORAGE_KEYS.LAST_CHECK_TIME, lastCheckTime.value.toString())
      localStorage.setItem(STORAGE_KEYS.NOTIFICATION_DISMISSED, JSON.stringify(notificationDismissed.value))
    } catch (error) {
      logger.error(LogModule.GENERAL, '保存更新通知状态失败', error)
    }
  }

  /**
   * 设置有新版本可用
   */
  const setUpdateAvailable = (info: UpdateInfo) => {
    hasNewVersion.value = true
    updateInfo.value = info
    lastCheckTime.value = Date.now()
    notificationDismissed.value = false // 重置通知状态
    saveToStorage()

    logger.info(LogModule.GENERAL, '检测到新版本，更新通知状态', {
      version: info.version,
      shouldShowBadge: shouldShowBadge.value
    })
  }

  /**
   * 清除更新通知（用户已查看或已更新）
   */
  const clearUpdateNotification = () => {
    hasNewVersion.value = false
    updateInfo.value = null
    notificationDismissed.value = false
    saveToStorage()

    logger.info(LogModule.GENERAL, '更新通知已清除')
  }

  /**
   * 标记通知已被查看（隐藏红点但保留更新信息）
   */
  const dismissNotification = () => {
    notificationDismissed.value = true
    saveToStorage()

    logger.debug(LogModule.GENERAL, '更新通知已标记为已查看')
  }

  /**
   * 更新最后检查时间
   */
  const updateLastCheckTime = () => {
    lastCheckTime.value = Date.now()
    saveToStorage()
  }

  /**
   * 检查是否需要显示红点
   * 考虑版本变化的情况
   */
  const checkShouldShowBadge = (currentVersion: string) => {
    if (!hasNewVersion.value || !updateInfo.value) {
      return false
    }

    // 如果当前版本已经是最新版本，清除通知
    if (currentVersion === updateInfo.value.version) {
      clearUpdateNotification()
      return false
    }

    return shouldShowBadge.value
  }

  // === 初始化 ===
  loadFromStorage()

  return {
    // 状态
    hasNewVersion,
    updateInfo,
    lastCheckTime,
    notificationDismissed,
    shouldShowBadge,

    // 方法
    setUpdateAvailable,
    clearUpdateNotification,
    dismissNotification,
    updateLastCheckTime,
    checkShouldShowBadge,
    loadFromStorage,
    saveToStorage
  }
})
