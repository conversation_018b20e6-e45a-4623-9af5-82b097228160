import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { invoke } from "@tauri-apps/api/core";
import type { Device, DeviceEvent, DeviceScanOptions, DeviceConnectionConfig, DeviceOperationResult, DeviceStatistics, DeviceManagerConfig } from "@/types/device-types";
import { DeviceType, DeviceStatus, DeviceEventType } from "@/types/device-types";
import { parseErrorMessage } from "@/utils/commonUtils";
import { DEFAULT_DEVICE_CONFIG } from "@/utils/device/deviceConstants";
import { logger, LogModule } from "@/utils/logger/logger";

// 默认设备管理器配置
const DEFAULT_CONFIG: DeviceManagerConfig = {
  autoScanInterval: 30000, // 30秒
  maxDevices: 50,
  enableAutoReconnect: true,
  connectionTimeout: 10000, // 10秒
  scanTimeout: 15000, // 15秒
};

export const useDeviceManagerStore = defineStore("deviceManager", () => {
  // === 状态 ===
  const devices = ref<Map<string, Device>>(new Map());
  const defaultDeviceId = ref<string | null>(null);
  const isScanning = ref(false);
  const isInitialized = ref(false);
  const lastScanTime = ref<string | null>(null);
  const config = ref<DeviceManagerConfig>({ ...DEFAULT_CONFIG });
  const events = ref<DeviceEvent[]>([]);
  const maxEventHistory = ref(100);

  // 自动扫描定时器
  let autoScanTimer: number | null = null;

  // === 计算属性 ===
  const deviceList = computed(() => Array.from(devices.value.values()));

  const connectedDevices = computed(() => deviceList.value.filter((device) => device.status === DeviceStatus.CONNECTED));

  const disconnectedDevices = computed(() => deviceList.value.filter((device) => device.status === DeviceStatus.DISCONNECTED));

  const defaultDevice = computed(() => (defaultDeviceId.value ? devices.value.get(defaultDeviceId.value) || null : null));

  const devicesByType = computed(() => {
    const result: Record<DeviceType, Device[]> = {
      [DeviceType.USB]: [],
      [DeviceType.WIFI]: [],
      [DeviceType.BLUETOOTH]: [],
    };

    deviceList.value.forEach((device) => {
      result[device.type].push(device);
    });

    return result;
  });

  const statistics = computed((): DeviceStatistics => {
    const total = deviceList.value.length;
    const connected = connectedDevices.value.length;
    const disconnected = disconnectedDevices.value.length;
    const error = deviceList.value.filter((d) => d.status === DeviceStatus.ERROR).length;

    const byType: Record<DeviceType, number> = {
      [DeviceType.USB]: devicesByType.value[DeviceType.USB].length,
      [DeviceType.WIFI]: devicesByType.value[DeviceType.WIFI].length,
      [DeviceType.BLUETOOTH]: devicesByType.value[DeviceType.BLUETOOTH].length,
    };

    const lastConnection = deviceList.value.filter((d) => d.lastConnected).sort((a, b) => new Date(b.lastConnected!).getTime() - new Date(a.lastConnected!).getTime())[0];

    return {
      totalDevices: total,
      connectedDevices: connected,
      disconnectedDevices: disconnected,
      errorDevices: error,
      devicesByType: byType,
      lastScanTime: lastScanTime.value,
      lastConnectionTime: lastConnection?.lastConnected || null,
    };
  });

  // === 内部工具函数 ===
  const addEvent = (event: Omit<DeviceEvent, "timestamp">) => {
    const fullEvent: DeviceEvent = {
      ...event,
      timestamp: new Date().toISOString(),
    };

    events.value.unshift(fullEvent);

    // 限制事件历史数量
    if (events.value.length > maxEventHistory.value) {
      events.value = events.value.slice(0, maxEventHistory.value);
    }
  };

  const updateDeviceStatus = (deviceId: string, status: DeviceStatus, error?: string) => {
    const device = devices.value.get(deviceId);
    if (!device) return;

    const now = new Date().toISOString();
    const updatedDevice: Device = {
      ...device,
      status,
      updatedAt: now,
      metadata: {
        ...device.metadata,
        lastError: error || device.metadata.lastError,
      },
    };

    if (status === DeviceStatus.CONNECTED) {
      updatedDevice.lastConnected = now;
    } else if (status === DeviceStatus.DISCONNECTED) {
      updatedDevice.lastDisconnected = now;
    }

    devices.value.set(deviceId, updatedDevice);

    addEvent({
      type: DeviceEventType.DEVICE_STATUS_CHANGED,
      deviceId,
      device: updatedDevice,
    });
  };

  // === 设备管理操作 ===
  const addDevice = (device: Device): void => {
    devices.value.set(device.deviceId, device);
    addEvent({
      type: DeviceEventType.DEVICE_DISCOVERED,
      deviceId: device.deviceId,
      device,
    });
  };

  const removeDevice = (deviceId: string): boolean => {
    const device = devices.value.get(deviceId);
    if (!device) return false;

    // 如果是默认设备，清除默认设置
    if (defaultDeviceId.value === deviceId) {
      defaultDeviceId.value = null;
    }

    devices.value.delete(deviceId);
    return true;
  };

  const updateDevice = (deviceId: string, updates: Partial<Device>): boolean => {
    const device = devices.value.get(deviceId);
    if (!device) return false;

    const updatedDevice: Device = {
      ...device,
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    devices.value.set(deviceId, updatedDevice);
    return true;
  };

  const setDefaultDevice = async (deviceId: string | null): Promise<DeviceOperationResult> => {
    try {
      if (deviceId && !devices.value.has(deviceId)) {
        throw new Error(`设备 ${deviceId} 不存在`);
      }

      // 调用后端保存默认设备设置
      await invoke("set_default_device", { deviceId });

      defaultDeviceId.value = deviceId;

      return {
        success: true,
        deviceId: deviceId || "",
        message: deviceId ? "默认设备设置成功" : "已清除默认设备",
      };
    } catch (error: any) {
      const errorMessage = parseErrorMessage(error);
      return {
        success: false,
        deviceId: deviceId || "",
        error: errorMessage,
      };
    }
  };

  // === 设备扫描 ===
  const scanDevices = async (options: DeviceScanOptions = {}): Promise<DeviceOperationResult> => {
    if (isScanning.value) {
      return {
        success: false,
        deviceId: "",
        error: "设备扫描正在进行中",
      };
    }

    isScanning.value = true;
    addEvent({ type: DeviceEventType.SCAN_STARTED });

    try {
      const scanResult = await invoke<Device[]>("scan_devices", { options });

      // 更新设备列表
      scanResult.forEach((device) => {
        devices.value.set(device.deviceId, device);
      });

      lastScanTime.value = new Date().toISOString();

      addEvent({
        type: DeviceEventType.SCAN_COMPLETED,
        data: { foundDevices: scanResult.length },
      });

      return {
        success: true,
        deviceId: "",
        message: `扫描完成，发现 ${scanResult.length} 个设备`,
        data: scanResult,
      };
    } catch (error: any) {
      const errorMessage = parseErrorMessage(error);
      addEvent({
        type: DeviceEventType.SCAN_ERROR,
        error: errorMessage,
      });

      return {
        success: false,
        deviceId: "",
        error: errorMessage,
      };
    } finally {
      isScanning.value = false;
    }
  };

  // === 设备连接 ===
  const connectDevice = async (deviceId: string, config?: DeviceConnectionConfig): Promise<DeviceOperationResult> => {
    const device = devices.value.get(deviceId);
    if (!device) {
      return {
        success: false,
        deviceId,
        error: "设备不存在",
      };
    }

    if (device.status === DeviceStatus.CONNECTED) {
      return {
        success: true,
        deviceId,
        message: "设备已连接",
      };
    }

    updateDeviceStatus(deviceId, DeviceStatus.CONNECTING);

    try {
      await invoke("connect_device", { deviceId, config });
      updateDeviceStatus(deviceId, DeviceStatus.CONNECTED);

      addEvent({
        type: DeviceEventType.DEVICE_CONNECTED,
        deviceId,
        device: devices.value.get(deviceId),
      });

      return {
        success: true,
        deviceId,
        message: "设备连接成功",
      };
    } catch (error: any) {
      const errorMessage = parseErrorMessage(error);
      updateDeviceStatus(deviceId, DeviceStatus.ERROR, errorMessage);

      addEvent({
        type: DeviceEventType.DEVICE_ERROR,
        deviceId,
        error: errorMessage,
      });

      return {
        success: false,
        deviceId,
        error: errorMessage,
      };
    }
  };

  const disconnectDevice = async (deviceId: string): Promise<DeviceOperationResult> => {
    const device = devices.value.get(deviceId);
    if (!device) {
      return {
        success: false,
        deviceId,
        error: "设备不存在",
      };
    }

    if (device.status === DeviceStatus.DISCONNECTED) {
      return {
        success: true,
        deviceId,
        message: "设备已断开",
      };
    }

    updateDeviceStatus(deviceId, DeviceStatus.DISCONNECTING);

    try {
      await invoke("disconnect_device", { deviceId });
      updateDeviceStatus(deviceId, DeviceStatus.DISCONNECTED);

      addEvent({
        type: DeviceEventType.DEVICE_DISCONNECTED,
        deviceId,
        device: devices.value.get(deviceId),
      });

      return {
        success: true,
        deviceId,
        message: "设备断开成功",
      };
    } catch (error: any) {
      const errorMessage = parseErrorMessage(error);
      updateDeviceStatus(deviceId, DeviceStatus.ERROR, errorMessage);

      return {
        success: false,
        deviceId,
        error: errorMessage,
      };
    }
  };

  // === 初始化和清理 ===
  const initialize = async (): Promise<void> => {
    if (isInitialized.value) return;

    try {
      // 加载保存的设备列表和默认设备
      const savedData = await invoke<{
        devices: Device[];
        defaultDeviceId: string | null;
      }>("load_device_manager_data");

      // 恢复设备列表
      savedData.devices.forEach((device) => {
        devices.value.set(device.deviceId, device);
      });

      defaultDeviceId.value = savedData.defaultDeviceId;

      // 启动自动扫描（仅在启用时）
      if (DEFAULT_DEVICE_CONFIG.ENABLE_AUTO_SCAN && config.value.autoScanInterval && config.value.autoScanInterval > 0) {
        startAutoScan();
      }

      isInitialized.value = true;
    } catch (error) {
      logger.warn(LogModule.DEVICE, "设备管理器初始化失败", error);
      isInitialized.value = true; // 即使失败也标记为已初始化
    }
  };

  const startAutoScan = (): void => {
    if (autoScanTimer) return;

    // 检查是否启用自动扫描
    if (!DEFAULT_DEVICE_CONFIG.ENABLE_AUTO_SCAN) {
      logger.info(LogModule.DEVICE, "自动设备扫描已禁用（功能未完全实现）");
      return;
    }

    autoScanTimer = setInterval(() => {
      if (!isScanning.value) {
        scanDevices({ includeDisconnected: false });
      }
    }, config.value.autoScanInterval);
  };

  const stopAutoScan = (): void => {
    if (autoScanTimer) {
      clearInterval(autoScanTimer);
      autoScanTimer = null;
    }
  };

  const cleanup = (): void => {
    stopAutoScan();
    devices.value.clear();
    events.value = [];
    isInitialized.value = false;
  };

  return {
    // 只读状态
    devices: readonly(devices),
    deviceList,
    connectedDevices,
    disconnectedDevices,
    defaultDevice,
    defaultDeviceId: readonly(defaultDeviceId),
    devicesByType,
    statistics,
    isScanning: readonly(isScanning),
    isInitialized: readonly(isInitialized),
    lastScanTime: readonly(lastScanTime),
    events: readonly(events),
    config: readonly(config),

    // 操作方法
    addDevice,
    removeDevice,
    updateDevice,
    setDefaultDevice,
    scanDevices,
    connectDevice,
    disconnectDevice,
    initialize,
    startAutoScan,
    stopAutoScan,
    cleanup,
  };
});
