import { config } from '@/config/server.config';
import {
  BatchUploadRequest,
  BatchUploadResponse,
  BatchUploadFileStatus,
  BatchUploadProgress,
  BatchUploadFileInfo,
  FileUploadRequest,
  UploadStatus,
} from '@/types/auth.types';
import { FileUtil } from '@/utils/file.util';
import { logger } from '@/utils/logger.util';
import crypto from 'crypto';
import fs from 'fs-extra';
import path from 'path';

/**
 * 批量上传服务
 * 负责管理msi和sig文件的批量上传
 */
export class BatchUploadService {
  private sessions: Map<string, BatchUploadSession> = new Map();
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.startCleanupTimer();
  }

  /**
   * 初始化批量上传会话
   */
  async initializeBatchUpload(request: BatchUploadRequest): Promise<BatchUploadResponse> {
    try {
      logger.info('Initializing batch upload session', {
        totalFiles: request.files.length,
        fileTypes: request.files.map(f => f.fileType),
        filenames: request.files.map(f => f.filename),
        hasMetadata: !!request.metadata,
        metadataKeys: request.metadata ? Object.keys(request.metadata) : [],
        hasSignatureInMetadata: !!(request.metadata as any)?.signature,
        signatureLength: (request.metadata as any)?.signature ? (request.metadata as any).signature.length : 0,
        signaturePreview: (request.metadata as any)?.signature ? (request.metadata as any).signature.substring(0, 50) + '...' : null,
      });

      // 验证文件配对（必须有一个installer文件，可选一个signature文件）
      this.validateFileSet(request.files);

      // 检查文件是否已存在
      await this.checkFileExistence(request);

      // 生成会话ID
      const sessionId = this.generateSessionId();

      // 创建临时目录
      const tempDir = path.join(config.storage.tempPath, sessionId);
      await FileUtil.ensureDir(tempDir);

      // 初始化文件状态
      const fileStatuses: BatchUploadFileStatus[] = request.files.map(file => ({
        filename: file.filename,
        fileType: file.fileType,
        status: UploadStatus.INITIALIZING,
        uploadedBytes: 0,
        totalBytes: file.fileSize,
      }));

      // 计算总大小
      const totalSize = request.files.reduce((sum, file) => sum + file.fileSize, 0);

      // 创建批量上传会话
      const session: BatchUploadSession = {
        sessionId,
        files: request.files,
        metadata: request.metadata,
        fileStatuses,
        tempDir,
        createdAt: new Date(),
        lastActivity: new Date(),
        status: UploadStatus.INITIALIZING,
      };

      this.sessions.set(sessionId, session);

      logger.info('Batch upload session initialized', {
        sessionId,
        totalFiles: request.files.length,
        totalSize,
      });

      return {
        sessionId,
        files: fileStatuses,
        totalFiles: request.files.length,
        totalSize,
      };
    } catch (error) {
      logger.error('初始化批量上传会话失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          stack: error instanceof Error ? error.stack : undefined,
          type: typeof error,
        },
        batchRequest: {
          totalFiles: request.files.length,
          totalSize: request.files.reduce((sum, file) => sum + file.fileSize, 0),
          fileTypes: [...new Set(request.files.map(f => f.fileType))],
          filenames: request.files.map(f => f.filename),
          metadata: request.metadata ? {
            version: request.metadata.version,
            platform: request.metadata.platform,
            architecture: request.metadata.architecture,
            channel: request.metadata.channel,
            releaseNotes: request.metadata.releaseNotes?.substring(0, 100) + (request.metadata.releaseNotes && request.metadata.releaseNotes.length > 100 ? '...' : ''),
          } : undefined,
        },
        sessionInfo: {
          sessionId: 'unknown',
          tempDir: 'unknown',
        },
        systemInfo: {
          tempPath: config.storage.tempPath,
          releasesPath: config.storage.releasesPath,
          timestamp: new Date().toISOString(),
        },
        timing: {
          timestamp: new Date().toISOString(),
        },
        operation: 'initialize_batch_upload_failed',
        module: 'version_management',
      });
      throw new Error(`Failed to initialize batch upload session: ${error}`);
    }
  }

  /**
   * 上传单个文件
   */
  async uploadFile(sessionId: string, filename: string, fileBuffer: Buffer): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error('Batch upload session not found');
      }

      // 查找文件信息
      const fileInfo = session.files.find(f => f.filename === filename);
      if (!fileInfo) {
        throw new Error('File not found in session');
      }

      // 验证文件大小
      if (fileBuffer.length !== fileInfo.fileSize) {
        throw new Error('File size mismatch');
      }

      // 优先检查是否有metadata中的fileHash（来自.hash文件）
      logger.info('File hash verification decision', {
        sessionId,
        filename,
        fileType: fileInfo.fileType,
        hasMetadataFileHash: !!session.metadata.fileHash,
        metadataFileHash: session.metadata.fileHash,
      });

      if (fileInfo.fileType === 'installer' && session.metadata.fileHash) {
        // 如果是安装包文件且metadata中有fileHash，直接使用metadata中的哈希值进行验证
        const expectedHash = session.metadata.fileHash.toLowerCase();
        const actualHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

        logger.info('File hash verification using metadata hash (from .hash file)', {
          sessionId,
          filename,
          expectedHashFromMetadata: expectedHash,
          actualHash,
          fileSize: fileBuffer.length,
        });

        if (actualHash !== expectedHash) {
          logger.warn('File hash verification failed using metadata hash', {
            sessionId,
            filename,
            expectedHash,
            actualHash,
            fileSize: fileBuffer.length,
          });
          throw new Error('File hash mismatch with metadata hash');
        }

        logger.info('File verified using metadata hash (from .hash file)', {
          sessionId,
          filename,
          fileSize: fileBuffer.length,
        });
      } else {
        // 检查是否有对应的哈希文件
        const hashFileName = `${filename}.hash`;
        const hashFileInfo = session.files.find((f: BatchUploadFileInfo) => f.filename === hashFileName);

        if (hashFileInfo) {
          // 如果有哈希文件，使用哈希文件中的哈希值进行验证
          const hashFilePath = path.join(session.tempDir, hashFileName);

          if (await FileUtil.exists(hashFilePath)) {
            try {
              const expectedHashFromFile = await fs.readFile(hashFilePath, 'utf-8');
              const expectedHash = expectedHashFromFile.trim().toLowerCase();
              const actualHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

              logger.info('File hash verification using hash file', {
                sessionId,
                filename,
                hashFileName,
                expectedHashFromFile: expectedHash,
                actualHash,
                fileSize: fileBuffer.length,
              });

              if (actualHash !== expectedHash) {
                logger.warn('File hash verification failed using hash file', {
                  sessionId,
                  filename,
                  hashFileName,
                  expectedHash,
                  actualHash,
                  fileSize: fileBuffer.length,
                });
                throw new Error('File hash mismatch with hash file');
              }

              logger.info('File verified using hash file', {
                sessionId,
                filename,
                hashFileName,
                fileSize: fileBuffer.length,
              });
            } catch (error) {
              logger.error('Failed to read hash file for verification', {
                sessionId,
                filename,
                hashFileName,
                error: error instanceof Error ? error.message : String(error),
              });
              throw new Error(`Failed to verify file using hash file: ${error}`);
            }
          } else {
            logger.warn('Hash file not found for verification', {
              sessionId,
              filename,
              hashFileName,
            });
            // 如果哈希文件不存在，回退到原来的验证方式
            await this.verifyFileHashLegacy(sessionId, filename, fileBuffer, fileInfo);
          }
        } else {
          // 没有哈希文件，使用原来的验证方式
          await this.verifyFileHashLegacy(sessionId, filename, fileBuffer, fileInfo);
        }
      }

      // 保存文件到临时目录
      const tempFilePath = path.join(session.tempDir, filename);
      await fs.writeFile(tempFilePath, fileBuffer);

      // 更新文件状态
      const fileStatus = session.fileStatuses.find(f => f.filename === filename);
      if (fileStatus) {
        fileStatus.status = UploadStatus.COMPLETED;
        fileStatus.uploadedBytes = fileInfo.fileSize;
      }

      session.lastActivity = new Date();

      logger.info('File uploaded successfully', {
        sessionId,
        filename,
        size: fileBuffer.length,
      });
    } catch (error) {
      const session = this.sessions.get(sessionId);
      const fileInfo = session?.files.find(f => f.filename === filename);

      logger.error('批量上传文件失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          stack: error instanceof Error ? error.stack : undefined,
          type: typeof error,
        },
        uploadInfo: {
          sessionId,
          filename,
          fileSize: fileBuffer.length,
          expectedSize: fileInfo?.fileSize,
          fileType: fileInfo?.fileType,
          expectedHash: fileInfo?.fileHash,
          actualHash: fileBuffer ? crypto.createHash('sha256').update(fileBuffer).digest('hex') : 'N/A',
        },
        sessionInfo: {
          totalFiles: session?.files.length,
          completedFiles: session?.fileStatuses.filter(f => f.status === UploadStatus.COMPLETED).length,
          status: session?.status,
          tempDir: session?.tempDir,
        },
        validation: {
          sizeMatch: fileBuffer.length === fileInfo?.fileSize,
          hashMatch: fileBuffer && fileInfo ? crypto.createHash('sha256').update(fileBuffer).digest('hex') === fileInfo.fileHash : false,
        },
        timing: {
          timestamp: new Date().toISOString(),
        },
        operation: 'batch_upload_file_failed',
        module: 'version_management',
      });
      throw error;
    }
  }

  /**
   * 完成批量上传
   */
  async completeBatchUpload(sessionId: string): Promise<string[]> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error('Batch upload session not found');
      }

      // 检查所有文件是否已上传
      const allCompleted = session.fileStatuses.every(f => f.status === UploadStatus.COMPLETED);
      if (!allCompleted) {
        throw new Error('Not all files have been uploaded');
      }

      session.status = UploadStatus.MERGING;

      // 移动文件到最终位置
      const finalPaths = await this.moveFilesToFinalLocation(session);

      // 处理文件关联（将签名信息添加到版本元数据）
      await this.processFileAssociation(session, finalPaths);

      // 保存版本元数据
      await this.saveVersionMetadata(session, finalPaths);

      // 更新会话状态
      session.status = UploadStatus.COMPLETED;
      session.lastActivity = new Date();

      logger.info('Batch upload completed successfully', {
        sessionId,
        files: finalPaths,
      });

      // 清理临时文件
      await this.cleanupSession(sessionId);
      this.sessions.delete(sessionId);

      return finalPaths;
    } catch (error) {
      const session = this.sessions.get(sessionId);

      logger.error('完成批量上传失败', {
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          stack: error instanceof Error ? error.stack : undefined,
          type: typeof error,
        },
        batchUploadInfo: {
          sessionId,
          totalFiles: session?.files.length,
          completedFiles: session?.fileStatuses.filter(f => f.status === UploadStatus.COMPLETED).length,
          status: session?.status,
          tempDir: session?.tempDir,
          createdAt: session?.createdAt,
          lastActivity: session?.lastActivity,
        },
        uploadProgress: {
          allCompleted: session ? session.fileStatuses.every(f => f.status === UploadStatus.COMPLETED) : false,
          fileStatuses: session?.fileStatuses.map(f => ({
            filename: f.filename,
            status: f.status,
            uploadedBytes: f.uploadedBytes,
          })),
        },
        systemInfo: {
          releasesPath: config.storage.releasesPath,
          tempPath: config.storage.tempPath,
          timestamp: new Date().toISOString(),
        },
        timing: {
          timestamp: new Date().toISOString(),
        },
        operation: 'complete_batch_upload_failed',
        module: 'version_management',
      });
      throw error;
    }
  }

  /**
   * 获取批量上传进度
   */
  getBatchUploadProgress(sessionId: string): BatchUploadProgress | null {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return null;
    }

    const totalBytes = session.files.reduce((sum, file) => sum + file.fileSize, 0);
    const uploadedBytes = session.fileStatuses.reduce((sum, status) => sum + status.uploadedBytes, 0);
    const completedFiles = session.fileStatuses.filter(f => f.status === UploadStatus.COMPLETED).length;

    return {
      sessionId,
      files: [...session.fileStatuses],
      overallProgress: {
        uploadedBytes,
        totalBytes,
        percentage: totalBytes > 0 ? Math.round((uploadedBytes / totalBytes) * 100) : 0,
        completedFiles,
        totalFiles: session.files.length,
      },
      status: session.status,
    };
  }

  /**
   * 检查文件是否已存在
   */
  private async checkFileExistence(request: BatchUploadRequest): Promise<void> {
    const channel = request.metadata.channel || 'stable';
    const channelDir = path.join(config.storage.releasesPath, channel);

    const existingFiles: string[] = [];

    for (const file of request.files) {
      const finalPath = path.join(channelDir, file.filename);
      if (await fs.pathExists(finalPath)) {
        existingFiles.push(file.filename);
      }
    }

    if (existingFiles.length > 0) {
      const fileList = existingFiles.join(', ');
      logger.warn('Files already exist in target directory', {
        channel,
        existingFiles,
        channelDir
      });

      throw new Error(`以下文件已存在于 ${channel} 频道中：${fileList}。上传将覆盖现有文件并创建备份。如果确认要继续，请重新上传。`);
    }
  }

  /**
   * 验证文件集合
   */
  private validateFileSet(files: BatchUploadFileInfo[]): void {
    const installerFiles = files.filter(f => f.fileType === 'installer');
    const signatureFiles = files.filter(f => f.fileType === 'signature');
    const hashFiles = files.filter(f => f.filename.endsWith('.hash'));

    if (installerFiles.length !== 1) {
      throw new Error('Must have exactly one installer file');
    }

    if (signatureFiles.length > 1) {
      throw new Error('Cannot have more than one signature file');
    }

    if (hashFiles.length > 1) {
      throw new Error('Cannot have more than one hash file');
    }

    // 验证签名文件名与安装包文件名的对应关系
    if (signatureFiles.length === 1) {
      const installerFile = installerFiles[0];
      const signatureFile = signatureFiles[0];

      if (installerFile && signatureFile && !signatureFile.filename.startsWith(installerFile.filename)) {
        throw new Error('Signature file name must match installer file name');
      }
    }

    // 验证哈希文件名与安装包文件名的对应关系
    if (hashFiles.length === 1) {
      const installerFile = installerFiles[0];
      const hashFile = hashFiles[0];

      if (installerFile && hashFile && !hashFile.filename.startsWith(installerFile.filename)) {
        throw new Error('Hash file name must match installer file name');
      }
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 移动文件到最终位置
   */
  private async moveFilesToFinalLocation(session: BatchUploadSession): Promise<string[]> {
    const channel = session.metadata.channel || 'stable';
    const channelDir = path.join(config.storage.releasesPath, channel);
    await FileUtil.ensureDir(channelDir);

    const finalPaths: string[] = [];

    for (const file of session.files) {
      const tempPath = path.join(session.tempDir, file.filename);
      const finalPath = path.join(channelDir, file.filename);

      // 检查目标文件是否已存在
      if (await fs.pathExists(finalPath)) {
        logger.warn('File already exists, will be overwritten', {
          sessionId: session.sessionId,
          filename: file.filename,
          finalPath,
          channel
        });

        // 创建备份文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = finalPath + `.backup-${timestamp}`;

        try {
          // 备份现有文件
          await fs.move(finalPath, backupPath);
          logger.info('Existing file backed up', {
            sessionId: session.sessionId,
            filename: file.filename,
            backupPath
          });
        } catch (backupError) {
          logger.error('Failed to backup existing file', {
            sessionId: session.sessionId,
            filename: file.filename,
            finalPath,
            backupError
          });
          // 如果备份失败，抛出友好的错误消息
          throw new Error(`文件 "${file.filename}" 已存在且无法备份。请检查文件权限或手动删除现有文件后重试。`);
        }
      }

      try {
        await fs.move(tempPath, finalPath);
        finalPaths.push(finalPath);

        logger.info('File moved to final location', {
          sessionId: session.sessionId,
          filename: file.filename,
          finalPath
        });
      } catch (moveError) {
        logger.error('Failed to move file to final location', {
          sessionId: session.sessionId,
          filename: file.filename,
          tempPath,
          finalPath,
          moveError
        });
        throw new Error(`无法移动文件 "${file.filename}" 到最终位置。请检查磁盘空间和文件权限。`);
      }
    }

    return finalPaths;
  }

  /**
   * 处理文件关联
   */
  private async processFileAssociation(session: BatchUploadSession, finalPaths: string[]): Promise<void> {
    const installerFile = session.files.find(f => f.fileType === 'installer');
    const signatureFile = session.files.find(f => f.fileType === 'signature');

    // 优先使用metadata中的签名内容（来自前端读取的.sig文件）
    if (session.metadata.signature) {
      logger.info('Using signature from metadata (from .sig file)', {
        sessionId: session.sessionId,
        installer: installerFile?.filename,
        hasSignature: true,
        signatureLength: session.metadata.signature.length,
      });
      // 签名内容已经在metadata中，无需额外处理
      return;
    }

    // 如果metadata中没有签名内容，尝试从上传的签名文件中读取（向后兼容）
    if (installerFile && signatureFile) {
      const sigPath = finalPaths.find(p => path.basename(p) === signatureFile.filename);

      if (sigPath) {
        // 检查文件是否存在
        const fileExists = await fs.pathExists(sigPath);

        if (fileExists) {
          // 读取签名文件内容
          const signature = await fs.readFile(sigPath, 'utf8');

          // 将签名信息存储到会话元数据中，后续保存到版本配置
          session.metadata.signature = signature.trim();

          logger.info('File association processed successfully (from uploaded file)', {
            sessionId: session.sessionId,
            installer: installerFile.filename,
            signature: signatureFile.filename,
          });
        } else {
          logger.error('Signature file does not exist', {
            sessionId: session.sessionId,
            sigPath,
          });
        }
      } else {
        logger.error('Signature file path not found', {
          sessionId: session.sessionId,
          signatureFilename: signatureFile.filename,
        });
      }
    }
  }

  /**
   * 保存版本元数据
   */
  private async saveVersionMetadata(session: BatchUploadSession, finalPaths: string[]): Promise<void> {
    try {
      // 获取安装包文件信息
      const installerFile = session.files.find(f => f.fileType === 'installer');
      if (!installerFile) {
        throw new Error('Installer file not found in session');
      }

      // 获取安装包文件路径和统计信息
      const installerPath = finalPaths.find(p => path.basename(p) === installerFile.filename);
      if (!installerPath) {
        throw new Error('Installer file path not found');
      }

      const stats = await fs.stat(installerPath);

      // 构建版本元数据
      // 优先使用从.hash文件读取的hash值，如果没有则使用计算的文件hash
      const checksum = session.metadata.fileHash || installerFile.fileHash;

      const versionMetadata = {
        filename: installerFile.filename,
        version: session.metadata.version,
        platform: session.metadata.platform,
        architecture: session.metadata.architecture,
        channel: session.metadata.channel || 'stable',
        fileSize: stats.size,
        checksum: checksum,
        uploadTime: new Date().toISOString(),
        releaseNotes: session.metadata.releaseNotes || `Release ${session.metadata.version}`,
        isForced: session.metadata.isForced || false,
      };

      logger.info('Version metadata checksum selection', {
        sessionId: session.sessionId,
        filename: installerFile.filename,
        hashFromFile: session.metadata.fileHash,
        hashFromCalculation: installerFile.fileHash,
        selectedChecksum: checksum,
        hashSource: session.metadata.fileHash ? 'hash-file' : 'calculation',
      });

      // 获取签名信息（如果存在）
      const signature = session.metadata.signature;

      logger.info('Processing version record with signature', {
        sessionId: session.sessionId,
        filename: installerFile.filename,
        version: session.metadata.version,
        hasSignature: !!signature,
        signatureLength: signature ? signature.length : 0,
        signaturePreview: signature ? signature.substring(0, 50) + '...' : null,
      });

      // 将版本信息写入数据库（包含签名信息）
      await this.processVersionRecord(versionMetadata, signature);

      // 注意：不再调用版本同步服务，因为现在使用数据库存储
      // 版本同步服务是为文件系统设计的，会覆盖数据库中的签名信息

      logger.info('Version metadata saved successfully', {
        sessionId: session.sessionId,
        filename: installerFile.filename,
        version: session.metadata.version,
        hasSignature: !!signature,
      });
    } catch (error) {
      logger.error('Failed to save version metadata', {
        sessionId: session.sessionId,
        error: error,
      });
      throw error;
    }
  }



  /**
   * 处理版本记录到数据库
   */
  private async processVersionRecord(versionMetadata: any, signature?: string): Promise<void> {
    try {
      const { getVersionService } = await import('@/services/service-factory');
      const versionService = getVersionService();

      const configData = {
        channels: {
          [versionMetadata.channel]: {
            version: versionMetadata.version,
            platforms: {
              [versionMetadata.platform]: {
                [versionMetadata.architecture]: {
                  filename: versionMetadata.filename,
                  size: versionMetadata.fileSize,
                  checksum: versionMetadata.checksum,
                  releaseDate: versionMetadata.uploadTime,
                  releaseNotes: versionMetadata.releaseNotes,
                  signature: signature, // 包含签名信息（如果有）
                }
              }
            }
          }
        }
      };

      logger.info('Saving version config to database', {
        channel: versionMetadata.channel,
        version: versionMetadata.version,
        platform: versionMetadata.platform,
        architecture: versionMetadata.architecture,
        filename: versionMetadata.filename,
        hasSignature: !!signature,
        signatureLength: signature ? signature.length : 0,
        configData: JSON.stringify(configData, null, 2),
      });

      // 保存到数据库
      await versionService.updateVersionsConfig(configData);

      logger.info('Version record processed successfully', {
        version: versionMetadata.version,
        platform: versionMetadata.platform,
        architecture: versionMetadata.architecture,
        channel: versionMetadata.channel,
        hasSignature: !!signature,
      });
    } catch (error) {
      logger.error('Failed to process version record', {
        versionMetadata: {
          version: versionMetadata.version,
          platform: versionMetadata.platform,
          architecture: versionMetadata.architecture,
          channel: versionMetadata.channel,
          filename: versionMetadata.filename,
        },
        hasSignature: !!signature,
        error: error,
      });
      throw new Error(`Failed to save version metadata: ${error}`);
    }
  }

  /**
   * 取消批量上传
   */
  async cancelBatchUpload(sessionId: string): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        logger.warn('Batch upload session not found for cancellation', { sessionId });
        return;
      }

      // 更新会话状态
      session.status = UploadStatus.CANCELLED;
      session.lastActivity = new Date();

      logger.info('Batch upload cancelled', {
        sessionId,
        fileCount: session.fileStatuses.length,
        files: session.fileStatuses.map(f => f.filename),
      });

      // 清理会话
      await this.cleanupSession(sessionId);
      this.sessions.delete(sessionId);
    } catch (error) {
      logger.error('Failed to cancel batch upload', { error, sessionId });
      throw error;
    }
  }

  /**
   * 清理会话
   */
  private async cleanupSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      try {
        await fs.remove(session.tempDir);
        logger.debug('Session cleanup completed', { sessionId });
      } catch (error) {
        logger.error('Failed to cleanup session', { sessionId, error });
      }
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 60 * 1000); // 每小时清理一次
  }

  /**
   * 清理过期会话
   */
  private cleanupExpiredSessions(): void {
    const now = new Date();
    const expiredSessions: string[] = [];

    this.sessions.forEach((session, sessionId) => {
      const age = now.getTime() - session.lastActivity.getTime();
      if (age > 24 * 60 * 60 * 1000) { // 24小时过期
        expiredSessions.push(sessionId);
      }
    });

    for (const sessionId of expiredSessions) {
      this.cleanupSession(sessionId);
      this.sessions.delete(sessionId);
    }

    if (expiredSessions.length > 0) {
      logger.info('Cleaned up expired batch upload sessions', {
        count: expiredSessions.length,
        sessionIds: expiredSessions,
      });
    }
  }

  /**
   * 传统的文件哈希验证方法（兼容前端计算的哈希）
   */
  private async verifyFileHashLegacy(sessionId: string, filename: string, fileBuffer: Buffer, fileInfo: BatchUploadFileInfo): Promise<void> {
    // 验证文件哈希 - 兼容前端备用哈希算法
    const actualHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
    const isStandardHash = this.verifyStandardHash(actualHash, fileInfo.fileHash);
    const isFallbackHash = this.verifyFallbackHash(fileBuffer, fileInfo.fileHash);

    // 强制使用 info 级别日志以确保能看到调试信息
    logger.info('Legacy file hash verification details', {
      sessionId,
      filename,
      expectedHash: fileInfo.fileHash,
      actualStandardHash: actualHash,
      fileSize: fileBuffer.length,
      hashLength: fileInfo.fileHash.length,
      isStandardHash,
      isFallbackHash,
      hashType: fileInfo.fileHash.length === 64 ? 'standard-sha256' :
                fileInfo.fileHash.length === 24 ? 'fallback-algorithm' : 'unknown',
    });

    if (!isStandardHash && !isFallbackHash) {
      logger.warn('Legacy file hash verification failed', {
        sessionId,
        filename,
        expectedHash: fileInfo.fileHash,
        actualStandardHash: actualHash,
        fileSize: fileBuffer.length,
        hashLength: fileInfo.fileHash.length,
        hashType: fileInfo.fileHash.length === 64 ? 'standard-sha256' :
                  fileInfo.fileHash.length === 24 ? 'fallback-algorithm' : 'unknown',
        isStandardHash,
        isFallbackHash,
      });
      throw new Error('File hash mismatch');
    }

    if (isFallbackHash && !isStandardHash) {
      logger.info('File verified using fallback hash algorithm', {
        sessionId,
        filename,
        fallbackHash: fileInfo.fileHash,
        fileSize: fileBuffer.length,
      });
    } else if (isStandardHash) {
      logger.info('File verified using standard SHA-256 hash', {
        sessionId,
        filename,
        fileSize: fileBuffer.length,
      });
    }
  }

  /**
   * 验证标准SHA256哈希
   */
  private verifyStandardHash(actualHash: string, expectedHash: string): boolean {
    return actualHash === expectedHash;
  }

  /**
   * 验证前端备用哈希算法
   * 备用算法格式：{hashHex}{sizeHex}{timeHex}
   * - hashHex: 8位十六进制简单哈希
   * - sizeHex: 8位十六进制文件大小
   * - timeHex: 8位十六进制时间戳（服务端验证时忽略，因为无法获得前端文件的lastModified时间）
   */
  private verifyFallbackHash(fileBuffer: Buffer, expectedHash: string): boolean {
    try {
      // 检查哈希长度是否符合备用算法格式（24位十六进制）
      if (expectedHash.length !== 24) {
        return false;
      }

      // 计算简单哈希（与前端算法保持一致）
      let hash = 0;
      for (let i = 0; i < fileBuffer.length; i++) {
        const char = fileBuffer[i] || 0;
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
      }

      // 提取期望的哈希部分和大小部分
      const expectedHashPart = expectedHash.substring(0, 8);
      const expectedSizePart = expectedHash.substring(8, 16);
      // 时间戳部分（16-24位）在服务端验证时忽略，因为服务端无法获得前端文件的lastModified时间

      // 计算实际的哈希和大小
      const actualHashHex = Math.abs(hash).toString(16).padStart(8, '0');
      const actualSizeHex = fileBuffer.length.toString(16).padStart(8, '0');

      // 验证哈希和大小部分（忽略时间戳部分）
      const hashMatch = actualHashHex === expectedHashPart;
      const sizeMatch = actualSizeHex === expectedSizePart;

      // 强制使用 info 级别日志以确保能看到调试信息
      logger.info('Fallback hash verification details', {
        expectedHash,
        expectedHashPart,
        expectedSizePart,
        actualHashHex,
        actualSizeHex,
        fileSize: fileBuffer.length,
        hashMatch,
        sizeMatch,
        result: hashMatch && sizeMatch,
      });

      return hashMatch && sizeMatch;
    } catch (error) {
      logger.debug('Fallback hash verification failed', {
        expectedHash,
        fileSize: fileBuffer.length,
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }
}

// 批量上传会话接口
interface BatchUploadSession {
  sessionId: string;
  files: BatchUploadFileInfo[];
  metadata: FileUploadRequest;
  fileStatuses: BatchUploadFileStatus[];
  tempDir: string;
  createdAt: Date;
  lastActivity: Date;
  status: UploadStatus;
}

// 导出单例实例
export const batchUploadService = new BatchUploadService();
