/**
 * 波形缩放率持久化管理工具
 * 实现缩放率在 project.json 中的保存和恢复功能
 */

import { useProjectStore } from "@/stores/haptics-project-store";
import { DEFAULT_ZOOM_LEVEL, MAX_ZOOM_LEVEL } from "@/components/editor/waveform/config/waveform-constants";
import { waveformLogger } from "@/utils/logger/logger";

// 缩放率持久化配置
export interface ZoomPersistenceConfig {
  fileUuid: string;
  debugMode?: boolean;
}

// 缩放率持久化结果
export interface ZoomPersistenceResult {
  success: boolean;
  zoomLevel?: number;
  error?: string;
}

/**
 * 波形缩放率持久化管理类
 */
export class WaveformZoomPersistence {
  private fileUuid: string;
  private debugMode: boolean;
  private projectStore: ReturnType<typeof useProjectStore>;

  constructor(config: ZoomPersistenceConfig) {
    this.fileUuid = config.fileUuid;
    this.debugMode = config.debugMode || false;
    this.projectStore = useProjectStore();
  }

  /**
   * 保存缩放率到 project.json
   * @param zoomLevel 要保存的缩放率
   * @returns 保存结果
   */
  async saveZoomLevel(zoomLevel: number): Promise<ZoomPersistenceResult> {
    try {
      // 将缩放率精度限制为两位小数
      const roundedZoomLevel = Math.round(zoomLevel * 100) / 100;

      if (this.debugMode) {
        waveformLogger.debug(`保存缩放率: ${this.fileUuid} -> ${roundedZoomLevel} (原始值: ${zoomLevel})`);
      }

      // 验证缩放率有效性
      if (!this.isValidZoomLevel(roundedZoomLevel)) {
        const error = `无效的缩放率: ${roundedZoomLevel}`;
        waveformLogger.warn(error);
        return { success: false, error };
      }

      // 获取当前项目和文件信息
      const currentProject = this.projectStore.currentProject;
      if (!currentProject || !currentProject.files) {
        const error = "当前没有打开的项目";
        waveformLogger.warn(`ZoomPersistence: ${error}`);
        return { success: false, error };
      }

      // 查找目标文件
      const fileIndex = currentProject.files.findIndex(file => file.fileUuid === this.fileUuid);
      if (fileIndex === -1) {
        const error = `文件未找到: ${this.fileUuid}`;
        waveformLogger.warn(`ZoomPersistence: ${error}`);
        return { success: false, error };
      }

      // 检查缩放率是否有变化
      const currentFile = currentProject.files[fileIndex];
      const currentZoomLevel = currentFile.zoomLevel || DEFAULT_ZOOM_LEVEL;

      if (Math.abs(currentZoomLevel - roundedZoomLevel) < 0.001) {
        if (this.debugMode) {
          waveformLogger.debug(`ZoomPersistence: 缩放率无变化，跳过保存: ${roundedZoomLevel}`);
        }
        return { success: true, zoomLevel: roundedZoomLevel };
      }

      // 更新文件的缩放率
      currentProject.files[fileIndex] = {
        ...currentFile,
        zoomLevel: roundedZoomLevel,
        lastModifiedTime: new Date().toISOString()
      };

      // 保存项目到 project.json
      const saveSuccess = await this.projectStore.handleSaveCurrentProject();
      
      if (saveSuccess) {
        if (this.debugMode) {
          waveformLogger.debug(`ZoomPersistence: 缩放率保存成功: ${this.fileUuid} -> ${roundedZoomLevel}`);
        }
        return { success: true, zoomLevel: roundedZoomLevel };
      } else {
        const error = "项目保存失败";
        waveformLogger.error(`ZoomPersistence: ${error}`);
        return { success: false, error };
      }

    } catch (error: any) {
      const errorMessage = `保存缩放率失败: ${error.message || error}`;
      waveformLogger.error(`ZoomPersistence: ${errorMessage}`, error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 从 project.json 恢复缩放率
   * @returns 恢复结果
   */
  restoreZoomLevel(): ZoomPersistenceResult {
    try {
      if (this.debugMode) {
        waveformLogger.debug(`ZoomPersistence: 恢复缩放率: ${this.fileUuid}`);
      }

      // 获取当前项目和文件信息
      const currentProject = this.projectStore.currentProject;
      if (!currentProject || !currentProject.files) {
        const error = "当前没有打开的项目";
        waveformLogger.warn(`ZoomPersistence: ${error}`);
        return {
          success: true,
          zoomLevel: DEFAULT_ZOOM_LEVEL
        };
      }

      // 查找目标文件
      const targetFile = currentProject.files.find(file => file.fileUuid === this.fileUuid);
      if (!targetFile) {
        const error = `文件未找到: ${this.fileUuid}`;
        waveformLogger.warn(`ZoomPersistence: ${error}`);
        return {
          success: true,
          zoomLevel: DEFAULT_ZOOM_LEVEL
        };
      }

      // 获取保存的缩放率，如果没有则使用默认值
      const savedZoomLevel = targetFile.zoomLevel || DEFAULT_ZOOM_LEVEL;

      // 验证缩放率有效性
      const validZoomLevel = this.isValidZoomLevel(savedZoomLevel)
        ? savedZoomLevel
        : DEFAULT_ZOOM_LEVEL;

      if (this.debugMode) {
        waveformLogger.debug(`ZoomPersistence: 缩放率恢复成功: ${this.fileUuid} -> ${validZoomLevel}`);
      }

      return { success: true, zoomLevel: validZoomLevel };

    } catch (error: any) {
      const errorMessage = `恢复缩放率失败: ${error.message || error}`;
      waveformLogger.error(`ZoomPersistence: ${errorMessage}`, error);
      return {
        success: false,
        error: errorMessage,
        zoomLevel: DEFAULT_ZOOM_LEVEL
      };
    }
  }

  /**
   * 验证缩放率是否有效
   * @param zoomLevel 要验证的缩放率
   * @returns 是否有效
   */
  private isValidZoomLevel(zoomLevel: number): boolean {
    return (
      typeof zoomLevel === 'number' &&
      !isNaN(zoomLevel) &&
      isFinite(zoomLevel) &&
      zoomLevel > 0 &&
      zoomLevel <= MAX_ZOOM_LEVEL
    );
  }

  /**
   * 获取文件当前的缩放率（不触发保存）
   * @returns 当前缩放率
   */
  getCurrentZoomLevel(): number {
    const result = this.restoreZoomLevel();
    return result.zoomLevel || DEFAULT_ZOOM_LEVEL;
  }
}

/**
 * 创建缩放率持久化管理器的工厂函数
 * @param fileUuid 文件UUID
 * @param debugMode 是否启用调试模式
 * @returns 缩放率持久化管理器实例
 */
export function createZoomPersistence(fileUuid: string, debugMode = false): WaveformZoomPersistence {
  return new WaveformZoomPersistence({ fileUuid, debugMode });
}

/**
 * 便捷函数：保存缩放率
 * @param fileUuid 文件UUID
 * @param zoomLevel 缩放率
 * @param debugMode 是否启用调试模式
 * @returns 保存结果
 */
export async function saveZoomLevel(
  fileUuid: string, 
  zoomLevel: number, 
  debugMode = false
): Promise<ZoomPersistenceResult> {
  const persistence = createZoomPersistence(fileUuid, debugMode);
  return await persistence.saveZoomLevel(zoomLevel);
}

/**
 * 便捷函数：恢复缩放率
 * @param fileUuid 文件UUID
 * @param debugMode 是否启用调试模式
 * @returns 恢复结果
 */
export function restoreZoomLevel(
  fileUuid: string, 
  debugMode = false
): ZoomPersistenceResult {
  const persistence = createZoomPersistence(fileUuid, debugMode);
  return persistence.restoreZoomLevel();
}

/**
 * 便捷函数：获取当前缩放率
 * @param fileUuid 文件UUID
 * @param debugMode 是否启用调试模式
 * @returns 当前缩放率
 */
export function getCurrentZoomLevel(
  fileUuid: string, 
  debugMode = false
): number {
  const persistence = createZoomPersistence(fileUuid, debugMode);
  return persistence.getCurrentZoomLevel();
}
