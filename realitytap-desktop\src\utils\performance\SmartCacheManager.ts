/**
 * 智能缓存管理器
 * 针对不同操作类型使用不同的缓存策略，优化事件绘制性能
 */

export type CacheStrategy = 'aggressive' | 'normal' | 'conservative' | 'disabled';
export type OperationType = 'drag' | 'property' | 'selection' | 'scroll' | 'zoom' | 'init';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number;
  priority: number;
  operationType: OperationType;
}

interface CacheConfig {
  maxSize: number;
  maxAge: number;
  cleanupInterval: number;
  strategy: CacheStrategy;
}

/**
 * 智能缓存管理器
 * 
 * 特性：
 * 1. 根据操作类型动态调整缓存策略
 * 2. LRU + 优先级混合淘汰算法
 * 3. 自适应缓存大小管理
 * 4. 性能监控和统计
 */
export class SmartCacheManager<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private config: CacheConfig;
  private cleanupTimer: number | null = null;
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalSize: 0,
    operations: new Map<OperationType, number>()
  };

  // 不同操作类型的缓存策略
  private readonly OPERATION_STRATEGIES: Record<OperationType, CacheStrategy> = {
    drag: 'disabled',      // 拖拽时禁用缓存，直接绘制
    property: 'conservative', // 属性调整时保守缓存
    selection: 'normal',   // 选择变化时正常缓存
    scroll: 'aggressive',  // 滚动时积极缓存
    zoom: 'disabled',      // 缩放时禁用缓存
    init: 'normal'         // 初始化时正常缓存
  };

  // 操作类型的优先级权重
  private readonly OPERATION_PRIORITIES: Record<OperationType, number> = {
    drag: 5,      // 拖拽最高优先级
    property: 4,  // 属性调整高优先级
    selection: 3, // 选择中等优先级
    scroll: 2,    // 滚动低优先级
    zoom: 1,      // 缩放最低优先级
    init: 3       // 初始化中等优先级
  };

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: config.maxSize || 50,
      maxAge: config.maxAge || 30000, // 30秒
      cleanupInterval: config.cleanupInterval || 10000, // 10秒
      strategy: config.strategy || 'normal'
    };

    this.startCleanupTimer();
  }

  /**
   * 获取缓存项
   */
  get(key: string, operationType: OperationType): T | null {
    const strategy = this.getEffectiveStrategy(operationType);
    
    // 如果策略禁用缓存，直接返回null
    if (strategy === 'disabled') {
      this.stats.misses++;
      return null;
    }

    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateOperationStats(operationType);
      return null;
    }

    // 检查是否过期
    if (this.isExpired(entry, strategy)) {
      this.cache.delete(key);
      this.stats.misses++;
      this.stats.evictions++;
      return null;
    }

    // 更新访问信息
    entry.lastAccessed = performance.now();
    entry.accessCount++;
    
    this.stats.hits++;
    this.updateOperationStats(operationType);
    
    return entry.data;
  }

  /**
   * 设置缓存项
   */
  set(key: string, data: T, operationType: OperationType, size: number = 1): void {
    const strategy = this.getEffectiveStrategy(operationType);
    
    // 如果策略禁用缓存，直接返回
    if (strategy === 'disabled') {
      return;
    }

    const now = performance.now();
    const priority = this.OPERATION_PRIORITIES[operationType];

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now,
      size,
      priority,
      operationType
    };

    // 检查是否需要清理空间
    if (this.cache.size >= this.config.maxSize) {
      this.evictEntries(strategy);
    }

    this.cache.set(key, entry);
    this.stats.totalSize += size;
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (entry) {
      this.stats.totalSize -= entry.size;
      this.cache.delete(key);
      return true;
    }
    return false;
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.stats.totalSize = 0;
    this.stats.evictions = 0;
  }

  /**
   * 根据操作类型清理缓存
   */
  clearByOperationType(operationType: OperationType): void {
    for (const [key, entry] of this.cache.entries()) {
      if (entry.operationType === operationType) {
        this.stats.totalSize -= entry.size;
        this.cache.delete(key);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? this.stats.hits / (this.stats.hits + this.stats.misses) 
      : 0;

    return {
      ...this.stats,
      hitRate,
      cacheSize: this.cache.size,
      maxSize: this.config.maxSize,
      strategy: this.config.strategy
    };
  }

  /**
   * 更新缓存配置
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 如果最大大小减少，需要清理缓存
    if (newConfig.maxSize && this.cache.size > newConfig.maxSize) {
      this.evictEntries(this.config.strategy);
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
  }

  /**
   * 获取有效的缓存策略
   */
  private getEffectiveStrategy(operationType: OperationType): CacheStrategy {
    // 操作类型策略优先于全局策略
    return this.OPERATION_STRATEGIES[operationType] || this.config.strategy;
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(entry: CacheEntry<T>, strategy: CacheStrategy): boolean {
    const now = performance.now();
    const age = now - entry.timestamp;
    
    // 根据策略调整过期时间
    let maxAge = this.config.maxAge;
    
    switch (strategy) {
      case 'aggressive':
        maxAge *= 2; // 积极缓存，延长过期时间
        break;
      case 'conservative':
        maxAge *= 0.5; // 保守缓存，缩短过期时间
        break;
      case 'normal':
      default:
        // 使用默认过期时间
        break;
    }
    
    return age > maxAge;
  }

  /**
   * 淘汰缓存项
   */
  private evictEntries(strategy: CacheStrategy): void {
    const entries = Array.from(this.cache.entries());
    
    // 根据策略选择淘汰算法
    let toEvict: string[] = [];
    
    switch (strategy) {
      case 'aggressive':
        // 积极缓存：只淘汰最老的项
        toEvict = this.evictByAge(entries, 1);
        break;
        
      case 'conservative':
        // 保守缓存：淘汰更多项，保持缓存较小
        toEvict = this.evictByLRU(entries, Math.ceil(this.config.maxSize * 0.3));
        break;
        
      case 'normal':
      default:
        // 正常缓存：LRU + 优先级混合淘汰
        toEvict = this.evictByPriorityLRU(entries, Math.ceil(this.config.maxSize * 0.2));
        break;
    }
    
    // 执行淘汰
    for (const key of toEvict) {
      const entry = this.cache.get(key);
      if (entry) {
        this.stats.totalSize -= entry.size;
        this.cache.delete(key);
        this.stats.evictions++;
      }
    }
  }

  /**
   * 按年龄淘汰
   */
  private evictByAge(entries: [string, CacheEntry<T>][], count: number): string[] {
    return entries
      .sort((a, b) => a[1].timestamp - b[1].timestamp)
      .slice(0, count)
      .map(([key]) => key);
  }

  /**
   * 按LRU淘汰
   */
  private evictByLRU(entries: [string, CacheEntry<T>][], count: number): string[] {
    return entries
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)
      .slice(0, count)
      .map(([key]) => key);
  }

  /**
   * 按优先级+LRU混合淘汰
   */
  private evictByPriorityLRU(entries: [string, CacheEntry<T>][], count: number): string[] {
    return entries
      .sort((a, b) => {
        // 优先级低的先淘汰
        const priorityDiff = a[1].priority - b[1].priority;
        if (priorityDiff !== 0) return priorityDiff;
        
        // 相同优先级按LRU
        return a[1].lastAccessed - b[1].lastAccessed;
      })
      .slice(0, count)
      .map(([key]) => key);
  }

  /**
   * 更新操作统计
   */
  private updateOperationStats(operationType: OperationType): void {
    const current = this.stats.operations.get(operationType) || 0;
    this.stats.operations.set(operationType, current + 1);
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredEntries();
    }, this.config.cleanupInterval) as unknown as number;
  }

  /**
   * 清理过期项
   */
  private cleanupExpiredEntries(): void {
    const toDelete: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry, this.getEffectiveStrategy(entry.operationType))) {
        toDelete.push(key);
      }
    }
    
    for (const key of toDelete) {
      this.delete(key);
      this.stats.evictions++;
    }
  }
}

/**
 * 便捷的缓存管理器创建函数
 */
export function createSmartCacheManager<T>(config?: Partial<CacheConfig>): SmartCacheManager<T> {
  return new SmartCacheManager<T>(config);
}
