package android.os.vibrator.realitytap.parser;

import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_CREATED;
import static android.os.vibrator.realitytap.he.RealityTapEffect.KEY_DESCRIPTION;

import org.json.JSONException;
import org.json.JSONObject;

import android.os.vibrator.realitytap.he.RealityTapEffect;
import android.os.vibrator.realitytap.he.Metadata;
import android.util.Log;

/** @hide */
abstract class AbstractJsonParser {
    private static final boolean DEBUG = false;

    public final RealityTapEffect parse(JSONObject topNode) throws JSONException {
        Metadata metadata = getMetadata(topNode);
        if (DEBUG) Log.d(HapticParser.TAG, "AbstractJsonParser parse metadata: " + metadata);
        RealityTapEffect effect = parseInternal(topNode);
        if (DEBUG) Log.d(HapticParser.TAG, "AbstractJsonParser parse effect: " + effect);
        effect.setMetadata(metadata);
        return effect;
    }

    abstract RealityTapEffect parseInternal(JSONObject topNode) throws JSONException;

    private Metadata getMetadata(JSONObject topNode) throws JSONException {
        JSONObject metaNode = topNode.getJSONObject(RealityTapEffect.KEY_METADATA);
        Metadata metadata = new Metadata();

        if (metaNode.has(KEY_CREATED)) {
            metadata.setCreated(metaNode.getString(KEY_CREATED));
        }
        if (metaNode.has(KEY_DESCRIPTION)) {
            metadata.setDescription(metaNode.getString(KEY_DESCRIPTION));
        }
        return metadata;
    }
}
