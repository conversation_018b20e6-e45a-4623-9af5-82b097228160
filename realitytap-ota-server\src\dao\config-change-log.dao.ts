import { BaseDAO } from './base.dao';

export interface ConfigChangeLogEntity {
  id: number;
  config_key: string;
  old_value: string | null;
  new_value: string | null;
  user_id: string | null;
  change_reason: string | null;
  created_at: string;
}

export interface CreateConfigChangeLogData {
  config_key: string;
  old_value?: any;
  new_value?: any;
  user_id?: string;
  change_reason?: string;
}

export class ConfigChangeLogDAO extends BaseDAO {
  constructor() {
    super('config_change_logs');
  }

  /**
   * 创建配置变更日志
   */
  async createConfigChangeLog(data: CreateConfigChangeLogData): Promise<number> {
    this.logOperation('createConfigChangeLog', data);
    
    const logData = {
      config_key: data.config_key,
      old_value: data.old_value !== undefined ? JSON.stringify(data.old_value) : null,
      new_value: data.new_value !== undefined ? JSON.stringify(data.new_value) : null,
      user_id: data.user_id || null,
      change_reason: data.change_reason || null
    };
    
    return await this.insert(logData);
  }

  /**
   * 根据配置键获取变更日志
   */
  async getChangeLogsByKey(configKey: string, limit?: number): Promise<ConfigChangeLogEntity[]> {
    this.logOperation('getChangeLogsByKey', { configKey, limit });
    return await this.findWhere<ConfigChangeLogEntity>(
      { config_key: configKey }, 
      'created_at DESC', 
      limit
    );
  }

  /**
   * 获取所有配置变更日志
   */
  async getAllChangeLogs(limit?: number): Promise<ConfigChangeLogEntity[]> {
    this.logOperation('getAllChangeLogs', { limit });
    
    let sql = `SELECT * FROM ${this.tableName} ORDER BY created_at DESC`;
    if (limit) {
      sql += ` LIMIT ${limit}`;
    }
    
    return await this.db.all<ConfigChangeLogEntity>(sql);
  }

  /**
   * 根据用户ID获取变更日志
   */
  async getChangeLogsByUserId(userId: string, limit?: number): Promise<ConfigChangeLogEntity[]> {
    this.logOperation('getChangeLogsByUserId', { userId, limit });
    return await this.findWhere<ConfigChangeLogEntity>(
      { user_id: userId }, 
      'created_at DESC', 
      limit
    );
  }

  /**
   * 获取指定时间范围内的变更日志
   */
  async getChangeLogsByDateRange(startDate: string, endDate: string): Promise<ConfigChangeLogEntity[]> {
    this.logOperation('getChangeLogsByDateRange', { startDate, endDate });
    
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE created_at >= ? AND created_at <= ? 
      ORDER BY created_at DESC
    `;
    
    return await this.db.all<ConfigChangeLogEntity>(sql, [startDate, endDate]);
  }

  /**
   * 删除旧的变更日志
   */
  async deleteOldChangeLogs(olderThanDays: number): Promise<number> {
    this.logOperation('deleteOldChangeLogs', { olderThanDays });
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    const sql = `DELETE FROM ${this.tableName} WHERE created_at < ?`;
    const result = await this.execute(sql, [cutoffDate.toISOString()]);
    
    return result;
  }

  /**
   * 批量创建配置变更日志
   */
  async batchCreateChangeLogs(logs: CreateConfigChangeLogData[]): Promise<void> {
    this.logOperation('batchCreateChangeLogs', { count: logs.length });
    
    const logData = logs.map(log => ({
      config_key: log.config_key,
      old_value: log.old_value !== undefined ? JSON.stringify(log.old_value) : null,
      new_value: log.new_value !== undefined ? JSON.stringify(log.new_value) : null,
      user_id: log.user_id || null,
      change_reason: log.change_reason || null
    }));
    
    await this.batchInsert(logData);
  }
}
