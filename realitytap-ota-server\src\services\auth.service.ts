import { config } from '@/config/server.config';
import { AdminUser, JWTPayload, LoginRequest, LoginResponse, RefreshTokenResponse } from '@/types/auth.types';
import { logger } from '@/utils/logger.util';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

/**
 * 身份验证服务
 * 处理管理员登录、token生成和验证
 */
class AuthService {
  private readonly saltRounds = 12;

  /**
   * 管理员登录
   */
  async login(loginRequest: LoginRequest): Promise<LoginResponse> {
    const { username, password } = loginRequest;

    // 验证用户名
    if (username !== config.admin.username) {
      logger.warn('管理员登录失败 - 用户名错误', {
        username,
        module: 'user_operation',
        operation: 'login_invalid_username',
      });
      throw new Error('Invalid credentials');
    }

    // 验证密码
    const isPasswordValid = await this.verifyPassword(password, config.admin.password);
    if (!isPasswordValid) {
      logger.warn('管理员登录失败 - 密码错误', {
        username,
        module: 'user_operation',
        operation: 'login_invalid_password',
      });
      throw new Error('Invalid credentials');
    }

    // 生成用户信息
    const user: AdminUser = {
      id: 'admin',
      username: config.admin.username,
      role: 'admin',
      loginTime: new Date().toISOString(),
    };

    // 生成JWT token
    const token = this.generateToken(user);
    const expiresIn = this.getTokenExpirationTime();

    logger.info('管理员登录成功', {
      username: user.username,
      loginTime: user.loginTime,
      module: 'user_operation',
      operation: 'login_success',
    });

    return {
      token,
      expiresIn,
      user,
    };
  }

  /**
   * 验证JWT token
   */
  verifyToken(token: string): AdminUser {
    try {
      const decoded = jwt.verify(token, config.admin.jwtSecret) as JWTPayload;

      // 验证token内容
      if (decoded.role !== 'admin' || decoded.username !== config.admin.username) {
        throw new Error('Invalid token payload');
      }

      const user: AdminUser = {
        id: decoded.userId,
        username: decoded.username,
        role: decoded.role,
        loginTime: new Date(decoded.iat * 1000).toISOString(),
      };

      return user;
    } catch (error: any) {
      logger.warn('Token verification failed', { error: error.message });
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * 刷新token
   */
  refreshToken(currentToken: string): RefreshTokenResponse {
    try {
      // 验证当前token（即使过期也要能解析）
      const decoded = jwt.verify(currentToken, config.admin.jwtSecret, {
        ignoreExpiration: true,
      }) as JWTPayload;

      // 检查token是否在合理的过期时间内
      const now = Math.floor(Date.now() / 1000);
      const maxRefreshTime = decoded.exp + 24 * 60 * 60; // 24小时内可以刷新

      if (now > maxRefreshTime) {
        throw new Error('Token too old to refresh');
      }

      // 生成新的用户信息
      const user: AdminUser = {
        id: decoded.userId,
        username: decoded.username,
        role: decoded.role,
        loginTime: new Date().toISOString(),
      };

      // 生成新token
      const newToken = this.generateToken(user);
      const expiresIn = this.getTokenExpirationTime();

      logger.info('Token refreshed successfully', {
        username: user.username,
      });

      return {
        token: newToken,
        expiresIn,
      };
    } catch (error: any) {
      logger.warn('Token refresh failed', { error: error.message });
      throw new Error('Unable to refresh token');
    }
  }

  /**
   * 生成JWT token
   */
  private generateToken(user: AdminUser): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: user.id,
      username: user.username,
      role: user.role,
    };

    return jwt.sign(payload, config.admin.jwtSecret, {
      expiresIn: config.admin.jwtExpiresIn,
    } as jwt.SignOptions);
  }

  /**
   * 验证密码
   */
  private async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    try {
      // 如果配置的密码不是hash，直接比较（开发环境）
      if (!hashedPassword.startsWith('$2')) {
        return plainPassword === hashedPassword;
      }

      // 使用bcrypt验证hash密码
      return await bcrypt.compare(plainPassword, hashedPassword);
    } catch (error) {
      logger.error('Password verification error', { error });
      return false;
    }
  }

  /**
   * 哈希密码（用于设置密码）
   */
  async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, this.saltRounds);
  }

  /**
   * 获取token过期时间（毫秒）
   */
  private getTokenExpirationTime(): number {
    const expiresIn = config.admin.jwtExpiresIn;

    // 解析时间字符串（如 '1h', '30m', '7d'）
    const timeUnit = expiresIn.slice(-1);
    const timeValue = parseInt(expiresIn.slice(0, -1), 10);

    switch (timeUnit) {
      case 's':
        return timeValue * 1000;
      case 'm':
        return timeValue * 60 * 1000;
      case 'h':
        return timeValue * 60 * 60 * 1000;
      case 'd':
        return timeValue * 24 * 60 * 60 * 1000;
      default:
        return 60 * 60 * 1000; // 默认1小时
    }
  }

  /**
   * 验证管理员配置
   */
  validateAdminConfig(): void {
    if (!config.admin.username || !config.admin.password) {
      throw new Error('Admin credentials not configured');
    }

    if (!config.admin.jwtSecret || config.admin.jwtSecret.length < 32) {
      throw new Error('JWT secret not configured or too short');
    }
  }

  /**
   * 生成安全的随机密码
   */
  generateSecurePassword(length: number = 16): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }

    return password;
  }

  /**
   * 检查密码强度
   */
  checkPasswordStrength(password: string): {
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) score += 1;
    else feedback.push('密码长度至少8位');

    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('包含小写字母');

    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('包含大写字母');

    if (/[0-9]/.test(password)) score += 1;
    else feedback.push('包含数字');

    if (/[^a-zA-Z0-9]/.test(password)) score += 1;
    else feedback.push('包含特殊字符');

    return { score, feedback };
  }
}

// 创建单例实例
export const authService = new AuthService();
