import { computed, ref, type Ref } from 'vue';
import type { RenderableEvent } from '@/types/haptic-editor';

/**
 * 事件tooltip配置接口
 */
export interface EventTooltipConfig {
  // 当前拖拽的频率值
  currentDraggedFrequency: Ref<number>;
  currentDraggedRelativeFrequency: Ref<number>;
  // 当前拖拽的时间和强度值
  currentDraggedTimeOffset: Ref<number>;
  currentDraggedIntensity: Ref<number>;
  // 拖拽状态
  isDragging: Ref<boolean>;
  draggingTarget: Ref<string | null>;
  draggedEvent: Ref<RenderableEvent | null>;
  draggedCurveIndex: Ref<number>;
  // 频率调节键状态
  isFrequencyAdjustmentKeyPressed: Ref<boolean>;
}

/**
 * 事件tooltip composable
 * 管理事件值显示的tooltip功能
 */
export function useEventTooltip(config: EventTooltipConfig) {
  // tooltip显示状态
  const showTooltip = ref(false);
  const tooltipPosition = ref({ x: 0, y: 0 });

  // 计算tooltip样式
  const tooltipStyle = computed(() => {
    const position = tooltipPosition.value;
    return {
      left: `${position.x + 15}px`, // 偏移15px避免遮挡鼠标
      top: `${position.y - 60}px`,  // 向上偏移60px显示在鼠标上方
      position: 'fixed' as const,  // 使用fixed定位，相对于视口
      pointerEvents: 'none' as const,
      zIndex: 1000,
    };
  });

  // 计算显示的频率值
  const displayFrequency = computed(() => {
    return Math.round(config.currentDraggedFrequency.value);
  });

  const displayRelativeFrequency = computed(() => {
    const relative = config.currentDraggedRelativeFrequency.value;
    const sign = relative >= 0 ? '+' : '';
    return `${sign}${Math.round(relative)}`;
  });

  // 计算显示的时间值（绝对时间）
  const displayTime = computed(() => {
    if (!config.draggedEvent.value) return '0ms';

    const event = config.draggedEvent.value;
    const curveIndex = config.draggedCurveIndex.value;

    if (event.type === 'continuous' && curveIndex >= 0) {
      const continuousEvent = event as any; // 类型转换以访问curves属性

      // 特殊处理：第一个Curve点的拖拽（调整startTime）
      if (curveIndex === 0) {
        // 第一个点：绝对时间 = 新的startTime = 原startTime + 时间偏移
        const absoluteTime = event.startTime + config.currentDraggedTimeOffset.value;
        return `${Math.round(absoluteTime)}ms`;
      }

      // 特殊处理：最后一个Curve点的拖拽（调整endTime）
      if (curveIndex === continuousEvent.curves?.length - 1) {
        // 最后一个点：绝对时间 = 新的endTime = startTime + 新的duration
        // currentDraggedTimeOffset 对于最后一个点表示duration的变化
        const newDuration = continuousEvent.duration + config.currentDraggedTimeOffset.value;
        const absoluteTime = event.startTime + newDuration;
        return `${Math.round(absoluteTime)}ms`;
      }

      // 中间Curve点：使用原有逻辑
      // 对于中间点，currentDraggedTimeOffset表示该点相对于事件开始的时间偏移
      const absoluteTime = event.startTime + config.currentDraggedTimeOffset.value;
      return `${Math.round(absoluteTime)}ms`;
    }

    return '0ms';
  });

  // 计算显示的振幅值
  const displayAmplitude = computed(() => {
    return `${Math.round(config.currentDraggedIntensity.value)}%`;
  });

  // 显示tooltip
  const showEventTooltip = (x: number, y: number) => {
    tooltipPosition.value = { x, y };
    showTooltip.value = true;
  };

  // 隐藏tooltip
  const hideEventTooltip = () => {
    showTooltip.value = false;
  };

  // 更新tooltip位置
  const updateTooltipPosition = (x: number, y: number) => {
    tooltipPosition.value = { x, y };
  };

  // 检查是否应该显示tooltip
  const shouldShowTooltip = computed(() => {
    return showTooltip.value &&
           config.isDragging.value &&
           config.draggingTarget.value === 'continuousCurvePoint';
  });

  return {
    // 状态
    showTooltip: shouldShowTooltip,
    tooltipStyle,
    displayFrequency,
    displayRelativeFrequency,
    displayTime,
    displayAmplitude,

    // 方法
    showEventTooltip,
    hideEventTooltip,
    updateTooltipPosition,
  };
}
