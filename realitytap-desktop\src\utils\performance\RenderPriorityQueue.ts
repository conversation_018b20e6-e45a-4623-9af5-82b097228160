/**
 * 渲染优先级队列
 * 支持优先级调度和批量处理，优化渲染性能
 */

export type RenderTaskType = 'event' | 'audio' | 'grid' | 'guides' | 'background';
export type RenderPriority = 'critical' | 'high' | 'normal' | 'low' | 'background';

interface RenderTask {
  id: string;
  type: RenderTaskType;
  priority: RenderPriority;
  execute: () => void;
  estimatedTime: number;
  dependencies?: string[];
  batchable: boolean;
  timestamp: number;
}

interface BatchGroup {
  type: RenderTaskType;
  tasks: RenderTask[];
  totalTime: number;
}

/**
 * 渲染优先级队列管理器
 * 
 * 特性：
 * 1. 优先级调度：确保重要任务优先执行
 * 2. 批量处理：合并相似任务减少开销
 * 3. 时间片管理：控制每帧的渲染时间
 * 4. 依赖管理：确保任务按正确顺序执行
 */
export class RenderPriorityQueue {
  private tasks = new Map<string, RenderTask>();
  private isProcessing = false;
  private frameTimeLimit = 16; // 16ms时间片，约60fps
  private currentFrameStartTime = 0;
  private animationFrameId: number | null = null;

  // 优先级权重
  private readonly PRIORITY_WEIGHTS: Record<RenderPriority, number> = {
    critical: 5,
    high: 4,
    normal: 3,
    low: 2,
    background: 1
  };

  // 任务类型的默认优先级
  private readonly DEFAULT_PRIORITIES: Record<RenderTaskType, RenderPriority> = {
    event: 'high',        // 事件绘制优先级高
    audio: 'normal',      // 音频波形正常优先级
    grid: 'low',          // 网格低优先级
    guides: 'low',        // 辅助线低优先级
    background: 'background' // 背景最低优先级
  };

  // 可批量处理的任务类型
  private readonly BATCHABLE_TYPES: Set<RenderTaskType> = new Set(['event', 'grid', 'guides']);

  /**
   * 添加渲染任务
   */
  addTask(
    id: string,
    type: RenderTaskType,
    execute: () => void,
    options: {
      priority?: RenderPriority;
      estimatedTime?: number;
      dependencies?: string[];
      batchable?: boolean;
    } = {}
  ): void {
    const task: RenderTask = {
      id,
      type,
      execute,
      priority: options.priority || this.DEFAULT_PRIORITIES[type],
      estimatedTime: options.estimatedTime || this.getEstimatedTime(type),
      dependencies: options.dependencies || [],
      batchable: options.batchable !== undefined ? options.batchable : this.BATCHABLE_TYPES.has(type),
      timestamp: performance.now()
    };

    // 如果已存在相同ID的任务，替换它
    if (this.tasks.has(id)) {
      this.tasks.set(id, task);
    } else {
      this.tasks.set(id, task);
    }

    this.scheduleProcessing();
  }

  /**
   * 移除任务
   */
  removeTask(id: string): boolean {
    return this.tasks.delete(id);
  }

  /**
   * 清空所有任务
   */
  clear(): void {
    this.tasks.clear();
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * 设置帧时间限制
   */
  setFrameTimeLimit(limit: number): void {
    this.frameTimeLimit = limit;
  }

  /**
   * 获取队列状态
   */
  getQueueStatus() {
    const tasksByPriority: Record<RenderPriority, number> = {
      critical: 0,
      high: 0,
      normal: 0,
      low: 0,
      background: 0
    };

    const tasksByType: Record<RenderTaskType, number> = {
      event: 0,
      audio: 0,
      grid: 0,
      guides: 0,
      background: 0
    };

    for (const task of this.tasks.values()) {
      tasksByPriority[task.priority]++;
      tasksByType[task.type]++;
    }

    return {
      totalTasks: this.tasks.size,
      isProcessing: this.isProcessing,
      tasksByPriority,
      tasksByType,
      frameTimeLimit: this.frameTimeLimit
    };
  }

  /**
   * 调度处理
   */
  private scheduleProcessing(): void {
    if (this.isProcessing || this.tasks.size === 0) {
      return;
    }

    // 检查是否有关键任务需要立即处理
    const criticalTasks = Array.from(this.tasks.values())
      .filter(task => task.priority === 'critical');

    if (criticalTasks.length > 0) {
      this.processCriticalTasks(criticalTasks);
      return;
    }

    // 其他任务使用 requestAnimationFrame 调度
    if (!this.animationFrameId) {
      this.animationFrameId = requestAnimationFrame(() => {
        this.animationFrameId = null;
        this.processTasksInFrame();
      });
    }
  }

  /**
   * 处理关键任务
   */
  private processCriticalTasks(tasks: RenderTask[]): void {
    this.isProcessing = true;

    try {
      // 按依赖关系排序
      const sortedTasks = this.sortTasksByDependencies(tasks);
      
      for (const task of sortedTasks) {
        this.executeTask(task);
        this.tasks.delete(task.id);
      }
    } finally {
      this.isProcessing = false;
      
      // 继续处理其他任务
      if (this.tasks.size > 0) {
        this.scheduleProcessing();
      }
    }
  }

  /**
   * 在帧时间内处理任务
   */
  private processTasksInFrame(): void {
    if (this.tasks.size === 0) {
      return;
    }

    this.isProcessing = true;
    this.currentFrameStartTime = performance.now();

    try {
      // 获取可执行的任务（没有未完成的依赖）
      const executableTasks = this.getExecutableTasks();
      
      if (executableTasks.length === 0) {
        return;
      }

      // 按优先级和类型分组
      const taskGroups = this.groupTasksForBatching(executableTasks);
      
      // 执行任务组
      for (const group of taskGroups) {
        if (!this.hasTimeRemaining()) {
          break;
        }
        
        this.executeTaskGroup(group);
      }

    } finally {
      this.isProcessing = false;
      
      // 如果还有任务且有剩余时间，继续处理
      if (this.tasks.size > 0) {
        if (this.hasTimeRemaining()) {
          // 立即继续处理
          this.processTasksInFrame();
        } else {
          // 下一帧处理
          this.scheduleProcessing();
        }
      }
    }
  }

  /**
   * 获取可执行的任务
   */
  private getExecutableTasks(): RenderTask[] {
    const executableTasks: RenderTask[] = [];
    
    for (const task of this.tasks.values()) {
      // 检查依赖是否都已完成
      const hasUnmetDependencies = task.dependencies?.some(depId => this.tasks.has(depId));
      
      if (!hasUnmetDependencies) {
        executableTasks.push(task);
      }
    }
    
    return executableTasks;
  }

  /**
   * 将任务分组以便批量处理
   */
  private groupTasksForBatching(tasks: RenderTask[]): BatchGroup[] {
    // 按优先级排序
    const sortedTasks = tasks.sort((a, b) => {
      const priorityDiff = this.PRIORITY_WEIGHTS[b.priority] - this.PRIORITY_WEIGHTS[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return a.timestamp - b.timestamp;
    });

    const groups: BatchGroup[] = [];
    const processedTasks = new Set<string>();

    for (const task of sortedTasks) {
      if (processedTasks.has(task.id)) continue;

      if (task.batchable) {
        // 查找相同类型的可批量任务
        const batchTasks = sortedTasks.filter(t => 
          !processedTasks.has(t.id) &&
          t.type === task.type &&
          t.batchable &&
          t.priority === task.priority
        );

        const totalTime = batchTasks.reduce((sum, t) => sum + t.estimatedTime, 0);
        
        groups.push({
          type: task.type,
          tasks: batchTasks,
          totalTime
        });

        batchTasks.forEach(t => processedTasks.add(t.id));
      } else {
        // 单独处理非批量任务
        groups.push({
          type: task.type,
          tasks: [task],
          totalTime: task.estimatedTime
        });

        processedTasks.add(task.id);
      }
    }

    return groups;
  }

  /**
   * 执行任务组
   */
  private executeTaskGroup(group: BatchGroup): void {
    const startTime = performance.now();

    try {
      if (group.tasks.length === 1) {
        // 单个任务直接执行
        this.executeTask(group.tasks[0]);
      } else {
        // 批量执行
        this.executeBatchTasks(group.tasks);
      }

      // 移除已执行的任务
      group.tasks.forEach(task => this.tasks.delete(task.id));

    } catch (error) {
      console.error(`执行任务组失败 [${group.type}]:`, error);
      
      // 即使失败也要移除任务，避免无限重试
      group.tasks.forEach(task => this.tasks.delete(task.id));
    }

    const executionTime = performance.now() - startTime;
    
    // 如果执行时间超出预期，记录警告
    if (executionTime > group.totalTime * 1.5) {
      console.warn(`任务组执行时间超出预期: ${executionTime.toFixed(2)}ms > ${group.totalTime.toFixed(2)}ms`);
    }
  }

  /**
   * 执行单个任务
   */
  private executeTask(task: RenderTask): void {
    task.execute();
  }

  /**
   * 批量执行任务
   */
  private executeBatchTasks(tasks: RenderTask[]): void {
    // 对于可批量的任务，可以进行一些优化
    // 例如：合并相似的绘制操作，减少状态切换等
    
    for (const task of tasks) {
      task.execute();
    }
  }

  /**
   * 按依赖关系排序任务
   */
  private sortTasksByDependencies(tasks: RenderTask[]): RenderTask[] {
    const sorted: RenderTask[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const visit = (task: RenderTask) => {
      if (visiting.has(task.id)) {
        throw new Error(`检测到循环依赖: ${task.id}`);
      }
      
      if (visited.has(task.id)) {
        return;
      }

      visiting.add(task.id);

      // 先处理依赖
      if (task.dependencies) {
        for (const depId of task.dependencies) {
          const depTask = tasks.find(t => t.id === depId);
          if (depTask) {
            visit(depTask);
          }
        }
      }

      visiting.delete(task.id);
      visited.add(task.id);
      sorted.push(task);
    };

    for (const task of tasks) {
      if (!visited.has(task.id)) {
        visit(task);
      }
    }

    return sorted;
  }

  /**
   * 检查是否还有剩余时间
   */
  private hasTimeRemaining(): boolean {
    const elapsed = performance.now() - this.currentFrameStartTime;
    return elapsed < this.frameTimeLimit;
  }

  /**
   * 获取任务类型的预估时间
   */
  private getEstimatedTime(type: RenderTaskType): number {
    const estimates: Record<RenderTaskType, number> = {
      event: 2,      // 2ms per event
      audio: 4,      // 4ms for audio waveform
      grid: 1,       // 1ms for grid
      guides: 0.5,   // 0.5ms for guides
      background: 0.2 // 0.2ms for background
    };

    return estimates[type];
  }
}

/**
 * 便捷的队列创建函数
 */
export function createRenderPriorityQueue(frameTimeLimit: number = 16): RenderPriorityQueue {
  const queue = new RenderPriorityQueue();
  queue.setFrameTimeLimit(frameTimeLimit);
  return queue;
}
