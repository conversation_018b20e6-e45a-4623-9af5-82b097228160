# RealityTap OTA 管理界面

基于 Vue3 + Naive UI 的 RealityTap OTA 服务器管理界面。

## 功能特性

- 🔐 **身份验证** - JWT token 认证，安全登录
- 📊 **仪表板** - 系统概览和统计信息
- 📦 **版本管理** - 文件上传、版本列表、删除操作
- 🖥️ **系统状态** - 健康检查、性能监控、资源使用情况
- 🌙 **主题切换** - 支持浅色/深色主题
- 📱 **响应式设计** - 适配不同屏幕尺寸

## 技术栈

- **前端框架**: Vue 3 + Composition API + `<script setup>`
- **UI 组件库**: Naive UI v2
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **构建工具**: Vite
- **类型检查**: TypeScript
- **HTTP 客户端**: Axios
- **图标**: @vicons/ionicons5

## 开发环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:5173

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 项目结构

```
src/
├── api/                    # API 接口
│   ├── auth.ts            # 身份验证 API
│   ├── admin.ts           # 管理 API
│   └── types.ts           # 类型定义
├── components/            # 组件
│   ├── Layout/           # 布局组件
│   │   └── AdminLayout.vue
│   └── Common/           # 通用组件
│       └── FileUpload.vue
├── router/               # 路由配置
│   └── index.ts
├── stores/               # 状态管理
│   ├── auth.ts          # 身份验证状态
│   ├── admin.ts         # 管理数据状态
│   └── theme.ts         # 主题状态
├── utils/               # 工具函数
│   ├── request.ts       # HTTP 请求封装
│   └── constants.ts     # 常量定义
├── views/               # 页面组件
│   ├── Login.vue        # 登录页面
│   ├── Dashboard.vue    # 仪表板
│   ├── VersionManagement.vue  # 版本管理
│   └── SystemStatus.vue # 系统状态
├── App.vue              # 根组件
└── main.ts              # 入口文件
```

## 配置说明

### 环境变量

开发环境下，API 请求会通过 Vite 代理转发到后端服务器（默认 http://localhost:3000）。

### 代理配置

在 `vite.config.ts` 中配置了 API 代理：

```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:3000',
      changeOrigin: true,
      secure: false,
    },
  },
}
```

## 部署说明

1. 构建生产版本：
   ```bash
   npm run build
   ```

2. 将 `dist` 目录中的文件部署到 Web 服务器

3. 配置 Web 服务器将 API 请求代理到后端服务器

## 开发指南

### 添加新页面

1. 在 `src/views/` 中创建 Vue 组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 如需要，在侧边栏菜单中添加导航项

### 添加新 API

1. 在 `src/api/types.ts` 中定义类型
2. 在相应的 API 文件中添加接口方法
3. 在组件或 store 中调用 API

### 状态管理

使用 Pinia 进行状态管理，主要的 store：

- `useAuthStore` - 身份验证状态
- `useAdminStore` - 管理数据状态
- `useThemeStore` - 主题状态

## 注意事项

- 确保后端服务器已启动并运行在正确的端口
- 开发时需要配置正确的 API 代理
- 生产环境需要配置 Web 服务器的反向代理
