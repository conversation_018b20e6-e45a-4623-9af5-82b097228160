// 波形图事件监听器管理组合式函数
// 负责处理所有事件监听器的配置和管理，包括鼠标、键盘、滚动等事件

import type { ScrollbarInst } from "naive-ui";
import type { Ref } from "vue";
import { ref } from "vue";
import { isPointInRadius } from "../utils/coordinate";
import { DEFAULT_POINT_RADIUS } from "../utils/drawing-helpers";
import { useAsyncOperationManager } from "./useAsyncOperationManager";
import { SCROLL_BOUNDARY_TOLERANCE } from "../config/waveform-constants";
import type { WaveformDataAccess } from "./useWaveformDataAccess";

// 事件监听器配置接口
export interface EventListenerConfig {
  // 基础引用
  waveformCanvas: Ref<HTMLCanvasElement | null>;
  canvasCtx: Ref<CanvasRenderingContext2D | null>;
  horizontalScrollbarRef: Ref<ScrollbarInst | null>;
  longPressTimer: Ref<number | null>;

  // Store 和状态
  waveformStore: any;
  props: any;

  // 数据访问接口（新增）
  dataAccess: WaveformDataAccess;

  // 坐标转换函数
  mapTimeToXLocal: (time: number) => number;
  mapIntensityToYLocal: (intensity: number) => number;
  mapYToIntensityLocal: (y: number) => number;
  mapXToTimeOffsetLocal: (x: number) => number;
  convertXToTimeLocal: (x: number) => number;

  // 画布状态函数
  getEffectiveDuration: () => number;
  getGraphAreaWidth: () => number;
  getLogicalGraphAreaWidth: () => number;

  // 绘制函数
  smartDrawWaveform: (forceRedraw?: boolean) => void;
  throttledDrawWaveform: (forceRedraw?: boolean) => void;
  resetDrawState: () => void;

  // 拖拽相关
  isDragging: Ref<boolean>;
  draggedEvent: Ref<any>;
  draggingTarget: Ref<string | null>;
  draggedCurveIndex: Ref<number>;

  // 拖拽处理函数
  dragHandleMouseDown: (event: MouseEvent) => void;
  dragHandleMouseUp: (event: MouseEvent) => void;
  dragHandleMouseMove: (event: MouseEvent) => void;
  dragResetState: () => void;

  // 点击处理
  handleCanvasClickFromComposable: any;
  shouldIgnoreClick: () => boolean;
  setDragEndTime: (time: number) => void;

  // 右键菜单相关
  isCanvasMenuVisible: Ref<boolean>;
  closeCanvasMenuFromContextMenu: () => void;
  handleCanvasContextMenuFromComposable: any;
  handleCanvasContextMenuFromContextMenu: any;
  handleAddTransientEventFromContextMenu: any;
  handleAddContinuousEventFromContextMenu: any;
  handleDeleteEventFromContextMenu: any;
  handleMenuMouseEnterFromContextMenu: any;
  handleMenuMouseLeaveFromContextMenu: any;
  handleDocumentClickFromContextMenu: any;
  contextMenuEventId: Ref<string | null>;

  // 辅助线相关
  hideRightClickGuide: (shouldRedraw?: boolean) => void;

  // 缩放相关
  handleWheelZoom?: (event: WheelEvent, mouseX: number, mouseY: number) => void;
  currentZoomLevel?: Ref<number>; // 当前缩放级别

  // 虚拟滚动相关
  updateVirtualScrollOffset: () => boolean;
  updateVirtualScrollOffsetLocal: () => void;
  checkAndFixScrollBoundary: (scrollbarRef: ScrollbarInst | null) => boolean;
  scrollLeftValue: Ref<number>;
  virtualScrollOffset: Ref<number>;
  logicalCanvasWidth: Ref<number>;
  canvasWidth: Ref<number>;
  canvasHeight: Ref<number>;

  // 常量
  PADDING: { top: number; right: number; bottom: number; left: number };
}

export function useWaveformEventListeners(config: EventListenerConfig) {
  // 使用异步操作管理器
  const asyncManager = useAsyncOperationManager();
  // 处理画布点击事件
  const handleCanvasClick = (mouseEvent: MouseEvent) => {
    // 如果菜单可见，先隐藏菜单（这会同时隐藏辅助线）
    if (config.isCanvasMenuVisible.value) {
      config.closeCanvasMenuFromContextMenu();
      // 阻止事件冒泡，避免 handleDocumentClick 重复处理
      mouseEvent.stopPropagation();
      return; // 隐藏菜单后直接返回，不处理其他点击逻辑
    }

    // 清除右键辅助线
    config.hideRightClickGuide(false); // 不立即重绘，因为后面会重绘

    // 检查是否应该忽略点击（拖动后的幽灵点击）
    if (config.shouldIgnoreClick()) {
      return;
    }

    if (config.isDragging.value) {
      return;
    }
    if (config.longPressTimer.value) {
      clearTimeout(config.longPressTimer.value);
      config.longPressTimer.value = null;
    }

    // 创建 emit 包装函数
    const emitWrapper = (event: string, ...args: any[]) => {
      if (event === "event-selected") {
        // 这里需要通过回调传递给父组件
        if (config.props.onEventSelected) {
          config.props.onEventSelected(args[0]);
        }
      }
    };

    // 使用 Composable 中的点击处理逻辑
    config.handleCanvasClickFromComposable(
      mouseEvent,
      config.waveformCanvas.value!,
      config.dataAccess.getEvents(), // 使用统一的数据访问接口
      config.mapTimeToXLocal,
      config.mapIntensityToYLocal,
      emitWrapper,
      config.isDragging.value,
      config.longPressTimer,
      config.updateVirtualScrollOffset,
      config.smartDrawWaveform,
      config.dragResetState
    );
  };

  // 处理鼠标离开事件
  const handleMouseLeave = (_mouseEvent: MouseEvent) => {
    // 重绘以移除辅助线等视觉效果
    if (!config.isDragging.value) {
      config.throttledDrawWaveform();
    }
  };

  // 处理非拖动状态下的鼠标移动（悬停）
  const handleMouseHover = (mouseEvent: MouseEvent) => {
    // 如果正在拖动，不处理悬停（移除辅助线检查）
    if (config.isDragging.value) return;

    const canvas = config.waveformCanvas.value;
    const ctx = config.canvasCtx.value;
    // 即使没有事件，也需要处理光标恢复
    if (!canvas || !ctx) return;

    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;
    const mouseX = (mouseEvent.clientX - rect.left) * dpr;
    const mouseY = (mouseEvent.clientY - rect.top) * dpr;

    // 检测鼠标是否悬停在任何关键点上
    // 检查所有事件
    for (const event of config.dataAccess.getEvents()) {
      if (event.type === "transient") {
        // 检查是否悬停在transient peak点上
        const peakX = config.mapTimeToXLocal(event.peakTime);
        const peakY = config.mapIntensityToYLocal(event.intensity);

        if (isPointInRadius(mouseX, mouseY, peakX, peakY, DEFAULT_POINT_RADIUS * 3)) {
          // 改变鼠标样式
          if (canvas) canvas.style.cursor = "crosshair";

          // 重绘波形以显示辅助线
          config.throttledDrawWaveform();
          return;
        }
      } else if (event.type === "continuous") {
        // 检查是否悬停在continuous curve点上
        for (let i = 0; i < event.curves.length; i++) {
          const point = event.curves[i];
          const pointX = config.mapTimeToXLocal(event.startTime + point.timeOffset);
          const pointY = config.mapIntensityToYLocal(point.drawIntensity);

          if (isPointInRadius(mouseX, mouseY, pointX, pointY, DEFAULT_POINT_RADIUS * 3)) {
            // 改变鼠标样式
            if (canvas) canvas.style.cursor = "crosshair";

            // 重绘波形以显示辅助线
            config.throttledDrawWaveform();
            return;
          }
        }
      }
    }

    // 如果没有悬停在任何点上，恢复默认鼠标样式并重绘（移除辅助线）
    if (canvas && canvas.style.cursor !== "default") {
      canvas.style.cursor = "default";
    }
  };

  // 添加滚动更新处理函数
  const handleScrollUpdateWrapper = asyncManager.createThrottle(
    () => {
      // 更新虚拟滚动偏移量
      config.updateVirtualScrollOffsetLocal();

      // 滚动时立即重绘以更新可见事件，确保事件位置同步
      if (!config.isDragging.value) {
        // 强制重绘，确保虚拟滚动时事件位置立即更新
        // 同时重置绘制状态缓存，确保可见事件列表得到更新
        config.resetDrawState();
        config.smartDrawWaveform(true);
      }
    },
    16,
    "滚动更新节流"
  ); // 提高滚动响应频率到约60fps
  const handleScrollUpdate = handleScrollUpdateWrapper.fn;

  // Wheel事件处理函数
  const handleGraphWheelScroll = (event: WheelEvent) => {
    // 清除右键辅助线
    config.hideRightClickGuide(false); // 不立即重绘，因为滚动会触发重绘

    // 如果菜单可见，先隐藏菜单
    if (config.isCanvasMenuVisible.value) {
      config.closeCanvasMenuFromContextMenu();
    }

    // 检查是否按下 Ctrl 键进行缩放
    if (event.ctrlKey && event.deltaY !== 0) {
      // 阻止默认行为（页面缩放）
      event.preventDefault();

      // 获取鼠标在画布中的位置
      const canvas = config.waveformCanvas.value;
      if (canvas) {
        const rect = canvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        // 调用缩放处理函数（如果存在）
        if (config.handleWheelZoom) {
          config.handleWheelZoom(event, mouseX, mouseY);
        }
      }
      return;
    }

    if (config.horizontalScrollbarRef.value && event.deltaY !== 0) {
      // 阻止默认的页面垂直滚动行为，因为我们正在处理水平滚动
      event.preventDefault();

      // 沿用原代码中对滚动量的计算逻辑
      // 基础滚动量：取 event.deltaY 的符号，乘以其绝对值（最大100），再乘以0.5
      let baseAmount = Math.sign(event.deltaY) * Math.min(Math.abs(event.deltaY), 100) * 0.5;

      // 滚动因子：根据 event.deltaMode 调整
      let scrollFactor = 1; // 默认对应 DOM_DELTA_PIXEL
      if (event.deltaMode === WheelEvent.DOM_DELTA_LINE) {
        // DOM_DELTA_LINE 通常值为 1
        scrollFactor = 20; // 与原逻辑一致
      } else if (event.deltaMode === WheelEvent.DOM_DELTA_PAGE) {
        // DOM_DELTA_PAGE 通常值为 2
        scrollFactor = 100; // 与原逻辑一致
      }

      const actualScrollAmount = baseAmount * scrollFactor;
      const currentScrollLeft = config.scrollLeftValue.value;

      // 计算滚动边界（考虑缩放）
      const currentZoom = config.currentZoomLevel?.value || 1.0;
      const scaledLogicalWidth = config.logicalCanvasWidth.value * currentZoom;
      const logicalScrollableWidth = scaledLogicalWidth - config.PADDING.left;
      const physicalScrollableWidth = config.props.availableParentWidth - config.PADDING.left - config.PADDING.right;
      const maxScrollLeft = Math.max(0, logicalScrollableWidth - physicalScrollableWidth);

      // 检查滚动边界
      if (actualScrollAmount < 0) {
        // 向左滚动：检查左边界
        if (currentScrollLeft <= Math.abs(actualScrollAmount) + SCROLL_BOUNDARY_TOLERANCE) {
          config.horizontalScrollbarRef.value.scrollTo({ left: 0, behavior: "auto" });
          return;
        }
      } else {
        // 向右滚动：检查右边界
        if (currentScrollLeft + actualScrollAmount >= maxScrollLeft - SCROLL_BOUNDARY_TOLERANCE) {
          config.horizontalScrollbarRef.value.scrollTo({
            left: maxScrollLeft,
            behavior: "auto",
          });
          return;
        }
      }

      // 在边界范围内，正常滚动
      config.horizontalScrollbarRef.value.scrollBy({
        left: actualScrollAmount,
        top: 0,
        behavior: "auto",
      });
    }
  };

  // 处理 n-scrollbar 的 @scroll 事件
  const onHorizontalScroll = (event: Event) => {
    const target = event.target as HTMLElement;
    if (target) {
      config.scrollLeftValue.value = target.scrollLeft;
    }

    // 检查并修正滚动边界
    const wasCorrected = config.checkAndFixScrollBoundary(config.horizontalScrollbarRef.value);

    // 如果进行了边界修正，跳过后续处理，等待下次滚动事件
    if (wasCorrected) {
      return;
    }

    // 更新虚拟滚动偏移量
    config.updateVirtualScrollOffsetLocal();

    // 立即处理滚动更新，不使用节流，确保边界位置的精确同步
    if (!config.isDragging.value) {
      // 计算是否在边界位置（考虑缩放）
      const currentZoom = config.currentZoomLevel?.value || 1.0;
      const scaledLogicalWidth = config.logicalCanvasWidth.value * currentZoom;
      const logicalScrollableWidth = scaledLogicalWidth - config.PADDING.left;
      const physicalScrollableWidth = config.props.availableParentWidth - config.PADDING.left - config.PADDING.right;
      const maxScrollLeft = Math.max(0, logicalScrollableWidth - physicalScrollableWidth);
      const isAtBoundary = config.scrollLeftValue.value <= SCROLL_BOUNDARY_TOLERANCE || config.scrollLeftValue.value >= maxScrollLeft - SCROLL_BOUNDARY_TOLERANCE;

      if (isAtBoundary) {
        // 边界位置立即重绘
        config.resetDrawState();
        config.smartDrawWaveform(true);
      } else {
        // 非边界位置使用节流
        handleScrollUpdate();
      }
    }
  };

  // 右键菜单状态管理
  const isRightMouseDown = ref(false);
  const rightMouseDownPosition = ref<{ x: number; y: number } | null>(null);

  // 处理鼠标按下事件（检测右键按下）
  const handleMouseDown = (mouseEvent: MouseEvent) => {
    // 检查是否是右键按下
    if (mouseEvent.button === 2) { // 右键
      mouseEvent.preventDefault();
      mouseEvent.stopPropagation();

      isRightMouseDown.value = true;
      rightMouseDownPosition.value = { x: mouseEvent.clientX, y: mouseEvent.clientY };

      // 立即显示右键菜单
      const canvas = config.waveformCanvas.value;
      if (canvas && config.canvasCtx.value) {
        config.handleCanvasContextMenuFromContextMenu(mouseEvent, canvas, config.dataAccess.getEvents(), config.mapTimeToXLocal, config.mapIntensityToYLocal, config.convertXToTimeLocal);
      }
      return;
    }

    // 如果不是右键，继续原有的拖拽逻辑
    config.dragHandleMouseDown(mouseEvent);
  };

  // 处理鼠标移动事件（检测右键拖拽）
  const handleMouseMove = (mouseEvent: MouseEvent) => {
    // 如果右键被按下且正在移动，实时更新菜单和辅助线位置
    if (isRightMouseDown.value && config.isCanvasMenuVisible.value) {
      mouseEvent.preventDefault();

      const canvas = config.waveformCanvas.value;
      if (canvas && config.canvasCtx.value) {
        // 重新计算菜单位置和时间点
        config.handleCanvasContextMenuFromContextMenu(mouseEvent, canvas, config.dataAccess.getEvents(), config.mapTimeToXLocal, config.mapIntensityToYLocal, config.convertXToTimeLocal);
      }
      return;
    }

    // 继续原有的鼠标移动逻辑
    config.dragHandleMouseMove(mouseEvent);
  };

  // 处理鼠标松开事件
  const handleMouseUp = (mouseEvent: MouseEvent) => {
    // 如果是右键松开
    if (mouseEvent.button === 2 && isRightMouseDown.value) {
      isRightMouseDown.value = false;
      rightMouseDownPosition.value = null;
      // 右键松开时不关闭菜单，保持菜单显示
      return;
    }

    // 继续原有的鼠标松开逻辑
    config.dragHandleMouseUp(mouseEvent);
  };

  // 使用 composable 中的右键菜单处理函数（保留原有的 contextmenu 事件处理，作为备用）
  const handleCanvasContextMenu = (mouseEvent: MouseEvent) => {
    // 阻止默认的右键菜单
    mouseEvent.preventDefault();
    mouseEvent.stopPropagation();

    // 如果菜单已经通过 mousedown 显示，则不重复处理
    if (config.isCanvasMenuVisible.value) {
      return;
    }

    const canvas = config.waveformCanvas.value;
    if (!canvas || !config.canvasCtx.value) return;

    config.handleCanvasContextMenuFromContextMenu(mouseEvent, canvas, config.dataAccess.getEvents(), config.mapTimeToXLocal, config.mapIntensityToYLocal, config.convertXToTimeLocal);
  };

  // 使用 composable 中的添加瞬态事件函数
  const handleAddTransientEvent = () => {
    config.handleAddTransientEventFromContextMenu(config.dataAccess.getEvents());
  };

  // 使用 composable 中的添加连续事件函数
  const handleAddContinuousEvent = () => {
    config.handleAddContinuousEventFromContextMenu(config.dataAccess.getEvents());
  };

  // 使用 composable 中的关闭菜单函数
  const closeCanvasMenu = () => {
    config.closeCanvasMenuFromContextMenu();
  };

  // 使用 composable 中的文档点击处理函数
  const handleDocumentClick = (event: MouseEvent) => {
    config.handleDocumentClickFromContextMenu(event);
  };

  // 使用 composable 中的菜单鼠标事件处理函数
  const handleMenuMouseEnter = () => {
    config.handleMenuMouseEnterFromContextMenu();
  };

  const handleMenuMouseLeave = () => {
    config.handleMenuMouseLeaveFromContextMenu();
  };

  // 使用 composable 中的删除事件函数
  const handleDeleteEvent = () => {
    config.handleDeleteEventFromContextMenu(config.contextMenuEventId);
  };

  // 返回所有事件处理函数
  return {
    // 主要事件处理函数
    handleCanvasClick,
    handleMouseLeave,
    handleMouseHover,
    handleGraphWheelScroll,
    onHorizontalScroll,

    // 新的鼠标事件处理函数（用于右键菜单增强）
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,

    // 右键菜单相关事件处理函数
    handleCanvasContextMenu,
    handleAddTransientEvent,
    handleAddContinuousEvent,
    closeCanvasMenu,
    handleDocumentClick,
    handleMenuMouseEnter,
    handleMenuMouseLeave,
    handleDeleteEvent,

    // 内部辅助函数
    handleScrollUpdate,
  };
}
