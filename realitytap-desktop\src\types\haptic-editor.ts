// Define interfaces for renderable event types used by waveform components

export interface RenderableTransientEvent {
  type: "transient";
  id: string; // Unique ID for Vue keys
  startTime: number; // Vibration actual start time (ms) = Math.floor(AbsoluteTime + RelativeTime)
  peakTime: number; // Calculated absolute vibration peak time (ms) = Math.floor(startTime + width/2)
  stopTime: number; // Calculated absolute vibration stop time (ms) = Math.floor(startTime + width)
  intensity: number; // Parameters.Intensity (0-100)
  frequency: number; // Parameters.Frequency (0-100)
  width: number; // Calculated width (10-25ms) based on Frequency
}

export interface RenderableContinuousCurvePoint {
  timeOffset: number; // CurvePoint.Time (ms, relative to event start)
  drawIntensity: number; // CurvePoint.Intensity (0-1) * Event.Parameters.Intensity (0-100)
  rawIntensity: number; // CurvePoint.Intensity (0-1)
  relativeCurveFrequency: number; // CurvePoint.Frequency (-100-100), this is the value to be edited directly
  curveFrequency: number; // Calculated: relativeCurveFrequency + Event.Parameters.Frequency (0-100), final range -100 to 200
}

export interface RenderableContinuousEvent {
  type: "continuous";
  id: string; // Unique ID for Vue keys
  startTime: number; // AbsoluteTime + RelativeTime
  stopTime: number; // Calculated: startTime + duration (where duration is curve-based)
  duration: number; // Calculated actual duration of the event based on its curve points (ms)
  originalEventDuration?: number; // Original Event.Duration from the source file, if available
  eventIntensity: number; // Parameters.Intensity (0-100)
  eventFrequency: number; // Parameters.Frequency (0-100)
  curves: RenderableContinuousCurvePoint[]; // 支持4-16个点
}

export type RenderableEvent =
  | RenderableTransientEvent
  | RenderableContinuousEvent;
