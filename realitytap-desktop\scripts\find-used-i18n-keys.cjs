#!/usr/bin/env node

/**
 * 查找实际使用的国际化翻译键
 * 扫描所有源代码文件，找出实际使用的 t() 和 $t() 调用
 */

const fs = require('fs');
const path = require('path');

// 要扫描的文件扩展名
const FILE_EXTENSIONS = ['.vue', '.ts', '.js'];

// 要扫描的目录
const SCAN_DIRS = [
  path.join(__dirname, '../src'),
];

// 提取翻译键的正则表达式
const I18N_PATTERNS = [
  // t("key") 或 t('key')
  /\bt\(\s*["']([^"']+)["']\s*\)/g,
  // $t("key") 或 $t('key')
  /\$t\(\s*["']([^"']+)["']\s*\)/g,
  // t(`key`) 模板字符串
  /\bt\(\s*`([^`]+)`\s*\)/g,
  // $t(`key`) 模板字符串
  /\$t\(\s*`([^`]+)`\s*\)/g,
];

// 递归扫描目录
function scanDirectory(dir) {
  const files = [];
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过 node_modules 和其他不需要的目录
        if (!item.startsWith('.') && item !== 'node_modules') {
          scan(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (FILE_EXTENSIONS.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  scan(dir);
  return files;
}

// 从文件内容中提取翻译键
function extractKeysFromContent(content) {
  const keys = new Set();
  
  for (const pattern of I18N_PATTERNS) {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const key = match[1];
      // 过滤掉包含变量的键（如 ${variable} 或 {variable}）
      if (!key.includes('${') && !key.includes('{') && key.trim()) {
        keys.add(key.trim());
      }
    }
  }
  
  return Array.from(keys);
}

// 主函数
function findUsedI18nKeys() {
  console.log('🔍 扫描源代码中使用的国际化翻译键...\n');
  
  const allUsedKeys = new Set();
  const fileKeyMap = new Map();
  
  // 扫描所有目录
  for (const dir of SCAN_DIRS) {
    console.log(`📂 扫描目录: ${dir}`);
    const files = scanDirectory(dir);
    console.log(`   找到 ${files.length} 个文件\n`);
    
    // 处理每个文件
    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const keys = extractKeysFromContent(content);
        
        if (keys.length > 0) {
          const relativePath = path.relative(path.join(__dirname, '..'), file);
          fileKeyMap.set(relativePath, keys);
          
          // 添加到总集合
          keys.forEach(key => allUsedKeys.add(key));
        }
      } catch (error) {
        console.error(`❌ 读取文件失败: ${file} - ${error.message}`);
      }
    }
  }
  
  // 按类别分组键
  const keysByCategory = {};
  const usedKeysArray = Array.from(allUsedKeys).sort();
  
  for (const key of usedKeysArray) {
    const category = key.split('.')[0];
    if (!keysByCategory[category]) {
      keysByCategory[category] = [];
    }
    keysByCategory[category].push(key);
  }
  
  // 输出结果
  console.log(`📊 总共找到 ${usedKeysArray.length} 个使用的翻译键\n`);
  
  console.log('📋 按类别分组的使用键:');
  for (const [category, keys] of Object.entries(keysByCategory)) {
    console.log(`\n🏷️  ${category} (${keys.length} 个键):`);
    keys.forEach(key => {
      console.log(`   - ${key}`);
    });
  }
  
  // 重点关注 update 和 about 相关的键
  console.log('\n🔍 重点分析 - update 相关的键:');
  const updateKeys = usedKeysArray.filter(key => key.startsWith('update.'));
  if (updateKeys.length > 0) {
    updateKeys.forEach(key => {
      console.log(`   ✅ ${key}`);
    });
  } else {
    console.log('   ❌ 没有找到 update 相关的键');
  }
  
  console.log('\n🔍 重点分析 - about 相关的键:');
  const aboutKeys = usedKeysArray.filter(key => key.startsWith('about.'));
  if (aboutKeys.length > 0) {
    aboutKeys.forEach(key => {
      console.log(`   ✅ ${key}`);
    });
  } else {
    console.log('   ❌ 没有找到 about 相关的键');
  }
  
  // 输出详细的文件使用情况
  console.log('\n📄 详细的文件使用情况:');
  for (const [file, keys] of fileKeyMap.entries()) {
    const updateOrAboutKeys = keys.filter(key => key.startsWith('update.') || key.startsWith('about.'));
    if (updateOrAboutKeys.length > 0) {
      console.log(`\n📁 ${file}:`);
      updateOrAboutKeys.forEach(key => {
        console.log(`   - ${key}`);
      });
    }
  }
  
  return {
    allUsedKeys: usedKeysArray,
    keysByCategory,
    updateKeys,
    aboutKeys,
    fileKeyMap
  };
}

// 运行分析
if (require.main === module) {
  const result = findUsedI18nKeys();
  
  // 保存结果到文件
  const outputFile = path.join(__dirname, 'used-i18n-keys.json');
  fs.writeFileSync(outputFile, JSON.stringify(result, null, 2));
  console.log(`\n💾 结果已保存到: ${outputFile}`);
}

module.exports = { findUsedI18nKeys };
