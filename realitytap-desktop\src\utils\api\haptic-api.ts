// RealityTap 触觉反馈 API
// 提供统一的触觉反馈接口，封装 Tauri 命令调用

import { invoke } from "@tauri-apps/api/core";
import { logger, LogModule } from "@/utils/logger/logger";
import type { HapticPlayParams, HapticActuatorStatus, ConfigFileInfo } from "@/types/haptic-types";

/**
 * 触觉反馈常量定义
 */
export const HAPTIC_CONSTANTS = {
  // 默认配置参数
  DEFAULT_MOTOR_DRIVE_FREQ: 170,
  DEFAULT_SAMPLING_RATE: 8,
  DEFAULT_CONFIG_FILE: "motors/LRA_0809_normal.conf",
  DEFAULT_TIMEOUT_MS: 500,

  // 振幅范围
  MAX_AMPLITUDE: 255,
  MIN_AMPLITUDE: 0,

  // 采样率映射
  SAMPLING_RATES: {
    SAMPLING_6KHZ: 4,
    SAMPLING_8KHZ: 5,
    SAMPLING_12KHZ: 6,
    SAMPLING_24KHZ: 8,
  },
} as const;

/**
 * 统一的设备配置接口
 */
export interface HapticDeviceConfig {
  id: number;
  motor_drive_freq: number;
  config_file: string;
  sampling_rate: number;
  timeout_ms?: number;
  enable_logging?: boolean;
  enable_events?: boolean;
  sample_throttle_ms?: number;
}

/**
 * 架构信息接口
 */
export interface ArchitectureInfo {
  architecture: "simplified" | "complex";
  simplified_enabled: boolean;
  version: string;
  description: string;
}

/**
 * 触觉反馈 API 类
 * 提供统一的触觉反馈接口，封装 Tauri 命令调用
 */
export class HapticApi {
  private static architectureInfo: ArchitectureInfo | null = null;

  /**
   * 获取当前架构信息
   */
  static async getArchitectureInfo(): Promise<ArchitectureInfo> {
    if (!this.architectureInfo) {
      try {
        this.architectureInfo = await invoke<ArchitectureInfo>("haptic_get_architecture_info");
        logger.info(LogModule.GENERAL, "当前架构信息:", this.architectureInfo);
      } catch (error) {
        logger.warn(LogModule.GENERAL, "无法获取架构信息，使用默认配置:", error);
        this.architectureInfo = {
          architecture: "simplified",
          simplified_enabled: true,
          version: "unified-v2.0",
          description: "统一架构，简化实现",
        };
      }
    }
    return this.architectureInfo;
  }

  /**
   * 检查是否使用简化架构
   */
  static async isSimplifiedArchitecture(): Promise<boolean> {
    const info = await this.getArchitectureInfo();
    return info.simplified_enabled;
  }

  /**
   * 转换配置格式
   */
  private static convertConfig(config: any): HapticDeviceConfig {
    return {
      id: config.id,
      motor_drive_freq: config.motor_drive_freq,
      config_file: config.config_file,
      sampling_rate: config.sampling_rate,
      timeout_ms: config.timeout_ms,
      enable_logging: config.enable_logging ?? true,
      enable_events: config.enable_events ?? true,
      sample_throttle_ms: config.sample_throttle_ms ?? 10,
    };
  }

  /**
   * 转换播放参数格式
   */
  private static convertPlayParams(params: any): HapticPlayParams {
    return {
      data: params.data,
      interval_ms: params.interval_ms,
      loop_count: params.loop_count,
      amplitude: params.amplitude,
      frequency_offset: params.frequency_offset,
    };
  }

  /**
   * 初始化触觉反馈库
   * @param configs 设备配置数组
   * @returns 初始化结果消息
   */
  static async initialize(configs: any[]): Promise<string> {
    try {
      const unifiedConfigs = configs.map((config) => this.convertConfig(config));
      const result = await invoke<string>("haptic_init_unified", {
        configs: unifiedConfigs,
      });

      const archInfo = await this.getArchitectureInfo();
      logger.info(LogModule.GENERAL, `触觉反馈库初始化成功 (${archInfo.architecture} 架构):`, result);
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "触觉反馈库初始化失败:", error);
      throw new Error(`初始化失败: ${error}`);
    }
  }

  /**
   * 启动触觉播放系统
   * @returns 启动结果消息
   */
  static async start(): Promise<string> {
    try {
      const result = await invoke<string>("haptic_start_unified");
      logger.info(LogModule.GENERAL, "触觉播放系统启动成功:", result);
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "触觉播放系统启动失败:", error);
      throw new Error(`启动失败: ${error}`);
    }
  }

  /**
   * 播放触觉数据
   * @param params 播放参数
   * @returns 播放结果消息
   */
  static async play(params: any): Promise<string> {
    try {
      const unifiedParams = this.convertPlayParams(params);
      const result = await invoke<string>("haptic_play_unified", {
        params: unifiedParams,
      });
      logger.debug(LogModule.GENERAL, "触觉数据播放成功:", result);
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "触觉数据播放失败:", error);
      throw new Error(`播放失败: ${error}`);
    }
  }

  /**
   * 保存触觉数据到文件
   * @param params 播放参数
   * @param filePath 保存文件路径
   * @returns 保存结果消息
   */
  static async saveToFile(params: any, filePath: string): Promise<string> {
    try {
      const unifiedParams = this.convertPlayParams(params);
      const saveParams = {
        ...unifiedParams,
        file_path: filePath,
      };

      const result = await invoke<string>("haptic_save_to_file", {
        params: saveParams,
      });
      logger.info(LogModule.GENERAL, "触觉数据保存到文件成功:", { filePath, result });
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "触觉数据保存到文件失败:", error);
      throw new Error(`保存到文件失败: ${error}`);
    }
  }

  /**
   * 播放预定义效果
   * @param effectId 效果ID
   * @param strength 强度 (0-100)
   * @returns 播放结果消息
   */
  static async playEffect(effectId: number, strength: number): Promise<string> {
    try {
      const result = await invoke<string>("haptic_play_effect_unified", {
        effect_id: effectId,
        strength,
      });
      logger.debug(LogModule.GENERAL, "预定义效果播放成功:", result);
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "预定义效果播放失败:", error);
      throw new Error(`播放效果失败: ${error}`);
    }
  }

  /**
   * 停止触觉播放
   * @returns 停止结果消息
   */
  static async stop(): Promise<string> {
    try {
      const result = await invoke<string>("haptic_stop_unified");
      logger.info(LogModule.GENERAL, "触觉播放已停止:", result);
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "停止触觉播放失败:", error);
      throw new Error(`停止失败: ${error}`);
    }
  }

  /**
   * 设置全局振幅
   * @param amplitude 振幅值 (0-255)
   * @returns 设置结果消息
   */
  static async setAmplitude(amplitude: number): Promise<string> {
    try {
      const result = await invoke<string>("haptic_set_amplitude_unified", { amplitude });
      logger.debug(LogModule.GENERAL, "振幅设置成功:", result);
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "设置振幅失败:", error);
      throw new Error(`设置振幅失败: ${error}`);
    }
  }

  /**
   * 获取系统状态
   * @returns 系统状态信息
   */
  static async getStatus(): Promise<any> {
    try {
      const result = await invoke<any>("haptic_get_status_unified");
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "获取状态失败:", error);
      throw new Error(`获取状态失败: ${error}`);
    }
  }

  /**
   * 清理触觉反馈库
   * @returns 清理结果消息
   */
  static async cleanup(): Promise<string> {
    try {
      const result = await invoke<string>("haptic_cleanup_unified");
      logger.info(LogModule.GENERAL, "资源清理成功:", result);
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "清理失败:", error);
      throw new Error(`清理失败: ${error}`);
    }
  }

  /**
   * 重新初始化触觉反馈库
   * @param configs 设备配置数组
   * @returns 重新初始化结果消息
   */
  static async reinitialize(configs: any[]): Promise<string> {
    try {
      const unifiedConfigs = configs.map((config) => this.convertConfig(config));
      const result = await invoke<string>("haptic_reinit_unified", {
        configs: unifiedConfigs,
      });
      logger.info(LogModule.GENERAL, "触觉反馈库重新初始化成功:", result);
      return result;
    } catch (error) {
      logger.error(LogModule.GENERAL, "重新初始化失败:", error);
      throw new Error(`重新初始化失败: ${error}`);
    }
  }

  // 兼容性方法 - 保留部分常用的状态查询命令
  /**
   * 获取设备状态
   * @param deviceId 设备ID
   * @returns 设备状态信息
   */
  static async getDeviceStatus(deviceId: number): Promise<HapticActuatorStatus> {
    try {
      return await invoke<HapticActuatorStatus>("haptic_get_device_status", {
        device_id: deviceId,
      });
    } catch (error) {
      logger.error(LogModule.GENERAL, "获取设备状态失败:", error);
      throw new Error(`获取设备状态失败: ${error}`);
    }
  }

  /**
   * 获取所有设备状态
   * @returns 所有设备状态数组
   */
  static async getAllDeviceStatus(): Promise<HapticActuatorStatus[]> {
    try {
      return await invoke<HapticActuatorStatus[]>("haptic_get_all_device_status");
    } catch (error) {
      logger.error(LogModule.GENERAL, "获取所有设备状态失败:", error);
      throw new Error(`获取所有设备状态失败: ${error}`);
    }
  }

  /**
   * 获取库状态
   * @returns 库状态信息
   */
  static async getLibraryStatus(): Promise<Record<string, boolean>> {
    try {
      return await invoke<Record<string, boolean>>("haptic_get_library_status");
    } catch (error) {
      logger.error(LogModule.GENERAL, "获取库状态失败:", error);
      throw new Error(`获取库状态失败: ${error}`);
    }
  }

  /**
   * 获取可用的配置文件列表
   * @returns 配置文件路径数组
   */
  static async getAvailableConfigs(): Promise<string[]> {
    try {
      return await invoke<string[]>("haptic_get_available_configs");
    } catch (error) {
      logger.error(LogModule.GENERAL, "获取可用配置失败:", error);
      throw new Error(`获取可用配置失败: ${error}`);
    }
  }

  /**
   * 验证配置文件是否存在
   * @param configFile 配置文件路径
   * @returns 文件是否存在
   */
  static async validateConfigFile(configFile: string): Promise<boolean> {
    try {
      return await invoke<boolean>("haptic_validate_config_file", {
        configFile: configFile,
      });
    } catch (error) {
      logger.error(LogModule.GENERAL, "验证配置文件失败:", error);
      throw new Error(`验证配置文件失败: ${error}`);
    }
  }

  /**
   * 获取配置文件详细信息
   * @param configFile 配置文件路径
   * @returns 配置文件信息
   */
  static async getConfigFileInfo(configFile: string): Promise<ConfigFileInfo> {
    try {
      return await invoke<ConfigFileInfo>("haptic_get_config_file_info", {
        configFile: configFile,
      });
    } catch (error) {
      logger.error(LogModule.GENERAL, "获取配置文件信息失败:", error);
      throw new Error(`获取配置文件信息失败: ${error}`);
    }
  }

  /**
   * 获取所有配置文件的详细信息
   * @returns 所有配置文件信息数组
   */
  static async getAllConfigInfo(): Promise<ConfigFileInfo[]> {
    try {
      return await invoke<ConfigFileInfo[]>("haptic_get_all_config_info");
    } catch (error) {
      logger.error(LogModule.GENERAL, "获取所有配置信息失败:", error);
      throw new Error(`获取所有配置信息失败: ${error}`);
    }
  }
}

/**
 * 便捷的函数式 API 接口
 * 为不需要类实例的场景提供直接调用方式
 */
export const hapticApi = {
  /**
   * 获取架构信息
   */
  getArchitectureInfo: () => HapticApi.getArchitectureInfo(),

  /**
   * 检查是否使用简化架构
   */
  isSimplifiedArchitecture: () => HapticApi.isSimplifiedArchitecture(),

  /**
   * 初始化触觉反馈库
   */
  initialize: (configs: any[]) => HapticApi.initialize(configs),

  /**
   * 启动触觉播放系统
   */
  start: () => HapticApi.start(),

  /**
   * 播放触觉数据
   */
  play: (params: any) => HapticApi.play(params),

  /**
   * 保存触觉数据到文件
   */
  saveToFile: (params: any, filePath: string) => HapticApi.saveToFile(params, filePath),

  /**
   * 播放预定义效果
   */
  playEffect: (effectId: number, strength: number) => HapticApi.playEffect(effectId, strength),

  /**
   * 停止触觉播放
   */
  stop: () => HapticApi.stop(),

  /**
   * 设置全局振幅
   */
  setAmplitude: (amplitude: number) => HapticApi.setAmplitude(amplitude),

  /**
   * 获取系统状态
   */
  getStatus: () => HapticApi.getStatus(),

  /**
   * 清理触觉反馈库
   */
  cleanup: () => HapticApi.cleanup(),

  /**
   * 重新初始化触觉反馈库
   */
  reinitialize: (configs: any[]) => HapticApi.reinitialize(configs),

  /**
   * 获取设备状态
   */
  getDeviceStatus: (deviceId: number) => HapticApi.getDeviceStatus(deviceId),

  /**
   * 获取所有设备状态
   */
  getAllDeviceStatus: () => HapticApi.getAllDeviceStatus(),

  /**
   * 获取库状态
   */
  getLibraryStatus: () => HapticApi.getLibraryStatus(),

  /**
   * 获取可用配置
   */
  getAvailableConfigs: () => HapticApi.getAvailableConfigs(),

  /**
   * 验证配置文件
   */
  validateConfigFile: (configFile: string) => HapticApi.validateConfigFile(configFile),

  /**
   * 获取配置文件信息
   */
  getConfigFileInfo: (configFile: string) => HapticApi.getConfigFileInfo(configFile),

  /**
   * 获取所有配置信息
   */
  getAllConfigInfo: () => HapticApi.getAllConfigInfo(),
};

// 默认导出 HapticApi 类
export default HapticApi;
