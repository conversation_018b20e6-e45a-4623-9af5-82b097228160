package android.os.vibrator.realitytap.he;

import android.os.Parcel;
import android.os.Parcelable;

import android.annotation.NonNull;

/** @hide */
public class Metadata implements Parcelable {
    public static final int META_DEFAULT_VERSION = 1;
    public static final String META_DEFAULT_CREATED = "2022-01-01";
    public static final String META_DEFAULT_DESCRIPTION = "Exported from RealityTap editor";

    private String created = META_DEFAULT_CREATED;
    private String description = META_DEFAULT_DESCRIPTION;
    private int version = META_DEFAULT_VERSION;

    public Metadata() {}

    protected Metadata(@NonNull Parcel in) {
        created = in.readString();
        description = in.readString();
        version = in.readInt();
    }

    public static final @NonNull Creator<Metadata> CREATOR = new Creator<>() {
        @Override
        public Metadata createFromParcel(@NonNull Parcel in) {
            return new Metadata(in);
        }

        @Override
        public Metadata[] newArray(int size) {
            return new Metadata[size];
        }
    };

    public String getCreated() {
        return created;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeString(created);
        dest.writeString(description);
        dest.writeInt(version);
    }

    @NonNull
    public Metadata copy() {
        Metadata copy = new Metadata();
        copy.setCreated(created);
        copy.setDescription(description);
        copy.setVersion(version);
        return copy;
    }

    @NonNull
    @Override
    public String toString() {
        return "Metadata{" +
                "created='" + created + '\'' +
                ", description='" + description + '\'' +
                ", version=" + version +
                '}';
    }
}
