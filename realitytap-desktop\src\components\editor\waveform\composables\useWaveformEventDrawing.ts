import type { RenderableContinuousEvent, RenderableEvent, RenderableTransientEvent } from "@/types/haptic-editor";
import { type Ref } from "vue";
import { waveformLogger } from "@/utils/logger/logger";
import {
  CONTINUOUS_SELECTED_STROKE_COLOR,
  CONTINUOUS_STROKE_COLOR,
  TRANSIENT_FILL_COLOR,
  TRANSIENT_SELECTED_FILL_COLOR,
  TRANSIENT_SELECTED_STROKE_COLOR,
  TRANSIENT_STROKE_COLOR,
  mapFrequencyToColor,
} from "../utils/color";
import { DEFAULT_LINE_WIDTH, DEFAULT_POINT_RADIUS, SELECTED_LINE_WIDTH, SELECTED_POINT_RADIUS } from "../utils/drawing-helpers";
import { EVENT_WAVEFORM_VERTICAL_OFFSET, DEBUG_EXPERIMENTS } from "../config/waveform-constants";
import { createSmartCacheManager, type OperationType } from "@/utils/performance/SmartCacheManager";
import { globalDataFlowManager } from "@/utils/performance/BidirectionalDataFlowManager";

// 事件绘制配置接口
export interface EventDrawingConfig {
  // 坐标转换函数
  mapTimeToXLocal: (time: number) => number;
  mapIntensityToYLocal: (intensity: number) => number;

  // 画布状态函数
  getGraphAreaWidth: () => number;

  // 事件选择状态检查
  isEventSelected: (event: RenderableEvent) => boolean;

  // 虚拟滚动偏移量
  virtualScrollOffset: Ref<number>;

  // 当前缩放级别
  currentZoomLevel?: Ref<number>;

  // Store状态
  waveformStore: {
    isAdjustingProperties: boolean;
  };
}

// 缓存相关类型
interface ContinuousEventCacheItem {
  points: Array<{ x: number; y: number; color: string }>;
  lastStartTime: number;
  lastStopTime?: number;
  lastDuration?: number;
  lastCurveHash: string;
  lastSelected: boolean;
  lastUsed: number;
  lastVirtualOffset: number;
  lastZoomLevel?: number; // 添加缩放级别缓存
}

export function useWaveformEventDrawing(config: EventDrawingConfig) {
  const { mapTimeToXLocal, mapIntensityToYLocal, getGraphAreaWidth, isEventSelected, virtualScrollOffset, currentZoomLevel, waveformStore } = config;

  // 创建智能缓存管理器
  const smartCache = createSmartCacheManager<ContinuousEventCacheItem>({
    maxSize: 50,
    maxAge: 30000, // 30秒
    cleanupInterval: 10000, // 10秒清理一次
    strategy: 'normal'
  });

  // 保持向后兼容的传统缓存接口
  const continuousEventCache = new Map<string, ContinuousEventCacheItem>();
  const MAX_CACHE_SIZE = 50;

  // 计算曲线点的哈希值，用于检测变化
  const getCurveHash = (curves: any[]): string => {
    return curves.map((p) => `${p.timeOffset},${p.drawIntensity},${p.curveFrequency}`).join("|");
  };

  // 快速哈希计算，用于属性调整时的轻量级检测
  const getFastCurveHash = (curves: any[]): string => {
    return `${curves.length}-${curves[0]?.timeOffset || 0}-${curves[curves.length - 1]?.timeOffset || 0}`;
  };

  // LRU缓存清理
  const cleanupCache = () => {
    if (continuousEventCache.size <= MAX_CACHE_SIZE) return;

    const entries = Array.from(continuousEventCache.entries()).sort((a, b) => a[1].lastUsed - b[1].lastUsed);
    const toDelete = entries.slice(0, entries.length - MAX_CACHE_SIZE);
    toDelete.forEach(([key]) => continuousEventCache.delete(key));
  };

  // 智能缓存失效：只清除特定事件的缓存
  const invalidateEventCache = (eventId: string, operationType?: OperationType) => {
    const currentOpType = operationType || getCurrentOperationType();

    // 使用智能失效策略
    smartInvalidateCache(currentOpType, eventId);

    // 通知数据流管理器缓存失效
    globalDataFlowManager.submitUpdate(
      'canvas',
      'property',
      { eventId, action: 'invalidate', operationType: currentOpType },
      'normal'
    );
  };

  // 清除所有缓存 - 提供给外部缓存管理系统使用
  const clearAllCache = () => {
    continuousEventCache.clear();
    smartCache.clear();
  };

  // 根据当前状态确定操作类型
  const getCurrentOperationType = (): OperationType => {
    if (waveformStore.isAdjustingProperties) {
      return 'property';
    }
    // 这里可以根据更多状态来判断操作类型
    // 例如：是否在拖拽、是否在滚动等
    return 'selection';
  };

  // 智能缓存失效策略
  const smartInvalidateCache = (operationType: OperationType, eventId?: string) => {
    switch (operationType) {
      case 'property':
        // 属性调整时，清除所有相关缓存
        if (eventId) {
          smartCache.delete(eventId);
          continuousEventCache.delete(eventId);
        } else {
          smartCache.clearByOperationType('selection');
          smartCache.clearByOperationType('scroll');
        }
        break;

      case 'drag':
        // 拖拽时，只清除当前事件的缓存
        if (eventId) {
          smartCache.delete(eventId);
          continuousEventCache.delete(eventId);
        }
        break;

      case 'zoom':
        // 缩放时，清除所有缓存
        smartCache.clear();
        continuousEventCache.clear();
        break;

      default:
        // 其他情况使用默认策略
        if (eventId) {
          smartCache.delete(eventId);
          continuousEventCache.delete(eventId);
        }
        break;
    }
  };

  // 注册数据流更新处理器
  globalDataFlowManager.registerHandler('canvas-property', async (update) => {
    try {
      // 处理来自Canvas的属性更新
      smartInvalidateCache('property', update.data.eventId);
      return true;
    } catch (error) {
      waveformLogger.error('处理Canvas属性更新失败:', error);
      return false;
    }
  });

  globalDataFlowManager.registerHandler('panel-property', async (update) => {
    try {
      // 处理来自属性面板的更新
      smartInvalidateCache('property', update.data.eventId);
      return true;
    } catch (error) {
      waveformLogger.error('处理面板属性更新失败:', error);
      return false;
    }
  });

  globalDataFlowManager.registerHandler('canvas-drag', async (update) => {
    try {
      // 处理拖拽更新
      smartInvalidateCache('drag', update.data.eventId);
      return true;
    } catch (error) {
      waveformLogger.error('处理拖拽更新失败:', error);
      return false;
    }
  });

  // 完全重置事件绘制状态（用于组件清理）
  const resetEventDrawingState = () => {
    waveformLogger.debug("完全重置事件绘制状态");

    // 清除所有缓存
    continuousEventCache.clear();

    // 强制垃圾回收
    if (typeof window !== 'undefined' && (window as any).gc) {
      try {
        (window as any).gc();
      } catch (error) {
        // 忽略垃圾回收错误
      }
    }

    waveformLogger.debug("事件绘制状态重置完成");
  };

  // 绘制瞬态事件
  const drawTransientEvent = (ctx: CanvasRenderingContext2D, event: RenderableTransientEvent) => {
    const peakX = mapTimeToXLocal(event.peakTime);
    const peakY = mapIntensityToYLocal(event.intensity);
    const startX = mapTimeToXLocal(event.startTime);
    const endX = mapTimeToXLocal(event.stopTime);
    // 添加垂直偏移，确保X轴可见
    const baseY = mapIntensityToYLocal(0) - EVENT_WAVEFORM_VERTICAL_OFFSET;

    const actualAreaWidth = getGraphAreaWidth();
    const tolerance = 50;

    if (endX < -tolerance || startX > actualAreaWidth + tolerance) return;

    const clampedStartX = Math.max(-tolerance, Math.min(startX, actualAreaWidth + tolerance));
    const clampedEndX = Math.max(-tolerance, Math.min(endX, actualAreaWidth + tolerance));
    const clampedPeakX = Math.max(-tolerance, Math.min(peakX, actualAreaWidth + tolerance));

    if (clampedEndX < -tolerance || clampedStartX > actualAreaWidth + tolerance) return;

    const isSelected = isEventSelected(event);
    const strokeColor = isSelected ? TRANSIENT_SELECTED_STROKE_COLOR : TRANSIENT_STROKE_COLOR;
    const fillColor = isSelected ? TRANSIENT_SELECTED_FILL_COLOR : TRANSIENT_FILL_COLOR;
    const lineWidth = isSelected ? SELECTED_LINE_WIDTH : DEFAULT_LINE_WIDTH;
    const pointRadius = isSelected ? SELECTED_POINT_RADIUS : DEFAULT_POINT_RADIUS;

    // 绘制填充
    ctx.beginPath();
    ctx.moveTo(clampedStartX, baseY);
    ctx.lineTo(clampedPeakX, peakY);
    ctx.lineTo(clampedEndX, baseY);
    ctx.fillStyle = fillColor;
    ctx.fill();

    // 绘制边框
    ctx.beginPath();
    ctx.moveTo(clampedStartX, baseY);
    ctx.lineTo(clampedPeakX, peakY);
    ctx.lineTo(clampedEndX, baseY);
    ctx.strokeStyle = strokeColor;
    ctx.lineWidth = lineWidth;
    ctx.stroke();

    // 在峰值处添加高亮点
    ctx.beginPath();
    ctx.arc(clampedPeakX, peakY, pointRadius, 0, Math.PI * 2);
    ctx.fillStyle = strokeColor;
    ctx.fill();
  };

  // 绘制连续事件
  const drawContinuousEvent = (ctx: CanvasRenderingContext2D, event: RenderableContinuousEvent) => {
    if (event.curves.length < 2) return; // 至少需要2个点才能形成线段

    const isSelected = isEventSelected(event);
    // 添加垂直偏移，确保X轴可见
    const baselineY = mapIntensityToYLocal(0) - EVENT_WAVEFORM_VERTICAL_OFFSET;
    const currentPointRadius = isSelected ? SELECTED_POINT_RADIUS : DEFAULT_POINT_RADIUS;
    const currentLineWidth = isSelected ? SELECTED_LINE_WIDTH : DEFAULT_LINE_WIDTH;
    const baseStrokeColor = isSelected ? CONTINUOUS_SELECTED_STROKE_COLOR : CONTINUOUS_STROKE_COLOR;

    const curveCount = event.curves.length;
    let points: Array<{ x: number; y: number; color: string }>;

    // 实验3: 事件绘制缓存配置
    if (DEBUG_EXPERIMENTS.DISABLE_EVENT_DRAW_CACHE) {
      waveformLogger.debug("🔍 实验3: 禁用事件绘制缓存");

      // 强制重新计算坐标，不使用缓存
      points = new Array(curveCount);

      // 单次遍历：计算坐标、颜色
      for (let i = 0; i < curveCount; i++) {
        const p = event.curves[i];
        const x = mapTimeToXLocal(event.startTime + p.timeOffset);
        const y = mapIntensityToYLocal(p.drawIntensity);
        const color = mapFrequencyToColor(p.curveFrequency, isSelected);

        points[i] = { x, y, color };
      }
    } else {
      // 使用智能缓存管理器
      const operationType = getCurrentOperationType();
      const isAdjusting = waveformStore.isAdjustingProperties;
      const curveHash = isAdjusting ? getFastCurveHash(event.curves) : getCurveHash(event.curves);
      const cacheKey = event.id;

      // 尝试从智能缓存获取
      const cached = smartCache.get(cacheKey, operationType);
      const now = performance.now();

      // 增加虚拟偏移量的容差检查，避免微小变化导致缓存频繁失效
      // 属性调整时使用更大的容差，减少缓存失效
      const offsetTolerance = isAdjusting ? 5.0 : 1.0; // 属性调整时使用5像素容差
      const offsetMatches = cached ? Math.abs(cached.lastVirtualOffset - virtualScrollOffset.value) <= offsetTolerance : false;

      // 检查缩放级别是否匹配
      const currentZoom = currentZoomLevel?.value || 1.0;
      const zoomMatches = cached ? (cached.lastZoomLevel === currentZoom) : false;

      if (cached &&
          cached.lastStartTime === event.startTime &&
          cached.lastStopTime === event.stopTime &&
          cached.lastDuration === event.duration &&
          cached.lastCurveHash === curveHash &&
          cached.lastSelected === isSelected &&
          offsetMatches &&
          zoomMatches) {
        // 使用缓存的坐标
        points = cached.points;
        // 更新虚拟偏移量（智能缓存会自动更新访问时间）
        cached.lastVirtualOffset = virtualScrollOffset.value;
      } else {
        // 重新计算坐标并缓存
        points = new Array(curveCount);

        // 单次遍历：计算坐标、颜色
        for (let i = 0; i < curveCount; i++) {
          const p = event.curves[i];
          const x = mapTimeToXLocal(event.startTime + p.timeOffset);
          const y = mapIntensityToYLocal(p.drawIntensity);
          const color = mapFrequencyToColor(p.curveFrequency, isSelected);

          points[i] = { x, y, color };
        }

        // 创建缓存项
        const cacheItem: ContinuousEventCacheItem = {
          points: points,
          lastStartTime: event.startTime,
          lastStopTime: event.stopTime,
          lastDuration: event.duration,
          lastCurveHash: curveHash,
          lastSelected: isSelected,
          lastUsed: now,
          lastVirtualOffset: virtualScrollOffset.value,
          lastZoomLevel: currentZoom, // 保存当前缩放级别
        };

        // 使用智能缓存存储（会根据操作类型自动选择策略）
        const estimatedSize = points.length * 3; // 估算缓存项大小
        smartCache.set(cacheKey, cacheItem, operationType, estimatedSize);

        // 同时更新传统缓存以保持兼容性
        continuousEventCache.set(cacheKey, cacheItem);

        // 通知数据流管理器缓存更新
        globalDataFlowManager.submitUpdate(
          'canvas',
          'property',
          { eventId: event.id, cacheKey, operationType },
          'low' // 缓存更新是低优先级
        );

        // 定期清理传统缓存
        if (continuousEventCache.size > MAX_CACHE_SIZE) {
          cleanupCache();
        }
      }
    }

    // 绘制填充区域（如果有足够的点）
    if (curveCount >= 2) {
      // 创建渐变
      const gradient = ctx.createLinearGradient(points[0].x, 0, points[curveCount - 1].x, 0);

      // 添加渐变色标（使用预计算的颜色）
      for (let i = 0; i < curveCount; i++) {
        const stopPosition = i / (curveCount - 1);
        gradient.addColorStop(stopPosition, points[i].color);
      }

      // 绘制填充路径
      ctx.beginPath();
      ctx.moveTo(points[0].x, points[0].y);

      // 绘制上部曲线
      for (let i = 1; i < curveCount; i++) {
        ctx.lineTo(points[i].x, points[i].y);
      }

      // 连接到基线形成闭合区域
      ctx.lineTo(points[curveCount - 1].x, baselineY);
      ctx.lineTo(points[0].x, baselineY);
      ctx.closePath();

      ctx.fillStyle = gradient;
      ctx.fill();
    }

    // 设置描边样式（一次性设置）
    ctx.strokeStyle = baseStrokeColor;
    ctx.lineWidth = currentLineWidth;
    ctx.fillStyle = baseStrokeColor;

    // 绘制曲线描边
    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < curveCount; i++) {
      ctx.lineTo(points[i].x, points[i].y);
    }
    ctx.stroke();

    // 批量绘制关键点标记（减少beginPath调用）
    for (let i = 0; i < curveCount; i++) {
      ctx.beginPath();
      ctx.arc(points[i].x, points[i].y, currentPointRadius, 0, Math.PI * 2);
      ctx.fill();
    }
  };

  // 简化的瞬态事件绘制函数 - 用于属性调整时的快速绘制
  const drawTransientEventSimplified = (ctx: CanvasRenderingContext2D, event: RenderableTransientEvent) => {
    const peakX = mapTimeToXLocal(event.peakTime);
    const peakY = mapIntensityToYLocal(event.intensity);
    const startX = mapTimeToXLocal(event.startTime);
    const endX = mapTimeToXLocal(event.stopTime);
    // 添加垂直偏移，确保X轴可见
    const baseY = mapIntensityToYLocal(0) - EVENT_WAVEFORM_VERTICAL_OFFSET;

    // 简化的可见性检查
    const actualAreaWidth = getGraphAreaWidth();
    if (endX < -50 || startX > actualAreaWidth + 50) return;

    // 使用固定颜色，避免复杂的颜色计算
    ctx.strokeStyle = "#4a9eff";
    ctx.fillStyle = "#4a9eff33";
    ctx.lineWidth = 1;

    // 简化绘制：只绘制轮廓，不绘制点
    ctx.beginPath();
    ctx.moveTo(startX, baseY);
    ctx.lineTo(peakX, peakY);
    ctx.lineTo(endX, baseY);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
  };

  // 简化的连续事件绘制函数 - 用于属性调整时的快速绘制
  const drawContinuousEventSimplified = (ctx: CanvasRenderingContext2D, event: RenderableContinuousEvent) => {
    const curveCount = event.curves.length;
    if (curveCount < 2) return;

    // 简化的可见性检查
    const startX = mapTimeToXLocal(event.startTime);
    const endX = mapTimeToXLocal(event.stopTime);
    const actualAreaWidth = getGraphAreaWidth();
    if (endX < -50 || startX > actualAreaWidth + 50) return;

    // 使用固定颜色，避免复杂的颜色计算
    ctx.strokeStyle = "#36ad6a";
    ctx.lineWidth = 1;

    // 简化绘制：只绘制曲线，不绘制点，不使用缓存
    ctx.beginPath();
    for (let i = 0; i < curveCount; i++) {
      const p = event.curves[i];
      const x = mapTimeToXLocal(event.startTime + p.timeOffset);
      const y = mapIntensityToYLocal(p.drawIntensity);

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();
  };

  return {
    drawTransientEvent,
    drawContinuousEvent,
    drawTransientEventSimplified,
    drawContinuousEventSimplified,
    invalidateEventCache,
    continuousEventCache,
    getCurveHash,
    getFastCurveHash,
    cleanupCache,
    clearAllCache,
    resetEventDrawingState,

    // 新增智能缓存接口
    getSmartCacheStats: () => smartCache.getStats(),
    clearSmartCache: () => smartCache.clear(),
    updateCacheConfig: (config: any) => smartCache.updateConfig(config),

    // 新增数据流管理接口
    getDataFlowStats: () => globalDataFlowManager.getMetrics(),
    getDataFlowQueueStatus: () => globalDataFlowManager.getQueueStatus(),
    smartInvalidateCache,

    // 增强的缓存管理
    invalidateByOperationType: (operationType: OperationType) => {
      smartCache.clearByOperationType(operationType);
      globalDataFlowManager.submitUpdate(
        'canvas',
        'property',
        { action: 'bulk-invalidate', operationType },
        'normal'
      );
    },
  };
}
