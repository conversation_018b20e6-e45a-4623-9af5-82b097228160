export interface AppVersionInfo {
  appName: string;
  appVersion: string;
  backendVersion: string;
  buildDate: string;
  buildMode: string;
  targetArch: string;
  targetOs: string;
}

// OTA 专用的版本信息
export interface OTAVersionInfo extends AppVersionInfo {
  releaseChannel: 'stable' | 'beta' | 'alpha';
  minimumVersion: string;
  deprecatedVersions: string[];
}

export interface VersionComparison {
  current: string;
  latest: string;
  hasUpdate: boolean;
  updateType: 'major' | 'minor' | 'patch';
}