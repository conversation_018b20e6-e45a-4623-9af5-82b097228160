import semver from 'semver';

/**
 * 版本比较工具类
 */
export class VersionUtil {
  /**
   * 比较两个版本号
   * @param current 当前版本
   * @param latest 最新版本
   * @returns 比较结果
   */
  static compare(current: string, latest: string): number {
    try {
      return semver.compare(current, latest);
    } catch (error) {
      throw new Error(`Invalid version format: ${error}`);
    }
  }

  /**
   * 检查是否有更新
   * @param current 当前版本
   * @param latest 最新版本
   * @returns 是否有更新
   */
  static hasUpdate(current: string, latest: string): boolean {
    try {
      return semver.gt(latest, current);
    } catch (error) {
      throw new Error(`Invalid version format: ${error}`);
    }
  }

  /**
   * 获取更新类型
   * @param current 当前版本
   * @param latest 最新版本
   * @returns 更新类型
   */
  static getUpdateType(current: string, latest: string): 'major' | 'minor' | 'patch' | 'none' {
    try {
      if (!this.hasUpdate(current, latest)) {
        return 'none';
      }

      const currentParsed = semver.parse(current);
      const latestParsed = semver.parse(latest);

      if (!currentParsed || !latestParsed) {
        throw new Error('Failed to parse version');
      }

      if (latestParsed.major > currentParsed.major) {
        return 'major';
      } else if (latestParsed.minor > currentParsed.minor) {
        return 'minor';
      } else {
        return 'patch';
      }
    } catch (error) {
      throw new Error(`Invalid version format: ${error}`);
    }
  }

  /**
   * 验证版本格式
   * @param version 版本号
   * @returns 是否有效
   */
  static isValid(version: string): boolean {
    return semver.valid(version) !== null;
  }

  /**
   * 清理版本号（移除前缀 v）
   * @param version 版本号
   * @returns 清理后的版本号
   */
  static clean(version: string): string {
    try {
      const cleaned = semver.clean(version);
      if (!cleaned) {
        throw new Error('Invalid version format');
      }
      return cleaned;
    } catch (error) {
      throw new Error(`Invalid version format: ${error}`);
    }
  }

  /**
   * 检查版本是否满足最小版本要求
   * @param version 当前版本
   * @param minimumVersion 最小版本要求
   * @returns 是否满足要求
   */
  static satisfiesMinimum(version: string, minimumVersion: string): boolean {
    try {
      return semver.gte(version, minimumVersion);
    } catch (error) {
      throw new Error(`Invalid version format: ${error}`);
    }
  }

  /**
   * 检查版本是否已弃用
   * @param version 版本号
   * @param deprecatedVersions 已弃用版本列表
   * @returns 是否已弃用
   */
  static isDeprecated(version: string, deprecatedVersions: string[]): boolean {
    return deprecatedVersions.includes(version);
  }

  /**
   * 获取版本的预发布标识
   * @param version 版本号
   * @returns 预发布标识
   */
  static getPrerelease(version: string): string | null {
    try {
      const prerelease = semver.prerelease(version);
      return prerelease ? prerelease.join('.') : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 检查版本是否为预发布版本
   * @param version 版本号
   * @returns 是否为预发布版本
   */
  static isPrerelease(version: string): boolean {
    try {
      return semver.prerelease(version) !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * 排序版本列表
   * @param versions 版本列表
   * @param ascending 是否升序排列
   * @returns 排序后的版本列表
   */
  static sort(versions: string[], ascending: boolean = true): string[] {
    try {
      return versions.sort((a, b) => {
        const result = semver.compare(a, b);
        return ascending ? result : -result;
      });
    } catch (error) {
      throw new Error(`Failed to sort versions: ${error}`);
    }
  }

  /**
   * 获取最新版本
   * @param versions 版本列表
   * @returns 最新版本
   */
  static getLatest(versions: string[]): string | null {
    if (versions.length === 0) {
      return null;
    }

    try {
      const sorted = this.sort(versions, false);
      return sorted[0] || null;
    } catch (error) {
      return null;
    }
  }
}
