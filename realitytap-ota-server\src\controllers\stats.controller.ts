import { authMiddleware } from '@/middleware/auth.middleware';
import { getStatsService } from '@/services/service-factory';
import { DownloadStatsQuery, ErrorResponse, SuccessResponse } from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import { StatsUtil } from '@/utils/stats.util';
import { NextFunction, Response, Router } from 'express';
import { z } from 'zod';

const router: Router = Router();

// 获取统计服务实例
const statsService = getStatsService();

// 验证模式
const StatsQuerySchema = z.object({
  startDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .optional(),
  endDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .optional(),
  version: z.string().optional(),
  platform: z.enum(['windows', 'macos', 'linux']).optional(),
  channel: z.enum(['stable', 'beta', 'alpha']).optional(),
  limit: z.coerce.number().min(1).max(1000).optional(),
  offset: z.coerce.number().min(0).optional(),
});

/**
 * 获取下载统计摘要
 * GET /api/v1/stats/downloads/summary
 */
router.get('/downloads/summary', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    logger.info('Getting download stats summary', {
      userId: req.user?.username,
      ip: req.ip,
    });

    const stats = await statsService.getSummaryStats();

    const response: SuccessResponse = {
      success: true,
      data: {
        ...stats,
        formattedTotalBytes: StatsUtil.formatFileSize(stats.totalBytes),
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get download stats summary', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    next(error);
  }
});

/**
 * 获取下载统计数据
 * GET /api/v1/stats/downloads
 */
router.get('/downloads', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    // 验证查询参数
    const queryValidation = StatsQuerySchema.safeParse(req.query);
    if (!queryValidation.success) {
      const errorResponse: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_QUERY_PARAMETERS',
          message: 'Invalid query parameters',
          details: queryValidation.error.errors,
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      return res.status(400).json(errorResponse);
    }

    const query: DownloadStatsQuery = queryValidation.data;

    logger.info('Getting download stats', {
      query,
      userId: req.user?.username,
      ip: req.ip,
    });

    const stats = await statsService.getDownloadStats(query);

    const response: SuccessResponse = {
      success: true,
      data: {
        ...stats,
        formattedTotalBytes: StatsUtil.formatFileSize(stats.totalBytes),
        query,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get download stats', {
      error: error.message,
      query: req.query,
      userId: req.user?.username,
      ip: req.ip,
    });

    return next(error);
  }
});

/**
 * 获取下载记录
 * GET /api/v1/stats/downloads/records
 */
router.get('/downloads/records', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    // 验证查询参数
    const queryValidation = StatsQuerySchema.safeParse(req.query);
    if (!queryValidation.success) {
      const errorResponse: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_QUERY_PARAMETERS',
          message: 'Invalid query parameters',
          details: queryValidation.error.errors,
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      return res.status(400).json(errorResponse);
    }

    const query: DownloadStatsQuery = queryValidation.data;

    logger.info('Getting download records', {
      query,
      userId: req.user?.username,
      ip: req.ip,
    });

    const records = await statsService.getDownloadRecords(query);

    const response: SuccessResponse = {
      success: true,
      data: {
        records,
        total: records.length,
        query,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get download records', {
      error: error.message,
      query: req.query,
      userId: req.user?.username,
      ip: req.ip,
    });

    return next(error);
  }
});

/**
 * 清理旧的统计数据
 * DELETE /api/v1/stats/downloads/cleanup
 */
router.delete('/downloads/cleanup', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    const { daysToKeep = 90 } = req.body;

    // 验证参数
    if (typeof daysToKeep !== 'number' || daysToKeep < 1 || daysToKeep > 365) {
      const errorResponse: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'daysToKeep must be a number between 1 and 365',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      return res.status(400).json(errorResponse);
    }

    logger.info('Cleaning up old stats', {
      daysToKeep,
      userId: req.user?.username,
      ip: req.ip,
    });

    await statsService.cleanupOldStats(daysToKeep);

    const response: SuccessResponse = {
      success: true,
      data: {
        message: 'Old stats cleaned up successfully',
        daysToKeep,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to cleanup old stats', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    return next(error);
  }
});

/**
 * 获取统计数据概览
 * GET /api/v1/stats/overview
 */
router.get('/overview', authMiddleware, async (req: any, res: Response, next: NextFunction) => {
  try {
    logger.info('Getting stats overview', {
      userId: req.user?.username,
      ip: req.ip,
    });

    const summary = await statsService.getSummaryStats();

    // 计算一些有用的概览数据
    const overview = {
      totalDownloads: summary.totalDownloads,
      totalBytes: summary.totalBytes,
      formattedTotalBytes: StatsUtil.formatFileSize(summary.totalBytes),
      versionsCount: Object.keys(summary.byVersion).length,
      platformsCount: Object.keys(summary.byPlatform).length,
      channelsCount: Object.keys(summary.byChannel).length,
      topVersion: getTopItem(summary.byVersion),
      topPlatform: getTopItem(summary.byPlatform),
      topChannel: getTopItem(summary.byChannel),
      recentDownloadsCount: summary.recentDownloads.length,
      lastDownloadTime: summary.recentDownloads[0]?.downloadTime || null,
    };

    const response: SuccessResponse = {
      success: true,
      data: overview,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error: any) {
    logger.error('Failed to get stats overview', {
      error: error.message,
      userId: req.user?.username,
      ip: req.ip,
    });

    next(error);
  }
});

/**
 * 获取最高统计项
 * @param stats 统计对象
 * @returns 最高统计项
 */
function getTopItem(stats: Record<string, number>): { name: string; count: number } | null {
  const entries = Object.entries(stats);
  if (entries.length === 0) {
    return null;
  }

  const [name, count] = entries.reduce((max, current) => (current[1] > max[1] ? current : max));

  return { name, count };
}

export { router as statsController };
