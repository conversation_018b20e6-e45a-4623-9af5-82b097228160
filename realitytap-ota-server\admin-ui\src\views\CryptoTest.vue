<template>
  <div class="crypto-test">
    <n-page-header title="加密功能测试" subtitle="测试Web Crypto API兼容性">
      <template #extra>
        <n-button @click="runTests">运行测试</n-button>
      </template>
    </n-page-header>

    <n-card title="环境信息">
      <n-descriptions :column="2" size="small">
        <n-descriptions-item label="安全上下文">
          <n-tag :type="envInfo.isSecureContext ? 'success' : 'warning'">
            {{ envInfo.isSecureContext ? '是' : '否' }}
          </n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="协议">{{ envInfo.protocol }}</n-descriptions-item>
        <n-descriptions-item label="主机名">{{ envInfo.hostname }}</n-descriptions-item>
        <n-descriptions-item label="Web Crypto 支持">
          <n-tag :type="envInfo.webCryptoSupported ? 'success' : 'warning'">
            {{ envInfo.webCryptoSupported ? '支持' : '不支持' }}
          </n-tag>
        </n-descriptions-item>
      </n-descriptions>
    </n-card>

    <n-card title="文件哈希测试" style="margin-top: 16px;">
      <n-space vertical>
        <n-upload
          :custom-request="() => {}"
          :show-file-list="false"
          @before-upload="handleFileSelect"
        >
          <n-button>选择测试文件</n-button>
        </n-upload>

        <div v-if="selectedFile">
          <n-text>选中文件: {{ selectedFile.name }} ({{ formatBytes(selectedFile.size) }})</n-text>
        </div>

        <n-button 
          v-if="selectedFile" 
          type="primary" 
          :loading="testing"
          @click="testFileHash"
        >
          测试文件哈希计算
        </n-button>

        <div v-if="testResults.length > 0">
          <n-divider>测试结果</n-divider>
          <n-timeline>
            <n-timeline-item
              v-for="(result, index) in testResults"
              :key="index"
              :type="result.success ? 'success' : 'error'"
              :title="result.title"
            >
              <template #icon>
                <n-icon>
                  <CheckmarkCircleOutline v-if="result.success" />
                  <CloseCircleOutline v-else />
                </n-icon>
              </template>
              <div>
                <p>{{ result.message }}</p>
                <n-text v-if="result.hash" depth="3" style="font-family: monospace;">
                  哈希值: {{ result.hash }}
                </n-text>
                <n-text v-if="result.error" type="error">
                  错误: {{ result.error }}
                </n-text>
              </div>
            </n-timeline-item>
          </n-timeline>
        </div>
      </n-space>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { 
  calculateFileHash, 
  getEnvironmentInfo, 
  validateEnvironment,
  isWebCryptoSupported 
} from '@/utils/crypto';
import { CheckmarkCircleOutline, CloseCircleOutline } from '@vicons/ionicons5';
import {
  NButton,
  NCard,
  NDescriptions,
  NDescriptionsItem,
  NDivider,
  NIcon,
  NPageHeader,
  NSpace,
  NTag,
  NText,
  NTimeline,
  NTimelineItem,
  NUpload,
  type UploadFileInfo,
} from 'naive-ui';

interface TestResult {
  title: string;
  message: string;
  success: boolean;
  hash?: string;
  error?: string;
}

const selectedFile = ref<File | null>(null);
const testing = ref(false);
const testResults = ref<TestResult[]>([]);
const envInfo = ref(getEnvironmentInfo());

onMounted(() => {
  validateEnvironment();
});

const runTests = () => {
  testResults.value = [];
  
  // 测试环境检测
  const env = getEnvironmentInfo();
  testResults.value.push({
    title: '环境检测',
    message: `协议: ${env.protocol}, 安全上下文: ${env.isSecureContext}, Web Crypto: ${env.webCryptoSupported}`,
    success: true
  });

  // 测试Web Crypto API可用性
  try {
    const supported = isWebCryptoSupported();
    testResults.value.push({
      title: 'Web Crypto API 检测',
      message: supported ? 'Web Crypto API 可用' : 'Web Crypto API 不可用，将使用备用方案',
      success: true
    });
  } catch (error: any) {
    testResults.value.push({
      title: 'Web Crypto API 检测',
      message: 'Web Crypto API 检测失败',
      success: false,
      error: error.message
    });
  }
};

const handleFileSelect = (data: { file: UploadFileInfo }): boolean => {
  const file = data.file.file;
  if (file) {
    selectedFile.value = file;
  }
  return false; // 阻止自动上传
};

const testFileHash = async () => {
  if (!selectedFile.value) return;

  testing.value = true;
  testResults.value = [];

  try {
    // 测试文件哈希计算
    const startTime = Date.now();
    const hash = await calculateFileHash(selectedFile.value);
    const endTime = Date.now();
    
    testResults.value.push({
      title: '文件哈希计算',
      message: `计算成功，耗时: ${endTime - startTime}ms`,
      success: true,
      hash: hash
    });

    // 再次计算验证一致性
    const hash2 = await calculateFileHash(selectedFile.value);
    testResults.value.push({
      title: '哈希一致性验证',
      message: hash === hash2 ? '两次计算结果一致' : '两次计算结果不一致',
      success: hash === hash2,
      hash: hash2
    });

  } catch (error: any) {
    testResults.value.push({
      title: '文件哈希计算',
      message: '计算失败',
      success: false,
      error: error.message
    });
  } finally {
    testing.value = false;
  }
};

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<style scoped>
.crypto-test {
  padding: 16px;
}
</style>
