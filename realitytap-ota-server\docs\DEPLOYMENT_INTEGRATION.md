# RealityTap OTA Server - 部署集成指南

## 📋 概述

本文档描述了 RealityTap OTA 服务器的完整部署集成流程，包括前端管理界面的构建和部署。

## 🏗️ 架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  Node.js App    │    │   Admin UI      │
│   (Optional)    │───▶│   (Backend)     │───▶│  (Vue3 SPA)    │
│   Port 80/443   │    │   Port 3000     │    │  /admin/*       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速部署

### 方法一：使用部署脚本（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd realitytap-ota-server

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，修改管理员密码等配置

# 执行一键部署
./scripts/deploy.sh
```

### 方法二：手动部署

```bash
# 1. 安装依赖
npm ci

# 2. 构建完整应用（包含前端）
npm run build:full

# 3. 启动服务
npm run start:prod
```

### 方法三：Docker 部署

```bash
# 1. 构建镜像
docker build -t realitytap-ota-server:latest .

# 2. 使用 Docker Compose 启动
docker-compose up -d

# 3. 检查服务状态
docker-compose ps
```

## 📁 文件结构

部署后的文件结构：

```
dist/
├── app.js                 # 主应用文件
├── config/               # 配置文件
├── controllers/          # 控制器
├── middleware/           # 中间件
├── services/            # 服务层
├── utils/               # 工具函数
├── admin-ui/            # 管理界面静态文件
│   ├── index.html       # 主页面
│   ├── assets/          # 静态资源
│   └── ...
└── storage/             # 存储目录
    ├── releases/        # 版本文件
    ├── metadata/        # 元数据
    └── logs/           # 日志文件
```

## 🔧 配置说明

### 环境变量

关键环境变量配置：

```bash
# 管理员配置（生产环境必须修改）
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
JWT_SECRET=your-super-secret-jwt-key-min-32-chars

# 服务器配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 存储配置
STORAGE_PATH=./storage
```

### 静态文件服务

应用会自动根据环境配置静态文件路径：

- **开发环境**: `admin-ui/dist/`
- **生产环境**: `dist/admin-ui/`

## 🐳 Docker 部署详解

### Dockerfile 特性

- **多阶段构建**: 优化镜像大小
- **前端构建**: 自动构建 Vue3 管理界面
- **安全配置**: 非 root 用户运行
- **健康检查**: 内置健康检查机制

### Docker Compose 配置

```yaml
services:
  realitytap-ota-server:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
    volumes:
      - ./storage:/app/storage
```

### 使用 Nginx 反向代理

启用 Nginx 代理（可选）：

```bash
# 启动包含 Nginx 的完整服务
docker-compose --profile production up -d
```

## 🔍 验证部署

### 健康检查

```bash
# 基础健康检查
curl http://localhost:3000/health

# 详细健康检查
curl http://localhost:3000/health/detailed
```

### 访问管理界面

1. 打开浏览器访问: `http://localhost:3000/admin`
2. 使用配置的管理员账号登录
3. 验证各项功能正常

### API 测试

```bash
# 测试版本检查 API
curl -X POST http://localhost:3000/api/v1/version/check \
  -H "Content-Type: application/json" \
  -d '{
    "currentVersion": "1.0.0",
    "platform": "windows",
    "architecture": "x86_64",
    "channel": "stable"
  }'
```

## 🛠️ 故障排查

### 常见问题

1. **管理界面无法访问**
   ```bash
   # 检查静态文件是否存在
   ls -la dist/admin-ui/
   
   # 重新构建管理界面
   npm run build-admin-ui
   ```

2. **Docker 构建失败**
   ```bash
   # 清理 Docker 缓存
   docker system prune -a
   
   # 重新构建
   docker build --no-cache -t realitytap-ota-server:latest .
   ```

3. **权限问题**
   ```bash
   # 检查存储目录权限
   ls -la storage/
   
   # 修复权限
   chmod -R 755 storage/
   ```

### 日志查看

```bash
# 应用日志
tail -f storage/logs/app.log

# Docker 日志
docker-compose logs -f realitytap-ota-server

# Nginx 日志（如果使用）
docker-compose logs -f nginx
```

## 🔒 安全建议

1. **修改默认密码**: 生产环境必须修改管理员密码
2. **使用 HTTPS**: 配置 SSL 证书
3. **防火墙配置**: 限制不必要的端口访问
4. **定期更新**: 保持依赖包最新
5. **备份策略**: 定期备份存储数据

## 📊 监控和维护

### 性能监控

- 使用 Docker 健康检查
- 监控磁盘空间使用
- 检查内存和 CPU 使用率

### 日志轮转

日志文件会自动轮转，配置在 Winston 中：

- 最大文件大小: 20MB
- 保留天数: 14 天
- 压缩旧日志: 是

## 🔄 更新部署

```bash
# 1. 停止服务
docker-compose down

# 2. 拉取最新代码
git pull

# 3. 重新构建和部署
./scripts/deploy.sh
```

## 📞 支持

如遇到部署问题，请：

1. 检查日志文件
2. 验证环境变量配置
3. 确认端口未被占用
4. 查看 Docker 容器状态
