# RealityTap 触觉反馈数据架构

## 概述

RealityTap是一种触觉反馈系统，通过定义特定的JSON结构来描述各种触觉效果。随着系统的发展，数据结构经历了多次迭代，目前主要存在三种格式：
- HE格式：最早期格式，包含基本触觉参数
- JSON V1：第一代标准格式，定义了简单的Pattern结构
- JSON V2：第二代增强格式，引入PatternList和多马达支持

## 数据结构演变

### 基础元数据

所有格式都包含以下基础元数据字段：
| 字段名 | 类型 | 描述 |
|--------|------|------|
| Version | 整型 | 数据格式版本号 |
| Created | 字符串 | 创建时间，格式为"YYYY-MM-DD" |
| Description | 字符串 | 触觉效果的文本描述 |

### 触觉事件类型

所有格式支持两种基本的触觉事件类型：
1. **瞬时事件(transient)**：简短、即时的触觉反馈
   - 特点：时长短，由振动频率决定(通常10-25ms)
   - 用途：按钮点击、碰撞等即时反馈

2. **包络事件(continuous)**：持续、可变的触觉反馈
   - 特点：可定义持续时间和变化曲线
   - 用途：持续性触觉效果，如爆炸、引擎震动等

## 格式详解

### HE格式 (.he文件)

最基础的格式，主要结构包括：
```json
{
  "Metadata": {
    "Version": 1,
    "Created": "2020-07-08",
    "Description": "game haptic"
  },
  "Pattern": [
    {
      "Event": {
        // 事件定义
      }
    }
  ]
}
```

### JSON V1格式

第一代正式格式，扩展了基础结构：
```json
{
  "Metadata": {
    "Created": "2020-08-10",
    "Description": "Haptic editor design",
    "Version": 1
  },
  "Pattern": [
    {
      "Event": {
        // 事件定义
      }
    }
  ]
}
```

### JSON V2格式

第二代增强格式，引入PatternList结构和马达索引：
```json
{
  "Metadata": {
    "Created": "2021-02-22",
    "Description": "RealityTap Haptics Content",
    "Version": 2
  },
  "PatternList": [
    {
      "AbsoluteTime": 0,
      "Pattern": [
        {
          "Event": {
            // 事件定义，增加Index字段
          }
        }
      ]
    }
  ]
}
```

## 版本差异对比

| 特性 | HE格式 | JSON V1 | JSON V2 |
|------|--------|---------|---------|
| 基础元数据 | ✓ | ✓ | ✓ |
| 瞬时事件 | ✓ | ✓ | ✓ |
| 包络事件 | ✓ | ✓ | ✓ |
| 注释支持 | ✓ | ✗ | ✗ |
| 多马达支持 | ✗ | ✗ | ✓ (Index) |
| 多模式序列 | ✗ | ✗ | ✓ (PatternList) |
| 绝对时间支持 | ✗ | ✗ | ✓ (AbsoluteTime) |

## 事件参数详解

### 瞬时事件(transient)参数

| 参数 | 类型 | 范围 | 描述 |
|------|------|------|------|
| RelativeTime | 整型 | ≥0 | 相对开始时间(毫秒) |
| Type | 字符串 | "transient" | 事件类型标识符 |
| Index | 整型 | [0,1] | 马达索引(仅V2) |
| Frequency | 整型 | [0,100] | 振动频率 |
| Intensity | 整型 | [0,100] | 振动强度 |

### 包络事件(continuous)参数

| 参数 | 类型 | 范围 | 描述 |
|------|------|------|------|
| RelativeTime | 整型 | ≥0 | 相对开始时间(毫秒) |
| Type | 字符串 | "continuous" | 事件类型标识符 |
| Index | 整型 | [0,1] | 马达索引(仅V2) |
| Duration | 整型 | >0 | 持续时间(毫秒) |
| Frequency | 整型 | [0,100] | 全局振动频率 |
| Intensity | 整型 | [0,100] | 全局振动强度 |
| Curve | 数组 | - | 振动曲线关键点 |

### 曲线关键点(Curve Point)参数

| 参数 | 类型 | 范围 | 描述 |
|------|------|------|------|
| Time | 整型 | [0,Duration] | 相对时间点(毫秒) |
| Intensity | 浮点型 | [0,1.0] | 该时间点振动强度 |
| Frequency | 整型 | [-100,100] | 该时间点振动频率变化 |

## 重要规则与约束

1. **时间约束**：
   - 相邻事件不得有时间重合
   - 所有事件必须按RelativeTime排序

2. **曲线约束**：
   - 曲线必须包含4个关键点
   - 第一个点必须是Time=0, Intensity=0的起始点
   - 最后一个点必须是Time=Duration, Intensity=0的结束点
   - 所有点的Time必须严格递增(不得重合)

3. **实际效果计算**：
   - 实际振动强度 = 关键点强度(0-1.0) × 全局振动强度(0-100)
   - 实际振动频率 = 关键点频率偏移值(-100~100) + 全局振动频率(0-100)

4. **V2特有规则**：
   - PatternList可包含多个具有不同AbsoluteTime的Pattern
   - Index参数用于指定触发不同马达

## 最佳实践

1. 瞬时事件适合模拟清脆、短促的触觉反馈，如按键、点击
2. 包络事件适合模拟渐变、持续的触觉反馈，如爆炸、引擎震动
3. 合理设计Curve曲线以获得平滑的触觉体验
4. V2格式中，充分利用多马达能力创造方向性触觉反馈
5. 合理安排事件时序，避免事件过密或重叠
