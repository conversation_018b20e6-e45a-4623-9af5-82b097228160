package android.os.vibrator.realitytap.parser;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/** @hide */
class JsonParserFactory {
    private static final Map<Integer, Class<? extends AbstractJsonParser>> parserClz = new HashMap<>();
    private static final Map<Integer, AbstractJsonParser> parsers = new ConcurrentHashMap<>();

    static {
        parserClz.put(1, JsonParserV1.class);
        parserClz.put(2, JsonParserV2.class);
        parserClz.put(3, JsonParserV3.class);
    }

    public static AbstractJsonParser getParser(int version) {
        AbstractJsonParser parser = parsers.get(version);
        if (parser != null) {
            return parser;
        }

        synchronized (JsonParserFactory.class) {
            parser = parsers.get(version);
            if (parser != null) {
                return parser;
            }

            // 创建新实例
            Class<? extends AbstractJsonParser> parserClass = parserClz.get(version);
            if (parserClass == null) {
                throw new IllegalArgumentException("Unsupported haptic file version: " + version);
            }

            try {
                parser = parserClass.newInstance();
                parsers.put(version, parser);
                return parser;
            } catch (InstantiationException | IllegalAccessException e) {
                throw new RuntimeException("Failed to create haptic file parser", e);
            }
        }
    }
}
