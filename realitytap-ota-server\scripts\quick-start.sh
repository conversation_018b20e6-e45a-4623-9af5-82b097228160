#!/bin/bash

# RealityTap OTA Server Quick Start Script
# This script provides a simple way to get started with the OTA server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_DIR="$PROJECT_ROOT/docker"

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
RealityTap OTA Server Quick Start Script

This script helps you quickly deploy the OTA server for development and testing.

Usage: $0 [OPTIONS]

Options:
    --stop          Stop the running services
    --restart       Restart the services
    --logs          Show service logs
    --status        Show service status
    --clean         Clean up containers and volumes
    -h, --help      Show this help message

Examples:
    $0              # Start the OTA server
    $0 --stop       # Stop the OTA server
    $0 --logs       # View logs
    $0 --clean      # Clean up everything

Default Configuration:
    - Admin Username: admin
    - Admin Password: admin123
    - Port: 3000
    - Mode: HTTP (development)
    
SECURITY WARNING: 
Change the default password before using in production!

EOF
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_info "Prerequisites check passed"
}

# Setup directories
setup_directories() {
    log_info "Setting up directories..."
    
    cd "$DOCKER_DIR"
    
    # Create data directories
    mkdir -p data/storage/database
    mkdir -p data/storage/releases/stable
    mkdir -p data/storage/releases/beta
    mkdir -p data/storage/releases/alpha
    mkdir -p data/storage/metadata
    mkdir -p data/storage/temp
    mkdir -p data/storage/backup
    mkdir -p data/logs
    
    log_info "Directories created successfully"
}

# Start services
start_services() {
    log_info "Starting RealityTap OTA Server..."
    
    cd "$DOCKER_DIR"
    
    # Use the simple compose file
    docker-compose -f docker-compose.simple.yml up -d --build
    
    log_info "Services started successfully!"
    log_info ""
    log_info "🎉 RealityTap OTA Server is now running!"
    log_info ""
    log_info "📱 Access the application:"
    log_info "   Web Interface: http://localhost:3000"
    log_info "   Admin Panel:   http://localhost:3000/admin"
    log_info ""
    log_info "🔐 Default Admin Credentials:"
    log_info "   Username: admin"
    log_info "   Password: admin123"
    log_info ""
    log_warn "⚠️  SECURITY WARNING: Change the default password before production use!"
    log_info ""
    log_info "📋 Useful Commands:"
    log_info "   View logs:    $0 --logs"
    log_info "   Stop server:  $0 --stop"
    log_info "   Restart:      $0 --restart"
    log_info "   Status:       $0 --status"
}

# Stop services
stop_services() {
    log_info "Stopping RealityTap OTA Server..."
    
    cd "$DOCKER_DIR"
    docker-compose -f docker-compose.simple.yml down
    
    log_info "Services stopped successfully!"
}

# Restart services
restart_services() {
    log_info "Restarting RealityTap OTA Server..."
    
    cd "$DOCKER_DIR"
    docker-compose -f docker-compose.simple.yml restart
    
    log_info "Services restarted successfully!"
}

# Show logs
show_logs() {
    log_info "Showing service logs (Press Ctrl+C to exit)..."
    
    cd "$DOCKER_DIR"
    docker-compose -f docker-compose.simple.yml logs -f
}

# Show status
show_status() {
    log_info "Service Status:"
    
    cd "$DOCKER_DIR"
    docker-compose -f docker-compose.simple.yml ps
    
    echo ""
    log_info "Container Health:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=realitytap"
}

# Clean up
clean_up() {
    log_warn "This will remove all containers, volumes, and data. Are you sure?"
    read -p "Type 'yes' to confirm: " -r
    
    if [[ $REPLY == "yes" ]]; then
        log_info "Cleaning up..."
        
        cd "$DOCKER_DIR"
        
        # Stop and remove containers
        docker-compose -f docker-compose.simple.yml down -v
        
        # Remove images
        docker rmi realitytap-ota-server_realitytap-ota-server 2>/dev/null || true
        
        # Remove data directories
        rm -rf data/
        
        log_info "Cleanup completed!"
    else
        log_info "Cleanup cancelled"
    fi
}

# Parse arguments
case "${1:-}" in
    --stop)
        check_prerequisites
        stop_services
        ;;
    --restart)
        check_prerequisites
        restart_services
        ;;
    --logs)
        check_prerequisites
        show_logs
        ;;
    --status)
        check_prerequisites
        show_status
        ;;
    --clean)
        check_prerequisites
        clean_up
        ;;
    -h|--help)
        show_help
        ;;
    "")
        # Default action: start services
        check_prerequisites
        setup_directories
        start_services
        ;;
    *)
        log_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
