// Project-related data models
use crate::models::{Group, HapticFile};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Project {
    pub project_uuid: Uuid,
    pub project_name: String,
    pub create_time: DateTime<Utc>,
    pub last_modified_time: DateTime<Utc>,
    pub author: String,
    pub version: String, // Schema version of project.json, e.g., "2.0.0-dir"
    pub description: String,
    pub tags: Vec<String>,
    #[serde(default)] // Allow empty groups
    pub groups: Vec<Group>,
    pub files: Vec<HapticFile>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RecentProject {
    pub project_uuid: Option<Uuid>,
    pub project_name: String,
    pub project_path: String,
    pub last_accessed: DateTime<Utc>,
    pub file_count: usize,
    pub icon: String,
}

#[derive(Serialize, Deserialize, Debug, <PERSON>lone, Default)]
#[serde(rename_all = "camelCase")]
pub struct RecentProjectsList {
    pub projects: Vec<RecentProject>,
}
