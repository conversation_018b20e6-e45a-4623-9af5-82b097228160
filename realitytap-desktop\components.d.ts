/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AboutButton: typeof import('./src/components/common/AboutButton.vue')['default']
    AboutDialog: typeof import('./src/components/common/AboutDialog.vue')['default']
    DebugButton: typeof import('./src/components/common/DebugButton.vue')['default']
    DebugSettings: typeof import('./src/components/debug/DebugSettings.vue')['default']
    DeviceCard: typeof import('./src/components/device/DeviceCard.vue')['default']
    DeviceList: typeof import('./src/components/device/DeviceList.vue')['default']
    DeviceManager: typeof import('./src/components/device/DeviceManager.vue')['default']
    DeviceStatusIndicator: typeof import('./src/components/device/DeviceStatusIndicator.vue')['default']
    EmptyProjectPrompt: typeof import('./src/components/editor/haptics/EmptyProjectPrompt.vue')['default']
    EnvironmentInfo: typeof import('./src/components/dev/EnvironmentInfo.vue')['default']
    FileSaveButton: typeof import('./src/components/editor/common/FileSaveButton.vue')['default']
    ForceUpdateDialog: typeof import('./src/components/common/ForceUpdateDialog.vue')['default']
    GlobalDeviceStatusIndicator: typeof import('./src/components/common/GlobalDeviceStatusIndicator.vue')['default']
    HapticEventPropertiesPanel: typeof import('./src/components/editor/adjustments/HapticEventPropertiesPanel.vue')['default']
    HapticFileExplorer: typeof import('./src/components/editor/haptics/HapticFileExplorer.vue')['default']
    HapticProjectEditor: typeof import('./src/components/editor/HapticProjectEditor.vue')['default']
    HapticWaveformEditor: typeof import('./src/components/editor/waveform/HapticWaveformEditor.vue')['default']
    InteractiveWaveformCanvas: typeof import('./src/components/editor/waveform/InteractiveWaveformCanvas.vue')['default']
    LanguageSwitcher: typeof import('./src/components/common/LanguageSwitcher.vue')['default']
    LogViewer: typeof import('./src/components/debug/LogViewer.vue')['default']
    MultiFileTabManager: typeof import('./src/components/editor/waveform/MultiFileTabManager.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NDivider: typeof import('naive-ui')['NDivider']
    NIcon: typeof import('naive-ui')['NIcon']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NModal: typeof import('naive-ui')['NModal']
    NProgress: typeof import('naive-ui')['NProgress']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NSwitch: typeof import('naive-ui')['NSwitch']
    ParameterSliderControl: typeof import('./src/components/editor/adjustments/ParameterSliderControl.vue')['default']
    PerformanceMonitor: typeof import('./src/components/dev/PerformanceMonitor.vue')['default']
    PlayEffectDialog: typeof import('./src/components/editor/haptics/PlayEffectDialog.vue')['default']
    ProgrammaticUpdateExample: typeof import('./src/components/editor/waveform/examples/ProgrammaticUpdateExample.vue')['default']
    ProjectNavigationHeader: typeof import('./src/components/editor/header/ProjectNavigationHeader.vue')['default']
    RedoButton: typeof import('./src/components/editor/common/RedoButton.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SecurityRestrictionsDemo: typeof import('./src/components/dev/SecurityRestrictionsDemo.vue')['default']
    TabCloseButton: typeof import('./src/components/editor/common/TabCloseButton.vue')['default']
    TimelineDurationPanel: typeof import('./src/components/editor/adjustments/TimelineDurationPanel.vue')['default']
    UndoButton: typeof import('./src/components/editor/common/UndoButton.vue')['default']
    UpdateDialog: typeof import('./src/components/common/UpdateDialog.vue')['default']
    WaveformContextMenu: typeof import('./src/components/editor/waveform/components/WaveformContextMenu.vue')['default']
    WaveformValueTooltip: typeof import('./src/components/editor/waveform/components/WaveformValueTooltip.vue')['default']
    WaveformYAxisScale: typeof import('./src/components/editor/waveform/components/WaveformYAxisScale.vue')['default']
    WindowTitleBar: typeof import('./src/components/common/WindowTitleBar.vue')['default']
  }
}
