#!/bin/bash

# RealityTap OTA Server Environment Setup Script
# This script helps users create a .env file with proper configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_DIR="$PROJECT_ROOT/docker"
ENV_FILE="$DOCKER_DIR/.env"

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Helper functions
generate_jwt_secret() {
    if command -v openssl &> /dev/null; then
        openssl rand -base64 48 | tr -d "=+/" | cut -c1-32
    else
        # Fallback method using /dev/urandom
        cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 32 | head -n 1
    fi
}

get_public_ip() {
    local ip=""
    
    # Try multiple services to get public IP
    for service in "ipinfo.io/ip" "ifconfig.me" "icanhazip.com" "ipecho.net/plain"; do
        if ip=$(curl -s --connect-timeout 5 "$service" 2>/dev/null); then
            # Validate IP format
            if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
                echo "$ip"
                return 0
            fi
        fi
    done
    
    # Fallback to localhost
    echo "localhost"
}

prompt_input() {
    local prompt="$1"
    local default="$2"
    local value=""

    if [[ -n "$default" ]]; then
        echo -ne "${CYAN}$prompt${NC} ${YELLOW}[默认: $default]${NC}: "
    else
        echo -ne "${CYAN}$prompt${NC}: "
    fi

    read -r value

    if [[ -z "$value" && -n "$default" ]]; then
        echo "$default"
    else
        echo "$value"
    fi
}

prompt_password() {
    local prompt="$1"
    local value=""

    echo -ne "${CYAN}$prompt${NC}: "
    read -s -r value
    echo

    echo "$value"
}

prompt_yes_no() {
    local prompt="$1"
    local default="$2"
    local value=""

    if [[ "$default" == "y" ]]; then
        echo -ne "${CYAN}$prompt${NC} ${YELLOW}[Y/n]${NC}: "
    else
        echo -ne "${CYAN}$prompt${NC} ${YELLOW}[y/N]${NC}: "
    fi

    read -r value

    if [[ -z "$value" ]]; then
        echo "$default"
    else
        echo "${value,,}"  # Convert to lowercase
    fi
}

validate_password() {
    local password="$1"
    
    if [[ ${#password} -lt 8 ]]; then
        log_error "密码长度至少需要 8 个字符"
        return 1
    fi
    
    return 0
}

show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  RealityTap OTA Server 环境配置向导"
    echo "=================================================="
    echo -e "${NC}"
    echo "此脚本将帮助您创建 Docker 部署所需的 .env 配置文件"
    echo ""
}

show_summary() {
    local admin_user="$1"
    local base_url="$2"
    local https_enabled="$3"
    local storage_path="$4"
    local keys_path="$5"
    
    echo ""
    echo -e "${BLUE}=================================================="
    echo "  配置摘要"
    echo -e "==================================================${NC}"
    echo -e "管理员用户名: ${GREEN}$admin_user${NC}"
    echo -e "访问地址: ${GREEN}$base_url${NC}"
    echo -e "HTTPS 模式: ${GREEN}$([ "$https_enabled" == "y" ] && echo "启用" || echo "禁用")${NC}"
    echo -e "存储目录: ${GREEN}$storage_path${NC}"
    echo -e "密钥目录: ${GREEN}$keys_path${NC}"
    echo ""
}

# Main setup function
main() {
    show_banner
    
    # Check if .env already exists
    if [[ -f "$ENV_FILE" ]]; then
        log_warn "发现已存在的 .env 文件: $ENV_FILE"
        echo -ne "${CYAN}是否备份现有文件并创建新的配置？${NC} ${YELLOW}[y/N]${NC}: "
        read -r backup_choice
        if [[ -z "$backup_choice" ]]; then
            backup_choice="n"
        else
            backup_choice="${backup_choice,,}"
        fi

        if [[ "$backup_choice" == "y" ]]; then
            backup_file="${ENV_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
            cp "$ENV_FILE" "$backup_file"
            log_info "已备份现有文件到: $backup_file"
        else
            log_info "取消配置，保留现有 .env 文件"
            exit 0
        fi
    fi
    
    # Ensure docker directory exists
    mkdir -p "$DOCKER_DIR"
    
    log_step "开始收集配置信息..."
    echo ""
    
    # 1. Admin Configuration
    echo -e "${YELLOW}=== 管理员配置 ===${NC}"

    # Test simple input first
    echo -ne "${CYAN}管理员用户名${NC} ${YELLOW}[默认: admin]${NC}: "
    read -r admin_username
    if [[ -z "$admin_username" ]]; then
        admin_username="admin"
    fi

    while true; do
        echo -ne "${CYAN}管理员密码（至少8个字符）${NC}: "
        read -s -r admin_password
        echo
        if [[ ${#admin_password} -ge 8 ]]; then
            break
        else
            log_error "密码长度至少需要 8 个字符"
        fi
    done
    
    # 2. Server Configuration
    echo ""
    echo -e "${YELLOW}=== 服务器配置 ===${NC}"
    
    # Get public IP
    log_info "正在获取服务器公网 IP..."
    public_ip=$(get_public_ip)
    log_info "检测到 IP: $public_ip"
    
    echo -ne "${CYAN}服务器端口${NC} ${YELLOW}[默认: 3000]${NC}: "
    read -r host_port
    if [[ -z "$host_port" ]]; then
        host_port="3000"
    fi

    # HTTPS Configuration
    echo -ne "${CYAN}是否启用 HTTPS？${NC} ${YELLOW}[y/N]${NC}: "
    read -r https_enabled
    if [[ -z "$https_enabled" ]]; then
        https_enabled="n"
    else
        https_enabled="${https_enabled,,}"
    fi

    if [[ "$https_enabled" == "y" ]]; then
        base_url="https://$public_ip:$host_port"

        echo -ne "${CYAN}SSL 证书文件路径${NC} ${YELLOW}[默认: ./ssl/cert.pem]${NC}: "
        read -r ssl_cert_path
        if [[ -z "$ssl_cert_path" ]]; then
            ssl_cert_path="./ssl/cert.pem"
        fi

        echo -ne "${CYAN}SSL 私钥文件路径${NC} ${YELLOW}[默认: ./ssl/key.pem]${NC}: "
        read -r ssl_key_path
        if [[ -z "$ssl_key_path" ]]; then
            ssl_key_path="./ssl/key.pem"
        fi
    else
        base_url="http://$public_ip:$host_port"
    fi

    echo -ne "${CYAN}外部访问 URL${NC} ${YELLOW}[默认: $base_url]${NC}: "
    read -r input_base_url
    if [[ -n "$input_base_url" ]]; then
        base_url="$input_base_url"
    fi

    # 3. Directory Configuration
    echo ""
    echo -e "${YELLOW}=== 目录配置 ===${NC}"

    echo -ne "${CYAN}数据存储目录${NC} ${YELLOW}[默认: ./data/storage]${NC}: "
    read -r storage_path
    if [[ -z "$storage_path" ]]; then
        storage_path="./data/storage"
    fi

    echo -ne "${CYAN}签名密钥目录${NC} ${YELLOW}[默认: ./keys]${NC}: "
    read -r keys_path
    if [[ -z "$keys_path" ]]; then
        keys_path="./keys"
    fi
    
    # 4. Generate JWT Secret
    echo ""
    log_step "生成 JWT 密钥..."
    jwt_secret=$(generate_jwt_secret)
    log_success "JWT 密钥已生成"
    
    # Show summary
    show_summary "$admin_username" "$base_url" "$https_enabled" "$storage_path" "$keys_path"
    
    # Confirm
    echo -ne "${CYAN}确认以上配置并生成 .env 文件？${NC} ${YELLOW}[Y/n]${NC}: "
    read -r confirm
    if [[ -z "$confirm" ]]; then
        confirm="y"
    else
        confirm="${confirm,,}"
    fi
    
    if [[ "$confirm" != "y" ]]; then
        log_info "已取消配置"
        exit 0
    fi
    
    # Generate .env file
    log_step "正在生成 .env 文件..."
    
    cat > "$ENV_FILE" << EOF
# RealityTap OTA Server Environment Configuration
# Generated by setup-env.sh on $(date)

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
APP_VERSION=latest
NODE_ENV=production
HOST_PORT=$host_port
CONTAINER_PORT=3000
BASE_URL=$base_url

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================
ADMIN_USERNAME=$admin_username
ADMIN_PASSWORD=$admin_password
JWT_SECRET=$jwt_secret
JWT_EXPIRES_IN=1h
SESSION_TIMEOUT=3600000

# =============================================================================
# HOST DIRECTORY MAPPING
# =============================================================================
STORAGE_HOST_PATH=$storage_path
KEYS_HOST_PATH=$keys_path
EOF

    # Add HTTPS configuration if enabled
    if [[ "$https_enabled" == "y" ]]; then
        cat >> "$ENV_FILE" << EOF

# =============================================================================
# SSL CONFIGURATION
# =============================================================================
SSL_CERT_HOST_PATH=$ssl_cert_path
SSL_KEY_HOST_PATH=$ssl_key_path
EOF
    fi

    # Add remaining configuration
    cat >> "$ENV_FILE" << EOF

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_ENABLED=true
DB_MAX_CONNECTIONS=10
DB_BUSY_TIMEOUT=30000
DB_ENABLE_WAL=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
ADMIN_RATE_LIMIT_WINDOW_MS=300000
ADMIN_RATE_LIMIT_MAX_REQUESTS=500
PUBLIC_RATE_LIMIT_WINDOW_MS=300000
PUBLIC_RATE_LIMIT_MAX_REQUESTS=200

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
ADMIN_MAX_FILE_SIZE=104857600
ADMIN_ALLOWED_FILE_TYPES=.exe,.msi,.dmg,.deb,.rpm,.tar.gz,.zip,.pkg

# =============================================================================
# TAURI UPDATER CONFIGURATION
# =============================================================================
TAURI_PRIVATE_KEY_PATH=/app/keys/private.key
TAURI_KEY_PASSWORD=

# =============================================================================
# DEBUG CONFIGURATION
# =============================================================================
DEBUG=false
EOF

    log_success "配置文件已生成: $ENV_FILE"
    
    # Show next steps
    echo ""
    echo -e "${BLUE}=================================================="
    echo "  下一步操作"
    echo -e "==================================================${NC}"
    echo "1. 检查生成的配置文件: $ENV_FILE"
    
    if [[ "$https_enabled" == "y" ]]; then
        echo "2. 准备 SSL 证书文件:"
        echo "   - 证书文件: $ssl_cert_path"
        echo "   - 私钥文件: $ssl_key_path"
        echo "3. 运行部署命令: ./scripts/docker-deploy.sh -m https"
    else
        echo "2. 运行部署命令: ./scripts/docker-deploy.sh"
    fi
    
    echo ""
    echo -e "${GREEN}配置完成！${NC}"
}

# Run main function
main "$@"
