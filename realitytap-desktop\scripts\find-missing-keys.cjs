#!/usr/bin/env node

/**
 * 查找前端使用但在某些语言文件中缺失的翻译键
 */

const fs = require('fs');
const path = require('path');

// 加载使用的键
const usedKeysFile = path.join(__dirname, 'used-i18n-keys.json');
if (!fs.existsSync(usedKeysFile)) {
  console.error('❌ 请先运行 find-used-i18n-keys.cjs 生成使用键列表');
  process.exit(1);
}

const { allUsedKeys } = JSON.parse(fs.readFileSync(usedKeysFile, 'utf8'));
const usedKeysSet = new Set(allUsedKeys);

// 语言文件路径
const localeFiles = {
  'zh-CN': path.join(__dirname, '../src/locales/zh-CN.ts'),
  'en-US': path.join(__dirname, '../src/locales/en-US.ts'),
  'ja-JP': path.join(__dirname, '../src/locales/ja-JP.ts'),
  'ko-KR': path.join(__dirname, '../src/locales/ko-KR.ts'),
};

// 提取翻译键的函数
function extractKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

// 加载语言文件
function loadLocaleFile(filePath) {
  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 移除 TypeScript 语法，提取 export default 的对象
    const match = content.match(/export\s+default\s+(\{[\s\S]*\})\s*as\s+const;?/);
    if (!match) {
      throw new Error('无法解析语言文件格式');
    }
    
    // 使用 eval 解析对象（注意：这在生产环境中不安全，但在构建脚本中可以接受）
    const obj = eval(`(${match[1]})`);
    return extractKeys(obj);
  } catch (error) {
    console.error(`❌ 加载语言文件失败: ${filePath} - ${error.message}`);
    return [];
  }
}

console.log('🔍 查找前端使用但在某些语言文件中缺失的翻译键...\n');

// 加载所有语言文件的键
const localeData = {};
for (const [locale, filePath] of Object.entries(localeFiles)) {
  console.log(`📖 加载 ${locale} 语言文件...`);
  const keys = loadLocaleFile(filePath);
  localeData[locale] = new Set(keys);
  console.log(`   ✅ 找到 ${keys.length} 个翻译键`);
}

console.log(`\n📊 前端代码使用了 ${allUsedKeys.length} 个翻译键\n`);

// 检查每个使用的键是否在所有语言文件中都存在
const missingByLocale = {};
let hasMissingKeys = false;

for (const [locale, keys] of Object.entries(localeData)) {
  const missingKeys = allUsedKeys.filter(key => !keys.has(key));
  
  if (missingKeys.length > 0) {
    hasMissingKeys = true;
    missingByLocale[locale] = missingKeys;
    console.log(`❌ ${locale} 缺失 ${missingKeys.length} 个前端使用的翻译键:`);
    missingKeys.forEach(key => {
      console.log(`   - ${key}`);
    });
    console.log();
  } else {
    console.log(`✅ ${locale} 包含所有前端使用的翻译键`);
  }
}

if (!hasMissingKeys) {
  console.log('🎉 所有语言文件都包含前端使用的翻译键！');
} else {
  console.log('⚠️ 发现前端使用但在某些语言文件中缺失的翻译键，需要补充翻译。');
  
  // 保存结果
  const resultFile = path.join(__dirname, 'missing-keys-analysis.json');
  fs.writeFileSync(resultFile, JSON.stringify({
    usedKeysCount: allUsedKeys.length,
    missingByLocale,
    timestamp: new Date().toISOString()
  }, null, 2));
  
  console.log(`\n💾 分析结果已保存到: ${resultFile}`);
}
