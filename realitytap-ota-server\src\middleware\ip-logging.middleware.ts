import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger.util';
import { IPUtil } from '@/utils/ip.util';
import { config } from '@/config/server.config';

/**
 * 扩展Request接口，添加IP相关信息
 */
declare global {
  namespace Express {
    interface Request {
      realClientIP?: string;
      ipInfo?: {
        realIP: string;
        sources: Record<string, string>;
        isPrivate: boolean;
        anonymized: string;
      };
    }
  }
}

/**
 * IP记录中间件配置
 */
interface IPLoggingOptions {
  /** 是否记录详细的IP来源信息（用于调试） */
  logDetailedSources?: boolean;
  /** 是否只记录OTA相关的请求 */
  otaRequestsOnly?: boolean;
  /** 是否在开发环境下记录更多信息 */
  verboseInDev?: boolean;
}

/**
 * 检查是否为OTA相关请求
 * @param req Express请求对象
 * @returns 是否为OTA相关请求
 */
function isOTARequest(req: Request): boolean {
  const otaPaths = [
    '/api/v1/version',
    '/api/v1/download',
    '/api/v1/updates',
    '/api/v1/health',
  ];

  return otaPaths.some(path => req.path.startsWith(path));
}

/**
 * 检查是否为安全相关请求
 */
function isSecurityRelatedRequest(req: Request): boolean {
  const securityPaths = [
    '/api/v1/admin/login',
    '/api/v1/admin/logout',
    '/api/v1/admin/refresh',
    '/api/v1/admin/upload',
    '/api/v1/admin/delete',
    '/api/v1/admin/config',
  ];

  // 安全相关路径
  const isSecurityPath = securityPaths.some(path => req.path.startsWith(path));

  // 非GET请求的管理操作
  const isAdminModification = req.path.startsWith('/api/v1/admin/') && req.method !== 'GET';

  return isSecurityPath || isAdminModification;
}

/**
 * 检查是否应该跳过IP记录
 * @param req Express请求对象
 * @returns 是否应该跳过
 */
function shouldSkipIPLogging(req: Request): boolean {
  const skipConditions = [
    // 跳过健康检查请求
    req.url === '/health' || req.url === '/ping',
    // 跳过静态资源请求
    req.url.startsWith('/admin/assets/'),
    // 跳过favicon请求
    req.url === '/favicon.ico',
    // 跳过某些内部请求
    req.url.startsWith('/_internal/'),
  ];

  return skipConditions.some(condition => condition);
}

/**
 * 创建IP记录中间件
 * @param options 配置选项
 * @returns Express中间件函数
 */
export function createIPLoggingMiddleware(options: IPLoggingOptions = {}) {
  const {
    logDetailedSources = false,
    otaRequestsOnly = false,
    verboseInDev = true,
  } = options;

  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // 检查是否应该跳过
      if (shouldSkipIPLogging(req)) {
        next();
        return;
      }

      // 如果只记录OTA请求，检查是否为OTA请求
      if (otaRequestsOnly && !isOTARequest(req)) {
        next();
        return;
      }

      // 获取真实客户端IP
      const realClientIP = IPUtil.getRealClientIP(req);
      
      // 获取详细IP信息
      const detailedInfo = IPUtil.getDetailedIPInfo(req);
      
      // 将IP信息添加到请求对象
      req.realClientIP = realClientIP;
      req.ipInfo = {
        realIP: realClientIP,
        sources: detailedInfo.sources,
        isPrivate: IPUtil.isPrivateIP(realClientIP),
        anonymized: IPUtil.anonymizeIP(realClientIP),
      };

      // 构建日志信息
      const logData: any = {
        clientIP: realClientIP,
        method: req.method,
        path: req.path,
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer'),
        isPrivateIP: req.ipInfo.isPrivate,
      };

      // 在开发环境或启用详细记录时，添加更多信息
      if ((config.server.nodeEnv === 'development' && verboseInDev) || logDetailedSources) {
        logData.ipSources = detailedInfo.sources;
        logData.anonymizedIP = req.ipInfo.anonymized;
      }

      // 记录策略：只记录安全相关和远程客户端请求
      const shouldLogRequest =
        isOTARequest(req) ||                    // OTA客户端请求
        !req.ipInfo.isPrivate ||               // 远程客户端请求
        isSecurityRelatedRequest(req);         // 安全相关请求

      if (shouldLogRequest) {
        const requestType = isOTARequest(req) ? 'OTA' :
                           !req.ipInfo.isPrivate ? 'Remote' : 'Security';

        // 只记录重要的客户端请求，减少冗余日志
        if (isOTARequest(req) || !req.ipInfo.isPrivate) {
          logger.info('客户端请求', {
            ...logData,
            requestType,
            timestamp: new Date().toISOString(),
            module: isOTARequest(req) ? 'ota_function' : 'system',
            operation: 'client_request',
          });
        }
      }

      next();
    } catch (error) {
      logger.error('IP日志中间件错误', {
        error: error instanceof Error ? error.message : String(error),
        path: req.path,
        method: req.method,
        module: 'system',
        operation: 'ip_logging_error',
      });
      
      // 即使出错也要继续处理请求
      next();
    }
  };
}

/**
 * 默认的IP记录中间件（用于OTA请求）
 */
export const ipLoggingMiddleware = createIPLoggingMiddleware({
  otaRequestsOnly: true,
  logDetailedSources: config.server.nodeEnv === 'development',
  verboseInDev: true,
});

/**
 * 全局IP记录中间件（记录所有请求）
 */
export const globalIPLoggingMiddleware = createIPLoggingMiddleware({
  otaRequestsOnly: false,
  logDetailedSources: config.server.nodeEnv === 'development',
  verboseInDev: true,
});

/**
 * 生产环境IP记录中间件（最小化日志）
 */
export const productionIPLoggingMiddleware = createIPLoggingMiddleware({
  otaRequestsOnly: true,
  logDetailedSources: false,
  verboseInDev: false,
});

/**
 * 获取请求的客户端IP信息（工具函数）
 * @param req Express请求对象
 * @returns IP信息对象
 */
export function getRequestIPInfo(req: Request): {
  ip: string;
  isPrivate: boolean;
  anonymized: string;
  sources?: Record<string, string>;
} {
  // 如果中间件已经处理过，直接返回
  if (req.ipInfo) {
    return {
      ip: req.ipInfo.realIP,
      isPrivate: req.ipInfo.isPrivate,
      anonymized: req.ipInfo.anonymized,
      sources: req.ipInfo.sources,
    };
  }

  // 否则临时获取IP信息
  const realIP = IPUtil.getRealClientIP(req);
  return {
    ip: realIP,
    isPrivate: IPUtil.isPrivateIP(realIP),
    anonymized: IPUtil.anonymizeIP(realIP),
  };
}

/**
 * 记录特定操作的IP信息（用于重要操作的额外记录）
 * @param req Express请求对象
 * @param operation 操作名称
 * @param additionalData 额外数据
 */
export function logOperationIP(
  req: Request,
  operation: string,
  additionalData: Record<string, any> = {}
): void {
  const ipInfo = getRequestIPInfo(req);
  
  logger.info(`Operation: ${operation}`, {
    operation,
    clientIP: ipInfo.ip,
    isPrivateIP: ipInfo.isPrivate,
    userAgent: req.get('User-Agent'),
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString(),
    ...additionalData,
  });
}
