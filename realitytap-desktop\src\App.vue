<script setup lang="ts">
import { onMounted, onUnmounted, computed, ref } from "vue";
import { NConfigProvider, NMessageProvider, NNotificationProvider, darkTheme } from "naive-ui";
import WindowTitleBar from "./components/common/WindowTitleBar.vue";
import { useWindowSettingsStore } from "./stores/window-settings-store";
import { useLanguageStore } from "./stores/language-store";
import { initializeSecurityRestrictions, isSecurityRestrictionsEnabled } from "./utils/security-restrictions";
import { useAutoUpdateChecker } from "./composables/useAutoUpdateChecker";
import { useUpdateNotificationStore } from "./stores/update-notification-store";
import { useGlobalUpdate } from "./composables/useGlobalUpdate";
import ForceUpdateDialog from "./components/common/ForceUpdateDialog.vue";
import { logger, LogModule } from "./utils/logger/logger";
import { useLibrtcoreGlobalManager } from "./composables/haptics/useLibrtcoreGlobalManager";
import { LIBRTCORE_CONFIG } from "./composables/haptics/useLibrtcoreGlobalConfig";
import { startGlobalConfigWatching, stopGlobalConfigWatching } from "./composables/haptics/useLibrtcoreConfigWatcher";
import { initializeHapticCallbackSystem, cleanupHapticCallbackSystem } from "./composables/haptics/useHapticCallbackManager";

const windowSettingsStore = useWindowSettingsStore();
const languageStore = useLanguageStore();
const autoUpdateChecker = useAutoUpdateChecker();
const updateStore = useUpdateNotificationStore();
const globalUpdate = useGlobalUpdate();

// librtcore 全局管理器
const librtcoreGlobalManager = useLibrtcoreGlobalManager();

// 当前应用版本
const currentVersion = ref("1.0.0");

// 计算是否应用安全限制样式类
const appContainerClass = computed(() => {
  const classes = ['app-container'];
  if (isSecurityRestrictionsEnabled()) {
    classes.push('security-restrictions-enabled');
  }
  return classes.join(' ');
});

// Initialize app on startup
onMounted(async () => {
  try {
    // 初始化安全限制（生产环境）
    initializeSecurityRestrictions();

    // 初始化语言设置
    await languageStore.initializeLanguage();

    // 初始化窗口设置
    await windowSettingsStore.initializeWindowSettings();
    await windowSettingsStore.setupWindowListeners();

    // 检查红点状态是否需要更新
    try {
      const { getVersion } = await import('@tauri-apps/api/app');
      const appVersion = await getVersion();
      currentVersion.value = appVersion;
      updateStore.checkShouldShowBadge(appVersion);
      logger.info(LogModule.GENERAL, "红点状态检查完成", { currentVersion: appVersion });
    } catch (error) {
      logger.warn(LogModule.GENERAL, "获取应用版本失败", error);
    }

    // 启动自动更新检查服务
    logger.info(LogModule.GENERAL, "启动自动更新检查服务");
    await autoUpdateChecker.start();

    // 初始化触觉回调系统（建立与后端单例的稳定绑定）
    try {
      logger.info(LogModule.GENERAL, "初始化触觉回调系统");
      await initializeHapticCallbackSystem();
      logger.info(LogModule.GENERAL, "触觉回调系统初始化成功");
    } catch (error) {
      logger.error(LogModule.GENERAL, "触觉回调系统初始化失败", error);
    }

    // 启动全局配置监听器
    logger.info(LogModule.GENERAL, "启动 librtcore 全局配置监听器");
    startGlobalConfigWatching();

    // 延迟启动 librtcore 后台初始化（不阻塞应用启动）
    setTimeout(async () => {
      try {
        // 首先同步后端状态
        logger.info(LogModule.GENERAL, "同步 librtcore 后端状态");
        const isAlreadyInitialized = await librtcoreGlobalManager.syncWithBackend();

        if (!isAlreadyInitialized) {
          logger.info(LogModule.GENERAL, "开始 librtcore 后台初始化");
          const success = await librtcoreGlobalManager.backgroundInitialize();
          if (success) {
            logger.info(LogModule.GENERAL, "librtcore 后台初始化成功");
          } else {
            logger.debug(LogModule.GENERAL, "librtcore 后台初始化跳过（缺少配置或已初始化）");
          }
        } else {
          logger.info(LogModule.GENERAL, "librtcore 已初始化，跳过后台初始化");
        }
      } catch (error) {
        // 后台初始化失败不影响应用正常运行
        logger.warn(LogModule.GENERAL, "librtcore 初始化过程失败", error);
      }
    }, LIBRTCORE_CONFIG.BACKGROUND_INIT_DELAY);

  } catch (error) {
    logger.error(LogModule.GENERAL, "应用初始化失败", error);
  }
});

// 应用关闭时清理资源
onUnmounted(async () => {
  try {
    logger.info(LogModule.GENERAL, "应用关闭，开始清理资源");

    // 停止配置监听器
    stopGlobalConfigWatching();

    // 清理触觉回调系统
    try {
      await cleanupHapticCallbackSystem();
      logger.info(LogModule.GENERAL, "触觉回调系统清理完成");
    } catch (error) {
      logger.warn(LogModule.GENERAL, "触觉回调系统清理失败", error);
    }

    // 清理 librtcore 资源（调用最终清理）
    await librtcoreGlobalManager.cleanup();

    logger.info(LogModule.GENERAL, "资源清理完成");
  } catch (error) {
    logger.warn(LogModule.GENERAL, "资源清理失败", error);
  }
});
</script>

<template>
  <NConfigProvider :theme="darkTheme" @contextmenu.prevent>
    <NNotificationProvider>
      <NMessageProvider>
        <div :class="appContainerClass">
          <WindowTitleBar />
          <div class="content-area">
            <router-view v-slot="{ Component }">
              <transition name="fade" mode="out-in">
                <component :is="Component" />
              </transition>
            </router-view>
          </div>
        </div>

        <!-- 强制更新对话框 -->
        <ForceUpdateDialog
          :visible="globalUpdate.showForceUpdateDialog.value"
          :update-info="globalUpdate.updateInfo.value"
          :current-version="currentVersion"
          @update:visible="globalUpdate.hideForceUpdate"
        />
      </NMessageProvider>
    </NNotificationProvider>
  </NConfigProvider>
</template>

<style>
.app-container {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #1a1a1a;
  color: #e6e6e6;
}

.content-area {
  flex: 1;
  margin-top: 40px; /* 标题栏高度 */
  overflow: hidden;
  height: calc(100vh - 40px);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
