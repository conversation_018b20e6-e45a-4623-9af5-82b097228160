import { BaseDAO } from './base.dao';

export interface CreateDeprecatedVersionData {
  version: string;
  reason?: string;
}

export interface UpdateDeprecatedVersionData {
  reason?: string;
}

export interface DeprecatedVersionEntity {
  id: number;
  version: string;
  deprecated_at: string;
  reason?: string;
}

export class DeprecatedVersionDAO extends BaseDAO {
  constructor() {
    super('deprecated_versions');
  }

  /**
   * 创建废弃版本记录
   */
  async createDeprecatedVersion(data: CreateDeprecatedVersionData): Promise<number> {
    this.logOperation('createDeprecatedVersion', data);
    return await this.insert(data);
  }

  /**
   * 根据版本号获取废弃版本记录
   */
  async getDeprecatedVersion(version: string): Promise<DeprecatedVersionEntity | undefined> {
    this.logOperation('getDeprecatedVersion', { version });
    return await this.findOneWhere<DeprecatedVersionEntity>({ version });
  }

  /**
   * 获取所有废弃版本
   */
  async getAllDeprecatedVersions(): Promise<DeprecatedVersionEntity[]> {
    this.logOperation('getAllDeprecatedVersions');
    return await this.findAll<DeprecatedVersionEntity>('deprecated_at DESC');
  }

  /**
   * 检查版本是否已废弃
   */
  async isVersionDeprecated(version: string): Promise<boolean> {
    this.logOperation('isVersionDeprecated', { version });
    return await this.exists({ version });
  }

  /**
   * 更新废弃版本记录
   */
  async updateDeprecatedVersion(version: string, data: UpdateDeprecatedVersionData): Promise<boolean> {
    this.logOperation('updateDeprecatedVersion', { version, data });
    
    const existing = await this.getDeprecatedVersion(version);
    if (!existing) {
      return false;
    }
    
    return await this.update(existing.id, data);
  }

  /**
   * 废弃一个版本
   */
  async deprecateVersion(version: string, reason?: string): Promise<void> {
    this.logOperation('deprecateVersion', { version, reason });
    
    const existing = await this.getDeprecatedVersion(version);
    if (existing) {
      // 如果已存在，更新原因
      if (reason) {
        await this.updateDeprecatedVersion(version, { reason });
      }
    } else {
      // 创建新的废弃记录
      await this.createDeprecatedVersion({ version, reason });
    }
  }

  /**
   * 取消废弃版本
   */
  async undeprecateVersion(version: string): Promise<boolean> {
    this.logOperation('undeprecateVersion', { version });
    
    const existing = await this.getDeprecatedVersion(version);
    if (!existing) {
      return false;
    }
    
    return await this.delete(existing.id);
  }

  /**
   * 批量废弃版本
   */
  async batchDeprecateVersions(versions: string[], reason?: string): Promise<void> {
    this.logOperation('batchDeprecateVersions', { count: versions.length, reason });
    
    await this.transaction(async () => {
      for (const version of versions) {
        await this.deprecateVersion(version, reason);
      }
    });
  }

  /**
   * 获取最近废弃的版本
   */
  async getRecentlyDeprecatedVersions(limit: number = 10): Promise<DeprecatedVersionEntity[]> {
    this.logOperation('getRecentlyDeprecatedVersions', { limit });
    
    const sql = `
      SELECT * FROM deprecated_versions 
      ORDER BY deprecated_at DESC 
      LIMIT ?
    `;
    
    return await this.query<DeprecatedVersionEntity>(sql, [limit]);
  }

  /**
   * 根据时间范围获取废弃版本
   */
  async getDeprecatedVersionsByDateRange(startDate: string, endDate: string): Promise<DeprecatedVersionEntity[]> {
    this.logOperation('getDeprecatedVersionsByDateRange', { startDate, endDate });
    
    const sql = `
      SELECT * FROM deprecated_versions 
      WHERE deprecated_at BETWEEN ? AND ?
      ORDER BY deprecated_at DESC
    `;
    
    return await this.query<DeprecatedVersionEntity>(sql, [startDate, endDate]);
  }

  /**
   * 清理旧的废弃版本记录
   */
  async cleanupOldDeprecatedVersions(olderThanDays: number): Promise<number> {
    this.logOperation('cleanupOldDeprecatedVersions', { olderThanDays });
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    const sql = 'DELETE FROM deprecated_versions WHERE deprecated_at < ?';
    const result = await this.execute(sql, [cutoffDate.toISOString()]);
    
    return result;
  }

  /**
   * 获取废弃版本统计
   */
  async getDeprecatedVersionStats(): Promise<{
    totalCount: number;
    thisMonth: number;
    thisYear: number;
    withReason: number;
  }> {
    this.logOperation('getDeprecatedVersionStats');
    
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    const thisYear = new Date(now.getFullYear(), 0, 1).toISOString();
    
    const [totalResult, monthResult, yearResult, reasonResult] = await Promise.all([
      this.query<{ count: number }>('SELECT COUNT(*) as count FROM deprecated_versions'),
      this.query<{ count: number }>('SELECT COUNT(*) as count FROM deprecated_versions WHERE deprecated_at >= ?', [thisMonth]),
      this.query<{ count: number }>('SELECT COUNT(*) as count FROM deprecated_versions WHERE deprecated_at >= ?', [thisYear]),
      this.query<{ count: number }>('SELECT COUNT(*) as count FROM deprecated_versions WHERE reason IS NOT NULL AND reason != ""')
    ]);
    
    return {
      totalCount: totalResult[0]?.count || 0,
      thisMonth: monthResult[0]?.count || 0,
      thisYear: yearResult[0]?.count || 0,
      withReason: reasonResult[0]?.count || 0
    };
  }

  /**
   * 清理所有废弃版本记录
   */
  async clearAllDeprecatedVersions(): Promise<number> {
    this.logOperation('clearAllDeprecatedVersions');
    return await this.deleteWhere({});
  }
}
