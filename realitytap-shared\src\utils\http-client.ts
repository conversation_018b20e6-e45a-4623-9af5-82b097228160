/**
 * 统一的HTTP客户端工具
 * 支持桌面客户端和服务器端的HTTP请求
 */

export interface HttpRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

export interface HttpResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export interface HttpProgressCallback {
  (progress: {
    loaded: number;
    total: number;
    percentage: number;
  }): void;
}

export interface DownloadOptions extends HttpRequestOptions {
  onProgress?: HttpProgressCallback;
  resumable?: boolean;
  rangeStart?: number;
}

/**
 * HTTP客户端基类
 * 提供统一的接口，具体实现由平台特定的子类提供
 */
export abstract class HttpClient {
  protected baseURL: string;
  protected defaultHeaders: Record<string, string>;
  protected defaultTimeout: number;

  constructor(baseURL: string = '', defaultHeaders: Record<string, string> = {}) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'User-Agent': 'RealityTap-Client/1.0.0',
      ...defaultHeaders,
    };
    this.defaultTimeout = 30000; // 30秒
  }

  /**
   * 发送HTTP请求
   */
  abstract request<T = any>(url: string, options?: HttpRequestOptions): Promise<HttpResponse<T>>;

  /**
   * 下载文件
   */
  abstract download(url: string, options?: DownloadOptions): Promise<ArrayBuffer>;

  /**
   * GET请求
   */
  async get<T = any>(url: string, options?: HttpRequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: 'GET' });
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, options?: HttpRequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: 'POST', body: data });
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, options?: HttpRequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: 'PUT', body: data });
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, options?: HttpRequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: 'DELETE' });
  }

  /**
   * 构建完整URL
   */
  protected buildURL(url: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    return `${this.baseURL.replace(/\/$/, '')}/${url.replace(/^\//, '')}`;
  }

  /**
   * 合并请求头
   */
  protected mergeHeaders(options?: HttpRequestOptions): Record<string, string> {
    return {
      ...this.defaultHeaders,
      ...(options?.headers || {}),
    };
  }

  /**
   * 重试逻辑
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    retries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let i = 0; i <= retries; i++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (i === retries) {
          break;
        }

        // 指数退避
        const waitTime = delay * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    throw lastError!;
  }

  /**
   * 检查响应状态
   */
  protected checkStatus(status: number, statusText: string): void {
    if (status >= 400) {
      throw new Error(`HTTP Error ${status}: ${statusText}`);
    }
  }
}

/**
 * HTTP错误类
 */
export class HttpError extends Error {
  public readonly status: number;
  public readonly statusText: string;
  public readonly response?: any;

  constructor(status: number, statusText: string, response?: any) {
    super(`HTTP Error ${status}: ${statusText}`);
    this.name = 'HttpError';
    this.status = status;
    this.statusText = statusText;
    this.response = response;
  }
}

/**
 * 网络错误类
 */
export class NetworkError extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'NetworkError';
  }
}

/**
 * 超时错误类
 */
export class TimeoutError extends Error {
  constructor(timeout: number) {
    super(`Request timeout after ${timeout}ms`);
    this.name = 'TimeoutError';
  }
}

/**
 * 工具函数：创建超时Promise
 */
export function createTimeoutPromise(timeout: number): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => reject(new TimeoutError(timeout)), timeout);
  });
}

/**
 * 工具函数：解析Content-Length
 */
export function parseContentLength(headers: Record<string, string>): number | null {
  const contentLength = headers['content-length'] || headers['Content-Length'];
  if (contentLength) {
    const length = parseInt(contentLength, 10);
    return isNaN(length) ? null : length;
  }
  return null;
}

/**
 * 工具函数：解析Range响应
 */
export function parseContentRange(headers: Record<string, string>): {
  start: number;
  end: number;
  total: number;
} | null {
  const contentRange = headers['content-range'] || headers['Content-Range'];
  if (contentRange) {
    const match = contentRange.match(/bytes (\d+)-(\d+)\/(\d+)/);
    if (match && match[1] && match[2] && match[3]) {
      return {
        start: parseInt(match[1], 10),
        end: parseInt(match[2], 10),
        total: parseInt(match[3], 10),
      };
    }
  }
  return null;
}

/**
 * 浏览器环境的HTTP客户端实现
 */
export class BrowserHttpClient extends HttpClient {
  async request<T = any>(url: string, options: HttpRequestOptions = {}): Promise<HttpResponse<T>> {
    const fullUrl = this.buildURL(url);
    const headers = this.mergeHeaders(options);
    const timeout = options.timeout || this.defaultTimeout;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(fullUrl, {
        method: options.method || 'GET',
        headers,
        body: options.body ? JSON.stringify(options.body) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      this.checkStatus(response.status, response.statusText);

      const data = await response.json() as T;

      // 转换 Headers 对象为普通对象
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new TimeoutError(timeout);
      }
      throw error;
    }
  }

  async download(url: string, options: DownloadOptions = {}): Promise<ArrayBuffer> {
    const fullUrl = this.buildURL(url);
    const headers = this.mergeHeaders(options);
    const timeout = options.timeout || this.defaultTimeout;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      // 支持断点续传
      if (options.resumable && options.rangeStart) {
        headers['Range'] = `bytes=${options.rangeStart}-`;
      }

      const response = await fetch(fullUrl, {
        method: 'GET',
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      this.checkStatus(response.status, response.statusText);

      if (!response.body) {
        throw new Error('Response body is null');
      }

      const contentLength = parseContentLength(Object.fromEntries(response.headers.entries()));
      const totalSize = contentLength || 0;
      let loadedSize = options.rangeStart || 0;

      const reader = response.body.getReader();
      const chunks: Uint8Array[] = [];

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          chunks.push(value);
          loadedSize += value.length;

          // 调用进度回调
          if (options.onProgress) {
            options.onProgress({
              loaded: loadedSize,
              total: totalSize,
              percentage: totalSize > 0 ? Math.round((loadedSize / totalSize) * 100) : 0,
            });
          }
        }
      } finally {
        reader.releaseLock();
      }

      // 合并所有数据块
      const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
      const result = new Uint8Array(totalLength);
      let offset = 0;

      for (const chunk of chunks) {
        result.set(chunk, offset);
        offset += chunk.length;
      }

      return result.buffer;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new TimeoutError(timeout);
      }
      throw error;
    }
  }
}

/**
 * Node.js环境的HTTP客户端实现
 */
export class NodeHttpClient extends HttpClient {
  async request<T = any>(url: string, options: HttpRequestOptions = {}): Promise<HttpResponse<T>> {
    // 动态导入Node.js模块
    const https = await import('https' as any);
    const http = await import('http' as any);

    const fullUrl = this.buildURL(url);
    const urlObj = new URL(fullUrl);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const headers = this.mergeHeaders(options);
    const timeout = options.timeout || this.defaultTimeout;

    return new Promise((resolve, reject) => {
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers,
        timeout,
      };

      const req = client.request(requestOptions, (res: any) => {
        let data = '';

        res.on('data', (chunk: any) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            this.checkStatus(res.statusCode || 0, res.statusMessage || '');

            const responseHeaders: Record<string, string> = {};
            Object.entries(res.headers).forEach(([key, value]) => {
              responseHeaders[key] = Array.isArray(value) ? value.join(', ') : (value as string) || '';
            });

            const response: HttpResponse<T> = {
              data: JSON.parse(data) as T,
              status: res.statusCode || 0,
              statusText: res.statusMessage || '',
              headers: responseHeaders,
            };

            resolve(response);
          } catch (error) {
            reject(error);
          }
        });
      });

      req.on('error', (error: any) => {
        reject(new NetworkError('Request failed', error));
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new TimeoutError(timeout));
      });

      if (options.body) {
        req.write(JSON.stringify(options.body));
      }

      req.end();
    });
  }

  async download(url: string, options: DownloadOptions = {}): Promise<ArrayBuffer> {
    // 动态导入Node.js模块
    const https = await import('https' as any);
    const http = await import('http' as any);

    const fullUrl = this.buildURL(url);
    const urlObj = new URL(fullUrl);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const headers = this.mergeHeaders(options);
    const timeout = options.timeout || this.defaultTimeout;

    // 支持断点续传
    if (options.resumable && options.rangeStart) {
      headers['Range'] = `bytes=${options.rangeStart}-`;
    }

    return new Promise((resolve, reject) => {
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'GET',
        headers,
        timeout,
      };

      const req = client.request(requestOptions, (res: any) => {
        try {
          this.checkStatus(res.statusCode || 0, res.statusMessage || '');
        } catch (error) {
          reject(error);
          return;
        }

        const responseHeaders: Record<string, string> = {};
        Object.entries(res.headers).forEach(([key, value]) => {
          responseHeaders[key] = Array.isArray(value) ? value.join(', ') : (value as string) || '';
        });

        const contentLength = parseContentLength(responseHeaders);
        const totalSize = contentLength || 0;
        let loadedSize = options.rangeStart || 0;
        const chunks: any[] = [];

        res.on('data', (chunk: any) => {
          chunks.push(chunk);
          loadedSize += chunk.length;

          // 调用进度回调
          if (options.onProgress) {
            options.onProgress({
              loaded: loadedSize,
              total: totalSize,
              percentage: totalSize > 0 ? Math.round((loadedSize / totalSize) * 100) : 0,
            });
          }
        });

        res.on('end', () => {
          const Buffer = (globalThis as any).Buffer;
          const buffer = Buffer.concat(chunks);
          resolve(buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength));
        });

        res.on('error', (error: any) => {
          reject(new NetworkError('Download failed', error));
        });
      });

      req.on('error', (error: any) => {
        reject(new NetworkError('Request failed', error));
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new TimeoutError(timeout));
      });

      req.end();
    });
  }
}

/**
 * HTTP客户端工厂函数
 * 根据环境自动选择合适的实现
 */
export function createHttpClient(baseURL?: string, defaultHeaders?: Record<string, string>): HttpClient {
  // 检测环境
  if (typeof globalThis !== 'undefined' && 'window' in globalThis && typeof fetch !== 'undefined') {
    // 浏览器环境
    return new BrowserHttpClient(baseURL, defaultHeaders);
  } else if (typeof (globalThis as any).process !== 'undefined' && (globalThis as any).process.versions?.node) {
    // Node.js 环境
    return new NodeHttpClient(baseURL, defaultHeaders);
  } else {
    throw new Error('Unsupported environment for HTTP client');
  }
}

/**
 * 默认HTTP客户端实例
 */
export const defaultHttpClient = createHttpClient();
