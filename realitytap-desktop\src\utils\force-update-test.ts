/**
 * 强制更新功能测试工具
 * 用于在开发环境中测试强制更新流程
 */

import { useGlobalUpdate } from "@/composables/useGlobalUpdate";
import type { UpdateInfo } from "@/composables/useUpdater";
import { logger, LogModule } from '@/utils/logger/logger';

/**
 * 模拟强制更新数据
 */
const mockForceUpdateInfo: UpdateInfo = {
  version: "2.0.0",
  date: new Date().toISOString(),
  body: "这是一个强制更新测试。\n\n更新内容：\n- 重要安全修复\n- 关键功能改进\n- 必须立即安装",
  force_update: true,
};

/**
 * 模拟普通更新数据
 */
const mockNormalUpdateInfo: UpdateInfo = {
  version: "1.5.0",
  date: new Date().toISOString(),
  body: "这是一个普通更新测试。\n\n更新内容：\n- 新功能添加\n- 性能优化\n- Bug 修复",
  force_update: false,
};

/**
 * 测试强制更新对话框
 */
export function testForceUpdate() {
  logger.info(LogModule.GENERAL, "[测试] 触发强制更新对话框");

  const globalUpdate = useGlobalUpdate();

  // 设置模拟的强制更新信息
  globalUpdate.setUpdateInfo(mockForceUpdateInfo);

  // 显示强制更新对话框
  globalUpdate.showForceUpdate();

  logger.info(LogModule.GENERAL, "[测试] 强制更新对话框已显示");
}

/**
 * 测试普通更新对话框
 */
export function testNormalUpdate() {
  logger.info(LogModule.GENERAL, "[测试] 触发普通更新对话框");

  const globalUpdate = useGlobalUpdate();

  // 设置模拟的普通更新信息
  globalUpdate.setUpdateInfo(mockNormalUpdateInfo);

  // 显示普通更新对话框
  globalUpdate.showUpdate();

  logger.info(LogModule.GENERAL, "[测试] 普通更新对话框已显示");
}

/**
 * 重置更新状态
 */
export function resetUpdateState() {
  logger.info(LogModule.GENERAL, "[测试] 重置更新状态");

  const globalUpdate = useGlobalUpdate();
  globalUpdate.reset();

  logger.info(LogModule.GENERAL, "[测试] 更新状态已重置");
}

/**
 * 在开发环境中启用强制更新测试工具
 */
export function enableForceUpdateTesting() {
  if (import.meta.env.DEV) {
    // 将测试函数添加到全局对象，方便在控制台中调用
    (window as any).forceUpdateTest = {
      testForceUpdate,
      testNormalUpdate,
      resetUpdateState,
    };
    
    logger.info(LogModule.GENERAL, "强制更新测试工具已启用");
    logger.info(LogModule.GENERAL, "可用命令", {
      commands: [
        "forceUpdateTest.testForceUpdate() - 测试强制更新",
        "forceUpdateTest.testNormalUpdate() - 测试普通更新",
        "forceUpdateTest.resetUpdateState() - 重置状态"
      ]
    });
  }
}
