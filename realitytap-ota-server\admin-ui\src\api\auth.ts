import request from '@/utils/request'
import type { 
  BaseResponse, 
  LoginRequest, 
  LoginResponse, 
  RefreshTokenResponse,
  AdminUser 
} from './types'

export const authApi = {
  // 管理员登录
  login: (data: LoginRequest): Promise<BaseResponse<LoginResponse>> => {
    return request.post('/admin/login', data).then(res => res.data)
  },

  // 管理员登出
  logout: (): Promise<BaseResponse> => {
    return request.post('/admin/logout').then(res => res.data)
  },

  // 获取管理员信息
  getProfile: (): Promise<BaseResponse<{ user: AdminUser; serverInfo: any }>> => {
    return request.get('/admin/profile').then(res => res.data)
  },

  // 刷新token
  refreshToken: (): Promise<BaseResponse<RefreshTokenResponse>> => {
    return request.post('/admin/refresh').then(res => res.data)
  },
}
