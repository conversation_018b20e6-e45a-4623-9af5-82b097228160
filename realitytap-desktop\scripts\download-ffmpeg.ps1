# FFmpeg Download Script
# This script downloads FFmpeg binaries for different platforms

param(
    [string]$Platform = "all",
    [switch]$Force = $false
)

$ErrorActionPreference = "Stop"

# FFmpeg version and download URL
$BASE_URL = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest"

# Target directory
$FFMPEG_DIR = Join-Path $PSScriptRoot "..\src-tauri\ffmpeg"

# Platform configuration
$PLATFORMS = @{
    "linux-x64" = @{
        "url" = "$BASE_URL/ffmpeg-master-latest-linux64-gpl.tar.xz"
        "dir" = "linux/x64"
        "binaries" = @("ffmpeg", "ffprobe")
    }
    "linux-arm64" = @{
        "url" = "$BASE_URL/ffmpeg-master-latest-linuxarm64-gpl.tar.xz"
        "dir" = "linux/arm64"
        "binaries" = @("ffmpeg", "ffprobe")
    }
    "macos" = @{
        "url" = "$BASE_URL/ffmpeg-master-latest-macos64-gpl.tar.xz"
        "dir" = "macos"
        "binaries" = @("ffmpeg", "ffprobe")
    }
}

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Download-And-Extract {
    param(
        [string]$Url,
        [string]$TargetDir,
        [array]$Binaries
    )
    
    $tempDir = Join-Path $env:TEMP "ffmpeg-download-$(Get-Random)"
    $archiveFile = Join-Path $tempDir "ffmpeg.tar.xz"
    
    try {
        # 创建临时目录
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
        
        Write-Info "下载 FFmpeg 从 $Url"
        Invoke-WebRequest -Uri $Url -OutFile $archiveFile -UseBasicParsing
        
        # 检查是否有 7zip 或 tar 命令
        if (Test-Command "7z") {
            Write-Info "使用 7zip 解压文件"
            & 7z x $archiveFile -o"$tempDir" -y | Out-Null
            $extractedDir = Get-ChildItem -Path $tempDir -Directory | Where-Object { $_.Name -like "*ffmpeg*" } | Select-Object -First 1
        }
        elseif (Test-Command "tar") {
            Write-Info "使用 tar 解压文件"
            & tar -xf $archiveFile -C $tempDir
            $extractedDir = Get-ChildItem -Path $tempDir -Directory | Where-Object { $_.Name -like "*ffmpeg*" } | Select-Object -First 1
        }
        else {
            throw "需要 7zip 或 tar 命令来解压文件。请安装其中一个。"
        }
        
        if (-not $extractedDir) {
            throw "Cannot find extracted FFmpeg directory"
        }
        
        $binDir = Join-Path $extractedDir.FullName "bin"
        if (-not (Test-Path $binDir)) {
            throw "无法找到 FFmpeg bin 目录"
        }
        
        # 复制二进制文件
        foreach ($binary in $Binaries) {
            $sourcePath = Join-Path $binDir $binary
            $targetPath = Join-Path $TargetDir $binary
            
            if (Test-Path $sourcePath) {
                Write-Info "复制 $binary 到 $targetPath"
                Copy-Item $sourcePath $targetPath -Force
            }
            else {
                Write-Warning "未找到 $binary 在 $sourcePath"
            }
        }
        
        Write-Info "成功下载并提取 FFmpeg 二进制文件到 $TargetDir"
    }
    finally {
        # 清理临时文件
        if (Test-Path $tempDir) {
            Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
}

function Main {
    Write-Info "开始下载 FFmpeg 二进制文件"
    
    # 检查目标目录是否存在
    if (-not (Test-Path $FFMPEG_DIR)) {
        New-Item -ItemType Directory -Path $FFMPEG_DIR -Force | Out-Null
    }
    
    # 确定要下载的平台
    $platformsToDownload = @()
    if ($Platform -eq "all") {
        $platformsToDownload = $PLATFORMS.Keys
    }
    elseif ($PLATFORMS.ContainsKey($Platform)) {
        $platformsToDownload = @($Platform)
    }
    else {
        Write-Error "不支持的平台: $Platform. 支持的平台: $($PLATFORMS.Keys -join ', '), all"
        exit 1
    }
    
    foreach ($platformKey in $platformsToDownload) {
        $config = $PLATFORMS[$platformKey]
        $targetDir = Join-Path $FFMPEG_DIR $config.dir
        
        # 检查是否已存在
        $allBinariesExist = $true
        foreach ($binary in $config.binaries) {
            $binaryPath = Join-Path $targetDir $binary
            if (-not (Test-Path $binaryPath)) {
                $allBinariesExist = $false
                break
            }
        }
        
        if ($allBinariesExist -and -not $Force) {
            Write-Info "平台 $platformKey 的 FFmpeg 二进制文件已存在，跳过下载"
            continue
        }
        
        try {
            Write-Info "下载平台 $platformKey 的 FFmpeg"
            Download-And-Extract -Url $config.url -TargetDir $targetDir -Binaries $config.binaries
        }
        catch {
            Write-Error "下载平台 $platformKey 失败: $($_.Exception.Message)"
            exit 1
        }
    }
    
    Write-Info "所有 FFmpeg 二进制文件下载完成"
}

# 运行主函数
Main
