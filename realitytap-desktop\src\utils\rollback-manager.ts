/**
 * 回滚管理器
 * 负责管理更新失败时的回滚操作和版本恢复
 */

import { invoke } from '@tauri-apps/api/core';
import type {
  RollbackInfo,
  RollbackStrategy,
  BackupInfo,
  RestorePoint,
  RollbackResult,
  UpdateError,
} from '../../../realitytap-shared/src/index';

/**
 * 回滚管理器类
 */
export class RollbackManager {
  private backups: BackupInfo[] = [];
  private restorePoints: RestorePoint[] = [];
  private maxBackups = 5;
  private maxRestorePoints = 10;

  /**
   * 创建备份
   */
  async createBackup(version: string, description?: string): Promise<BackupInfo> {
    try {
      const backupInfo: BackupInfo = {
        id: this.generateBackupId(),
        version,
        description: description || `Backup for version ${version}`,
        timestamp: new Date(),
        size: 0,
        path: '',
        checksum: '',
        type: 'full',
      };

      // 调用Tauri命令创建备份
      const result = await invoke<{
        path: string;
        size: number;
        checksum: string;
      }>('create_backup', {
        backupId: backupInfo.id,
        version,
        description: backupInfo.description,
      });

      backupInfo.path = result.path;
      backupInfo.size = result.size;
      backupInfo.checksum = result.checksum;

      this.addBackup(backupInfo);
      return backupInfo;
    } catch (error) {
      throw new Error(`Failed to create backup: ${error}`);
    }
  }

  /**
   * 创建还原点
   */
  async createRestorePoint(
    version: string,
    type: 'pre_update' | 'post_update' | 'manual',
    description?: string
  ): Promise<RestorePoint> {
    try {
      const restorePoint: RestorePoint = {
        id: this.generateRestorePointId(),
        version,
        type,
        description: description || `Restore point for ${type}`,
        timestamp: new Date(),
        configSnapshot: {},
        fileHashes: {},
        registrySnapshot: {},
      };

      // 调用Tauri命令创建还原点
      const result = await invoke<{
        configSnapshot: Record<string, any>;
        fileHashes: Record<string, string>;
        registrySnapshot: Record<string, any>;
      }>('create_restore_point', {
        restorePointId: restorePoint.id,
        version,
        type,
        description: restorePoint.description,
      });

      restorePoint.configSnapshot = result.configSnapshot;
      restorePoint.fileHashes = result.fileHashes;
      restorePoint.registrySnapshot = result.registrySnapshot;

      this.addRestorePoint(restorePoint);
      return restorePoint;
    } catch (error) {
      throw new Error(`Failed to create restore point: ${error}`);
    }
  }

  /**
   * 执行回滚
   */
  async performRollback(
    strategy: RollbackStrategy,
    targetVersion?: string
  ): Promise<RollbackResult> {
    try {
      const rollbackInfo: RollbackInfo = {
        strategy,
        targetVersion: targetVersion || this.getLastKnownGoodVersion(),
        timestamp: new Date(),
        reason: 'Manual rollback',
        backupUsed: null,
        restorePointUsed: null,
      };

      let result: RollbackResult;

      switch (strategy) {
        case 'backup_restore':
          result = await this.rollbackFromBackup(rollbackInfo);
          break;
        case 'restore_point':
          result = await this.rollbackFromRestorePoint(rollbackInfo);
          break;
        case 'config_only':
          result = await this.rollbackConfigOnly(rollbackInfo);
          break;
        case 'reinstall':
          result = await this.rollbackByReinstall(rollbackInfo);
          break;
        default:
          throw new Error(`Unsupported rollback strategy: ${strategy}`);
      }

      return result;
    } catch (error) {
      return {
        success: false,
        rollbackInfo: {
          strategy,
          targetVersion: targetVersion || 'unknown',
          timestamp: new Date(),
          reason: 'Rollback failed',
          backupUsed: null,
          restorePointUsed: null,
        },
        error: error as UpdateError,
        duration: 0,
      };
    }
  }

  /**
   * 从备份回滚
   */
  private async rollbackFromBackup(rollbackInfo: RollbackInfo): Promise<RollbackResult> {
    const startTime = Date.now();

    try {
      // 查找目标版本的备份
      const backup = this.findBackupByVersion(rollbackInfo.targetVersion);
      if (!backup) {
        throw new Error(`No backup found for version ${rollbackInfo.targetVersion}`);
      }

      // 验证备份完整性
      const isValid = await this.verifyBackup(backup);
      if (!isValid) {
        throw new Error(`Backup verification failed for ${backup.id}`);
      }

      // 执行备份恢复
      await invoke('restore_from_backup', {
        backupId: backup.id,
        backupPath: backup.path,
      });

      rollbackInfo.backupUsed = backup;

      return {
        success: true,
        rollbackInfo,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        rollbackInfo,
        error: error as UpdateError,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * 从还原点回滚
   */
  private async rollbackFromRestorePoint(rollbackInfo: RollbackInfo): Promise<RollbackResult> {
    const startTime = Date.now();

    try {
      // 查找目标版本的还原点
      const restorePoint = this.findRestorePointByVersion(rollbackInfo.targetVersion);
      if (!restorePoint) {
        throw new Error(`No restore point found for version ${rollbackInfo.targetVersion}`);
      }

      // 执行还原点恢复
      await invoke('restore_from_restore_point', {
        restorePointId: restorePoint.id,
        configSnapshot: restorePoint.configSnapshot,
        fileHashes: restorePoint.fileHashes,
        registrySnapshot: restorePoint.registrySnapshot,
      });

      rollbackInfo.restorePointUsed = restorePoint;

      return {
        success: true,
        rollbackInfo,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        rollbackInfo,
        error: error as UpdateError,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * 仅回滚配置
   */
  private async rollbackConfigOnly(rollbackInfo: RollbackInfo): Promise<RollbackResult> {
    const startTime = Date.now();

    try {
      // 查找配置还原点
      const restorePoint = this.findRestorePointByVersion(rollbackInfo.targetVersion);
      if (!restorePoint) {
        throw new Error(`No restore point found for version ${rollbackInfo.targetVersion}`);
      }

      // 仅恢复配置
      await invoke('restore_config_only', {
        configSnapshot: restorePoint.configSnapshot,
      });

      rollbackInfo.restorePointUsed = restorePoint;

      return {
        success: true,
        rollbackInfo,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        rollbackInfo,
        error: error as UpdateError,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * 通过重新安装回滚
   */
  private async rollbackByReinstall(rollbackInfo: RollbackInfo): Promise<RollbackResult> {
    const startTime = Date.now();

    try {
      // 执行重新安装
      await invoke('reinstall_version', {
        targetVersion: rollbackInfo.targetVersion,
      });

      return {
        success: true,
        rollbackInfo,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        rollbackInfo,
        error: error as UpdateError,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * 验证备份完整性
   */
  async verifyBackup(backup: BackupInfo): Promise<boolean> {
    try {
      const result = await invoke<boolean>('verify_backup', {
        backupPath: backup.path,
        expectedChecksum: backup.checksum,
      });
      return result;
    } catch (error) {
      console.error('Backup verification failed:', error);
      return false;
    }
  }

  /**
   * 清理旧备份
   */
  async cleanupOldBackups(): Promise<void> {
    if (this.backups.length <= this.maxBackups) {
      return;
    }

    // 按时间排序，保留最新的备份
    const sortedBackups = [...this.backups].sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );

    const backupsToRemove = sortedBackups.slice(this.maxBackups);

    for (const backup of backupsToRemove) {
      try {
        await invoke('remove_backup', { backupId: backup.id });
        this.removeBackup(backup.id);
      } catch (error) {
        console.error(`Failed to remove backup ${backup.id}:`, error);
      }
    }
  }

  /**
   * 清理旧还原点
   */
  async cleanupOldRestorePoints(): Promise<void> {
    if (this.restorePoints.length <= this.maxRestorePoints) {
      return;
    }

    // 按时间排序，保留最新的还原点
    const sortedRestorePoints = [...this.restorePoints].sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );

    const restorePointsToRemove = sortedRestorePoints.slice(this.maxRestorePoints);

    for (const restorePoint of restorePointsToRemove) {
      try {
        await invoke('remove_restore_point', { restorePointId: restorePoint.id });
        this.removeRestorePoint(restorePoint.id);
      } catch (error) {
        console.error(`Failed to remove restore point ${restorePoint.id}:`, error);
      }
    }
  }

  /**
   * 获取可用的回滚选项
   */
  getAvailableRollbackOptions(): {
    strategies: RollbackStrategy[];
    versions: string[];
    backups: BackupInfo[];
    restorePoints: RestorePoint[];
  } {
    const versions = new Set<string>();
    
    this.backups.forEach(backup => versions.add(backup.version));
    this.restorePoints.forEach(point => versions.add(point.version));

    const strategies: RollbackStrategy[] = [];
    
    if (this.backups.length > 0) {
      strategies.push('backup_restore');
    }
    
    if (this.restorePoints.length > 0) {
      strategies.push('restore_point', 'config_only');
    }
    
    strategies.push('reinstall');

    return {
      strategies,
      versions: Array.from(versions).sort(),
      backups: [...this.backups],
      restorePoints: [...this.restorePoints],
    };
  }

  /**
   * 获取推荐的回滚策略
   */
  getRecommendedRollbackStrategy(error?: UpdateError): RollbackStrategy {
    if (!error) {
      return this.backups.length > 0 ? 'backup_restore' : 'reinstall';
    }

    // 根据错误类型推荐策略
    switch (error.code) {
      case 'INSTALL_FAILED':
      case 'FILE_CORRUPTED':
        return this.backups.length > 0 ? 'backup_restore' : 'reinstall';
      
      case 'SYSTEM_ERROR':
      case 'PERMISSION_DENIED':
        return this.restorePoints.length > 0 ? 'config_only' : 'restore_point';
      
      default:
        return this.backups.length > 0 ? 'backup_restore' : 'reinstall';
    }
  }

  // 私有辅助方法
  private addBackup(backup: BackupInfo): void {
    this.backups.unshift(backup);
    this.cleanupOldBackups();
  }

  private removeBackup(backupId: string): void {
    this.backups = this.backups.filter(backup => backup.id !== backupId);
  }

  private addRestorePoint(restorePoint: RestorePoint): void {
    this.restorePoints.unshift(restorePoint);
    this.cleanupOldRestorePoints();
  }

  private removeRestorePoint(restorePointId: string): void {
    this.restorePoints = this.restorePoints.filter(point => point.id !== restorePointId);
  }

  private findBackupByVersion(version: string): BackupInfo | null {
    return this.backups.find(backup => backup.version === version) || null;
  }

  private findRestorePointByVersion(version: string): RestorePoint | null {
    return this.restorePoints.find(point => point.version === version) || null;
  }

  private getLastKnownGoodVersion(): string {
    // 从还原点或备份中获取最后一个已知良好版本
    const allVersions = [
      ...this.backups.map(b => b.version),
      ...this.restorePoints.map(p => p.version),
    ];
    
    return allVersions[0] || '1.0.0';
  }

  private generateBackupId(): string {
    return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRestorePointId(): string {
    return `restore_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取备份列表
   */
  getBackups(): BackupInfo[] {
    return [...this.backups];
  }

  /**
   * 获取还原点列表
   */
  getRestorePoints(): RestorePoint[] {
    return [...this.restorePoints];
  }

  /**
   * 获取存储使用情况
   */
  getStorageUsage(): {
    totalBackupSize: number;
    backupCount: number;
    restorePointCount: number;
    oldestBackup: Date | null;
    newestBackup: Date | null;
  } {
    const totalBackupSize = this.backups.reduce((sum, backup) => sum + backup.size, 0);
    const backupDates = this.backups.map(b => b.timestamp);
    
    return {
      totalBackupSize,
      backupCount: this.backups.length,
      restorePointCount: this.restorePoints.length,
      oldestBackup: backupDates.length > 0 ? new Date(Math.min(...backupDates.map(d => d.getTime()))) : null,
      newestBackup: backupDates.length > 0 ? new Date(Math.max(...backupDates.map(d => d.getTime()))) : null,
    };
  }
}

/**
 * 全局回滚管理器实例
 */
export const globalRollbackManager = new RollbackManager();
