import type { Ref } from "vue";
import { DEBUG_EXPERIMENTS } from "../config/waveform-constants";
import { waveformLogger } from "@/utils/logger/logger";

// 使用 ReturnType 来获取正确的 Store 类型
type FileWaveformEditorStore = ReturnType<typeof import("@/stores/haptics-editor-store").useFileWaveformEditorStore>;

/**
 * 波形图渲染管道 Composable
 * 管理渲染管道和优化逻辑，减少主组件的复杂度
 */

export interface RenderingPipelineConfig {
  // Store 和状态
  waveformStore: FileWaveformEditorStore;
  isDragging: Ref<boolean>;
  
  // 异步管理器
  asyncManager: {
    createThrottle: <T extends (...args: any[]) => any>(fn: T, delay: number, description?: string) => { fn: T };
  };
  
  // 绘制函数
  drawWaveformMain: (forceRedraw?: boolean) => void;
  drawWaveformLightweight: (forceRedraw?: boolean) => void;
}

export interface RenderingFunctions {
  drawWaveform: (forceRedraw?: boolean) => void;
  throttledDrawWaveform: (forceRedraw?: boolean) => void;
  dragDrawWaveform: (forceRedraw?: boolean) => void;
  propertyAdjustDrawWaveform: (forceRedraw?: boolean) => void;
  smartDrawWaveform: (forceRedraw?: boolean) => void;
}

export function useWaveformRenderingPipeline(config: RenderingPipelineConfig): RenderingFunctions {
  
  // 基础绘制函数
  const drawWaveform = (forceRedraw: boolean = false) => {
    if (config.drawWaveformMain) {
      config.drawWaveformMain(forceRedraw);
    }
  };

  // 节流版本的重绘函数配置
  let throttledDrawWaveform: (forceRedraw?: boolean) => void;
  if (DEBUG_EXPERIMENTS.DISABLE_THROTTLING) {
    waveformLogger.debug("🔍 实验5: 禁用节流机制，直接调用主绘制函数");
    throttledDrawWaveform = drawWaveform;
  } else {
    const throttledDrawWaveformWrapper = config.asyncManager.createThrottle(
      (forceRedraw: boolean = false) => {
        drawWaveform(forceRedraw);
      },
      32, // 约30fps的重绘频率
      "标准重绘节流"
    );
    throttledDrawWaveform = throttledDrawWaveformWrapper.fn as (forceRedraw?: boolean) => void;
  }

  // 拖动时使用更高频率的重绘函数
  const dragDrawWaveform = (forceRedraw: boolean = false) => {
    drawWaveform(forceRedraw);
  }; // 拖动时不使用节流，确保最流畅的体验

  // 属性调整专用的轻量级重绘函数
  const propertyAdjustDrawWaveformWrapper = config.asyncManager.createThrottle(
    (forceRedraw: boolean = false) => {
      config.drawWaveformLightweight(forceRedraw);
    },
    16, // 60fps 高响应频率
    "属性调整重绘节流"
  );
  const propertyAdjustDrawWaveform = propertyAdjustDrawWaveformWrapper.fn as (forceRedraw?: boolean) => void;

  // 简化的重绘调度函数
  // 由于智能调度功能已被禁用并移除，直接使用基础绘制函数
  const smartDrawWaveform = (forceRedraw: boolean = false) => {
    drawWaveform(forceRedraw);
  };

  return {
    drawWaveform,
    throttledDrawWaveform,
    dragDrawWaveform,
    propertyAdjustDrawWaveform,
    smartDrawWaveform,
  };
}
