/**
 * 波形图UI状态同步管理
 * 实现Chrome标签页级别的UI状态缓存和恢复
 */

import { ref, watch, onBeforeUnmount } from "vue";
import { fileEditorStateManager } from "@/stores/haptics-editor-store";
import { logger, LogModule } from "@/utils/logger/logger";

// UI状态接口
export interface WaveformUIState {
  scrollLeft: number;
  scrollTop: number;
  zoomLevel: number;
  viewportStart: number;
  viewportEnd: number;
  containerWidth: number;
  containerHeight: number;
}

// 状态同步配置
export interface UIStateSyncConfig {
  fileUuid: string;
  enableAutoSave?: boolean;
  saveInterval?: number; // 自动保存间隔（毫秒）
  debugMode?: boolean;
}

/**
 * 波形图UI状态同步Composable
 * 提供自动保存/恢复UI交互状态的功能
 */
export function useWaveformUIStateSync(config: UIStateSyncConfig) {
  const { fileUuid, enableAutoSave = true, saveInterval = 500, debugMode = false } = config;

  // 当前UI状态
  const currentUIState = ref<WaveformUIState>({
    scrollLeft: 0,
    scrollTop: 0,
    zoomLevel: 1,
    viewportStart: 0,
    viewportEnd: 1000,
    containerWidth: 800,
    containerHeight: 400,
  });

  // 状态变化标记
  const hasUnsavedChanges = ref(false);
  let saveTimer: ReturnType<typeof setTimeout> | null = null;

  // 保存UI状态到文件状态管理器
  const saveUIState = (state?: Partial<WaveformUIState>): void => {
    try {
      const stateToSave = state ? { ...currentUIState.value, ...state } : currentUIState.value;

      fileEditorStateManager.updateUIState(fileUuid, {
        canvasScrollLeft: stateToSave.scrollLeft,
        canvasScrollTop: stateToSave.scrollTop,
        zoomLevel: stateToSave.zoomLevel,
        viewportStart: stateToSave.viewportStart,
        viewportEnd: stateToSave.viewportEnd,
        waveformContainerWidth: stateToSave.containerWidth,
        waveformContainerHeight: stateToSave.containerHeight,
      });

      hasUnsavedChanges.value = false;

      if (debugMode) {
        logger.debug(LogModule.PROJECT, "保存UI状态", { fileUuid, state: stateToSave });
      }
    } catch (error) {
      logger.error(LogModule.PROJECT, "保存UI状态失败", { fileUuid, error });
    }
  };

  // 从文件状态管理器恢复UI状态
  const restoreUIState = (): WaveformUIState => {
    try {
      const fileState = fileEditorStateManager.getFileState(fileUuid);

      const restoredState: WaveformUIState = {
        scrollLeft: fileState.canvasScrollLeft,
        scrollTop: fileState.canvasScrollTop,
        zoomLevel: fileState.zoomLevel,
        viewportStart: fileState.viewportStart,
        viewportEnd: fileState.viewportEnd,
        containerWidth: fileState.waveformContainerWidth,
        containerHeight: fileState.waveformContainerHeight,
      };

      // 更新当前状态
      currentUIState.value = { ...restoredState };

      if (debugMode) {
        logger.debug(LogModule.PROJECT, "恢复UI状态", { fileUuid, state: restoredState });
      }

      return restoredState;
    } catch (error) {
      logger.error(LogModule.PROJECT, "恢复UI状态失败", { fileUuid, error });
      return currentUIState.value;
    }
  };

  // 更新UI状态（带防抖保存）
  const updateUIState = (updates: Partial<WaveformUIState>): void => {
    // 更新当前状态
    Object.assign(currentUIState.value, updates);
    hasUnsavedChanges.value = true;

    if (debugMode) {
      logger.debug(LogModule.PROJECT, "更新UI状态", { fileUuid, updates });
    }

    // 防抖保存
    if (enableAutoSave) {
      if (saveTimer) {
        clearTimeout(saveTimer);
      }
      saveTimer = setTimeout(() => {
        saveUIState();
      }, saveInterval);
    }
  };

  // 立即保存（跳过防抖）
  const saveImmediately = (): void => {
    if (saveTimer) {
      clearTimeout(saveTimer);
      saveTimer = null;
    }
    saveUIState();
  };

  // 重置UI状态到默认值
  const resetUIState = (): void => {
    currentUIState.value = {
      scrollLeft: 0,
      scrollTop: 0,
      zoomLevel: 1,
      viewportStart: 0,
      viewportEnd: 1000,
      containerWidth: 800,
      containerHeight: 400,
    };
    saveUIState();

    if (debugMode) {
      logger.debug(LogModule.PROJECT, "重置UI状态", { fileUuid });
    }
  };

  // 创建响应式的状态更新器
  const createStateUpdater = <K extends keyof WaveformUIState>(key: K) => {
    return (value: WaveformUIState[K]) => {
      updateUIState({ [key]: value } as Partial<WaveformUIState>);
    };
  };

  // 便捷的状态更新器
  const stateUpdaters = {
    setScrollLeft: createStateUpdater("scrollLeft"),
    setScrollTop: createStateUpdater("scrollTop"),
    setZoomLevel: createStateUpdater("zoomLevel"),
    setViewportStart: createStateUpdater("viewportStart"),
    setViewportEnd: createStateUpdater("viewportEnd"),
    setContainerWidth: createStateUpdater("containerWidth"),
    setContainerHeight: createStateUpdater("containerHeight"),

    // 批量更新
    setScrollPosition: (left: number, top: number) => {
      updateUIState({ scrollLeft: left, scrollTop: top });
    },

    setViewport: (start: number, end: number) => {
      updateUIState({ viewportStart: start, viewportEnd: end });
    },

    setContainerSize: (width: number, height: number) => {
      updateUIState({ containerWidth: width, containerHeight: height });
    },
  };

  // 监听状态变化（用于调试）
  if (debugMode) {
    watch(
      currentUIState,
      (newState, oldState) => {
        logger.debug(LogModule.PROJECT, "UI状态变化", {
          fileUuid,
          from: oldState,
          to: newState,
        });
      },
      { deep: true }
    );
  }

  // 组件卸载时保存状态
  onBeforeUnmount(() => {
    if (hasUnsavedChanges.value) {
      saveImmediately();
    }
    if (saveTimer) {
      clearTimeout(saveTimer);
    }
  });

  return {
    // 状态
    currentUIState: currentUIState.value,
    hasUnsavedChanges,

    // 核心方法
    saveUIState,
    restoreUIState,
    updateUIState,
    saveImmediately,
    resetUIState,

    // 便捷更新器
    ...stateUpdaters,

    // 工具方法
    getState: () => ({ ...currentUIState.value }),
    setState: (state: WaveformUIState) => {
      currentUIState.value = { ...state };
      saveUIState();
    },

    // 状态比较（高效的浅比较，避免JSON序列化开销）
    hasStateChanged: (compareState: WaveformUIState): boolean => {
      const current = currentUIState.value;
      return (
        current.scrollLeft !== compareState.scrollLeft ||
        current.scrollTop !== compareState.scrollTop ||
        current.zoomLevel !== compareState.zoomLevel ||
        current.viewportStart !== compareState.viewportStart ||
        current.viewportEnd !== compareState.viewportEnd ||
        current.containerWidth !== compareState.containerWidth ||
        current.containerHeight !== compareState.containerHeight
      );
    },
  };
}

/**
 * 创建UI状态同步的响应式引用
 * 用于在模板中直接绑定状态值
 */
export function createUIStateRefs(uiStateSync: ReturnType<typeof useWaveformUIStateSync>) {
  return {
    scrollLeft: ref(uiStateSync.currentUIState.scrollLeft),
    scrollTop: ref(uiStateSync.currentUIState.scrollTop),
    zoomLevel: ref(uiStateSync.currentUIState.zoomLevel),
    viewportStart: ref(uiStateSync.currentUIState.viewportStart),
    viewportEnd: ref(uiStateSync.currentUIState.viewportEnd),
    containerWidth: ref(uiStateSync.currentUIState.containerWidth),
    containerHeight: ref(uiStateSync.currentUIState.containerHeight),
  };
}

/**
 * UI状态同步钩子工厂
 * 简化在组件中的使用
 */
export function createUIStateSyncHook(fileUuid: string, options?: Partial<UIStateSyncConfig>) {
  return () =>
    useWaveformUIStateSync({
      fileUuid,
      ...options,
    });
}
