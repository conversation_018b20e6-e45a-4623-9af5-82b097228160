# RealityTap OTA Server 数据包结构文档

## 概述

本文档详细描述了 RealityTap OTA 更新服务器的数据包结构，包括 API 请求/响应格式、数据库表结构、配置文件格式以及内部数据传输结构。

## 目录

- [API 数据包结构](#api-数据包结构)
- [数据库表结构](#数据库表结构)
- [配置数据结构](#配置数据结构)
- [文件上传数据结构](#文件上传数据结构)
- [统计数据结构](#统计数据结构)
- [错误响应结构](#错误响应结构)

---

## API 数据包结构

### 1. Tauri Plugin Updater 兼容 API

#### 更新检查请求
```http
GET /api/v1/updates/{target}/{current_version}
```

**路径参数:**
- `target`: 目标平台 (如: `windows-x86_64`, `darwin-x86_64`, `linux-x86_64`)
- `current_version`: 当前版本号 (如: `1.0.0`)

#### 更新检查响应 (有更新)
```json
{
  "url": "http://localhost:3000/api/v1/download/realitytap-1.1.0-windows-x86_64.msi",
  "version": "1.1.0",
  "notes": "修复了一些已知问题，提升了性能",
  "pub_date": "2024-01-15T10:30:00Z",
  "signature": "dW50cnVzdGVkIGNvbW1lbnQ6IHNpZ25hdHVyZSBmcm9tIHRhdXJpIHNlY3JldCBrZXkK...",
  "force_update": false
}
```

#### 无更新响应
```http
HTTP/1.1 204 No Content
```

### 2. 版本管理 API

#### 版本检查请求 (兼容性 API)
```http
POST /api/v1/version/check
Content-Type: application/json

{
  "currentVersion": "1.0.0",
  "platform": "windows",
  "architecture": "x86_64",
  "channel": "stable",
  "locale": "zh-CN"
}
```

#### 版本检查响应
```json
{
  "hasUpdate": true,
  "latestVersion": "1.1.0",
  "downloadUrl": "http://localhost:3000/api/v1/download/realitytap-1.1.0-windows-x86_64.msi",
  "releaseNotes": "修复了一些已知问题，提升了性能",
  "fileSize": 52428800,
  "checksum": "sha256:a1b2c3d4e5f6...",
  "isForced": false,
  "signature": "dW50cnVzdGVkIGNvbW1lbnQ6...",
  "releaseDate": "2024-01-15T10:30:00Z"
}
```

#### 获取可用版本
```http
GET /api/v1/version/available
```

```json
{
  "success": true,
  "data": {
    "stable": {
      "version": "1.1.0",
      "platforms": {
        "windows": {
          "x86_64": {
            "filename": "realitytap-1.1.0-windows-x86_64.msi",
            "size": 52428800,
            "checksum": "sha256:a1b2c3d4e5f6...",
            "releaseDate": "2024-01-15T10:30:00Z",
            "releaseNotes": "修复了一些已知问题，提升了性能",
            "signature": "dW50cnVzdGVkIGNvbW1lbnQ6..."
          }
        }
      }
    }
  },
  "timestamp": "2024-01-15T12:00:00Z",
  "version": "1.0.0"
}
```

### 3. 文件下载 API

#### 文件下载请求
```http
GET /api/v1/download/{filename}
Range: bytes=0-1023  # 可选，支持断点续传
```

#### 文件下载响应
```http
HTTP/1.1 200 OK
Content-Type: application/octet-stream
Content-Length: 52428800
Content-Disposition: attachment; filename="realitytap-1.1.0-windows-x86_64.msi"
Accept-Ranges: bytes
ETag: "a1b2c3d4e5f6..."

[二进制文件内容]
```

### 4. 健康检查 API

#### 健康检查请求
```http
GET /health
```

#### 健康检查响应
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T12:00:00Z",
  "uptime": 3600,
  "version": "1.0.0",
  "storage": {
    "available": true,
    "freeSpace": **********0,
    "totalSpace": **********00
  },
  "memory": {
    "used": 134217728,
    "total": **********,
    "percentage": 12.5
  }
}
```

---

## 数据库表结构

### 1. 渠道表 (channels)
```sql
CREATE TABLE channels (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name VARCHAR(50) UNIQUE NOT NULL,           -- 渠道名称 (stable/beta/alpha)
  enabled BOOLEAN NOT NULL DEFAULT 1,         -- 是否启用
  description TEXT,                           -- 渠道描述
  auto_update BOOLEAN NOT NULL DEFAULT 1,     -- 是否自动更新
  rollout_percentage INTEGER NOT NULL DEFAULT 100, -- 推出百分比
  priority INTEGER NOT NULL DEFAULT 1,        -- 优先级
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 版本表 (versions)
```sql
CREATE TABLE versions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  channel_id INTEGER NOT NULL,               -- 关联渠道ID
  version VARCHAR(50) NOT NULL,               -- 版本号
  force_update BOOLEAN NOT NULL DEFAULT 0,   -- 是否强制更新
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (channel_id) REFERENCES channels(id) ON DELETE CASCADE,
  UNIQUE(channel_id, version)
);
```

### 3. 平台发布表 (platform_releases)
```sql
CREATE TABLE platform_releases (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  version_id INTEGER NOT NULL,               -- 关联版本ID
  platform VARCHAR(20) NOT NULL,             -- 平台 (windows/macos/linux)
  architecture VARCHAR(20) NOT NULL,         -- 架构 (x86_64/aarch64/x86)
  filename VARCHAR(255) NOT NULL,            -- 文件名
  file_size BIGINT NOT NULL,                 -- 文件大小
  checksum VARCHAR(128) NOT NULL,            -- 文件校验和
  signature TEXT,                            -- 数字签名
  release_date DATETIME NOT NULL,            -- 发布日期
  release_notes TEXT,                        -- 发布说明
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (version_id) REFERENCES versions(id) ON DELETE CASCADE,
  UNIQUE(version_id, platform, architecture)
);
```

### 4. 最低版本要求表 (minimum_versions)
```sql
CREATE TABLE minimum_versions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  platform VARCHAR(20) NOT NULL,             -- 平台
  architecture VARCHAR(20) NOT NULL,         -- 架构
  minimum_version VARCHAR(50) NOT NULL,      -- 最低版本要求
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(platform, architecture)
);
```

### 5. 废弃版本表 (deprecated_versions)
```sql
CREATE TABLE deprecated_versions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  version VARCHAR(50) UNIQUE NOT NULL,       -- 废弃的版本号
  deprecated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  reason TEXT                                -- 废弃原因
);
```

### 6. 配置变更日志表 (config_change_logs)
```sql
CREATE TABLE config_change_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  config_key VARCHAR(100) NOT NULL,          -- 配置键
  old_value TEXT,                            -- 旧值
  new_value TEXT,                            -- 新值
  user_id VARCHAR(50),                       -- 操作用户ID
  change_reason TEXT,                        -- 变更原因
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 7. 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  config_key VARCHAR(100) UNIQUE NOT NULL,   -- 配置键
  config_value TEXT NOT NULL,                -- 配置值 (JSON格式)
  config_type VARCHAR(20) NOT NULL DEFAULT 'string', -- 配置类型
  description TEXT,                          -- 配置描述
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 8. 下载记录表 (download_records)
```sql
CREATE TABLE download_records (
  id VARCHAR(36) PRIMARY KEY,                -- UUID
  filename VARCHAR(255) NOT NULL,            -- 下载文件名
  version VARCHAR(50) NOT NULL,              -- 版本号
  platform VARCHAR(20) NOT NULL,            -- 平台
  architecture VARCHAR(20) NOT NULL,        -- 架构
  channel VARCHAR(50) NOT NULL,             -- 渠道
  download_time DATETIME NOT NULL,          -- 下载时间
  client_ip VARCHAR(45) NOT NULL,           -- 客户端IP
  user_agent TEXT,                          -- 用户代理
  file_size BIGINT NOT NULL,                -- 文件大小
  download_duration INTEGER,               -- 下载耗时(毫秒)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 9. 系统日志表 (system_logs)
```sql
CREATE TABLE system_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  timestamp DATETIME NOT NULL,              -- 日志时间戳
  level VARCHAR(10) NOT NULL,               -- 日志级别
  message TEXT NOT NULL,                    -- 日志消息
  module VARCHAR(50) NOT NULL,              -- 模块名称
  operation VARCHAR(100),                   -- 操作类型
  client_ip VARCHAR(45),                    -- 客户端IP
  is_private_ip BOOLEAN DEFAULT 0,          -- 是否私有IP
  user_agent TEXT,                          -- 用户代理
  path VARCHAR(500),                        -- 请求路径
  method VARCHAR(10),                       -- HTTP方法
  status_code INTEGER,                      -- 状态码
  meta TEXT,                                -- 元数据(JSON格式)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

---

## 配置数据结构

### 1. 服务器配置 (ServerConfig)
```typescript
interface ServerConfig {
  server: {
    nodeEnv: string;                        // 运行环境
    port: number;                           // 端口号
    host: string;                           // 主机地址
    baseUrl?: string;                       // 外部访问基础URL
  };
  database: {
    enabled: boolean;                       // 是否启用数据库
    type: 'sqlite';                         // 数据库类型
    path: string;                           // 数据库文件路径
    backupPath: string;                     // 备份路径
    maxConnections: number;                 // 最大连接数
    busyTimeout: number;                    // 忙碌超时
    enableWAL: boolean;                     // 是否启用WAL模式
  };
  storage: {
    basePath: string;                       // 存储基础路径
    releasesPath: string;                   // 发布文件路径
    metadataPath: string;                   // 元数据路径
    logsPath: string;                       // 日志路径
    tempPath: string;                       // 临时文件路径
  };
  security: {
    corsOrigin: string | string[];          // CORS允许的源
    rateLimitWindowMs: number;              // 速率限制窗口时间
    rateLimitMaxRequests: number;           // 速率限制最大请求数
    adminRateLimitWindowMs: number;         // 管理API速率限制窗口
    adminRateLimitMaxRequests: number;      // 管理API速率限制最大请求数
    publicRateLimitWindowMs: number;        // 公共API速率限制窗口
    publicRateLimitMaxRequests: number;     // 公共API速率限制最大请求数
    rateLimitWhitelistIps: string[];        // 速率限制白名单IP
  };
}
```

### 2. 系统配置 (SystemConfig)
```typescript
interface SystemConfig {
  upload: {
    maxFileSize: number;                    // 最大文件大小 (默认: 500MB)
    allowedExtensions: string[];            // 允许的文件扩展名
    tempDir: string;                        // 临时目录
    cleanupInterval: number;                // 清理间隔 (毫秒)
  };
  download: {
    rateLimit: {
      windowMs: number;                     // 速率限制窗口 (默认: 15分钟)
      maxRequests: number;                  // 最大请求数 (默认: 100)
    };
    enableStats: boolean;                   // 是否启用统计
    enableCompression: boolean;             // 是否启用压缩
  };
  storage: {
    basePath: string;                       // 存储基础路径
    cleanupInterval: number;                // 清理间隔 (毫秒)
    maxStorageSize: number;                 // 最大存储大小 (字节)
  };
  security: {
    enableCORS: boolean;                    // 是否启用CORS
    allowedOrigins: string[];               // 允许的源
    enableHelmet: boolean;                  // 是否启用Helmet安全中间件
  };
  logging: {
    level: string;                          // 日志级别
    enableFileLogging: boolean;             // 是否启用文件日志
    maxLogFiles: number;                    // 最大日志文件数
    maxLogSize: string;                     // 最大日志文件大小
  };
}
```

---

## 文件上传数据结构

### 1. 文件上传请求
```typescript
interface FileUploadRequest {
  version: string;                          // 版本号
  platform: 'windows' | 'macos' | 'linux'; // 平台
  architecture: 'x86_64' | 'aarch64' | 'x86'; // 架构
  channel: 'stable' | 'beta' | 'alpha';    // 发布渠道
  releaseNotes?: string;                    // 发布说明
  isForced?: boolean;                       // 是否强制更新
}
```

### 2. 批量上传请求
```typescript
interface BatchUploadRequest {
  files: BatchUploadFileInfo[];             // 文件信息列表
  metadata: FileUploadRequest;              // 元数据
}

interface BatchUploadFileInfo {
  filename: string;                         // 文件名
  fileSize: number;                         // 文件大小
  fileHash: string;                         // 文件哈希
  fileType: 'installer' | 'signature';     // 文件类型
}
```

### 3. 上传进度响应
```typescript
interface UploadProgress {
  sessionId: string;                        // 会话ID
  uploadedBytes: number;                    // 已上传字节数
  totalBytes: number;                       // 总字节数
  uploadedChunks: number;                   // 已上传块数
  totalChunks: number;                      // 总块数
  percentage: number;                       // 上传百分比
  speed: number;                            // 上传速度 (字节/秒)
  estimatedTimeRemaining: number;           // 预计剩余时间 (秒)
  status: UploadStatus;                     // 上传状态
}

enum UploadStatus {
  INITIALIZING = 'initializing',            // 初始化中
  UPLOADING = 'uploading',                  // 上传中
  PAUSED = 'paused',                        // 已暂停
  COMPLETED = 'completed',                  // 已完成
  FAILED = 'failed',                        // 失败
  CANCELLED = 'cancelled',                  // 已取消
  MERGING = 'merging',                      // 合并中
}
```

### 4. 批量上传响应
```typescript
interface BatchUploadResponse {
  sessionId: string;                        // 会话ID
  files: BatchUploadFileStatus[];           // 文件状态列表
  totalFiles: number;                       // 总文件数
  totalSize: number;                        // 总大小
}

interface BatchUploadFileStatus {
  filename: string;                         // 文件名
  fileType: 'installer' | 'signature';     // 文件类型
  status: UploadStatus;                     // 状态
  uploadedBytes: number;                    // 已上传字节数
  totalBytes: number;                       // 总字节数
  error?: string;                           // 错误信息
}
```

---

## 统计数据结构

### 1. 下载统计查询
```typescript
interface DownloadStatsQuery {
  startDate?: string;                       // 开始日期
  endDate?: string;                         // 结束日期
  version?: string;                         // 版本号
  platform?: string;                       // 平台
  channel?: string;                         // 渠道
  limit?: number;                           // 限制数量
  offset?: number;                          // 偏移量
}
```

### 2. 下载统计数据
```typescript
interface DownloadStatsData {
  totalDownloads: number;                   // 总下载次数
  totalBytes: number;                       // 总下载字节数
  byVersion: Record<string, number>;        // 按版本统计
  byPlatform: Record<string, number>;       // 按平台统计
  byChannel: Record<string, number>;        // 按渠道统计
  byDate: Record<string, number>;           // 按日期统计
  recentDownloads: DownloadRecord[];        // 最近下载记录
}
```

### 3. 下载记录
```typescript
interface DownloadRecord {
  id: string;                               // 记录ID
  filename: string;                         // 文件名
  version: string;                          // 版本号
  platform: string;                        // 平台
  architecture: string;                     // 架构
  channel: string;                          // 渠道
  downloadTime: string;                     // 下载时间
  clientIP: string;                         // 客户端IP
  userAgent: string;                        // 用户代理
  fileSize: number;                         // 文件大小
  downloadDuration?: number;                // 下载耗时
}
```

---

## 错误响应结构

### 1. 错误响应格式
```typescript
interface ErrorResponse {
  success: false;                           // 成功标识
  error: {
    code: string;                           // 错误代码
    message: string;                        // 错误消息
    details?: any;                          // 错误详情
  };
  timestamp: string;                        // 时间戳
  version: string;                          // 服务器版本
}
```

### 2. 成功响应格式
```typescript
interface SuccessResponse<T = any> {
  success: true;                            // 成功标识
  data: T;                                  // 响应数据
  timestamp: string;                        // 时间戳
  version: string;                          // 服务器版本
}
```

### 3. 常见错误代码
```typescript
const ERROR_CODES = {
  // 通用错误
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  INVALID_REQUEST: 'INVALID_REQUEST',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',

  // 版本相关错误
  VERSION_NOT_FOUND: 'VERSION_NOT_FOUND',
  INVALID_VERSION_FORMAT: 'INVALID_VERSION_FORMAT',
  UNSUPPORTED_PLATFORM: 'UNSUPPORTED_PLATFORM',

  // 文件相关错误
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  CHECKSUM_MISMATCH: 'CHECKSUM_MISMATCH',

  // 上传相关错误
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  UPLOAD_TIMEOUT: 'UPLOAD_TIMEOUT',
  UPLOAD_CANCELLED: 'UPLOAD_CANCELLED',

  // 数据库相关错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  MIGRATION_ERROR: 'MIGRATION_ERROR',

  // 配置相关错误
  CONFIG_ERROR: 'CONFIG_ERROR',
  INVALID_CONFIG: 'INVALID_CONFIG',
} as const;
```

---

## 管理 API 数据结构

### 1. 管理员认证
```typescript
// 登录请求
interface AdminLoginRequest {
  username: string;                         // 用户名
  password: string;                         // 密码
}

// 登录响应
interface AdminLoginResponse {
  success: true;
  data: {
    token: string;                          // JWT令牌
    expiresIn: number;                      // 过期时间(秒)
    user: {
      username: string;                     // 用户名
      role: string;                         // 角色
    };
  };
  timestamp: string;
  version: string;
}
```

### 2. 版本管理
```typescript
// 版本列表响应
interface VersionListResponse {
  success: true;
  data: {
    versions: VersionInfo[];                // 版本列表
    total: number;                          // 总数
    page: number;                           // 当前页
    pageSize: number;                       // 页大小
  };
  timestamp: string;
  version: string;
}

interface VersionInfo {
  id: number;                               // 版本ID
  version: string;                          // 版本号
  channel: string;                          // 渠道
  platform: string;                        // 平台
  architecture: string;                     // 架构
  filename: string;                         // 文件名
  fileSize: number;                         // 文件大小
  fileHash: string;                         // 文件哈希
  signature?: string;                       // 数字签名
  releaseNotes: string;                     // 发布说明
  isForced: boolean;                        // 是否强制更新
  isActive: boolean;                        // 是否激活
  releaseDate: string;                      // 发布日期
  createdAt: string;                        // 创建时间
  updatedAt: string;                        // 更新时间
}
```

---

## 环境变量配置

### 1. 服务器配置环境变量
```bash
# 服务器配置
NODE_ENV=development                        # 运行环境
PORT=3000                                   # 端口号
HOST=0.0.0.0                               # 主机地址
BASE_URL=http://localhost:3000              # 外部访问基础URL

# 数据库配置
DB_ENABLED=true                             # 是否启用数据库
DB_TYPE=sqlite                              # 数据库类型
DB_PATH=./storage/database/ota.db           # 数据库文件路径
DB_BACKUP_PATH=./storage/backup/database    # 备份路径
DB_MAX_CONNECTIONS=10                       # 最大连接数
DB_BUSY_TIMEOUT=30000                       # 忙碌超时
DB_ENABLE_WAL=true                          # 是否启用WAL模式

# 存储配置
STORAGE_PATH=./storage                      # 存储基础路径
RELEASES_PATH=./storage/releases            # 发布文件路径
METADATA_PATH=./storage/metadata            # 元数据路径
LOGS_PATH=./storage/logs                    # 日志路径
TEMP_PATH=./storage/temp                    # 临时文件路径

# 安全配置
CORS_ORIGIN=*                               # CORS允许的源
RATE_LIMIT_WINDOW_MS=900000                 # 速率限制窗口时间
RATE_LIMIT_MAX_REQUESTS=100                 # 速率限制最大请求数
ADMIN_RATE_LIMIT_WINDOW_MS=60000            # 管理API速率限制窗口
ADMIN_RATE_LIMIT_MAX_REQUESTS=30            # 管理API速率限制最大请求数
PUBLIC_RATE_LIMIT_WINDOW_MS=60000           # 公共API速率限制窗口
PUBLIC_RATE_LIMIT_MAX_REQUESTS=100          # 公共API速率限制最大请求数

# 管理员配置
ADMIN_USERNAME=admin                        # 管理员用户名
ADMIN_PASSWORD=your_secure_password         # 管理员密码
JWT_SECRET=your_jwt_secret_key              # JWT密钥
JWT_EXPIRES_IN=24h                          # JWT过期时间
SESSION_TIMEOUT=86400                       # 会话超时时间(秒)

# 日志配置
LOG_LEVEL=info                              # 日志级别
LOG_MAX_FILES=10                            # 最大日志文件数
LOG_MAX_SIZE=10m                            # 最大日志文件大小
LOG_DATE_PATTERN=YYYY-MM-DD                 # 日志日期格式

# HTTPS配置 (可选)
HTTPS_ENABLED=false                         # 是否启用HTTPS
HTTPS_PORT=3443                             # HTTPS端口
SSL_CERT_PATH=./ssl/cert.pem                # SSL证书路径
SSL_KEY_PATH=./ssl/key.pem                  # SSL私钥路径
```

---

## 数据流图

```mermaid
graph TD
    A[客户端应用] -->|版本检查请求| B[OTA服务器]
    B -->|查询版本信息| C[SQLite数据库]
    C -->|返回版本数据| B
    B -->|版本检查响应| A

    A -->|下载请求| D[文件下载API]
    D -->|读取文件| E[存储系统]
    E -->|文件流| D
    D -->|文件数据| A
    D -->|记录下载日志| C

    F[管理员] -->|上传文件| G[管理API]
    G -->|保存文件| E
    G -->|更新版本信息| C
    G -->|生成签名| H[签名系统]

    I[系统监控] -->|健康检查| B
    B -->|系统状态| I

    J[日志系统] -->|记录操作日志| C
    K[统计系统] -->|查询统计数据| C
```

---

## 总结

本文档详细描述了 RealityTap OTA 服务器的完整数据包结构，包括：

1. **API 接口**: 涵盖版本检查、文件下载、健康监控等核心功能
2. **数据库设计**: 完整的 SQLite 表结构和关系设计
3. **配置管理**: 系统配置和环境变量的详细说明
4. **文件上传**: 支持单文件和批量上传的完整流程
5. **统计监控**: 下载统计和系统监控的数据结构
6. **错误处理**: 统一的错误响应格式和错误代码定义

该文档为开发者提供了完整的数据结构参考，便于理解系统架构和进行二次开发。
