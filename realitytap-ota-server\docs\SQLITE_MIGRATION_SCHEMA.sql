-- RealityTap OTA Server SQLite Database Schema
-- 从 JSON 文件存储迁移到 SQLite 数据库的表结构设计

-- 1. 渠道表 (channels)
-- 替代 storage/metadata/channels.json
CREATE TABLE channels (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name VARCHAR(50) UNIQUE NOT NULL,
  enabled BO<PERSON>EAN NOT NULL DEFAULT 1,
  description TEXT,
  auto_update BOOLEAN NOT NULL DEFAULT 1,
  rollout_percentage INTEGER NOT NULL DEFAULT 100,
  priority INTEGER NOT NULL DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 版本表 (versions)
-- 存储每个渠道的版本信息
CREATE TABLE versions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  channel_id INTEGER NOT NULL,
  version VARCHAR(50) NOT NULL,
  force_update BOOLEAN NOT NULL DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOR<PERSON><PERSON><PERSON> KEY (channel_id) REFERENCES channels(id) ON DELETE CASCADE,
  UNIQUE(channel_id, version)
);

-- 3. 平台发布表 (platform_releases)
-- 存储每个版本在不同平台的发布信息
CREATE TABLE platform_releases (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  version_id INTEGER NOT NULL,
  platform VARCHAR(20) NOT NULL,
  architecture VARCHAR(20) NOT NULL,
  filename VARCHAR(255) NOT NULL,
  file_size BIGINT NOT NULL,
  checksum VARCHAR(128) NOT NULL,
  signature TEXT,
  release_date DATETIME NOT NULL,
  release_notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (version_id) REFERENCES versions(id) ON DELETE CASCADE,
  UNIQUE(version_id, platform, architecture)
);

-- 4. 系统配置表 (system_config)
-- 替代 storage/config/server-config.json
CREATE TABLE system_config (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  config_key VARCHAR(100) UNIQUE NOT NULL,
  config_value TEXT NOT NULL,
  config_type VARCHAR(20) NOT NULL DEFAULT 'string', -- string, number, boolean, json
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 5. 下载记录表 (download_records)
-- 替代 storage/stats/downloads/*.json 文件
CREATE TABLE download_records (
  id VARCHAR(36) PRIMARY KEY,
  filename VARCHAR(255) NOT NULL,
  version VARCHAR(50) NOT NULL,
  platform VARCHAR(20) NOT NULL,
  architecture VARCHAR(20) NOT NULL,
  channel VARCHAR(50) NOT NULL,
  download_time DATETIME NOT NULL,
  client_ip VARCHAR(45) NOT NULL,
  user_agent TEXT,
  file_size BIGINT NOT NULL,
  download_duration INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 6. 最小版本表 (minimum_versions)
-- 存储平台的最小支持版本
CREATE TABLE minimum_versions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  platform VARCHAR(20) NOT NULL,
  architecture VARCHAR(20) NOT NULL,
  minimum_version VARCHAR(50) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(platform, architecture)
);

-- 7. 废弃版本表 (deprecated_versions)
-- 存储已废弃的版本信息
CREATE TABLE deprecated_versions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  version VARCHAR(50) UNIQUE NOT NULL,
  deprecated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  reason TEXT
);

-- 8. 配置变更日志表 (config_change_logs)
-- 记录配置变更历史
CREATE TABLE config_change_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  config_key VARCHAR(100) NOT NULL,
  old_value TEXT,
  new_value TEXT,
  user_id VARCHAR(50),
  change_reason TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 9. 系统日志表 (system_logs)
-- 记录系统操作日志
CREATE TABLE system_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  timestamp DATETIME NOT NULL,
  level VARCHAR(10) NOT NULL,
  message TEXT NOT NULL,
  module VARCHAR(50) NOT NULL,
  operation VARCHAR(100),
  client_ip VARCHAR(45),
  is_private_ip BOOLEAN DEFAULT 0,
  user_agent TEXT,
  path VARCHAR(500),
  method VARCHAR(10),
  status_code INTEGER,
  meta TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以优化查询性能
-- 下载记录相关索引
CREATE INDEX idx_download_records_time ON download_records(download_time);
CREATE INDEX idx_download_records_version ON download_records(version);
CREATE INDEX idx_download_records_platform ON download_records(platform);
CREATE INDEX idx_download_records_channel ON download_records(channel);
CREATE INDEX idx_download_records_composite ON download_records(version, platform, channel);

-- 平台发布相关索引
CREATE INDEX idx_platform_releases_version ON platform_releases(version_id);
CREATE INDEX idx_platform_releases_platform ON platform_releases(platform, architecture);

-- 渠道相关索引
CREATE INDEX idx_channels_name ON channels(name);
CREATE INDEX idx_channels_enabled ON channels(enabled);

-- 版本相关索引
CREATE INDEX idx_versions_channel ON versions(channel_id);
CREATE INDEX idx_versions_version ON versions(version);
CREATE INDEX idx_versions_force_update ON versions(force_update);

-- 配置相关索引
CREATE INDEX idx_system_config_key ON system_config(config_key);
CREATE INDEX idx_config_logs_key ON config_change_logs(config_key);
CREATE INDEX idx_config_logs_time ON config_change_logs(created_at);

-- 系统日志相关索引
CREATE INDEX idx_system_logs_timestamp ON system_logs(timestamp);
CREATE INDEX idx_system_logs_level ON system_logs(level);
CREATE INDEX idx_system_logs_module ON system_logs(module);
CREATE INDEX idx_system_logs_operation ON system_logs(operation);
CREATE INDEX idx_system_logs_client_ip ON system_logs(client_ip);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
