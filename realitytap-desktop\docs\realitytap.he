{
 "Metadata": {
 "Version": 1, // 版本号，整形
 "Created": "2020-07-08", // 创建时间，String类型
 "Description": "game haptic" // 震动效果描述，String类型
 },
 "Pattern":
 [
 {
 "Event": {
 "Type": "continuous", // 事件类型: continuous->持续震动。transient->简短震动
 "RelativeTime": 0, // 相对开始时间, 整形, 单位ms
 "Duration": 300, // 持续震动类型参数：持续时间。整形, 单位ms
 "Parameters": {
 "Intensity": 80, // 震动强度, 整形, [0,100]。0->平台支持的最小值, 100->平台支持的最大值。
 "Frequency": 50, // 震动频率, 整形, [0,100]。0->平台支持的最小值, 100->平台支持的最大值。
 "Curve": [ // 持续震动类型参数：曲线。实现上保证平滑过渡效果
 {"Time": 0, "Intensity": 0, "Frequency": 25}, // 起始点，必须。time为RelativeTime，Intensity必须取值为0。
 {"Time": 100, "Intensity": 0.7, "Frequency": -30},
 {"Time": 200, "Intensity": 0.75, "Frequency": 90},
 {"Time": 300, "Intensity": 0, "Frequency": 50} // 结束点，必须。time为Duration，Intensity必须取值为0。
 ]
 }
 }
 },
 {
 "Event": {
 "Type": "transient", // 事件类型: continuous->持续震动, transient->简短震动
 "RelativeTime": 400, // 相对开始时间, 整形, 单位ms
 "Parameters": {
 "Intensity": 80, // 震动强度, 整形, [0,100]。0->平台支持的最小值, 100->平台支持的最大值。
 "Frequency": 40 // 震动频率, 整形, [0,100]。0->平台支持的最小值, 100->平台支持的最大值。
 }
 }
 }
 ]
}