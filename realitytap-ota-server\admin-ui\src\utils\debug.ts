/**
 * 调试工具 - 用于诊断登录后页面空白问题
 */

export const debugAuth = {
  /**
   * 检查认证状态
   */
  checkAuthState() {
    const token = localStorage.getItem('admin_token');
    const user = localStorage.getItem('admin_user');
    
    console.group('🔍 认证状态检查');
    console.log('Token存在:', !!token);
    console.log('User存在:', !!user);
    
    if (token) {
      console.log('Token长度:', token.length);
      console.log('Token前缀:', token.substring(0, 20) + '...');
    }
    
    if (user) {
      try {
        const userObj = JSON.parse(user);
        console.log('用户信息:', userObj);
      } catch (error) {
        console.error('用户信息解析失败:', error);
      }
    }
    
    console.groupEnd();
  },

  /**
   * 检查路由状态
   */
  checkRouteState() {
    console.group('🛣️ 路由状态检查');
    console.log('当前路径:', window.location.pathname);
    console.log('当前hash:', window.location.hash);
    console.log('当前search:', window.location.search);
    console.groupEnd();
  },

  /**
   * 检查Vue应用状态
   */
  checkVueAppState() {
    console.group('⚡ Vue应用状态检查');
    
    const appElement = document.getElementById('app');
    console.log('App元素存在:', !!appElement);
    
    if (appElement) {
      console.log('App元素内容长度:', appElement.innerHTML.length);
      console.log('App元素子节点数:', appElement.children.length);
    }
    
    console.groupEnd();
  },

  /**
   * 完整诊断
   */
  fullDiagnosis() {
    console.clear();
    console.log('🚀 开始完整诊断...');
    
    this.checkAuthState();
    this.checkRouteState();
    this.checkVueAppState();
    
    console.log('✅ 诊断完成');
  }
};

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  (window as any).debugAuth = debugAuth;
}
