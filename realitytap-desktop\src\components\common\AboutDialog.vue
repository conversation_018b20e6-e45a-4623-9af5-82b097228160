<template>
  <NModal
    v-model:show="isVisible"
    preset="dialog"
    :title="t('about.title')"
    :positive-text="t('common.close')"
    @positive-click="hideAboutDialog"
    @mask-click="hideAboutDialog"
    @esc="hideAboutDialog"
    :style="{ width: '572px', maxWidth: '80vw' }"
    class="about-dialog"
  >
    <div class="about-content">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <NSpin size="medium" />
        <p>{{ t("about.loading") }}</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <NAlert type="error" :title="t('common.error')">
          {{ error }}
        </NAlert>
      </div>

      <!-- 正常内容 -->
      <div v-else class="about-main">
        <!-- Logo 和应用信息 -->
        <div class="app-info-section">
          <div class="logo-container">
            <img :src="logoUrl" :alt="t('app.title')" class="app-logo" />
          </div>
          <div class="app-details">
            <h2 class="app-name">{{ t("app.title") }}</h2>
            <div class="version-info">
              <p
                class="version-text"
                @click="handleVersionClick"
              >
                {{ t("about.version") }}: {{ formattedVersion }}
              </p>
              <p class="build-info">{{ t("about.buildInfo") }}: {{ formattedBuildInfo }}</p>
              <p class="platform-info">{{ t("about.platform") }}: {{ formattedPlatformInfo }}</p>
            </div>
          </div>
        </div>

        <!-- 分隔线 -->
        <NDivider />

        <!-- 版本检查区域 (预留功能) -->
        <div class="update-section">
          <h3>{{ t("about.updateCheck.title") }}</h3>
          <div class="update-status">
            <div v-if="versionCheckStatus.isChecking" class="checking-status">
              <NSpin size="small" />
              <span>{{ t("about.updateCheck.checking") }}</span>
            </div>
            <div v-else-if="versionCheckStatus.hasNewVersion" class="new-version-available">
              <NAlert type="info" :title="t('about.updateCheck.newVersionAvailable')">
                {{ t("about.updateCheck.latestVersion") }}: {{ versionCheckStatus.latestVersion }}
              </NAlert>
              <NButton type="primary" @click="showUpdateDialog" class="download-button">
                {{ t("about.updateCheck.viewUpdate") }}
              </NButton>
            </div>
            <div v-else-if="versionCheckStatus.error" class="check-error">
              <NAlert type="warning" :title="t('about.updateCheck.checkFailed')">
                {{ versionCheckStatus.error }}
              </NAlert>
            </div>
            <div v-else class="up-to-date">
              <NAlert type="success" :title="t('about.updateCheck.upToDate')" />
            </div>
          </div>
          <NButton @click="checkForUpdates" :disabled="versionCheckStatus.isChecking" class="check-button">
            {{ t("about.updateCheck.checkNow") }}
          </NButton>
        </div>

        <!-- 隐藏的调试按钮 -->
        <div v-if="showDebugButton" class="debug-section">
          <NDivider />
          <div class="debug-controls">
            <h3>{{ t("debug.title") }}</h3>
            <NSpace>
              <NButton
                text
                size="small"
                class="debug-action-button"
                @click="openDebugSettings"
                :title="t('debug.menu.settings')"
              >
                <template #icon>
                  <NIcon><SettingsIcon /></NIcon>
                </template>
                {{ t('debug.menu.settings') }}
              </NButton>

              <NButton
                text
                size="small"
                class="debug-action-button"
                @click="openLogViewer"
                :title="t('debug.menu.viewLogs')"
              >
                <template #icon>
                  <NIcon><LogIcon /></NIcon>
                </template>
                {{ t('debug.menu.viewLogs') }}
              </NButton>

              <NButton
                text
                size="small"
                class="debug-action-button"
                @click="exportDebugInfo"
                :title="t('debug.menu.exportDebugInfo')"
              >
                <template #icon>
                  <NIcon><ExportIcon /></NIcon>
                </template>
                {{ t('debug.menu.exportDebugInfo') }}
              </NButton>
            </NSpace>
          </div>
        </div>

      </div>
    </div>
  </NModal>

  <!-- 更新对话框 -->
  <UpdateDialog
    v-model:visible="updateDialogVisible"
    :update-info="updateInfo"
    :current-version="formattedVersion"
    @remind-later="handleRemindLater"
    @install-later="handleInstallLater"
  />

  <!-- 调试组件 -->
  <DebugSettings
    ref="debugSettingsRef"
    @open-log-viewer="openLogViewer"
  />

  <LogViewer ref="logViewerRef" />

</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from "@/composables/useI18n";
import { useAboutDialog } from "@/composables/useAboutDialog";
import { useMessage, NIcon } from 'naive-ui';
import { invoke } from '@tauri-apps/api/core';
import UpdateDialog from "./UpdateDialog.vue";
import DebugSettings from "@/components/debug/DebugSettings.vue";
import LogViewer from "@/components/debug/LogViewer.vue";
import logoUrl from "@/assets/awa_logo.png";
import {
  SettingsOutline as SettingsIcon,
  DocumentTextOutline as LogIcon,
  DownloadOutline as ExportIcon
} from '@vicons/ionicons5';
import { logger, LogModule } from "@/utils/logger/logger";

// === 组合函数 ===
const { t } = useI18n();
const message = useMessage();
const {
  isVisible,
  isLoading,
  error,
  versionCheckStatus,

  updateDialogVisible,
  updateInfo,
  formattedVersion,
  formattedBuildInfo,
  formattedPlatformInfo,
  showAboutDialog,
  hideAboutDialog,
  checkForUpdates,
  showUpdateDialog,
  remindLater,
  installLater,
} = useAboutDialog();

// === 调试相关状态 ===
const debugClickCount = ref(0);
const showDebugButton = ref(false);
const debugSettingsRef = ref();
const logViewerRef = ref();

// 重置调试点击计数的定时器
let debugClickTimer: number | null = null;

// === 红点通知逻辑 ===
// 红点应该在用户真正查看更新详情时才消失，而不是仅仅打开关于对话框
// 这个逻辑将在用户点击"查看更新"按钮时触发



// === 事件处理函数 ===
// 注意：下载和安装逻辑现在由 UpdateDialog 内部处理

/**
 * 处理版本号点击事件
 */
const handleVersionClick = () => {
  debugClickCount.value++;

  // 清除之前的定时器
  if (debugClickTimer) {
    clearTimeout(debugClickTimer);
  }

  // 如果达到5次点击，显示调试按钮
  if (debugClickCount.value >= 5) {
    showDebugButton.value = true;
    message.success(t('debug.activated'));
    debugClickCount.value = 0; // 重置计数
  } else {
    // 设置3秒后重置计数
    debugClickTimer = setTimeout(() => {
      debugClickCount.value = 0;
    }, 3000);
  }
};

/**
 * 打开调试设置
 */
const openDebugSettings = async () => {
  await debugSettingsRef.value?.showDebugSettings();
};

/**
 * 打开日志查看器
 */
const openLogViewer = async () => {
  await logViewerRef.value?.showLogViewer();
};

/**
 * 导出调试信息
 */
const exportDebugInfo = async () => {
  try {
    const exportPath = await invoke<string>('export_debug_info');
    message.success(t('debug.exportSuccess', { path: exportPath }));
  } catch (error) {
    logger.error(LogModule.GENERAL, 'Failed to export debug info', error);
    message.error(t('debug.errors.exportFailed'));
  }
};

const handleRemindLater = () => {
  logger.info(LogModule.GENERAL, "用户选择稍后提醒");
  remindLater();
};

const handleInstallLater = () => {
  logger.info(LogModule.GENERAL, "用户选择稍后安装");
  installLater();
};



// === 监听器 ===
// 注意：下载进度监听现在由 UpdateDialog 内部处理



// === 暴露给父组件的方法 ===
defineExpose({
  showAboutDialog,
});
</script>

<style scoped>
.about-content {
  width: 100%;
  max-width: 600px;
}

/* 强制设置对话框宽度 */
:deep(.n-dialog) {
  width: 640px !important;
  max-width: 90vw !important;
}

:deep(.n-dialog .n-dialog__content) {
  width: 100% !important;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
}

.about-main {
  padding: 8px;
}

.app-info-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.logo-container {
  flex-shrink: 0;
}

.app-logo {
  width: 172px;
  height: 74px;
  object-fit: contain;
}

.app-details {
  flex: 1;
}

.app-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.app-subtitle {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: var(--n-text-color-2);
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.version-text,
.build-info,
.platform-info {
  margin: 0;
  font-size: 13px;
  color: var(--n-text-color-2);
  user-select: none;
}

.update-section {
  margin-bottom: 24px;
}

.update-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--n-text-color);
}

.update-status {
  margin-bottom: 16px;
}

.checking-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--n-text-color-2);
}

.new-version-available {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.download-button,
.check-button {
  margin-top: 8px;
}

/* 调试区域样式 */
.debug-section {
  margin-top: 20px;
}

.debug-controls h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--n-text-color);
}

.debug-action-button {
  color: var(--n-text-color-2);
  transition: color 0.2s ease;
  font-size: 12px;
}

.debug-action-button:hover {
  color: var(--n-text-color);
}

.debug-action-button .n-icon {
  font-size: 14px;
}

</style>
