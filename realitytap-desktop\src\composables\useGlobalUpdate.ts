/**
 * 全局更新状态管理
 * Global update state management
 */

import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useUpdater } from './useUpdater';
import type { UpdateInfo } from './useUpdater';
import { logger, LogModule } from '@/utils/logger/logger';
import { getUpdaterConfig } from '@/config/updater.config';

// === 全局状态 ===
const showUpdateNotification = ref(false);
const showUpdateDialog = ref(false);
const showForceUpdateDialog = ref(false);
const lastCheckTime = ref<Date | null>(null);

// 配置
const config = getUpdaterConfig();
const CHECK_INTERVAL = config.checkInterval;
const AUTO_CHECK_ENABLED = config.autoCheck;

let checkInterval: number | null = null;

/**
 * 全局更新管理组合函数
 */
export function useGlobalUpdate() {
  const updater = useUpdater();

  // === 计算属性 ===
  const shouldShowNotification = computed(() => {
    return updater.hasUpdate.value && !showUpdateDialog.value && !showForceUpdateDialog.value && showUpdateNotification.value;
  });

  const shouldShowForceUpdate = computed(() => {
    return updater.hasUpdate.value && updater.isForceUpdate.value;
  });

  const timeSinceLastCheck = computed(() => {
    if (!lastCheckTime.value) return null;
    return Date.now() - lastCheckTime.value.getTime();
  });

  const needsCheck = computed(() => {
    if (!lastCheckTime.value) return true;
    const timeSince = timeSinceLastCheck.value;
    return timeSince ? timeSince > CHECK_INTERVAL : true;
  });

  // === 方法 ===

  /**
   * 检查更新（带国际化错误处理）
   */
  const checkForUpdates = async (silent = false): Promise<UpdateInfo | null> => {
    try {
      const result = await updater.checkForUpdates(silent);
      lastCheckTime.value = new Date();

      // 如果有更新，根据是否为强制更新决定显示方式
      if (result) {
        if (result.force_update) {
          // 强制更新：无论是否静默检查都要显示强制更新对话框
          showForceUpdateDialog.value = true;
          showUpdateNotification.value = false;
          logger.warn(LogModule.GENERAL, "检测到强制更新，显示强制更新对话框");
        } else if (!silent) {
          // 普通更新：只在非静默检查时显示通知
          showUpdateNotification.value = true;
        }
      }

      return result;
    } catch (error) {
      // 错误已经在 updater 中处理，这里只记录日志
      logger.error(LogModule.GENERAL, 'Update check failed', error);
      return null;
    }
  };

  /**
   * 显示更新对话框
   */
  const showUpdate = () => {
    showUpdateDialog.value = true;
    showUpdateNotification.value = false;
  };

  /**
   * 隐藏更新对话框
   */
  const hideUpdate = () => {
    showUpdateDialog.value = false;
  };

  /**
   * 显示强制更新对话框
   */
  const showForceUpdate = () => {
    showForceUpdateDialog.value = true;
    showUpdateNotification.value = false;
    showUpdateDialog.value = false;
  };

  /**
   * 隐藏强制更新对话框
   */
  const hideForceUpdate = () => {
    showForceUpdateDialog.value = false;
  };

  /**
   * 关闭更新通知
   */
  const dismissNotification = () => {
    showUpdateNotification.value = false;
  };

  /**
   * 安装更新（带国际化错误处理）
   */
  const installUpdate = async (): Promise<boolean> => {
    try {
      const result = await updater.downloadAndInstall();
      if (result) {
        showUpdateDialog.value = false;
      }
      return result;
    } catch (error) {
      // 错误已经在 updater 中处理，这里只记录日志
      logger.error(LogModule.GENERAL, 'Update installation failed', error);
      return false;
    }
  };

  /**
   * 启动自动检查
   */
  const startAutoCheck = () => {
    if (!AUTO_CHECK_ENABLED || checkInterval) return;

    // 立即检查一次（静默）
    if (needsCheck.value) {
      checkForUpdates(true);
    }

    // 设置定时检查
    checkInterval = window.setInterval(() => {
      if (needsCheck.value) {
        checkForUpdates(true);
      }
    }, CHECK_INTERVAL);
  };

  /**
   * 停止自动检查
   */
  const stopAutoCheck = () => {
    if (checkInterval) {
      clearInterval(checkInterval);
      checkInterval = null;
    }
  };

  /**
   * 重置状态
   */
  const reset = () => {
    updater.reset();
    showUpdateNotification.value = false;
    showUpdateDialog.value = false;
    showForceUpdateDialog.value = false;
    lastCheckTime.value = null;
  };

  // === 生命周期 ===
  onMounted(() => {
    startAutoCheck();
  });

  onUnmounted(() => {
    stopAutoCheck();
  });

  return {
    // 从 updater 导出的状态和方法
    ...updater,

    // 全局状态
    lastCheckTime,
    showUpdateNotification,
    showUpdateDialog,
    showForceUpdateDialog,

    // 计算属性
    shouldShowNotification,
    shouldShowForceUpdate,
    timeSinceLastCheck,
    needsCheck,

    // 方法
    checkForUpdates,
    showUpdate,
    hideUpdate,
    showForceUpdate,
    hideForceUpdate,
    dismissNotification,
    installUpdate,
    startAutoCheck,
    stopAutoCheck,
    reset,
  };
}

