---
type: "agent_requested"
description: "适用于 realitytap-ota-server 的开发指南，涵盖项目架构、技术栈、核心开发原则等。"
---
# RealityTap OTA Server - 开发指南

## 项目架构
- **应用**: OTA 更新服务器 (Over-The-Air Updates)
- **核心**: Node.js + Express + TypeScript
- **环境**: Windows + PowerShell + VSCode
- **原则**: 简单易维护、无复杂数据结构

## 技术栈要求
- use context7
- 具体的数字或者字符串比如使用常量定义

## 基本原则
- 请记住，代码是写给人看的，只是机器恰好可以运行
- 随意更改代码是会死人的
- 使用现代化和专业化的日志系统

### 前端 (必须)
- Vue3 Composition API + `<script setup>`
- TypeScript 严格模式
- Pinia 状态管理
- Naive UI v2 组件库
- Vite 构建工具

### 后端 (必须)
- Node.js 18+ + Express.js
- TypeScript 严格模式
- Zod 数据验证
- <PERSON> 日志记录
- Jest 单元测试
- Docker 容器化部署

### 核心依赖
- Express.js 4.x (Web框架)
- Zod (数据验证)
- Winston (日志系统)
- Semver (版本管理)
- fs-extra (文件操作)
- @realitytap/shared (共享库)

## 核心开发原则
### 简单易维护架构 (强制)
- **最小化复杂性**: 避免过度设计和复杂数据结构
- **清晰分层**: Controller → Service → Util 三层架构
- **单一职责**: 每个模块只负责一个明确的功能
- **无状态设计**: 服务器无状态，便于水平扩展
- **DRY原则**:  Don't Repeat Yourself，提高代码可维护性和一致性

## 开发要求
### 代码规范
1. **简单明确**: 优先选择简单直接的实现方式
2. **类型安全**: 所有函数必须有完整的 TypeScript 类型
3. **错误处理**: 统一的错误处理和日志记录
4. **数据验证**: 使用 Zod 验证所有输入数据
5. **安全优先**: 实施适当的安全措施 (CORS, 限流, 验证)
6. **性能考虑**: 文件操作异步化，避免阻塞
7. **文档要求**: 文档英文名准确详细注意大小写，所有文档必须放置在 `docs/` 目录下

### OTA 特定要求
**文件管理**:
- 版本文件存储在 `storage/releases/` 目录
- 元数据存储在 `storage/metadata/` 目录
- 支持多平台文件管理 (Windows/Mac/Linux)

**版本控制**:
- 使用语义化版本 (Semantic Versioning)
- 支持版本比较和更新检查
- 维护版本历史和回滚能力

**安全措施**:
- 文件完整性校验 (SHA256)
- 下载限流和访问控制
- 请求参数验证

### 测试要求
**必须测试的功能**:
- 版本比较逻辑
- 文件上传/下载功能
- API 端点响应
- 错误处理机制
- 安全验证逻辑

**测试规范**:
- 单元测试覆盖核心逻辑
- 集成测试验证 API 功能
- 使用 Jest + Supertest
- 测试文件存放在 `tests/` 目录

### 性能和安全
**性能优化**:
- 使用流式文件传输
- 实施适当的缓存策略
- 压缩响应数据
- 异步文件操作

**安全考虑**:
- 输入验证和清理
- 文件路径安全检查
- 访问日志记录
- 错误信息不泄露敏感数据

### 部署要求
**Docker 支持**:
- 提供 Dockerfile 和 docker-compose.yml
- 支持环境变量配置
- 数据卷持久化存储

**配置管理**:
- 使用环境变量配置
- 支持开发/生产环境切换
- 配置验证和默认值

### 开发检查清单
- [ ] 代码是否遵循简单易维护原则
- [ ] 是否正确使用三层架构 (Controller/Service/Util)
- [ ] 类型定义是否完整和准确
- [ ] 是否实施了适当的数据验证
- [ ] 错误处理是否统一和完善
- [ ] 是否考虑了安全性要求
- [ ] 文件操作是否异步化
- [ ] 是否编写了必要的测试
- [ ] 是否遵循 OTA 特定要求
- [ ] 配置是否通过环境变量管理

**重要**:
- 始终优先考虑简单性和可维护性，避免过度工程化
- 确保所有文件操作都是安全和高效的
- 实施完善的错误处理和日志记录
- 保持 API 的一致性和向后兼容性
