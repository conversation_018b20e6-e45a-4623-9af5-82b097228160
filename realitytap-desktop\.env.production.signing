# RealityTap Desktop 生产环境签名配置
# Production signing configuration for RealityTap Desktop

# Tauri 签名私钥内容 (生产环境)
# Production private key content (base64 encoded)
TAURI_SIGNING_PRIVATE_KEY="dW50cnVzdGVkIGNvbW1lbnQ6IHJzaWduIGVuY3J5cHRlZCBzZWNyZXQga2V5ClJXUlRZMEl5RmdPbndnR2crc2ZwU0xSbDlmazJnV3NFNHJ4V3A4VWZPNkt6ZFNrSmJra0FBQkFBQUFBQUFBQUFBQUlBQUFBQUFJbkdRWlYyMktoS0IrU2hSV2hFZDlqRTZzUlF4MFNDcmw1MENISnQ3S0FiZXlkeUNVUXNJVEo1NkQ3alU0SFgxQ3B3YUZ2YVE4NmNRUUtwZ3hzTkhWWjhNT3lxbC9UcjlVTkMzZEFjVlg0M1d3Q3BmYWtXZTBGVXZNVWM0T0Q4T3h6cVM5MVkrOEk9Cg=="

# Tauri 签名私钥密码 (生产环境 - 需要设置实际密码)
# Production private key password (set your actual password)
TAURI_SIGNING_PRIVATE_KEY_PASSWORD="awa123456"

# 使用说明:
# Usage instructions:
# 1. 将此文件复制为 .env.production.signing
#    Copy this file as .env.production.signing
# 2. 设置实际的密码
#    Set your actual password
# 3. 在生产构建前加载环境变量:
#    Load environment variables before production build:
#    Get-Content .env.production.signing | ForEach-Object { 
#        if ($_ -match '^([^=]+)=(.*)$') { 
#            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], 'Process') 
#        } 
#    }

# 生产环境公钥 (用于 tauri.conf.json):
# Production public key (for tauri.conf.json):
# dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDZBMjY3MEU2RTcwQjIyMEUKUldRT0lndm41bkFtYWhMUXhWSTI3K2haNVBLMEd3dXJGb1F1ZVdmaEdjUG4vV3MzaVJkNWVpR3oK
