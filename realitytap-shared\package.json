{"name": "@realitytap/shared", "version": "1.0.0", "description": "Shared types and utilities for RealityTap ecosystem", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"zod": "^3.22.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "jest": "^29.0.0", "eslint": "^8.0.0"}}