/**
 * 客户端相关类型定义
 */

export interface UpdateError {
  code: string;
  message: string;
  details?: any;
}

export interface ClientConfig {
  serverBaseURL: string;
  channel: 'stable' | 'beta' | 'alpha';
  checkInterval?: number;
  downloadTimeout?: number;
  retryAttempts?: number;
  autoInstall?: boolean;
}

export interface ProgressCallback {
  (progress: {
    stage: 'checking' | 'downloading' | 'verifying' | 'installing';
    percentage: number;
    completed: number;
    total: number;
    speed?: number;
    eta?: number;
    message?: string;
  }): void;
}

export interface UpdateEvent {
  type: 'check-start' | 'check-complete' | 'download-start' | 'download-progress' | 
        'download-complete' | 'install-start' | 'install-complete' | 'error' | 'cancelled';
  data?: any;
  timestamp: Date;
}

export type UpdateEventListener = (event: UpdateEvent) => void;
