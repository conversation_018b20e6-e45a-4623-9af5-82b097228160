/**
 * 智能重绘管理器
 * 根据操作类型选择最优的重绘策略，提升Canvas绘制性能
 */

export type RedrawType = 'drag' | 'property' | 'selection' | 'scroll' | 'zoom' | 'audio' | 'init';
export type RedrawPriority = 'immediate' | 'high' | 'normal' | 'low';

interface RedrawTask {
  id: string;
  type: RedrawType;
  priority: RedrawPriority;
  force: boolean;
  timestamp: number;
  context?: any;
}

export interface RedrawStrategy {
  useCache: boolean;
  skipAudio: boolean;
  skipGrid: boolean;
  skipGuides: boolean;
  simplifiedEvents: boolean;
  batchUpdates: boolean;
  maxFrameTime: number;
}

/**
 * 智能重绘管理器
 * 
 * 特性：
 * 1. 根据操作类型自动选择最优重绘策略
 * 2. 支持优先级调度和任务合并
 * 3. 自适应性能调整
 * 4. 批量处理和帧率控制
 */
export class SmartRedrawManager {
  private pendingTasks = new Map<string, RedrawTask>();
  private isProcessing = false;
  private frameTimeHistory: number[] = [];
  private animationFrameId: number | null = null;
  private lastRedrawTime = 0; // 记录最后一次重绘时间，用于性能分析

  // 重绘策略配置
  private readonly REDRAW_STRATEGIES: Record<RedrawType, RedrawStrategy> = {
    // 拖拽：保持视觉质量，适度优化性能
    drag: {
      useCache: false,        // 跳过缓存，直接绘制
      skipAudio: false,       // 保留音频波形
      skipGrid: false,        // 保留网格绘制，确保视觉一致性
      skipGuides: false,      // 保留辅助线
      simplifiedEvents: false,// 保留完整事件绘制，确保颜色正确
      batchUpdates: false,    // 不批量处理，立即响应
      maxFrameTime: 12        // 12ms内完成，约83fps，平衡性能和质量
    },
    
    // 属性调整：平衡性能和质量
    property: {
      useCache: false,        // 属性变化时跳过缓存
      skipAudio: false,       // 保留音频波形
      skipGrid: false,        // 保留网格
      skipGuides: false,      // 保留辅助线
      simplifiedEvents: false,// 使用完整事件绘制
      batchUpdates: true,     // 批量处理多个属性变化
      maxFrameTime: 16        // 16ms内完成，60fps
    },
    
    // 选择变化：轻量级更新
    selection: {
      useCache: true,         // 使用缓存
      skipAudio: false,       // 保留音频波形
      skipGrid: false,        // 保留网格
      skipGuides: false,      // 保留辅助线
      simplifiedEvents: false,// 使用完整事件绘制
      batchUpdates: true,     // 批量处理
      maxFrameTime: 16        // 16ms内完成
    },
    
    // 滚动：中等性能要求
    scroll: {
      useCache: true,         // 使用缓存
      skipAudio: false,       // 保留音频波形
      skipGrid: true,         // 滚动时跳过网格
      skipGuides: true,       // 滚动时跳过辅助线
      simplifiedEvents: true, // 使用简化事件绘制
      batchUpdates: false,    // 立即响应滚动
      maxFrameTime: 12        // 12ms内完成
    },
    
    // 缩放：完整重绘
    zoom: {
      useCache: false,        // 缩放时清除缓存
      skipAudio: false,       // 保留音频波形
      skipGrid: false,        // 保留网格
      skipGuides: false,      // 保留辅助线
      simplifiedEvents: false,// 使用完整事件绘制
      batchUpdates: false,    // 立即响应
      maxFrameTime: 32        // 32ms内完成，允许更长时间
    },
    
    // 音频相关：专门优化
    audio: {
      useCache: false,        // 音频变化时清除缓存
      skipAudio: false,       // 重点绘制音频波形
      skipGrid: false,        // 保留网格
      skipGuides: false,      // 保留辅助线
      simplifiedEvents: false,// 使用完整事件绘制
      batchUpdates: true,     // 批量处理
      maxFrameTime: 24        // 24ms内完成
    },
    
    // 初始化：完整绘制
    init: {
      useCache: false,        // 初始化时不使用缓存
      skipAudio: false,       // 完整绘制
      skipGrid: false,        // 完整绘制
      skipGuides: false,      // 完整绘制
      simplifiedEvents: false,// 完整绘制
      batchUpdates: false,    // 立即绘制
      maxFrameTime: 50        // 50ms内完成，允许较长时间
    }
  };

  // 优先级权重
  private readonly PRIORITY_WEIGHTS: Record<RedrawPriority, number> = {
    immediate: 4,
    high: 3,
    normal: 2,
    low: 1
  };

  constructor(
    private drawWaveformMain: (strategy: RedrawStrategy) => void,
    private drawWaveformLightweight: (strategy: RedrawStrategy) => void,
    private clearCache: () => void
  ) {
    // 定期清理性能历史数据
    setInterval(() => {
      if (this.frameTimeHistory.length > 100) {
        this.frameTimeHistory = this.frameTimeHistory.slice(-50);
      }
    }, 10000);
  }

  /**
   * 请求重绘
   */
  requestRedraw(
    type: RedrawType,
    force: boolean = false,
    priority: RedrawPriority = 'normal',
    context?: any
  ): void {
    const taskId = `${type}-${Date.now()}`;
    const task: RedrawTask = {
      id: taskId,
      type,
      priority,
      force,
      timestamp: performance.now(),
      context
    };

    // 检查是否可以合并任务
    const existingTask = this.findMergeableTask(task);
    if (existingTask) {
      // 更新现有任务的优先级和强制标志
      if (this.PRIORITY_WEIGHTS[task.priority] > this.PRIORITY_WEIGHTS[existingTask.priority]) {
        existingTask.priority = task.priority;
      }
      existingTask.force = existingTask.force || task.force;
      existingTask.timestamp = task.timestamp;
    } else {
      this.pendingTasks.set(taskId, task);
    }

    this.scheduleExecution();
  }

  /**
   * 立即执行重绘
   */
  immediateRedraw(type: RedrawType, force: boolean = true, context?: any): void {
    this.requestRedraw(type, force, 'immediate', context);
  }

  /**
   * 取消指定类型的重绘任务
   */
  cancelRedraw(type: RedrawType): void {
    for (const [id, task] of this.pendingTasks.entries()) {
      if (task.type === type) {
        this.pendingTasks.delete(id);
      }
    }
  }

  /**
   * 取消所有重绘任务
   */
  cancelAllRedraws(): void {
    this.pendingTasks.clear();
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * 获取当前性能指标
   */
  getPerformanceMetrics() {
    if (this.frameTimeHistory.length === 0) {
      return {
        averageFrameTime: 0,
        maxFrameTime: 0,
        minFrameTime: 0,
        frameRate: 0,
        isPerformanceGood: true
      };
    }

    const avg = this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length;
    const max = Math.max(...this.frameTimeHistory);
    const min = Math.min(...this.frameTimeHistory);
    const frameRate = 1000 / avg;
    const isPerformanceGood = avg < 16 && max < 32;

    return {
      averageFrameTime: avg,
      maxFrameTime: max,
      minFrameTime: min,
      frameRate,
      isPerformanceGood
    };
  }

  /**
   * 查找可合并的任务
   */
  private findMergeableTask(newTask: RedrawTask): RedrawTask | null {
    for (const task of this.pendingTasks.values()) {
      // 相同类型的任务可以合并
      if (task.type === newTask.type) {
        return task;
      }
      
      // 某些类型可以被更高优先级的任务替代
      if (this.canTaskReplace(newTask, task)) {
        this.pendingTasks.delete(task.id);
        return null;
      }
    }
    return null;
  }

  /**
   * 判断任务是否可以被替代
   */
  private canTaskReplace(newTask: RedrawTask, existingTask: RedrawTask): boolean {
    const newWeight = this.PRIORITY_WEIGHTS[newTask.priority];
    const existingWeight = this.PRIORITY_WEIGHTS[existingTask.priority];
    
    // 高优先级任务可以替代低优先级任务
    if (newWeight > existingWeight) {
      return true;
    }
    
    // 特殊规则：拖拽可以替代属性调整
    if (newTask.type === 'drag' && existingTask.type === 'property') {
      return true;
    }
    
    return false;
  }

  /**
   * 调度执行
   */
  private scheduleExecution(): void {
    if (this.isProcessing || this.pendingTasks.size === 0) {
      return;
    }

    // 对于立即执行的任务，直接处理
    const immediateTasks = Array.from(this.pendingTasks.values())
      .filter(task => task.priority === 'immediate');
    
    if (immediateTasks.length > 0) {
      this.processImmediateTasks(immediateTasks);
      return;
    }

    // 其他任务使用 requestAnimationFrame 调度
    if (!this.animationFrameId) {
      this.animationFrameId = requestAnimationFrame(() => {
        this.animationFrameId = null;
        this.processPendingTasks();
      });
    }
  }

  /**
   * 处理立即执行的任务
   */
  private processImmediateTasks(tasks: RedrawTask[]): void {
    this.isProcessing = true;
    
    try {
      // 选择最高优先级的任务
      const task = tasks.reduce((highest, current) => 
        this.PRIORITY_WEIGHTS[current.priority] > this.PRIORITY_WEIGHTS[highest.priority] 
          ? current : highest
      );
      
      this.executeRedraw(task);
      
      // 清理已处理的立即任务
      for (const t of tasks) {
        this.pendingTasks.delete(t.id);
      }
    } finally {
      this.isProcessing = false;
      
      // 继续处理其他任务
      if (this.pendingTasks.size > 0) {
        this.scheduleExecution();
      }
    }
  }

  /**
   * 处理待处理的任务
   */
  private processPendingTasks(): void {
    if (this.pendingTasks.size === 0) {
      return;
    }

    this.isProcessing = true;
    const startTime = performance.now();

    try {
      // 按优先级排序任务
      const sortedTasks = Array.from(this.pendingTasks.values())
        .sort((a, b) => {
          const priorityDiff = this.PRIORITY_WEIGHTS[b.priority] - this.PRIORITY_WEIGHTS[a.priority];
          if (priorityDiff !== 0) return priorityDiff;
          return a.timestamp - b.timestamp; // 相同优先级按时间排序
        });

      // 执行最高优先级的任务
      const task = sortedTasks[0];
      this.executeRedraw(task);
      this.pendingTasks.delete(task.id);

      // 记录性能数据
      const frameTime = performance.now() - startTime;
      this.frameTimeHistory.push(frameTime);
      this.lastRedrawTime = performance.now();

    } finally {
      this.isProcessing = false;
      
      // 继续处理剩余任务
      if (this.pendingTasks.size > 0) {
        this.scheduleExecution();
      }
    }
  }

  /**
   * 执行重绘
   */
  private executeRedraw(task: RedrawTask): void {
    const strategy = this.getRedrawStrategy(task);

    // 根据策略选择绘制函数
    // 只有在明确要求简化事件时才使用轻量级绘制
    if (strategy.simplifiedEvents) {
      this.drawWaveformLightweight(strategy);
    } else {
      this.drawWaveformMain(strategy);
    }

    // 如果策略要求清除缓存
    if (!strategy.useCache || task.force) {
      this.clearCache();
    }
  }

  /**
   * 获取重绘策略
   */
  private getRedrawStrategy(task: RedrawTask): RedrawStrategy {
    const baseStrategy = { ...this.REDRAW_STRATEGIES[task.type] };
    
    // 根据当前性能动态调整策略
    const metrics = this.getPerformanceMetrics();
    if (!metrics.isPerformanceGood) {
      // 性能不佳时使用更激进的优化
      baseStrategy.skipGrid = true;
      baseStrategy.skipGuides = true;
      baseStrategy.simplifiedEvents = true;
      baseStrategy.maxFrameTime = Math.min(baseStrategy.maxFrameTime, 12);
    }
    
    return baseStrategy;
  }

  /**
   * 获取最后一次重绘时间
   */
  getLastRedrawTime(): number {
    return this.lastRedrawTime;
  }
}

/**
 * 便捷的重绘管理器创建函数
 */
export function createSmartRedrawManager(
  drawWaveformMain: (strategy: RedrawStrategy) => void,
  drawWaveformLightweight: (strategy: RedrawStrategy) => void,
  clearCache: () => void
): SmartRedrawManager {
  return new SmartRedrawManager(drawWaveformMain, drawWaveformLightweight, clearCache);
}
