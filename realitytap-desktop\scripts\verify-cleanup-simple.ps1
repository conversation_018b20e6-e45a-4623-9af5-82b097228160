# Simple PowerShell Cleanup Verification Script
param(
    [switch]$Verbose = $false
)

$ErrorActionPreference = "Stop"

Write-Host "Verifying PowerShell code cleanup..." -ForegroundColor Green

# Get project root directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

Write-Host "Project root: $ProjectRoot" -ForegroundColor Cyan

# Change to project root directory
Set-Location $ProjectRoot

# Define PowerShell-related keywords to search for
$PowerShellKeywords = @(
    "powershell.exe",
    "prepare_powershell_install",
    "Show-Notification.*Add-Type",
    "BalloonTip",
    "NotifyIcon"
)

$FoundIssues = @()
$TotalFilesChecked = 0

Write-Host "Checking for PowerShell remnants..." -ForegroundColor Yellow

# Check Rust files
$RustFiles = Get-ChildItem -Path "src-tauri\src\**\*.rs" -Recurse -ErrorAction SilentlyContinue

foreach ($File in $RustFiles) {
    $TotalFilesChecked++
    $Content = Get-Content -Path $File.FullName -Raw -ErrorAction SilentlyContinue
    
    if ($Content) {
        foreach ($Keyword in $PowerShellKeywords) {
            if ($Content -match $Keyword) {
                $FoundIssues += [PSCustomObject]@{
                    File = $File.FullName.Replace($ProjectRoot, "").TrimStart('\')
                    Keyword = $Keyword
                }
            }
        }
    }
}

Write-Host "Check results:" -ForegroundColor Cyan
Write-Host "  Files checked: $TotalFilesChecked" -ForegroundColor Gray
Write-Host "  Issues found: $($FoundIssues.Count)" -ForegroundColor Gray

if ($FoundIssues.Count -eq 0) {
    Write-Host ""
    Write-Host "SUCCESS: No PowerShell remnants found!" -ForegroundColor Green
    Write-Host "PowerShell code cleanup completed!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "WARNING: Found PowerShell remnants:" -ForegroundColor Yellow
    
    foreach ($Issue in $FoundIssues) {
        Write-Host "  File: $($Issue.File)" -ForegroundColor Red
        Write-Host "  Keyword: $($Issue.Keyword)" -ForegroundColor Red
        Write-Host ""
    }
    
    Write-Host "Please clean up the above PowerShell remnants" -ForegroundColor Red
    exit 1
}

# Check if PowerShell test files exist
Write-Host ""
Write-Host "Checking for PowerShell test files..." -ForegroundColor Yellow

$PowerShellTestFiles = Get-ChildItem -Path "scripts\*powershell*.ps1" -ErrorAction SilentlyContinue

if ($PowerShellTestFiles.Count -eq 0) {
    Write-Host "SUCCESS: No PowerShell test files found" -ForegroundColor Green
} else {
    Write-Host "WARNING: Found PowerShell test files:" -ForegroundColor Yellow
    foreach ($File in $PowerShellTestFiles) {
        Write-Host "  - $($File.Name)" -ForegroundColor Red
    }
}

# Verify independent installer exists
Write-Host ""
Write-Host "Verifying independent installer..." -ForegroundColor Yellow

$InstallerFiles = @(
    "installer\src\main.rs",
    "installer\Cargo.toml",
    "installer\src\notification.rs",
    "installer\src\config.rs"
)

$MissingFiles = @()
foreach ($File in $InstallerFiles) {
    if (-not (Test-Path $File)) {
        $MissingFiles += $File
    }
}

if ($MissingFiles.Count -eq 0) {
    Write-Host "SUCCESS: Independent installer files are complete" -ForegroundColor Green
} else {
    Write-Host "WARNING: Missing installer files:" -ForegroundColor Yellow
    foreach ($File in $MissingFiles) {
        Write-Host "  - $File" -ForegroundColor Red
    }
}

# Summary
Write-Host ""
Write-Host "Cleanup verification summary:" -ForegroundColor Cyan

$PowerShellClean = $FoundIssues.Count -eq 0
$TestFilesClean = $PowerShellTestFiles.Count -eq 0
$InstallerComplete = $MissingFiles.Count -eq 0

Write-Host "  PowerShell code cleanup: $(if ($PowerShellClean) { 'COMPLETE' } else { 'INCOMPLETE' })" -ForegroundColor $(if ($PowerShellClean) { 'Green' } else { 'Red' })
Write-Host "  Test files cleanup: $(if ($TestFilesClean) { 'COMPLETE' } else { 'INCOMPLETE' })" -ForegroundColor $(if ($TestFilesClean) { 'Green' } else { 'Red' })
Write-Host "  Independent installer: $(if ($InstallerComplete) { 'COMPLETE' } else { 'INCOMPLETE' })" -ForegroundColor $(if ($InstallerComplete) { 'Green' } else { 'Red' })

if ($PowerShellClean -and $TestFilesClean -and $InstallerComplete) {
    Write-Host ""
    Write-Host "SUCCESS: PowerShell cleanup verification passed!" -ForegroundColor Green
    Write-Host "You can now use the independent installer for OTA updates" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "FAILED: Cleanup verification failed, please resolve the above issues" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "  1. Build with installer: .\scripts\build-with-installer.ps1" -ForegroundColor White
Write-Host "  2. Test OTA update functionality" -ForegroundColor White
Write-Host ""
