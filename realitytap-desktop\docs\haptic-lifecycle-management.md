# RealityTap Haptic 生命周期管理指南

## 概述

本文档介绍了 RealityTap 桌面应用中 librtcore 的生命周期管理机制，解决了以下关键问题：

1. **确保 `awa_realitytap_init` 只被调用一次**
2. **提供安全的资源清理机制**
3. **支持安全的重新初始化**
4. **防止资源泄漏和重复初始化**

## 系统架构总览

```mermaid
graph TB
    subgraph "前端层 (TypeScript/Vue)"
        A[Vue 组件] --> B[useHapticManager]
        B --> C[hapticApi]
    end

    subgraph "Tauri Commands 层 (Rust)"
        C --> D[haptic_init]
        C --> E[haptic_cleanup]
        C --> F[haptic_reinit]
        C --> G[haptic_force_reset]
    end

    subgraph "生命周期管理层 (Rust)"
        D --> H[LibraryLifecycleManager]
        E --> H
        F --> H
        G --> H

        H --> I[状态管理]
        H --> J[安全机制]
        H --> K[资源管理]
    end

    subgraph "FFI 层 (Rust)"
        H --> L[load_library]
        H --> M[unload_library]
        H --> N[get_api]
    end

    subgraph "Native 层 (C/C++)"
        L --> O[librtcore.dll]
        M --> O
        N --> O

        O --> P[awa_realitytap_init]
        O --> Q[awa_realitytap_stop_vibration]
        O --> R[awa_realitytap_reinit]
    end

    subgraph "状态跟踪"
        I --> S[NotInitialized]
        I --> T[Initializing]
        I --> U[Initialized]
        I --> V[Cleaning]
        I --> W[Error]
    end

    subgraph "安全保障"
        J --> X[双重检查锁定]
        J --> Y[原子操作]
        J --> Z[参数缓存]
    end

    subgraph "资源控制"
        K --> AA[库加载/卸载]
        K --> BB[状态重置]
        K --> CC[内存清理]
    end
```

## 问题背景

librtcore 原生 API 存在以下限制：
- 缺少明确的资源清理函数（如 `awa_realitytap_cleanup`）
- 重复调用 `awa_realitytap_init` 可能导致未定义行为
- 没有内置的状态跟踪机制

## 解决方案架构

### 1. 整体架构图

```mermaid
graph TB
    A[前端 useHapticManager] --> B[Rust Commands Layer]
    B --> C[LibraryLifecycleManager]
    C --> D[FFI Layer]
    D --> E[librtcore.dll]

    C --> F[状态管理]
    F --> F1[NotInitialized]
    F --> F2[Initializing]
    F --> F3[Initialized]
    F --> F4[Reinitializing]
    F --> F5[Cleaning]
    F --> F6[Cleaned]
    F --> F7[Error]

    C --> G[安全机制]
    G --> G1[双重检查锁定]
    G --> G2[原子操作]
    G --> G3[参数缓存]
    G --> G4[错误恢复]

    C --> H[资源管理]
    H --> H1[库加载/卸载]
    H --> H2[状态重置]
    H --> H3[内存清理]
```

### 2. 生命周期管理器 (LibraryLifecycleManager)

位于 `src-tauri/src/haptic/lifecycle.rs`，提供：

```rust
pub enum LibraryState {
    NotInitialized,    // 未初始化
    Initializing,      // 正在初始化
    Initialized,       // 已初始化
    Reinitializing,    // 正在重新初始化
    Cleaning,          // 正在清理
    Cleaned,           // 清理完成
    Error(String),     // 错误状态
}
```

### 3. 状态转换图

```mermaid
stateDiagram-v2
    [*] --> NotInitialized
    NotInitialized --> Initializing : safe_initialize()
    Initializing --> Initialized : 成功
    Initializing --> Error : 失败

    Initialized --> Cleaning : safe_cleanup()
    Cleaning --> Cleaned : 成功
    Cleaning --> Error : 失败

    Initialized --> Reinitializing : safe_reinitialize()
    Reinitializing --> Initialized : 成功
    Reinitializing --> Error : 失败

    Error --> NotInitialized : force_reset()
    Cleaned --> NotInitialized : 可重新初始化

    Error --> Error : 错误状态保持
```

### 4. 初始化流程图

```mermaid
sequenceDiagram
    participant Frontend as 前端组件
    participant Manager as useHapticManager
    participant Commands as Rust Commands
    participant Lifecycle as LifecycleManager
    participant FFI as FFI Layer
    participant Core as librtcore

    Frontend->>Manager: initialize(configs)
    Manager->>Commands: haptic_init(configs)
    Commands->>Lifecycle: safe_initialize(params)

    Lifecycle->>Lifecycle: 检查状态（双重锁定）
    alt 已初始化
        Lifecycle-->>Commands: 跳过初始化
    else 未初始化
        Lifecycle->>Lifecycle: 设置 Initializing 状态
        Lifecycle->>FFI: load_library()
        FFI->>Core: 加载 librtcore.dll
        Lifecycle->>FFI: awa_realitytap_init()
        FFI->>Core: 调用初始化
        Core-->>FFI: 返回结果
        FFI-->>Lifecycle: 返回结果
        Lifecycle->>Lifecycle: 设置 Initialized 状态
    end

    Lifecycle-->>Commands: 返回结果
    Commands-->>Manager: 返回结果
    Manager-->>Frontend: 初始化完成
```

### 5. 核心功能

#### 安全初始化
- 双重检查锁定模式防止重复初始化
- 原子操作确保线程安全
- 参数缓存用于重新初始化

#### 资源清理
- 调用 `awa_realitytap_stop_vibration()` 停止所有活动
- 卸载动态库强制清理资源
- 重置所有状态标志

#### 重新初始化
- 先清理现有资源
- 等待清理完成
- 重新加载库并初始化

## API 使用指南

### 1. 清理流程图

```mermaid
sequenceDiagram
    participant Frontend as 前端组件
    participant Manager as useHapticManager
    participant Commands as Rust Commands
    participant Lifecycle as LifecycleManager
    participant FFI as FFI Layer
    participant Core as librtcore

    Frontend->>Manager: cleanup()
    Manager->>Commands: haptic_cleanup()
    Commands->>Lifecycle: safe_cleanup()

    Lifecycle->>Lifecycle: 设置 Cleaning 状态
    Lifecycle->>FFI: awa_realitytap_stop_vibration()
    FFI->>Core: 停止所有振动
    Core-->>FFI: 返回结果

    Lifecycle->>FFI: unload_library()
    FFI->>FFI: 卸载 librtcore.dll
    FFI-->>Lifecycle: 卸载完成

    Lifecycle->>Lifecycle: 重置所有状态
    Lifecycle->>Lifecycle: 设置 Cleaned 状态

    Lifecycle-->>Commands: 清理完成
    Commands-->>Manager: 清理完成
    Manager-->>Frontend: 清理成功
```

### 2. 前端使用

```typescript
import { useHapticManager } from '@/composables/haptics/useHapticManager';

const hapticManager = useHapticManager();

// 标准初始化流程
const initHaptic = async () => {
  try {
    // 1. 初始化 Channel 系统
    await hapticManager.initChannelSystem();
    
    // 2. 启动消息处理器
    await hapticManager.startMessageProcessor();
    
    // 3. 安全初始化（确保只调用一次）
    const config = hapticUtils.createDefaultConfig(0);
    await hapticManager.initialize([config]);
    
    // 4. 启动播放系统
    await hapticManager.start();
    
    console.log('触觉反馈初始化成功');
  } catch (error) {
    console.error('初始化失败:', error);
  }
};

// 安全清理
const cleanupHaptic = async () => {
  try {
    await hapticManager.cleanup();
    console.log('资源清理成功');
  } catch (error) {
    console.error('清理失败:', error);
  }
};

// 重新初始化
const reinitHaptic = async () => {
  try {
    const newConfig = hapticUtils.createDefaultConfig(0, 'new_motor.bin');
    await hapticManager.reinitialize([newConfig]);
    console.log('重新初始化成功');
  } catch (error) {
    console.error('重新初始化失败:', error);
  }
};

// 错误恢复
const recoverFromError = async () => {
  try {
    await hapticManager.forceReset();
    console.log('强制重置成功');
  } catch (error) {
    console.error('强制重置失败:', error);
  }
};
```

### 3. 组件生命周期管理图

```mermaid
graph LR
    A[组件挂载] --> B[初始化 Channel]
    B --> C[启动消息处理器]
    C --> D[安全初始化 librtcore]
    D --> E[启动播放系统]
    E --> F[组件运行中]

    F --> G[组件卸载]
    G --> H[停止播放]
    H --> I[停止消息处理器]
    I --> J[清理 librtcore 资源]
    J --> K[组件销毁]

    F --> L[错误发生]
    L --> M[强制重置]
    M --> N[重新初始化]
    N --> F
```

### 4. 组件生命周期管理

```vue
<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue';
import { useHapticManager } from '@/composables/haptics/useHapticManager';

const hapticManager = useHapticManager();

onMounted(async () => {
  // 组件挂载时初始化
  await initHaptic();
});

onUnmounted(async () => {
  // 组件卸载时清理资源
  try {
    await hapticManager.cleanup();
  } catch (error) {
    console.error('清理资源失败:', error);
  }
});
</script>
```

### 5. 状态监控架构图

```mermaid
graph TB
    A[状态监控系统] --> B[前端状态]
    A --> C[后端状态]
    A --> D[生命周期状态]

    B --> B1[isInitialized]
    B --> B2[isStarted]
    B --> B3[isChannelInitialized]
    B --> B4[isMessageProcessorRunning]
    B --> B5[connectionStatus]
    B --> B6[error]

    C --> C1[manager_initialized]
    C --> C2[manager_started]
    C --> C3[device_count]

    D --> D1[lifecycle_state]
    D --> D2[is_ever_initialized]
    D --> D3[init_time]
    D --> D4[init_count]

    A --> E[状态同步机制]
    E --> E1[定期轮询]
    E --> E2[事件驱动更新]
    E --> E3[错误状态检测]
```

### 6. 状态监控

```typescript
// 监控生命周期状态
const monitorLifecycle = async () => {
  try {
    const status = await hapticManager.getLifecycleStatus();
    console.log('生命周期状态:', status);

    // 状态包含：
    // - lifecycle_state: 当前状态
    // - is_ever_initialized: 是否曾经初始化过
    // - init_time: 初始化时间
    // - init_count: 初始化次数
    // - manager_initialized: 管理器初始化状态
    // - manager_started: 管理器启动状态
    // - device_count: 设备数量
  } catch (error) {
    console.error('获取状态失败:', error);
  }
};

// 监听状态变化
watch(() => hapticManager.isInitialized.value, (initialized) => {
  console.log('初始化状态变化:', initialized);
});

watch(() => hapticManager.error.value, (error) => {
  if (error) {
    console.error('触觉反馈错误:', error);
    // 可以触发错误恢复
    recoverFromError();
  }
});
```

## 最佳实践

### 1. 初始化顺序

```typescript
// ✅ 推荐的初始化顺序
const initializeHapticSystem = async () => {
  try {
    // 1. 检查是否已初始化
    const status = await hapticManager.getLifecycleStatus();
    if (status.lifecycle_state === 'Initialized') {
      console.log('已初始化，跳过');
      return;
    }

    // 2. 初始化 Channel 系统
    await hapticManager.initChannelSystem();
    
    // 3. 启动消息处理器
    await hapticManager.startMessageProcessor();
    
    // 4. 初始化触觉库（自动防重复）
    const config = hapticUtils.createDefaultConfig(0);
    await hapticManager.initialize([config]);
    
    // 5. 启动播放系统
    await hapticManager.start();
    
  } catch (error) {
    console.error('初始化失败:', error);
    throw error;
  }
};
```

### 2. 错误处理流程图

```mermaid
flowchart TD
    A[操作失败] --> B{错误类型}

    B -->|初始化错误| C[检查状态]
    B -->|播放错误| D[检查连接]
    B -->|资源错误| E[强制重置]

    C --> C1{已初始化?}
    C1 -->|是| C2[跳过初始化]
    C1 -->|否| C3[重新初始化]

    D --> D1{连接正常?}
    D1 -->|是| D2[重试操作]
    D1 -->|否| D3[重新连接]

    E --> E1[强制重置]
    E1 --> E2[清理状态]
    E2 --> E3[重新初始化]

    C2 --> F[操作成功]
    C3 --> F
    D2 --> F
    D3 --> F
    E3 --> F

    C3 -->|失败| G[报告错误]
    D3 -->|失败| G
    E3 -->|失败| G
```

### 3. 错误处理和恢复

```typescript
// ✅ 完善的错误处理
const robustHapticOperation = async () => {
  try {
    await hapticManager.playEffect(1, 80);
  } catch (error) {
    console.error('播放失败:', error);

    // 尝试恢复
    try {
      await hapticManager.forceReset();

      // 重新初始化
      const config = hapticUtils.createDefaultConfig(0);
      await hapticManager.initialize([config]);
      await hapticManager.start();

      // 重试操作
      await hapticManager.playEffect(1, 80);

    } catch (recoveryError) {
      console.error('恢复失败:', recoveryError);
      throw recoveryError;
    }
  }
};
```

### 4. 资源清理流程图

```mermaid
flowchart TD
    A[开始清理] --> B[停止播放系统]
    B --> C[停止消息处理器]
    C --> D[清理事件监听器]
    D --> E[调用 librtcore 清理]

    E --> F[停止所有振动]
    F --> G[卸载动态库]
    G --> H[重置状态标志]
    H --> I[清理完成]

    B -->|失败| J[记录错误]
    C -->|失败| J
    D -->|失败| J
    E -->|失败| K[强制清理]

    J --> L[继续下一步]
    L --> C

    K --> M[强制卸载库]
    M --> N[强制重置状态]
    N --> O[强制清理完成]
```

### 5. 资源清理

```typescript
// ✅ 正确的清理顺序
const cleanupHapticSystem = async () => {
  try {
    // 1. 停止播放
    await hapticManager.stop();

    // 2. 停止消息处理器
    await hapticManager.stopMessageProcessor();

    // 3. 清理 librtcore 资源
    await hapticManager.cleanup();

    console.log('清理完成');
  } catch (error) {
    console.error('清理失败:', error);

    // 强制清理
    try {
      await hapticManager.forceReset();
    } catch (forceError) {
      console.error('强制清理失败:', forceError);
    }
  }
};
```

## 故障排除

### 1. 常见问题

#### 重复初始化错误
```
错误: librtcore 正在初始化中，请等待
解决: 等待当前初始化完成，或使用 forceReset() 强制重置
```

#### 资源清理失败
```
错误: 资源清理失败
解决: 使用 forceReset() 强制重置所有状态
```

#### 状态不一致
```
错误: 管理器状态与 librtcore 状态不一致
解决: 调用 getLifecycleStatus() 检查状态，必要时强制重置
```

### 2. 调试工具

```typescript
// 调试状态
const debugHapticState = async () => {
  const status = await hapticManager.getLifecycleStatus();
  console.table(status);
  
  console.log('管理器状态:', {
    isInitialized: hapticManager.isInitialized.value,
    isStarted: hapticManager.isStarted.value,
    isChannelInitialized: hapticManager.isChannelInitialized.value,
    isMessageProcessorRunning: hapticManager.isMessageProcessorRunning.value,
    connectionStatus: hapticManager.connectionStatus.value,
    error: hapticManager.error.value,
  });
};
```

## 注意事项

1. **线程安全**: 所有生命周期操作都是线程安全的
2. **状态一致性**: 前端状态与后端状态保持同步
3. **错误恢复**: 提供多级错误恢复机制
4. **资源管理**: 自动管理内存和资源释放
5. **性能优化**: 避免不必要的重复操作

## 架构图索引

本文档包含以下架构图，帮助理解系统设计：

1. **[系统架构总览](#系统架构总览)** - 完整的系统分层架构
2. **[整体架构图](#1-整体架构图)** - 核心组件关系图
3. **[状态转换图](#3-状态转换图)** - librtcore 状态转换流程
4. **[初始化流程图](#4-初始化流程图)** - 安全初始化的时序图
5. **[清理流程图](#1-清理流程图)** - 资源清理的时序图
6. **[组件生命周期管理图](#3-组件生命周期管理图)** - Vue 组件生命周期
7. **[状态监控架构图](#5-状态监控架构图)** - 状态监控系统设计
8. **[错误处理流程图](#2-错误处理流程图)** - 错误处理和恢复机制
9. **[资源清理流程图](#4-资源清理流程图)** - 详细的清理步骤

## 快速导航

- **开发者**: 查看 [API 使用指南](#api-使用指南) 和 [最佳实践](#最佳实践)
- **架构师**: 查看 [解决方案架构](#解决方案架构) 和 [架构图索引](#架构图索引)
- **运维人员**: 查看 [故障排除](#故障排除) 和 [调试工具](#2-调试工具)
- **测试人员**: 查看 [状态监控](#6-状态监控) 和 [错误处理](#3-错误处理和恢复)

## 更新日志

### v2.0.0
- 新增生命周期管理器
- 实现安全的初始化和清理机制
- 添加状态跟踪和监控
- 提供错误恢复功能
- 完善架构图和文档
