/**
 * 语言管理 Store
 * Language Management Store
 */

import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import {
  type SupportedLocale,
  SUPPORTED_LOCALES,
  DEFAULT_LOCALE,
  getInitialLanguage,
  switchLanguage as switchI18nLanguage,
  // getCurrentLanguage,
  setStoredLanguage,
} from "@/locales";
import { logger, LogModule } from "@/utils/logger/logger";

export const useLanguageStore = defineStore("language", () => {
  // === 状态 ===
  const currentLocale = ref<SupportedLocale>(getInitialLanguage());
  const isInitialized = ref(false);

  // === 计算属性 ===
  const currentLanguageName = computed(() => {
    return SUPPORTED_LOCALES[currentLocale.value];
  });

  const availableLanguages = computed(() => {
    return Object.entries(SUPPORTED_LOCALES).map(([code, name]) => ({
      code: code as SupportedLocale,
      name,
      isCurrent: code === currentLocale.value,
    }));
  });

  const isDefaultLanguage = computed(() => {
    return currentLocale.value === DEFAULT_LOCALE;
  });

  // === 方法 ===

  /**
   * 初始化语言设置
   */
  const initializeLanguage = async (): Promise<void> => {
    try {
      const initialLang = getInitialLanguage();
      await setLanguage(initialLang);
      isInitialized.value = true;

      logger.info(LogModule.GENERAL, `Language initialized: ${initialLang} (${SUPPORTED_LOCALES[initialLang]})`);
    } catch (error) {
      logger.error(LogModule.GENERAL, "Failed to initialize language", error);
      // 回退到默认语言
      await setLanguage(DEFAULT_LOCALE);
      isInitialized.value = true;
    }
  };

  /**
   * 设置语言
   */
  const setLanguage = async (locale: SupportedLocale): Promise<void> => {
    if (!(locale in SUPPORTED_LOCALES)) {
      logger.warn(LogModule.GENERAL, `Unsupported locale: ${locale}, falling back to ${DEFAULT_LOCALE}`);
      locale = DEFAULT_LOCALE;
    }

    try {
      // 更新 i18n
      switchI18nLanguage(locale);

      // 更新本地状态
      currentLocale.value = locale;

      // 保存到存储
      setStoredLanguage(locale);

      logger.info(LogModule.GENERAL, `Language switched to: ${locale} (${SUPPORTED_LOCALES[locale]})`);
    } catch (error) {
      logger.error(LogModule.GENERAL, "Failed to set language", error);
      throw error;
    }
  };

  /**
   * 切换到下一个语言
   */
  const switchToNextLanguage = async (): Promise<void> => {
    const locales = Object.keys(SUPPORTED_LOCALES) as SupportedLocale[];
    const currentIndex = locales.indexOf(currentLocale.value);
    const nextIndex = (currentIndex + 1) % locales.length;
    const nextLocale = locales[nextIndex];

    await setLanguage(nextLocale);
  };

  /**
   * 重置到默认语言
   */
  const resetToDefaultLanguage = async (): Promise<void> => {
    await setLanguage(DEFAULT_LOCALE);
  };

  /**
   * 检查语言是否已初始化
   */
  const waitForInitialization = (): Promise<void> => {
    return new Promise((resolve) => {
      if (isInitialized.value) {
        resolve();
        return;
      }

      const checkInitialized = () => {
        if (isInitialized.value) {
          resolve();
        } else {
          setTimeout(checkInitialized, 10);
        }
      };

      checkInitialized();
    });
  };

  /**
   * 获取语言统计信息
   */
  const getLanguageStats = () => {
    return {
      current: currentLocale.value,
      currentName: currentLanguageName.value,
      isDefault: isDefaultLanguage.value,
      isInitialized: isInitialized.value,
      supportedCount: Object.keys(SUPPORTED_LOCALES).length,
      availableLanguages: availableLanguages.value,
    };
  };

  // === 监听语言变化事件 ===
  if (typeof window !== "undefined") {
    window.addEventListener("language-changed", (event: Event) => {
      const customEvent = event as CustomEvent;
      const { locale } = customEvent.detail;
      if (locale !== currentLocale.value) {
        currentLocale.value = locale;
      }
    });
  }

  return {
    // 状态
    currentLocale: readonly(currentLocale),
    isInitialized: readonly(isInitialized),

    // 计算属性
    currentLanguageName,
    availableLanguages,
    isDefaultLanguage,

    // 方法
    initializeLanguage,
    setLanguage,
    switchToNextLanguage,
    resetToDefaultLanguage,
    waitForInitialization,
    getLanguageStats,
  };
});
