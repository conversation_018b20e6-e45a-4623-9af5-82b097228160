// FFmpeg-based audio processing utilities
use crate::error::{<PERSON>rro<PERSON>, Result};
use crate::models::audio::AudioInfo;
use log;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::process::Command;
use std::fs;
use std::env;
use tempfile::NamedTempFile;

/// FFprobe stream information
#[derive(Debug, Deserialize, Serialize)]
pub struct FFprobeStream {
    pub codec_type: String,
    pub codec_name: Option<String>,
    pub sample_rate: Option<String>,
    pub channels: Option<u32>,
    pub duration: Option<String>,
    pub bit_rate: Option<String>,
}

/// FFprobe format information
#[derive(Debug, Deserialize, Serialize)]
pub struct FFprobeFormat {
    pub duration: Option<String>,
    pub bit_rate: Option<String>,
    pub nb_streams: u32,
}

/// FFprobe output structure
#[derive(Debug, Deserialize, Serialize)]
pub struct FFprobeOutput {
    pub streams: Vec<FFprobeStream>,
    pub format: FFprobeFormat,
}

/// Get the path to the bundled ffprobe executable
fn get_ffprobe_path() -> String {
    let exe_ext = if cfg!(target_os = "windows") { ".exe" } else { "" };
    let exe_name = format!("ffprobe{}", exe_ext);

    // Try to get the executable directory (for both dev and production)
    if let Ok(current_exe) = env::current_exe() {
        if let Some(exe_dir) = current_exe.parent() {
            // First try: same directory as executable (production)
            let bundled_path = exe_dir.join(&exe_name);
            if bundled_path.exists() {
                log::info!("使用预编译的 ffprobe (生产环境): {}", bundled_path.display());
                return bundled_path.to_string_lossy().to_string();
            }

            // Second try: resources directory (alternative production layout)
            let resources_path = exe_dir.join("resources").join(&exe_name);
            if resources_path.exists() {
                log::info!("使用预编译的 ffprobe (资源目录): {}", resources_path.display());
                return resources_path.to_string_lossy().to_string();
            }
        }
    }

    // Fallback to system ffprobe
    log::info!("使用系统 ffprobe");
    "ffprobe".to_string()
}

/// Get the path to the bundled ffmpeg executable
fn get_ffmpeg_path() -> String {
    let exe_ext = if cfg!(target_os = "windows") { ".exe" } else { "" };
    let exe_name = format!("ffmpeg{}", exe_ext);

    // Try to get the executable directory (for both dev and production)
    if let Ok(current_exe) = env::current_exe() {
        if let Some(exe_dir) = current_exe.parent() {
            // First try: same directory as executable (production)
            let bundled_path = exe_dir.join(&exe_name);
            if bundled_path.exists() {
                log::info!("使用预编译的 ffmpeg (生产环境): {}", bundled_path.display());
                return bundled_path.to_string_lossy().to_string();
            }

            // Second try: resources directory (alternative production layout)
            let resources_path = exe_dir.join("resources").join(&exe_name);
            if resources_path.exists() {
                log::info!("使用预编译的 ffmpeg (资源目录): {}", resources_path.display());
                return resources_path.to_string_lossy().to_string();
            }
        }
    }

    // Fallback to system ffmpeg
    log::info!("使用系统 ffmpeg");
    "ffmpeg".to_string()
}

/// Check if ffprobe is available (bundled or system)
pub fn check_ffprobe_available() -> bool {
    let ffprobe_path = get_ffprobe_path();
    match Command::new(&ffprobe_path).arg("-version").output() {
        Ok(output) => output.status.success(),
        Err(_) => false,
    }
}

/// Check if ffmpeg is available (bundled or system)
pub fn check_ffmpeg_available() -> bool {
    let ffmpeg_path = get_ffmpeg_path();
    match Command::new(&ffmpeg_path).arg("-version").output() {
        Ok(output) => output.status.success(),
        Err(_) => false,
    }
}

/// Extract audio information from MP4 file using ffprobe
pub fn extract_mp4_info_with_ffprobe(abs_path: &PathBuf) -> Result<Option<AudioInfo>> {
    log::info!("使用 ffprobe 提取 MP4 音频信息: {}", abs_path.display());

    // Check if ffprobe is available
    if !check_ffprobe_available() {
        return Err(Error::Io(
            "ffprobe 未找到。请安装 FFmpeg 工具包。\n\
            下载地址: https://ffmpeg.org/download.html".to_string()
        ));
    }

    // Run ffprobe command using the appropriate path
    let ffprobe_path = get_ffprobe_path();
    let output = Command::new(&ffprobe_path)
        .arg("-v")
        .arg("quiet")
        .arg("-print_format")
        .arg("json")
        .arg("-show_format")
        .arg("-show_streams")
        .arg(abs_path.as_os_str())
        .output()
        .map_err(|e| Error::Io(format!("执行 ffprobe 命令失败: {}", e)))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(Error::Io(format!("ffprobe 执行失败: {}", stderr)));
    }

    // Parse JSON output
    let stdout = String::from_utf8_lossy(&output.stdout);
    let probe_result: FFprobeOutput = serde_json::from_str(&stdout)
        .map_err(|e| Error::Io(format!("解析 ffprobe 输出失败: {}", e)))?;

    // Find the first audio stream
    let audio_stream = probe_result.streams
        .iter()
        .find(|stream| stream.codec_type == "audio");

    if let Some(stream) = audio_stream {
        // Extract duration (prefer stream duration, fallback to format duration)
        let duration_str = stream.duration
            .as_ref()
            .or(probe_result.format.duration.as_ref());

        let duration_ms = if let Some(duration_str) = duration_str {
            match duration_str.parse::<f64>() {
                Ok(duration_seconds) => (duration_seconds * 1000.0) as u64,
                Err(_) => {
                    log::warn!("无法解析音频时长: {}", duration_str);
                    0
                }
            }
        } else {
            log::warn!("未找到音频时长信息");
            0
        };

        // Extract sample rate
        let sample_rate = if let Some(sample_rate_str) = &stream.sample_rate {
            match sample_rate_str.parse::<u32>() {
                Ok(rate) => rate,
                Err(_) => {
                    log::warn!("无法解析采样率: {}", sample_rate_str);
                    44100 // Default fallback
                }
            }
        } else {
            log::warn!("未找到采样率信息，使用默认值 44100");
            44100
        };

        let audio_info = AudioInfo {
            duration_ms,
            sample_rate,
        };

        log::info!("ffprobe 提取音频信息成功: {:?}", audio_info);
        Ok(Some(audio_info))
    } else {
        log::warn!("MP4 文件中未找到音频流");
        Ok(None)
    }
}

/// Extract complete audio data from MP4 file using ffmpeg
pub fn extract_mp4_audio_data_with_ffmpeg(abs_path: &PathBuf) -> Result<(Vec<f32>, AudioInfo)> {
    log::info!("使用 ffmpeg 提取 MP4 完整音频数据: {}", abs_path.display());

    // First get audio info
    let audio_info = extract_mp4_info_with_ffprobe(abs_path)?
        .ok_or_else(|| Error::Io("无法获取 MP4 音频信息".to_string()))?;

    // Check if ffmpeg is available
    if !check_ffmpeg_available() {
        return Err(Error::Io(
            "ffmpeg 未找到。请安装 FFmpeg 工具包。\n\
            下载地址: https://ffmpeg.org/download.html".to_string()
        ));
    }

    // Create temporary file for raw audio output
    let temp_file = NamedTempFile::new()
        .map_err(|e| Error::Io(format!("创建临时文件失败: {}", e)))?;
    let temp_path = temp_file.path();

    // Extract audio as raw f32 PCM data using the appropriate path
    let ffmpeg_path = get_ffmpeg_path();
    let output = Command::new(&ffmpeg_path)
        .arg("-i")
        .arg(abs_path.as_os_str())
        .arg("-vn") // No video
        .arg("-acodec")
        .arg("pcm_f32le") // 32-bit float PCM, little endian
        .arg("-f")
        .arg("f32le") // Raw f32 format
        .arg("-y") // Overwrite output file
        .arg(temp_path.as_os_str())
        .output()
        .map_err(|e| Error::Io(format!("执行 ffmpeg 命令失败: {}", e)))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(Error::Io(format!("ffmpeg 音频提取失败: {}", stderr)));
    }

    // Read the raw audio data
    let raw_data = fs::read(temp_path)
        .map_err(|e| Error::Io(format!("读取临时音频文件失败: {}", e)))?;

    // Convert bytes to f32 samples
    let mut samples = Vec::with_capacity(raw_data.len() / 4);
    for chunk in raw_data.chunks_exact(4) {
        let sample = f32::from_le_bytes([chunk[0], chunk[1], chunk[2], chunk[3]]);
        samples.push(sample);
    }

    log::info!("ffmpeg 音频数据提取成功，样本数: {}", samples.len());
    Ok((samples, audio_info))
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_ffmpeg_availability() {
        // 测试 ffmpeg 和 ffprobe 是否可用
        let ffmpeg_available = check_ffmpeg_available();
        let ffprobe_available = check_ffprobe_available();

        println!("FFmpeg available: {}", ffmpeg_available);
        println!("FFprobe available: {}", ffprobe_available);

        // 这些函数应该能正常运行，不管是否找到 ffmpeg
        // 在有预编译版本的情况下，应该返回 true
    }

    #[test]
    fn test_path_detection() {
        // 测试路径检测函数
        let ffmpeg_path = get_ffmpeg_path();
        let ffprobe_path = get_ffprobe_path();

        println!("Detected FFmpeg path: {}", ffmpeg_path);
        println!("Detected FFprobe path: {}", ffprobe_path);

        // 路径应该不为空
        assert!(!ffmpeg_path.is_empty());
        assert!(!ffprobe_path.is_empty());
    }

    #[test]
    fn test_mp4_info_extraction_error_handling() {
        // 测试不存在的文件
        let non_existent_file = PathBuf::from("non_existent_file.mp4");
        let result = extract_mp4_info_with_ffprobe(&non_existent_file);

        // 应该返回错误
        assert!(result.is_err());

        if let Err(e) = result {
            println!("Expected error for non-existent file: {}", e);
        }
    }

    #[test]
    fn test_mp4_audio_data_extraction_error_handling() {
        // 测试不存在的文件
        let non_existent_file = PathBuf::from("non_existent_file.mp4");
        let result = extract_mp4_audio_data_with_ffmpeg(&non_existent_file);

        // 应该返回错误
        assert!(result.is_err());

        if let Err(e) = result {
            println!("Expected error for non-existent file: {}", e);
        }
    }
}