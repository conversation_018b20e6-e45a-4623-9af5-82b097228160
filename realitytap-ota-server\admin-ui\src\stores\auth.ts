import { authApi } from '@/api/auth';
import type { AdminUser, LoginRequest } from '@/api/types';
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('admin_token'));
  const user = ref<AdminUser | null>(null);
  const loading = ref(false);

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value);

  // 登录
  const login = async (credentials: LoginRequest) => {
    loading.value = true;
    try {
      const response = await authApi.login(credentials);

      if (response.success && response.data) {
        token.value = response.data.token;
        user.value = response.data.user;

        // 保存到本地存储
        localStorage.setItem('admin_token', response.data.token);
        localStorage.setItem('admin_user', JSON.stringify(response.data.user));

        return { success: true };
      } else {
        throw new Error(response.error?.message || '登录失败');
      }
    } catch (error: any) {
      console.error('Login error:', error);

      // 处理不同类型的错误
      let errorMessage = '登录失败，请检查用户名和密码';

      if (error.response) {
        const { status, data } = error.response;

        if (status === 401) {
          // 401错误通常表示用户名或密码错误
          errorMessage = data?.error?.message || data?.message || '用户名或密码错误，请重新输入';
        } else if (status === 429) {
          errorMessage = '登录尝试过于频繁，请稍后再试';
        } else if (status === 500) {
          errorMessage = '服务器错误，请稍后重试';
        } else {
          errorMessage = data?.error?.message || data?.message || `登录失败 (${status})`;
        }
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络设置';
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        message: errorMessage,
      };
    } finally {
      loading.value = false;
    }
  };

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout();
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除状态和本地存储
      token.value = null;
      user.value = null;
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
    }
  };

  // 获取用户信息
  const fetchProfile = async () => {
    if (!token.value) return false;

    try {
      const response = await authApi.getProfile();
      if (response.success && response.data) {
        user.value = response.data.user;
        return true;
      } else {
        throw new Error('获取用户信息失败');
      }
    } catch (error) {
      console.error('Fetch profile error:', error);
      await logout();
      return false;
    }
  };

  // 初始化认证状态
  const initAuth = async () => {
    const savedToken = localStorage.getItem('admin_token');
    const savedUser = localStorage.getItem('admin_user');

    if (savedToken && savedUser) {
      token.value = savedToken;
      try {
        user.value = JSON.parse(savedUser);

        // 只在必要时验证token（避免刚登录后立即验证）
        // 如果用户信息已存在且token存在，先尝试使用现有状态
        if (user.value && token.value) {
          // 异步验证token有效性，但不阻塞当前流程
          fetchProfile().catch(error => {
            console.warn('Token validation failed in background:', error);
            // 如果验证失败，在后台清除状态，但不立即跳转
            // 让用户继续使用，直到下次API调用时处理
          });
        }
      } catch (error) {
        console.error('Init auth error:', error);
        // 解析用户信息失败时才清除状态
        await logout();
      }
    }
  };

  // 刷新token
  const refreshToken = async () => {
    if (!token.value) return false;

    try {
      const response = await authApi.refreshToken();
      if (response.success && response.data) {
        token.value = response.data.token;
        localStorage.setItem('admin_token', response.data.token);
        return true;
      }
    } catch (error) {
      console.error('Refresh token error:', error);
      await logout();
    }
    return false;
  };

  return {
    // 状态
    token: computed(() => token.value),
    user: computed(() => user.value),
    loading: computed(() => loading.value),
    isAuthenticated,

    // 方法
    login,
    logout,
    fetchProfile,
    initAuth,
    refreshToken,
  };
});
