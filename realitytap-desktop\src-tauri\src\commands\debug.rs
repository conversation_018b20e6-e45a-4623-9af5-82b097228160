// Debug and logging commands for production troubleshooting
use crate::error::Result;
use serde::{Deserialize, Serialize};
use std::fs;
use std::sync::{Arc, Mutex};
use once_cell::sync::Lazy;

/// Debug mode configuration
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct DebugConfig {
    /// Whether debug mode is enabled
    pub enabled: bool,
    /// Debug level (error, warn, info, debug, trace)
    pub level: String,
    /// Whether to log OTA operations in detail
    pub log_ota_operations: bool,
    /// Whether to log device operations in detail
    pub log_device_operations: bool,
}

impl Default for DebugConfig {
    fn default() -> Self {
        Self {
            enabled: cfg!(debug_assertions), // 开发环境启用，生产环境禁用
            level: "info".to_string(),
            log_ota_operations: true,
            log_device_operations: false,
        }
    }
}

// 全局调试配置管理器（内存中）
static RUNTIME_DEBUG_CONFIG: Lazy<Arc<Mutex<DebugConfig>>> = Lazy::new(|| {
    Arc::new(Mutex::new(DebugConfig::default()))
});

/// Get the log file path for the application
#[tauri::command]
pub async fn get_log_file_path(_app: tauri::AppHandle) -> Result<String> {
    log::info!("Getting log file path");

    // Use the same app data directory as configured in lib.rs
    let app_data_dir = crate::commands::app_data::get_app_data_dir()?;
    let log_dir = app_data_dir.join("logs");

    // The log file name should match what we configured in lib.rs
    let log_file = log_dir.join("realitytap-studio.log");

    log::info!("Log file path: {:?}", log_file);

    Ok(log_file.to_string_lossy().to_string())
}

/// Read the content of the log file
#[tauri::command]
pub async fn read_log_file(_app: tauri::AppHandle, lines: Option<usize>) -> Result<String> {
    log::info!("Reading log file content");

    // Use the same app data directory as configured in lib.rs
    let app_data_dir = crate::commands::app_data::get_app_data_dir()?;
    let log_dir = app_data_dir.join("logs");
    let log_file = log_dir.join("realitytap-studio.log");
    
    if !log_file.exists() {
        log::warn!("Log file does not exist: {:?}", log_file);
        return Ok("日志文件不存在或尚未创建".to_string());
    }
    
    let content = fs::read_to_string(&log_file)
        .map_err(|e| crate::error::Error::Io(format!("Failed to read log file: {}", e)))?;
    
    // If lines parameter is specified, return only the last N lines
    if let Some(line_count) = lines {
        let lines: Vec<&str> = content.lines().collect();
        let start_index = if lines.len() > line_count {
            lines.len() - line_count
        } else {
            0
        };
        
        let result = lines[start_index..].join("\n");
        log::info!("Returning last {} lines of log file", line_count);
        Ok(result)
    } else {
        log::info!("Returning full log file content ({} bytes)", content.len());
        Ok(content)
    }
}

/// Clear the log file
#[tauri::command]
pub async fn clear_log_file(_app: tauri::AppHandle) -> Result<()> {
    log::info!("Clearing log file");

    // Use the same app data directory as configured in lib.rs
    let app_data_dir = crate::commands::app_data::get_app_data_dir()?;
    let log_dir = app_data_dir.join("logs");
    let log_file = log_dir.join("realitytap-studio.log");
    
    if log_file.exists() {
        fs::write(&log_file, "")
            .map_err(|e| crate::error::Error::Io(format!("Failed to clear log file: {}", e)))?;
        log::info!("Log file cleared successfully");
    } else {
        log::info!("Log file does not exist, nothing to clear");
    }
    
    Ok(())
}

/// Set debug mode configuration (runtime only, not persisted)
#[tauri::command]
pub async fn set_debug_mode(_app: tauri::AppHandle, config: DebugConfig) -> Result<()> {
    log::info!("Setting runtime debug mode configuration: {:?}", config);

    // 更新运行时配置
    {
        let mut runtime_config = RUNTIME_DEBUG_CONFIG.lock().unwrap();
        *runtime_config = config.clone();
    }

    // 记录配置更改
    if config.enabled {
        log::info!("Debug mode enabled with level: {}", config.level);
        log::info!("OTA operations logging: {}", config.log_ota_operations);
        log::info!("Device operations logging: {}", config.log_device_operations);
    } else {
        log::info!("Debug mode disabled");
    }

    Ok(())
}

/// Get current debug mode configuration
#[tauri::command]
pub async fn get_debug_mode(_app: tauri::AppHandle) -> Result<DebugConfig> {
    let config = RUNTIME_DEBUG_CONFIG.lock().unwrap().clone();
    log::debug!("Debug mode configuration: {:?}", config);
    Ok(config)
}

/// Open external URL or file path
#[tauri::command]
pub async fn open_external_url(url: String) -> Result<()> {
    log::info!("Opening external URL: {}", url);

    // Handle file:// URLs by converting them to proper paths
    let path_to_open = if url.starts_with("file://") {
        url.strip_prefix("file://").unwrap_or(&url).to_string()
    } else {
        url.clone()
    };

    // Use the appropriate command based on the operating system
    let result = if cfg!(target_os = "windows") {
        std::process::Command::new("explorer")
            .arg(&path_to_open)
            .spawn()
    } else if cfg!(target_os = "macos") {
        std::process::Command::new("open")
            .arg(&path_to_open)
            .spawn()
    } else {
        // Linux and other Unix-like systems
        std::process::Command::new("xdg-open")
            .arg(&path_to_open)
            .spawn()
    };

    match result {
        Ok(_) => {
            log::info!("Successfully opened: {}", path_to_open);
            Ok(())
        }
        Err(e) => {
            log::error!("Failed to open {}: {}", path_to_open, e);
            Err(crate::error::Error::Io(format!("Failed to open {}: {}", path_to_open, e)))
        }
    }
}

/// Export logs with system information for troubleshooting
#[tauri::command]
pub async fn export_debug_info(app: tauri::AppHandle) -> Result<String> {
    log::info!("Exporting debug information");

    let app_data_dir = crate::commands::app_data::get_app_data_dir()?;

    // Create exports directory
    let exports_dir = app_data_dir.join("exports");
    fs::create_dir_all(&exports_dir)
        .map_err(|e| crate::error::Error::Io(format!("Failed to create exports directory: {}", e)))?;

    // Use temporary file manager to create a temporary file first
    let temp_manager = crate::utils::temp_file::get_global_temp_manager()?;
    let temp_file = temp_manager.create_temp_file("debug_info", "txt")?;

    // Generate final export file name with timestamp
    let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
    let export_file = exports_dir.join(format!("debug_info_{}.txt", timestamp));
    
    // Collect debug information
    let mut debug_info = String::new();
    debug_info.push_str(&format!("=== RealityTap Studio Debug Information ===\n"));
    debug_info.push_str(&format!("Export Time: {}\n", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")));
    debug_info.push_str(&format!("Application Version: {}\n", env!("CARGO_PKG_VERSION")));
    debug_info.push_str(&format!("Build Mode: {}\n", if cfg!(debug_assertions) { "debug" } else { "release" }));
    debug_info.push_str("\n");
    
    // Add system information
    debug_info.push_str("=== System Information ===\n");
    debug_info.push_str(&format!("OS: {}\n", std::env::consts::OS));
    debug_info.push_str(&format!("Architecture: {}\n", std::env::consts::ARCH));
    debug_info.push_str("\n");
    
    // Add debug configuration
    if let Ok(debug_config) = get_debug_mode(app.clone()).await {
        debug_info.push_str("=== Debug Configuration ===\n");
        debug_info.push_str(&format!("Debug Mode: {}\n", debug_config.enabled));
        debug_info.push_str(&format!("Log Level: {}\n", debug_config.level));
        debug_info.push_str(&format!("OTA Logging: {}\n", debug_config.log_ota_operations));
        debug_info.push_str(&format!("Device Logging: {}\n", debug_config.log_device_operations));
        debug_info.push_str("\n");
    }
    
    // Add recent log entries
    debug_info.push_str("=== Recent Log Entries (Last 100 lines) ===\n");
    if let Ok(log_content) = read_log_file(app.clone(), Some(100)).await {
        debug_info.push_str(&log_content);
    } else {
        debug_info.push_str("Failed to read log file\n");
    }
    
    // Write the debug information to temporary file first with UTF-8 BOM for better cross-platform compatibility
    let utf8_bom = "\u{FEFF}"; // UTF-8 BOM
    let content_with_bom = format!("{}{}", utf8_bom, debug_info);
    fs::write(temp_file.path(), content_with_bom.as_bytes())
        .map_err(|e| crate::error::Error::Io(format!("Failed to write debug info to temp file: {}", e)))?;

    // Atomically move the temporary file to the final location
    fs::rename(temp_file.path(), &export_file)
        .map_err(|e| crate::error::Error::Io(format!("Failed to move temp file to final location: {}", e)))?;

    // Disable auto cleanup since we've moved the file
    let mut temp_file = temp_file;
    temp_file.set_auto_cleanup(false);

    let export_path = export_file.to_string_lossy().to_string();
    log::info!("Debug information exported to: {}", export_path);

    Ok(export_path)
}

/// Clean up temporary files older than specified hours
#[tauri::command]
pub async fn cleanup_temp_files(max_age_hours: Option<u64>) -> Result<usize> {
    log::info!("Cleaning up temporary files");

    let age_hours = max_age_hours.unwrap_or(24); // Default to 24 hours
    let temp_manager = crate::utils::temp_file::get_global_temp_manager()?;
    let cleaned_count = temp_manager.cleanup_old_files(age_hours)?;

    log::info!("Cleaned up {} temporary files older than {} hours", cleaned_count, age_hours);
    Ok(cleaned_count)
}
