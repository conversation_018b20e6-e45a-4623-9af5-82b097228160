import { ref, watch, nextTick, type ComputedRef } from "vue";
import type { TreeOption as NaiveTreeOption } from "naive-ui";
import type { HapticFile, HapticsGroup, RealityTapProject } from "@/types/haptic-project";
import { getItemFromNodeKey, findNodeByKey } from "@/utils/tree/treeUtils";
import type { TreeNode } from "@/utils/tree/treeUtils";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 树形状态管理 Composable
 * 负责管理树形组件的选中状态、展开状态和与项目存储的同步
 */
export function useTreeState(
  projectStore: {
    selectedFileUuid: string | null;
    lastCreatedFileUuid: string | null;
    activeProject: RealityTapProject | null;
  },
  treeData: ComputedRef<TreeNode[]>,
  triggerFileRename: (node: NaiveTreeOption) => void,
  emit: (event: "select-project-item", item: HapticFile | HapticsGroup | null) => void
) {
  // 基础状态
  const selectedTreeKeys = ref<string[]>([]);
  const expandedTreeKeys = ref<string[]>([]);
  const lastSelectedFileKey = ref<string | null>(null);

  /**
   * 确保父分组也被展开
   */
  const ensureParentGroupsExpanded = (groupUuid: string) => {
    const group = projectStore.activeProject?.groups?.find((g) => g.groupUuid === groupUuid);
    if (group && group.parentGroupUuid) {
      const parentKey = `group-${group.parentGroupUuid}`;
      if (!expandedTreeKeys.value.includes(parentKey)) {
        expandedTreeKeys.value.push(parentKey);
      }
      // 递归确保所有父分组都展开
      ensureParentGroupsExpanded(group.parentGroupUuid);
    }
  };

  /**
   * 选择项目项目并发出事件
   */
  const selectProjectItem = async (item: HapticFile | HapticsGroup | null, fromWatcher = false) => {
    // 智能文件切换：检查是否为已选中的文件
    if (item && "fileUuid" in item) {
      // 如果点击的是当前已选中的文件，直接返回，避免重复加载
      // 但如果是从 watcher 调用的，则允许发射事件以确保编辑界面同步
      if (projectStore.selectedFileUuid === item.fileUuid && !fromWatcher) {
        logger.debug(LogModule.PROJECT, "文件已选中，跳过重复加载", { fileName: item.name });
        return;
      }

      if (!fromWatcher) {
        logger.debug(LogModule.PROJECT, "切换到文件，等待前一个组件清理完成", { fileName: item.name });
        // 等待一个微任务周期，确保前一个组件的 onBeforeUnmount 钩子执行完成
        await nextTick();
        // 额外的短暂延迟，确保清理操作完成
        await new Promise((resolve) => setTimeout(resolve, 50));
      }
    }

    emit("select-project-item", item);
  };

  /**
   * 处理树节点选择
   */
  const handleTreeSelect = (keys: string[], _options: (NaiveTreeOption | null)[], meta: { node: NaiveTreeOption | null; action: "select" | "unselect" }) => {
    if (meta.action === "select" && meta.node) {
      const key = String(meta.node.key);
      const isNodeGroup = key.startsWith("group-");

      if (isNodeGroup) {
        // 如果点击的是分组节点且已有文件节点被选中，恢复文件选中状态
        if (lastSelectedFileKey.value && selectedTreeKeys.value.includes(lastSelectedFileKey.value)) {
          nextTick(() => {
            selectedTreeKeys.value = [lastSelectedFileKey.value as string];
          });

          // 处理分组的展开/折叠
          const currentExpandedKeys = [...expandedTreeKeys.value];
          const index = currentExpandedKeys.indexOf(key);
          if (index > -1) {
            currentExpandedKeys.splice(index, 1);
          } else {
            currentExpandedKeys.push(key);
          }
          expandedTreeKeys.value = currentExpandedKeys;

          return;
        }
      } else {
        // 如果选中的是文件节点，更新lastSelectedFileKey
        lastSelectedFileKey.value = key;
      }

      const originalItem = getItemFromNodeKey(key, projectStore.activeProject);
      selectProjectItem(originalItem || null);
    } else if (meta.action === "unselect" && meta.node) {
      // 如果是取消选择文件节点，则阻止取消选择
      const key = String(meta.node.key);
      const isNodeGroup = key.startsWith("group-");

      if (!isNodeGroup && selectedTreeKeys.value.includes(key)) {
        // 如果是文件节点且正在尝试取消选择它，恢复选中状态
        nextTick(() => {
          selectedTreeKeys.value = [key];
        });
        return;
      }
      selectProjectItem(null); // 其他情况正常取消选择
    } else if (keys.length === 0) {
      selectProjectItem(null); // 完全清除选择
    }
  };

  /**
   * 获取节点属性（事件处理器）
   */
  const getBaseNodeProps = (
    { option }: { option: NaiveTreeOption }
  ): import("vue").HTMLAttributes & Record<string, unknown> => {
    const key = String(option.key);
    const isNodeGroup = key.startsWith("group-");

    return {
      onClick: () => {
        if (isNodeGroup) {
          const currentExpandedKeys = [...expandedTreeKeys.value];
          const index = currentExpandedKeys.indexOf(key);

          if (index > -1) {
            currentExpandedKeys.splice(index, 1);
          } else {
            currentExpandedKeys.push(key);
          }
          expandedTreeKeys.value = currentExpandedKeys;
        }
      },
    };
  };

  /**
   * 处理展开键更新
   */
  const handleExpandedKeysUpdate = (keys: string[]) => {
    expandedTreeKeys.value = keys;
  };

  // 监听项目存储的选中文件变化
  watch(
    () => projectStore.selectedFileUuid,
    (newUuid) => {
      if (newUuid) {
        selectedTreeKeys.value = [`file-${newUuid}`];

        // 获取对应的文件对象并发射选择事件，确保编辑界面同步
        const fileItem = projectStore.activeProject?.files?.find(f => f.fileUuid === newUuid);
        if (fileItem) {
          // 发射选择事件给父组件，确保编辑界面更新
          selectProjectItem(fileItem, true);
        }

        // 检查是否是新文件创建导致的选择
        if (projectStore.lastCreatedFileUuid === newUuid) {
          nextTick(() => {
            // 确保树已更新为新节点
            const nodeToRenameKey = `file-${newUuid}`;
            const nodeToRename = findNodeByKey(treeData.value, nodeToRenameKey);
            if (nodeToRename) {
              triggerFileRename(nodeToRename);
            }
            // 重置标志
            if (projectStore.lastCreatedFileUuid) {
              projectStore.lastCreatedFileUuid = null;
            }
          });
        }
      } else {
        selectedTreeKeys.value = [];
        // 清空选择时也要发射事件
        selectProjectItem(null);
      }
    },
    { immediate: true } // 立即执行以设置初始选择
  );

  return {
    // 状态
    selectedTreeKeys,
    expandedTreeKeys,
    lastSelectedFileKey,

    // 方法
    ensureParentGroupsExpanded,
    selectProjectItem,
    handleTreeSelect,
    getBaseNodeProps,
    handleExpandedKeysUpdate,
  };
}
