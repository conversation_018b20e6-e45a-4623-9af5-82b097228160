#!/usr/bin/env node

/**
 * 验证更新器配置
 */

import fs from 'fs';

console.log('🔍 验证更新器配置...\n');

// 检查密钥文件
const keyFiles = [
  'keys/realitytap-updater.key',
  'keys/realitytap-updater.key.pub',
  '../realitytap-ota-server/keys/realitytap-updater.key',
  '../realitytap-ota-server/keys/realitytap-updater.key.pub'
];

console.log('📁 检查密钥文件:');
keyFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} (不存在)`);
  }
});

// 检查配置文件中的公钥
console.log('\n🔑 检查配置文件中的公钥:');

const configs = [
  { file: 'src-tauri/tauri.conf.json', env: '生产环境' },
  { file: 'src-tauri/tauri.dev.conf.json', env: '开发环境' }
];

let pubkeyFromFile = null;
if (fs.existsSync('keys/realitytap-updater.key.pub')) {
  pubkeyFromFile = fs.readFileSync('keys/realitytap-updater.key.pub', 'utf8').trim();
}

configs.forEach(({ file, env }) => {
  if (fs.existsSync(file)) {
    try {
      const config = JSON.parse(fs.readFileSync(file, 'utf8'));
      const pubkey = config.plugins?.updater?.pubkey;
      
      if (pubkey) {
        const decoded = Buffer.from(pubkey, 'base64').toString('utf8');
        console.log(`✅ ${env} (${file})`);
        console.log(`   公钥 ID: ${decoded.match(/minisign public key: ([A-F0-9]+)/)?.[1] || '未知'}`);
        
        if (pubkeyFromFile && decoded === pubkeyFromFile) {
          console.log(`   ✅ 与本地密钥文件匹配`);
        } else if (pubkeyFromFile) {
          console.log(`   ⚠️  与本地密钥文件不匹配`);
        }
      } else {
        console.log(`❌ ${env} (${file}) - 缺少公钥配置`);
      }
    } catch (error) {
      console.log(`❌ ${env} (${file}) - 解析失败: ${error.message}`);
    }
  } else {
    console.log(`❌ ${env} (${file}) - 文件不存在`);
  }
});

// 检查 OTA 服务器环境配置
console.log('\n🔧 检查 OTA 服务器配置:');
const otaEnvFile = '../realitytap-ota-server/.env';
if (fs.existsSync(otaEnvFile)) {
  const envContent = fs.readFileSync(otaEnvFile, 'utf8');
  
  if (envContent.includes('TAURI_PRIVATE_KEY_PATH')) {
    console.log('✅ TAURI_PRIVATE_KEY_PATH 已配置');
  } else {
    console.log('❌ TAURI_PRIVATE_KEY_PATH 未配置');
  }
  
  if (envContent.includes('TAURI_KEY_PASSWORD')) {
    console.log('✅ TAURI_KEY_PASSWORD 已配置');
  } else {
    console.log('❌ TAURI_KEY_PASSWORD 未配置');
  }
} else {
  console.log(`❌ ${otaEnvFile} 不存在`);
}

console.log('\n✨ 配置验证完成！');
console.log('\n📋 后续步骤:');
console.log('1. 重启 OTA 服务器: cd ../realitytap-ota-server && npm run dev');
console.log('2. 清理并重新构建: rm -rf src-tauri/target && npm run build');
console.log('3. 测试更新功能: npm run dev');
