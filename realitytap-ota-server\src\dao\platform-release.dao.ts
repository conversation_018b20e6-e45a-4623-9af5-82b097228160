import { BaseDAO } from './base.dao';
import { PlatformRelease } from '@/types/server.types';

export interface PlatformReleaseEntity {
  id: number;
  version_id: number;
  platform: string;
  architecture: string;
  filename: string;
  file_size: number;
  checksum: string;
  signature?: string;
  release_date: string;
  release_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CreatePlatformReleaseData {
  version_id: number;
  platform: string;
  architecture: string;
  filename: string;
  file_size: number;
  checksum: string;
  signature?: string;
  release_date: string;
  release_notes?: string;
}

export interface UpdatePlatformReleaseData {
  version_id?: number;
  platform?: string;
  architecture?: string;
  filename?: string;
  file_size?: number;
  checksum?: string;
  signature?: string;
  release_date?: string;
  release_notes?: string;
}

export class PlatformReleaseDAO extends BaseDAO {
  constructor() {
    super('platform_releases');
  }

  /**
   * 创建平台发布
   */
  async createPlatformRelease(data: CreatePlatformReleaseData): Promise<number> {
    this.logOperation('createPlatformRelease', data);
    return await this.insert(data);
  }

  /**
   * 根据版本ID获取所有平台发布
   */
  async getPlatformReleasesByVersionId(versionId: number): Promise<PlatformReleaseEntity[]> {
    this.logOperation('getPlatformReleasesByVersionId', { versionId });
    return await this.findWhere<PlatformReleaseEntity>({ version_id: versionId }, 'platform ASC, architecture ASC');
  }

  /**
   * 根据版本ID、平台和架构获取发布
   */
  async getPlatformRelease(versionId: number, platform: string, architecture: string): Promise<PlatformReleaseEntity | undefined> {
    this.logOperation('getPlatformRelease', { versionId, platform, architecture });
    return await this.findOneWhere<PlatformReleaseEntity>({
      version_id: versionId,
      platform,
      architecture
    });
  }

  /**
   * 根据渠道、平台和架构获取最新发布
   */
  async getLatestPlatformRelease(channelName: string, platform: string, architecture: string): Promise<PlatformReleaseEntity | undefined> {
    this.logOperation('getLatestPlatformRelease', { channelName, platform, architecture });
    
    const sql = `
      SELECT pr.* FROM platform_releases pr
      JOIN versions v ON pr.version_id = v.id
      JOIN channels c ON v.channel_id = c.id
      WHERE c.name = ? AND c.enabled = 1 
        AND pr.platform = ? AND pr.architecture = ?
      ORDER BY v.version DESC, pr.release_date DESC
      LIMIT 1
    `;
    
    const results = await this.query<PlatformReleaseEntity>(sql, [channelName, platform, architecture]);
    return results[0];
  }

  /**
   * 更新平台发布
   */
  async updatePlatformRelease(id: number, data: UpdatePlatformReleaseData): Promise<boolean> {
    this.logOperation('updatePlatformRelease', { id, data });
    return await this.update(id, data);
  }

  /**
   * 删除平台发布
   */
  async deletePlatformRelease(id: number): Promise<boolean> {
    this.logOperation('deletePlatformRelease', { id });
    return await this.delete(id);
  }

  /**
   * 根据版本ID删除所有平台发布
   */
  async deletePlatformReleasesByVersionId(versionId: number): Promise<number> {
    this.logOperation('deletePlatformReleasesByVersionId', { versionId });
    return await this.deleteWhere({ version_id: versionId });
  }

  /**
   * 检查平台发布是否存在
   */
  async platformReleaseExists(versionId: number, platform: string, architecture: string): Promise<boolean> {
    return await this.exists({ version_id: versionId, platform, architecture });
  }

  /**
   * 获取所有平台发布（带版本和渠道信息）
   */
  async getAllPlatformReleasesWithDetails(): Promise<Array<PlatformReleaseEntity & {
    version: string;
    channel_name: string;
    channel_enabled: boolean;
    force_update: boolean;
  }>> {
    this.logOperation('getAllPlatformReleasesWithDetails');

    const sql = `
      SELECT pr.*, v.version, v.force_update, c.name as channel_name, c.enabled as channel_enabled
      FROM platform_releases pr
      JOIN versions v ON pr.version_id = v.id
      JOIN channels c ON v.channel_id = c.id
      ORDER BY c.priority ASC, v.version DESC, pr.platform ASC, pr.architecture ASC
    `;

    return await this.query<PlatformReleaseEntity & {
      version: string;
      channel_name: string;
      channel_enabled: boolean;
      force_update: boolean;
    }>(sql);
  }

  /**
   * 获取平台发布统计信息
   */
  async getPlatformReleaseStats(): Promise<{
    totalReleases: number;
    releasesByPlatform: Record<string, number>;
    releasesByArchitecture: Record<string, number>;
    releasesByChannel: Record<string, number>;
  }> {
    this.logOperation('getPlatformReleaseStats');

    const totalReleases = await this.count();
    
    // 按平台统计
    const platformSql = `
      SELECT platform, COUNT(*) as count
      FROM platform_releases
      GROUP BY platform
    `;
    const platformStats = await this.query<{ platform: string; count: number }>(platformSql);
    
    // 按架构统计
    const archSql = `
      SELECT architecture, COUNT(*) as count
      FROM platform_releases
      GROUP BY architecture
    `;
    const archStats = await this.query<{ architecture: string; count: number }>(archSql);
    
    // 按渠道统计
    const channelSql = `
      SELECT c.name as channel_name, COUNT(pr.id) as count
      FROM channels c
      LEFT JOIN versions v ON c.id = v.channel_id
      LEFT JOIN platform_releases pr ON v.id = pr.version_id
      GROUP BY c.id, c.name
    `;
    const channelStats = await this.query<{ channel_name: string; count: number }>(channelSql);

    const releasesByPlatform: Record<string, number> = {};
    for (const stat of platformStats) {
      releasesByPlatform[stat.platform] = stat.count;
    }

    const releasesByArchitecture: Record<string, number> = {};
    for (const stat of archStats) {
      releasesByArchitecture[stat.architecture] = stat.count;
    }

    const releasesByChannel: Record<string, number> = {};
    for (const stat of channelStats) {
      releasesByChannel[stat.channel_name] = stat.count;
    }

    return {
      totalReleases,
      releasesByPlatform,
      releasesByArchitecture,
      releasesByChannel
    };
  }

  /**
   * 批量创建平台发布
   */
  async batchCreatePlatformReleases(releases: CreatePlatformReleaseData[]): Promise<void> {
    this.logOperation('batchCreatePlatformReleases', { count: releases.length });
    
    await this.transaction(async () => {
      for (const release of releases) {
        await this.createPlatformRelease(release);
      }
    });
  }

  /**
   * 根据文件名查找平台发布
   */
  async getPlatformReleaseByFilename(filename: string): Promise<PlatformReleaseEntity | undefined> {
    this.logOperation('getPlatformReleaseByFilename', { filename });
    return await this.findOneWhere<PlatformReleaseEntity>({ filename });
  }

  /**
   * 获取指定平台和架构的所有发布历史
   */
  async getPlatformReleaseHistory(platform: string, architecture: string, limit: number = 10): Promise<Array<PlatformReleaseEntity & { 
    version: string; 
    channel_name: string;
  }>> {
    this.logOperation('getPlatformReleaseHistory', { platform, architecture, limit });
    
    const sql = `
      SELECT pr.*, v.version, c.name as channel_name
      FROM platform_releases pr
      JOIN versions v ON pr.version_id = v.id
      JOIN channels c ON v.channel_id = c.id
      WHERE pr.platform = ? AND pr.architecture = ?
      ORDER BY pr.release_date DESC
      LIMIT ?
    `;
    
    return await this.query<PlatformReleaseEntity & { 
      version: string; 
      channel_name: string;
    }>(sql, [platform, architecture, limit]);
  }

  /**
   * 清理旧的平台发布（保留指定数量的最新发布）
   */
  async cleanupOldPlatformReleases(platform: string, architecture: string, keepCount: number = 5): Promise<number> {
    this.logOperation('cleanupOldPlatformReleases', { platform, architecture, keepCount });
    
    const sql = `
      DELETE FROM platform_releases 
      WHERE platform = ? AND architecture = ? AND id NOT IN (
        SELECT id FROM (
          SELECT id FROM platform_releases 
          WHERE platform = ? AND architecture = ?
          ORDER BY release_date DESC 
          LIMIT ?
        ) as keep_releases
      )
    `;
    
    const result = await this.execute(sql, [platform, architecture, platform, architecture, keepCount]);
    return result;
  }

  /**
   * 根据校验和查找平台发布
   */
  async getPlatformReleaseByChecksum(checksum: string): Promise<PlatformReleaseEntity | undefined> {
    this.logOperation('getPlatformReleaseByChecksum', { checksum });
    return await this.findOneWhere<PlatformReleaseEntity>({ checksum });
  }
}
