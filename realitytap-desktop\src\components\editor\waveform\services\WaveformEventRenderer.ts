/**
 * 波形事件渲染服务
 * 负责所有事件的绘制逻辑，包括缓存管理和性能优化
 */

import type {
  RenderableEvent,
  RenderableTransientEvent,
  RenderableContinuousEvent,
  RenderableContinuousCurvePoint,
} from "@/types/haptic-editor";
import { WaveformCoordinateService } from "./WaveformCoordinateService";
import { EVENT_WAVEFORM_VERTICAL_OFFSET } from "../config/waveform-constants";

export interface RenderConfig {
  defaultLineWidth: number;
  selectedLineWidth: number;
  defaultPointRadius: number;
  selectedPointRadius: number;
  colors: {
    transientStroke: string;
    transientFill: string;
    transientSelectedStroke: string;
    transientSelectedFill: string;
    continuousStroke: string;
    continuousSelectedStroke: string;
  };
}

export interface EventCacheEntry {
  points: Array<{ x: number; y: number; color: string }>;
  lastStartTime: number;
  lastStopTime: number;
  lastDuration: number;
  lastCurveHash: string;
  lastSelected: boolean;
  lastUsed: number;
  lastVirtualOffset: number;
}

export interface RenderStats {
  lastDrawTime: number;
  averageDrawTime: number;
  drawCount: number;
  visibleEventCount: number;
  cacheHitRate: number;
}

export class WaveformEventRenderer {
  private coordinateService: WaveformCoordinateService;
  private config: RenderConfig;
  private eventCache = new Map<string, EventCacheEntry>();
  private maxCacheSize = 50;
  private stats: RenderStats = {
    lastDrawTime: 0,
    averageDrawTime: 0,
    drawCount: 0,
    visibleEventCount: 0,
    cacheHitRate: 0,
  };

  constructor(coordinateService: WaveformCoordinateService, config: RenderConfig) {
    this.coordinateService = coordinateService;
    this.config = config;
  }

  /**
   * 渲染所有可见事件
   */
  renderEvents(
    ctx: CanvasRenderingContext2D,
    events: RenderableEvent[],
    selectedEventId: string | null,
    previewEvent?: RenderableEvent
  ): void {
    const startTime = performance.now();
    const visibleEvents = this.getVisibleEvents(events);

    for (const event of visibleEvents) {
      const eventToDraw = previewEvent && event.id === previewEvent.id ? previewEvent : event;
      const isSelected = selectedEventId === event.id;
      const isPreview = previewEvent && event.id === previewEvent.id;

      if (eventToDraw.type === "transient") {
        this.renderTransientEvent(ctx, eventToDraw as RenderableTransientEvent, isSelected);
      } else if (eventToDraw.type === "continuous") {
        this.renderContinuousEvent(ctx, eventToDraw as RenderableContinuousEvent, isSelected, isPreview);
      }
    }

    // 更新性能统计
    const endTime = performance.now();
    this.updateStats(endTime - startTime, visibleEvents.length);
  }

  /**
   * 渲染瞬态事件
   */
  private renderTransientEvent(
    ctx: CanvasRenderingContext2D,
    event: RenderableTransientEvent,
    isSelected: boolean
  ): void {
    // 将时间转换为画布坐标
    const peakX = this.coordinateService.mapTimeToX(event.peakTime);
    const peakY = this.coordinateService.mapIntensityToY(event.intensity);
    const startX = this.coordinateService.mapTimeToX(event.startTime);
    const endX = this.coordinateService.mapTimeToX(event.stopTime);
    // 添加垂直偏移，确保X轴可见
    const baseY = this.coordinateService.mapIntensityToY(0) - EVENT_WAVEFORM_VERTICAL_OFFSET;

    // 检查事件是否在可见范围内
    const graphArea = this.coordinateService.getGraphArea();
    const tolerance = 50;
    if (endX < -tolerance || startX > graphArea.width + tolerance) return;

    // 限制坐标在可见范围内
    const clampedStartX = Math.max(-tolerance, Math.min(startX, graphArea.width + tolerance));
    const clampedEndX = Math.max(-tolerance, Math.min(endX, graphArea.width + tolerance));
    const clampedPeakX = Math.max(-tolerance, Math.min(peakX, graphArea.width + tolerance));

    if (clampedEndX < -tolerance || clampedStartX > graphArea.width + tolerance) return;

    // 选择颜色和样式
    const strokeColor = isSelected ? this.config.colors.transientSelectedStroke : this.config.colors.transientStroke;
    const fillColor = isSelected ? this.config.colors.transientSelectedFill : this.config.colors.transientFill;
    const lineWidth = isSelected ? this.config.selectedLineWidth : this.config.defaultLineWidth;
    const pointRadius = isSelected ? this.config.selectedPointRadius : this.config.defaultPointRadius;

    // 绘制填充
    ctx.beginPath();
    ctx.moveTo(clampedStartX, baseY);
    ctx.lineTo(clampedPeakX, peakY);
    ctx.lineTo(clampedEndX, baseY);
    ctx.fillStyle = fillColor;
    ctx.fill();

    // 绘制边框
    ctx.beginPath();
    ctx.moveTo(clampedStartX, baseY);
    ctx.lineTo(clampedPeakX, peakY);
    ctx.lineTo(clampedEndX, baseY);
    ctx.strokeStyle = strokeColor;
    ctx.lineWidth = lineWidth;
    ctx.stroke();

    // 在峰值处添加高亮点
    ctx.beginPath();
    ctx.arc(clampedPeakX, peakY, pointRadius, 0, Math.PI * 2);
    ctx.fillStyle = strokeColor;
    ctx.fill();
  }

  /**
   * 渲染连续事件
   */
  private renderContinuousEvent(
    ctx: CanvasRenderingContext2D,
    event: RenderableContinuousEvent,
    isSelected: boolean,
    isPreview: boolean = false
  ): void {
    if (event.curves.length < 2) return;

    // 添加垂直偏移，确保X轴可见
    const baselineY = this.coordinateService.mapIntensityToY(0) - EVENT_WAVEFORM_VERTICAL_OFFSET;
    const currentPointRadius = isSelected ? this.config.selectedPointRadius : this.config.defaultPointRadius;
    const currentLineWidth = isSelected ? this.config.selectedLineWidth : this.config.defaultLineWidth;
    const baseStrokeColor = isSelected ? this.config.colors.continuousSelectedStroke : this.config.colors.continuousStroke;

    const curveCount = event.curves.length;
    let points: Array<{ x: number; y: number; color: string }>;

    // 检查缓存（预览事件跳过缓存）
    if (isPreview) {
      // 预览事件总是重新计算，不使用缓存
      points = this.calculateContinuousEventPoints(event, isSelected);
    } else {
      const cacheKey = event.id;
      const cached = this.eventCache.get(cacheKey);
      const now = performance.now();
      const curveHash = this.getCurveHash(event.curves);
      const virtualOffset = this.coordinateService.getVirtualOffset();

      const offsetTolerance = 1.0;
      const offsetMatches = cached ? Math.abs(cached.lastVirtualOffset - virtualOffset) <= offsetTolerance : false;

      if (cached &&
          cached.lastStartTime === event.startTime &&
          cached.lastStopTime === event.stopTime &&
          cached.lastDuration === event.duration &&
          cached.lastCurveHash === curveHash &&
          cached.lastSelected === isSelected &&
          offsetMatches) {
        // 使用缓存
        points = cached.points;
        cached.lastUsed = now;
        cached.lastVirtualOffset = virtualOffset;
      } else {
        // 重新计算坐标
        points = this.calculateContinuousEventPoints(event, isSelected);

        // 更新缓存
        this.eventCache.set(cacheKey, {
          points: points,
          lastStartTime: event.startTime,
          lastStopTime: event.stopTime,
          lastDuration: event.duration,
          lastCurveHash: curveHash,
          lastSelected: isSelected,
          lastUsed: now,
          lastVirtualOffset: virtualOffset,
        });

        // 清理缓存
        if (this.eventCache.size > this.maxCacheSize) {
          this.cleanupCache();
        }
      }
    }

    // 绘制填充区域
    if (curveCount >= 2) {
      this.renderContinuousEventFill(ctx, points, baselineY);
    }

    // 绘制曲线描边
    this.renderContinuousEventStroke(ctx, points, baseStrokeColor, currentLineWidth);

    // 绘制关键点
    this.renderContinuousEventPoints(ctx, points, baseStrokeColor, currentPointRadius);
  }

  /**
   * 获取可见事件列表
   */
  private getVisibleEvents(events: RenderableEvent[]): RenderableEvent[] {
    if (!events || events.length === 0) return [];

    const effectiveDuration = this.coordinateService.getEffectiveDuration();

    // 简化逻辑：只有事件数量超过50个时才启用视口裁剪
    const hasManyEvents = events.length > 50;
    if (!hasManyEvents) {
      return events.slice(0, 100); // 最多显示100个事件
    }

    // 基于虚拟偏移量计算可见时间范围
    const graphArea = this.coordinateService.getGraphArea();
    const virtualOffset = this.coordinateService.getVirtualOffset();

    // 计算当前可见的时间范围
    const visibleStartTime = (virtualOffset / graphArea.logicalWidth) * effectiveDuration;
    const visibleEndTime = ((virtualOffset + graphArea.width) / graphArea.logicalWidth) * effectiveDuration;

    // 添加缓冲区域，防止滚动时事件突然消失
    const bufferRatio = 0.3;
    const bufferTime = (visibleEndTime - visibleStartTime) * bufferRatio;
    const bufferedStartTime = Math.max(0, visibleStartTime - bufferTime);
    const bufferedEndTime = Math.min(effectiveDuration, visibleEndTime + bufferTime);

    // 过滤可见事件
    const visibleEvents = events.filter((event) => {
      return event.stopTime >= bufferedStartTime && event.startTime <= bufferedEndTime;
    });

    return visibleEvents.slice(0, 100);
  }

  /**
   * 计算连续事件的坐标点
   */
  private calculateContinuousEventPoints(
    event: RenderableContinuousEvent,
    isSelected: boolean
  ): Array<{ x: number; y: number; color: string }> {
    const points = new Array(event.curves.length);

    for (let i = 0; i < event.curves.length; i++) {
      const p = event.curves[i];
      const x = this.coordinateService.mapTimeToX(event.startTime + p.timeOffset);
      const y = this.coordinateService.mapIntensityToY(p.drawIntensity);
      const color = this.mapFrequencyToColor(p.curveFrequency, isSelected);

      points[i] = { x, y, color };
    }

    return points;
  }

  /**
   * 渲染连续事件的填充区域
   */
  private renderContinuousEventFill(
    ctx: CanvasRenderingContext2D,
    points: Array<{ x: number; y: number; color: string }>,
    baselineY: number
  ): void {
    const curveCount = points.length;

    // 创建渐变
    const gradient = ctx.createLinearGradient(points[0].x, 0, points[curveCount - 1].x, 0);

    // 添加渐变色标
    for (let i = 0; i < curveCount; i++) {
      const stopPosition = i / (curveCount - 1);
      gradient.addColorStop(stopPosition, points[i].color);
    }

    // 绘制填充路径
    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);

    // 绘制上部曲线
    for (let i = 1; i < curveCount; i++) {
      ctx.lineTo(points[i].x, points[i].y);
    }

    // 连接到基线形成闭合区域
    ctx.lineTo(points[curveCount - 1].x, baselineY);
    ctx.lineTo(points[0].x, baselineY);
    ctx.closePath();

    ctx.fillStyle = gradient;
    ctx.fill();
  }

  /**
   * 渲染连续事件的描边
   */
  private renderContinuousEventStroke(
    ctx: CanvasRenderingContext2D,
    points: Array<{ x: number; y: number; color: string }>,
    strokeColor: string,
    lineWidth: number
  ): void {
    ctx.strokeStyle = strokeColor;
    ctx.lineWidth = lineWidth;

    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].x, points[i].y);
    }
    ctx.stroke();
  }

  /**
   * 渲染连续事件的关键点
   */
  private renderContinuousEventPoints(
    ctx: CanvasRenderingContext2D,
    points: Array<{ x: number; y: number; color: string }>,
    fillColor: string,
    pointRadius: number
  ): void {
    ctx.fillStyle = fillColor;

    for (let i = 0; i < points.length; i++) {
      ctx.beginPath();
      ctx.arc(points[i].x, points[i].y, pointRadius, 0, Math.PI * 2);
      ctx.fill();
    }
  }

  /**
   * 频率映射到颜色
   */
  private mapFrequencyToColor(frequency: number, _isSelected: boolean): string {
    const MIN_FREQUENCY = -100;
    const MAX_FREQUENCY = 200;

    let r, g, b;
    const baseAlpha = 0.6;
    const lowRgb = [0, 100, 255];
    const midRgb = [100, 255, 100];
    const highRgb = [255, 100, 0];

    if (frequency <= MIN_FREQUENCY) {
      [r, g, b] = lowRgb;
    } else if (frequency >= MAX_FREQUENCY) {
      [r, g, b] = highRgb;
    } else if (frequency < 0) {
      const t = (frequency - MIN_FREQUENCY) / (0 - MIN_FREQUENCY);
      r = lowRgb[0] + t * (midRgb[0] - lowRgb[0]);
      g = lowRgb[1] + t * (midRgb[1] - lowRgb[1]);
      b = lowRgb[2] + t * (midRgb[2] - lowRgb[2]);
    } else {
      const t = (frequency - MIN_FREQUENCY) / (MAX_FREQUENCY - MIN_FREQUENCY);
      r = midRgb[0] + t * (highRgb[0] - midRgb[0]);
      g = midRgb[1] + t * (highRgb[1] - midRgb[1]);
      b = midRgb[2] + t * (highRgb[2] - midRgb[2]);
    }

    return `rgba(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)}, ${baseAlpha})`;
  }

  /**
   * 计算曲线点的哈希值，用于检测变化
   */
  private getCurveHash(curves: RenderableContinuousCurvePoint[]): string {
    return curves.map(p => `${p.timeOffset},${p.drawIntensity},${p.curveFrequency}`).join('|');
  }

  /**
   * LRU缓存清理
   */
  private cleanupCache(): void {
    if (this.eventCache.size <= this.maxCacheSize) return;

    const entries = Array.from(this.eventCache.entries())
      .sort((a, b) => a[1].lastUsed - b[1].lastUsed);

    const toDelete = entries.slice(0, entries.length - this.maxCacheSize);
    toDelete.forEach(([key]) => this.eventCache.delete(key));
  }

  /**
   * 更新性能统计
   */
  private updateStats(drawTime: number, visibleEvents: number): void {
    this.stats.lastDrawTime = drawTime;
    this.stats.drawCount++;
    this.stats.visibleEventCount = visibleEvents;

    const alpha = 0.1;
    this.stats.averageDrawTime = this.stats.averageDrawTime * (1 - alpha) + drawTime * alpha;
  }

  /**
   * 清除特定事件的缓存
   */
  invalidateEventCache(eventId: string): void {
    this.eventCache.delete(eventId);
  }

  /**
   * 清除所有缓存
   */
  clearCache(): void {
    this.eventCache.clear();
  }

  /**
   * 获取性能统计
   */
  getStats(): RenderStats {
    return { ...this.stats };
  }
}