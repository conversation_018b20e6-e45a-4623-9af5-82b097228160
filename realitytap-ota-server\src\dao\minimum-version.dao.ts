import { BaseDAO } from './base.dao';

export interface CreateMinimumVersionData {
  platform: string;
  architecture: string;
  minimum_version: string;
}

export interface UpdateMinimumVersionData {
  minimum_version?: string;
}

export interface MinimumVersionEntity {
  id: number;
  platform: string;
  architecture: string;
  minimum_version: string;
  created_at: string;
  updated_at: string;
}

export class MinimumVersionDAO extends BaseDAO {
  constructor() {
    super('minimum_versions');
  }

  /**
   * 创建最小版本要求
   */
  async createMinimumVersion(data: CreateMinimumVersionData): Promise<number> {
    this.logOperation('createMinimumVersion', data);
    return await this.insert(data);
  }

  /**
   * 根据平台和架构获取最小版本
   */
  async getMinimumVersion(platform: string, architecture: string): Promise<MinimumVersionEntity | undefined> {
    this.logOperation('getMinimumVersion', { platform, architecture });
    return await this.findOneWhere<MinimumVersionEntity>({ platform, architecture });
  }

  /**
   * 获取所有最小版本要求
   */
  async getAllMinimumVersions(): Promise<MinimumVersionEntity[]> {
    this.logOperation('getAllMinimumVersions');
    return await this.findAll<MinimumVersionEntity>('platform ASC, architecture ASC');
  }

  /**
   * 根据平台获取最小版本要求
   */
  async getMinimumVersionsByPlatform(platform: string): Promise<MinimumVersionEntity[]> {
    this.logOperation('getMinimumVersionsByPlatform', { platform });
    return await this.findWhere<MinimumVersionEntity>({ platform }, 'architecture ASC');
  }

  /**
   * 更新最小版本要求
   */
  async updateMinimumVersion(platform: string, architecture: string, data: UpdateMinimumVersionData): Promise<boolean> {
    this.logOperation('updateMinimumVersion', { platform, architecture, data });
    
    const existing = await this.getMinimumVersion(platform, architecture);
    if (!existing) {
      return false;
    }
    
    return await this.update(existing.id, data);
  }

  /**
   * 设置最小版本要求（创建或更新）
   */
  async setMinimumVersion(platform: string, architecture: string, minimumVersion: string): Promise<void> {
    this.logOperation('setMinimumVersion', { platform, architecture, minimumVersion });
    
    const existing = await this.getMinimumVersion(platform, architecture);
    
    if (existing) {
      await this.updateMinimumVersion(platform, architecture, { minimum_version: minimumVersion });
    } else {
      await this.createMinimumVersion({
        platform,
        architecture,
        minimum_version: minimumVersion
      });
    }
  }

  /**
   * 删除最小版本要求
   */
  async deleteMinimumVersion(platform: string, architecture: string): Promise<boolean> {
    this.logOperation('deleteMinimumVersion', { platform, architecture });
    
    const existing = await this.getMinimumVersion(platform, architecture);
    if (!existing) {
      return false;
    }
    
    return await this.delete(existing.id);
  }

  /**
   * 批量设置最小版本要求
   */
  async batchSetMinimumVersions(minimumVersions: Record<string, string>): Promise<void> {
    this.logOperation('batchSetMinimumVersions', { count: Object.keys(minimumVersions).length });
    
    await this.transaction(async () => {
      for (const [key, version] of Object.entries(minimumVersions)) {
        if (typeof version === 'string') {
          // 解析平台和架构，这里假设 key 格式为 "platform" 或 "platform:architecture"
          const [platform, architecture = 'x86_64'] = key.split(':');
          if (platform) {
            await this.setMinimumVersion(platform, architecture, version);
          }
        }
      }
    });
  }

  /**
   * 检查版本是否满足最小版本要求
   */
  async checkVersionRequirement(platform: string, architecture: string, version: string): Promise<boolean> {
    this.logOperation('checkVersionRequirement', { platform, architecture, version });
    
    const minimumVersion = await this.getMinimumVersion(platform, architecture);
    if (!minimumVersion) {
      return true; // 没有最小版本要求，允许所有版本
    }
    
    // 使用 semver 进行版本比较
    const semver = require('semver');
    
    try {
      return semver.gte(version, minimumVersion.minimum_version);
    } catch (error) {
      // 如果不是标准的 semver 格式，使用字符串比较
      return version >= minimumVersion.minimum_version;
    }
  }

  /**
   * 清理所有最小版本要求
   */
  async clearAllMinimumVersions(): Promise<number> {
    this.logOperation('clearAllMinimumVersions');
    return await this.deleteWhere({});
  }
}
