<!--
  日志查看器组件
  显示和管理控制台日志
-->
<template>
  <div class="log-viewer">
    <n-card title="控制台日志">
      <n-space vertical>
        <n-space>
          <n-button @click="clearLogs">清空日志</n-button>
          <n-button @click="addTestLog">添加测试日志</n-button>
          <n-switch v-model:value="autoScroll">
            <template #checked>自动滚动</template>
            <template #unchecked>手动滚动</template>
          </n-switch>
        </n-space>
        
        <div class="log-container" ref="logContainer">
          <div v-for="(log, index) in logs" :key="index" :class="'log-entry log-' + log.level">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <pre class="log-message">{{ log.message }}</pre>
          </div>
        </div>
      </n-space>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { NCard, NSpace, NButton, NSwitch } from "naive-ui";

interface LogEntry {
  time: string;
  level: string;
  message: string;
}

const logs = ref<LogEntry[]>([]);
const autoScroll = ref(true);
const logContainer = ref<HTMLElement | null>(null);

const formatLogMessage = (args: any[]): string => {
  return args.map(arg => {
    if (arg === null) return 'null';
    if (arg === undefined) return 'undefined';
    if (typeof arg === 'string') return arg;
    if (typeof arg === 'number' || typeof arg === 'boolean') return String(arg);
    if (arg instanceof Error) {
      return `${arg.name}: ${arg.message}\n${arg.stack || ''}`;
    }
    if (typeof arg === 'object') {
      try {
        return JSON.stringify(arg, null, 2);
      } catch (e) {
        // 处理循环引用等情况
        return `[Object: ${Object.prototype.toString.call(arg)}]`;
      }
    }
    return String(arg);
  }).join(' ');
};

const addLog = (level: string, ...args: any[]) => {
  const message = formatLogMessage(args);

  logs.value.push({
    time: new Date().toLocaleTimeString(),
    level,
    message,
  });

  if (autoScroll.value) {
    nextTick(() => {
      if (logContainer.value) {
        logContainer.value.scrollTop = logContainer.value.scrollHeight;
      }
    });
  }
};

const clearLogs = () => {
  logs.value = [];
};

const addTestLog = () => {
  const testData = [
    // 简单字符串
    ["这是一条测试信息"],
    // 对象
    ["用户数据:", { id: 123, name: "张三", email: "<EMAIL>", roles: ["admin", "user"] }],
    // 错误对象
    ["更新检查失败:", new Error("网络连接超时")],
    // 复杂嵌套对象
    ["API响应:", {
      status: 200,
      data: { users: [{ id: 1, name: "用户1" }, { id: 2, name: "用户2" }] },
      meta: { total: 2, page: 1 }
    }],
    // 数组
    ["配置列表:", ["config1", "config2", "config3"]],
    // null 和 undefined
    ["空值测试:", null, undefined],
    // 混合类型
    ["混合数据:", "字符串", 42, true, { key: "value" }]
  ];

  const levels = ["info", "warn", "error", "debug"];
  const randomData = testData[Math.floor(Math.random() * testData.length)];
  const randomLevel = levels[Math.floor(Math.random() * levels.length)];

  addLog(randomLevel, ...randomData);
};

// 拦截控制台输出
onMounted(() => {
  const originalLog = console.log;
  const originalWarn = console.warn;
  const originalError = console.error;
  const originalDebug = console.debug;

  console.log = (...args: any[]) => {
    addLog("info", ...args);
    originalLog.apply(console, args);
  };

  console.warn = (...args: any[]) => {
    addLog("warn", ...args);
    originalWarn.apply(console, args);
  };

  console.error = (...args: any[]) => {
    addLog("error", ...args);
    originalError.apply(console, args);
  };

  console.debug = (...args: any[]) => {
    addLog("debug", ...args);
    originalDebug.apply(console, args);
  };

  // 添加初始日志
  addLog("info", "日志查看器已启动");
});
</script>

<style scoped>
/* 日志查看器样式 */
.log-container {
  height: 400px;
  overflow-y: auto;
  background: var(--input-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px;
  font-family: monospace;
  font-size: 12px;
}

.log-entry {
  display: flex;
  gap: 8px;
  padding: 2px 0;
  border-bottom: 1px solid var(--divider-color);
}

.log-time {
  color: var(--text-color-3);
  min-width: 80px;
}

.log-level {
  min-width: 50px;
  font-weight: bold;
}

.log-info .log-level {
  color: #18a058;
}

.log-warn .log-level {
  color: #f0a020;
}

.log-error .log-level {
  color: #d03050;
}

.log-debug .log-level {
  color: #70c0e8;
}

.log-message {
  flex: 1;
  word-break: break-word;
  white-space: pre-wrap;
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  background: transparent;
  border: none;
  padding: 0;
}
</style>
