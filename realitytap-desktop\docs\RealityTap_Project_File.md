## RealityTap数据文件
- 数据文件为一个使用 **ZIP** 格式压缩的压缩包，扩展名：`.arpf`
- 压缩包内应包含一个 **checksum 文件**（例如 `checksum.md5` 或类似机制）用于验证文件完整性。
- 压缩包内包含一个或多个振动描述文件 (`.he`) 及其可选的关联音频文件。
- **`.he` 文件和其对应的关联音频文件通常存放在压缩包内的独立子目录中，支持多层嵌套结构以进行组织。**
- 项目的元数据信息包含在压缩包的根目录下的 `project.json` 文件中。
- `project.json` 能够直接展示此项目的具体信息：
  - 项目名称 (`projectName`)
  - 项目唯一标识符 (`projectUuid`)
  - 项目创建时间 (`createTime`)
  - 项目最后修改时间 (`lastModifiedTime`)
  - 项目作者 (`author`)
  - 项目版本 (`version`) - 指 `.arpf` 文件本身的格式或结构版本，与内部 `.he` 文件版本无关。
  - 项目描述 (`description`)
  - 项目标签 (`tags`)
- 振动描述文件为 JSON 格式，后缀名为 `.he`，支持 V1 和 V2 两种格式标准（在 `project.json` 中标明）。
- 多个振动描述文件可以通过子目录结构或在 `project.json` 中定义的 `groups` 进行分组管理。

### 1. 项目文件结构

#### 1.1 project.json 结构
```json
{
  "projectUuid": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "projectName": "示例项目",
  "createTime": "2023-08-15T14:30:00",
  "lastModifiedTime": "2023-08-18T09:45:22",
  "author": "RealityTap开发者",
  "version": "1.1.0",
  "description": "这是一个示例触觉反馈项目",
  "tags": ["游戏", "震动", "示例"],
  "groups": [
    {
      "groupUuid": "g1a1b1c1-d1e1-f1g1-h1i1-j1k1l1m1n1o1",
      "name": "基础效果",
      "path": "basic",
      "description": "基础触觉反馈效果集合"
    },
    {
      "groupUuid": "g2a2b2c2-d2e2-f2g2-h2i2-j2k2l2m2n2o2",
      "name": "高级效果",
      "path": "advanced",
      "description": "高级触觉反馈效果集合"
    }
  ],
  "files": [
    {
      "fileUuid": "f1a1b1c1-d1e1-f1g1-h1i1-j1k1l1m1n1o1",
      "name": "click.he",
      "path": "basic/click.he",
      "group": "g1a1b1c1-d1e1-f1g1-h1i1-j1k1l1m1n1o1",
      "formatVersion": "V1",
      "createTime": "2023-08-15T15:20:00",
      "lastModifiedTime": "2023-08-15T15:20:00",
      "description": "点击效果",
      "version": "1",
      "associatedAudio": "basic/audio/click.wav"
    },
    {
      "fileUuid": "f2a2b2c2-d2e2-f2g2-h2i2-j2k2l2m2n2o2",
      "name": "explosion.he",
      "path": "advanced/explosion.he",
      "group": "g2a2b2c2-d2e2-f2g2-h2i2-j2k2l2m2n2o2",
      "formatVersion": "V2",
      "createTime": "2023-08-16T10:05:30",
      "lastModifiedTime": "2023-08-17T11:22:15",
      "description": "爆炸效果",
      "version": "2",
      "associatedAudio": "advanced/audio/explosion.mp3"
    }
  ]
}
```

### 2. 文件管理功能

#### 2.1.1 文件列表
- 以列表形式展示所有振动描述文件 (`.he`)。
- 显示文件名称、创建时间、最后修改时间、格式版本 (V1/V2)、所属分组等信息。
- 支持按文件名、创建时间、修改时间、**标签**、**格式版本 (V1/V2)** 等进行排序。
- 支持按文件名、标签、格式版本、所属分组进行筛选和搜索（仅限元数据搜索）。
- 支持文件预览功能（如果可行，例如显示波形缩略图或基本信息）。
- 支持按组分类查看文件。
- 显示文件内容版本信息和关联音频状态。
- **提供不同的视图模式，例如传统的"列表视图"和可能的"缩略图视图"。**

#### 2.1.2 文件操作
- 新建振动描述文件 (`.he`)。
- 编辑现有文件（调用文件编辑器）。
- 删除文件（同时处理关联音频和 `project.json` 条目）。
- 复制/克隆现有文件（生成新的 UUID）。
- 导入/导出文件功能
- 文件格式转换 (V1 转 V2，反之亦然，更新 `project.json` 中的 `formatVersion`)。
- 创建、重命名、删除文件组（更新 `project.json` 和可能的目录结构）。
- 文件重命名和移动到不同组（更新 `project.json` 和可能的目录结构）。
- 批量处理文件操作：
  - **批量修改标签**
  - **批量移动文件到不同组**
  - 批量删除
  - 批量格式转换
- 关联/取消关联音频文件（更新 `project.json` 和压缩包内容）。

#### 2.1.3 文件编辑器
- 可视化的振动模式编辑界面
- 支持设置振动强度、持续时间
- 支持创建振动序列
- 实时预览功能
- 支持多种事件类型编辑
  - 瞬时事件(transient)编辑
  - 包络事件(continuous)编辑
  - 曲线控制点调整
- 音频同步编辑功能
  - 波形可视化
  - 时间轴对齐
  - 音频播放与振动同步
- V2特有功能编辑
  - 多马达控制
  - 绝对时间编排
  - 多Pattern序列编辑

#### 2.1.4 导入/导出与验证
- **项目导入 (`.arpf`):**
  - 支持导入单个 `.arpf` 文件到当前工作区或合并到现有项目中。
  - **合并项目时，提供参数化选项处理文件/分组命名冲突（例如：覆盖、重命名、跳过），由用户选择。**
  - **导入验证:** 导入时检查 `project.json` 结构和基本类型是否正确，校验 checksum 文件，检查 `files` 条目中列出的 `.he` 文件和关联音频文件是否存在于压缩包内。
- **项目导出 (`.arpf`):**
  - 支持将整个项目或选定的文件/分组导出为单个 `.arpf` 压缩包。
  - **导出时可以选择将所有（或选定的）`.he` 文件统一转换为 V1 或 V2 格式。**
  - **支持将当前项目结构（或部分选定结构）导出为项目模板 (`.arpt` 或类似扩展名)，用于快速创建具有预设分组和文件结构的新项目。**
  - 导出时重新生成 checksum 文件。
  - 导出时可选择是否包含关联的音频文件。
- **项目加载验证:**
  - 打开 `.arpf` 项目时，进行完整性检查（类似导入验证）。
- **错误处理:**
  - 对导入/加载过程中发生的验证失败提供清晰的用户提示，指出问题所在（如 `project.json` 格式错误、文件缺失、checksum 不匹配等）。
  - 如果可能，提供简单的修复建议（例如，提示用户手动定位缺失的文件）。
