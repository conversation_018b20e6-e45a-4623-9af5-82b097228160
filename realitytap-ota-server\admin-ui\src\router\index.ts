import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
  history: createWebHistory('/admin/'),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: {
        requiresAuth: false,
        title: '登录',
      },
    },
    {
      path: '/',
      name: 'Layout',
      component: () => import('@/components/Layout/AdminLayout.vue'),
      meta: {
        requiresAuth: true,
      },
      children: [
        {
          path: '',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue'),
          meta: {
            title: '仪表板',
            icon: 'dashboard',
          },
        },
        {
          path: '/versions',
          name: 'VersionManagement',
          component: () => import('@/views/VersionManagement.vue'),
          meta: {
            title: '版本管理',
            icon: 'folder',
          },
        },
        {
          path: '/system',
          name: 'SystemStatus',
          component: () => import('@/views/SystemStatus.vue'),
          meta: {
            title: '系统状态',
            icon: 'settings',
          },
        },
        {
          path: '/logs',
          name: 'LogViewer',
          component: () => import('@/views/LogViewer.vue'),
          meta: {
            title: '系统日志',
            icon: 'document-text',
          },
        },
        {
          path: '/multi-upload-test',
          name: 'MultiUploadTest',
          component: () => import('@/views/MultiUploadTest.vue'),
          meta: {
            title: '多文件上传测试',
            icon: 'cloud-upload',
          },
        },
        {
          path: '/batch-upload-debug',
          name: 'BatchUploadDebug',
          component: () => import('@/views/BatchUploadDebug.vue'),
          meta: {
            title: '批量上传调试',
            icon: 'bug',
          },
        },
        {
          path: '/crypto-test',
          name: 'CryptoTest',
          component: () => import('@/views/CryptoTest.vue'),
          meta: {
            title: '加密功能测试',
            icon: 'shield-checkmark',
          },
        },
      ],
    },
    {
      path: '/test',
      name: 'TestPage',
      component: () => import('@/views/TestPage.vue'),
      meta: {
        requiresAuth: false,
        title: '测试页面',
      },
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      redirect: '/',
    },
  ],
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  try {
    console.log(`🛣️ 路由导航: ${from.path} -> ${to.path}`);

    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - RealityTap OTA 管理`;
    }

    // 检查是否需要身份验证
    if (to.meta.requiresAuth !== false) {
      // 简单检查本地存储，避免过早使用store
      const savedToken = localStorage.getItem('admin_token');
      const savedUser = localStorage.getItem('admin_user');

      if (!savedToken || !savedUser) {
        console.log('🔒 未找到认证信息，跳转到登录页');
        next('/login');
        return;
      }

      console.log('✅ 找到认证信息，允许访问');
    } else {
      // 如果访问登录页且已有认证信息，重定向到首页
      const savedToken = localStorage.getItem('admin_token');
      const savedUser = localStorage.getItem('admin_user');

      if (savedToken && savedUser && to.path === '/login') {
        console.log('🔄 已登录用户访问登录页，重定向到首页');
        next('/');
        return;
      }
    }

    next();
  } catch (error) {
    console.error('❌ 路由守卫错误:', error);
    // 发生错误时，根据目标路径决定处理方式
    if (to.path === '/login') {
      next();
    } else {
      next('/login');
    }
  }
});

export default router;
