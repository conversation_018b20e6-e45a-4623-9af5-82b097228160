// 坐标管理组合式函数
// 负责坐标转换、映射等坐标相关的逻辑

import {
  convertXToTime,
  mapIntensityToY,
  mapTimeToX,
  mapXOffsetToTimeOffset,
  mapXToTimeOffset,
  mapYToIntensity
} from '../utils/coordinate'

export interface WaveformCoordinateConfig {
  padding: {
    top: number
    right: number
    bottom: number
    left: number
  }
  safeOffset: number
}

export function useWaveformCoordinate(
  canvasState: {
    canvasWidth: { value: number }
    canvasHeight: { value: number }
    virtualScrollOffset: { value: number }
    getEffectiveDuration: () => number
    getGraphAreaWidth: () => number
    getLogicalGraphAreaWidth: () => number
    getGraphAreaHeight: () => number
  },
  config: WaveformCoordinateConfig,
  zoomLevel: { value: number } = { value: 1.0 }
) {
  // 时间映射到X坐标（支持虚拟滚动和缩放）
  const mapTimeToXLocal = (time: number): number => {
    const areaWidth = canvasState.getLogicalGraphAreaWidth()
    const offset = canvasState.virtualScrollOffset.value
    const zoom = zoomLevel.value

    // 应用缩放：缩放后的逻辑宽度
    const scaledAreaWidth = areaWidth * zoom

    return mapTimeToX(
      time,
      canvasState.getEffectiveDuration(),
      scaledAreaWidth,
      config.padding.left,
      config.safeOffset,
      offset
    )
  }

  // 强度映射到Y坐标
  const mapIntensityToYLocal = (intensity: number): number =>
    mapIntensityToY(
      intensity,
      canvasState.getGraphAreaHeight(),
      config.padding.top,
      canvasState.canvasHeight.value,
      config.padding.bottom
    )

  // Y坐标映射到强度
  const mapYToIntensityLocal = (y: number): number =>
    mapYToIntensity(y, canvasState.getGraphAreaHeight(), config.padding.top)

  // X坐标差值映射到时间偏移（支持虚拟滚动和缩放）
  const mapXToTimeOffsetLocal = (pixelOffset: number): number => {
    const areaWidth = canvasState.getLogicalGraphAreaWidth()
    const logicalAreaWidth = canvasState.getLogicalGraphAreaWidth()
    const zoom = zoomLevel.value

    // 应用缩放：缩放后的逻辑宽度
    const scaledAreaWidth = areaWidth * zoom
    const scaledLogicalAreaWidth = logicalAreaWidth * zoom

    return mapXToTimeOffset(
      pixelOffset,
      scaledAreaWidth,
      canvasState.getEffectiveDuration(),
      scaledLogicalAreaWidth
    )
  }

  // X偏移到时间偏移（支持虚拟滚动和缩放）
  const mapXOffsetToTimeOffsetLocal = (pixelOffset: number): number => {
    const areaWidth = canvasState.getLogicalGraphAreaWidth()
    const logicalAreaWidth = canvasState.getLogicalGraphAreaWidth()
    const zoom = zoomLevel.value

    // 应用缩放：缩放后的逻辑宽度
    const scaledAreaWidth = areaWidth * zoom
    const scaledLogicalAreaWidth = logicalAreaWidth * zoom

    return mapXOffsetToTimeOffset(
      pixelOffset,
      scaledAreaWidth,
      canvasState.getEffectiveDuration(),
      scaledLogicalAreaWidth
    )
  }

  // X坐标转换为时间点（支持虚拟滚动和缩放）
  const convertXToTimeLocal = (x: number): number => {
    const areaWidth = canvasState.getLogicalGraphAreaWidth()
    const offset = canvasState.virtualScrollOffset.value
    const logicalAreaWidth = canvasState.getLogicalGraphAreaWidth()
    const zoom = zoomLevel.value

    // 应用缩放：缩放后的逻辑宽度
    const scaledAreaWidth = areaWidth * zoom
    const scaledLogicalAreaWidth = logicalAreaWidth * zoom

    return convertXToTime(
      x,
      scaledAreaWidth,
      canvasState.getEffectiveDuration(),
      offset,
      scaledLogicalAreaWidth
    )
  }

  return {
    // 坐标转换函数
    mapTimeToXLocal,
    mapIntensityToYLocal,
    mapYToIntensityLocal,
    mapXToTimeOffsetLocal,
    mapXOffsetToTimeOffsetLocal,
    convertXToTimeLocal,
  }
}
