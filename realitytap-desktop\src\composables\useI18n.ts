/**
 * 国际化组合函数
 * Internationalization Composable
 */

import { useI18n as useVueI18n } from "vue-i18n";
import { computed } from "vue";
import { useLanguageStore } from "@/stores/language-store";
import type { SupportedLocale } from "@/locales";

/**
 * 增强的国际化组合函数
 * 提供类型安全的翻译功能和语言管理
 */
export function useI18n() {
  const { t, locale, availableLocales } = useVueI18n();
  const languageStore = useLanguageStore();

  // === 翻译函数 ===

  /**
   * 类型安全的翻译函数
   */
  const translate = (key: string, values?: Record<string, any>): string => {
    try {
      return t(key, values || {});
    } catch (error) {
      console.warn(`Translation failed for key: ${key}`, error);
      return key; // 返回原始键作为回退
    }
  };

  /**
   * 带回退的翻译函数
   */
  const translateWithFallback = (key: string, fallback: string, values?: Record<string, any>): string => {
    try {
      const result = t(key, values || {});
      return result === key ? fallback : result;
    } catch (error) {
      console.warn(`Translation failed for key: ${key}`, error);
      return fallback;
    }
  };

  /**
   * 复数形式翻译
   */
  const translatePlural = (key: string, count: number, values?: Record<string, any>): string => {
    try {
      return t(key, { count, ...values }, count);
    } catch (error) {
      console.warn(`Plural translation failed for key: ${key}`, error);
      return key;
    }
  };

  // === 语言管理 ===

  /**
   * 当前语言
   */
  const currentLanguage = computed(() => languageStore.currentLocale);

  /**
   * 当前语言名称
   */
  const currentLanguageName = computed(() => languageStore.currentLanguageName);

  /**
   * 可用语言列表
   */
  const availableLanguages = computed(() => languageStore.availableLanguages);

  /**
   * 切换语言
   */
  const switchLanguage = async (newLocale: SupportedLocale): Promise<void> => {
    await languageStore.setLanguage(newLocale);
  };

  /**
   * 是否为默认语言
   */
  const isDefaultLanguage = computed(() => languageStore.isDefaultLanguage);

  // === 常用翻译快捷方式 ===

  /**
   * 通用文本翻译
   */
  const common = {
    confirm: () => translate("common.confirm"),
    cancel: () => translate("common.cancel"),
    save: () => translate("common.save"),
    delete: () => translate("common.delete"),
    edit: () => translate("common.edit"),
    add: () => translate("common.add"),
    remove: () => translate("common.remove"),
    close: () => translate("common.close"),
    open: () => translate("common.open"),
    create: () => translate("common.create"),
    loading: () => translate("common.loading"),
    success: () => translate("common.success"),
    error: () => translate("common.error"),
    warning: () => translate("common.warning"),
    info: () => translate("common.info"),
  };

  /**
   * 应用相关翻译
   */
  const app = {
    title: () => translate("app.title"),
    subtitle: () => translate("app.subtitle"),
  };

  /**
   * 项目相关翻译
   */
  const project = {
    createTitle: () => translate("project.create.title"),
    createSuccess: () => translate("project.create.success"),
    createFailed: (error: string) => translate("project.create.failedMessage", { error }),
    openTitle: () => translate("project.open.title"),
    openFailed: (error: string) => translate("project.open.failedMessage", { error }),
    saveSuccess: () => translate("project.save.success"),
    saveFailed: (error: string) => translate("project.save.failed", { error }),
  };

  /**
   * 设备相关翻译
   */
  const device = {
    connected: () => translate("device.status.connected"),
    disconnected: () => translate("device.status.disconnected"),
    connecting: () => translate("device.status.connecting"),
    error: () => translate("device.status.error"),
    noDevices: () => translate("device.status.noDevices"),
    deviceCount: (count: number) => translate("device.status.deviceCount", { count }),
    errorCount: (count: number) => translate("device.status.errorCount", { count }),
  };

  /**
   * 错误消息翻译
   */
  const errors = {
    unknown: () => translate("errors.unknown"),
    networkError: () => translate("errors.networkError"),
    fileNotFound: () => translate("errors.fileNotFound"),
    permissionDenied: () => translate("errors.permissionDenied"),
    invalidFormat: () => translate("errors.invalidFormat"),
    operationFailed: () => translate("errors.operationFailed"),
  };

  // === 调试功能 ===

  /**
   * 获取翻译键的调试信息
   */
  const getTranslationDebugInfo = (key: string) => {
    return {
      key,
      currentLocale: currentLanguage.value,
      translation: translate(key),
      exists: t(key) !== key,
    };
  };

  /**
   * 检查翻译键是否存在
   */
  const hasTranslation = (key: string): boolean => {
    try {
      return t(key) !== key;
    } catch {
      return false;
    }
  };

  return {
    // 核心翻译函数
    t: translate,
    translate,
    translateWithFallback,
    translatePlural,

    // 语言管理
    currentLanguage,
    currentLanguageName,
    availableLanguages,
    switchLanguage,
    isDefaultLanguage,

    // 快捷翻译
    common,
    app,
    project,
    device,
    errors,

    // 调试功能
    getTranslationDebugInfo,
    hasTranslation,

    // Vue I18n 原始功能
    locale,
    availableLocales,
  };
}
