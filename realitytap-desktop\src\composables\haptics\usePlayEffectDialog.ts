/**
 * PlayEffectDialog 主要逻辑组合函数
 * Main logic composable for PlayEffectDialog component
 */

import { ref, computed, onMounted, onUnmounted } from "vue";
import type {
  PlaybackControl,
  MotorModel,
  CanvasConfig,
  ConfigFileInfo,
  UsePlayEffectDialogReturn,
  PlaybackControlMethods,
  CanvasDrawingMethods,
  MotorSelectionMethods
} from "@/types/play-effect-dialog";
import {
  DEFAULT_MOTOR_MODELS,
  DEFAULT_CANVAS_CONFIG,
  DEFAULT_PLAYBACK_CONTROL,
  PlaybackState as PlaybackStateEnum
} from "@/types/play-effect-dialog";
import { useI18n } from "@/composables/useI18n";
import { useMessage } from "naive-ui";
import { LogModule, logger } from "@/utils/logger/logger";
import { invoke } from "@tauri-apps/api/core";
import { useAppConfig } from "@/composables/useAppConfig";
import { useLibrtcoreGlobalManager } from "@/composables/haptics/useLibrtcoreGlobalManager";
import {
  parseRatedF0FromFileName,
  generateMotorIdFromFileName,
  generateDisplayNameFromFileName
} from "@/utils/format/motorConfigParser";
import type { RenderableEvent } from "@/types/haptic-editor";
import { convertRealityTapToArray } from "@/utils/format/realityTapArrayConverter";
import { HapticApi } from "@/utils/api/haptic-api";
import { listen } from "@tauri-apps/api/event";
import type { UnlistenFn } from "@tauri-apps/api/event";

/**
 * PlayEffectDialog 主要逻辑组合函数
 */
export function usePlayEffectDialog(
  props: any, // 使用 any 类型避免复杂的类型定义
  emit: any   // 使用 any 类型避免复杂的类型定义
): UsePlayEffectDialogReturn {

  const { t } = useI18n();
  const message = useMessage();
  const { playEffectDialogConfig } = useAppConfig();
  const librtcoreGlobalManager = useLibrtcoreGlobalManager();

  // ===== 状态管理 =====
  
  // 播放控制状态
  const playbackControl = ref<PlaybackControl>({
    ...DEFAULT_PLAYBACK_CONTROL,
    totalDuration: computed(() => props.totalDuration || 0).value
  });

  // 选中的马达
  const selectedMotor = ref<MotorModel | null>(null);

  // 可用马达列表
  const availableMotors = ref<MotorModel[]>([...DEFAULT_MOTOR_MODELS]);

  // 马达加载状态
  const isLoadingMotors = ref(false);

  // Canvas 配置
  const canvasConfig = ref<CanvasConfig>({ ...DEFAULT_CANVAS_CONFIG });

  // Canvas 元素引用
  const canvasRef = ref<HTMLCanvasElement | null>(null);
  const canvasContext = ref<CanvasRenderingContext2D | null>(null);

  // 事件监听器
  let hapticEventUnlisten: UnlistenFn | null = null;

  // ===== 计算属性 =====

  const isPlaying = computed(() =>
    playbackControl.value.state === PlaybackStateEnum.PLAYING
  );

  const canPlay = computed(() =>
    props.events && props.events.length > 0 && selectedMotor.value !== null
  );

  // ===== 播放控制方法 =====

  const playbackMethods: PlaybackControlMethods = {
    async play() {
      if (!canPlay.value) {
        message.warning(t("playEffect.noEvents"));
        return;
      }

      try {
        playbackControl.value.state = PlaybackStateEnum.LOADING;
        logger.info(LogModule.GENERAL, "开始播放效果", {
          motorId: selectedMotor.value?.id,
          eventsCount: props.events?.length || 0
        });

        // 1. 确保 librtcore 已初始化
        if (!librtcoreGlobalManager.canPlay.value) {
          throw new Error("librtcore 未初始化或无法播放");
        }

        // 2. 将 RenderableEvent[] 转换为 int 数组
        const events: RenderableEvent[] = props.events || [];
        logger.debug(LogModule.GENERAL, "转换事件数据", { eventsCount: events.length });

        const conversionResult = convertRealityTapToArray(events, {
          enableLogging: true,
          validateInput: true
        });

        if (!conversionResult.success || !conversionResult.data) {
          throw new Error(`事件转换失败: ${conversionResult.error}`);
        }

        const hapticData = conversionResult.data;
        logger.info(LogModule.GENERAL, "事件转换成功", {
          originalEventsCount: events.length,
          convertedArrayLength: hapticData.length,
          version: conversionResult.version
        });

        // 3. 调用 Rust 后端播放 API
        const playParams = {
          data: hapticData,
          interval_ms: 0,
          loop_count: 1,
          amplitude: 255,
          frequency_offset: 0
        };

        logger.debug(LogModule.GENERAL, "调用后端播放API", playParams);
        const result = await HapticApi.play(playParams);

        playbackControl.value.state = PlaybackStateEnum.PLAYING;
        playbackControl.value.currentTime = 0;

        // 触发播放事件
        emit("play", selectedMotor.value?.id || "");

        logger.info(LogModule.GENERAL, "播放成功", { result });
      } catch (error) {
        logger.error(LogModule.GENERAL, "播放失败", error);
        playbackControl.value.state = PlaybackStateEnum.STOPPED;
        message.error(t("playEffect.errorPlaying", { error: String(error) }));
      }
    },

    async stop() {
      try {
        logger.info(LogModule.GENERAL, "停止播放效果");

        // 调用后端停止API
        if (librtcoreGlobalManager.canPlay.value) {
          await HapticApi.stop();
          logger.debug(LogModule.GENERAL, "后端停止API调用成功");
        }

        playbackControl.value.state = PlaybackStateEnum.STOPPED;
        playbackControl.value.currentTime = 0;

        // 触发停止事件
        emit("stop");
      } catch (error) {
        logger.error(LogModule.GENERAL, "停止失败", error);
        message.error(t("common.error"));
      }
    },

    setVolume(volume: number) {
      playbackControl.value.volume = Math.max(0, Math.min(1, volume));
      logger.debug(LogModule.GENERAL, "设置音量", { volume: playbackControl.value.volume });
    },

    setLooping(loop: boolean) {
      playbackControl.value.isLooping = loop;
      logger.debug(LogModule.GENERAL, "设置循环播放", { loop });
    },

    seekTo(time: number) {
      const clampedTime = Math.max(0, Math.min(playbackControl.value.totalDuration, time));
      playbackControl.value.currentTime = clampedTime;
      logger.debug(LogModule.GENERAL, "跳转到时间点", { time: clampedTime });
    }
  };

  // ===== Canvas 绘制方法 =====

  const canvasMethods: CanvasDrawingMethods = {
    initCanvas(canvas: HTMLCanvasElement) {
      canvasRef.value = canvas;
      canvasContext.value = canvas.getContext("2d");
      
      if (!canvasContext.value) {
        logger.error(LogModule.GENERAL, "无法获取 Canvas 2D 上下文");
        return;
      }

      // 设置 Canvas 尺寸
      this.resizeCanvas(canvasConfig.value.width, canvasConfig.value.height);
      
      logger.debug(LogModule.GENERAL, "Canvas 初始化完成", {
        width: canvas.width,
        height: canvas.height
      });
    },

    resizeCanvas(width: number, height: number) {
      if (!canvasRef.value) return;

      canvasRef.value.width = width;
      canvasRef.value.height = height;
      canvasConfig.value.width = width;
      canvasConfig.value.height = height;

      // 重新绘制
      this.clearCanvas();
      this.drawTimeline();
      if (props.events && props.events.length > 0) {
        this.drawEvents(props.events);
      }
    },

    clearCanvas() {
      if (!canvasContext.value) return;

      const ctx = canvasContext.value;
      const config = canvasConfig.value;
      
      ctx.fillStyle = config.backgroundColor;
      ctx.fillRect(0, 0, config.width, config.height);
    },

    drawTimeline() {
      if (!canvasContext.value) return;

      const ctx = canvasContext.value;
      const config = canvasConfig.value;
      
      // TODO: 实现时间线绘制逻辑
      // 绘制网格、坐标轴等
      
      ctx.strokeStyle = config.gridColor;
      ctx.lineWidth = 1;
      
      // 绘制水平网格线
      for (let y = config.padding.top; y < config.height - config.padding.bottom; y += 50) {
        ctx.beginPath();
        ctx.moveTo(config.padding.left, y);
        ctx.lineTo(config.width - config.padding.right, y);
        ctx.stroke();
      }
      
      // 绘制垂直网格线
      for (let x = config.padding.left; x < config.width - config.padding.right; x += 100) {
        ctx.beginPath();
        ctx.moveTo(x, config.padding.top);
        ctx.lineTo(x, config.height - config.padding.bottom);
        ctx.stroke();
      }
    },

    drawEvents(events) {
      if (!canvasContext.value || !events || events.length === 0) return;

      const ctx = canvasContext.value;
      const config = canvasConfig.value;
      
      // TODO: 实现事件绘制逻辑
      // 根据事件类型绘制不同的图形
      
      ctx.fillStyle = config.eventColor;
      
      events.forEach((_event, index) => {
        // 简单的矩形表示事件
        const x = config.padding.left + (index * 50);
        const y = config.padding.top + 50;
        const width = 40;
        const height = 20;
        
        ctx.fillRect(x, y, width, height);
      });
    },

    drawPlayhead(currentTime: number) {
      if (!canvasContext.value) return;

      const ctx = canvasContext.value;
      const config = canvasConfig.value;
      
      // TODO: 实现播放头绘制逻辑
      const totalDuration = playbackControl.value.totalDuration;
      if (totalDuration === 0) return;
      
      const progress = currentTime / totalDuration;
      const x = config.padding.left + progress * (config.width - config.padding.left - config.padding.right);
      
      ctx.strokeStyle = "#ff0000";
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(x, config.padding.top);
      ctx.lineTo(x, config.height - config.padding.bottom);
      ctx.stroke();
    },

    updateData(data) {
      // TODO: 更新绘制数据
      this.clearCanvas();
      this.drawTimeline();
      this.drawEvents(data.events);
    }
  };

  // ===== 马达选择方法 =====

  const motorMethods: MotorSelectionMethods = {
    getAvailableMotors() {
      return availableMotors.value;
    },

    async loadAvailableMotors() {
      if (isLoadingMotors.value) return;

      isLoadingMotors.value = true;
      try {
        logger.info(LogModule.GENERAL, "开始加载可用马达配置");

        // 调用后端 API 获取配置文件信息
        const configInfos: ConfigFileInfo[] = await invoke('haptic_get_all_config_info');

        // 转换为马达模型
        const motors: MotorModel[] = configInfos
          .filter(info => info.exists)
          .map(info => {
            // 使用工具函数解析文件名信息
            const motorId = generateMotorIdFromFileName(info.name);
            const baseName = info.name.replace(/\.(conf|bin)$/, '');
            const displayName = generateDisplayNameFromFileName(info.name);
            const ratedF0 = parseRatedF0FromFileName(info.name);

            return {
              id: motorId,
              name: baseName,
              displayName,
              configPath: info.path,
              ratedF0,
              resonantFreq: ratedF0, // 保持向后兼容
              description: `马达配置文件: ${info.name} (${(info.size / 1024).toFixed(1)} KB, F0: ${ratedF0}Hz)`
            };
          });

        if (motors.length > 0) {
          availableMotors.value = motors;
      
          // 尝试恢复用户上次选择的马达
          const lastSelectedMotorId = playEffectDialogConfig?.value?.lastSelectedMotorId;
          if (lastSelectedMotorId) {
            const savedMotor = motors.find(m => m.id === lastSelectedMotorId);
            if (savedMotor) {
              selectedMotor.value = savedMotor;
              return; // 成功恢复，直接返回
            }
          }

          // 如果没有选中的马达，选择第一个
          if (!selectedMotor.value) {
            selectedMotor.value = motors[0];
          }
          logger.info(LogModule.GENERAL, "马达配置加载成功", { count: motors.length });
        } else {
          logger.warn(LogModule.GENERAL, "未找到可用的马达配置文件，使用默认配置");
          availableMotors.value = [...DEFAULT_MOTOR_MODELS];
          selectedMotor.value = DEFAULT_MOTOR_MODELS[0];
        }
      } catch (error) {
        logger.error(LogModule.GENERAL, "加载马达配置失败", error);
        message.warning(t("playEffect.motorLoadFailed"));
        // 使用默认配置作为回退
        availableMotors.value = [...DEFAULT_MOTOR_MODELS];
        selectedMotor.value = DEFAULT_MOTOR_MODELS[0];
      } finally {
        isLoadingMotors.value = false;
      }
    },

    selectMotor(motorId: string) {
      const motor = availableMotors.value.find(m => m.id === motorId);
      if (motor) {
        selectedMotor.value = motor;
        emit("motor-change", motorId);
        logger.info(LogModule.GENERAL, "选择马达", {
          motorId,
          motorName: motor.name,
          configPath: motor.configPath
        });
      }
    },

    getCurrentMotor() {
      return selectedMotor.value;
    }
  };

  // ===== 事件处理器 =====

  const handleClose = () => {
    // 停止播放
    if (isPlaying.value) {
      playbackMethods.stop();
    }
    
    emit("update:visible", false);
  };

  const handlePlay = async () => {
    // 只有在未播放状态下才能开始播放
    if (!isPlaying.value) {
      await playbackMethods.play();
    }
  };

  const handleStop = async () => {
    await playbackMethods.stop();
  };

  const handleMotorChange = (motorId: string) => {
    motorMethods.selectMotor(motorId);
  };

  // ===== 初始化 =====

  // 组件挂载时加载马达配置和设置事件监听
  onMounted(async () => {
    await motorMethods.loadAvailableMotors();

    // 设置触觉生命周期事件监听
    try {
      hapticEventUnlisten = await listen("haptic:lifecycle", (event) => {
        const payload = event.payload as any;
        const eventType = payload.type; // start, complete, stop

        logger.debug(LogModule.GENERAL, `收到触觉生命周期事件: ${eventType}`, payload);

        // 根据事件类型更新播放状态
        switch (eventType) {
          case "start":
            // 播放开始
            playbackControl.value.state = PlaybackStateEnum.PLAYING;
            playbackControl.value.currentTime = 0;
            logger.info(LogModule.GENERAL, "触觉播放开始，更新UI状态");
            break;

          case "complete":
            // 播放完成
            playbackControl.value.state = PlaybackStateEnum.STOPPED;
            playbackControl.value.currentTime = 0;
            logger.info(LogModule.GENERAL, "触觉播放完成，更新UI状态");
            break;

          case "stop":
            // 播放停止
            playbackControl.value.state = PlaybackStateEnum.STOPPED;
            playbackControl.value.currentTime = 0;
            logger.info(LogModule.GENERAL, "触觉播放停止，更新UI状态");
            break;

          default:
            logger.warn(LogModule.GENERAL, `未知的触觉生命周期事件类型: ${eventType}`);
        }
      });

      logger.info(LogModule.GENERAL, "PlayEffectDialog: 触觉事件监听器设置完成");
    } catch (error) {
      logger.error(LogModule.GENERAL, "PlayEffectDialog: 设置触觉事件监听器失败", error);
    }
  });

  // 组件卸载时清理事件监听器
  onUnmounted(() => {
    if (hapticEventUnlisten) {
      hapticEventUnlisten();
      hapticEventUnlisten = null;
      logger.debug(LogModule.GENERAL, "PlayEffectDialog: 触觉事件监听器已清理");
    }
  });

  return {
    // 状态
    playbackControl,
    selectedMotor,
    canvasConfig,
    availableMotors,
    isLoadingMotors,

    // 方法
    playbackMethods,
    canvasMethods,
    motorMethods,

    // 事件处理
    handleClose,
    handlePlay,
    handleStop,
    handleMotorChange
  };
}
