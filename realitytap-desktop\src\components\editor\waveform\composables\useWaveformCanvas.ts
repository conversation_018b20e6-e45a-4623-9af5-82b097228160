// 画布管理组合式函数
// 负责画布尺寸、虚拟滚动、坐标缓存等画布相关的状态和逻辑

import { ref, type Ref } from 'vue'
import type { ScrollbarInst } from 'naive-ui'
import {
  calculateTargetGraphWidth,
  calculateActualCanvasWidth,
  calculateVirtualScrollOffset,
  graphAreaWidth,
  graphAreaHeight,
} from '../utils/coordinate'
import { SCROLL_BOUNDARY_TOLERANCE } from "../config/waveform-constants";
import { waveformLogger } from "@/utils/logger/logger";

export interface WaveformCanvasState {
  // 画布尺寸
  canvasWidth: number
  canvasHeight: number
  logicalCanvasWidth: number

  // 滚动状态
  scrollLeftValue: number
  virtualScrollOffset: number

  // 坐标缓存
  coordinateCache: {
    graphAreaWidth: number
    graphAreaHeight: number
    effectiveDuration: number
    lastCanvasWidth: number
    lastCanvasHeight: number
    lastTotalDuration: number
    lastAudioDuration: number | null
  }
}

export interface WaveformCanvasConfig {
  padding: {
    top: number
    right: number
    bottom: number
    left: number
  }
  safeOffset: number
}

export function useWaveformCanvas(
  props: any, // 使用any类型避免复杂的类型定义
  config: WaveformCanvasConfig,
  currentZoomLevel?: Ref<number> // 可选的当前缩放级别参数
) {
  // 画布状态
  const canvasWidth = ref(0)
  const canvasHeight = ref(0)
  const logicalCanvasWidth = ref(0)
  const scrollLeftValue = ref(0)
  const virtualScrollOffset = ref(0)

  // 坐标缓存
  const coordinateCache = ref({
    graphAreaWidth: 0,
    graphAreaHeight: 0,
    effectiveDuration: 0,
    lastCanvasWidth: 0,
    lastCanvasHeight: 0,
    lastTotalDuration: 0,
    lastAudioDuration: null as number | null,
  })

  // 计算有效时长
  const getEffectiveDuration = () => {
    const currentAudioDuration = props.audioDuration
    const currentTotalDuration = props.totalEffectDuration

    if (coordinateCache.value.lastAudioDuration !== currentAudioDuration ||
      coordinateCache.value.lastTotalDuration !== currentTotalDuration) {

      // Store 已经正确处理了音频时长锁定，直接使用 totalEffectDuration
      coordinateCache.value.effectiveDuration = currentTotalDuration
      coordinateCache.value.lastAudioDuration = currentAudioDuration ?? null
      coordinateCache.value.lastTotalDuration = currentTotalDuration

      waveformLogger.debug(`有效时长更新 - 音频时长: ${currentAudioDuration}ms, 总时长: ${currentTotalDuration}ms`);
    }
    return coordinateCache.value.effectiveDuration
  }

  // 计算绘图区域宽度（使用缓存优化）
  const getGraphAreaWidth = () => {
    if (coordinateCache.value.lastCanvasWidth !== canvasWidth.value) {
      coordinateCache.value.graphAreaWidth = graphAreaWidth(
        canvasWidth.value,
        config.padding.left,
        config.padding.right,
        config.safeOffset
      )
      coordinateCache.value.lastCanvasWidth = canvasWidth.value
    }
    return coordinateCache.value.graphAreaWidth
  }

  // 获取逻辑绘图区域宽度
  const getLogicalGraphAreaWidth = () => {
    return graphAreaWidth(
      logicalCanvasWidth.value,
      config.padding.left,
      config.padding.right,
      config.safeOffset
    )
  }

  // 计算绘图区域高度（使用缓存优化）
  const getGraphAreaHeight = () => {
    if (coordinateCache.value.lastCanvasHeight !== canvasHeight.value) {
      coordinateCache.value.graphAreaHeight = graphAreaHeight(
        canvasHeight.value,
        config.padding.top,
        config.padding.bottom
      )
      coordinateCache.value.lastCanvasHeight = canvasHeight.value
    }
    return coordinateCache.value.graphAreaHeight
  }

  // 计算目标画布宽度
  const calculateCanvasWidth = () => {
    // Store 已经正确处理了音频时长锁定，直接使用 totalEffectDuration
    return calculateTargetGraphWidth(
      props.availableParentWidth,
      props.totalEffectDuration,
      props.baselineDuration,
      config.padding.left,
      config.padding.right,
      props.audioDuration
    )
  }

  // 更新虚拟滚动偏移量
  const updateVirtualScrollOffset = () => {
    // 获取当前缩放级别，默认为1.0
    const currentZoom = currentZoomLevel?.value || 1.0

    // 计算缩放后的逻辑宽度
    const scaledLogicalWidth = logicalCanvasWidth.value * currentZoom

    const newOffset = calculateVirtualScrollOffset(
      scrollLeftValue.value,
      scaledLogicalWidth, // 使用缩放后的逻辑宽度
      props.availableParentWidth,
      config.padding.left
    )

    // 计算最大滚动位置，用于边界检查（考虑缩放）
    const logicalScrollableWidth = scaledLogicalWidth - config.padding.left
    const physicalScrollableWidth = props.availableParentWidth - config.padding.left - config.padding.right
    const maxScrollLeft = Math.max(0, logicalScrollableWidth - physicalScrollableWidth)

    // 在边界位置时，强制更新偏移量以确保精确同步
    const isAtLeftBoundary = scrollLeftValue.value <= SCROLL_BOUNDARY_TOLERANCE
    const isAtRightBoundary = scrollLeftValue.value >= maxScrollLeft - SCROLL_BOUNDARY_TOLERANCE

    if (isAtLeftBoundary || isAtRightBoundary || Math.abs(newOffset - virtualScrollOffset.value) > 0.1) {
      virtualScrollOffset.value = newOffset
      return true // 表示偏移量发生了变化
    }
    return false
  }

  // 更新Canvas位置以跟随虚拟滚动
  const updateCanvasPosition = (canvasElement: HTMLCanvasElement | null) => {
    if (!canvasElement) return

    // 获取当前缩放级别，默认为1.0
    const currentZoom = currentZoomLevel?.value || 1.0

    // 计算缩放后的逻辑宽度
    const scaledLogicalWidth = logicalCanvasWidth.value * currentZoom

    const logicalScrollableWidth = scaledLogicalWidth - config.padding.left
    const physicalScrollableWidth = props.availableParentWidth - config.padding.left - config.padding.right
    const maxScrollLeft = Math.max(0, logicalScrollableWidth - physicalScrollableWidth)

    if (maxScrollLeft > 0) {
      const tolerance = SCROLL_BOUNDARY_TOLERANCE

      // 如果滚动位置接近0，直接设置Canvas位置为0
      if (scrollLeftValue.value <= tolerance) {
        canvasElement.style.left = '0px'
        return
      }

      // 如果滚动位置接近或超过最大值，设置Canvas到最大偏移位置
      if (scrollLeftValue.value >= maxScrollLeft - tolerance) {
        const maxCanvasOffset = logicalScrollableWidth - physicalScrollableWidth
        canvasElement.style.left = `${maxCanvasOffset}px`
        return
      }

      // 计算Canvas的左偏移量
      const scrollRatio = Math.max(0, Math.min(scrollLeftValue.value / maxScrollLeft, 1))
      const maxCanvasOffset = logicalScrollableWidth - physicalScrollableWidth
      const canvasLeftOffset = scrollRatio * maxCanvasOffset

      canvasElement.style.left = `${canvasLeftOffset}px`
    } else {
      canvasElement.style.left = '0px'
    }
  }

  // 调整画布尺寸
  const resizeCanvas = (
    targetWidth: number,
    canvasElement: HTMLCanvasElement | null,
    canvasContext: CanvasRenderingContext2D | null,
    containerElement: HTMLDivElement | null
  ) => {
    if (!canvasElement || !canvasContext || !containerElement) return

    const dpr = window.devicePixelRatio || 1

    // 设置逻辑宽度
    logicalCanvasWidth.value = targetWidth

    // 计算实际画布宽度（限制内存分配）
    const actualCanvasWidth = calculateActualCanvasWidth(
      targetWidth,
      props.availableParentWidth,
      config.padding.left,
      config.padding.right
    )

    // 使用实际宽度设置画布
    canvasWidth.value = actualCanvasWidth

    if (containerElement) {
      canvasHeight.value = containerElement.getBoundingClientRect().height
    }

    // 设置画布尺寸
    canvasElement.width = (actualCanvasWidth - config.padding.left) * dpr
    canvasElement.height = canvasHeight.value * dpr
    canvasElement.style.width = `${actualCanvasWidth - config.padding.left}px`
    canvasElement.style.height = `${canvasHeight.value}px`
    canvasContext.scale(dpr, dpr)

    // 更新虚拟滚动偏移量和Canvas位置
    updateVirtualScrollOffset()
  }

  // 检查并修正滚动边界
  const checkAndFixScrollBoundary = (scrollbarRef: ScrollbarInst | null) => {
    if (!scrollbarRef) return false

    const currentScrollLeft = scrollLeftValue.value

    // 获取当前缩放级别，默认为1.0
    const currentZoom = currentZoomLevel?.value || 1.0

    // 计算缩放后的逻辑宽度
    const scaledLogicalWidth = logicalCanvasWidth.value * currentZoom

    // 计算最大滚动位置（考虑缩放）
    const logicalScrollableWidth = scaledLogicalWidth - config.padding.left
    const physicalScrollableWidth = props.availableParentWidth - config.padding.left - config.padding.right
    const maxScrollLeft = Math.max(0, logicalScrollableWidth - physicalScrollableWidth)

    // 检查左边界
    if (currentScrollLeft > 0 && currentScrollLeft <= SCROLL_BOUNDARY_TOLERANCE) {
      scrollbarRef.scrollTo({ left: 0, behavior: "auto" })
      scrollLeftValue.value = 0
      return true
    }

    // 检查右边界
    if (currentScrollLeft > maxScrollLeft && Math.abs(currentScrollLeft - maxScrollLeft) > SCROLL_BOUNDARY_TOLERANCE) {
      scrollbarRef.scrollTo({ left: maxScrollLeft, behavior: "auto" })
      scrollLeftValue.value = maxScrollLeft
      return true
    }

    return false
  }

  return {
    // 状态
    canvasWidth,
    canvasHeight,
    logicalCanvasWidth,
    scrollLeftValue,
    virtualScrollOffset,
    coordinateCache,

    // 计算函数
    getEffectiveDuration,
    getGraphAreaWidth,
    getLogicalGraphAreaWidth,
    getGraphAreaHeight,
    calculateCanvasWidth,

    // 操作函数
    updateVirtualScrollOffset,
    updateCanvasPosition,
    resizeCanvas,
    checkAndFixScrollBoundary,
  }
}
