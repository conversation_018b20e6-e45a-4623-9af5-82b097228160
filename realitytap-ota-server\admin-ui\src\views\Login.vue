<template>
  <div class="login-container" :style="containerStyle">
    <div class="login-card" :style="cardStyle">
      <div class="login-header">
        <h1 class="login-title">RealityTap OTA</h1>
        <p class="login-subtitle">管理界面</p>
      </div>

      <n-form ref="formRef" :model="formData" :rules="rules" size="large" @submit.prevent="handleLogin">
        <n-form-item path="username">
          <n-input v-model:value="formData.username" placeholder="用户名" :input-props="{ autocomplete: 'username' }">
            <template #prefix>
              <n-icon :component="PersonOutline" />
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="password">
          <n-input
            v-model:value="formData.password"
            type="password"
            placeholder="密码"
            show-password-on="click"
            :input-props="{ autocomplete: 'current-password' }"
            @keydown.enter="handleLogin"
          >
            <template #prefix>
              <n-icon :component="LockClosedOutline" />
            </template>
          </n-input>
        </n-form-item>

        <n-form-item>
          <n-checkbox v-model:checked="rememberMe"> 记住登录状态 </n-checkbox>
        </n-form-item>

        <n-form-item>
          <n-button
            type="primary"
            size="large"
            :loading="authStore.loading"
            :disabled="!formData.username || !formData.password"
            block
            @click="handleLogin"
          >
            登录
          </n-button>
        </n-form-item>
      </n-form>

      <div class="login-footer">
        <p class="version-info">
          版本 1.0.0 |
          <n-button text @click="toggleTheme">
            <n-icon :component="themeStore.isDark ? SunnyOutline : MoonOutline" />
            {{ themeStore.isDark ? '浅色模式' : '深色模式' }}
          </n-button>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth';
import { useThemeStore } from '@/stores/theme';
import { LockClosedOutline, MoonOutline, PersonOutline, SunnyOutline } from '@vicons/ionicons5';
import {
  NButton,
  NCheckbox,
  NForm,
  NFormItem,
  NIcon,
  NInput,
  useMessage,
  type FormInst,
  type FormRules,
} from 'naive-ui';
import { computed, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const message = useMessage();
const authStore = useAuthStore();
const themeStore = useThemeStore();

const formRef = ref<FormInst | null>(null);
const rememberMe = ref(true);

const formData = reactive({
  username: '',
  password: '',
});

const rules: FormRules = {
  username: [
    {
      required: true,
      message: '请输入用户名',
      trigger: ['input', 'blur'],
    },
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: ['input', 'blur'],
    },
    {
      min: 1,
      message: '密码不能为空',
      trigger: ['input', 'blur'],
    },
  ],
};

const handleLogin = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const result = await authStore.login({
      username: formData.username,
      password: formData.password,
    });

    if (result.success) {
      message.success('登录成功');

      // 确保认证状态已更新后再跳转
      await new Promise(resolve => setTimeout(resolve, 100));

      // 使用 replace 而不是 push，避免用户返回到登录页
      router.replace('/');
    } else {
      message.error(result.message || '登录失败');
    }
  } catch (error) {
    console.error('Login validation error:', error);
    message.error('登录过程中发生错误，请重试');
  }
};

const toggleTheme = () => {
  themeStore.toggleTheme();
};

// 动态背景样式
const containerStyle = computed(() => {
  if (themeStore.isDark) {
    // 深色模式：深色系渐变
    return {
      background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
    };
  } else {
    // 浅色模式：保持原有的紫色渐变
    return {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    };
  }
});

// 动态卡片样式
const cardStyle = computed(() => {
  if (themeStore.isDark) {
    return {
      background: 'rgba(255, 255, 255, 0.05)',
      backdropFilter: 'blur(20px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
    };
  } else {
    return {
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(20px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
    };
  }
});

onMounted(() => {
  // 如果已经登录，直接跳转到首页
  if (authStore.isAuthenticated) {
    router.push('/');
  }
});
</script>

<style scoped>
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  transition: background 0.3s ease;
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--n-text-color);
}

.login-subtitle {
  font-size: 16px;
  color: var(--n-text-color-2);
  margin: 0;
}

.login-footer {
  margin-top: 24px;
  text-align: center;
}

.version-info {
  font-size: 12px;
  color: var(--n-text-color-3);
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 修复输入框文字垂直对齐问题 */
:deep(.n-input) {
  .n-input-wrapper {
    padding: 14px 16px;
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  .n-input__input-el {
    height: auto;
    min-height: 20px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    padding: 0;
  }

  .n-input__placeholder {
    line-height: 1.5;
    display: flex;
    align-items: center;
  }

  /* 确保前缀图标也垂直居中 */
  .n-input__prefix {
    display: flex;
    align-items: center;
    margin-right: 8px;
  }

  /* 确保后缀（如密码显示按钮）也垂直居中 */
  .n-input__suffix {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
}

/* 优化主题切换按钮样式 */
.login-footer .n-button {
  transition: all 0.3s ease;
}

/* 优化表单项间距 */
:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item:last-child) {
  margin-bottom: 0;
}

/* 优化按钮样式 */
:deep(.n-button) {
  transition: all 0.3s ease;
  font-weight: 500;
}

/* 优化复选框样式 */
:deep(.n-checkbox) {
  .n-checkbox__label {
    font-size: 14px;
    color: var(--n-text-color-2);
  }
}
</style>
