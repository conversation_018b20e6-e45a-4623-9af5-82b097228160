/**
 * RealityTap 版本检测工具
 * 自动检测 JSON 数据的版本（V1 或 V2）
 */

import type { ConvertToArrayInput, VersionDetectionResult, ConvertToArrayOptions } from "@/types/reality-tap-converter";
import type { RenderableEvent } from "@/types/haptic-editor";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 检测 RealityTap 数据的版本
 * @param input 输入数据
 * @param options 检测选项
 * @returns 版本检测结果
 */
export function detectRealityTapVersion(input: ConvertToArrayInput, options: ConvertToArrayOptions = {}): VersionDetectionResult {
  const startTime = performance.now();

  try {
    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, "[VersionDetector] 开始版本检测", {
        inputType: typeof input,
        isArray: Array.isArray(input),
        hasForceVersion: !!options.forceVersion,
        strictMode: options.strictMode
      });
    }

    // 1. 检查是否强制指定版本
    if (options.forceVersion) {
      if (options.enableLogging) {
        logger.info(LogModule.WAVEFORM, "[VersionDetector] 使用强制指定版本", {
          forceVersion: options.forceVersion,
          detectionTime: `${(performance.now() - startTime).toFixed(2)}ms`
        });
      }

      return {
        success: true,
        version: options.forceVersion,
        detectionMethod: "forced",
        confidence: 1.0,
        details: `强制指定版本: ${options.forceVersion}`,
      };
    }

    // 2. 检查输入数据类型
    if (!input || typeof input !== "object") {
      const errorMsg = "输入数据无效或不是对象类型";

      if (options.enableLogging) {
        logger.error(LogModule.WAVEFORM, "[VersionDetector] 输入数据无效", {
          inputType: typeof input,
          isNull: input === null,
          isUndefined: input === undefined,
          detectionTime: `${(performance.now() - startTime).toFixed(2)}ms`
        });
      }

      return {
        success: false,
        detectionMethod: "metadata",
        confidence: 0,
        details: errorMsg,
      };
    }

    // 3. 检查是否为 RenderableEvent 数组
    if (Array.isArray(input)) {
      if (options.enableLogging) {
        logger.debug(LogModule.WAVEFORM, "[VersionDetector] 检测到数组输入，分析 RenderableEvent", {
          arrayLength: input.length
        });
      }

      return detectVersionFromRenderableEvents(input as RenderableEvent[], options);
    }

    // 4. 检查元数据中的版本信息
    const metadataResult = detectVersionFromMetadata(input, options);
    if (metadataResult.success) {
      return metadataResult;
    }

    // 5. 基于数据结构特征检测版本
    const structureResult = detectVersionFromStructure(input, options);
    if (structureResult.success) {
      return structureResult;
    }

    // 6. 默认返回 V1（向后兼容）
    if (options.enableLogging) {
      logger.warn(LogModule.WAVEFORM, "[VersionDetector] 无法确定版本，默认使用 V1");
    }

    return {
      success: true,
      version: 1,
      detectionMethod: "default",
      confidence: 0.3,
      details: "无法确定版本，默认使用 V1",
    };
  } catch (error) {
    if (options.enableLogging) {
      logger.error(LogModule.WAVEFORM, "[VersionDetector] 版本检测过程中发生错误", error);
    }

    return {
      success: false,
      detectionMethod: "metadata",
      confidence: 0,
      details: `检测过程中发生错误: ${error instanceof Error ? error.message : String(error)}`,
    };
  } finally {
    if (options.enableLogging) {
      const processingTime = performance.now() - startTime;
      logger.debug(LogModule.WAVEFORM, "[VersionDetector] 版本检测完成", {
        processingTime: `${processingTime.toFixed(2)}ms`,
      });
    }
  }
}

/**
 * 从元数据中检测版本
 */
function detectVersionFromMetadata(input: any, options: ConvertToArrayOptions): VersionDetectionResult {
  try {
    // 检查 Metadata.Version 字段
    const metadata = input.Metadata || input.metadata;
    if (metadata && typeof metadata === "object") {
      const version = metadata.Version || metadata.version;

      if (version === 1 || version === 2) {
        if (options.enableLogging) {
          logger.debug(LogModule.WAVEFORM, "[VersionDetector] 从元数据检测到版本", {
            version,
            metadataPath: metadata.Version ? "Metadata.Version" : "metadata.version",
          });
        }

        return {
          success: true,
          version: version as 1 | 2,
          detectionMethod: "metadata",
          confidence: 1.0,
          details: `从元数据字段检测到版本: ${version}`,
        };
      }
    }

    return {
      success: false,
      detectionMethod: "metadata",
      confidence: 0,
      details: "元数据中未找到有效的版本信息",
    };
  } catch (error) {
    return {
      success: false,
      detectionMethod: "metadata",
      confidence: 0,
      details: `元数据检测失败: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * 基于数据结构特征检测版本
 */
function detectVersionFromStructure(input: any, options: ConvertToArrayOptions): VersionDetectionResult {
  try {
    const hasPattern = "Pattern" in input || "pattern" in input;
    const hasPatternList = "PatternList" in input || "patternList" in input;

    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, "[VersionDetector] 结构特征检测", {
        hasPattern,
        hasPatternList,
        inputKeys: Object.keys(input),
      });
    }

    // V2 特征：存在 PatternList
    if (hasPatternList) {
      return {
        success: true,
        version: 2,
        detectionMethod: "structure",
        confidence: 0.9,
        details: "检测到 PatternList 字段，判断为 V2 格式",
      };
    }

    // V1 特征：存在 Pattern 但没有 PatternList
    if (hasPattern && !hasPatternList) {
      return {
        success: true,
        version: 1,
        detectionMethod: "structure",
        confidence: 0.8,
        details: "检测到 Pattern 字段且无 PatternList，判断为 V1 格式",
      };
    }

    // 进一步检查事件结构
    const events = extractEventsFromInput(input);
    if (events && events.length > 0) {
      return detectVersionFromEvents(events, options);
    }

    return {
      success: false,
      detectionMethod: "structure",
      confidence: 0,
      details: "无法从数据结构特征确定版本",
    };
  } catch (error) {
    return {
      success: false,
      detectionMethod: "structure",
      confidence: 0,
      details: `结构检测失败: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * 从 RenderableEvent 数组检测版本
 */
function detectVersionFromRenderableEvents(events: RenderableEvent[], options: ConvertToArrayOptions): VersionDetectionResult {
  if (options.enableLogging) {
    logger.debug(LogModule.WAVEFORM, "[VersionDetector] 检测 RenderableEvent 数组版本", {
      eventCount: events.length,
    });
  }

  // RenderableEvent 数组通常对应 V1 格式的简化表示
  // 但也可能需要转换为 V2 格式，这里默认返回 V1
  return {
    success: true,
    version: 1,
    detectionMethod: "structure",
    confidence: 0.7,
    details: "RenderableEvent 数组默认使用 V1 格式",
  };
}

/**
 * 从事件数据检测版本
 */
function detectVersionFromEvents(events: any[], _options: ConvertToArrayOptions): VersionDetectionResult {
  try {
    if (!events || events.length === 0) {
      return {
        success: false,
        detectionMethod: "structure",
        confidence: 0,
        details: "没有事件数据可供分析",
      };
    }

    // 检查第一个事件的结构
    const firstEvent = events[0];
    const eventData = firstEvent.Event || firstEvent.event || firstEvent;

    // V2 特征：存在 Index 字段
    if ("Index" in eventData || "index" in eventData) {
      return {
        success: true,
        version: 2,
        detectionMethod: "structure",
        confidence: 0.8,
        details: "事件中检测到 Index 字段，判断为 V2 格式",
      };
    }

    // 默认判断为 V1
    return {
      success: true,
      version: 1,
      detectionMethod: "structure",
      confidence: 0.6,
      details: "事件结构符合 V1 格式特征",
    };
  } catch (error) {
    return {
      success: false,
      detectionMethod: "structure",
      confidence: 0,
      details: `事件分析失败: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * 从输入数据中提取事件数组
 */
function extractEventsFromInput(input: any): any[] | null {
  try {
    // 尝试从不同的路径提取事件
    if (input.Pattern && Array.isArray(input.Pattern)) {
      return input.Pattern;
    }

    if (input.pattern && Array.isArray(input.pattern)) {
      return input.pattern;
    }

    if (input.PatternList && Array.isArray(input.PatternList)) {
      // V2 格式：从 PatternList 中提取事件
      const allEvents: any[] = [];
      for (const patternItem of input.PatternList) {
        if (patternItem.Pattern && Array.isArray(patternItem.Pattern)) {
          allEvents.push(...patternItem.Pattern);
        }
      }
      return allEvents.length > 0 ? allEvents : null;
    }

    if (input.patternList && Array.isArray(input.patternList)) {
      // V2 格式：从 patternList 中提取事件
      const allEvents: any[] = [];
      for (const patternItem of input.patternList) {
        if (patternItem.pattern && Array.isArray(patternItem.pattern)) {
          allEvents.push(...patternItem.pattern);
        }
      }
      return allEvents.length > 0 ? allEvents : null;
    }

    return null;
  } catch (error) {
    return null;
  }
}

/**
 * 验证检测到的版本是否有效
 */
export function validateDetectedVersion(version: number): version is 1 | 2 {
  return version === 1 || version === 2;
}

/**
 * 获取版本检测的详细信息（用于调试）
 */
export function getVersionDetectionDebugInfo(input: ConvertToArrayInput): Record<string, any> {
  const debugInfo: Record<string, any> = {
    inputType: typeof input,
    isArray: Array.isArray(input),
    hasMetadata: false,
    hasPattern: false,
    hasPatternList: false,
    eventCount: 0,
    firstEventStructure: null,
  };

  try {
    if (input && typeof input === "object" && !Array.isArray(input)) {
      const obj = input as any;
      debugInfo.hasMetadata = !!(obj.Metadata || obj.metadata);
      debugInfo.hasPattern = !!(obj.Pattern || obj.pattern);
      debugInfo.hasPatternList = !!(obj.PatternList || obj.patternList);

      const events = extractEventsFromInput(obj);
      if (events) {
        debugInfo.eventCount = events.length;
        if (events.length > 0) {
          debugInfo.firstEventStructure = Object.keys(events[0]);
        }
      }
    } else if (Array.isArray(input)) {
      debugInfo.eventCount = input.length;
      if (input.length > 0) {
        debugInfo.firstEventStructure = Object.keys(input[0]);
      }
    }
  } catch (error) {
    debugInfo.error = error instanceof Error ? error.message : String(error);
  }

  return debugInfo;
}
