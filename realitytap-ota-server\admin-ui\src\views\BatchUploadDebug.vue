<template>
  <div class="batch-upload-debug">
    <n-page-header title="批量上传调试" subtitle="用于调试批量上传取消功能">
      <template #extra>
        <n-space>
          <n-button type="primary" @click="showUploadModal = true">
            <template #icon>
              <n-icon><cloud-upload-outline /></n-icon>
            </template>
            开始批量上传测试
          </n-button>
          <n-button @click="clearLogs">清空日志</n-button>
        </n-space>
      </template>
    </n-page-header>

    <n-card title="调试日志" class="debug-logs">
      <n-scrollbar style="max-height: 400px;">
        <div v-for="(log, index) in debugLogs" :key="index" class="log-entry">
          <span class="log-timestamp">{{ log.timestamp }}</span>
          <span :class="`log-level log-${log.level}`">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
          <pre v-if="log.data" class="log-data">{{ JSON.stringify(log.data, null, 2) }}</pre>
        </div>
      </n-scrollbar>
    </n-card>

    <!-- 上传模态框 -->
    <n-modal
      v-model:show="showUploadModal"
      preset="card"
      title="批量上传测试"
      style="width: 800px"
      :mask-closable="false"
    >
      <chunk-file-upload 
        @success="handleUploadSuccess" 
        @cancel="handleUploadCancel"
        ref="uploadComponent"
      />
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="debugUploadState">调试上传状态</n-button>
          <n-button @click="testCancelMetadata" type="warning">测试取消元数据编辑</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import ChunkFileUpload from '@/components/Common/ChunkFileUpload.vue';
import { CloudUploadOutline } from '@vicons/ionicons5';
import {
  NButton,
  NCard,
  NIcon,
  NModal,
  NPageHeader,
  NScrollbar,
  NSpace,
  useMessage,
} from 'naive-ui';
import { ref, onMounted } from 'vue';

const message = useMessage();
const showUploadModal = ref(false);
const uploadComponent = ref<InstanceType<typeof ChunkFileUpload> | null>(null);

interface DebugLog {
  timestamp: string;
  level: 'info' | 'warn' | 'error';
  message: string;
  data?: any;
}

const debugLogs = ref<DebugLog[]>([]);

const addLog = (level: 'info' | 'warn' | 'error', message: string, data?: any) => {
  debugLogs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    level,
    message,
    data,
  });
};

const clearLogs = () => {
  debugLogs.value = [];
};

const handleUploadSuccess = () => {
  addLog('info', '上传成功回调被触发');
  showUploadModal.value = false;
  message.success('文件上传成功');
};

const handleUploadCancel = () => {
  addLog('info', '上传取消回调被触发');
  showUploadModal.value = false;
};

const debugUploadState = () => {
  if (!uploadComponent.value) {
    addLog('warn', '上传组件引用不存在');
    return;
  }

  try {
    const debugState = uploadComponent.value.getDebugState();
    addLog('info', '当前上传组件状态', debugState);
  } catch (error: any) {
    addLog('error', '获取调试状态失败', { error: error.message });
  }
};

const testCancelMetadata = () => {
  if (!uploadComponent.value) {
    addLog('warn', '上传组件引用不存在');
    return;
  }

  try {
    addLog('info', '手动触发取消元数据编辑测试');

    // 先获取当前状态
    const debugState = uploadComponent.value.getDebugState();
    addLog('info', '取消前的状态', debugState);

    // 调用取消方法
    uploadComponent.value.cancelMetadataEdit();

    addLog('info', '取消元数据编辑方法已调用');
  } catch (error: any) {
    addLog('error', '调用取消元数据编辑失败', { error: error.message });
  }
};

// 监听控制台日志（如果可能的话）
onMounted(() => {
  addLog('info', '批量上传调试页面已加载');
  
  // 重写 console.log 来捕获组件内部的日志
  const originalLog = console.log;
  const originalWarn = console.warn;
  const originalError = console.error;
  
  console.log = (...args) => {
    originalLog(...args);
    if (args.length > 0 && typeof args[0] === 'string') {
      if (args[0].includes('cancelMetadataEdit') || args[0].includes('showMetadataFormAfterUpload')) {
        addLog('info', `Console: ${args[0]}`, args.slice(1));
      }
    }
  };
  
  console.warn = (...args) => {
    originalWarn(...args);
    if (args.length > 0 && typeof args[0] === 'string') {
      if (args[0].includes('cancelMetadataEdit') || args[0].includes('showMetadataFormAfterUpload')) {
        addLog('warn', `Console: ${args[0]}`, args.slice(1));
      }
    }
  };
  
  console.error = (...args) => {
    originalError(...args);
    if (args.length > 0 && typeof args[0] === 'string') {
      if (args[0].includes('cancelMetadataEdit') || args[0].includes('showMetadataFormAfterUpload')) {
        addLog('error', `Console: ${args[0]}`, args.slice(1));
      }
    }
  };
});
</script>

<style scoped>
.batch-upload-debug {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.debug-logs {
  margin-top: 20px;
}

.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-timestamp {
  color: #666;
  min-width: 80px;
}

.log-level {
  min-width: 50px;
  font-weight: bold;
}

.log-info {
  color: #1890ff;
}

.log-warn {
  color: #faad14;
}

.log-error {
  color: #ff4d4f;
}

.log-message {
  flex: 1;
}

.log-data {
  margin-top: 4px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 11px;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
