#!/usr/bin/env tsx

/**
 * 发布管理脚本
 * 用于上传和管理OTA更新包
 */

import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import { logger } from '../src/utils/logger.util';

interface UploadOptions {
  file: string;
  version: string;
  platform: string;
  architecture: string;
  channel: 'stable' | 'beta' | 'alpha';
  releaseNotes?: string;
  force?: boolean;
  dryRun?: boolean;
}

interface ReleaseInfo {
  filename: string;
  size: number;
  checksum: string;
  releaseDate: string;
  releaseNotes: string;
}

/**
 * 计算文件SHA256校验和
 */
async function calculateChecksum(filePath: string): Promise<string> {
  const hash = crypto.createHash('sha256');
  const stream = await fs.readFile(filePath);
  hash.update(stream);
  return `sha256:${hash.digest('hex')}`;
}

/**
 * 获取文件大小
 */
async function getFileSize(filePath: string): Promise<number> {
  const stats = await fs.stat(filePath);
  return stats.size;
}

/**
 * 验证文件路径
 */
async function validateFile(filePath: string): Promise<void> {
  try {
    const stats = await fs.stat(filePath);
    if (!stats.isFile()) {
      throw new Error(`${filePath} 不是一个文件`);
    }
  } catch (error) {
    throw new Error(`文件不存在或无法访问: ${filePath}`);
  }
}

/**
 * 验证版本格式
 */
function validateVersion(version: string): void {
  const versionRegex = /^\d+\.\d+\.\d+(?:-[a-zA-Z0-9]+(?:\.\d+)?)?$/;
  if (!versionRegex.test(version)) {
    throw new Error(`无效的版本格式: ${version}`);
  }
}

/**
 * 验证平台和架构
 */
function validatePlatformAndArch(platform: string, architecture: string): void {
  const validPlatforms = ['windows', 'macos', 'linux'];
  const validArchitectures = ['x86_64', 'aarch64', 'x86'];

  if (!validPlatforms.includes(platform)) {
    throw new Error(`不支持的平台: ${platform}. 支持的平台: ${validPlatforms.join(', ')}`);
  }

  if (!validArchitectures.includes(architecture)) {
    throw new Error(`不支持的架构: ${architecture}. 支持的架构: ${validArchitectures.join(', ')}`);
  }
}

/**
 * 读取版本配置
 */
async function readVersionsConfig(): Promise<any> {
  const configPath = path.join(process.cwd(), 'storage/metadata/versions.json');
  try {
    const content = await fs.readFile(configPath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    throw new Error(`无法读取版本配置文件: ${configPath}`);
  }
}

/**
 * 写入版本配置
 */
async function writeVersionsConfig(config: any): Promise<void> {
  const configPath = path.join(process.cwd(), 'storage/metadata/versions.json');
  const backupPath = `${configPath}.backup.${Date.now()}`;
  
  // 创建备份
  try {
    await fs.copyFile(configPath, backupPath);
    console.log(`✓ 创建配置备份: ${backupPath}`);
  } catch (error) {
    console.warn(`⚠ 无法创建配置备份: ${error}`);
  }

  // 写入新配置
  await fs.writeFile(configPath, JSON.stringify(config, null, 2));
  console.log(`✓ 更新版本配置: ${configPath}`);
}

/**
 * 复制发布文件
 */
async function copyReleaseFile(
  sourcePath: string, 
  targetDir: string, 
  filename: string
): Promise<void> {
  const targetPath = path.join(targetDir, filename);
  
  // 确保目标目录存在
  await fs.mkdir(targetDir, { recursive: true });
  
  // 复制文件
  await fs.copyFile(sourcePath, targetPath);
  console.log(`✓ 复制文件: ${sourcePath} -> ${targetPath}`);
}

/**
 * 更新版本配置
 */
function updateVersionConfig(
  config: any,
  options: UploadOptions,
  releaseInfo: ReleaseInfo
): void {
  // 确保渠道存在
  if (!config.channels[options.channel]) {
    config.channels[options.channel] = {
      version: options.version,
      platforms: {}
    };
  }

  // 更新版本
  config.channels[options.channel].version = options.version;

  // 确保平台存在
  if (!config.channels[options.channel].platforms[options.platform]) {
    config.channels[options.channel].platforms[options.platform] = {};
  }

  // 更新平台架构信息
  config.channels[options.channel].platforms[options.platform][options.architecture] = releaseInfo;

  console.log(`✓ 更新配置: ${options.channel}/${options.platform}/${options.architecture} -> ${options.version}`);
}

/**
 * 生成文件名
 */
function generateFilename(options: UploadOptions, originalFilename: string): string {
  const ext = path.extname(originalFilename);
  return `realitytap-${options.version}-${options.platform}-${options.architecture}${ext}`;
}

/**
 * 显示发布信息
 */
function displayReleaseInfo(options: UploadOptions, releaseInfo: ReleaseInfo): void {
  console.log('\n📦 发布信息:');
  console.log(`  版本: ${options.version}`);
  console.log(`  平台: ${options.platform}`);
  console.log(`  架构: ${options.architecture}`);
  console.log(`  渠道: ${options.channel}`);
  console.log(`  文件名: ${releaseInfo.filename}`);
  console.log(`  文件大小: ${(releaseInfo.size / 1024 / 1024).toFixed(2)} MB`);
  console.log(`  校验和: ${releaseInfo.checksum}`);
  console.log(`  发布时间: ${releaseInfo.releaseDate}`);
  if (releaseInfo.releaseNotes) {
    console.log(`  发布说明: ${releaseInfo.releaseNotes}`);
  }
}

/**
 * 主上传函数
 */
async function uploadRelease(options: UploadOptions): Promise<void> {
  console.log('🚀 开始上传发布包...\n');

  // 验证输入
  await validateFile(options.file);
  validateVersion(options.version);
  validatePlatformAndArch(options.platform, options.architecture);

  // 计算文件信息
  console.log('📊 计算文件信息...');
  const fileSize = await getFileSize(options.file);
  const checksum = await calculateChecksum(options.file);
  console.log(`✓ 文件大小: ${(fileSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`✓ 校验和: ${checksum}`);

  // 生成发布信息
  const originalFilename = path.basename(options.file);
  const filename = generateFilename(options, originalFilename);
  const releaseInfo: ReleaseInfo = {
    filename,
    size: fileSize,
    checksum,
    releaseDate: new Date().toISOString(),
    releaseNotes: options.releaseNotes || `Release ${options.version}`
  };

  // 显示发布信息
  displayReleaseInfo(options, releaseInfo);

  if (options.dryRun) {
    console.log('\n🔍 干运行模式，不会实际执行操作');
    return;
  }

  // 读取现有配置
  console.log('\n📖 读取版本配置...');
  const config = await readVersionsConfig();

  // 检查是否已存在
  const existingRelease = config.channels[options.channel]?.platforms[options.platform]?.[options.architecture];
  if (existingRelease && !options.force) {
    console.error(`\n❌ 发布已存在: ${options.channel}/${options.platform}/${options.architecture}`);
    console.error(`   当前版本: ${config.channels[options.channel].version}`);
    console.error(`   使用 --force 参数强制覆盖`);
    process.exit(1);
  }

  // 复制文件
  console.log('\n📁 复制发布文件...');
  const targetDir = path.join(process.cwd(), 'storage/releases', options.channel);
  await copyReleaseFile(options.file, targetDir, filename);

  // 更新配置
  console.log('\n⚙️ 更新版本配置...');
  updateVersionConfig(config, options, releaseInfo);
  await writeVersionsConfig(config);

  console.log('\n🎉 发布上传完成！');
}

/**
 * 解析命令行参数
 */
function parseArgs(): UploadOptions {
  const args = process.argv.slice(2);
  const options: Partial<UploadOptions> = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    const nextArg = args[i + 1];

    switch (arg) {
      case '--file':
      case '-f':
        options.file = nextArg;
        i++;
        break;
      case '--version':
      case '-v':
        options.version = nextArg;
        i++;
        break;
      case '--platform':
      case '-p':
        options.platform = nextArg;
        i++;
        break;
      case '--architecture':
      case '--arch':
      case '-a':
        options.architecture = nextArg;
        i++;
        break;
      case '--channel':
      case '-c':
        options.channel = nextArg as any;
        i++;
        break;
      case '--release-notes':
      case '-n':
        options.releaseNotes = nextArg;
        i++;
        break;
      case '--force':
        options.force = true;
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
    }
  }

  // 验证必需参数
  const required = ['file', 'version', 'platform', 'architecture', 'channel'];
  for (const field of required) {
    if (!options[field as keyof UploadOptions]) {
      console.error(`❌ 缺少必需参数: --${field}`);
      showHelp();
      process.exit(1);
    }
  }

  return options as UploadOptions;
}

/**
 * 显示帮助信息
 */
function showHelp(): void {
  console.log(`
RealityTap OTA 发布上传工具

用法:
  npm run upload-release -- [选项]

必需参数:
  -f, --file <path>           发布文件路径
  -v, --version <version>     版本号 (例如: 1.0.0)
  -p, --platform <platform>  平台 (windows|macos|linux)
  -a, --arch <architecture>  架构 (x86_64|aarch64|x86)
  -c, --channel <channel>     发布渠道 (stable|beta|alpha)

可选参数:
  -n, --release-notes <text> 发布说明
  --force                    强制覆盖已存在的发布
  --dry-run                  干运行模式，不实际执行
  -h, --help                 显示帮助信息

示例:
  npm run upload-release -- \\
    --file ./realitytap-1.0.0.exe \\
    --version 1.0.0 \\
    --platform windows \\
    --arch x86_64 \\
    --channel stable \\
    --release-notes "Initial stable release"
`);
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  try {
    const options = parseArgs();
    await uploadRelease(options);
  } catch (error) {
    console.error('\n❌ 上传失败:', error);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

export { uploadRelease };
