import { DownloadRecord, DownloadStatsData, DownloadStatsQuery } from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import { DownloadRecordDAO } from '@/dao/download-record.dao';

/**
 * 基于数据库的统计管理服务
 */
export class StatsDatabaseService {
  private downloadRecordDAO: DownloadRecordDAO;
  private statsCache: DownloadStatsData | null = null;
  private lastCacheUpdate: number = 0;
  private readonly cacheTimeout = 300000; // 5分钟缓存

  constructor() {
    this.downloadRecordDAO = new DownloadRecordDAO();
  }

  /**
   * 记录下载
   */
  async recordDownload(downloadInfo: {
    filename: string;
    version: string;
    platform: string;
    architecture: string;
    channel: string;
    clientIP: string;
    userAgent: string;
    fileSize: number;
    downloadDuration?: number;
  }): Promise<void> {
    try {
      await this.downloadRecordDAO.createDownloadRecord({
        filename: downloadInfo.filename,
        version: downloadInfo.version,
        platform: downloadInfo.platform,
        architecture: downloadInfo.architecture,
        channel: downloadInfo.channel,
        download_time: new Date().toISOString(),
        client_ip: downloadInfo.clientIP,
        user_agent: downloadInfo.userAgent,
        file_size: downloadInfo.fileSize,
        download_duration: downloadInfo.downloadDuration
      });

      // 清除缓存以确保统计数据是最新的
      this.clearCache();

      logger.debug('Download recorded in database', {
        filename: downloadInfo.filename,
        version: downloadInfo.version,
        platform: downloadInfo.platform,
        clientIP: downloadInfo.clientIP
      });
    } catch (error) {
      logger.error('Failed to record download in database', { downloadInfo, error });
      throw new Error(`Failed to record download: ${error}`);
    }
  }

  /**
   * 获取下载统计数据
   */
  async getDownloadStats(query: DownloadStatsQuery = {}): Promise<DownloadStatsData> {
    try {
      // 如果没有查询条件且缓存有效，返回缓存数据
      if (this.shouldUseCache(query)) {
        return this.statsCache!;
      }

      const stats = await this.downloadRecordDAO.getAggregatedStats(query);

      // 如果是无条件查询，更新缓存
      if (this.isUnconditionalQuery(query)) {
        this.statsCache = stats;
        this.lastCacheUpdate = Date.now();
      }

      logger.debug('Download stats retrieved from database', { query, totalDownloads: stats.totalDownloads });
      return stats;
    } catch (error) {
      logger.error('Failed to get download stats from database', { query, error });
      throw new Error(`Failed to get download stats: ${error}`);
    }
  }

  /**
   * 获取下载记录
   */
  async getDownloadRecords(query: DownloadStatsQuery = {}): Promise<DownloadRecord[]> {
    try {
      const records = await this.downloadRecordDAO.getDownloadRecords(query);
      
      logger.debug('Download records retrieved from database', { 
        query, 
        recordCount: records.length 
      });
      
      return records;
    } catch (error) {
      logger.error('Failed to get download records from database', { query, error });
      throw new Error(`Failed to get download records: ${error}`);
    }
  }

  /**
   * 获取热门下载
   */
  async getPopularDownloads(limit: number = 10, days: number = 30): Promise<Array<{
    filename: string;
    version: string;
    platform: string;
    channel: string;
    downloadCount: number;
    totalBytes: number;
  }>> {
    try {
      const popularDownloads = await this.downloadRecordDAO.getPopularDownloads(limit, days);
      
      return popularDownloads.map(item => ({
        filename: item.filename,
        version: item.version,
        platform: item.platform,
        channel: item.channel,
        downloadCount: item.download_count,
        totalBytes: item.total_bytes
      }));
    } catch (error) {
      logger.error('Failed to get popular downloads from database', { limit, days, error });
      throw new Error(`Failed to get popular downloads: ${error}`);
    }
  }

  /**
   * 获取下载趋势
   */
  async getDownloadTrends(days: number = 30): Promise<Array<{
    date: string;
    downloadCount: number;
    totalBytes: number;
  }>> {
    try {
      const trends = await this.downloadRecordDAO.getDownloadTrends(days);
      
      return trends.map(item => ({
        date: item.date,
        downloadCount: item.download_count,
        totalBytes: item.total_bytes
      }));
    } catch (error) {
      logger.error('Failed to get download trends from database', { days, error });
      throw new Error(`Failed to get download trends: ${error}`);
    }
  }

  /**
   * 获取用户下载历史
   */
  async getUserDownloadHistory(clientIP: string, limit: number = 50): Promise<DownloadRecord[]> {
    try {
      const history = await this.downloadRecordDAO.getUserDownloadHistory(clientIP, limit);
      
      logger.debug('User download history retrieved from database', { 
        clientIP, 
        limit, 
        recordCount: history.length 
      });
      
      return history;
    } catch (error) {
      logger.error('Failed to get user download history from database', { clientIP, limit, error });
      throw new Error(`Failed to get user download history: ${error}`);
    }
  }

  /**
   * 获取统计概览
   */
  async getStatsOverview(): Promise<{
    totalDownloads: number;
    totalBytes: number;
    todayDownloads: number;
    weekDownloads: number;
    monthDownloads: number;
    topVersions: Array<{ version: string; count: number }>;
    topPlatforms: Array<{ platform: string; count: number }>;
    topChannels: Array<{ channel: string; count: number }>;
  }> {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();

      const [
        totalStats,
        todayStats,
        weekStats,
        monthStats
      ] = await Promise.all([
        this.getDownloadStats(),
        this.getDownloadStats({ startDate: today }),
        this.getDownloadStats({ startDate: weekAgo }),
        this.getDownloadStats({ startDate: monthAgo })
      ]);

      // 获取排行榜数据
      const topVersions = Object.entries(totalStats.byVersion)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([version, count]) => ({ version, count }));

      const topPlatforms = Object.entries(totalStats.byPlatform)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([platform, count]) => ({ platform, count }));

      const topChannels = Object.entries(totalStats.byChannel)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([channel, count]) => ({ channel, count }));

      return {
        totalDownloads: totalStats.totalDownloads,
        totalBytes: totalStats.totalBytes,
        todayDownloads: todayStats.totalDownloads,
        weekDownloads: weekStats.totalDownloads,
        monthDownloads: monthStats.totalDownloads,
        topVersions,
        topPlatforms,
        topChannels
      };
    } catch (error) {
      logger.error('Failed to get stats overview from database', { error });
      throw new Error(`Failed to get stats overview: ${error}`);
    }
  }

  /**
   * 清理旧的下载记录
   */
  async cleanupOldRecords(olderThanDays: number = 90): Promise<number> {
    try {
      const deletedCount = await this.downloadRecordDAO.deleteOldRecords(olderThanDays);
      
      // 清除缓存
      this.clearCache();
      
      logger.info('Old download records cleaned up from database', { 
        olderThanDays, 
        deletedCount 
      });
      
      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup old records from database', { olderThanDays, error });
      throw new Error(`Failed to cleanup old records: ${error}`);
    }
  }

  /**
   * 批量导入下载记录
   */
  async batchImportRecords(records: Array<{
    filename: string;
    version: string;
    platform: string;
    architecture: string;
    channel: string;
    downloadTime: string;
    clientIP: string;
    userAgent: string;
    fileSize: number;
    downloadDuration?: number;
  }>): Promise<void> {
    try {
      const recordsData = records.map(record => ({
        filename: record.filename,
        version: record.version,
        platform: record.platform,
        architecture: record.architecture,
        channel: record.channel,
        download_time: record.downloadTime,
        client_ip: record.clientIP,
        user_agent: record.userAgent,
        file_size: record.fileSize,
        download_duration: record.downloadDuration
      }));

      await this.downloadRecordDAO.batchCreateDownloadRecords(recordsData);
      
      // 清除缓存
      this.clearCache();
      
      logger.info('Download records batch imported to database', { 
        recordCount: records.length 
      });
    } catch (error) {
      logger.error('Failed to batch import records to database', { 
        recordCount: records.length, 
        error 
      });
      throw new Error(`Failed to batch import records: ${error}`);
    }
  }

  /**
   * 获取统计摘要（兼容接口方法）
   */
  async getSummaryStats(): Promise<DownloadStatsData> {
    return this.getDownloadStats();
  }

  /**
   * 清理旧的统计数据（兼容接口方法）
   */
  async cleanupOldStats(daysToKeep: number): Promise<void> {
    await this.cleanupOldRecords(daysToKeep);
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.statsCache = null;
    this.lastCacheUpdate = 0;
    logger.debug('Stats database service cache cleared');
  }

  /**
   * 检查是否应该使用缓存
   */
  private shouldUseCache(query: DownloadStatsQuery): boolean {
    if (!this.statsCache) return false;
    if (!this.isUnconditionalQuery(query)) return false;
    
    const now = Date.now();
    return now - this.lastCacheUpdate < this.cacheTimeout;
  }

  /**
   * 检查是否是无条件查询
   */
  private isUnconditionalQuery(query: DownloadStatsQuery): boolean {
    return !query.startDate && 
           !query.endDate && 
           !query.version && 
           !query.platform && 
           !query.channel &&
           !query.limit &&
           !query.offset;
  }
}

// 导出单例实例
export const statsDatabaseService = new StatsDatabaseService();
