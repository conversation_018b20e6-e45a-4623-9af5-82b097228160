/**
 * librtcore 配置变化监听器
 * 监听应用配置变化，自动更新全局 librtcore 配置
 */

import { watch, onUnmounted, getCurrentInstance } from "vue";
import { useAppConfig } from "@/composables/useAppConfig";
import { useLibrtcoreGlobalManager } from "./useLibrtcoreGlobalManager";
import { useLibrtcoreGlobalConfig } from "./useLibrtcoreGlobalConfig";
import { logger, LogModule } from "@/utils/logger/logger";
import type { SamplingRateType } from "@/types/haptic-types";

/**
 * 配置变化监听器选项
 */
export interface ConfigWatcherOptions {
  /** 是否启用自动配置更新 */
  enableAutoUpdate?: boolean;
  /** 是否在启动时检查配置 */
  checkOnStart?: boolean;
  /** 防抖延迟时间（毫秒） */
  debounceDelay?: number;
}

/**
 * 配置变化监听器返回接口
 */
export interface UseLibrtcoreConfigWatcherReturn {
  /** 开始监听配置变化 */
  startWatching: () => void;
  /** 停止监听配置变化 */
  stopWatching: () => void;
  /** 手动检查并更新配置 */
  checkAndUpdateConfig: () => Promise<boolean>;
  /** 是否正在监听 */
  isWatching: boolean;
}

/**
 * librtcore 配置变化监听器
 */
export function useLibrtcoreConfigWatcher(
  options: ConfigWatcherOptions = {}
): UseLibrtcoreConfigWatcherReturn {
  const {
    enableAutoUpdate = true,
    checkOnStart = true,
    debounceDelay = 500,
  } = options;

  const { playEffectDialogConfig } = useAppConfig();
  const librtcoreGlobalManager = useLibrtcoreGlobalManager();
  const { findMotorById, validateConfig } = useLibrtcoreGlobalConfig();

  let isWatching = false;
  let stopWatchers: (() => void)[] = [];
  let debounceTimer: number | null = null;

  /**
   * 防抖执行配置更新
   */
  const debouncedUpdateConfig = (motorId: string, samplingRate: SamplingRateType) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    debounceTimer = setTimeout(async () => {
      await updateConfigurationInternal(motorId, samplingRate);
    }, debounceDelay);
  };

  /**
   * 内部配置更新逻辑
   */
  const updateConfigurationInternal = async (
    motorId: string,
    samplingRate: SamplingRateType
  ): Promise<boolean> => {
    try {
      const motor = findMotorById(motorId);
      if (!motor) {
        logger.warn(LogModule.GENERAL, "配置监听器：找不到指定的马达", { motorId });
        return false;
      }

      if (!validateConfig(motor, samplingRate)) {
        logger.warn(LogModule.GENERAL, "配置监听器：配置验证失败", {
          motorId,
          samplingRate
        });
        return false;
      }

      // 首先尝试同步后端状态
      logger.debug(LogModule.GENERAL, "配置监听器：同步后端状态");
      const synced = await librtcoreGlobalManager.syncWithBackend();

      if (synced) {
        // 后端已初始化，检查配置是否匹配
        if (!librtcoreGlobalManager.isConfigurationChanged(motor, samplingRate)) {
          logger.debug(LogModule.GENERAL, "配置监听器：后端已初始化且配置匹配，无需更新");
          return true;
        } else {
          logger.info(LogModule.GENERAL, "配置监听器：后端已初始化但配置不匹配，需要更新", {
            motorId,
            motorName: motor.name,
            samplingRate,
          });
        }
      } else {
        // 后端未初始化，检查前端配置是否变化
        if (!librtcoreGlobalManager.isConfigurationChanged(motor, samplingRate)) {
          logger.debug(LogModule.GENERAL, "配置监听器：配置未发生变化，跳过更新");
          return true;
        }

        logger.info(LogModule.GENERAL, "配置监听器：检测到配置变化，开始更新", {
          motorId,
          motorName: motor.name,
          samplingRate,
        });
      }

      const success = await librtcoreGlobalManager.updateConfiguration(motor, samplingRate);

      if (success) {
        logger.info(LogModule.GENERAL, "配置监听器：配置更新成功");
      } else {
        logger.error(LogModule.GENERAL, "配置监听器：配置更新失败");
      }

      return success;
    } catch (error) {
      logger.error(LogModule.GENERAL, "配置监听器：配置更新过程中出现错误", error);
      return false;
    }
  };

  /**
   * 检查并更新配置
   */
  const checkAndUpdateConfig = async (): Promise<boolean> => {
    const config = playEffectDialogConfig.value;
    if (!config?.lastSelectedMotorId || !config?.lastSelectedSamplingRate) {
      logger.debug(LogModule.GENERAL, "配置监听器：缺少必要的配置信息");
      return false;
    }

    // 启动时先同步后端状态，避免不必要的初始化
    logger.debug(LogModule.GENERAL, "配置监听器：启动时检查配置，先同步后端状态");
    const synced = await librtcoreGlobalManager.syncWithBackend();

    if (synced) {
      logger.info(LogModule.GENERAL, "配置监听器：后端已初始化，检查配置是否需要更新");
      // 后端已初始化，只有在配置不匹配时才更新
      const motor = findMotorById(config.lastSelectedMotorId);
      if (motor && !librtcoreGlobalManager.isConfigurationChanged(motor, config.lastSelectedSamplingRate)) {
        logger.debug(LogModule.GENERAL, "配置监听器：后端配置已匹配，无需更新");
        return true;
      }
    }

    return await updateConfigurationInternal(
      config.lastSelectedMotorId,
      config.lastSelectedSamplingRate
    );
  };

  /**
   * 开始监听配置变化
   */
  const startWatching = () => {
    if (isWatching) {
      logger.debug(LogModule.GENERAL, "配置监听器：已在监听中");
      return;
    }

    logger.info(LogModule.GENERAL, "配置监听器：开始监听配置变化");

    // 监听马达配置变化
    const stopMotorWatcher = watch(
      () => playEffectDialogConfig.value?.lastSelectedMotorId,
      (newMotorId, oldMotorId) => {
        if (newMotorId && newMotorId !== oldMotorId && enableAutoUpdate) {
          const samplingRate = playEffectDialogConfig.value?.lastSelectedSamplingRate;
          if (samplingRate) {
            logger.debug(LogModule.GENERAL, "配置监听器：检测到马达变化", {
              oldMotorId,
              newMotorId,
            });
            debouncedUpdateConfig(newMotorId, samplingRate);
          }
        }
      },
      { immediate: false }
    );

    // 监听采样率配置变化
    const stopSamplingRateWatcher = watch(
      () => playEffectDialogConfig.value?.lastSelectedSamplingRate,
      (newSamplingRate, oldSamplingRate) => {
        if (newSamplingRate && newSamplingRate !== oldSamplingRate && enableAutoUpdate) {
          const motorId = playEffectDialogConfig.value?.lastSelectedMotorId;
          if (motorId) {
            logger.debug(LogModule.GENERAL, "配置监听器：检测到采样率变化", {
              oldSamplingRate,
              newSamplingRate,
            });
            debouncedUpdateConfig(motorId, newSamplingRate);
          }
        }
      },
      { immediate: false }
    );

    stopWatchers = [stopMotorWatcher, stopSamplingRateWatcher];
    isWatching = true;

    // 启动时检查配置
    if (checkOnStart) {
      setTimeout(() => {
        checkAndUpdateConfig().catch(error => {
          logger.warn(LogModule.GENERAL, "配置监听器：启动时配置检查失败", error);
        });
      }, 1000); // 延迟1秒确保其他组件已初始化
    }
  };

  /**
   * 停止监听配置变化
   */
  const stopWatching = () => {
    if (!isWatching) {
      return;
    }

    logger.info(LogModule.GENERAL, "配置监听器：停止监听配置变化");

    // 清除防抖定时器
    if (debounceTimer) {
      clearTimeout(debounceTimer);
      debounceTimer = null;
    }

    // 停止所有监听器
    stopWatchers.forEach(stop => stop());
    stopWatchers = [];
    isWatching = false;
  };

  // 组件卸载时自动停止监听（仅在组件上下文中）
  const currentInstance = getCurrentInstance();
  if (currentInstance) {
    onUnmounted(() => {
      stopWatching();
    });
  }

  return {
    startWatching,
    stopWatching,
    checkAndUpdateConfig,
    isWatching,
  };
}

/**
 * 全局配置监听器实例
 * 在应用启动时自动开始监听
 */
let globalConfigWatcher: UseLibrtcoreConfigWatcherReturn | null = null;

/**
 * 获取或创建全局配置监听器
 */
export function getGlobalConfigWatcher(): UseLibrtcoreConfigWatcherReturn {
  if (!globalConfigWatcher) {
    globalConfigWatcher = useLibrtcoreConfigWatcher({
      enableAutoUpdate: true,
      checkOnStart: true,
      debounceDelay: 500,
    });
  }
  return globalConfigWatcher;
}

/**
 * 启动全局配置监听
 */
export function startGlobalConfigWatching(): void {
  const watcher = getGlobalConfigWatcher();
  watcher.startWatching();
}

/**
 * 停止全局配置监听
 */
export function stopGlobalConfigWatching(): void {
  if (globalConfigWatcher) {
    globalConfigWatcher.stopWatching();
  }
}
