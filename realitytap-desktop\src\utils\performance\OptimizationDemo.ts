/**
 * 性能优化演示
 * 展示如何在实际项目中使用第一阶段的优化功能
 */

import { debounce, immediate, getDebounceManager } from './UnifiedDebounceManager';
import { runPerformanceTest } from './PerformanceTestHelper';
import { logger, LogModule } from '@/utils/logger/logger';

/**
 * 演示统一防抖管理器的使用
 */
export class OptimizationDemo {
  private canvasElement: HTMLCanvasElement | null = null;
  private sliderElement: HTMLInputElement | null = null;

  constructor() {
    this.setupElements();
    this.setupEventListeners();
  }

  /**
   * 设置DOM元素
   */
  private setupElements() {
    // 创建演示用的Canvas和滑块
    this.canvasElement = document.createElement('canvas');
    this.canvasElement.width = 800;
    this.canvasElement.height = 400;
    this.canvasElement.style.border = '1px solid #ccc';
    this.canvasElement.style.cursor = 'crosshair';

    this.sliderElement = document.createElement('input');
    this.sliderElement.type = 'range';
    this.sliderElement.min = '0';
    this.sliderElement.max = '100';
    this.sliderElement.value = '50';
    this.sliderElement.style.width = '300px';

    // 添加到页面（如果在浏览器环境中）
    if (typeof document !== 'undefined') {
      const container = document.createElement('div');
      container.style.padding = '20px';
      
      const title = document.createElement('h3');
      title.textContent = '性能优化演示';
      
      const sliderLabel = document.createElement('label');
      sliderLabel.textContent = '强度调节: ';
      
      const canvasLabel = document.createElement('div');
      canvasLabel.textContent = 'Canvas 绘制区域:';
      canvasLabel.style.marginTop = '20px';
      
      container.appendChild(title);
      container.appendChild(sliderLabel);
      container.appendChild(this.sliderElement);
      container.appendChild(canvasLabel);
      container.appendChild(this.canvasElement);
      
      document.body.appendChild(container);
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners() {
    if (!this.canvasElement || !this.sliderElement) return;

    // 滑块变化事件 - 使用高优先级防抖
    this.sliderElement.addEventListener('input', (event) => {
      const value = parseFloat((event.target as HTMLInputElement).value);
      
      debounce(
        'slider-to-canvas',
        () => this.updateCanvasFromSlider(value),
        8, // 8ms延迟，约120fps响应
        'high' // 用户交互是最高优先级
      );
    });

    // Canvas鼠标事件 - 模拟拖拽操作
    this.canvasElement.addEventListener('mousedown', (event) => {
      this.startDrag(event);
    });

    // 添加性能测试按钮
    this.addTestButton();
  }

  /**
   * 从滑块更新Canvas
   */
  private updateCanvasFromSlider(value: number) {
    if (!this.canvasElement) return;

    const ctx = this.canvasElement.getContext('2d');
    if (!ctx) return;

    // 清除画布
    ctx.clearRect(0, 0, this.canvasElement.width, this.canvasElement.height);

    // 绘制基于滑块值的图形
    const intensity = value / 100;
    const radius = 20 + intensity * 50;
    const alpha = 0.3 + intensity * 0.7;

    ctx.fillStyle = `rgba(0, 150, 255, ${alpha})`;
    ctx.beginPath();
    ctx.arc(
      this.canvasElement.width / 2,
      this.canvasElement.height / 2,
      radius,
      0,
      Math.PI * 2
    );
    ctx.fill();

    // 显示数值
    ctx.fillStyle = '#333';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(
      `强度: ${value.toFixed(1)}`,
      this.canvasElement.width / 2,
      this.canvasElement.height / 2 + 5
    );

    logger.debug(LogModule.PERFORMANCE, `Canvas更新: 强度=${value.toFixed(1)}`);
  }

  /**
   * 开始拖拽操作
   */
  private startDrag(_event: MouseEvent) {
    if (!this.canvasElement || !this.sliderElement) return;

    const canvas = this.canvasElement;
    const rect = canvas.getBoundingClientRect();

    const handleMouseMove = (moveEvent: MouseEvent) => {
      // 计算鼠标在Canvas中的相对位置
      const x = moveEvent.clientX - rect.left;
      
      // 根据位置计算新的强度值
      const intensity = Math.max(0, Math.min(100, (x / canvas.width) * 100));

      // 使用高优先级防抖更新滑块
      debounce(
        'canvas-to-slider',
        () => this.updateSliderFromCanvas(intensity),
        8, // 8ms延迟，确保流畅拖拽
        'high' // 拖拽操作是高优先级
      );

      // 立即更新Canvas预览（不使用防抖）
      immediate('canvas-preview', () => this.updateCanvasFromSlider(intensity));
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      logger.debug(LogModule.DRAG, '拖拽结束');
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    logger.debug(LogModule.DRAG, '开始拖拽');
  }

  /**
   * 从Canvas更新滑块
   */
  private updateSliderFromCanvas(value: number) {
    if (!this.sliderElement) return;

    this.sliderElement.value = value.toString();
    
    // 触发input事件，确保其他监听器能够响应
    const event = new Event('input', { bubbles: true });
    this.sliderElement.dispatchEvent(event);

    logger.debug(LogModule.PERFORMANCE, `滑块更新: 强度=${value.toFixed(1)}`);
  }

  /**
   * 添加性能测试按钮
   */
  private addTestButton() {
    if (typeof document === 'undefined') return;

    const button = document.createElement('button');
    button.textContent = '运行性能测试';
    button.style.marginTop = '20px';
    button.style.padding = '10px 20px';
    button.style.backgroundColor = '#007bff';
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '4px';
    button.style.cursor = 'pointer';

    button.addEventListener('click', async () => {
      button.disabled = true;
      button.textContent = '测试中...';

      try {
        const report = await runPerformanceTest(3000); // 3秒测试
        
        // 显示结果
        const resultDiv = document.createElement('div');
        resultDiv.style.marginTop = '20px';
        resultDiv.style.padding = '10px';
        resultDiv.style.backgroundColor = '#f8f9fa';
        resultDiv.style.border = '1px solid #dee2e6';
        resultDiv.style.borderRadius = '4px';
        resultDiv.style.fontFamily = 'monospace';
        resultDiv.style.fontSize = '12px';
        resultDiv.style.whiteSpace = 'pre-wrap';
        resultDiv.textContent = report;

        // 移除之前的结果
        const existingResult = document.querySelector('.performance-result');
        if (existingResult) {
          existingResult.remove();
        }

        resultDiv.className = 'performance-result';
        button.parentElement?.appendChild(resultDiv);

      } catch (error) {
        logger.error(LogModule.PERFORMANCE, '性能测试失败', error);
        alert('性能测试失败，请查看控制台');
      } finally {
        button.disabled = false;
        button.textContent = '运行性能测试';
      }
    });

    // 添加统计信息按钮
    const statsButton = document.createElement('button');
    statsButton.textContent = '查看防抖统计';
    statsButton.style.marginLeft = '10px';
    statsButton.style.padding = '10px 20px';
    statsButton.style.backgroundColor = '#28a745';
    statsButton.style.color = 'white';
    statsButton.style.border = 'none';
    statsButton.style.borderRadius = '4px';
    statsButton.style.cursor = 'pointer';

    statsButton.addEventListener('click', () => {
      const manager = getDebounceManager();
      const stats = manager.getAllStats();
      const activeCount = manager.getActiveTaskCount();

      logger.info(LogModule.PERFORMANCE, '=== 防抖管理器统计 ===');
      logger.info(LogModule.PERFORMANCE, `活跃任务数: ${activeCount}`);
      logger.info(LogModule.PERFORMANCE, '执行统计', stats);

      alert(`活跃任务数: ${activeCount}\n详细统计请查看控制台`);
    });

    if (this.canvasElement?.parentElement) {
      const buttonContainer = document.createElement('div');
      buttonContainer.style.marginTop = '20px';
      buttonContainer.appendChild(button);
      buttonContainer.appendChild(statsButton);
      this.canvasElement.parentElement.appendChild(buttonContainer);
    }
  }

  /**
   * 模拟高频操作测试
   */
  public simulateHighFrequencyOperations() {
    logger.info(LogModule.PERFORMANCE, '开始模拟高频操作...');

    // 模拟快速滑块调整
    for (let i = 0; i < 50; i++) {
      setTimeout(() => {
        const value = Math.random() * 100;
        debounce(
          'high-freq-slider',
          () => this.updateCanvasFromSlider(value),
          8,
          'high'
        );
      }, i * 10); // 每10ms一次操作
    }

    // 模拟快速Canvas拖拽
    for (let i = 0; i < 30; i++) {
      setTimeout(() => {
        const value = Math.random() * 100;
        debounce(
          'high-freq-canvas',
          () => this.updateSliderFromCanvas(value),
          8,
          'high'
        );
      }, i * 15); // 每15ms一次操作
    }

    logger.info(LogModule.PERFORMANCE, '高频操作模拟完成，请观察性能表现');
  }

  /**
   * 清理资源
   */
  public cleanup() {
    const manager = getDebounceManager();
    manager.cancelAll();
    
    if (this.canvasElement?.parentElement) {
      this.canvasElement.parentElement.remove();
    }
  }
}

// 导出便捷函数
export function createOptimizationDemo(): OptimizationDemo {
  return new OptimizationDemo();
}

export function runHighFrequencyTest(): void {
  const demo = createOptimizationDemo();
  setTimeout(() => {
    demo.simulateHighFrequencyOperations();
  }, 1000);
}
