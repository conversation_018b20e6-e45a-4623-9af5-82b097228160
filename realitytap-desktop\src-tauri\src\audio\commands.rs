// Audio-related Tauri commands
use crate::{
    audio::{analysis::*, formats::*},
    error::{Error, Result},
    models::audio::AudioInfo,
    project::{AUDIO_DIR_NAME, io::VIDEO_DIR_NAME},
};
use log;
use std::fs;
use std::path::PathBuf;
use tauri;

#[tauri::command]
pub async fn get_audio_info(
    project_dir_path: String,
    audio_relative_path: String,
) -> Result<Option<AudioInfo>> {
    log::info!("get_audio_info called: project_dir_path={}, audio_relative_path={}", project_dir_path, audio_relative_path);

    // 智能路径解析：根据文件扩展名判断是音频文件还是视频文件
    let file_ext = PathBuf::from(&audio_relative_path)
        .extension()
        .and_then(|e| e.to_str())
        .unwrap_or("")
        .to_lowercase();

    let abs_path = if file_ext == "mp4" {
        // 视频文件在 video 目录下
        let full_video_relative_path = PathBuf::from("video").join(&audio_relative_path);
        PathBuf::from(&project_dir_path).join(&full_video_relative_path)
    } else {
        // 音频文件在 audio 目录下
        let full_audio_relative_path = PathBuf::from(AUDIO_DIR_NAME).join(&audio_relative_path);
        PathBuf::from(&project_dir_path).join(&full_audio_relative_path)
    };

    log::info!("解析后的文件路径: {}", abs_path.display());
    
    if !abs_path.exists() {
        log::error!("音频文件不存在: {}", abs_path.display());
        return Err(Error::NotFound(format!(
            "音频文件不存在: {}",
            abs_path.display()
        )));
    }
    
    let ext = abs_path
        .extension()
        .and_then(|e| e.to_str())
        .unwrap_or("")
        .to_lowercase();
    log::info!("音频文件扩展名: {}", ext);
    
    let (_, audio_info) = match ext.as_str() {
        "wav" => {
            log::info!("开始解析 WAV 文件: {}", abs_path.display());
            let result = extract_info_from_wav(&abs_path)?;
            log::info!("WAV 文件解析结果: audio_info={:?}", result.1);
            result
        },
        "mp3" => {
            log::info!("开始解析 MP3 文件: {}", abs_path.display());
            let result = extract_info_from_mp3(&abs_path)?;
            log::info!("MP3 文件解析结果: audio_info={:?}", result.1);
            result
        },
        "mp4" => {
            log::info!("开始解析 MP4 文件: {}", abs_path.display());
            let result = extract_info_from_mp4(&abs_path)?;
            log::info!("MP4 文件解析结果: audio_info={:?}", result.1);
            result
        },
        // 未来可扩展：如需支持更多格式，在此添加分支
        _ => {
            log::warn!("暂不支持的音频格式: {}", ext);
            (vec![], None)
        }
    };

    if audio_info.is_none() {
        log::warn!("音频文件解析失败，返回 None: {}", abs_path.display());
    }

    log::info!("get_audio_info 返回: audio_info={:?}", audio_info);
    Ok(audio_info)
}

#[tauri::command]
pub async fn get_audio_info_from_file(file_path: String) -> Result<Option<AudioInfo>> {
    log::info!("get_audio_info_from_file called: file_path={}", file_path);
    let abs_path = PathBuf::from(&file_path);

    if !abs_path.exists() {
        log::error!("音频文件不存在: {}", abs_path.display());
        return Err(Error::NotFound(format!(
            "音频文件不存在: {}",
            abs_path.display()
        )));
    }

    let ext = abs_path
        .extension()
        .and_then(|e| e.to_str())
        .unwrap_or("")
        .to_lowercase();

    log::info!("音频文件扩展名: {}", ext);
    let (_, audio_info) = match ext.as_str() {
        "wav" => {
            log::info!("开始解析 WAV 文件: {}", abs_path.display());
            let result = extract_info_from_wav(&abs_path)?;
            log::info!("WAV 文件解析结果: audio_info={:?}", result.1);
            result
        },
        "mp3" => {
            log::info!("开始解析 MP3 文件: {}", abs_path.display());
            let result = extract_info_from_mp3(&abs_path)?;
            log::info!("MP3 文件解析结果: audio_info={:?}", result.1);
            result
        },
        "mp4" => {
            log::info!("开始解析 MP4 文件: {}", abs_path.display());
            let result = extract_info_from_mp4(&abs_path)?;
            log::info!("MP4 文件解析结果: audio_info={:?}", result.1);
            result
        },
        // 未来可扩展：如需支持更多格式，在此添加分支
        _ => {
            log::warn!("暂不支持的音频格式: {}", ext);
            (vec![], None)
        }
    };

    if audio_info.is_none() {
        log::warn!("音频文件解析失败，返回 None: {}", abs_path.display());
    }

    log::info!("get_audio_info_from_file 返回: audio_info={:?}", audio_info);
    Ok(audio_info)
}

#[tauri::command]
pub async fn copy_audio_file_to_project(
    project_dir_path: String,
    source_file_path: String,
    target_relative_path: String,
) -> Result<()> {
    let proj_dir_path = PathBuf::from(&project_dir_path);
    let source_path = PathBuf::from(&source_file_path);

    if !source_path.exists() {
        return Err(Error::NotFound(format!(
            "Source audio file not found: {:?}",
            source_path
        )));
    }

    let audio_dir = proj_dir_path.join(AUDIO_DIR_NAME);
    let target_path = audio_dir.join(&target_relative_path);

    if let Some(parent) = target_path.parent() {
        fs::create_dir_all(parent)
            .map_err(|e| Error::Io(format!("Failed to create audio parent dir: {}", e)))?;
    }

    fs::copy(&source_path, &target_path)
        .map_err(|e| Error::Io(format!("Failed to copy audio file: {}", e)))?;

    Ok(())
}

#[tauri::command]
pub async fn copy_video_file_to_project(
    project_dir_path: String,
    source_file_path: String,
    target_relative_path: String,
) -> Result<()> {
    let proj_dir_path = PathBuf::from(&project_dir_path);
    let source_path = PathBuf::from(&source_file_path);

    if !source_path.exists() {
        return Err(Error::NotFound(format!(
            "Source video file not found: {:?}",
            source_path
        )));
    }

    let video_dir = proj_dir_path.join(VIDEO_DIR_NAME);
    let target_path = video_dir.join(&target_relative_path);

    if let Some(parent) = target_path.parent() {
        fs::create_dir_all(parent)
            .map_err(|e| Error::Io(format!("Failed to create video parent dir: {}", e)))?;
    }

    fs::copy(&source_path, &target_path)
        .map_err(|e| Error::Io(format!("Failed to copy video file: {}", e)))?;

    Ok(())
}

#[tauri::command]
pub async fn get_video_audio_info(
    project_dir_path: String,
    video_relative_path: String,
) -> Result<Option<AudioInfo>> {
    log::info!("get_video_audio_info called: project_dir_path={}, video_relative_path={}", project_dir_path, video_relative_path);
    let full_video_relative_path = PathBuf::from(VIDEO_DIR_NAME).join(&video_relative_path);
    let abs_path = PathBuf::from(&project_dir_path).join(&full_video_relative_path);
    log::info!("Resolved video abs_path: {}", abs_path.display());

    if !abs_path.exists() {
        log::error!("视频文件不存在: {}", abs_path.display());
        return Err(Error::NotFound(format!(
            "视频文件不存在: {}",
            abs_path.display()
        )));
    }

    let ext = abs_path
        .extension()
        .and_then(|e| e.to_str())
        .unwrap_or("")
        .to_lowercase();
    log::info!("视频文件扩展名: {}", ext);

    let (_, audio_info) = match ext.as_str() {
        "mp4" => extract_info_from_mp4(&abs_path)?,
        // 未来可扩展：如需支持更多视频格式，在此添加分支
        _ => {
            log::warn!("暂不支持的视频格式: {}", ext);
            (vec![], None)
        }
    };
    log::info!("get_video_audio_info 返回: audio_info={:?}", audio_info);
    Ok(audio_info)
}

#[tauri::command]
pub async fn get_audio_amplitude_data(
    project_dir_path: String,
    audio_relative_path: String,
    max_samples: Option<usize>,
) -> Result<AmplitudeData> {
    log::info!("get_audio_amplitude_data called: project_dir_path={}, audio_relative_path={}, max_samples={:?}",
               project_dir_path, audio_relative_path, max_samples);

    // 智能路径解析：根据文件扩展名判断是音频文件还是视频文件
    let file_ext = PathBuf::from(&audio_relative_path)
        .extension()
        .and_then(|e| e.to_str())
        .unwrap_or("")
        .to_lowercase();

    let abs_path = if file_ext == "mp4" {
        // 视频文件在 video 目录下
        let full_video_relative_path = PathBuf::from("video").join(&audio_relative_path);
        PathBuf::from(&project_dir_path).join(&full_video_relative_path)
    } else {
        // 音频文件在 audio 目录下
        let full_audio_relative_path = PathBuf::from(AUDIO_DIR_NAME).join(&audio_relative_path);
        PathBuf::from(&project_dir_path).join(&full_audio_relative_path)
    };

    log::info!("解析后的文件路径: {}", abs_path.display());

    if !abs_path.exists() {
        log::error!("音频文件不存在: {}", abs_path.display());
        return Err(Error::NotFound(format!(
            "音频文件不存在: {}",
            abs_path.display()
        )));
    }

    let (mut samples, audio_info) = extract_audio_data(&abs_path, true)?;

    // 智能计算最大样本数
    let effective_max_samples = if let Some(max_count) = max_samples {
        max_count
    } else {
        // 根据音频时长智能计算采样点数
        calculate_optimal_sample_count(audio_info.duration_ms)
    };

    // 如果样本数超过限制，进行智能下采样
    if samples.len() > effective_max_samples {
        let original_count = samples.len();
        samples = downsample_audio_with_peak_preservation(samples, effective_max_samples);
        log::info!("音频下采样: {} -> {} 个采样点", original_count, samples.len());
    }

    let amplitude_data = create_amplitude_data(&samples, audio_info.sample_rate, audio_info.duration_ms);

    log::info!("get_audio_amplitude_data 返回: samples.len()={}", amplitude_data.samples.len());
    Ok(amplitude_data)
}

#[tauri::command]
pub async fn get_audio_frequency_data(
    project_dir_path: String,
    audio_relative_path: String,
    fft_size: Option<usize>,
) -> Result<FrequencyData> {
    log::info!("get_audio_frequency_data called: project_dir_path={}, audio_relative_path={}, fft_size={:?}",
               project_dir_path, audio_relative_path, fft_size);

    let full_audio_relative_path = PathBuf::from(AUDIO_DIR_NAME).join(&audio_relative_path);
    let abs_path = PathBuf::from(&project_dir_path).join(&full_audio_relative_path);

    if !abs_path.exists() {
        log::error!("音频文件不存在: {}", abs_path.display());
        return Err(Error::NotFound(format!(
            "音频文件不存在: {}",
            abs_path.display()
        )));
    }

    let (samples, audio_info) = extract_audio_data(&abs_path, true)?;
    let frequency_data = compute_frequency_spectrum(&samples, audio_info.sample_rate, fft_size);

    log::info!("get_audio_frequency_data 返回: frequencies.len()={}", frequency_data.frequencies.len());
    Ok(frequency_data)
}

#[tauri::command]
pub async fn get_audio_analysis_data(
    project_dir_path: String,
    audio_relative_path: String,
    max_amplitude_samples: Option<usize>,
    fft_size: Option<usize>,
) -> Result<AudioAnalysisData> {
    log::info!("get_audio_analysis_data called: project_dir_path={}, audio_relative_path={}, max_amplitude_samples={:?}, fft_size={:?}",
               project_dir_path, audio_relative_path, max_amplitude_samples, fft_size);

    let full_audio_relative_path = PathBuf::from(AUDIO_DIR_NAME).join(&audio_relative_path);
    let abs_path = PathBuf::from(&project_dir_path).join(&full_audio_relative_path);

    if !abs_path.exists() {
        log::error!("音频文件不存在: {}", abs_path.display());
        return Err(Error::NotFound(format!(
            "音频文件不存在: {}",
            abs_path.display()
        )));
    }

    let (samples, audio_info) = extract_audio_data(&abs_path, true)?;
    let analysis_data = create_audio_analysis(&samples, audio_info, max_amplitude_samples, fft_size);

    log::info!("get_audio_analysis_data 返回: amplitude_samples.len()={}, frequencies.len()={}",
               analysis_data.amplitude_data.samples.len(),
               analysis_data.frequency_data.frequencies.len());
    Ok(analysis_data)
}
