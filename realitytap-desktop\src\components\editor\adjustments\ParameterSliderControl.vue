<template>
  <div class="slider-control">
    <div class="slider-header">
      <label class="slider-label" v-if="label">{{ label }}</label>
    </div>

    <div class="slider-container">
      <div class="slider-input-layout">
        <n-slider
          ref="sliderRef"
          class="custom-slider"
          v-model:value="internalValue"
          :min="min"
          :max="max"
          :step="step"
          :tooltip="false"
          @update:value="onSliderValueChange"
        />
        <n-input-number
          v-model:value="internalValue"
          :min="min"
          :max="max"
          :step="step"
          :show-button="false"
          size="small"
          class="value-input"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch, onMounted, onUnmounted } from "vue";
import { NSlider, NInputNumber } from "naive-ui";
import { debounce } from "@/utils/performance/UnifiedDebounceManager";
import { logger, LogModule } from "@/utils/logger/logger";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  value: {
    type: Number,
    required: true,
  },
  min: {
    type: Number,
    default: 0,
  },
  max: {
    type: Number,
    default: 100,
  },
  step: {
    type: Number,
    default: 1,
  },
  showValue: {
    type: Boolean,
    default: true,
  },
  precision: {
    type: Number,
    default: null, // null 表示不限制精度
  },
});

const emit = defineEmits<{
  (e: "update:value", value: number): void;
  (e: "slider-start", value: number): void;
  (e: "slider-end", value: number): void;
}>();

// 数值精度格式化函数
const formatValue = (value: number): number => {
  if (props.precision === null) return value;
  return Number(value.toFixed(props.precision));
};

// 内部状态，用于共享滑块和数字输入框的值
const internalValue = ref(formatValue(props.value));

// 滑块引用
const sliderRef = ref<InstanceType<typeof NSlider>>();

// 滑块交互状态
const isSliderInteracting = ref(false);
const sliderStartValue = ref(0);
const boundElement = ref<HTMLElement | null>(null);

// 生成唯一的防抖键，基于组件标签和值类型
const debounceKey = `slider-${props.label || 'unnamed'}-${Date.now()}`;

// 滑块开始交互事件
const onSliderStart = (event: Event) => {
  logger.debug(LogModule.GENERAL, '[SliderControl] 滑块开始交互', {
    label: props.label,
    value: internalValue.value,
    event: event.type
  });

  isSliderInteracting.value = true;
  sliderStartValue.value = internalValue.value;
  emit("slider-start", formatValue(internalValue.value));
};

// 滑块结束交互事件
const onSliderEnd = (event: Event) => {
  logger.debug(LogModule.GENERAL, '[SliderControl] 滑块结束交互', {
    label: props.label,
    startValue: sliderStartValue.value,
    endValue: internalValue.value,
    event: event.type,
    isInteracting: isSliderInteracting.value
  });

  if (isSliderInteracting.value) {
    isSliderInteracting.value = false;
    // 只触发slider-end事件，不重复触发update:value
    // update:value已经在拖动过程中实时触发了
    emit("slider-end", formatValue(internalValue.value));
  }
};

// 滑块值变化事件（恢复实时更新，保持Canvas响应性）
const onSliderValueChange = (newValue: number) => {
  if (isSliderInteracting.value) {
    // 交互过程中立即更新，确保Canvas实时响应
    const formattedValue = formatValue(newValue);
    emit("update:value", formattedValue);
  } else {
    // 非交互状态（如程序设置值）使用防抖
    debounce(
      debounceKey,
      () => {
        const formattedValue = formatValue(newValue);
        emit("update:value", formattedValue);
      },
      8,
      'high'
    );
  }
};

// 监听内部值变化
watch(internalValue, onSliderValueChange);

// 监听props.value的变化，同步到内部状态
watch(
  () => props.value,
  (newValue) => {
    internalValue.value = formatValue(newValue);
  }
);

// 绑定DOM事件
onMounted(() => {
  // 使用重试机制确保所有滑块都能正确绑定
  const bindEvents = (attempt = 1, maxAttempts = 5) => {
    if (sliderRef.value) {
      // 获取滑块的DOM元素
      const sliderElement = sliderRef.value.$el;
      if (sliderElement) {
        // 查找滑块手柄元素，尝试多种选择器
        let handleElement = sliderElement.querySelector('.n-slider-handle');
        if (!handleElement) {
          handleElement = sliderElement.querySelector('.n-slider__handle');
        }
        if (!handleElement) {
          handleElement = sliderElement.querySelector('[role="slider"]');
        }
        if (!handleElement) {
          // 如果还是找不到，直接在整个滑块元素上绑定
          handleElement = sliderElement;
        }

        if (handleElement) {
          logger.debug(LogModule.GENERAL, '[SliderControl] 绑定滑块事件', {
            label: props.label,
            elementType: handleElement.tagName,
            attempt: attempt
          });

          // 绑定鼠标事件
          handleElement.addEventListener('mousedown', onSliderStart, { passive: false });
          document.addEventListener('mouseup', onSliderEnd, { passive: false });

          // 绑定触摸事件
          handleElement.addEventListener('touchstart', onSliderStart, { passive: false });
          document.addEventListener('touchend', onSliderEnd, { passive: false });

          // 存储元素引用用于清理
          boundElement.value = handleElement;
          (sliderRef.value as any)._boundElement = handleElement;
          return; // 绑定成功，退出
        } else {
          logger.warn(LogModule.GENERAL, '[SliderControl] 未找到滑块手柄元素', {
            label: props.label,
            attempt: attempt
          });
        }
      } else {
        logger.warn(LogModule.GENERAL, '[SliderControl] 未找到滑块DOM元素', {
          label: props.label,
          attempt: attempt
        });
      }
    }

    // 如果绑定失败且还有重试次数，则延迟重试
    if (attempt < maxAttempts) {
      setTimeout(() => bindEvents(attempt + 1, maxAttempts), 100 * attempt); // 递增延迟
    } else {
      logger.error(LogModule.GENERAL, '[SliderControl] 事件绑定最终失败', { label: props.label });
    }
  };

  // 开始绑定，初始延迟200ms
  setTimeout(() => bindEvents(), 200);
});

// 清理事件监听器
onUnmounted(() => {
  // 清理全局事件监听器
  document.removeEventListener('mouseup', onSliderEnd);
  document.removeEventListener('touchend', onSliderEnd);

  // 清理元素特定的事件监听器
  if (boundElement.value) {
    boundElement.value.removeEventListener('mousedown', onSliderStart);
    boundElement.value.removeEventListener('touchstart', onSliderStart);
  }
});
</script>

<style scoped>
.slider-control {
  margin-bottom: 1rem;
  width: 100%;
}

.slider-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  align-items: center;
}

.slider-label {
  font-size: 0.9rem;
  color: #e6e6e6;
}

.slider-container {
  position: relative;
}

.slider-input-layout {
  display: flex;
  align-items: center;
  gap: 12px;
}

.custom-slider {
  flex: 1;
  min-width: 0;
}

.value-input {
  width: 50px;
  flex-shrink: 0;
}

/* 调整Naive UI滑块样式以更匹配原设计 */
:deep(.n-slider-rail) {
  height: 4px !important;
}

:deep(.n-slider-handle) {
  width: 14px !important;
  height: 14px !important;
}

:deep(.n-slider-handle:hover),
:deep(.n-slider-handle:active) {
  transform: scale(1.1);
}

:deep(.value-input .n-input) {
  border-radius: 12px;
  background-color: #333;
  border: 1px solid #444;
}

:deep(.value-input .n-input__input-el) {
  text-align: center;
  font-size: 12px;
  height: 20px;
  color: #e6e6e6;
}

:deep(.value-input .n-input-wrapper) {
  padding: 0;
}

:deep(.value-input .n-input__border, .value-input .n-input__state-border) {
  border-color: #444;
}

:deep(.value-input:hover .n-input__border) {
  border-color: #4785eb;
}

:deep(.value-input .n-base-selection-tags) {
  justify-content: flex-end;
}
</style>
