/**
 * 客户端专用类型定义
 */

import type { 
  DownloadStatus, 
  UpdateStatus, 
  OTAErrorCode, 
  NotificationType,
  ChecksumAlgorithm 
} from '../constants/ota';

/**
 * 客户端配置
 */
export interface ClientConfig {
  /** 服务器基础URL */
  serverBaseURL: string;
  /** 更新检查间隔（毫秒） */
  checkInterval: number;
  /** 是否自动下载更新 */
  autoDownload: boolean;
  /** 是否自动安装更新 */
  autoInstall: boolean;
  /** 发布渠道 */
  channel: 'stable' | 'beta' | 'alpha';
  /** 是否启用通知 */
  enableNotifications: boolean;
  /** 是否启用后台检查 */
  enableBackgroundCheck: boolean;
  /** 最大重试次数 */
  maxRetries: number;
  /** 请求超时时间 */
  timeout: number;
  /** 下载目录 */
  downloadDir?: string;
  /** 代理设置 */
  proxy?: ProxyConfig;
}

/**
 * 代理配置
 */
export interface ProxyConfig {
  /** 代理类型 */
  type: 'http' | 'https' | 'socks5';
  /** 代理主机 */
  host: string;
  /** 代理端口 */
  port: number;
  /** 用户名（可选） */
  username?: string;
  /** 密码（可选） */
  password?: string;
}

/**
 * 更新会话信息
 */
export interface UpdateSession {
  /** 会话ID */
  sessionId: string;
  /** 当前版本 */
  currentVersion: string;
  /** 目标版本 */
  targetVersion: string;
  /** 更新状态 */
  status: UpdateStatus;
  /** 开始时间 */
  startTime: Date;
  /** 结束时间 */
  endTime?: Date;
  /** 下载信息 */
  download?: DownloadSession;
  /** 错误信息 */
  error?: UpdateError;
}

/**
 * 下载会话信息
 */
export interface DownloadSession {
  /** 下载URL */
  url: string;
  /** 本地文件路径 */
  filePath: string;
  /** 文件大小 */
  totalSize: number;
  /** 已下载大小 */
  downloadedSize: number;
  /** 下载状态 */
  status: DownloadStatus;
  /** 下载速度（字节/秒） */
  speed: number;
  /** 预计剩余时间（秒） */
  estimatedTimeRemaining: number;
  /** 开始时间 */
  startTime: Date;
  /** 暂停时间 */
  pauseTime?: Date;
  /** 恢复时间 */
  resumeTime?: Date;
  /** 校验和 */
  checksum?: string;
  /** 校验算法 */
  checksumAlgorithm?: ChecksumAlgorithm;
}

/**
 * 更新错误信息
 */
export interface UpdateError {
  /** 错误代码 */
  code: OTAErrorCode;
  /** 错误消息 */
  message: string;
  /** 详细信息 */
  details?: string;
  /** 错误堆栈 */
  stack?: string;
  /** 发生时间 */
  timestamp: Date;
  /** 是否可重试 */
  retryable: boolean;
}

/**
 * 系统信息
 */
export interface SystemInfo {
  /** 操作系统 */
  platform: string;
  /** 系统架构 */
  architecture: string;
  /** 系统版本 */
  osVersion: string;
  /** 可用磁盘空间（字节） */
  availableSpace: number;
  /** 总磁盘空间（字节） */
  totalSpace: number;
  /** 内存信息 */
  memory: {
    total: number;
    available: number;
  };
  /** 网络状态 */
  networkStatus: 'online' | 'offline' | 'limited';
}

/**
 * 更新通知
 */
export interface UpdateNotification {
  /** 通知类型 */
  type: NotificationType;
  /** 通知标题 */
  title: string;
  /** 通知内容 */
  message: string;
  /** 通知时间 */
  timestamp: Date;
  /** 是否需要用户操作 */
  requiresAction: boolean;
  /** 操作按钮 */
  actions?: NotificationAction[];
  /** 自动关闭时间（毫秒） */
  autoClose?: number;
}

/**
 * 通知操作
 */
export interface NotificationAction {
  /** 操作ID */
  id: string;
  /** 操作标签 */
  label: string;
  /** 操作类型 */
  type: 'primary' | 'secondary' | 'danger';
  /** 操作回调 */
  callback: () => void | Promise<void>;
}

/**
 * 更新历史记录
 */
export interface UpdateHistory {
  /** 记录ID */
  id: string;
  /** 源版本 */
  fromVersion: string;
  /** 目标版本 */
  toVersion: string;
  /** 更新类型 */
  updateType: 'major' | 'minor' | 'patch';
  /** 更新状态 */
  status: 'success' | 'failed' | 'cancelled';
  /** 开始时间 */
  startTime: Date;
  /** 结束时间 */
  endTime?: Date;
  /** 耗时（毫秒） */
  duration?: number;
  /** 文件大小 */
  fileSize?: number;
  /** 错误信息 */
  error?: string;
  /** 发布说明 */
  releaseNotes?: string;
}

/**
 * 用户偏好设置
 */
export interface UserPreferences {
  /** 是否启用自动检查 */
  autoCheck: boolean;
  /** 检查频率 */
  checkFrequency: 'hourly' | 'daily' | 'weekly' | 'manual';
  /** 是否在有网络时自动下载 */
  autoDownloadOnWifi: boolean;
  /** 是否显示预发布版本 */
  showPrerelease: boolean;
  /** 通知偏好 */
  notifications: {
    updateAvailable: boolean;
    downloadComplete: boolean;
    installComplete: boolean;
    errors: boolean;
  };
  /** 下载偏好 */
  download: {
    maxConcurrentDownloads: number;
    pauseOnBattery: boolean;
    limitBandwidth: boolean;
    maxBandwidth?: number; // KB/s
  };
}

/**
 * 更新进度回调
 */
export interface ProgressCallback {
  (progress: {
    /** 当前阶段 */
    stage: 'checking' | 'downloading' | 'verifying' | 'installing';
    /** 进度百分比 (0-100) */
    percentage: number;
    /** 已完成大小 */
    completed: number;
    /** 总大小 */
    total: number;
    /** 当前速度 */
    speed?: number;
    /** 预计剩余时间 */
    eta?: number;
    /** 状态消息 */
    message?: string;
  }): void;
}

/**
 * 更新事件
 */
export interface UpdateEvent {
  /** 事件类型 */
  type: 'check-start' | 'check-complete' | 'download-start' | 'download-progress' | 
        'download-complete' | 'install-start' | 'install-complete' | 'error' | 'cancelled';
  /** 事件数据 */
  data?: any;
  /** 事件时间 */
  timestamp: Date;
}

/**
 * 更新事件监听器
 */
export type UpdateEventListener = (event: UpdateEvent) => void;

/**
 * 文件验证结果
 */
export interface FileVerificationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 计算的校验和 */
  actualChecksum: string;
  /** 期望的校验和 */
  expectedChecksum: string;
  /** 文件大小 */
  fileSize: number;
  /** 验证耗时（毫秒） */
  verificationTime: number;
}

/**
 * 安装选项
 */
export interface InstallOptions {
  /** 是否静默安装 */
  silent: boolean;
  /** 是否强制安装 */
  force: boolean;
  /** 安装后是否重启应用 */
  restartApp: boolean;
  /** 安装前是否备份 */
  backup: boolean;
  /** 自定义安装参数 */
  customArgs?: string[];
}
