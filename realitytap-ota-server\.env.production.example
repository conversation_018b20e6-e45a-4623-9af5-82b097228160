# Production Environment Configuration Example
# Copy this file to .env.production and modify values as needed

# Server Configuration
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Database Configuration
DB_ENABLED=true
DB_TYPE=sqlite
DB_PATH=./storage/database/ota.db
DB_BACKUP_PATH=./storage/backup/database
DB_MAX_CONNECTIONS=10
DB_BUSY_TIMEOUT=30000
DB_ENABLE_WAL=true

# Storage Configuration
STORAGE_PATH=./storage
RELEASES_PATH=./storage/releases
METADATA_PATH=./storage/metadata
LOGS_PATH=./storage/logs

# Security Configuration
CORS_ORIGIN=https://your-domain.com

# 通用速率限制配置（向后兼容）
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 管理API速率限制配置（生产环境更严格）
ADMIN_RATE_LIMIT_WINDOW_MS=900000
ADMIN_RATE_LIMIT_MAX_REQUESTS=300

# 公共API速率限制配置（生产环境更严格）
PUBLIC_RATE_LIMIT_WINDOW_MS=900000
PUBLIC_RATE_LIMIT_MAX_REQUESTS=100

# 速率限制IP白名单（生产环境可配置管理员IP）
# RATE_LIMIT_WHITELIST_IPS=*************,*********

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h

# Logging Configuration
LOG_LEVEL=warn
LOG_FORMAT=json
LOG_MAX_SIZE=10m
LOG_MAX_FILES=7d
ENABLE_ACCESS_LOG=false
ENABLE_AUTH_FAILURE_DEDUP=true
LOG_DEDUP_CACHE_DURATION=60000

# File Upload Configuration
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=.exe,.dmg,.deb,.rpm,.tar.gz,.zip

# Download Configuration
ENABLE_RANGE_REQUESTS=true
ENABLE_COMPRESSION=true
DOWNLOAD_TIMEOUT=300000

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000
