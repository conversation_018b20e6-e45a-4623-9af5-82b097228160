import { fileService } from '@/services/file.service';
import { ErrorResponse, SuccessResponse } from '@/types/server.types';
import { logger } from '@/utils/logger.util';
import { getRequestIPInfo, logOperationIP } from '@/middleware/ip-logging.middleware';
import { NextFunction, Request, Response, Router } from 'express';

const router: Router = Router();

/**
 * 文件下载 API
 * GET /api/v1/download/:filename
 */
router.get('/:filename', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { filename: rawFilename } = req.params;

    // 验证原始文件名参数
    if (!rawFilename) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'MISSING_FILENAME',
          message: 'Filename parameter is required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }

    // 解码URL编码的文件名
    const filename = decodeURIComponent(rawFilename);

    // 验证文件名
    if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_FILENAME',
          message: 'Invalid filename',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }

    // 获取客户端IP信息
    const ipInfo = getRequestIPInfo(req);

    // 记录文件下载操作
    logOperationIP(req, 'FILE_DOWNLOAD', {
      filename,
      decodedFilename: decodeURIComponent(filename),
      range: req.headers.range,
      referer: req.get('Referer'),
    });

    // 记录下载请求
    logger.info('版本文件下载请求', {
      filename,
      decodedFilename: decodeURIComponent(filename),
      userAgent: req.get('User-Agent'),
      clientIP: ipInfo.ip,
      isPrivateIP: ipInfo.isPrivate,
      range: req.headers.range,
      referer: req.get('Referer'),
      module: 'version_management',
      operation: 'file_download',
    });

    // 检查文件是否存在
    const fileExists = await fileService.fileExists(filename);
    if (!fileExists) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'FILE_NOT_FOUND',
          message: 'File not found',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(404).json(response);
      return;
    }

    // 获取文件信息用于统计
    const fileInfo = await fileService.getFileInfo(filename);
    const downloadStartTime = Date.now();

    // 流式传输文件
    await fileService.streamFile(filename, req, res);

    // 记录下载统计（异步，不阻塞响应）
    setImmediate(async () => {
      try {
        const { getStatsService } = await import('@/services/service-factory');
        const { StatsUtil } = await import('@/utils/stats.util');

        const statsService = getStatsService();

        const downloadDuration = Date.now() - downloadStartTime;
        const metadata = StatsUtil.parseFileMetadata(filename);

        await statsService.recordDownload({
          filename,
          version: metadata.version,
          platform: metadata.platform,
          architecture: metadata.architecture,
          channel: metadata.channel,
          clientIP: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          fileSize: fileInfo.size,
          downloadDuration,
        });
      } catch (error) {
        logger.warn('Failed to record download stats', { filename, error });
      }
    });
  } catch (error) {
    const errorIPInfo = getRequestIPInfo(req);
    logger.error('Download failed', {
      filename: req.params.filename,
      error,
      userAgent: req.get('User-Agent'),
      clientIP: errorIPInfo.ip,
    });
    next(error);
  }
});

/**
 * 获取文件信息
 * HEAD /api/v1/download/:filename
 */
router.head('/:filename', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { filename: rawFilename } = req.params;

    // 验证原始文件名参数
    if (!rawFilename) {
      res.status(400).end();
      return;
    }

    // 解码URL编码的文件名
    const filename = decodeURIComponent(rawFilename);

    // 验证文件名
    if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      res.status(400).end();
      return;
    }

    // 获取文件信息
    const fileInfo = await fileService.getFileInfo(filename);

    if (!fileInfo.exists) {
      res.status(404).end();
      return;
    }

    // 设置响应头
    res.setHeader('Content-Length', fileInfo.size.toString());
    res.setHeader('Last-Modified', fileInfo.mtime.toUTCString());
    res.setHeader('Accept-Ranges', 'bytes');
    res.setHeader('Content-Type', 'application/octet-stream');
    res.status(200).end();
  } catch (error) {
    logger.error('Failed to get file info', {
      filename: req.params.filename,
      error,
    });
    next(error);
  }
});

/**
 * 获取文件列表
 * GET /api/v1/download/list/:channel?
 */
router.get('/list/:channel?', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { channel } = req.params;

    // 验证渠道参数
    if (channel && !['stable', 'beta', 'alpha'].includes(channel)) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_CHANNEL',
          message: 'Invalid channel specified',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }

    // 获取文件列表
    const files = await fileService.listReleaseFiles(channel);

    // 获取每个文件的详细信息
    const fileDetails = await Promise.all(
      files.map(async filename => {
        try {
          const info = await fileService.getFileInfo(filename);
          return {
            filename,
            size: info.size,
            lastModified: info.mtime.toISOString(),
            downloadUrl: `/api/v1/download/${encodeURIComponent(filename)}`,
          };
        } catch (error) {
          logger.warn('Failed to get file info for listing', { filename, error });
          return {
            filename,
            size: 0,
            lastModified: new Date().toISOString(),
            downloadUrl: `/api/v1/download/${encodeURIComponent(filename)}`,
            error: 'Failed to get file info',
          };
        }
      }),
    );

    const response: SuccessResponse = {
      success: true,
      data: {
        channel: channel || 'all',
        files: fileDetails,
        total: fileDetails.length,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error) {
    logger.error('Failed to list files', { channel: req.params.channel, error });
    next(error);
  }
});

/**
 * 验证文件完整性
 * POST /api/v1/download/verify/:filename
 */
router.post('/verify/:filename', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { filename } = req.params;
    const { checksum } = req.body;

    // 验证参数
    if (!filename || !checksum) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'MISSING_PARAMETERS',
          message: 'Filename and checksum are required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }

    // 验证文件完整性
    const isValid = await fileService.verifyFileIntegrity(filename, checksum);

    const response: SuccessResponse = {
      success: true,
      data: {
        filename,
        checksum,
        isValid,
        message: isValid ? 'File integrity verified' : 'File integrity check failed',
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error) {
    logger.error('File verification failed', {
      filename: req.params.filename,
      checksum: req.body.checksum,
      error,
    });
    next(error);
  }
});

/**
 * 获取文件校验和
 * GET /api/v1/download/checksum/:filename
 */
router.get('/checksum/:filename', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { filename: rawFilename } = req.params;

    // 验证原始文件名参数
    if (!rawFilename) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'MISSING_FILENAME',
          message: 'Filename parameter is required',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }

    // 解码URL编码的文件名
    const filename = decodeURIComponent(rawFilename);

    // 验证文件名
    if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'INVALID_FILENAME',
          message: 'Invalid filename',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(400).json(response);
      return;
    }

    // 检查文件是否存在
    const fileExists = await fileService.fileExists(filename);
    if (!fileExists) {
      const response: ErrorResponse = {
        success: false,
        error: {
          code: 'FILE_NOT_FOUND',
          message: 'File not found',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      };
      res.status(404).json(response);
      return;
    }

    // 计算校验和
    const checksum = await fileService.calculateFileChecksum(filename);

    const response: SuccessResponse = {
      success: true,
      data: {
        filename,
        checksum,
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };

    res.json(response);
  } catch (error) {
    logger.error('Failed to calculate checksum', {
      filename: req.params.filename,
      error,
    });
    next(error);
  }
});

export { router as downloadController };
