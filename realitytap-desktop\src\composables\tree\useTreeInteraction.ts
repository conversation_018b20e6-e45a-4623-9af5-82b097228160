import { ref, type Ref } from "vue";
import { useDialog, useMessage } from "naive-ui";
import type { HapticsGroup } from "@/types/haptic-project";
import { parseErrorMessage } from "@/utils/commonUtils";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 树形交互处理 Composable
 * 负责处理删除分组等复杂交互逻辑
 */
export function useTreeInteraction(
  projectStore: {
    activeProject: { groups?: HapticsGroup[]; files?: any[] } | null;
    removeGroup: (groupUuid: string, mode: "restrict" | "cascade") => Promise<void>;
  },
  selectedTreeKeys: Ref<string[]>,
  emit: (event: "select-project-item", item: any) => void,
  cancelAnyOngoingEdit: () => void,
  t: (key: string, values?: Record<string, any>) => string
) {
  const dialog = useDialog();
  const message = useMessage();
  const isDeletingGroup = ref(false);

  /**
   * 递归统计分组及其所有子分组、文件的函数
   */
  const collectGroupDescendants = (groupUuid: string) => {
    const allGroupUuids = new Set<string>();
    const allFileUuids = new Set<string>();
    const groups = projectStore.activeProject?.groups || [];
    const files = projectStore.activeProject?.files || [];

    // 递归收集所有子分组
    function collect(uuid: string) {
      allGroupUuids.add(uuid);
      for (const g of groups) {
        if (g.parentGroupUuid === uuid) {
          collect(g.groupUuid);
        }
      }
    }
    collect(groupUuid);

    // 收集所有属于这些分组的文件
    for (const f of files) {
      if (f.group && allGroupUuids.has(f.group)) {
        allFileUuids.add(f.fileUuid);
      }
    }
    return {
      groupUuids: Array.from(allGroupUuids),
      fileUuids: Array.from(allFileUuids),
    };
  };

  /**
   * 删除分组处理函数
   */
  const handleDeleteGroup = async (group: HapticsGroup) => {
    if (isDeletingGroup.value) return;
    isDeletingGroup.value = true;

    try {
      // 强制取消所有正在进行的重命名/新建分组状态
      cancelAnyOngoingEdit();
      const { fileUuids } = collectGroupDescendants(group.groupUuid);
      const fileCount = fileUuids.length;

      // 如果文件数为0，弹一次Dialog
      if (fileCount === 0) {
        await new Promise<void>((resolve, reject) => {
          dialog.warning({
            title: t('editor.groupDelete.confirmTitle'),
            content: t('editor.groupDelete.confirmMessage', { name: group.name }),
            positiveText: t('editor.groupDelete.confirmDelete'),
            negativeText: t('common.cancel'),
            onPositiveClick: async () => {
              try {
                await projectStore.removeGroup(group.groupUuid, "restrict");
                message.success(t('editor.groupDelete.deleteSuccess', { name: group.name }));
                // 清空选中项
                selectedTreeKeys.value = [];
                emit("select-project-item", null);
                resolve();
              } catch (error: any) {
                message.error(t('editor.groupDelete.deleteFailed', { error: parseErrorMessage(error) }));
                reject(error);
              }
            },
            onNegativeClick: () => {
              message.info(t('editor.groupDelete.cancelled'));
              resolve();
            },
          });
        });
      } else {
        // 如果文件数>0，连续两次Dialog
        await new Promise<void>((resolve, reject) => {
          dialog.warning({
            title: t('editor.groupDelete.confirmWithContentTitle'),
            content: t('editor.groupDelete.confirmWithContentMessage', { name: group.name, count: fileCount }),
            positiveText: t('editor.groupDelete.confirmDelete'),
            negativeText: t('common.cancel'),
            onPositiveClick: async () => {
              try {
                await projectStore.removeGroup(group.groupUuid, "cascade");
                message.success(t('editor.groupDelete.deleteWithContentSuccess', { name: group.name }));
                selectedTreeKeys.value = [];
                emit("select-project-item", null);
                resolve();
              } catch (error: any) {
                message.error(t('editor.groupDelete.deleteFailed', { error: parseErrorMessage(error) }));
                reject(error);
              }
            },
            onNegativeClick: () => {
              message.info(t('editor.groupDelete.cancelled'));
              resolve();
            },
          });
        });
      }
    } catch (error: any) {
      logger.error(LogModule.PROJECT, "删除分组时发生错误", error);
      message.error(t('editor.groupDelete.deleteFailed', { error: parseErrorMessage(error) }));
    } finally {
      isDeletingGroup.value = false;
    }
  };

  return {
    isDeletingGroup,
    collectGroupDescendants,
    handleDeleteGroup,
  };
}
