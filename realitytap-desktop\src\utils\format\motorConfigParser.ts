/**
 * 马达配置文件名解析工具
 * Motor configuration file name parser utilities
 */

import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 从马达配置文件名中解析额定F0频率
 * 文件名格式：LRA_0809_normal_170Hz.conf
 * @param fileName 文件名
 * @returns 额定F0频率 (Hz)，解析失败时返回默认值170Hz
 */
export function parseRatedF0FromFileName(fileName: string): number {
  const DEFAULT_F0 = 170; // 默认F0频率

  try {
    // 正则表达式匹配 _数字Hz 的模式
    const f0Match = fileName.match(/_(\d+)Hz/i);
    
    if (f0Match && f0Match[1]) {
      const f0Value = parseInt(f0Match[1], 10);
      
      // 验证F0值是否在合理范围内 (50-500Hz)
      if (f0Value >= 50 && f0Value <= 500) {
        logger.debug(LogModule.GENERAL, "成功解析马达额定F0", { 
          fileName, 
          ratedF0: f0Value 
        });
        return f0Value;
      } else {
        logger.warn(LogModule.GENERAL, "解析的F0值超出合理范围，使用默认值", { 
          fileName, 
          parsedF0: f0Value, 
          defaultF0: DEFAULT_F0 
        });
      }
    } else {
      logger.warn(LogModule.GENERAL, "文件名中未找到F0频率信息，使用默认值", { 
        fileName, 
        defaultF0: DEFAULT_F0 
      });
    }
  } catch (error) {
    logger.error(LogModule.GENERAL, "解析马达额定F0失败，使用默认值", { 
      fileName, 
      error, 
      defaultF0: DEFAULT_F0 
    });
  }

  return DEFAULT_F0;
}

/**
 * 生成实际频率选项
 * 实际频率 = 额定F0 ± 10Hz 范围内的所有整数
 * @param ratedF0 额定F0频率
 * @returns 实际频率选项数组
 */
export function generateActualFrequencyOptions(ratedF0: number): number[] {
  const options: number[] = [];
  const minFreq = Math.max(1, ratedF0 - 10); // 确保最小频率为1Hz
  const maxFreq = ratedF0 + 10;

  for (let freq = minFreq; freq <= maxFreq; freq++) {
    options.push(freq);
  }

  return options;
}

/**
 * 从文件名生成马达ID
 * 将文件名转换为小写并去除扩展名
 * @param fileName 文件名
 * @returns 马达ID
 */
export function generateMotorIdFromFileName(fileName: string): string {
  return fileName
    .replace(/\.(conf|bin)$/i, '') // 去除扩展名
    .toLowerCase(); // 转为小写
}

/**
 * 从文件名生成显示名称
 * 将下划线替换为空格，去除扩展名，首字母大写
 * @param fileName 文件名
 * @returns 显示名称
 */
export function generateDisplayNameFromFileName(fileName: string): string {
  const baseName = fileName.replace(/\.(conf|bin)$/i, ''); // 去除扩展名
  return baseName
    .replace(/_/g, ' ') // 下划线替换为空格
    .replace(/\b\w/g, l => l.toUpperCase()); // 首字母大写
}
