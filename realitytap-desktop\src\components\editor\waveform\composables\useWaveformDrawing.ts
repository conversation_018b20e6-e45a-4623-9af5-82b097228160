import type { RenderableContinuousCurvePoint, RenderableContinuousEvent, RenderableEvent, RenderableTransientEvent } from "@/types/haptic-editor";
import { ref, type Ref } from "vue";
import {
  AXIS_COLOR,
  CONTINUOUS_SELECTED_STROKE_COLOR,
  CONTINUOUS_STROKE_COLOR,
  TRANSIENT_FILL_COLOR,
  TRANSIENT_SELECTED_FILL_COLOR,
  TRANSIENT_SELECTED_STROKE_COLOR,
  TRANSIENT_STROKE_COLOR,
  mapFrequencyToColor,
} from "../utils/color";
import {
  DEFAULT_LINE_WIDTH,
  DEFAULT_POINT_RADIUS,
  SELECTED_LINE_WIDTH,
  SELECTED_POINT_RADIUS
} from "../utils/drawing-helpers";

import { EVENT_WAVEFORM_VERTICAL_OFFSET } from "../config/waveform-constants";

// 绘制相关的配置接口
export interface DrawingConfig {
  padding: { top: number; right: number; bottom: number; left: number };
  canvasWidth: Ref<number>;
  canvasHeight: Ref<number>;
  virtualScrollOffset: Ref<number>;
  getEffectiveDuration: () => number;
  getGraphAreaWidth: () => number;
  getLogicalGraphAreaWidth: () => number;
  getGraphAreaHeight: () => number;
  mapTimeToXLocal: (time: number) => number;
  mapIntensityToYLocal: (intensity: number) => number;
  isEventSelected: (event: RenderableEvent) => boolean;
  isDragging: Ref<boolean>;
  draggedEvent: Ref<RenderableEvent | null>;
  createPreviewEvent: () => RenderableEvent | undefined;
  waveformStore: any;
}

// 绘制状态缓存
export interface DrawState {
  lastEventCount: number;
  lastSelectedEventId: string | null;
  lastEffectiveDuration: number;
  lastScrollLeft: number;
}

// 连续事件坐标缓存
export interface ContinuousEventCacheItem {
  points: Array<{ x: number; y: number; color: string }>;
  lastStartTime: number;
  lastCurveHash: string;
  lastSelected: boolean;
  lastUsed: number;
  lastVirtualOffset: number;
}

export function useWaveformDrawing(config: DrawingConfig) {
  // 性能监控相关变量（简化版本）
  const performanceStats = ref({ drawTime: 0, eventCount: 0 });
  const updatePerformanceStats = (stats: { drawTime: number; eventCount: number }) => {
    performanceStats.value = stats;
  };

  // 绘制状态缓存
  const drawState = ref<DrawState>({
    lastEventCount: 0,
    lastSelectedEventId: null,
    lastEffectiveDuration: 0,
    lastScrollLeft: 0,
  });

  // 连续事件坐标缓存
  const continuousEventCache = new Map<string, ContinuousEventCacheItem>();
  const MAX_CACHE_SIZE = 50;

  // 计算曲线点的哈希值，用于检测变化
  const getCurveHash = (curves: RenderableContinuousCurvePoint[]): string => {
    return curves.map((p) => `${p.timeOffset},${p.drawIntensity},${p.curveFrequency}`).join("|");
  };

  // 快速哈希计算，用于属性调整时的轻量级检测
  const getFastCurveHash = (curves: RenderableContinuousCurvePoint[]): string => {
    return `${curves.length}-${curves[0]?.timeOffset || 0}-${curves[curves.length - 1]?.timeOffset || 0}`;
  };

  // LRU缓存清理
  const cleanupCache = () => {
    if (continuousEventCache.size <= MAX_CACHE_SIZE) return;

    const entries = Array.from(continuousEventCache.entries()).sort((a, b) => a[1].lastUsed - b[1].lastUsed);
    const toDelete = entries.slice(0, entries.length - MAX_CACHE_SIZE);
    toDelete.forEach(([key]) => continuousEventCache.delete(key));
  };

  // 智能缓存失效：只清除特定事件的缓存
  const invalidateEventCache = (eventId: string) => {
    continuousEventCache.delete(eventId);
  };

  // 重置绘制状态缓存
  const resetDrawState = () => {
    drawState.value.lastEventCount = 0;
    drawState.value.lastSelectedEventId = null;
    drawState.value.lastEffectiveDuration = 0;
    drawState.value.lastScrollLeft = 0;
    continuousEventCache.clear();
  };

  // 检查是否需要重绘
  const shouldRedraw = (events: RenderableEvent[], scrollLeftValue: number): boolean => {
    if (config.isDragging.value) {
      return true;
    }

    const currentEventCount = events?.length || 0;
    const currentSelectedEventId = config.waveformStore.selectedEventId;
    const currentEffectiveDuration = config.getEffectiveDuration();
    const currentScrollLeft = scrollLeftValue;

    const needsRedraw =
      drawState.value.lastEventCount !== currentEventCount ||
      drawState.value.lastSelectedEventId !== currentSelectedEventId ||
      drawState.value.lastEffectiveDuration !== currentEffectiveDuration ||
      Math.abs(drawState.value.lastScrollLeft - currentScrollLeft) > 10;

    if (needsRedraw) {
      drawState.value.lastEventCount = currentEventCount;
      drawState.value.lastSelectedEventId = currentSelectedEventId;
      drawState.value.lastEffectiveDuration = currentEffectiveDuration;
      drawState.value.lastScrollLeft = currentScrollLeft;
    }

    return needsRedraw;
  };

  // 简化的视口裁剪函数
  const getVisibleEvents = (events: RenderableEvent[]): RenderableEvent[] => {
    if (!events || events.length === 0) return [];

    const effectiveDuration = config.getEffectiveDuration();
    const hasManyEvents = events.length > 50;

    if (!hasManyEvents) {
      return events.slice(0, 100);
    }

    const logicalGraphWidth = config.getLogicalGraphAreaWidth();
    const physicalGraphWidth = config.getGraphAreaWidth();
    const currentVirtualOffset = config.virtualScrollOffset.value;

    const visibleStartTime = (currentVirtualOffset / logicalGraphWidth) * effectiveDuration;
    const visibleEndTime = ((currentVirtualOffset + physicalGraphWidth) / logicalGraphWidth) * effectiveDuration;

    const bufferRatio = config.isDragging.value ? 0.5 : 0.3;
    const bufferTime = (visibleEndTime - visibleStartTime) * bufferRatio;
    const bufferedStartTime = Math.max(0, visibleStartTime - bufferTime);
    const bufferedEndTime = Math.min(effectiveDuration, visibleEndTime + bufferTime);

    let visibleEvents = events.filter((event) => {
      return event.stopTime >= bufferedStartTime && event.startTime <= bufferedEndTime;
    });

    // 拖动时确保被拖动的事件始终包含在可见事件中
    if (config.isDragging.value && config.draggedEvent.value) {
      const draggedEventId = config.draggedEvent.value.id;
      const isDraggedEventVisible = visibleEvents.some((e) => e.id === draggedEventId);

      if (!isDraggedEventVisible) {
        const draggedEventInList = events.find((e) => e.id === draggedEventId);
        if (draggedEventInList) {
          visibleEvents = [...visibleEvents, draggedEventInList];
        }
      }
    }

    return visibleEvents.slice(0, 100);
  };

  // 简化的网格绘制函数
  const drawSimplifiedGrid = (ctx: CanvasRenderingContext2D) => {
    ctx.strokeStyle = AXIS_COLOR;
    ctx.lineWidth = 1;

    const baseY = config.mapIntensityToYLocal(0);
    ctx.beginPath();
    ctx.moveTo(config.padding.left, baseY);
    ctx.lineTo(config.getGraphAreaWidth() + config.padding.left, baseY);
    ctx.stroke();

    const intensityLevels = [25, 50, 75];
    ctx.strokeStyle = "#e0e0e0";
    ctx.lineWidth = 0.5;

    for (const intensity of intensityLevels) {
      const y = config.mapIntensityToYLocal(intensity);
      ctx.beginPath();
      ctx.moveTo(config.padding.left, y);
      ctx.lineTo(config.getGraphAreaWidth() + config.padding.left, y);
      ctx.stroke();
    }
  };

  // 绘制瞬态事件
  const drawTransientEvent = (ctx: CanvasRenderingContext2D, event: RenderableTransientEvent) => {
    const peakX = config.mapTimeToXLocal(event.peakTime);
    const peakY = config.mapIntensityToYLocal(event.intensity);
    const startX = config.mapTimeToXLocal(event.startTime);
    const endX = config.mapTimeToXLocal(event.stopTime);
    // 添加垂直偏移，确保X轴可见
    const baseY = config.mapIntensityToYLocal(0) - EVENT_WAVEFORM_VERTICAL_OFFSET;

    const actualAreaWidth = config.getGraphAreaWidth();
    const tolerance = 50;

    if (endX < -tolerance || startX > actualAreaWidth + tolerance) return;

    const clampedStartX = Math.max(-tolerance, Math.min(startX, actualAreaWidth + tolerance));
    const clampedEndX = Math.max(-tolerance, Math.min(endX, actualAreaWidth + tolerance));
    const clampedPeakX = Math.max(-tolerance, Math.min(peakX, actualAreaWidth + tolerance));

    if (clampedEndX < -tolerance || clampedStartX > actualAreaWidth + tolerance) return;

    const isSelected = config.isEventSelected(event);
    const strokeColor = isSelected ? TRANSIENT_SELECTED_STROKE_COLOR : TRANSIENT_STROKE_COLOR;
    const fillColor = isSelected ? TRANSIENT_SELECTED_FILL_COLOR : TRANSIENT_FILL_COLOR;
    const lineWidth = isSelected ? SELECTED_LINE_WIDTH : DEFAULT_LINE_WIDTH;
    const pointRadius = isSelected ? SELECTED_POINT_RADIUS : DEFAULT_POINT_RADIUS;

    // 绘制填充
    ctx.beginPath();
    ctx.moveTo(clampedStartX, baseY);
    ctx.lineTo(clampedPeakX, peakY);
    ctx.lineTo(clampedEndX, baseY);
    ctx.fillStyle = fillColor;
    ctx.fill();

    // 绘制边框
    ctx.beginPath();
    ctx.moveTo(clampedStartX, baseY);
    ctx.lineTo(clampedPeakX, peakY);
    ctx.lineTo(clampedEndX, baseY);
    ctx.strokeStyle = strokeColor;
    ctx.lineWidth = lineWidth;
    ctx.stroke();

    // 峰值点
    ctx.beginPath();
    ctx.arc(clampedPeakX, peakY, pointRadius, 0, Math.PI * 2);
    ctx.fillStyle = strokeColor;
    ctx.fill();
  };

  // 绘制连续事件
  const drawContinuousEvent = (ctx: CanvasRenderingContext2D, event: RenderableContinuousEvent) => {
    if (event.curves.length < 2) return;

    const isSelected = config.isEventSelected(event);
    // 添加垂直偏移，确保X轴可见
    const baselineY = config.mapIntensityToYLocal(0) - EVENT_WAVEFORM_VERTICAL_OFFSET;
    const currentPointRadius = isSelected ? SELECTED_POINT_RADIUS : DEFAULT_POINT_RADIUS;
    const currentLineWidth = isSelected ? SELECTED_LINE_WIDTH : DEFAULT_LINE_WIDTH;
    const baseStrokeColor = isSelected ? CONTINUOUS_SELECTED_STROKE_COLOR : CONTINUOUS_STROKE_COLOR;

    const curveCount = event.curves.length;
    let points: Array<{ x: number; y: number; color: string }>;

    // 检查缓存
    const isAdjusting = config.waveformStore.isAdjustingProperties;
    const curveHash = isAdjusting ? getFastCurveHash(event.curves) : getCurveHash(event.curves);
    const cacheKey = event.id;
    const cached = continuousEventCache.get(cacheKey);
    const now = performance.now();

    const offsetTolerance = isAdjusting ? 5.0 : 1.0;
    const offsetMatches = cached ? Math.abs(cached.lastVirtualOffset - config.virtualScrollOffset.value) <= offsetTolerance : false;

    if (cached && cached.lastStartTime === event.startTime && cached.lastCurveHash === curveHash && cached.lastSelected === isSelected && offsetMatches) {
      points = cached.points;
      cached.lastUsed = now;
      cached.lastVirtualOffset = config.virtualScrollOffset.value;
    } else {
      points = new Array(curveCount);

      for (let i = 0; i < curveCount; i++) {
        const p = event.curves[i];
        const x = config.mapTimeToXLocal(event.startTime + p.timeOffset);
        const y = config.mapIntensityToYLocal(p.drawIntensity);
        const color = mapFrequencyToColor(p.curveFrequency, isSelected);
        points[i] = { x, y, color };
      }

      continuousEventCache.set(cacheKey, {
        points: points,
        lastStartTime: event.startTime,
        lastCurveHash: curveHash,
        lastSelected: isSelected,
        lastUsed: now,
        lastVirtualOffset: config.virtualScrollOffset.value,
      });

      if (continuousEventCache.size > MAX_CACHE_SIZE) {
        cleanupCache();
      }
    }

    // 绘制填充区域
    if (curveCount >= 2) {
      const gradient = ctx.createLinearGradient(points[0].x, 0, points[curveCount - 1].x, 0);

      for (let i = 0; i < curveCount; i++) {
        const stopPosition = i / (curveCount - 1);
        gradient.addColorStop(stopPosition, points[i].color);
      }

      ctx.beginPath();
      ctx.moveTo(points[0].x, points[0].y);

      for (let i = 1; i < curveCount; i++) {
        ctx.lineTo(points[i].x, points[i].y);
      }

      ctx.lineTo(points[curveCount - 1].x, baselineY);
      ctx.lineTo(points[0].x, baselineY);
      ctx.closePath();

      ctx.fillStyle = gradient;
      ctx.fill();
    }

    // 绘制曲线描边
    ctx.strokeStyle = baseStrokeColor;
    ctx.lineWidth = currentLineWidth;
    ctx.fillStyle = baseStrokeColor;

    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < curveCount; i++) {
      ctx.lineTo(points[i].x, points[i].y);
    }
    ctx.stroke();

    // 绘制关键点标记
    for (let i = 0; i < curveCount; i++) {
      ctx.beginPath();
      ctx.arc(points[i].x, points[i].y, currentPointRadius, 0, Math.PI * 2);
      ctx.fill();
    }
  };

  // 简化的瞬态事件绘制
  const drawTransientEventSimplified = (ctx: CanvasRenderingContext2D, event: RenderableTransientEvent) => {
    const peakX = config.mapTimeToXLocal(event.peakTime);
    const peakY = config.mapIntensityToYLocal(event.intensity);
    const startX = config.mapTimeToXLocal(event.startTime);
    const endX = config.mapTimeToXLocal(event.stopTime);
    // 添加垂直偏移，确保X轴可见
    const baseY = config.mapIntensityToYLocal(0) - EVENT_WAVEFORM_VERTICAL_OFFSET;

    const actualAreaWidth = config.getGraphAreaWidth();
    if (endX < -50 || startX > actualAreaWidth + 50) return;

    ctx.strokeStyle = "#4a9eff";
    ctx.fillStyle = "#4a9eff33";
    ctx.lineWidth = 1;

    ctx.beginPath();
    ctx.moveTo(startX, baseY);
    ctx.lineTo(peakX, peakY);
    ctx.lineTo(endX, baseY);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
  };

  // 简化的连续事件绘制
  const drawContinuousEventSimplified = (ctx: CanvasRenderingContext2D, event: RenderableContinuousEvent) => {
    const curveCount = event.curves.length;
    if (curveCount < 2) return;

    const startX = config.mapTimeToXLocal(event.startTime);
    const endX = config.mapTimeToXLocal(event.stopTime);
    const actualAreaWidth = config.getGraphAreaWidth();
    if (endX < -50 || startX > actualAreaWidth + 50) return;

    ctx.strokeStyle = "#36ad6a";
    ctx.lineWidth = 1;

    ctx.beginPath();
    for (let i = 0; i < curveCount; i++) {
      const p = event.curves[i];
      const x = config.mapTimeToXLocal(event.startTime + p.timeOffset);
      const y = config.mapIntensityToYLocal(p.drawIntensity);

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();
  };

  return {
    // 状态
    drawState,
    continuousEventCache,
    performanceStats,

    // 缓存管理
    getCurveHash,
    getFastCurveHash,
    cleanupCache,
    invalidateEventCache,
    resetDrawState,

    // 绘制检查
    shouldRedraw,
    updatePerformanceStats,
    getVisibleEvents,

    // 绘制函数
    drawSimplifiedGrid,
    drawTransientEvent,
    drawContinuousEvent,
    drawTransientEventSimplified,
    drawContinuousEventSimplified,
  };
}
