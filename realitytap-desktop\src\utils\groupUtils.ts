import type { HapticsGroup } from "@/types/haptic-project";

/**
 * 获取分组链（根到当前分组），优先递归 parentGroupUuid，异常时用 path 字段兜底。
 * @param groups 所有分组数组
 * @param groupUuid 当前分组的 uuid
 * @returns HapticsGroup[] 分组链（根到当前分组）
 */
export function getGroupChainByUuid(groups: HapticsGroup[], groupUuid: string): HapticsGroup[] {
  if (!groups || !groupUuid) return [];
  const chain: HapticsGroup[] = [];
  let current = groups.find(g => g.groupUuid === groupUuid);
  const visited = new Set<string>();
  // 递归 parentGroupUuid
  while (current) {
    if (!current) break; // 防止 current 为 undefined
    if (visited.has(current.groupUuid)) {
      // 检测到循环引用，防止死循环
      break;
    }
    chain.push(current);
    visited.add(current.groupUuid);
    if (!current.parentGroupUuid) break;
    current = groups.find(g => g.groupUuid === current?.parentGroupUuid);
  }
  if (chain.length > 0) {
    return chain.reverse(); // 根到当前分组
  }
  // fallback: 用 path 字段兜底
  const fallback = groups.find(g => g.groupUuid === groupUuid);
  if (fallback && fallback.path) {
    const pathParts = fallback.path.split("/");
    // 尝试用 path 片段在 groups 中查找 name，否则直接用 path 片段
    const chainByPath: HapticsGroup[] = pathParts.map((part, idx) => {
      // 在 groups 中查找 path 匹配的分组
      const pathSoFar = pathParts.slice(0, idx + 1).join("/");
      const found = groups.find(g => g.path === pathSoFar);
      if (found) return found;
      // 构造一个临时分组对象
      return {
        groupUuid: `fallback-${pathSoFar}`,
        name: part,
        path: pathSoFar,
        description: '',
        parentGroupUuid: idx === 0 ? null : `fallback-${pathParts.slice(0, idx).join("/")}`
      };
    });
    return chainByPath;
  }
  return [];
} 