import { invoke } from "@tauri-apps/api/core";
import type { FileEditorState } from "../types";
import type { AudioAmplitudeData } from "../index";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 音频相关Actions接口
 */
export interface AudioActions {
  loadAudioData: (projectDirPath: string, audioRelativePath: string, maxSamples?: number) => Promise<void>;
  setAudioData: (data: AudioAmplitudeData | null) => void;
  clearAudioData: () => void;
  getAudioData: () => AudioAmplitudeData | null;
  hasAudioData: () => boolean;
  retryAudioLoad: (projectDirPath: string, audioRelativePath: string, maxSamples?: number) => Promise<void>;
}

/**
 * 创建音频相关的Actions
 */
export function createAudioActions(
  state: FileEditorState,
  setState: (updates: Partial<FileEditorState>, options?: { skipHistoryRecord?: boolean }) => void
): AudioActions {

  /**
   * 根据文件类型和预估时长计算最优采样点数
   */
  const calculateOptimalSampleCount = (audioRelativePath: string, estimatedDurationMs?: number): number => {
    const fileExt = audioRelativePath.toLowerCase().split('.').pop() || '';

    // 如果有预估时长，使用智能计算
    if (estimatedDurationMs) {
      if (estimatedDurationMs <= 30_000) return 2048;        // ≤30秒：高精度
      if (estimatedDurationMs <= 60_000) return 1536;        // 30秒-1分钟：较高精度
      if (estimatedDurationMs <= 300_000) return 1024;       // 1-5分钟：中等精度
      if (estimatedDurationMs <= 600_000) return 512;        // 5-10分钟：较低精度
      if (estimatedDurationMs <= 1_800_000) return 256;      // 10-30分钟：低精度
      return 128;                                             // >30分钟：最低精度
    }

    // 基于文件类型的默认策略
    if (fileExt === 'mp4') {
      // 视频文件通常较长，使用较少的采样点
      return 512;
    } else {
      // 音频文件通常较短，可以使用更多采样点
      return 1024;
    }
  };

  /**
   * 异步加载音频振幅数据
   */
  const loadAudioData = async (projectDirPath: string, audioRelativePath: string, maxSamples?: number): Promise<void> => {
    if (!projectDirPath || !audioRelativePath) {
      setState({ audioAmplitudeData: null });
      return;
    }

    try {
      logger.debug(LogModule.WAVEFORM, "开始加载音频振幅数据", {
        projectDirPath,
        audioRelativePath,
        maxSamples
      });

      // 如果没有指定maxSamples，使用智能计算
      const effectiveMaxSamples = maxSamples || calculateOptimalSampleCount(audioRelativePath);

      const amplitudeData = await invoke<AudioAmplitudeData>("get_audio_amplitude_data", {
        projectDirPath,
        audioRelativePath,
        maxSamples: effectiveMaxSamples,
      });

      setState({
        audioAmplitudeData: amplitudeData,
        audioDuration: amplitudeData.duration_ms
      });

      logger.info(LogModule.WAVEFORM, "音频振幅数据加载成功", {
        samplesCount: amplitudeData.samples.length,
        duration: amplitudeData.duration_ms,
        sampleRate: amplitudeData.sample_rate,
        requestedMaxSamples: effectiveMaxSamples,
        fileType: audioRelativePath.toLowerCase().split('.').pop(),
      });
    } catch (error) {
      logger.error(LogModule.WAVEFORM, "加载音频振幅数据失败", error);

      setState({
        audioAmplitudeData: null,
        audioDuration: null
      });

      throw error;
    }
  };

  /**
   * 直接设置音频数据
   */
  const setAudioData = (data: AudioAmplitudeData | null) => {
    if (data) {
      // 验证数据有效性
      if (typeof data === "object" && data.samples && Array.isArray(data.samples) && data.samples.length > 0) {
        logger.debug(LogModule.WAVEFORM, "设置有效的音频数据", {
          samplesCount: data.samples.length,
          duration: data.duration_ms,
          sampleRate: data.sample_rate,
        });
        setState({
          audioAmplitudeData: data,
          audioDuration: data.duration_ms
        });
      } else {
        logger.warn(LogModule.WAVEFORM, "接收到无效的音频数据，清理状态");
        setState({
          audioAmplitudeData: null,
          audioDuration: null
        });
      }
    } else {
      logger.debug(LogModule.WAVEFORM, "设置音频数据为null，清理状态");
      setState({
        audioAmplitudeData: null,
        audioDuration: null
      });
    }
  };

  /**
   * 清除音频数据
   */
  const clearAudioData = () => {
    logger.debug(LogModule.WAVEFORM, "清除音频数据");
    setState({
      audioAmplitudeData: null,
      audioDuration: null
    });
  };

  /**
   * 获取音频数据
   */
  const getAudioData = (): AudioAmplitudeData | null => {
    return state.audioAmplitudeData;
  };

  /**
   * 检查是否有音频数据
   */
  const hasAudioData = (): boolean => {
    return !!(state.audioAmplitudeData &&
              state.audioAmplitudeData.samples &&
              Array.isArray(state.audioAmplitudeData.samples) &&
              state.audioAmplitudeData.samples.length > 0);
  };

  /**
   * 重试音频数据加载
   */
  const retryAudioLoad = async (projectDirPath: string, audioRelativePath: string, maxSamples?: number): Promise<void> => {
    logger.info(LogModule.WAVEFORM, "重试加载音频数据", { projectDirPath, audioRelativePath });
    return loadAudioData(projectDirPath, audioRelativePath, maxSamples);
  };

  return {
    loadAudioData,
    setAudioData,
    clearAudioData,
    getAudioData,
    hasAudioData,
    retryAudioLoad,
  };
}
