/**
 * 安装器组合函数
 * 提供安全安装相关的响应式状态和方法
 */

import { ref, computed, readonly } from 'vue';
import { useI18n } from '@/composables/useI18n';
import { safeInstallerService } from '@/services/installer/installer.service';
import { gracefulShutdownManager } from '@/utils/graceful-shutdown/gracefulShutdown';
import type { SafeInstallOptions, SafeInstallResult } from '@/services/ota.service';
import type { InstallState } from '@/services/installer/installer.service';
import { logger, LogModule } from '@/utils/logger/logger';

export interface InstallerState {
  isInstalling: boolean;
  installProgress: number;
  currentOperation: string;
  installLogs: string[];
  error: string | null;
  canCancel: boolean;
}

/**
 * 安装器组合函数
 */
export function useInstaller() {
  const { t } = useI18n();

  // === 响应式状态 ===
  const isInstalling = ref(false);
  const installProgress = ref(0);
  const currentOperation = ref('');
  const installLogs = ref<string[]>([]);
  const error = ref<string | null>(null);
  const canCancel = ref(true);
  const installResult = ref<SafeInstallResult | null>(null);

  // === 计算属性 ===
  const installerState = computed<InstallerState>(() => ({
    isInstalling: isInstalling.value,
    installProgress: installProgress.value,
    currentOperation: currentOperation.value,
    installLogs: installLogs.value,
    error: error.value,
    canCancel: canCancel.value,
  }));

  const isCompleted = computed(() => 
    installResult.value?.success === true
  );

  const isFailed = computed(() => 
    installResult.value?.success === false || error.value !== null
  );

  const progressText = computed(() => {
    if (installProgress.value === 0) {
      return t('installer.preparing');
    } else if (installProgress.value === 100) {
      return t('installer.completed');
    } else {
      return `${installProgress.value}%`;
    }
  });

  const statusText = computed(() => {
    if (error.value) {
      return t('installer.failed');
    } else if (isCompleted.value) {
      return t('installer.success');
    } else if (isInstalling.value) {
      return t('installer.installing');
    } else {
      return t('installer.ready');
    }
  });

  // === 方法 ===

  /**
   * 开始安全安装
   */
  const startSafeInstall = async (
    packagePath: string,
    options: SafeInstallOptions = {}
  ): Promise<SafeInstallResult> => {
    try {
      logger.info(LogModule.GENERAL, 'useInstaller.startSafeInstall 开始');
      
      // 重置状态
      resetState();
      isInstalling.value = true;
      currentOperation.value = t('installer.initializing');

      // 开始安装
      const result = await safeInstallerService.startSafeInstall(packagePath, options);
      
      installResult.value = result;
      
      if (result.success) {
        installProgress.value = 100;
        currentOperation.value = t('installer.completed');
        addLog(t('installer.installSuccess'));
      } else {
        error.value = result.errorMessage || t('installer.unknownError');
        addLog(t('installer.installFailed', { error: error.value }));
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      error.value = errorMessage;
      addLog(t('installer.installFailed', { error: errorMessage }));
      
      return {
        success: false,
        errorMessage,
        logs: installLogs.value,
      };
    } finally {
      isInstalling.value = false;
      canCancel.value = false;
    }
  };

  /**
   * 开始安装并优雅退出
   */
  const startInstallAndGracefulExit = async (
    packagePath: string,
    options: SafeInstallOptions = {}
  ): Promise<SafeInstallResult> => {
    try {
      logger.info(LogModule.GENERAL, 'useInstaller.startInstallAndGracefulExit 开始');
      
      // 开始安装
      const result = await startSafeInstall(packagePath, options);
      
      if (result.success) {
        addLog(t('installer.preparingExit'));
        
        // 延迟一下让用户看到成功消息
        setTimeout(() => {
          gracefulShutdownManager.startGracefulShutdown({
            delay: 1000,
            saveUserData: true,
            cleanupTempFiles: true,
          }).catch(err => logger.error(LogModule.GENERAL, '优雅关闭失败', err));
        }, 2000);
      }
      
      return result;
      
    } catch (err) {
      logger.error(LogModule.GENERAL, '安装并退出失败', err);
      throw err;
    }
  };

  /**
   * 取消安装
   */
  const cancelInstall = async (): Promise<void> => {
    try {
      if (!canCancel.value) {
        logger.warn(LogModule.GENERAL, '当前状态不允许取消安装');
        return;
      }

      logger.info(LogModule.GENERAL, '取消安装');
      addLog(t('installer.cancelling'));
      
      await safeInstallerService.cancelInstall();
      
      // 更新状态
      isInstalling.value = false;
      canCancel.value = false;
      currentOperation.value = t('installer.cancelled');
      addLog(t('installer.installCancelled'));
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      logger.error(LogModule.GENERAL, '取消安装失败', { error: errorMessage });
      error.value = errorMessage;
      addLog(t('installer.cancelFailed', { error: errorMessage }));
    }
  };

  /**
   * 监控安装状态
   */
  const monitorInstallState = async (): Promise<void> => {
    if (!isInstalling.value) {
      return;
    }

    try {
      const state = await safeInstallerService.getInstallState();
      if (state) {
        updateStateFromInstallState(state);
      }
    } catch (err) {
      logger.error(LogModule.GENERAL, '获取安装状态失败', err);
    }
  };

  /**
   * 重置状态
   */
  const resetState = (): void => {
    isInstalling.value = false;
    installProgress.value = 0;
    currentOperation.value = '';
    installLogs.value = [];
    error.value = null;
    canCancel.value = true;
    installResult.value = null;
  };

  /**
   * 添加日志
   */
  const addLog = (message: string): void => {
    const timestamp = new Date().toLocaleTimeString();
    installLogs.value.push(`[${timestamp}] ${message}`);
    
    // 限制日志数量
    if (installLogs.value.length > 100) {
      installLogs.value = installLogs.value.slice(-50);
    }
  };

  /**
   * 从安装状态更新本地状态
   */
  const updateStateFromInstallState = (state: InstallState): void => {
    installProgress.value = state.progress;
    
    if (state.currentOperation) {
      currentOperation.value = state.currentOperation;
    }
    
    // 更新日志
    if (state.logs.length > installLogs.value.length) {
      const newLogs = state.logs.slice(installLogs.value.length);
      installLogs.value.push(...newLogs);
    }
    
    // 更新错误状态
    if (state.error && !error.value) {
      error.value = state.error;
    }

    // 更新取消状态 - InstallState 没有 state 属性，根据其他状态判断
    if (!state.isInstalling || state.error) {
      canCancel.value = false;
    }
  };

  /**
   * 获取操作文本
   */
  const getOperationText = (operation: string): string => {
    const operationMap: Record<string, string> = {
      'initializing': t('installer.operations.initializing'),
      'validating': t('installer.operations.validating'),
      'waiting_for_app_exit': t('installer.operations.waitingForExit'),
      'backing_up': t('installer.operations.backingUp'),
      'installing': t('installer.operations.installing'),
      'restarting': t('installer.operations.restarting'),
      'completed': t('installer.operations.completed'),
      'failed': t('installer.operations.failed'),
      'cancelled': t('installer.operations.cancelled'),
    };
    
    return operationMap[operation] || operation;
  };

  // === 返回值 ===
  return {
    // 状态
    installerState: readonly(installerState),
    isInstalling: readonly(isInstalling),
    installProgress: readonly(installProgress),
    currentOperation: readonly(currentOperation),
    installLogs: readonly(installLogs),
    error: readonly(error),
    canCancel: readonly(canCancel),
    installResult: readonly(installResult),
    
    // 计算属性
    isCompleted: readonly(isCompleted),
    isFailed: readonly(isFailed),
    progressText: readonly(progressText),
    statusText: readonly(statusText),
    
    // 方法
    startSafeInstall,
    startInstallAndGracefulExit,
    cancelInstall,
    monitorInstallState,
    resetState,
    addLog,
    getOperationText,
  };
}
