import { BaseDAO } from './base.dao';
import { DownloadRecord, DownloadStatsData, DownloadStatsQuery } from '@/types/server.types';
import { v4 as uuidv4 } from 'uuid';

export interface DownloadRecordEntity extends DownloadRecord {
  created_at: string;
}

export interface CreateDownloadRecordData {
  filename: string;
  version: string;
  platform: string;
  architecture: string;
  channel: string;
  download_time: string;
  client_ip: string;
  user_agent: string;
  file_size: number;
  download_duration?: number;
}

export class DownloadRecordDAO extends BaseDAO {
  constructor() {
    super('download_records');
  }

  /**
   * 创建下载记录
   */
  async createDownloadRecord(data: CreateDownloadRecordData): Promise<string> {
    this.logOperation('createDownloadRecord', data);

    const recordId = uuidv4();

    // 明确指定要插入的字段，排除 created_at（它有默认值）
    const sql = `
      INSERT INTO download_records (
        id, filename, version, platform, architecture, channel,
        download_time, client_ip, user_agent, file_size, download_duration
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      recordId,
      data.filename,
      data.version,
      data.platform,
      data.architecture,
      data.channel,
      data.download_time,
      data.client_ip,
      data.user_agent,
      data.file_size,
      data.download_duration || null
    ];

    await this.execute(sql, values);
    return recordId;
  }

  /**
   * 根据ID获取下载记录
   */
  async getDownloadRecordById(id: string): Promise<DownloadRecordEntity | undefined> {
    this.logOperation('getDownloadRecordById', { id });
    return await this.findById<DownloadRecordEntity>(id);
  }

  /**
   * 根据查询条件获取下载记录
   */
  async getDownloadRecords(query: DownloadStatsQuery = {}): Promise<DownloadRecordEntity[]> {
    this.logOperation('getDownloadRecords', query);
    
    let sql = 'SELECT * FROM download_records WHERE 1=1';
    const params: any[] = [];
    
    if (query.startDate) {
      sql += ' AND download_time >= ?';
      params.push(query.startDate);
    }
    
    if (query.endDate) {
      sql += ' AND download_time <= ?';
      params.push(query.endDate);
    }
    
    if (query.version) {
      sql += ' AND version = ?';
      params.push(query.version);
    }
    
    if (query.platform) {
      sql += ' AND platform = ?';
      params.push(query.platform);
    }
    
    if (query.channel) {
      sql += ' AND channel = ?';
      params.push(query.channel);
    }
    
    sql += ' ORDER BY download_time DESC';
    
    if (query.limit) {
      sql += ` LIMIT ${query.limit}`;
      if (query.offset) {
        sql += ` OFFSET ${query.offset}`;
      }
    }
    
    return await this.query<DownloadRecordEntity>(sql, params);
  }

  /**
   * 获取下载统计数据
   */
  async getDownloadStats(query: DownloadStatsQuery = {}): Promise<DownloadStatsData> {
    this.logOperation('getDownloadStats', query);
    
    const records = await this.getDownloadRecords(query);
    
    const stats: DownloadStatsData = {
      totalDownloads: records.length,
      totalBytes: 0,
      byVersion: {},
      byPlatform: {},
      byChannel: {},
      byDate: {},
      recentDownloads: records.slice(0, 100) // 最近100条记录
    };
    
    for (const record of records) {
      // 总字节数
      stats.totalBytes += record.fileSize;

      // 按版本统计
      stats.byVersion[record.version] = (stats.byVersion[record.version] || 0) + 1;

      // 按平台统计
      stats.byPlatform[record.platform] = (stats.byPlatform[record.platform] || 0) + 1;

      // 按渠道统计
      stats.byChannel[record.channel] = (stats.byChannel[record.channel] || 0) + 1;

      // 按日期统计
      const date = record.downloadTime.split('T')[0]; // 提取日期部分
      if (date) {
        stats.byDate[date] = (stats.byDate[date] || 0) + 1;
      }
    }
    
    return stats;
  }

  /**
   * 获取聚合统计数据
   */
  async getAggregatedStats(query: DownloadStatsQuery = {}): Promise<DownloadStatsData> {
    this.logOperation('getAggregatedStats', query);
    
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    
    if (query.startDate) {
      whereClause += ' AND download_time >= ?';
      params.push(query.startDate);
    }
    
    if (query.endDate) {
      whereClause += ' AND download_time <= ?';
      params.push(query.endDate);
    }
    
    if (query.version) {
      whereClause += ' AND version = ?';
      params.push(query.version);
    }
    
    if (query.platform) {
      whereClause += ' AND platform = ?';
      params.push(query.platform);
    }
    
    if (query.channel) {
      whereClause += ' AND channel = ?';
      params.push(query.channel);
    }
    
    // 获取总计数据
    const totalSql = `
      SELECT COUNT(*) as total_downloads, SUM(file_size) as total_bytes
      FROM download_records ${whereClause}
    `;
    const totalResult = await this.query<{ total_downloads: number; total_bytes: number }>(totalSql, params);
    
    // 按版本统计
    const versionSql = `
      SELECT version, COUNT(*) as count
      FROM download_records ${whereClause}
      GROUP BY version
    `;
    const versionStats = await this.query<{ version: string; count: number }>(versionSql, params);
    
    // 按平台统计
    const platformSql = `
      SELECT platform, COUNT(*) as count
      FROM download_records ${whereClause}
      GROUP BY platform
    `;
    const platformStats = await this.query<{ platform: string; count: number }>(platformSql, params);
    
    // 按渠道统计
    const channelSql = `
      SELECT channel, COUNT(*) as count
      FROM download_records ${whereClause}
      GROUP BY channel
    `;
    const channelStats = await this.query<{ channel: string; count: number }>(channelSql, params);
    
    // 按日期统计
    const dateSql = `
      SELECT DATE(download_time) as date, COUNT(*) as count
      FROM download_records ${whereClause}
      GROUP BY DATE(download_time)
      ORDER BY date DESC
    `;
    const dateStats = await this.query<{ date: string; count: number }>(dateSql, params);
    
    // 最近下载记录
    const recentSql = `
      SELECT * FROM download_records ${whereClause}
      ORDER BY download_time DESC
      LIMIT 100
    `;
    const recentDownloads = await this.query<DownloadRecord>(recentSql, params);
    
    // 构建结果
    const byVersion: Record<string, number> = {};
    for (const stat of versionStats) {
      byVersion[stat.version] = stat.count;
    }
    
    const byPlatform: Record<string, number> = {};
    for (const stat of platformStats) {
      byPlatform[stat.platform] = stat.count;
    }
    
    const byChannel: Record<string, number> = {};
    for (const stat of channelStats) {
      byChannel[stat.channel] = stat.count;
    }
    
    const byDate: Record<string, number> = {};
    for (const stat of dateStats) {
      byDate[stat.date] = stat.count;
    }
    
    return {
      totalDownloads: totalResult[0]?.total_downloads || 0,
      totalBytes: totalResult[0]?.total_bytes || 0,
      byVersion,
      byPlatform,
      byChannel,
      byDate,
      recentDownloads
    };
  }

  /**
   * 批量创建下载记录
   */
  async batchCreateDownloadRecords(records: CreateDownloadRecordData[]): Promise<void> {
    this.logOperation('batchCreateDownloadRecords', { count: records.length });

    if (records.length === 0) return;

    const sql = `
      INSERT INTO download_records (
        id, filename, version, platform, architecture, channel,
        download_time, client_ip, user_agent, file_size, download_duration
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await this.transaction(async () => {
      for (const record of records) {
        const values = [
          uuidv4(),
          record.filename,
          record.version,
          record.platform,
          record.architecture,
          record.channel,
          record.download_time,
          record.client_ip,
          record.user_agent,
          record.file_size,
          record.download_duration || null
        ];

        await this.execute(sql, values);
      }
    });
  }

  /**
   * 删除旧的下载记录
   */
  async deleteOldRecords(olderThanDays: number): Promise<number> {
    this.logOperation('deleteOldRecords', { olderThanDays });
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    const sql = 'DELETE FROM download_records WHERE download_time < ?';
    const result = await this.execute(sql, [cutoffDate.toISOString()]);
    
    return result;
  }

  /**
   * 获取热门下载
   */
  async getPopularDownloads(limit: number = 10, days: number = 30): Promise<Array<{
    filename: string;
    version: string;
    platform: string;
    channel: string;
    download_count: number;
    total_bytes: number;
  }>> {
    this.logOperation('getPopularDownloads', { limit, days });
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    const sql = `
      SELECT 
        filename,
        version,
        platform,
        channel,
        COUNT(*) as download_count,
        SUM(file_size) as total_bytes
      FROM download_records
      WHERE download_time >= ?
      GROUP BY filename, version, platform, channel
      ORDER BY download_count DESC
      LIMIT ?
    `;
    
    return await this.query<{
      filename: string;
      version: string;
      platform: string;
      channel: string;
      download_count: number;
      total_bytes: number;
    }>(sql, [cutoffDate.toISOString(), limit]);
  }

  /**
   * 获取下载趋势数据
   */
  async getDownloadTrends(days: number = 30): Promise<Array<{
    date: string;
    download_count: number;
    total_bytes: number;
  }>> {
    this.logOperation('getDownloadTrends', { days });
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    const sql = `
      SELECT 
        DATE(download_time) as date,
        COUNT(*) as download_count,
        SUM(file_size) as total_bytes
      FROM download_records
      WHERE download_time >= ?
      GROUP BY DATE(download_time)
      ORDER BY date ASC
    `;
    
    return await this.query<{
      date: string;
      download_count: number;
      total_bytes: number;
    }>(sql, [cutoffDate.toISOString()]);
  }

  /**
   * 获取用户下载历史
   */
  async getUserDownloadHistory(clientIp: string, limit: number = 50): Promise<DownloadRecordEntity[]> {
    this.logOperation('getUserDownloadHistory', { clientIp, limit });
    
    return await this.findWhere<DownloadRecordEntity>(
      { client_ip: clientIp },
      'download_time DESC',
      limit
    );
  }
}
