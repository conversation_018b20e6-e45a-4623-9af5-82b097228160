/**
 * 波形缩放管理 Composable
 * 负责处理波形图的缩放功能，包括缩放级别管理、坐标转换、状态同步等
 */

import { ref, computed, watch } from "vue";
import {
  DEFAULT_ZOOM_LEVEL,
  MAX_ZOOM_LEVEL,
  ZOOM_STEP,
  ZOOM_SENSITIVITY,
  FULL_SCREEN_TIME_MS,
  MAX_VISIBLE_TIME_MULTIPLIER,
  ZOOM_DEBUG_MODE,
} from "../config/waveform-constants";
import { calculateTargetGraphWidth } from "../utils/canvas-calculations";
import { waveformLogger } from "@/utils/logger/logger";

// 缩放配置接口
export interface ZoomConfig {
  // UI状态同步函数
  updateUIState?: (updates: { zoomLevel: number }) => void;

  // 重绘函数
  redrawWaveform?: () => void;

  // 获取当前总时长的函数
  getEffectiveDuration?: () => number;

  // 动态最小缩放率计算所需参数
  getAvailableParentWidth?: () => number;
  getBaselineDuration?: () => number;
  getPaddingLeft?: () => number;
  getPaddingRight?: () => number;
  getAudioDuration?: () => number | null;

  // 调试模式
  debugMode?: boolean;
}

// 缩放状态接口
export interface ZoomState {
  currentZoom: number;
  targetZoom: number;
  isZooming: boolean;
  zoomCenter: { x: number; y: number } | null;
}

/**
 * 波形缩放管理 Composable
 */
export function useWaveformZoom(config: ZoomConfig = {}) {
  const {
    updateUIState,
    redrawWaveform,
    getEffectiveDuration,
    getAvailableParentWidth,
    getBaselineDuration,
    getPaddingLeft,
    getPaddingRight,
    getAudioDuration,
    debugMode = ZOOM_DEBUG_MODE, // 使用配置文件中的调试模式设置
  } = config;

  // 缩放状态
  const currentZoom = ref<number>(DEFAULT_ZOOM_LEVEL);
  const targetZoom = ref<number>(DEFAULT_ZOOM_LEVEL);
  const isZooming = ref<boolean>(false);
  const zoomCenter = ref<{ x: number; y: number } | null>(null);

  // 动态最小缩放率
  const dynamicMinZoomLevel = ref<number>(0.1); // 默认值，与原 MIN_ZOOM_LEVEL 相同

  // 计算动态最小缩放率
  const calculateDynamicMinZoom = (): number => {
    // 如果缺少必要的函数，返回默认值
    if (!getAvailableParentWidth || !getEffectiveDuration || !getBaselineDuration || !getPaddingLeft || !getPaddingRight) {
      if (debugMode) {
        waveformLogger.debug(`缺少必要函数，返回默认值 0.1`, {
          hasAvailableParentWidth: !!getAvailableParentWidth,
          hasEffectiveDuration: !!getEffectiveDuration,
          hasBaselineDuration: !!getBaselineDuration,
          hasPaddingLeft: !!getPaddingLeft,
          hasPaddingRight: !!getPaddingRight,
        });
      }
      return 0.1; // 默认最小缩放率
    }

    try {
      const availableWidth = getAvailableParentWidth();
      const totalDuration = getEffectiveDuration();
      const baselineDuration = getBaselineDuration();
      const paddingLeft = getPaddingLeft();
      const paddingRight = getPaddingRight();
      const audioDuration = getAudioDuration ? getAudioDuration() : null;

      if (debugMode) {
        waveformLogger.debug(`获取到的参数值`, {
          availableWidth,
          totalDuration,
          baselineDuration,
          paddingLeft,
          paddingRight,
          audioDuration,
        });
      }

      if (availableWidth <= 0 || totalDuration <= 0 || baselineDuration <= 0) {
        if (debugMode) {
          waveformLogger.debug(`参数值无效，返回默认值 0.1`, {
            availableWidth,
            totalDuration,
            baselineDuration,
          });
        }
        return 0.1; // 无效参数时返回默认值
      }

      // 当文件时长 ≤ FULL_SCREEN_TIME_MS 时，强制使用 1.0 作为最小缩放率
      // 确保内容铺满父元素宽度，符合 FULL_SCREEN_TIME_MS 的设计原则
      if (totalDuration <= FULL_SCREEN_TIME_MS) {
        if (debugMode) {
          waveformLogger.debug(`文件时长 ${totalDuration}ms ≤ ${FULL_SCREEN_TIME_MS}ms，强制使用最小缩放率 1.0`);
        }
        return 1.0;
      }

      // 计算当前内容的逻辑宽度（使用与画布计算相同的逻辑）
      const logicalWidth = calculateTargetGraphWidth(availableWidth, totalDuration, baselineDuration, paddingLeft, paddingRight, audioDuration);

      // 计算使内容刚好铺满父元素宽度的缩放率
      // 当 logicalWidth * zoomLevel = availableWidth 时，内容刚好铺满
      const minZoom = availableWidth / logicalWidth;

      // 确保最小缩放率不会太小（设置下限为 0.01）
      const clampedMinZoom = Math.max(0.01, minZoom);

      if (debugMode) {
        waveformLogger.debug(`计算动态最小缩放率成功`, {
          availableWidth,
          totalDuration,
          baselineDuration,
          logicalWidth,
          calculatedMinZoom: minZoom,
          clampedMinZoom,
          willReachOneToOneRatio: Math.abs(logicalWidth * clampedMinZoom - availableWidth) < 1,
        });
      }

      return clampedMinZoom;
    } catch (error) {
      if (debugMode) {
        waveformLogger.error(`计算动态最小缩放率时出错:`, error);
      }
      return 0.1; // 出错时返回默认值
    }
  };

  // 计算基于可视时长的最小缩放率
  const calculateVisibleTimeBasedMinZoom = (): number => {
    // 如果缺少必要的函数，返回默认值
    if (!getAvailableParentWidth || !getEffectiveDuration || !getBaselineDuration || !getPaddingLeft || !getPaddingRight) {
      if (debugMode) {
        waveformLogger.debug(`计算可视时长最小缩放率 - 缺少必要函数，返回默认值 0.1`);
      }
      return 0.1; // 默认最小缩放率
    }

    try {
      const availableWidth = getAvailableParentWidth();
      const totalDuration = getEffectiveDuration();
      const baselineDuration = getBaselineDuration();
      const paddingLeft = getPaddingLeft();
      const paddingRight = getPaddingRight();
      const audioDuration = getAudioDuration ? getAudioDuration() : null;

      if (availableWidth <= 0 || totalDuration <= 0 || baselineDuration <= 0) {
        if (debugMode) {
          waveformLogger.debug(`计算可视时长最小缩放率 - 参数值无效，返回默认值 0.1`);
        }
        return 0.1; // 无效参数时返回默认值
      }

      // 计算最大允许的可视时长
      const maxVisibleTime = FULL_SCREEN_TIME_MS * MAX_VISIBLE_TIME_MULTIPLIER;

      // 计算当前内容的逻辑宽度
      const logicalWidth = calculateTargetGraphWidth(availableWidth, totalDuration, baselineDuration, paddingLeft, paddingRight, audioDuration);

      // 计算可视区域的绘图宽度（去除padding）
      const visibleDrawingWidth = availableWidth - paddingLeft - paddingRight;

      // 计算时间像素比（每毫秒对应多少像素）
      const timePixelRatio = (logicalWidth - paddingLeft - paddingRight) / totalDuration;

      // 计算使可视范围内时长不超过 maxVisibleTime 的最小缩放率
      // 公式：visibleDrawingWidth / zoomLevel / timePixelRatio <= maxVisibleTime
      // 即：zoomLevel >= visibleDrawingWidth / (maxVisibleTime * timePixelRatio)
      const minZoomForVisibleTime = visibleDrawingWidth / (maxVisibleTime * timePixelRatio);

      // 确保最小缩放率不会太小（设置下限为 0.01）
      const clampedMinZoom = Math.max(0.01, minZoomForVisibleTime);

      if (debugMode) {
        waveformLogger.debug(`计算可视时长最小缩放率成功`, {
          availableWidth,
          totalDuration,
          maxVisibleTime,
          logicalWidth,
          visibleDrawingWidth,
          timePixelRatio,
          calculatedMinZoom: minZoomForVisibleTime,
          clampedMinZoom,
          visibleTimeAtMinZoom: visibleDrawingWidth / clampedMinZoom / timePixelRatio,
        });
      }

      return clampedMinZoom;
    } catch (error) {
      if (debugMode) {
        waveformLogger.error(`计算可视时长最小缩放率时出错:`, error);
      }
      return 0.1; // 出错时返回默认值
    }
  };

  // 更新动态最小缩放率
  const updateDynamicMinZoom = (): void => {
    // 计算两种最小缩放率限制
    const dynamicMinZoom = calculateDynamicMinZoom();
    const visibleTimeMinZoom = calculateVisibleTimeBasedMinZoom();

    // 取两者中较大的值作为最终的最小缩放率
    const finalMinZoom = Math.max(dynamicMinZoom, visibleTimeMinZoom);

    dynamicMinZoomLevel.value = finalMinZoom;

    if (debugMode) {
      waveformLogger.debug(`更新最小缩放率限制`, {
        dynamicMinZoom,
        visibleTimeMinZoom,
        finalMinZoom,
        appliedLimit: finalMinZoom === dynamicMinZoom ? "dynamic" : "visibleTime",
      });
    }

    // 如果当前缩放率小于新的最小值，立即调整到最小值
    if (currentZoom.value < finalMinZoom) {
      setZoomLevel(finalMinZoom);
    }
  };

  // 计算属性
  const zoomState = computed<ZoomState>(() => ({
    currentZoom: currentZoom.value,
    targetZoom: targetZoom.value,
    isZooming: isZooming.value,
    zoomCenter: zoomCenter.value,
  }));

  // 缩放级别是否在有效范围内
  const isValidZoomLevel = (zoom: number): boolean => {
    return zoom >= dynamicMinZoomLevel.value && zoom <= MAX_ZOOM_LEVEL;
  };

  // 限制缩放级别在有效范围内
  const clampZoomLevel = (zoom: number): number => {
    return Math.max(dynamicMinZoomLevel.value, Math.min(MAX_ZOOM_LEVEL, zoom));
  };

  // 检查是否允许缩放
  const isZoomAllowed = (): boolean => {
    if (!getEffectiveDuration) return true; // 如果没有提供时长函数，默认允许缩放

    const currentDuration = getEffectiveDuration();
    return currentDuration > FULL_SCREEN_TIME_MS;
  };

  // 计算缩放后的坐标转换因子
  const getZoomTransform = (zoom: number = currentZoom.value) => {
    return {
      scaleX: zoom,
      scaleY: 1, // Y轴不缩放，保持强度值的可读性
    };
  };

  // 设置缩放级别（立即设置，无动画）
  const setZoomLevel = (zoom: number, center?: { x: number; y: number }): void => {
    const clampedZoom = clampZoomLevel(zoom);

    // 避免设置相同的缩放级别
    if (Math.abs(currentZoom.value - clampedZoom) < 0.001) {
      return;
    }

    if (debugMode) {
      waveformLogger.debug(`设置缩放级别: ${clampedZoom}`, { center });
    }

    currentZoom.value = clampedZoom;
    targetZoom.value = clampedZoom;
    zoomCenter.value = center || null;

    // 同步到UI状态
    if (updateUIState) {
      updateUIState({ zoomLevel: clampedZoom });
    }

    // 触发重绘
    if (redrawWaveform) {
      redrawWaveform();
    }
  };

  // 平滑缩放到目标级别（带动画）
  const zoomToLevel = (zoom: number, center?: { x: number; y: number }): void => {
    const clampedZoom = clampZoomLevel(zoom);

    if (Math.abs(currentZoom.value - clampedZoom) < 0.01) {
      // 缩放变化太小，直接设置
      setZoomLevel(clampedZoom, center);
      return;
    }

    if (debugMode) {
      waveformLogger.debug(`平滑缩放到: ${clampedZoom}`, {
        from: currentZoom.value,
        center,
      });
    }

    // 对于滚轮缩放，使用立即设置而不是动画，避免状态不同步
    setZoomLevel(clampedZoom, center);
  };

  // 相对缩放（基于当前缩放级别）
  const zoomBy = (delta: number, center?: { x: number; y: number }): void => {
    const newZoom = currentZoom.value + delta;
    zoomToLevel(newZoom, center);
  };

  // 缩放步进操作
  const zoomIn = (center?: { x: number; y: number }): void => {
    zoomBy(ZOOM_STEP, center);
  };

  const zoomOut = (center?: { x: number; y: number }): void => {
    zoomBy(-ZOOM_STEP, center);
  };

  // 重置缩放到默认级别
  const resetZoom = (): void => {
    zoomToLevel(DEFAULT_ZOOM_LEVEL);
  };

  // 处理滚轮缩放事件
  const handleWheelZoom = (event: WheelEvent, mouseX: number, mouseY: number): void => {
    // 检查是否允许缩放
    if (!isZoomAllowed()) {
      if (debugMode) {
        const currentDuration = getEffectiveDuration ? getEffectiveDuration() : 0;
        waveformLogger.debug(`缩放被禁用 - 当前时长: ${currentDuration}ms, 阈值: ${FULL_SCREEN_TIME_MS}ms`);
      }
      return; // 不允许缩放时直接返回
    }

    // 计算缩放变化量
    const zoomDelta = -Math.sign(event.deltaY) * ZOOM_SENSITIVITY;

    if (debugMode) {
      waveformLogger.debug(`滚轮缩放事件`, {
        deltaY: event.deltaY,
        zoomDelta,
        currentZoom: currentZoom.value,
        mousePosition: { x: mouseX, y: mouseY },
      });
    }

    // 以鼠标位置为中心进行缩放
    zoomBy(zoomDelta, { x: mouseX, y: mouseY });
  };

  // 从UI状态恢复缩放级别
  const restoreZoomLevel = (zoom: number): void => {
    const clampedZoom = clampZoomLevel(zoom);

    if (debugMode) {
      waveformLogger.debug(`恢复缩放级别: ${clampedZoom}`);
    }

    currentZoom.value = clampedZoom;
    targetZoom.value = clampedZoom;
  };

  // 延迟初始化动态最小缩放率，确保所有参数都准备好
  const initializeDynamicMinZoom = () => {
    // 检查所有必要的参数函数是否都已准备好
    if (getAvailableParentWidth && getEffectiveDuration && getBaselineDuration && getPaddingLeft && getPaddingRight) {
      try {
        const availableWidth = getAvailableParentWidth();
        const totalDuration = getEffectiveDuration();
        const baselineDuration = getBaselineDuration();

        if (debugMode) {
          waveformLogger.debug(`初始化参数检查`, {
            availableWidth,
            totalDuration,
            baselineDuration,
            hasAllFunctions: true,
          });
        }

        if (availableWidth > 0 && totalDuration > 0 && baselineDuration > 0) {
          updateDynamicMinZoom();
          return true;
        }
      } catch (error) {
        if (debugMode) {
          waveformLogger.warn(`初始化时参数获取失败:`, error);
        }
      }
    }

    if (debugMode) {
      waveformLogger.debug(`初始化延迟，参数未准备好`);
    }
    return false;
  };

  // 初始化状态跟踪
  const isInitialized = ref(false);

  // 尝试立即初始化
  const performInitialization = () => {
    const success = initializeDynamicMinZoom();
    isInitialized.value = success;
    return success;
  };

  performInitialization();

  // 监听总时长变化，动态更新最小缩放率
  if (getEffectiveDuration) {
    watch(
      () => getEffectiveDuration!(),
      () => {
        // 如果之前没有成功初始化，先尝试初始化
        if (!isInitialized.value) {
          performInitialization();
        } else {
          updateDynamicMinZoom();
        }
      },
      { immediate: false }
    );
  }

  // 获取当前缩放信息
  const getZoomInfo = () => ({
    current: currentZoom.value,
    target: targetZoom.value,
    isZooming: isZooming.value,
    center: zoomCenter.value,
    min: dynamicMinZoomLevel.value,
    max: MAX_ZOOM_LEVEL,
    step: ZOOM_STEP,
    canZoomIn: currentZoom.value < MAX_ZOOM_LEVEL,
    canZoomOut: currentZoom.value > dynamicMinZoomLevel.value,
  });

  return {
    // 状态
    currentZoom,
    targetZoom,
    isZooming,
    zoomCenter,
    zoomState,

    // 核心方法
    setZoomLevel,
    zoomToLevel,
    zoomBy,
    zoomIn,
    zoomOut,
    resetZoom,
    handleWheelZoom,
    restoreZoomLevel,

    // 工具方法
    isValidZoomLevel,
    clampZoomLevel,
    getZoomTransform,
    getZoomInfo,

    // 动态最小缩放率相关
    updateDynamicMinZoom,
    calculateDynamicMinZoom,
    calculateVisibleTimeBasedMinZoom,
    initializeDynamicMinZoom,
    dynamicMinZoomLevel,

    // 常量
    MIN_ZOOM: dynamicMinZoomLevel.value,
    MAX_ZOOM: MAX_ZOOM_LEVEL,
    DEFAULT_ZOOM: DEFAULT_ZOOM_LEVEL,
    ZOOM_STEP,
  };
}
