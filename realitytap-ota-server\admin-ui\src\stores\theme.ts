import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const isDark = ref<boolean>(
    localStorage.getItem('theme') === 'dark' ||
    (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)
  )

  // 计算属性
  const theme = computed(() => isDark.value ? 'dark' : 'light')

  // 切换主题
  const toggleTheme = () => {
    isDark.value = !isDark.value
    localStorage.setItem('theme', theme.value)
  }

  // 设置主题
  const setTheme = (newTheme: 'light' | 'dark') => {
    isDark.value = newTheme === 'dark'
    localStorage.setItem('theme', newTheme)
  }

  return {
    isDark: computed(() => isDark.value),
    theme,
    toggleTheme,
    setTheme,
  }
})
