# RealityTap 触觉反馈设计系统

<div align="center">

![RealityTap Logo](https://img.shields.io/badge/RealityTap-触觉设计工具-blue?style=for-the-badge)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Rust](https://img.shields.io/badge/Rust-000000?style=for-the-badge&logo=rust&logoColor=white)](https://www.rust-lang.org/)
[![Vue.js](https://img.shields.io/badge/Vue.js-35495E?style=for-the-badge&logo=vuedotjs&logoColor=4FC08D)](https://vuejs.org/)
[![Tauri](https://img.shields.io/badge/Tauri-24C8DB?style=for-the-badge&logo=tauri&logoColor=white)](https://tauri.app/)

</div>

## 📖 项目概述

RealityTap 触觉反馈设计系统是一个专为**触觉设计（Haptic Design）**打造的现代化工具链，提供从创作到分发的完整解决方案。该设计系统包含桌面创作工具、自动更新服务和共享组件库，采用现代化技术栈构建，注重性能、安全性和用户体验。

### ✨ 核心特色

- 🎨 **专业触觉设计工具** - 基于 Tauri + Vue.js 的现代化桌面应用
- 🚀 **现代化 OTA 更新服务** - 基于 SQLite 数据库，可靠且易于维护
- 🔧 **类型安全的共享库** - TypeScript 类型定义确保开发效率
- 🌐 **跨平台支持** - Windows、macOS、Linux 全平台覆盖
- 📦 **容器化部署** - Docker 支持，一键部署
- 🔒 **安全可靠** - 文件完整性校验，安全的更新机制

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph UserLayer["用户层"]
        U1[Windows 用户]
        U2[macOS 用户]
        U3[Linux 用户]
    end

    subgraph AppLayer["应用层"]
        DT["RealityTap Desktop<br/>Tauri + Vue.js<br/>tauri-plugin-updater"]
    end

    subgraph ServiceLayer["服务层"]
        OTA["OTA 更新服务<br/>Node.js + Express<br/>SQLite 数据库"]
    end

    subgraph SharedLayer["共享层"]
        SHARED["共享类型库<br/>@realitytap/shared"]
    end

    subgraph StorageLayer["数据存储层"]
        DB["SQLite 数据库<br/>版本管理 + 统计数据"]
        FS["文件系统<br/>更新包 + 日志文件"]
    end

    U1 --> DT
    U2 --> DT
    U3 --> DT

    DT --> OTA
    DT -.-> SHARED
    OTA -.-> SHARED
    OTA --> DB
    OTA --> FS

    style DT fill:#e1f5fe
    style OTA fill:#f3e5f5
    style SHARED fill:#e8f5e8
    style DB fill:#fff8e1
    style FS fill:#fff3e0
```

### 项目结构

```
realitytap-ecosystem/
├── realitytap-desktop/          # 🎨 桌面创作工具
│   ├── src/                     # Vue.js 前端源码
│   │   ├── components/          # Vue 组件
│   │   ├── stores/              # Pinia 状态管理
│   │   ├── router/              # Vue Router 路由
│   │   └── utils/               # 工具函数
│   ├── src-tauri/               # Rust 后端源码
│   │   ├── src/                 # Rust 源码
│   │   ├── Cargo.toml           # Rust 依赖配置
│   │   └── tauri.conf.json      # Tauri 配置
│   ├── docs/                    # 技术文档
│   └── package.json             # Node.js 依赖
├── realitytap-ota-server/       # 🚀 OTA 更新服务
│   ├── src/                     # TypeScript 源码
│   │   ├── controllers/         # API 控制器
│   │   ├── services/            # 业务逻辑服务
│   │   ├── middleware/          # Express 中间件
│   │   └── utils/               # 工具函数
│   ├── storage/                 # 文件存储目录
│   │   ├── releases/            # 发布文件
│   │   ├── metadata/            # 元数据文件
│   │   └── logs/                # 日志文件
│   ├── scripts/                 # 部署脚本
│   ├── tests/                   # 测试文件
│   ├── Dockerfile               # Docker 配置
│   └── docker-compose.yml       # Docker Compose 配置
└── realitytap-shared/           # 🔧 共享类型库
    ├── src/                     # TypeScript 源码
    │   ├── types/               # 类型定义
    │   ├── schemas/             # 数据验证模式
    │   ├── constants/           # 共享常量
    │   ├── utils/               # 工具函数
    │   └── validators/          # 数据验证器
    └── dist/                    # 编译输出
```

## 🎯 子项目详细介绍

### 🎨 RealityTap Desktop

**专业的触觉设计桌面应用**

RealityTap Desktop 是一个基于 Tauri 框架构建的现代化桌面应用，专为触觉效果设计而打造。

#### 核心功能
- 📝 **项目管理** - 创建、编辑、保存触觉设计项目
- 🎵 **音频处理** - 支持多种音频格式的导入和处理
- 🔧 **效果编辑** - 直观的触觉效果编辑界面
- 📱 **设备连接** - 支持多种触觉设备的连接和测试
- 🌍 **国际化** - 多语言支持

#### 技术栈
- **前端**: Vue.js 3.5.13 + TypeScript + Naive UI
- **状态管理**: Pinia 3.0.2
- **路由**: Vue Router 4
- **构建工具**: Vite 6.3.1
- **后端**: Rust + Tauri 2.5.0
- **音频处理**: Symphonia + Hound + RustFFT

#### 特色亮点
- ⚡ **高性能** - Rust 后端确保出色的性能表现
- 🔒 **安全可靠** - Tauri 提供的安全沙箱环境
- 🎨 **现代化 UI** - 基于 Naive UI 的精美界面
- 📦 **轻量级** - 相比 Electron 更小的安装包体积

### 🚀 RealityTap OTA Server

**现代化的自动更新服务**

RealityTap OTA Server 是一个专为桌面应用设计的现代化 OTA（Over-The-Air）更新服务器，采用 SQLite 数据库确保数据可靠性和一致性。

#### 核心功能
- 🔍 **版本检查** - 多平台、多架构的版本比较和更新检测
- 📦 **文件分发** - 高效的更新包下载服务，支持断点续传
- 🔒 **安全验证** - SHA256 文件完整性校验和数字签名
- 📊 **统计分析** - 基于数据库的详细更新统计和分析
- 🚀 **多渠道支持** - stable/beta/alpha 发布渠道管理
- 🔄 **Tauri 兼容** - 完全兼容 tauri-plugin-updater 规范

#### 技术栈
- **后端框架**: Node.js + TypeScript + Express
- **数据存储**: SQLite 数据库 + 文件系统
- **文件存储**: 本地文件系统
- **容器化**: Docker + Docker Compose

#### 设计理念
- 🎯 **现代化架构** - 基于 SQLite 的可靠数据存储
- 📊 **数据一致性** - 事务支持确保数据完整性
- ⚙️ **易于维护** - 结构化数据存储，便于查询和管理
- 🐳 **容器友好** - 单一进程，易于容器化部署
- 🔄 **平滑迁移** - 支持从 JSON 存储无缝迁移到数据库

### 🔧 RealityTap Shared

**类型安全的共享组件库**

RealityTap Shared 是整个设计系统的共享类型定义和工具函数库，确保各组件间的类型安全和代码复用。

#### 核心功能
- 📋 **类型定义** - 统一的 TypeScript 类型定义
- 🛡️ **数据验证** - 基于 Zod 的数据验证模式
- 🔧 **工具函数** - 版本比较、校验和计算等通用工具
- 📡 **API 接口** - 标准化的 API 接口定义

#### 包含模块
- **types/** - 版本、OTA、API、平台相关类型
- **schemas/** - 数据验证模式定义
- **constants/** - API 端点、错误代码等常量
- **utils/** - 版本比较、平台检测等工具函数
- **validators/** - 数据格式验证器

#### 技术特色
- 🔒 **类型安全** - 完整的 TypeScript 类型覆盖
- ✅ **数据验证** - 运行时数据验证确保可靠性
- 🔄 **代码复用** - 避免重复代码，提高开发效率
- 📦 **模块化设计** - 清晰的模块划分，易于维护

## 🛠️ 技术栈总览

### 前端技术
- **Vue.js 3.5.13** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Naive UI 2.41.0** - 现代化的 Vue 3 组件库
- **Pinia 3.0.2** - Vue 3 官方状态管理库
- **Vue Router 4** - Vue.js 官方路由管理器
- **Vite 6.3.1** - 下一代前端构建工具

### 后端技术
- **Rust** - 系统级编程语言，高性能和内存安全
- **Tauri 2.5.0** - 构建跨平台桌面应用的框架
- **Node.js** - JavaScript 运行时环境
- **Express** - 快速、极简的 Web 框架

### 开发工具
- **Docker** - 容器化平台
- **Jest** - JavaScript 测试框架
- **ESLint** - 代码质量检查工具
- **Zod** - TypeScript 优先的数据验证库

### 音频处理
- **Symphonia** - Rust 音频解码库
- **Hound** - WAV 音频文件处理
- **RustFFT** - 快速傅里叶变换库

## 🚀 快速开始

### 环境要求

- **Node.js** >= 18.0.0
- **Rust** >= 1.70.0
- **SQLite** >= 3.35.0 (通常随系统自带)
- **npm** 或 **yarn** 或 **pnpm**
- **Docker** (可选，用于容器化部署)

### 一键启动整个系统

1. **克隆项目**
```bash
git clone https://github.com/your-org/realitytap-ecosystem.git
cd realitytap-ecosystem
```

2. **安装依赖**
```bash
# 安装共享库依赖
cd realitytap-shared
npm install
npm run build
cd ..

# 安装桌面应用依赖
cd realitytap-desktop
npm install
cd ..

# 安装 OTA 服务依赖
cd realitytap-ota-server
npm install

# 初始化数据库
npm run db:init
cd ..
```

3. **启动 OTA 服务**
```bash
cd realitytap-ota-server
npm run dev
# 服务将在 http://localhost:3000 启动
```

4. **启动桌面应用**
```bash
cd realitytap-desktop
npm run tauri:dev
# 桌面应用将自动启动
```

### Docker 快速部署

使用 Docker Compose 一键启动 OTA 服务：

```bash
cd realitytap-ota-server
docker-compose up -d
```

### 验证安装

1. **检查 OTA 服务**
```bash
curl http://localhost:3000/health
```

2. **检查桌面应用**
   - 桌面应用应该自动启动并显示主界面
   - 尝试创建一个新项目验证功能正常

## 👨‍💻 开发指南

### 开发环境搭建

1. **安装 Rust 开发环境**
```bash
# 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 安装 Tauri CLI
cargo install tauri-cli
```

2. **安装 Node.js 开发环境**
```bash
# 使用 nvm 安装 Node.js
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

3. **配置开发工具**
```bash
# 安装全局工具
npm install -g typescript eslint prettier
```

### 开发工作流

#### 桌面应用开发

```bash
cd realitytap-desktop

# 开发模式启动
npm run tauri:dev

# 构建应用
npm run tauri:build

# 运行测试
npm run test

# 代码检查
npm run lint
```

#### OTA 服务开发

```bash
cd realitytap-ota-server

# 开发模式启动
npm run dev

# 构建项目
npm run build

# 运行测试
npm run test

# 代码检查
npm run lint

# 数据库相关命令
npm run db:init          # 初始化数据库
npm run db:migrate       # 执行数据库迁移
npm run db:seed          # 填充测试数据
npm run migrate:to-sqlite # 从 JSON 迁移到 SQLite
npm run migrate:validate  # 验证迁移结果
```

#### 共享库开发

```bash
cd realitytap-shared

# 监听模式构建
npm run dev

# 构建库
npm run build

# 运行测试
npm run test
```

### 代码规范

- **TypeScript** - 所有 JavaScript 代码使用 TypeScript
- **ESLint** - 遵循项目 ESLint 配置
- **Prettier** - 统一代码格式化
- **Conventional Commits** - 提交信息遵循约定式提交规范

## 🚀 部署指南

### 生产环境部署

#### OTA 服务部署

**方式一：Docker 部署（推荐）**

```bash
# 构建镜像
cd realitytap-ota-server
docker build -t realitytap-ota-server:latest .

# 运行容器
docker run -d \
  --name realitytap-ota \
  -p 3000:3000 \
  -v $(pwd)/storage:/app/storage \
  -v $(pwd)/database:/app/database \
  -e NODE_ENV=production \
  -e DB_ENABLED=true \
  -e DB_PATH=/app/database/ota.db \
  realitytap-ota-server:latest
```

**方式二：传统部署**

```bash
# 构建项目
npm run build

# 使用 PM2 管理进程
npm install -g pm2
pm2 start ecosystem.config.js

# 或直接启动
npm run start:prod
```

#### 桌面应用分发

```bash
cd realitytap-desktop

# 构建所有平台
npm run tauri:build

# 构建特定平台
npm run tauri:build -- --target x86_64-pc-windows-msvc  # Windows
npm run tauri:build -- --target x86_64-apple-darwin     # macOS Intel
npm run tauri:build -- --target aarch64-apple-darwin    # macOS Apple Silicon
npm run tauri:build -- --target x86_64-unknown-linux-gnu # Linux
```

### 反向代理配置

#### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name ota.realitytap.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ota.realitytap.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 大文件上传支持
    client_max_body_size 1G;
    proxy_read_timeout 300s;
    proxy_connect_timeout 75s;
}
```

## 🗄️ 数据库管理

### 数据库初始化

首次部署时需要初始化数据库：

```bash
cd realitytap-ota-server

# 初始化数据库结构
npm run db:init

# 填充基础数据
npm run db:seed
```

### 从 JSON 迁移到 SQLite

如果您之前使用的是 JSON 存储，可以使用迁移工具：

```bash
# 执行迁移（包含备份）
npm run migrate:to-sqlite

# 验证迁移结果
npm run migrate:validate

# 清理旧的 JSON 文件（可选）
npm run migrate:cleanup
```

### 数据库备份与恢复

```bash
# 创建数据库备份
npm run db:backup

# 从备份恢复
npm run db:restore -- --backup-file=backup-2024-01-15.db

# 查看备份列表
npm run db:list-backups
```

### 数据库维护

```bash
# 数据库健康检查
npm run db:health

# 优化数据库
npm run db:optimize

# 查看数据库统计
npm run db:stats
```
## 📡 API 文档

### OTA 更新 API

#### Tauri Plugin Updater API (推荐)

```http
GET /api/v1/updates/{target}/{current_version}
```

**路径参数：**
- `target`: 目标平台 (windows-x86_64/darwin-x86_64/linux-x86_64 等)
- `current_version`: 当前版本号

**查询参数：**
- `channel`: 发布渠道 (stable/beta/alpha，默认 stable)

**响应示例：**
```json
{
  "version": "1.2.0",
  "notes": "修复了若干问题，增加了新功能",
  "pub_date": "2024-01-15T10:30:00.000Z",
  "platforms": {
    "windows-x86_64": {
      "signature": "dW50cnVzdGVkIGNvbW1lbnQ6IHNpZ25hdHVyZSBmcm9tIHRhdXJpIHNlY3JldCBrZXkK...",
      "url": "https://releases.realitytap.com/api/v1/download/realitytap-1.2.0-windows-x86_64.msi"
    }
  }
}
```

#### 传统版本检查 API (兼容)

```http
GET /api/v1/version/check
```

**查询参数：**
- `platform`: 平台类型 (windows/macos/linux)
- `arch`: 架构类型 (x86_64/aarch64)
- `current_version`: 当前版本号
- `channel`: 发布渠道 (stable/beta/alpha)

**响应示例：**
```json
{
  "success": true,
  "data": {
    "has_update": true,
    "latest_version": "1.2.0",
    "download_url": "/api/v1/download/realitytap-1.2.0-windows-x86_64.msi",
    "checksum": "sha256:abc123...",
    "signature": "dW50cnVzdGVkIGNvbW1lbnQ6IHNpZ25hdHVyZSBmcm9tIHRhdXJpIHNlY3JldCBrZXkK...",
    "release_notes": "修复了若干问题，增加了新功能",
    "file_size": 52428800
  }
}
```

#### 文件下载

```http
GET /api/v1/download/:filename
```

**特性：**
- 支持 HTTP 范围请求（断点续传）
- 自动文件完整性验证
- 下载统计记录

### 健康检查

```http
GET /health
```

**响应示例：**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "version": "1.0.0",
  "uptime": 3600
}
```

## 🔄 OTA 更新流程

```mermaid
sequenceDiagram
    participant Desktop as RealityTap Desktop
    participant OTA as OTA Server
    participant Storage as File Storage

    Desktop->>OTA: 1. 检查更新 (GET /api/v1/version/check)
    Note over Desktop,OTA: 携带当前版本、平台、架构信息

    OTA->>Storage: 2. 读取版本元数据
    Storage-->>OTA: 3. 返回版本信息

    alt 有新版本
        OTA-->>Desktop: 4. 返回更新信息
        Note over OTA,Desktop: 包含下载链接、校验和、发布说明

        Desktop->>OTA: 5. 下载更新包 (GET /api/v1/download/:filename)
        Note over Desktop,OTA: 支持断点续传

        OTA->>Storage: 6. 读取更新文件
        Storage-->>OTA: 7. 返回文件流
        OTA-->>Desktop: 8. 传输文件数据

        Desktop->>Desktop: 9. 验证文件完整性
        Desktop->>Desktop: 10. 安装更新

        Desktop->>OTA: 11. 反馈安装结果 (可选)
        OTA->>Storage: 12. 记录更新统计
    else 无新版本
        OTA-->>Desktop: 4. 返回无更新信息
    end
```

## 🔄 技术迁移说明

### SQLite 数据库迁移

RealityTap OTA Server 已从 JSON 文件存储迁移到 SQLite 数据库，提供更好的数据一致性和查询性能。

#### 迁移优势

- **数据一致性**: 事务支持确保数据完整性
- **查询性能**: 索引优化提升查询速度
- **并发支持**: 更好的多用户访问支持
- **数据分析**: 支持复杂的统计查询
- **备份恢复**: 标准化的备份和恢复机制

#### 迁移步骤

1. **备份现有数据**
   ```bash
   # 自动备份 JSON 文件
   npm run migrate:backup
   ```

2. **执行迁移**
   ```bash
   # 迁移数据到 SQLite
   npm run migrate:to-sqlite
   ```

3. **验证迁移**
   ```bash
   # 验证数据完整性
   npm run migrate:validate
   ```

4. **清理旧文件**
   ```bash
   # 清理 JSON 文件（可选）
   npm run migrate:cleanup
   ```

### Tauri Plugin Updater 集成

RealityTap Desktop 已集成 tauri-plugin-updater 插件，提供标准化的更新机制。

#### 主要变更

- **标准化 API**: 兼容 Tauri 官方更新规范
- **数字签名**: 支持更新包数字签名验证
- **多端点支持**: 支持主备更新服务器
- **自动重试**: 内置重试机制提高成功率

#### 配置说明

桌面应用的更新配置位于 `tauri.conf.json`:

```json
{
  "plugins": {
    "updater": {
      "active": true,
      "endpoints": [
        "https://releases.realitytap.com/api/v1/updates/{{target}}/{{current_version}}",
        "https://backup-releases.realitytap.com/api/v1/updates/{{target}}/{{current_version}}"
      ],
      "dialog": false,
      "pubkey": "your-public-key-here"
    }
  }
}
```

## 📚 相关文档

- [桌面应用技术架构文档](./realitytap-desktop/docs/Technical_Architecture.md)
- [OTA 服务详细文档](./realitytap-ota-server/README.md)
- [共享库使用指南](./realitytap-shared/README.md)
- [Tauri Plugin Updater 迁移指南](./realitytap-ota-server/TAURI_UPDATER_MIGRATION.md)
- [数据库迁移脚本文档](./realitytap-ota-server/scripts/README.md)

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

### 贡献流程

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/amazing-feature`)
3. **提交更改** (`git commit -m 'Add some amazing feature'`)
4. **推送到分支** (`git push origin feature/amazing-feature`)
5. **创建 Pull Request**

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明：**
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 代码审查

所有 Pull Request 都需要经过代码审查：
- 确保代码符合项目规范
- 通过所有自动化测试
- 至少一位维护者的批准
---

<div align="center">

**🎨 用 RealityTap 创造触觉的未来 🚀**

Made with ❤️ by the AWA Customer Support Team

</div>