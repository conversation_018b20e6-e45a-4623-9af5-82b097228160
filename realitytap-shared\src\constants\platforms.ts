import type { Platform, Architecture, ReleaseChannel } from '../types/platform';

/**
 * 支持的平台列表
 */
export const SUPPORTED_PLATFORMS: Platform[] = ['windows', 'macos', 'linux'];

/**
 * 支持的架构列表
 */
export const SUPPORTED_ARCHITECTURES: Architecture[] = ['x86_64', 'aarch64', 'x86'];

/**
 * 支持的发布渠道列表
 */
export const SUPPORTED_CHANNELS: ReleaseChannel[] = ['stable', 'beta', 'alpha'];

/**
 * 平台文件扩展名映射
 */
export const PLATFORM_FILE_EXTENSIONS: Record<Platform, string[]> = {
  windows: ['.exe', '.msi', '.zip'],
  macos: ['.dmg', '.pkg', '.tar.gz'],
  linux: ['.deb', '.rpm', '.tar.gz', '.appimage'],
};

/**
 * 平台显示名称映射
 */
export const PLATFORM_DISPLAY_NAMES: Record<Platform, string> = {
  windows: 'Windows',
  macos: 'macOS',
  linux: 'Linux',
};

/**
 * 架构显示名称映射
 */
export const ARCHITECTURE_DISPLAY_NAMES: Record<Architecture, string> = {
  x86_64: 'x64',
  aarch64: 'ARM64',
  x86: 'x86',
};

/**
 * 渠道显示名称映射
 */
export const CHANNEL_DISPLAY_NAMES: Record<ReleaseChannel, string> = {
  stable: '稳定版',
  beta: '测试版',
  alpha: '开发版',
};

/**
 * 渠道优先级映射
 */
export const CHANNEL_PRIORITIES: Record<ReleaseChannel, number> = {
  stable: 1,
  beta: 2,
  alpha: 3,
};

/**
 * 平台检测用户代理字符串模式
 */
export const PLATFORM_USER_AGENT_PATTERNS: Record<Platform, RegExp[]> = {
  windows: [
    /Windows NT/i,
    /Win32/i,
    /Win64/i,
    /WOW64/i,
  ],
  macos: [
    /Mac OS X/i,
    /macOS/i,
    /Macintosh/i,
    /Darwin/i,
  ],
  linux: [
    /Linux/i,
    /X11/i,
    /Ubuntu/i,
    /Debian/i,
    /CentOS/i,
    /RedHat/i,
    /Fedora/i,
  ],
};

/**
 * 架构检测用户代理字符串模式
 */
export const ARCHITECTURE_USER_AGENT_PATTERNS: Record<Architecture, RegExp[]> = {
  x86_64: [
    /x86_64/i,
    /x64/i,
    /amd64/i,
    /Win64/i,
    /WOW64/i,
  ],
  aarch64: [
    /aarch64/i,
    /arm64/i,
    /ARM64/i,
  ],
  x86: [
    /i386/i,
    /i686/i,
    /x86/i,
    /Win32/i,
  ],
};

/**
 * 默认平台配置
 */
export const DEFAULT_PLATFORM_CONFIG = {
  platform: 'windows' as Platform,
  architecture: 'x86_64' as Architecture,
  channel: 'stable' as ReleaseChannel,
};

/**
 * 平台兼容性矩阵
 */
export const PLATFORM_COMPATIBILITY_MATRIX: Record<Platform, Record<Architecture, boolean>> = {
  windows: {
    x86_64: true,
    aarch64: true,
    x86: true,
  },
  macos: {
    x86_64: true,
    aarch64: true,
    x86: false,
  },
  linux: {
    x86_64: true,
    aarch64: true,
    x86: true,
  },
};
