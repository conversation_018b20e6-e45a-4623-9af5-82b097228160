#!/usr/bin/env pwsh

<#
.SYNOPSIS
    构建 RealityTap Desktop 应用并自动生成更新文件
    Build RealityTap Desktop app and automatically generate update files

.DESCRIPTION
    此脚本会：
    1. 检查签名密钥环境变量
    2. 构建前端应用
    3. 构建 Tauri 应用并生成更新文件和签名
    4. 显示生成的更新文件信息

.PARAMETER Configuration
    构建配置：release 或 debug（默认：release）

.PARAMETER SkipFrontend
    跳过前端构建（默认：false）

.PARAMETER Verbose
    显示详细输出（默认：false）

.EXAMPLE
    .\scripts\build-with-updater.ps1
    .\scripts\build-with-updater.ps1 -Configuration debug
    .\scripts\build-with-updater.ps1 -SkipFrontend
#>

param(
    [ValidateSet("release", "debug")]
    [string]$Configuration = "release",
    
    [switch]$SkipFrontend,
    
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 获取项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot

Write-ColorOutput "🚀 开始构建 RealityTap Desktop 应用（配置：$Configuration）" "Cyan"
Write-ColorOutput "📁 项目目录: $ProjectRoot" "Gray"

# 在构建前检查 DLL 文件
Write-ColorOutput "`n📦 检查 DLL 文件..." "Yellow"

$DllDir = Join-Path $ProjectRoot "src-tauri/libs/windows/x64"
if (Test-Path $DllDir) {
    $DllFiles = Get-ChildItem $DllDir -Filter "*.dll"
    if ($DllFiles.Count -gt 0) {
        Write-ColorOutput "✅ 发现 $($DllFiles.Count) 个 DLL 文件:" "Green"
        foreach ($dll in $DllFiles) {
            Write-ColorOutput "   - $($dll.Name)" "White"
        }
    } else {
        Write-ColorOutput "⚠️  DLL 目录为空" "Yellow"
    }
} else {
    Write-ColorOutput "⚠️  DLL 目录不存在: $DllDir" "Yellow"
}

# 检查签名密钥环境变量
Write-ColorOutput "`n🔐 检查签名配置..." "Yellow"

# 首先尝试从当前环境变量获取
$PrivateKey = $env:TAURI_SIGNING_PRIVATE_KEY
$PrivateKeyPassword = $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD

# 如果环境变量未设置，尝试从 .env.production.signing 文件加载
if (-not $PrivateKey) {
    $EnvFile = ".env.production.signing"

    if (Test-Path $EnvFile) {
        Write-ColorOutput "📁 从 $EnvFile 自动加载环境变量..." "Yellow"

        Get-Content $EnvFile | ForEach-Object {
            if ($_ -match '^([^=]+)=(.*)$' -and -not $_.StartsWith('#')) {
                $name = $matches[1].Trim()
                $value = $matches[2].Trim()

                # 移除引号（如果存在）
                if ($value.StartsWith('"') -and $value.EndsWith('"')) {
                    $value = $value.Substring(1, $value.Length - 2)
                }

                if ($value) {
                    [Environment]::SetEnvironmentVariable($name, $value, 'Process')
                    Write-ColorOutput "✅ 自动设置 $name" "Green"
                }
            }
        }

        # 重新获取环境变量
        $PrivateKey = $env:TAURI_SIGNING_PRIVATE_KEY
        $PrivateKeyPassword = $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD
    }
}

if (-not $PrivateKey) {
    Write-ColorOutput "❌ 错误: 未设置 TAURI_SIGNING_PRIVATE_KEY 环境变量" "Red"
    Write-ColorOutput "请选择以下方式之一设置签名密钥：" "Yellow"
    Write-ColorOutput "  方式1: 运行设置脚本" "Gray"
    Write-ColorOutput "    npm run setup:signing" "Cyan"
    Write-ColorOutput "  方式2: 手动设置环境变量" "Gray"
    Write-ColorOutput "    `$env:TAURI_SIGNING_PRIVATE_KEY=`"path/to/your/private.key`"" "Gray"
    Write-ColorOutput "    `$env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD=`"your-password`"" "Gray"
    Write-ColorOutput "  方式3: 确保 .env.production.signing 文件存在且配置正确" "Gray"
    exit 1
}

Write-ColorOutput "✅ 签名密钥配置已找到" "Green"
if ($PrivateKeyPassword) {
    Write-ColorOutput "✅ 签名密钥密码已设置" "Green"
} else {
    Write-ColorOutput "⚠️  未设置签名密钥密码（如果密钥有密码保护，请设置 TAURI_SIGNING_PRIVATE_KEY_PASSWORD）" "Yellow"
}

try {
    # 1. 构建前端（如果需要）
    if (-not $SkipFrontend) {
        Write-ColorOutput "`n🔨 第一步: 构建前端应用..." "Yellow"
        
        if ($Verbose) {
            npm run build
        } else {
            npm run build | Out-Null
        }
        
        if ($LASTEXITCODE -ne 0) {
            throw "前端构建失败"
        }
        
        Write-ColorOutput "✅ 前端构建完成" "Green"
    } else {
        Write-ColorOutput "`n⏭️  跳过前端构建" "Yellow"
    }
    
    # 2. 构建 Tauri 应用并生成更新文件
    Write-ColorOutput "`n🔨 第二步: 构建 Tauri 应用并生成更新文件..." "Yellow"
    
    # 设置构建命令
    $BuildCommand = if ($Configuration -eq "release") {
        "npm run tauri:build"
    } else {
        "npm run tauri build -- --debug"
    }
    
    Write-ColorOutput "🔧 执行构建命令: $BuildCommand" "Cyan"
    
    if ($Configuration -eq "release") {
        if ($Verbose) {
            npm run tauri:build
        } else {
            npm run tauri:build
        }
    } else {
        if ($Verbose) {
            npm run tauri build -- --debug
        } else {
            npm run tauri build -- --debug
        }
    }

    # 检查构建结果，即使退出代码不为 0，也可能有 MSI 文件生成
    $BuildFailed = $LASTEXITCODE -ne 0

    if ($BuildFailed) {
        Write-ColorOutput "⚠️  Tauri 构建过程报告失败，但检查是否有文件生成..." "Yellow"

        # 检查是否有 MSI 文件生成
        $BundleDir = if ($Configuration -eq "release") {
            Join-Path $ProjectRoot "src-tauri/target/release/bundle"
        } else {
            Join-Path $ProjectRoot "src-tauri/target/debug/bundle"
        }

        if (Test-Path $BundleDir) {
            $MsiFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.msi"
            if ($MsiFiles.Count -gt 0) {
                Write-ColorOutput "✅ 发现 MSI 文件已生成，继续处理签名..." "Green"
            } else {
                throw "Tauri 应用构建失败，退出代码: $LASTEXITCODE，且未找到 MSI 文件"
            }
        } else {
            throw "Tauri 应用构建失败，退出代码: $LASTEXITCODE，且未找到构建目录"
        }
    }

    Write-ColorOutput "✅ Tauri 应用构建完成" "Green"

    # 3. 检查并生成签名文件（如果需要）
    Write-ColorOutput "`n🔐 第三步: 检查和生成签名文件..." "Yellow"

    $BundleDir = if ($Configuration -eq "release") {
        Join-Path $ProjectRoot "src-tauri/target/release/bundle"
    } else {
        Join-Path $ProjectRoot "src-tauri/target/debug/bundle"
    }

    if (Test-Path $BundleDir) {
        $MsiFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.msi"
        foreach ($MsiFile in $MsiFiles) {
            $SigFile = "$($MsiFile.FullName).sig"
            if (-not (Test-Path $SigFile)) {
                Write-ColorOutput "🔧 为 $($MsiFile.Name) 生成签名文件..." "Cyan"

                # 使用环境变量中的密钥内容
                # TAURI_SIGNING_PRIVATE_KEY 现在直接包含 base64 编码的密钥内容
                $KeyContent = $PrivateKey

                # 生成签名 - 使用正确的命令格式
                try {
                    if ($PrivateKeyPassword) {
                        & tauri signer sign "$($MsiFile.FullName)" --private-key "$KeyContent" --password "$PrivateKeyPassword"
                    } else {
                        & tauri signer sign "$($MsiFile.FullName)" --private-key "$KeyContent"
                    }

                    if ($LASTEXITCODE -eq 0) {
                        Write-ColorOutput "✅ 签名文件生成成功: $SigFile" "Green"
                    } else {
                        Write-ColorOutput "❌ 签名文件生成失败，退出代码: $LASTEXITCODE" "Red"
                    }
                } catch {
                    Write-ColorOutput "❌ 签名文件生成失败: $_" "Red"
                }
            } else {
                Write-ColorOutput "✅ 签名文件已存在: $SigFile" "Green"
            }
        }
    }
    
    # 4. 显示构建结果和更新文件
    Write-ColorOutput "`n📦 构建结果:" "Green"
    
    $BundleDir = if ($Configuration -eq "release") {
        Join-Path $ProjectRoot "src-tauri/target/release/bundle"
    } else {
        Join-Path $ProjectRoot "src-tauri/target/debug/bundle"
    }
    
    if (Test-Path $BundleDir) {
        Write-ColorOutput "📁 构建产物位置: $BundleDir" "Cyan"
        
        # 查找 MSI 文件
        $MsiFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.msi"
        foreach ($MsiFile in $MsiFiles) {
            Write-ColorOutput "📦 安装包: $($MsiFile.FullName)" "White"
            
            # 查找对应的签名文件
            $SigFile = "$($MsiFile.FullName).sig"
            if (Test-Path $SigFile) {
                Write-ColorOutput "🔐 签名文件: $SigFile" "Green"
                
                # 显示签名文件内容（前100个字符）
                $SigContent = Get-Content $SigFile -Raw
                $SigPreview = if ($SigContent.Length -gt 100) {
                    $SigContent.Substring(0, 100) + "..."
                } else {
                    $SigContent
                }
                Write-ColorOutput "   签名内容: $SigPreview" "Gray"
            } else {
                Write-ColorOutput "❌ 未找到签名文件: $SigFile" "Red"
            }
            
            # 显示文件大小
            $FileSize = [math]::Round($MsiFile.Length / 1MB, 2)
            Write-ColorOutput "   文件大小: $FileSize MB" "Gray"
            Write-ColorOutput "   修改时间: $($MsiFile.LastWriteTime)" "Gray"
        }
        
        # 查找其他更新相关文件
        $UpdateFiles = Get-ChildItem $BundleDir -Recurse -Filter "*.sig"
        if ($UpdateFiles.Count -gt 0) {
            Write-ColorOutput "`n🔐 生成的签名文件:" "Cyan"
            foreach ($UpdateFile in $UpdateFiles) {
                Write-ColorOutput "   $($UpdateFile.FullName)" "White"
            }
        }
    } else {
        Write-ColorOutput "❌ 未找到构建产物目录: $BundleDir" "Red"
    }
    
    # 5. 显示下一步操作提示
    Write-ColorOutput "`n💡 下一步操作:" "Yellow"
    Write-ColorOutput "1. 将生成的 MSI 文件和 .sig 签名文件上传到更新服务器" "White"
    Write-ColorOutput "2. 更新服务器的版本信息（如果使用静态 JSON）" "White"
    Write-ColorOutput "3. 测试更新功能：npm run test:ota" "White"
    
    Write-ColorOutput "`n🎉 构建完成！" "Green"
    
} catch {
    Write-ColorOutput "`n❌ 构建失败: $_" "Red"
    exit 1
}

Write-ColorOutput "`n✨ 所有操作完成！" "Green"
